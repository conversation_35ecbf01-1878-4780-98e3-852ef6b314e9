package br.com.celk.bo.service.rest.agendamento.service;

import br.com.celk.agendamento.dto.AgendamentoIntranetDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.ejb.Stateless;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
@Stateless
public class AgendamentoService {

    public List<AgendamentoIntranetDTO> findAgendamentosIntranet(String dataConfirmacaoStr) throws DAOException, ValidacaoException, ParseException {
        Date dataConfirmacao = new SimpleDateFormat("yyyy-MM-dd").parse(dataConfirmacaoStr);
        return BOFactory.getBO(AgendamentoFacade.class).findAgendamentosIntranet(dataConfirmacao);
    }
}
