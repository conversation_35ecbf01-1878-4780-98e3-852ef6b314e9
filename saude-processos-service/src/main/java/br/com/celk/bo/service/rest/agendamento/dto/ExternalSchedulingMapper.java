package br.com.celk.bo.service.rest.agendamento.dto;

public class ExternalSchedulingMapper {

    public ExternalSchedulingReserveDTO toSchedulingReserveDTO(ExternalSchedulingDTO schedulingDTO) {
        ExternalSchedulingReserveDTO schedulingReserveDTO = new ExternalSchedulingReserveDTO();
        TimeExternalScheduleDTO timeScheduleDTO = new TimeExternalScheduleDTO();
        timeScheduleDTO.setTimeScheduleId(schedulingDTO.getGridScheduleTimeId());

        schedulingReserveDTO.setTimeSchedule(timeScheduleDTO);
        schedulingReserveDTO.setCareUnitId(schedulingDTO.getCareUnitId());
        schedulingReserveDTO.setGridScheduleServiceId(schedulingDTO.getGridScheduleServiceId());
        schedulingReserveDTO.setPatientSaudeId(schedulingDTO.getPatientId());
        schedulingReserveDTO.setProcedureId(schedulingDTO.getProcedureId());
        schedulingReserveDTO.setGridSheduleObs(schedulingDTO.getGridSheduleObs());


        return schedulingReserveDTO;
    }
}
