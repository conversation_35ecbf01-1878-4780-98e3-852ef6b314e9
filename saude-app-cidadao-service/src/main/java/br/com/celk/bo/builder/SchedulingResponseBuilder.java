package br.com.celk.bo.builder;

import br.com.celk.bo.dto.ClkError;
import br.com.celk.bo.dto.SchedulingResponseDTO;

import java.util.List;

public class SchedulingResponseBuilder {

    private SchedulingResponseDTO response;

    public SchedulingResponseBuilder builder() {
        this.response = new SchedulingResponseDTO();
        return this;
    }

    public SchedulingResponseBuilder setCode(Long code) {
        this.response.setCode(code);
        return this;
    }

    public SchedulingResponseBuilder setSchedulingId(Long schedulingId) {
        this.response.setSchedulingId(schedulingId);
        return this;
    }

    public SchedulingResponseBuilder setCorrelationId(Long correlationId) {
        this.response.setCorrelationId(correlationId);
        return this;
    }

    public SchedulingResponseBuilder setMessage(String message) {
        this.response.setMessage(message);
        return this;
    }

    public SchedulingResponseBuilder setErrors(List<ClkError> errors) {
        this.response.setErrors(errors);
        return this;
    }

    public SchedulingResponseDTO build() {
        return this.response;
    }
}
