package br.com.celk.bo.dto.exame.enums;

public enum StatusExame {

    ABERTO(1L, "ABERTO"),
    CONCLUIDO(2L, "CONCLUIDO"),
    NAO_REALIZADO(3L, "NAO_REALIZADO"),
    CANCELADO(4L, "CANCELADO");
    private Long valor;
    private final String descricao;

    StatusExame(Long value, String descricao) {
        this.valor = valor;
        this.descricao = descricao;
    }

    public static StatusExame valeuOf(Long value) {
        for (StatusExame statusExame : StatusExame.values()) {
            if (statusExame.getValor().equals(value)) {
                return statusExame;
            }
        }
        return null;
    }

    public Long getValor() {
        return valor;
    }

    public String getDescricao() {
        return descricao;
    }
}

