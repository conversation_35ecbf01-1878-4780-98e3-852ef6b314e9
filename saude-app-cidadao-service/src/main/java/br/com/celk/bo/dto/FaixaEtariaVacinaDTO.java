package br.com.celk.bo.dto;

import br.com.celk.bo.dto.vacina.enums.FaixaEtariaVacinaEnum;

public class FaixaEtariaVacinaDTO {

    public FaixaEtariaVacinaDTO(FaixaEtariaVacinaEnum faixaEtaria){
        this.setDescricao(faixaEtaria.name());
        this.setIdadeInicial(faixaEtaria.getIdadeInicial());
        this.setIdadeLimite(faixaEtaria.getIdadeLimite());
    }

    private String descricao;
    private Long idadeInicial;
    private Long idadeLimite;

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public Long getIdadeInicial() {
        return idadeInicial;
    }

    public Long getIdadeLimite() {
        return idadeLimite;
    }

    public void setIdadeInicial(Long idadeInicial) {
        this.idadeInicial = idadeInicial;
    }

    public void setIdadeLimite(Long idadeLimite) {
        this.idadeLimite = idadeLimite;
    }
}
