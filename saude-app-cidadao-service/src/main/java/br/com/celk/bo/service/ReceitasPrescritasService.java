package br.com.celk.bo.service;

import br.com.celk.bo.dto.DispensacaoReceitasPrescritasDTO;
import br.com.celk.bo.dto.MedicamentoDTO;
import br.com.ksisolucoes.bo.appcidadao.interfaces.facade.AppCidadaoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.TipoViaMedicamento;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;

import javax.ejb.Stateless;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

@Stateless
public class ReceitasPrescritasService {

    private static final Long DISPENSADO = 1L;

    public Response salvarDispensacao(String token) {
        try {

            Receituario receituario = buscarReceituario(token);

            if (receituario == null) {
                return Response.status(Response.Status.BAD_REQUEST)
                        .entity("{\"error\":\"Receituario não encontrado.\"}")
                        .type(MediaType.APPLICATION_JSON)
                        .build();
            }

            if (receituario.getFlagDispensado() != null) {
                if (receituario.getFlagDispensado().equals(DISPENSADO)) {
                    return Response.status(Response.Status.BAD_REQUEST)
                            .entity("{\"error\":\"Receituario já dispensado.\"}")
                            .type(MediaType.APPLICATION_JSON)
                            .build();
                }
            }
            receituario.setFlagDispensado(DISPENSADO);

            BOFactory.getBO(AppCidadaoFacade.class).atualizarReceituario(receituario);

        } catch (ValidacaoException | DAOException exception) {
            Loggable.log.error(exception.getMessage());
        }

        return Response.status(Response.Status.OK)
                .entity("{\"message\":\"Receituario dispensado!\"}")
                .type(MediaType.APPLICATION_JSON)
                .build();
    }

    public Response consultaPrescricao(String token) {
        Receituario receituario = buscarReceituario(token);

        if (receituario == null) {
            return Response.status(Response.Status.BAD_REQUEST)
                    .entity("{\"error\":\"Receituario não encontrado.\"}")
                    .type(MediaType.APPLICATION_JSON)
                    .build();
        }

        Long codigoEnderecoDomicilio = receituario.getUsuarioCadsus().getEnderecoUsuarioCadsus().getCodigo();
        Long codigoProfissional = receituario.getProfissional().getCodigo();
        Long codigoReceituario = receituario.getCodigo();

        EnderecoUsuarioCadsus enderecoUsuarioCadsus = buscarEnderecoUsuarioCadsus(codigoEnderecoDomicilio);
        Profissional profissional = buscarProfissional(codigoProfissional);

        List<ReceituarioItem> medicamentoReceituario = buscarMedicamentos(codigoReceituario);

        List<MedicamentoDTO> medicamentoDTOList = getMedicamentoDTO(medicamentoReceituario);

        return Response.ok(new DispensacaoReceitasPrescritasDTO(receituario, medicamentoDTOList, enderecoUsuarioCadsus, profissional), MediaType.APPLICATION_JSON).build();

    }

    private List<MedicamentoDTO> getMedicamentoDTO(List<ReceituarioItem> medicamentoReceituario) {
        List<MedicamentoDTO> medicamentoDTOList = new ArrayList<>();

        for (ReceituarioItem item : medicamentoReceituario) {

            MedicamentoDTO medicamentoDTO = new MedicamentoDTO();

            medicamentoDTO.setIdMedicamento(item.getCodigo());
            medicamentoDTO.setNomeMedicamento(item.getNomeProduto());
            medicamentoDTO.setQuantidadePrescrita(item.getQuantidadePrescrita() == null ? "Quantidade Prescrita não cadastrada." : item.getQuantidadePrescrita().toString());
            medicamentoDTO.setTipoReceita(item.getTipoReceitaProdutoNaoCadastrado() == null ? "Tipo Receita não cadastrado." : item.getTipoReceitaProdutoNaoCadastrado().getDescricao());
            medicamentoDTO.setViaAdministracao(item.getTipoViaMedicamento() == null ? "Via Administração não cadastrada." : item.getTipoViaMedicamento().getDescricao());
            medicamentoDTO.setPosologia(item.getPosologia() == null ? "Posologia não cadastrada." : item.getPosologia());

            medicamentoDTOList.add(medicamentoDTO);
        }
        return medicamentoDTOList;
    }

    private Receituario buscarReceituario(String token) {
        return LoadManager.getInstance(Receituario.class)
                .addProperties(new HQLProperties(Receituario.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, Receituario.PROP_USUARIO_CADSUS).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Receituario.PROP_CODIGO, Long.parseLong(token)))
                .startLeitura().getVO();
    }

    private EnderecoUsuarioCadsus buscarEnderecoUsuarioCadsus(Long codigoEnderecoDomicilio) {
        return LoadManager.getInstance(EnderecoUsuarioCadsus.class)
                .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(EnderecoUsuarioCadsus.PROP_CODIGO, codigoEnderecoDomicilio))
                .startLeitura().getVO();
    }

    private Profissional buscarProfissional(Long codigoProfissional) {
        return LoadManager.getInstance(Profissional.class)
                .addProperties(new HQLProperties(Profissional.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Profissional.PROP_CODIGO, codigoProfissional))
                .startLeitura().getVO();
    }

    private List<ReceituarioItem> buscarMedicamentos(Long codigoReceituario) {
        return LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addProperties(new HQLProperties(TipoViaMedicamento.class, ReceituarioItem.PROP_TIPO_VIA_MEDICAMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ReceituarioItem.PROP_RECEITUARIO, codigoReceituario))
                .start().getList();
    }
}