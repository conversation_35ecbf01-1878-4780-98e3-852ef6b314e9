package br.com.celk.esus;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.QueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.cadsus.cds.*;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.util.ArrayList;
import java.util.List;

public class GerarArquivoEsusIntegracaoCdsAtendimentoDomiciliarTestHelper {

    private GerarArquivoEsusIntegracaoCdsAtendimentoDomiciliarTestHelper() { }

    public static ExportacaoEsusDTOParam getEsusDtoParam() {
        ExportacaoEsusDTOParam esusDTOParam = new ExportacaoEsusDTOParam();
        esusDTOParam.setCodigoProcesso(1L);
        esusDTOParam.setPeriodo(new DatePeriod());

        return esusDTOParam;
    }

    public static List<QueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO> queryConsultaEsusIntegracaoCdsAtendimentoDomiciliar() throws Exception {
        List<QueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO> queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTOS = new ArrayList<>();

        queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTOS.add(getQueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO());

        return queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTOS;
    }

    private static QueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO getQueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO() throws Exception {
        QueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO = new QueryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO();

        queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO.setEsusIntegracaoCds(getEsusIntegracaoCds());
        queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO.setItemList(getItemList());
        queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO.setItemProcedimentoList(getItemProcedimentoList());

        return queryConsultaEsusIntegracaoCdsAtendimentoDomiciliarDTO;
    }

    private static List<EsusFichaAtendDomiciliarItem> getItemList() throws Exception {
        List<EsusFichaAtendDomiciliarItem> esusFichaAtendDomiciliarItems = new ArrayList<>();

        esusFichaAtendDomiciliarItems.add(getEsusFichaAtendDomiciliarItem());

        return esusFichaAtendDomiciliarItems;
    }

    private static EsusFichaAtendDomiciliarItem getEsusFichaAtendDomiciliarItem() throws Exception {
        EsusFichaAtendDomiciliarItem esusFichaAtendDomiciliarItem = new EsusFichaAtendDomiciliarItem();

        esusFichaAtendDomiciliarItem.setCodigo(1L);
        esusFichaAtendDomiciliarItem.setTurno(EsusFichaProcedimentoItem.Turno.VESPERTINO.value());
        esusFichaAtendDomiciliarItem.setUsuarioCadsus(GerarArquivoEsusIntegracaoCdsTestHelper.getUsuarioCadsus());
        esusFichaAtendDomiciliarItem.setDataNascimento(DataUtil.stringToDate("26/01/2000"));
        esusFichaAtendDomiciliarItem.setSexo(EsusFichaProcedimentoItem.Sexo.MASCULINO.value());
        esusFichaAtendDomiciliarItem.setLocalAtendimento(EsusFichaMarcadoresConsumoAlimentar.LocalAtendimento.UBS.value());
        esusFichaAtendDomiciliarItem.setModalidade(EsusFichaAtendIndividualItem.ModalidadeAD.AD1.value());
        esusFichaAtendDomiciliarItem.setTipoAtendimento((Long) EsusFichaAtendDomiciliarItem.TipoAtendimento.ATENDIMENTO_PROGRAMADO.value());
        esusFichaAtendDomiciliarItem.setSomatorioCondicao(1L);
        esusFichaAtendDomiciliarItem.setCid(new Cid());
        esusFichaAtendDomiciliarItem.getCid().setCodigo("123456789");
        esusFichaAtendDomiciliarItem.setConduta(EsusFichaAtendDomiciliarItem.Conduta.ALTA_CLINICA_AD.value());
        esusFichaAtendDomiciliarItem.setCiap("123");

        return esusFichaAtendDomiciliarItem;
    }

    private static List<EsusFichaAtendDomiciliarItemProcedimento> getItemProcedimentoList() {
        List<EsusFichaAtendDomiciliarItemProcedimento> esusFichaAtendDomiciliarItemProcedimentos = new ArrayList<>();

        esusFichaAtendDomiciliarItemProcedimentos.add(getEsusFichaAtendDomiciliarItemProcedimento());

        return esusFichaAtendDomiciliarItemProcedimentos;
    }

    private static EsusFichaAtendDomiciliarItemProcedimento getEsusFichaAtendDomiciliarItemProcedimento() {
        EsusFichaAtendDomiciliarItemProcedimento esusFichaAtendDomiciliarItemProcedimento = new EsusFichaAtendDomiciliarItemProcedimento();

        esusFichaAtendDomiciliarItemProcedimento.setCodigo(1L);
        esusFichaAtendDomiciliarItemProcedimento.setProcedimento(new Procedimento());
        esusFichaAtendDomiciliarItemProcedimento.getProcedimento().setReferencia("456789");
        esusFichaAtendDomiciliarItemProcedimento.setEsusFichaAtendDomiciliarItem(new EsusFichaAtendDomiciliarItem());
        esusFichaAtendDomiciliarItemProcedimento.getEsusFichaAtendDomiciliarItem().setCodigo(1L);

        return esusFichaAtendDomiciliarItemProcedimento;
    }

    private static EsusIntegracaoCds getEsusIntegracaoCds() throws Exception {
        EsusIntegracaoCds esusIntegracaoCds = new EsusIntegracaoCds();
        
        esusIntegracaoCds.setCodigo(1L);
        esusIntegracaoCds.setEsusFichaAtendimentoDomiciliar(getEsusFichaAtendimentoDomiciliar());

        return esusIntegracaoCds;
    }

    private static EsusFichaAtendDomiciliar getEsusFichaAtendimentoDomiciliar() throws Exception {
        EsusFichaAtendDomiciliar esusFichaAtendDomiciliar = new EsusFichaAtendDomiciliar();

        esusFichaAtendDomiciliar.setCodigo(1L);
        esusFichaAtendDomiciliar.setEmpresa(GerarArquivoEsusIntegracaoCdsTestHelper.getEmpresa());
        esusFichaAtendDomiciliar.setProfissionalPrincipal(GerarArquivoEsusIntegracaoCdsTestHelper.getProfissional());
        esusFichaAtendDomiciliar.setCboPrincipal(GerarArquivoEsusIntegracaoCdsTestHelper.getTabelaCbo());
        esusFichaAtendDomiciliar.setCodigoIne("1234567890");
        esusFichaAtendDomiciliar.setDataAtendimento(DataUtil.stringToDate("21/01/2021"));

        return esusFichaAtendDomiciliar;
    }

    public static TabelaCbo getTabelaCbo() {
        TabelaCbo tabelaCbo = new TabelaCbo();

        tabelaCbo.setCbo("123456");

        return tabelaCbo;
    }
}