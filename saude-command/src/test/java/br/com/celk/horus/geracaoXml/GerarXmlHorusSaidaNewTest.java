package br.com.celk.horus.geracaoXml;

import br.com.celk.horus.dto.GeracaoXmlHorusDTO;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;


import java.io.IOException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GerarXmlHorusSaidaNewTest {

    private GeracaoXmlHorusDTO dto;
    private GerarXmlHorusSaidaNew xmlSaida;


    @Before
    public void setUp(){
        LocalDate ld_inicial = LocalDate.of(2021,07,01);
        LocalDate ld_final = LocalDate.of(2021,07,31);
        DatePeriod dp = new DatePeriod(Date.from(ld_inicial.atStartOfDay(ZoneId.systemDefault()).toInstant()),Date.from(ld_final.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        dto = new GeracaoXmlHorusDTO();
        dto.setTipoXml("S");
        dto.setCodigoSincronizacaoHorusProcesso(43521779L);
        dto.setTipoSincronizacao(1L);
        dto.setLstInconsistencia(null);
        dto.setCodigoProdutoList(null);
        dto.setPeriodo(dp);
        dto.setLstEmpresas(listaDeEmpresasVinculadasHorus());
    }

    private List<Empresa> listaDeEmpresasVinculadasHorus(){
        Empresa e1 = new Empresa();
        Empresa e2 = new Empresa();
        Empresa e3 = new Empresa();
        Empresa e4 = new Empresa();
        Empresa e5 = new Empresa();
        Empresa e6 = new Empresa();
        e1.setCodigo(176859L);
        e1.setDescricao("CENTRO DE SAUDE CENIRO MARTINS");
        e2.setCodigo(249941L);
        e2.setDescricao("NUTAJ - FARMÁCIA JUDICIAL");
        e3.setCodigo(369874L);
        e3.setDescricao("EMPRESA DE TESTE 1");
        e4.setCodigo(123456L);
        e4.setDescricao("EMPRESA DE TESTE 2");
        e5.setCodigo(147852l);
        e5.setDescricao("EMPRESA DE TESTE 3");
        e6.setCodigo(852963l);
        e6.setDescricao("EMPRESA DE TESTE 4");
        List<Empresa> empresasHorus = new ArrayList<>();
        empresasHorus.add(e1);
        empresasHorus.add(e2);
        empresasHorus.add(e3);
        empresasHorus.add(e4);
        empresasHorus.add(e5);
        empresasHorus.add(e6);
        return empresasHorus;
    }

    @Test()
    public void obterValorEmKbytesDeUmObjeto(){
        int bytes = 0;
        xmlSaida = new GerarXmlHorusSaidaNew(dto);
        try {
            bytes = xmlSaida.transformarObjetoParaArquivo(dto);
        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println(bytes+" bytes");
        boolean resultado = bytes > 0;
        Assert.assertEquals(true,resultado);
    }

    @Test
    public void deveRetornarOValorDaQtdDeLotes(){
        int QTD_LOTES_SAIDA = 2500;
        int TAMANHO_MAX_ARQUIVO = 4096000;
        int bytes = 0;
        int   numeroRegistroPorLote = 0;
        xmlSaida = new GerarXmlHorusSaidaNew(dto);
        try {
            bytes = xmlSaida.transformarObjetoParaArquivo(dto);
        } catch (IOException e) {
            e.printStackTrace();
        }
        bytes = bytes * 2000;
        if(bytes > TAMANHO_MAX_ARQUIVO){
            numeroRegistroPorLote = QTD_LOTES_SAIDA;
        }else{
            numeroRegistroPorLote = bytes;
        }
        Assert.assertEquals(2500,numeroRegistroPorLote);
    }

    @Test
    public void deveRetornarAQuantidadeMaximaDeLotes(){
        int QTD_LOTES_SAIDA = 2500;
        int TAMANHO_MAX_ARQUIVO = 4096000;
        int bytes = 0;
        int   numeroRegistroPorLote = 0;
        xmlSaida = new GerarXmlHorusSaidaNew(dto);
        try {
            for (int i = 0; i < dto.getLstEmpresas().size();i++) {
                bytes = bytes + xmlSaida.transformarObjetoParaArquivo(dto.getLstEmpresas().get(i));
                if(i == dto.getLstEmpresas().size()-2){ bytes = bytes*2000;}
                if(bytes < TAMANHO_MAX_ARQUIVO){
                    numeroRegistroPorLote = i;
                }else{
                    break;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        if(numeroRegistroPorLote > 2){
            numeroRegistroPorLote = QTD_LOTES_SAIDA;
        }
        boolean resultado = numeroRegistroPorLote == QTD_LOTES_SAIDA;
        Assert.assertEquals(true,resultado);

    }

    @Test
    public void deveRetornarOValorDeBytesMenorQueOTamanhoMaximoDoArquivo(){
        int QTD_LOTES_SAIDA = 2500;
        int TAMANHO_MAX_ARQUIVO = 4096000;
        int bytes = 0;
        int   numeroRegistroPorLote = 0;
        xmlSaida = new GerarXmlHorusSaidaNew(dto);
        try {
            bytes = xmlSaida.transformarObjetoParaArquivo(dto);
        } catch (IOException e) {
            e.printStackTrace();
        }
        if(bytes > TAMANHO_MAX_ARQUIVO){
            numeroRegistroPorLote = QTD_LOTES_SAIDA;
        }else{
            numeroRegistroPorLote = bytes;
        }
        boolean resultado = numeroRegistroPorLote < TAMANHO_MAX_ARQUIVO;
        Assert.assertEquals(true,resultado);
    }
}
