package br.com.celk.report.unidadesaude;

import br.com.celk.report.unidadesaude.query.QueryRelacaoGestantesSemAderenciaIndicadores;
import br.com.celk.report.unidadesaude.query.QueryRelacaoGestantesSemAderenciaIndicadoresHelper;
import br.com.celk.system.report.TipoRelatorio;
import br.com.celk.unidadesaude.esus.relatorios.RelacaoGestantesSemAderenciaIndicadoresDTOParam;
import br.com.ksisolucoes.util.Bundle;
import junit.framework.Assert;
import org.junit.Before;
import org.junit.Test;

public class RelacaoGestantesSemAderenciaIndicadoresTest {
    RelacaoGestantesSemAderenciaIndicadores relacaoGestantesSemAderenciaIndicadores;

    @Before
    public void setUp() {
        RelacaoGestantesSemAderenciaIndicadoresDTOParam relacaoGestantesSemAderenciaIndicadoresDTOParam = new RelacaoGestantesSemAderenciaIndicadoresDTOParam();
        relacaoGestantesSemAderenciaIndicadoresDTOParam.setTipoArquivo(TipoRelatorio.PDF);
        relacaoGestantesSemAderenciaIndicadores = new RelacaoGestantesSemAderenciaIndicadores(relacaoGestantesSemAderenciaIndicadoresDTOParam);
    }

    @Test
    public void shouldReturnCorrectXML() {
        Assert.assertEquals("should return the correct jrxml path", "/br/com/celk/report/unidadesaude/jrxml/relacao_gestantes_sem_aderencia.jrxml", relacaoGestantesSemAderenciaIndicadores.getXML());
    }
    @Test
    public void shouldReturnCorrectQuery() {
        Assert.assertEquals("should return the correct query", new QueryRelacaoGestantesSemAderenciaIndicadores().getClass(), relacaoGestantesSemAderenciaIndicadores.getQuery().getClass());
    }

    @Test
    public void shouldReturnCorrectXlsMapping() {
        Assert.assertEquals("should return the correct xls mapping", QueryRelacaoGestantesSemAderenciaIndicadoresHelper.getColumnMapping().toString(), relacaoGestantesSemAderenciaIndicadores.getMapeamentoPlanilha().toString());
    }

    @Test
    public void shouldReturnCorrectTitle() {
        Assert.assertEquals("should return the correct title", Bundle.getStringApplication("relacao_gestantes_sem_aderencia"), relacaoGestantesSemAderenciaIndicadores.getTitulo());
    }

    @Test
    public void shouldReturnCorrectReportType() {
        Assert.assertEquals("should return the correct report type", TipoRelatorio.PDF, relacaoGestantesSemAderenciaIndicadores.getTipoRelatorio());
    }

}