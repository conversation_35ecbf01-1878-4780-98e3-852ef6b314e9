package br.com.ksisolucoes.bo.prontuario.basico.atendimento.cenarios;

import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

public class BuildCenarioLiberarCotaUtilizada {

    private BuildCenarioLiberarCotaUtilizada() {
    }

    public static AgendaGradeAtendimentoHorario buildAgendamentoGradeAtendimentoHorario() {
        AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario = new AgendaGradeAtendimentoHorario(1L);
        agendaGradeAtendimentoHorario.setValorCotaUtilizado(50.00);
        agendaGradeAtendimentoHorario.setExamePrestadorCompetencia(buildExamePrestadorCompetencia());
        return agendaGradeAtendimentoHorario;
    }

    public static ExamePrestadorCompetencia buildExamePrestadorCompetencia() {
        ExamePrestadorCompetencia competencia = new ExamePrestadorCompetencia(1L);
        competencia.setTetoFinanceiroRealizado(100.00);
        competencia.setTetoRecursoProprio(100.00);
        competencia.setTetoRecursoProprioRealizado(50.00);
        return competencia;
    }

    public static Empresa buildEmpresa() {
        return new Empresa(1L);
    }

    public static TipoProcedimento buildExameProcedimento() {
        TipoProcedimento tipoProcedimento = new TipoProcedimento(1L);
        tipoProcedimento.setExameProcedimento(new ExameProcedimento(1L));
        return tipoProcedimento;
    }
}
