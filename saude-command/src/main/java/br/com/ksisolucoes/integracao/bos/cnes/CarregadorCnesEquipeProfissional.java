/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.cnes;

import br.com.ksisolucoes.integracao.conversao.MCGenericType;
import br.com.ksisolucoes.integracao.dao.util.vo.ModoConversao;
import br.com.ksisolucoes.integracao.dao.DAOException;
import br.com.ksisolucoes.integracao.dao.interfaces.SearchDAO;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoExistente;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoImpl;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoSequence;
import br.com.ksisolucoes.integracao.dao.util.vo.AtributoSync;
import br.com.ksisolucoes.integracao.dao.util.vo.EntidadeSync;
import br.com.ksisolucoes.integracao.vos.AtributoImpl;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import br.com.ksisolucoes.vo.basico.EquipeProfissional;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CarregadorCnesEquipeProfissional extends CarregadorCnesTemplate {

    private Date dataAtualizacao;
    private SearchDAO daoDestino;
    private SearchDAO daoOrigem;

    public CarregadorCnesEquipeProfissional(Date dataAtualizacao, SearchDAO daoOrigem, SearchDAO daoDestino) {
        this.dataAtualizacao = dataAtualizacao;
        this.daoOrigem = daoOrigem;
        this.daoDestino = daoDestino;
    }

    @Override
    protected List<Entidade> load() throws DAOException {
        EntidadeSync entidadeSync = new EntidadeSync("LFCES038", "equipe_profissional");
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("cd_equipe_profissional", false, true, false), new ValorAtributoSequence(daoDestino, "seq_gem", "cd_equipe_profissional", "equipe_profissional",
                new AtributoImpl("cd_equipe"),
                new AtributoImpl("cd_profissional"))));
        entidadeSync.addAtributo(new AtributoSync("COD_MUN || '-' || COD_AREA || '-' || SEQ_EQUIPE", new AtributoImpl("cd_equipe", true), new ModoConversao() {

            @Override
            public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
                try {
                    String[] valores = value.toString().split("-");
                    
                    Long cd_equipe_area = (Long) daoDestino.find("cd_equipe_area", "equipe_area",
                            new AtributoImpl("cod_cid", Long.valueOf(valores[0])),
                            new AtributoImpl("cd_area", Long.valueOf(valores[1])));
                    
                    return daoDestino.find("cd_equipe", "equipe",
                            new AtributoImpl("cd_equipe_area", cd_equipe_area),
                            new AtributoImpl("seq_equipe", Long.valueOf(valores[2])));
                } catch (DAOException ex) {
                    throw new IllegalArgumentException(ex.getMessage());
                }
            }
        }));

        entidadeSync.addAtributo(new AtributoSync("PROF_ID", new AtributoImpl("cd_profissional", true, true, false), new ModoConversao() {

            @Override
            public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
                try {
                    return new ValorAtributoExistente(daoDestino, "profissional", "cd_profissional", new AtributoImpl("profissional_id_cnes", new ValorAtributoImpl(value))).getValor();
                } catch (DAOException ex) {
                    throw new IllegalArgumentException(ex.getMessage());
                }
            }
        }));

        entidadeSync.addAtributo(new AtributoSync("DT_ENTRADA", new AtributoImpl("dt_entrada")));
        entidadeSync.addAtributo(new AtributoSync("DT_DESLIGAMENTO", new AtributoImpl("dt_desligamento")));
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("dt_atualizacao"), new ValorAtributoImpl(this.dataAtualizacao)));
        entidadeSync.addAtributo(new AtributoSync("FL_EQUIPEMINIMA", new AtributoImpl("fl_equipe_minima")));
        entidadeSync.addAtributo(new AtributoSync("MICROAREA", new AtributoImpl("micro_area"),new MCGenericType(Long.class)));
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("status"), new ValorAtributoImpl(EquipeProfissional.STATUS_ATIVO)));

        return this.daoOrigem.loadSynchronizer(entidadeSync);
    }
}
