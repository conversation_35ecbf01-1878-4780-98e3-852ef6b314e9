/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.integracao.bos.cnes;

import br.com.ksisolucoes.integracao.conversao.MCGenericType;
import br.com.ksisolucoes.integracao.dao.util.vo.ModoConversao;
import br.com.ksisolucoes.integracao.conversao.datasus.MCAgrupamento;
import br.com.ksisolucoes.integracao.dao.DAOException;
import br.com.ksisolucoes.integracao.dao.interfaces.SearchDAO;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoExistente;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoImpl;
import br.com.ksisolucoes.integracao.dao.util.ValorAtributoSequence;
import br.com.ksisolucoes.integracao.dao.util.vo.AtributoSync;
import br.com.ksisolucoes.integracao.dao.util.vo.EntidadeSync;
import br.com.ksisolucoes.integracao.vos.AtributoImpl;
import br.com.ksisolucoes.integracao.vos.interfaces.Entidade;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CarregadorCnesProfissionalCargaHoraria extends CarregadorCnesTemplate {

    private Date dataAtualizacao;
    private SearchDAO daoOrigem;
    private SearchDAO daoDestino;

    public CarregadorCnesProfissionalCargaHoraria(Date dataAtualizacao, SearchDAO daoOrigem, SearchDAO daoDestino) {
        this.dataAtualizacao = dataAtualizacao;
        this.daoOrigem = daoOrigem;
        this.daoDestino = daoDestino;
    }

    @Override
    protected List<Entidade> load() throws DAOException {
        EntidadeSync entidadeSync = new EntidadeSync("LFCES021", "profissional_carga_horaria");
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("cd_prof_carga_horaria", false, true, false), new ValorAtributoSequence(daoDestino, "seq_gem", "cd_prof_carga_horaria", "profissional_carga_horaria",
                new AtributoImpl("empresa"),
                new AtributoImpl("cd_profissional"),
                new AtributoImpl("cd_cbo"),
                new AtributoImpl("tp_sus_nao_sus"),
                new AtributoImpl("cd_vinculacao"),
                new AtributoImpl("cd_tipo_vinculo"),
                new AtributoImpl("cd_subtipo_vinculo"))));
        entidadeSync.addAtributo(new AtributoSync("UNIDADE_ID", new AtributoImpl("empresa", true, true, false), new ModoConversao() {

            public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
                try {
                    return new ValorAtributoExistente(daoDestino, "empresa", "empresa", new AtributoImpl("unidade_id_cnes", new ValorAtributoImpl(value))).getValor();
                } catch (DAOException ex) {
                    throw new IllegalArgumentException(ex.getMessage());
                }
            }
        }));

        entidadeSync.addAtributo(new AtributoSync("PROF_ID", new AtributoImpl("cd_profissional", true, true, false), new ModoConversao() {

            public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
                try {
                    return new ValorAtributoExistente(daoDestino, "profissional", "cd_profissional", new AtributoImpl("profissional_id_cnes", new ValorAtributoImpl(value))).getValor();
                } catch (DAOException ex) {
                    throw new IllegalArgumentException(ex.getMessage());
                }
            }
        }));

        entidadeSync.addAtributo(new AtributoSync("COD_CBO", new AtributoImpl("cd_cbo", true)));
        entidadeSync.addAtributo(new AtributoSync("TP_SUS_NAO_SUS", new AtributoImpl("tp_sus_nao_sus", true)));
        entidadeSync.addAtributo(new AtributoSync("IND_VINC", new AtributoImpl("cd_vinculacao", true), new MCAgrupamento(0, 2)));
        entidadeSync.addAtributo(new AtributoSync("IND_VINC", new AtributoImpl("cd_tipo_vinculo", true), new MCAgrupamento(2, 4)));
        entidadeSync.addAtributo(new AtributoSync("IND_VINC", new AtributoImpl("cd_subtipo_vinculo", true), new MCAgrupamento(4, 6)));
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("tp_registro"), new ValorAtributoImpl(ProfissionalCargaHoraria.TIPO_REGISTRO_INCLUSAO)));
        entidadeSync.addAtributo(new AtributoSync("CONSELHOID", new AtributoImpl("cd_orgao_emissor"), new MCGenericType(Long.class)));
        entidadeSync.addAtributo(new AtributoSync("N_REGISTRO", new AtributoImpl("nr_registro")));
        entidadeSync.addAtributo(new AtributoSync("CG_HORAAMB", new AtributoImpl("carga_hr_amb")));
        entidadeSync.addAtributo(new AtributoSync("CGHORAHOSP", new AtributoImpl("carga_hr_hosp")));
        entidadeSync.addAtributo(new AtributoSync("CGHORAOUTR", new AtributoImpl("carga_hr_outros")));
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("dt_atualizacao"), new ValorAtributoImpl(this.dataAtualizacao)));
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("dt_desativacao"), new ValorAtributoImpl(null)));
        entidadeSync.addAtributo(new AtributoSync(new AtributoImpl("competencia_inicio", false, true, false), new ValorAtributoImpl(Data.adjustRangeDay(new Date()).getDataInicial())));

        
        EntidadeSync entidadeProfissionalSync = new EntidadeSync("LFCES021", "profissional");

        entidadeProfissionalSync.addAtributo(new AtributoSync("PROF_ID", new AtributoImpl("cd_profissional", true, true, false), new ModoConversao() {

            public Object getValorConvertido(String tipo, Object value) throws IllegalArgumentException {
                try {
                    return new ValorAtributoExistente(daoDestino, "profissional", "cd_profissional", new AtributoImpl("profissional_id_cnes", new ValorAtributoImpl(value))).getValor();
                } catch (DAOException ex) {
                    throw new IllegalArgumentException(ex.getMessage());
                }
            }
        }));

        entidadeProfissionalSync.addAtributo(new AtributoSync("N_REGISTRO", new AtributoImpl("nr_registro")));

        List<Entidade> entidades = new ArrayList<Entidade>();

        entidades.addAll(this.daoOrigem.loadSynchronizer(entidadeSync));
        entidades.addAll(this.daoOrigem.loadSynchronizer(entidadeProfissionalSync));

        return entidades;
    }

    @Override
    protected void entitiesInterceptor(List<Entidade> entidades) throws DAOException {
        boolean primeiraImportacao = daoDestino.existsProfissionalCargaHoraria();

        if(primeiraImportacao){
            for (Entidade entidade : entidades) {
                if("profissional_carga_horaria".equals(entidade.getNomeEntidade())){
                    ((List<AtributoSync>)entidade.getAtributos()).add(new AtributoSync(new AtributoImpl("dt_ativacao"), new ValorAtributoImpl(Data.getDataAtual())));
                }
            }
        }
    }

}
