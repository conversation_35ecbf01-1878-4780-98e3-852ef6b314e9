/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.vigilancia.estabelecimentoocorrencia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoOcorrencia;

public class CadastrarOcorrenciaEstabelecimento extends AbstractCommandTransaction<CadastrarOcorrenciaEstabelecimento> {

    private EstabelecimentoOcorrencia ocorrencia;
    private Estabelecimento estabelecimento;
    private String motivo;

    public CadastrarOcorrenciaEstabelecimento(String motivo, Estabelecimento estabelecimento) {
        this.motivo = motivo;
        this.estabelecimento = estabelecimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (this.motivo == null || this.motivo.trim().equals("")) {
            throw new ValidacaoException("É necessário adicionar um motivo para cadastrar uma Ocorrência!");
        }

        ocorrencia = new EstabelecimentoOcorrencia();
        ocorrencia.setDataOcorrencia(DataUtil.getDataAtual());
        ocorrencia.setDescricao(motivo);
        ocorrencia.setEstabelecimento(estabelecimento);

        if (getSessao() == null) {
            ocorrencia.setUsuario(new Usuario(Usuario.USUARIO_ADMINISTRADOR));
        } else {
            ocorrencia.setUsuario(getSessao().<Usuario>getUsuario());
        }

        ocorrencia = BOFactory.save(ocorrencia);
    }
}
