package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoevento;

import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoEvento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

/**
 *
 * <AUTHOR>
 */
public class SaveRequerimentoEvento extends SaveVO<RequerimentoEvento> {

    public SaveRequerimentoEvento(RequerimentoEvento vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        ConfiguracaoVigilancia cv = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();

        if (this.vo.getUsuarioCadastro() == null) {
            this.vo.setUsuarioCadastro(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getAnoBase() == null) {
            this.vo.setAnoBase(cv.getAnoBaseAlvara());
        }

        if (vo.getRequerimentoVigilancia().getCpfSolicitante() != null && !CpfCnpJValidator.CPFIsValid(StringUtil.getDigits(vo.getRequerimentoVigilancia().getCpfSolicitante()))) {
            throw new ValidacaoException(VigilanciaHelper.createMessageCpfInvalid(vo.getRequerimentoVigilancia().getProtocolo()));
        }
    }

}