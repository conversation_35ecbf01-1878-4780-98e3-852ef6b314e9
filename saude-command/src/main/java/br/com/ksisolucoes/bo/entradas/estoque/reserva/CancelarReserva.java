package br.com.ksisolucoes.bo.entradas.estoque.reserva;

import br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa.DecrementarEstoqueReservado;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.ConcurrentDAOException;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.Reserva;

public class CancelarReserva extends AbstractCommandTransaction {

    private Reserva reserva;
    private Double quantidadeCancelar;

    public CancelarReserva(Reserva reserva) {
        this.reserva = reserva;
    }
    
    public CancelarReserva(Reserva reserva, Double quantidadeCancelar) {
        this.reserva = reserva;
        this.quantidadeCancelar = quantidadeCancelar;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Long version = this.reserva.getVersion();
        
        Reserva reservaAtual = (Reserva) HibernateUtil.lockTable(Reserva.class, this.reserva.getCodigo());;
        
        if(! reservaAtual.getVersion().equals(version)){
            throw new ConcurrentDAOException();
        }

        if(! Reserva.STATUS_ABERTO.equals(reservaAtual.getStatus())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_somente_reserva_aberto_cancelar")+"\n"+reservaAtual.getDescricaoIdentificacao());
        }
                
        double saldoReserva = reservaAtual.getSaldoReserva();
        if(Coalesce.asDouble(quantidadeCancelar) <= 0){
            quantidadeCancelar = saldoReserva;
        }

        if (quantidadeCancelar > saldoReserva) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_quantidade_cancelar_maior_saldo")+"\n"+reservaAtual.getDescricaoIdentificacao()); 
        }
        reservaAtual.setMotivoConfirmacao(this.reserva.getMotivoConfirmacao());
        reservaAtual.setMotivoCancelamento(this.reserva.getMotivoCancelamento());
        reservaAtual.setQuantidadeCancelada(reservaAtual.getQuantidadeCancelada() + quantidadeCancelar);

        reservaAtual.setQuantidade(reservaAtual.getQuantidade() - quantidadeCancelar);

        new DecrementarEstoqueReservado(reservaAtual, this.quantidadeCancelar).start();
        
        BOFactory.save(reservaAtual);
    }
}