package br.com.ksisolucoes.bo.materiais.pedidotransferencia;

import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.ConcurrentDAOException;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferencia;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CancelarPedidoTransferencia extends AbstractCommandTransaction {

    private Long codigoPedidoTransferencia;
    private Long version;
    private OrigemProcessoPedido origemProcessoPedido;
    private String motivo;
    private List<Usuario> usuariosList;
    private StringBuilder mensagemBuilder = new StringBuilder();

    public CancelarPedidoTransferencia(Long codigoPedidoTransferencia, Long version, OrigemProcessoPedido origemProcessoPedido) {
        this.codigoPedidoTransferencia = codigoPedidoTransferencia;
        this.origemProcessoPedido = origemProcessoPedido;
        this.version = version;
    }

    public CancelarPedidoTransferencia(Long codigoPedidoTransferencia, Long version, OrigemProcessoPedido origemProcessoPedido, String motivo) {
        this.codigoPedidoTransferencia = codigoPedidoTransferencia;
        this.origemProcessoPedido = origemProcessoPedido;
        this.version = version;
        this.motivo = motivo;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        PedidoTransferencia pedidoTransferencia = (PedidoTransferencia) getSession().get(PedidoTransferencia.class, codigoPedidoTransferencia);
        if(! pedidoTransferencia.getVersion().equals(version)){
            throw new ConcurrentDAOException();
        }
        
        Criteria criteria = getSession().createCriteria(PedidoTransferenciaItem.class)
                    .add(Restrictions.eq(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA), pedidoTransferencia));
            
        List<PedidoTransferenciaItem> itens = criteria.list();

        List<DTOPedidoTransferenciaItem> dtoptis = new ArrayList<DTOPedidoTransferenciaItem>();
        
        for (PedidoTransferenciaItem pedidoTransferenciaItem : itens) {
            if (PedidoTransferenciaItem.StatusPedidoTransferenciaItem.SEPARANDO.value().equals(pedidoTransferenciaItem.getStatus())
                    || PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value().equals(pedidoTransferenciaItem.getStatus())) {
                BOFactory.getBO(PedidoTransferenciaFacade.class).cancelarPedidoTransferenciaItem(pedidoTransferenciaItem.getCodigo(), origemProcessoPedido, null);
            
                DTOPedidoTransferenciaItem dtopti = new DTOPedidoTransferenciaItem();
                dtopti.setPedidoTransferenciaItem(pedidoTransferenciaItem);

                dtoptis.add(dtopti);
            }
        }
        
        BOFactory.getBO(PedidoTransferenciaFacade.class).gerarLotesReservas(dtoptis);
        
        pedidoTransferencia.setStatus(PedidoTransferencia.STATUS_CANCELADO);
        pedidoTransferencia.setDataCancelamento(Data.getDataAtual());
        pedidoTransferencia.setUsuarioCancelamento(getSessao().<Usuario>getUsuario());
        if (motivo != null){
            pedidoTransferencia.setMotivoCancelamento(motivo);
            MensagemDTO mensagemDTO = new MensagemDTO();
            mensagemDTO.setAssunto(Bundle.getStringApplication("pedidoAlmoxarifado"));
            mensagemDTO.setUsuarios(Arrays.asList(pedidoTransferencia.getUsuario()));
            mensagemDTO.setMensagem(Bundle.getStringApplication("msgPedidoXCanceladoPeloAlmoxarifado", pedidoTransferencia.getCodigo()) + ". Motivo: " + pedidoTransferencia.getMotivoCancelamento());
            BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
        }

        BOFactory.getBO(CadastroFacade.class).save(pedidoTransferencia);
    }

}
