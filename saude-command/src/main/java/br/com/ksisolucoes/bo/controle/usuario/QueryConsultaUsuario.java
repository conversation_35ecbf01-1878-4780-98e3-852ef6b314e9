package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.basico.interfaces.dto.QueryConsultaUsuarioDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaUsuario extends CommandQueryPager<QueryConsultaUsuario> {

    private QueryConsultaUsuarioDTOParam param;

    public QueryConsultaUsuario(QueryConsultaUsuarioDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("u.codigo", true);
        hql.addToSelect("u.nome", true);
        hql.addToSelect("u.login", true);
        hql.addToSelect("p.codigo", "profissional.codigo");

        hql.setTypeSelect(Usuario.class.getName());
        hql.addToFrom("Usuario u"
                + " left join u.profissional p");

        hql.addToWhereWhithAnd("u.flagIdentificavel = ", param.getFlagIdentificavel());
        if (param.isFlagVinculoProfissional() != null && param.isFlagVinculoProfissional()) {
            hql.addToWhereWhithAnd("p.codigo is not null");
        }
        if(param.getTipoUsuario() == null) {
            hql.addToWhereWhithAnd("u.tipoUsuario in ", Arrays.asList(Usuario.TipoUsuario.USUARIO_SAUDE.value(), Usuario.TipoUsuario.USUARIO_INTEGRACAO.value()));
        } else {
            hql.addToWhereWhithAnd("u.tipoUsuario in ", Arrays.asList(Usuario.TipoUsuario.USUARIO_VIGILANCIA.value()));
        }
        hql.addToWhereWhithAnd("u.codigo = ", param.getCodigo());
        if (param.isFiltrarUsuariosAtivos()) {
            hql.addToWhereWhithAnd("u.status = ", Usuario.STATUS_ATIVO);
        }

        hql.addToWhereWhithAnd(hql.getConsultaLiked("u.nome", param.getNome()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("u.codigo || ' ' || u.nome", param.getKeyword()));

        if (param.getPropSort() != null) {
            hql.addToOrder("u." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("u.nome");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
