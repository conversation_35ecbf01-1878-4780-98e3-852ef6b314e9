package br.com.ksisolucoes.bo.materiais.pedidocompra;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompra;
import br.com.ksisolucoes.vo.materiais.pedidocompra.PedidoCompraItem;

import java.math.BigDecimal;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class PedidoCompraHelper {

    public PedidoCompraHelper() {
    }

    public static Long statusPedidoCompra(Long codigoPedidoCompra) throws DAOException, ValidacaoException {
        PedidoCompraItem proxy = on(PedidoCompraItem.class);

        List<PedidoCompraItem> list = LoadManager.getInstance(PedidoCompraItem.class)
                .addProperty(path(proxy.getStatus()))
                .addProperty(path(proxy.getQuantidadeRecebida()))
                .addParameter(new QueryCustomParameter(path(proxy.getPedidoCompra().getCodigo()), codigoPedidoCompra))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getStatus()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getQuantidadeRecebida()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();

        Long status = PedidoCompraItem.Status.CANCELADA.value();
        boolean considerarCancelados = true;
        String utilizaAutorizacaoFornecimentoDescritivo = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaAutorizacaoFornecimentoDescritivo");
        if (RepositoryComponentDefault.SIM.equals(utilizaAutorizacaoFornecimentoDescritivo)) {
            status = retornaStatusPedidoCompraPregao(list, status, considerarCancelados);
        } else {
            status = retornaStatusPedidoCompra(list, status, considerarCancelados);
        }
        return status;

    }

    private static Long retornaStatusPedidoCompra(List<PedidoCompraItem> list, Long status, boolean considerarCancelados) {
        Integer countCancelada = 0;
        Integer countRecebida = 0;
        Integer countParcial = 0;
        Integer countPendente = 0;
        for (PedidoCompraItem item : list) {
            if (PedidoCompraItem.Status.CANCELADA.value().equals(item.getStatus()) && considerarCancelados) {
                countCancelada++;
            } else if (PedidoCompraItem.Status.RECEBIDA.value().equals(item.getStatus())) {
                countRecebida++;
                considerarCancelados = false;
            } else if (PedidoCompraItem.Status.PENDENTE.value().equals(item.getStatus()) && Coalesce.asBigDecimal(item.getQuantidadeRecebida()).compareTo(new BigDecimal(0)) != 0) {
                countParcial++;
                break;
            } else if (PedidoCompraItem.Status.PENDENTE.value().equals(item.getStatus())) {
                countPendente++;
                considerarCancelados = false;
            }
        }
        if (countParcial > 0) {
            status = PedidoCompra.Status.PARCIAL.value();
        } else if (countCancelada > 0 && countPendente == 0 && countParcial == 0 && countRecebida == 0) {
            status = PedidoCompra.Status.CANCELADA.value();
        } else if (countRecebida > 0 && countPendente == 0 && countParcial == 0) {
            status = PedidoCompra.Status.RECEBIDA.value();
        } else if (countPendente > 0 && countRecebida == 0 && countParcial == 0) {
            status = PedidoCompra.Status.PENDENTE.value();
        } else {
            status = PedidoCompra.Status.PARCIAL.value();
        }
        return status;
    }

    private static Long retornaStatusPedidoCompraPregao(List<PedidoCompraItem> list, Long status, boolean considerarCancelados) {
        Integer countCancelada = 0;
        Integer countRecebida = 0;
        Integer countParcial = 0;
        Integer countPendente = 0;
        for (PedidoCompraItem item : list) {
            if (PedidoCompraItem.Status.CANCELADA.value().equals(item.getStatus()) && considerarCancelados) {
                countCancelada++;
            } else if (PedidoCompraItem.Status.RECEBIDA.value().equals(item.getStatus()) && Coalesce.asBigDecimal(item.getSaldo()).compareTo(BigDecimal.ZERO) <= 0) {
                countRecebida++;
                considerarCancelados = false;
            } else if (PedidoCompraItem.Status.PENDENTE.value().equals(item.getStatus()) && Coalesce.asBigDecimal(item.getQuantidadeRecebida()).compareTo(new BigDecimal(0)) != 0) {
                countParcial++;
                break;
            } else if (PedidoCompraItem.Status.PENDENTE.value().equals(item.getStatus())) {
                countPendente++;
                considerarCancelados = false;
            }
        }
        if (countParcial > 0) {
            status = PedidoCompra.Status.PARCIAL.value();
        } else if (countCancelada > 0 && countPendente == 0 && countParcial == 0 && countRecebida == 0) {
            status = PedidoCompra.Status.CANCELADA.value();
        } else if (countRecebida > 0 && countPendente == 0 && countParcial == 0) {
            status = PedidoCompra.Status.RECEBIDA.value();
        } else if (countPendente > 0 && countRecebida == 0 && countParcial == 0) {
            status = PedidoCompra.Status.PENDENTE.value();
        } else {
            status = PedidoCompra.Status.PARCIAL.value();
        }

        return status;
    }

}
