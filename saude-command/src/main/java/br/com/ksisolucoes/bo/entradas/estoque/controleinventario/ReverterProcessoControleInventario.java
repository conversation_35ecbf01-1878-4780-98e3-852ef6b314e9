/*
 * ReverterProcessoControleInventario.java
 *
 * Created on 8 de Setembro de 2006, 09:42
 *
 * To change this template, choose Tools | Template Manager
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.entradas.estoque.controleinventario;

import br.com.ksisolucoes.bo.entradas.estoque.movimentoestoque.SaveLancamentoMovimentoEstoque;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import org.hibernate.criterion.Restrictions;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ReverterProcessoControleInventario extends AbstractCommandTransaction {
    
    private List< Empresa > empresas;
    private Date date;
    private Inventario inventario;

    /** Creates a new instance of ReverterProcessoControleInventario */
    public ReverterProcessoControleInventario(List< Empresa > empresas, Date date, Inventario inventario) {
        this.empresas = empresas;
        this.date = date;
        this.inventario = inventario;
    }
    
    public void execute() throws DAOException, ValidacaoException {
        /*
         * Validao de parmetros obrigatrios.
         *---------------------------------------------------------------------*/
        RetornoValidacao retornoValidacao = new RetornoValidacao();
        Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();
        //Seta tipo de documento padro para inventrio, ou seja, tipo documento do tipo entrada
        TipoDocumento tipoDocumento = parametro.getTipoDocumentoEntrada();
        
        if ( tipoDocumento == null ){
            retornoValidacao.add( Bundle.getStringApplication( "msg_parametro_X_nao_definido", Bundle.getStringApplication( "rotulo_tipo_documento", this.sessao.getLocale() ) ), "parametro." + Parametro.PROP_TIPO_DOCUMENTO );
        }
        
        if ( !retornoValidacao.isValido() ){
            throw new ValidacaoException( retornoValidacao );
        }
        /*---------------------------------------------------------------------*/
        
        QueryProcessoControleInventario.Bean bean = new QueryProcessoControleInventario.Bean();
        bean.setEmpresas(this.empresas);
        bean.getStatus().add( ControleInventario.STATUS_PROCESSADO );
        bean.getStatus().add( ControleInventario.STATUS_ZERADO );
        bean.setDate( this.date );
        bean.setInventario(inventario);

        List< ControleInventario > controlesInventario = null;
        try {
            controlesInventario = new QueryProcessoControleInventario( bean ).start().getDtoList();
        } catch (SGKException ex) {
            throw new DAOException(ex);
        }
        
        Map<String, MovimentoEstoque> mValues = new HashMap<String, MovimentoEstoque>();
        for ( ControleInventario ci : controlesInventario ){
            ControleInventario controleInventario = ci;
            
            MovimentoEstoque movimentoEstoque = new MovimentoEstoque();
            movimentoEstoque.setProduto( controleInventario.getProduto() );
            MovimentoEstoquePK movimentoEstoquePK = new MovimentoEstoquePK();
            movimentoEstoquePK.setEmpresa( controleInventario.getId().getEmpresa() );
            movimentoEstoque.setId( movimentoEstoquePK );
            movimentoEstoque.setTipoDocumento( tipoDocumento );
            movimentoEstoque.setDeposito(controleInventario.getDeposito());
            movimentoEstoque.setGrupoEstoque(controleInventario.getGrupoEstoque());
            movimentoEstoque.setLocalizacaoEstrutura(controleInventario.getLocalizacaoEstrutura());
            movimentoEstoque.setDataPortaria(controleInventario.getDataInventario());
            
            movimentoEstoque.setQuantidade( controleInventario.getEstoqueFisico() );
            String chave = controleInventario.getId().getEmpresa().getCodigo().toString()+controleInventario.getDeposito().getCodigo().toString()+controleInventario.getGrupoEstoque()+controleInventario.getProduto().getCodigo()+controleInventario.getLocalizacaoEstrutura().getCodigo();
            if ( !mValues.containsKey( chave ) ){
                mValues.put( chave, movimentoEstoque );
            }
            
            if ( controleInventario.getStatus().equals( ControleInventario.STATUS_PROCESSADO ) ){
                ControleInventario contInv = (ControleInventario) getSession().createCriteria(ControleInventario.class)
                        .add(Restrictions.eq(VOUtils.montarPath(ControleInventario.PROP_ID, ControleInventarioPK.PROP_CODIGO), controleInventario.getId().getCodigo()))
                        .add(Restrictions.eq(VOUtils.montarPath(ControleInventario.PROP_ID, ControleInventarioPK.PROP_EMPRESA, Empresa.PROP_CODIGO), controleInventario.getId().getEmpresa().getCodigo()))
                        .uniqueResult();
                contInv.setStatus(ControleInventario.STATUS_ABERTO);
                contInv.setContagem1(null);
                contInv.setContagem2(null);
                contInv.setContagem3(null);
                contInv.setUltimaContagem(null);
            } else {
                new DeleteControleInventario( controleInventario ).start();
            }
        }

        if ( !mValues.isEmpty() ){
            LancamentoMovimentoEstoque lancamentoMovimentoEstoque = new LancamentoMovimentoEstoque();
            lancamentoMovimentoEstoque.getMovimentoEstoqueSet().addAll( mValues.values() );
            new SaveLancamentoMovimentoEstoque( lancamentoMovimentoEstoque ).start();
        }

        if (inventario != null && Inventario.Situacao.PROCESSADO.value().equals(inventario.getSituacao())) {
            inventario.setUsuarioProcessamento(null);
            inventario.setDataProcessamento(null);
            inventario.setSituacao(Inventario.Situacao.ABERTO.value());
            BOFactory.save(inventario);
        }
    }
    
    public List<Empresa> getEmpresas() {
        return empresas;
    }
    
    public void setEmpresas(List<Empresa> empresas) {
        this.empresas = empresas;
    }
    
    public Date getDate() {
        return date;
    }
    
    public void setDate(Date date) {
        this.date = date;
    }
    
}
