/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.programasaude.hiperdia;

import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.programasaude.Hiperdia;
import br.com.ksisolucoes.vo.programasaude.HiperdiaMedicamento;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaudeUsuario;
import java.util.List;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class RemoverHiperdia extends AbstractCommandTransaction {

    private Hiperdia hiperdia;

    public RemoverHiperdia(Hiperdia hiperdia) {
        this.hiperdia = hiperdia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<HiperdiaMedicamento> hiperdiaMedicamentosLoad = getSession().createCriteria(HiperdiaMedicamento.class)
                .add(Restrictions.eq(VOUtils.montarPath(HiperdiaMedicamento.PROP_HIPERDIA), this.hiperdia)).list();

        for (HiperdiaMedicamento hiperdiaMedicamento : hiperdiaMedicamentosLoad) {
            BOFactory.getBO(CadastroFacade.class).delete(hiperdiaMedicamento);
        }

        ProgramaSaudeUsuario programaSaudeUsuario = this.hiperdia.getProgramaSaudeUsuario();
        BOFactory.getBO(CadastroFacade.class).delete(this.hiperdia);

        Criteria cHiperdia = getSession().createCriteria(Hiperdia.class);
        cHiperdia.add(Restrictions.eq(VOUtils.montarPath(Hiperdia.PROP_PROGRAMA_SAUDE_USUARIO), programaSaudeUsuario));

        List<Hiperdia> hiperdiaList = cHiperdia.list();

        if (hiperdiaList.isEmpty()) {
            ProgramaSaudeUsuario psu = (ProgramaSaudeUsuario) getSession().get(ProgramaSaudeUsuario.class, programaSaudeUsuario.getCodigo());
            BOFactory.getBO(CadastroFacade.class).delete(psu);
        }
    }
}
