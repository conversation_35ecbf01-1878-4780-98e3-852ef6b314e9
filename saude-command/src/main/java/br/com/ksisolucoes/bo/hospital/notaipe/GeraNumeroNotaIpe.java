package br.com.ksisolucoes.bo.hospital.notaipe;

import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto.LancamentosProntoAtendimentoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.SequencialContaPaciente;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.hibernate.Criteria;
import org.hibernate.LockMode;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class GeraNumeroNotaIpe extends AbstractCommandTransaction {

    private List<ContaPaciente> listaContas;
    private List<ItemContaPaciente> listaLancamentos;
    private Long quantidadeItensPorNota;
    private List<Long> listaNotas = new ArrayList<Long>();

    public GeraNumeroNotaIpe(List<ContaPaciente> listaContas, Long quantidadeItensPorNota) {
        this.listaContas = listaContas;
        this.quantidadeItensPorNota = quantidadeItensPorNota;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        listaLancamentos = getListaLancamentos(listaContas);

        Long numeroNota = getNota(listaContas.get(0).getDataFechamento(), listaContas.get(0).getAtendimentoInformacao().getAtendimentoPrincipal().getConvenio());
        listaNotas.add(numeroNota);

        Long quant = new Long(0);
        for (int i = 0; i < listaLancamentos.size(); i++) {
            if (quant.equals(quantidadeItensPorNota)) {
                numeroNota = getNota(listaContas.get(0).getDataFechamento(), listaContas.get(0).getAtendimentoInformacao().getAtendimentoPrincipal().getConvenio());
                listaNotas.add(numeroNota);
                quant=new Long(0);
            }
            ItemContaPaciente icp = (ItemContaPaciente) getSession().get(ItemContaPaciente.class, listaLancamentos.get(i).getCodigo());
            icp.setNumeroNota(numeroNota);
            BOFactory.save(icp);
            quant++;
        };
    }

    private Long getNota(Date competenciaNota, Convenio convenio) throws DAOException, ValidacaoException {

        SequencialContaPaciente scp = getSequencialContaPaciente(Data.getDataParaPrimeiroDiaMes(competenciaNota), convenio);

        if (scp == null) {
            scp = new SequencialContaPaciente();
            scp.setMesAno(Data.getDataParaPrimeiroDiaMes(competenciaNota));
            scp.setConvenio(convenio);
            scp.setSequencial(1L);

        } else {
            scp.setSequencial(scp.getSequencial() + 1);
        }
        BOFactory.save(scp);

        return scp.getSequencial();
    }

    private SequencialContaPaciente getSequencialContaPaciente(Date mesAno, Convenio convenio) {

        Criteria cSequencial = getSession().createCriteria(SequencialContaPaciente.class);
        cSequencial.add(Restrictions.eq(SequencialContaPaciente.PROP_MES_ANO, mesAno));
        cSequencial.add(Restrictions.eq(SequencialContaPaciente.PROP_CONVENIO, convenio));
        cSequencial.setLockMode(LockMode.PESSIMISTIC_WRITE);

        SequencialContaPaciente sequencial = (SequencialContaPaciente) cSequencial.uniqueResult();

        return sequencial;
    }

    private List<ItemContaPaciente> getListaLancamentos(List<ContaPaciente> listaContas) {

        Criteria cLancamentos = getSession().createCriteria(ItemContaPaciente.class);
        cLancamentos.add(Restrictions.in(ItemContaPaciente.PROP_CONTA_PACIENTE, listaContas));
        cLancamentos.add(Restrictions.eq(ItemContaPaciente.PROP_STATUS, ItemContaPaciente.Status.CONFIRMADO.value()));

        return cLancamentos.list();
    }

    public List<Long> getListNotas() {
        return listaNotas;
    }
}
