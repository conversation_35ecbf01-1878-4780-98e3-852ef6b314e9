/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.frota.registromanutencao;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.RegistroManutencao;
import br.com.ksisolucoes.vo.frota.RegistroManutencaoItem;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DeleteRegistroManutencao extends DeleteVO<RegistroManutencao> {

    public DeleteRegistroManutencao(RegistroManutencao vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<RegistroManutencaoItem> itensExistentes = LoadManager.getInstance(RegistroManutencaoItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RegistroManutencaoItem.PROP_REGISTRO_MANUTENCAO), vo))
                .start().getList();
        for (RegistroManutencaoItem registroManutencaoItem : itensExistentes) {
            BOFactory.delete(registroManutencaoItem);
        }
    }
    
}
