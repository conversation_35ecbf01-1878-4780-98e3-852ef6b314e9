/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.requisicaopadrao;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadrao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoPadraoExame;
import java.util.ArrayList;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class SaveRequisicaoPadrao extends SaveVO<RequisicaoPadrao> {

    public SaveRequisicaoPadrao(RequisicaoPadrao vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(Data.getDataAtual());
        }
        if (this.vo.getDescricao() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido", Bundle.getStringApplication("rotulo_descricao")));
        }
        if (this.vo.getTipoExame() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido", Bundle.getStringApplication("rotulo_tipo_exame")));
        }
        
    }
    
    @Override
    protected void depoisSave() throws ValidacaoException, DAOException {
        if (this.vo.getRequisicaoPadraoExames() == null || this.vo.getRequisicaoPadraoExames().isEmpty()) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_obrigatorio_informar_itens"));
        }
        List<Long> newIds = new ArrayList();
        for (RequisicaoPadraoExame requisicaoPadraoExame : this.vo.getRequisicaoPadraoExames()) {
            requisicaoPadraoExame.setRequisicaoPadrao(this.vo);
            BOFactory.getBO(CadastroFacade.class).save(requisicaoPadraoExame);
            newIds.add(requisicaoPadraoExame.getCodigo());
        }

        if (!newIds.isEmpty()) {
            List<RequisicaoPadraoExame> requisicaoPadraoExamesForDelete = this.getSession().createCriteria(RequisicaoPadraoExame.class)
                    .add(Restrictions.eq(RequisicaoPadraoExame.PROP_REQUISICAO_PADRAO, this.vo))
                    .add(Restrictions.not(Restrictions.in(RequisicaoPadraoExame.PROP_CODIGO, newIds)))
                    .list();
            for (RequisicaoPadraoExame requisicaoPadraoExame : requisicaoPadraoExamesForDelete) {
                BOFactory.getBO(CadastroFacade.class).delete(requisicaoPadraoExame);
            }
        }
    }
    
}
