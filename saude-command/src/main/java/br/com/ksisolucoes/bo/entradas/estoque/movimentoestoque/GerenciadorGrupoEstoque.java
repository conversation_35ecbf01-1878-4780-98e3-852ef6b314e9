/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.entradas.estoque.movimentoestoque;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.estoque.EstoqueHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.entradas.estoque.Deposito;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.Fabricante;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoquePK;
import br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura;

import java.math.MathContext;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * Classe dimensionada a agrupar o estoque de um produto de varias formas: Geral, deposito, deposito e grupo
 * <AUTHOR>
 */
public class GerenciadorGrupoEstoque extends AbstractCommandTransaction {

    private EstoqueEmpresa estoqueEmpresa;
    private Long codigoDeposito;
    private String grupo;
    private List<GrupoEstoque> grupoEstoqueList;
    private GrupoEstoque grupoEstoqueDeposito;
    private GrupoEstoque grupoEstoqueDepositoGrupo;
    private Date dataValidade;
    private Fabricante fabricante;
    private LocalizacaoEstrutura localizacaoEstrutura;

    public GerenciadorGrupoEstoque(EstoqueEmpresa estoqueEmpresa, Long codigoDeposito, String grupo, Date dataValidade) {
        this(estoqueEmpresa, codigoDeposito, grupo);
        this.dataValidade = dataValidade;
    }
    
    public GerenciadorGrupoEstoque(EstoqueEmpresa estoqueEmpresa, Long codigoDeposito, String grupo, Date dataValidade, Fabricante fabricante, LocalizacaoEstrutura localizacaoEstrutura) {
        this(estoqueEmpresa, codigoDeposito, grupo);
        this.dataValidade = dataValidade;
        this.fabricante = fabricante;
        this.localizacaoEstrutura = localizacaoEstrutura;
    }
    public GerenciadorGrupoEstoque(EstoqueEmpresa estoqueEmpresa, Long codigoDeposito, String grupo) {
        this.estoqueEmpresa = estoqueEmpresa;
        this.codigoDeposito = codigoDeposito;
        this.grupo = grupo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        this.codigoDeposito = Coalesce.asLong(this.codigoDeposito, getCodigoDepositoPadrao(estoqueEmpresa.getId().getEmpresa()));
        this.grupo = Coalesce.asString(this.grupo, GrupoEstoque.GRUPO_ESTOQUE_PADRAO);
        if(localizacaoEstrutura == null){
            localizacaoEstrutura = EstoqueHelper.getLocalizacaoEstruturaPadrao();
        }

        QueryGrupoEstoqueProduto query = new QueryGrupoEstoqueProduto(this.estoqueEmpresa.getId().getEmpresa(), this.estoqueEmpresa.getId().getProduto());
        query.start();
        this.grupoEstoqueList = query.getGrupoEstoqueList();
    }

    public EstoqueEmpresa getEstoqueEmpresa() {
        return estoqueEmpresa;
    }

    public GrupoEstoque getGrupoEstoqueDeposito() throws ValidacaoException {
        if (this.grupoEstoqueDeposito == null) {
            this.grupoEstoqueDeposito = groupGrupoEstoque(this.codigoDeposito, null);
        }
        return this.grupoEstoqueDeposito;
    }

    public GrupoEstoque getGrupoEstoqueDepositoGrupo() throws ValidacaoException {
        if (this.grupoEstoqueDepositoGrupo == null) {
            this.grupoEstoqueDepositoGrupo = groupGrupoEstoque(this.codigoDeposito, this.grupo);
        }
        return this.grupoEstoqueDepositoGrupo;
    }

    private GrupoEstoque groupGrupoEstoque(Long codigoDeposito, String grupo) throws ValidacaoException {
        GrupoEstoque grupoEstoque = new GrupoEstoque();
        List<List<GrupoEstoque>> listGrupoEstoqueList = null;

        if (codigoDeposito != null && grupo != null && localizacaoEstrutura != null) {
            listGrupoEstoqueList = CollectionUtils.groupList(this.grupoEstoqueList, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_CODIGO_DEPOSITO), VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_GRUPO), VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_LOCALIZACAO_ESTRUTURA, LocalizacaoEstrutura.PROP_CODIGO));
        } else if (codigoDeposito != null && grupo != null) {
            listGrupoEstoqueList = CollectionUtils.groupList(this.grupoEstoqueList, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_CODIGO_DEPOSITO), VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_GRUPO));
        } else if (codigoDeposito != null){
            listGrupoEstoqueList = CollectionUtils.groupList(this.grupoEstoqueList, VOUtils.montarPath(GrupoEstoque.PROP_ID, GrupoEstoquePK.PROP_CODIGO_DEPOSITO));
        } else {
            listGrupoEstoqueList = Arrays.asList(this.grupoEstoqueList);
        }

        nivel:
        for (List<GrupoEstoque> list : listGrupoEstoqueList) {
            for (GrupoEstoque _grupoEstoque : list) {
                if (codigoDeposito != null) {
                    if (codigoDeposito.equals(_grupoEstoque.getId().getCodigoDeposito())) {
                        if (grupo != null) {
                            if (grupo.equals(_grupoEstoque.getId().getGrupo())) {
                                if (localizacaoEstrutura != null && localizacaoEstrutura.getCodigo() != null) {
                                    if (localizacaoEstrutura.getCodigo().equals(_grupoEstoque.getId().getLocalizacaoEstrutura().getCodigo())) {
                                        acumularGrupoEstoque(grupoEstoque, _grupoEstoque);
                                        break nivel;
                                    }
                                } else {
                                    acumularGrupoEstoque(grupoEstoque, _grupoEstoque);
                                }
                            }
                        } else {
                            acumularGrupoEstoque(grupoEstoque, _grupoEstoque);
                        }
                    }
                } else {
                    acumularGrupoEstoque(grupoEstoque, _grupoEstoque);
                }
            }
        }

        if (grupoEstoque.getId() == null) {
            grupoEstoque.setId(new GrupoEstoquePK(this.estoqueEmpresa, grupo, codigoDeposito, localizacaoEstrutura));
//            grupoEstoque.getId().setEstoqueEmpresa(estoqueEmpresa);
//            grupoEstoque.getId().setGrupo(grupo);
//            grupoEstoque.getId().setCodigoDeposito(codigoDeposito);
        }
        if (this.dataValidade!=null) {
            grupoEstoque.setDataValidade(this.dataValidade);
        }
        if(this.fabricante != null){
            grupoEstoque.setFabricante(this.fabricante);
            grupoEstoque.setLaboratorioFabricante(this.fabricante.getDescricao());
        }

        return grupoEstoque;
    }

    private void acumularGrupoEstoque(GrupoEstoque grupoEstoqueAcumulado, GrupoEstoque grupoEstoque){
        grupoEstoqueAcumulado.setId(grupoEstoque.getId());
//        grupoEstoqueAcumulado.getId().setEstoqueEmpresa(this.estoqueEmpresa);
//        grupoEstoqueAcumulado.getId().setCodigoDeposito(grupoEstoque.getId().getCodigoDeposito());
//        grupoEstoqueAcumulado.getId().setGrupo(grupoEstoque.getId().getGrupo());

        if (grupoEstoqueAcumulado.getDataValidade()== null) {
            grupoEstoqueAcumulado.setDataValidade(grupoEstoque.getDataValidade());
        }

        grupoEstoqueAcumulado.setEstoqueDevolucao(new Dinheiro(Coalesce.asDouble(grupoEstoqueAcumulado.getEstoqueDevolucao()), new MathContext(4, Dinheiro.ROUND_DEFAULT)).somar(Coalesce.asDouble(grupoEstoque.getEstoqueDevolucao())).doubleValue());
        grupoEstoqueAcumulado.setEstoqueEncomendado(new Dinheiro(Coalesce.asDouble(grupoEstoqueAcumulado.getEstoqueEncomendado()), new MathContext(4, Dinheiro.ROUND_DEFAULT)).somar(Coalesce.asDouble(grupoEstoque.getEstoqueEncomendado())).doubleValue());
        grupoEstoqueAcumulado.setEstoqueFisico(new Dinheiro(Coalesce.asDouble(grupoEstoqueAcumulado.getEstoqueFisico()), new MathContext(4, Dinheiro.ROUND_DEFAULT)).somar(Coalesce.asDouble(grupoEstoque.getEstoqueFisico())).doubleValue());
        grupoEstoqueAcumulado.setEstoqueNaoConforme(new Dinheiro(Coalesce.asDouble(grupoEstoqueAcumulado.getEstoqueNaoConforme()), new MathContext(4, Dinheiro.ROUND_DEFAULT)).somar(Coalesce.asDouble(grupoEstoque.getEstoqueNaoConforme())).doubleValue());
        grupoEstoqueAcumulado.setEstoqueReservado(new Dinheiro(Coalesce.asDouble(grupoEstoqueAcumulado.getEstoqueReservado()), new MathContext(4, Dinheiro.ROUND_DEFAULT)).somar(Coalesce.asDouble(grupoEstoque.getEstoqueReservado())).doubleValue());
        grupoEstoqueAcumulado.setEstoqueReservadoDevolucao(new Dinheiro(Coalesce.asDouble(grupoEstoqueAcumulado.getEstoqueReservadoDevolucao()), new MathContext(4, Dinheiro.ROUND_DEFAULT)).somar(Coalesce.asDouble(grupoEstoque.getEstoqueReservadoDevolucao())).doubleValue());

//        if (Coalesce.asLong(grupoEstoque.getNumeroUltimoMovimento()) > Coalesce.asLong(grupoEstoqueAcumulado.getNumeroUltimoMovimento())) {
//            grupoEstoqueAcumulado.setNumeroUltimoMovimento(grupoEstoque.getNumeroUltimoMovimento());
//        }
//        }
    }

    public void merge(GrupoEstoque grupoEstoque, GrupoEstoque grupoEstoqueSession){
        grupoEstoqueSession.setDataValidade(grupoEstoque.getDataValidade());

        grupoEstoqueSession.setEstoqueDevolucao(grupoEstoque.getEstoqueDevolucao());
        grupoEstoqueSession.setEstoqueEncomendado(grupoEstoque.getEstoqueEncomendado());
        grupoEstoqueSession.setEstoqueFisico(grupoEstoque.getEstoqueFisico());
        grupoEstoqueSession.setEstoqueNaoConforme(grupoEstoque.getEstoqueNaoConforme());
        grupoEstoqueSession.setEstoqueReservado(grupoEstoque.getEstoqueReservado());
        grupoEstoqueSession.setEstoqueReservadoDevolucao(grupoEstoque.getEstoqueReservadoDevolucao());
        if(grupoEstoque.getFabricante() != null){
            grupoEstoqueSession.setFabricante(grupoEstoque.getFabricante());
            grupoEstoqueSession.setLaboratorioFabricante(grupoEstoque.getFabricante().getDescricao());
        }
    }

    private Long getCodigoDepositoPadrao(Empresa empresa) throws DAOException, ValidacaoException {
        EmpresaMaterial em = LoadManager.getInstance(EmpresaMaterial.class)
                .addParameter(new QueryCustomParameter(EmpresaMaterial.PROP_CODIGO,empresa.getCodigo()))
                .addProperty(VOUtils.montarPath(EmpresaMaterial.PROP_DEPOSITO,Deposito.PROP_CODIGO))
                .start().getVO();

        if (em.getDeposito() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_deve_ser_definido_deposito_padrao_cadastro_empresa"));
        }

        return em.getDeposito().getCodigo();
    }
}
