/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.cadsus.usuariocadsusdocumento;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.TipoDocumentoUsuario;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarUsuarioCadsusDocumentoImportacao extends AbstractCommandTransaction {

    private final UsuarioCadsusDocumento usuarioCadsusDocumento;
    private UsuarioCadsus usuarioCadsus;

    public SalvarUsuarioCadsusDocumentoImportacao(UsuarioCadsusDocumento usuarioCadsusDocumento, UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
        this.usuarioCadsusDocumento = usuarioCadsusDocumento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (usuarioCadsusDocumento.getCodigo() == null) {
            TipoDocumentoUsuario tipoDocumentoUsuario = usuarioCadsusDocumento.getTipoDocumento();
            List<TipoDocumentoUsuario> tipoDocumentoUsuarios = new ArrayList<>();

            if (TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO.equals(tipoDocumentoUsuario.getCodigo())
                    || TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO.equals(tipoDocumentoUsuario.getCodigo())) {

                tipoDocumentoUsuarios.add(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_NASCIMENTO));
                tipoDocumentoUsuarios.add(new TipoDocumentoUsuario(TipoDocumentoUsuario.TIPO_DOCUMENTO_CERTIDAO_CASAMENTO));
            } else {
                tipoDocumentoUsuarios.add(tipoDocumentoUsuario);
            }

            List<UsuarioCadsusDocumento> loadDocumentos = LoadManager.getInstance(UsuarioCadsusDocumento.class)
                    .addProperties(new HQLProperties(UsuarioCadsusDocumento.class).getProperties())
                    .addProperties(new HQLProperties(TipoDocumentoUsuario.class, UsuarioCadsusDocumento.PROP_TIPO_DOCUMENTO).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDocumento.PROP_TIPO_DOCUMENTO, BuilderQueryCustom.QueryParameter.IN, tipoDocumentoUsuarios))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDocumento.PROP_SITUACAO_EXCLUIDO, UsuarioCadsusDocumento.STATUS_ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS, usuarioCadsus))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(loadDocumentos)) {
                for (UsuarioCadsusDocumento item : loadDocumentos) {
                    item.setSituacaoExcluido(UsuarioCadsusDocumento.STATUS_EXCLUIDO);
                    BOFactory.getBO(CadastroFacade.class).save(item);
                }
            }
        }

        BOFactory.getBO(CadastroFacade.class).save(usuarioCadsusDocumento);
    }
}
