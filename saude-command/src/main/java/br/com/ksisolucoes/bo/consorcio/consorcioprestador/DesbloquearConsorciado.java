package br.com.ksisolucoes.bo.consorcio.consorcioprestador;

/**
 * <AUTHOR>
 */

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;

import java.util.List;

public class DesbloquearConsorciado extends AbstractCommandTransaction {

    @Override
    public void execute() throws DAOException, ValidacaoException {

        List<Empresa> empresaList = LoadManager.getInstance(Empresa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_SITUACAO_BLOQUEIO, Empresa.SituacaoBloqueio.BLOQUEADO_PERIODO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_TIPO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO))
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_DATA_BLOQUEIO_FINAL, QueryCustom.QueryCustomParameter.MENOR, DataUtil.getDataAtualSemHora()))
                .start().getList();

        BOFactory.getBO(ConsorcioFacade.class).salvarOcorrenciaConsorciado(empresaList, null, null, "Desbloqueado pelo agendador de processos", false);
    }
}
