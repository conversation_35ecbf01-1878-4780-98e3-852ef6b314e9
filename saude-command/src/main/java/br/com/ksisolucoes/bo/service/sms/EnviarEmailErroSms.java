package br.com.ksisolucoes.bo.service.sms;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Email;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class EnviarEmailErroSms extends AbstractCommandTransaction {

    private Throwable ex;

    public EnviarEmailErroSms(Throwable ex) {
        this.ex = ex;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        // Quando houver erro no envio do sms, o processo é abortado e então é enviada uma mensagem interna (Caixa de mensagem) para ao responsável pelo agendamento
        String emails = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("emailAvisoErro");
        Email email = Email.create();
        email.assunto(Bundle.getStringApplication("msg_erro_envio_sms_confirmacao_agendamento"));
        email.para(emails);
        StringBuilder mensagem = new StringBuilder();
        Empresa empresa = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("UnidadePadraoAgendadorProcessos");

        mensagem.append("Erro gerado em ").append(new SimpleDateFormat("dd/MM/yyyy HH:mm").format(new Date())).append("hs.");
        mensagem.append("\n");
        if (empresa != null) {
            mensagem.append("Cidade: ").append(empresa.getCidade().getDescricao());
        } else {
            mensagem.append("Parâmetro UnidadePadraoAgendadorProcessos não configurado");
        }
        mensagem.append("\n");
        mensagem.append("Deve-se conferir os parâmetros da url, usuário, senha, chave do SMS e garantir que tenha saldo para enviar as mensagens");
        mensagem.append("\n");
        mensagem.append("\n");

        if (ex instanceof DAOException) { // Se o erro for proveniente de uma transação, deve registrar a mensagem obtida da exception
            mensagem.append(Bundle.getStringApplication("msg_sistema_encontrou_erro_enviar_sms_erro_X", ex.getCause() != null ? ex.getCause().getMessage() : ex.getMessage()));
            mensagem.append("\n").append(Arrays.toString(ex.getStackTrace()));
        } else { // Se for qualquer outro erro, registra uma mensagem pré-definida e a stacktrace
            mensagem.append(Bundle.getStringApplication("msg_ocorreu_erro_durante_envio_sms_erro_X", ex));
        }
        email.mensagem(mensagem.toString());

        try {
            BOFactory.getBO(CommomFacade.class).enviarEmailNovaTransacao(email);
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }
    }
}
