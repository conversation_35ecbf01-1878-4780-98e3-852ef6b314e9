package br.com.ksisolucoes.bo.basico.pesquisa;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.basico.pesquisa.dto.CadastroPesquisaDTO;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa;
import br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta;
import java.util.ArrayList;
import java.util.List;
import org.apache.xmlbeans.impl.xb.xsdschema.RestrictionDocument;

/**
 *
 * <AUTHOR>
 */
public class SalvarPesquisa extends AbstractCommandTransaction {

    private CadastroPesquisaDTO dto;

    public SalvarPesquisa(CadastroPesquisaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        if (dto.getPesquisa().getDataCadastro() == null) {
            dto.getPesquisa().setDataCadastro(DataUtil.getDataAtual());
        }
        if (dto.getPesquisa().getUsuario() == null) {
            dto.getPesquisa().setUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        }

        BOFactory.save(dto.getPesquisa());

        List<PesquisaPergunta> listaPesquisaPergunta = new ArrayList();

        for (PerguntaPesquisa pp : dto.getPerguntas()) {
            PesquisaPergunta pesquisaPergunta;
            PesquisaPergunta pesquisaPerguntaBanco = (PesquisaPergunta) getSession().createCriteria(PesquisaPergunta.class)
                    .add(Restrictions.eq(PesquisaPergunta.PROP_PESQUISA, dto.getPesquisa()))
                    .createAlias(PesquisaPergunta.PROP_PERGUNTA_PESQUISA, PesquisaPergunta.PROP_PERGUNTA_PESQUISA)
                    .add(Restrictions.eq(VOUtils.montarPath(PesquisaPergunta.PROP_PERGUNTA_PESQUISA, PerguntaPesquisa.PROP_CODIGO), pp.getCodigo()))
                    .uniqueResult();
            if (pesquisaPerguntaBanco == null) {
                pesquisaPergunta = new PesquisaPergunta();
                pesquisaPergunta.setPesquisa(dto.getPesquisa());
                pesquisaPergunta.setPerguntaPesquisa(pp);
            } else {
                pesquisaPergunta = pesquisaPerguntaBanco;
            }
            listaPesquisaPergunta.add(pesquisaPergunta);
        }
        VOUtils.persistirListaVosModificados(PesquisaPergunta.class, listaPesquisaPergunta, new QueryCustom.QueryCustomParameter(PesquisaPergunta.PROP_PESQUISA, dto.getPesquisa()));
    }

}
