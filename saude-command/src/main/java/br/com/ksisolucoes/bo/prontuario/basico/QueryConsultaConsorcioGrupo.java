/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaConsorcioGrupoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGrupo;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaConsorcioGrupo extends CommandQueryPager<QueryConsultaConsorcioGrupo> {

    private QueryConsultaConsorcioGrupoDTOParam param;

    public QueryConsultaConsorcioGrupo(QueryConsultaConsorcioGrupoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("consorcioGrupo.codigo",true);
        hql.addToSelect("consorcioGrupo.descricao",true);

        hql.setTypeSelect(ConsorcioGrupo.class.getName());
        hql.addToFrom("ConsorcioGrupo consorcioGrupo");
        
        hql.addToWhereWhithAnd("consorcioGrupo.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("consorcioGrupo.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("consorcioGrupo.codigo || ' ' || consorcioGrupo.descricao",param.getKeyword()));
        
        hql.addToOrder("consorcioGrupo.descricao");
        
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String,Object>>)result);
    }

}
