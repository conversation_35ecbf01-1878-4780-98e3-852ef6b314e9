package br.com.ksisolucoes.bo.vigilancia.vigilanciaprofissional;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaProfissionalVigilanciaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultarVigilanciaProfissional extends CommandQueryPager<QueryConsultarVigilanciaProfissional> {

    private ConsultaProfissionalVigilanciaDTOParam param;

    public QueryConsultarVigilanciaProfissional(ConsultaProfissionalVigilanciaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("vigilanciaProfissional.codigo", "codigo");
        hql.addToSelect("vigilanciaProfissional.nomeProfissional", "nomeProfissional");
        hql.addToSelect("vigilanciaProfissional.nomeDiretor", "nomeDiretor");
        hql.addToSelect("vigilanciaProfissional.descricaoEspecialidade", "descricaoEspecialidade");
        hql.addToSelect("vigilanciaProfissional.descricaoSala", "descricaoSala");
        hql.addToSelect("vigilanciaProfissional.numeroRegistro", "numeroRegistro");
        hql.addToSelect("vigilanciaProfissional.uf", "uf");
        hql.addToSelect("vigilanciaProfissional.numeroCadastroVigilanciaSanitaria", "numeroCadastroVigilanciaSanitaria");
        hql.addToSelect("vigilanciaProfissional.telefone1", "telefone1");
        hql.addToSelect("vigilanciaProfissional.telefone2", "telefone2");
        hql.addToSelect("vigilanciaProfissional.celular", "celular");
        hql.addToSelect("vigilanciaProfissional.dataCadastro", "dataCadastro");
        hql.addToSelect("vigilanciaProfissional.version", "version");
        hql.addToSelect("vigilanciaProfissional.numeroEndereco", "numeroEndereco");
        hql.addToSelect("vigilanciaProfissional.cpf", "cpf");
        hql.addToSelect("vigilanciaProfissional.rg", "rg");
        hql.addToSelect("vigilanciaProfissional.dataEmissaoRg", "dataEmissaoRg");
        hql.addToSelect("vigilanciaProfissional.emiteReceita", "emiteReceita");
        hql.addToSelect("vigilanciaProfissional.flagSolicReceitaB1", "flagSolicReceitaB1");
        hql.addToSelect("vigilanciaProfissional.flagSolicReceitaB2", "flagSolicReceitaB2");
        hql.addToSelect("vigilanciaProfissional.flagSolicReceitaC2", "flagSolicReceitaC2");
        hql.addToSelect("vigilanciaProfissional.flagSolicReceitaTalidomida", "flagSolicReceitaTalidomida");

        hql.addToSelect("vigilanciaEndereco.codigo", "profissionalEndereco.vigilanciaEndereco.codigo");
        hql.addToSelect("vigilanciaEndereco.cep", "profissionalEndereco.vigilanciaEndereco.cep");
        hql.addToSelect("vigilanciaEndereco.bairro", "profissionalEndereco.vigilanciaEndereco.bairro");
        hql.addToSelect("vigilanciaEndereco.logradouro", "profissionalEndereco.vigilanciaEndereco.logradouro");
        hql.addToSelect("vigilanciaEndereco.dataAlteracao", "profissionalEndereco.vigilanciaEndereco.dataAlteracao");
        hql.addToSelect("vigilanciaEndereco.dataCadastro", "profissionalEndereco.vigilanciaEndereco.dataCadastro");

        hql.addToSelect("profissionalEndereco.codigo", "profissionalEndereco.codigo");
        hql.addToSelect("profissionalEndereco.numero", "profissionalEndereco.numero");
        hql.addToSelect("profissionalEndereco.complemento", "profissionalEndereco.complemento");

        hql.addToSelect("cidade.codigo", "profissionalEndereco.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("cidade.descricao", "profissionalEndereco.vigilanciaEndereco.cidade.descricao");

        hql.addToSelect("estado.codigo", "profissionalEndereco.vigilanciaEndereco.cidade.estado.codigo");
        hql.addToSelect("estado.sigla", "profissionalEndereco.vigilanciaEndereco.cidade.estado.sigla");
        hql.addToSelect("estado.descricao", "profissionalEndereco.vigilanciaEndereco.cidade.estado.descricao");
        
        hql.addToSelect("usuarioCadastro.codigo", "usuarioCadastro.codigo");
        hql.addToSelect("usuarioCadastro.nome", "usuarioCadastro.nome");

        hql.addToSelect("cbo.cbo", "cbo.cbo");
        hql.addToSelect("cbo.descricao", "cbo.descricao");
        
        hql.addToFrom("VigilanciaProfissional vigilanciaProfissional"
                + " left join vigilanciaProfissional.profissionalEndereco profissionalEndereco"
                + " left join profissionalEndereco.vigilanciaEndereco vigilanciaEndereco"
                + " left join vigilanciaEndereco.cidade cidade"
                + " left join cidade.estado estado"
                + " left join vigilanciaProfissional.usuarioCadastro usuarioCadastro"
                + " left join vigilanciaProfissional.cbo cbo");

        hql.addToWhereWhithAnd(hql.getConsultaLiked("vigilanciaProfissional.nomeProfissional", param.getNomeProfissional()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("vigilanciaProfissional.nomeDiretor", param.getNomeDiretor()));
        hql.addToWhereWhithAnd("vigilanciaProfissional.numeroRegistro = ", param.getNumeroRegistro());
        hql.addToWhereWhithAnd("vigilanciaProfissional.profissionalEndereco.vigilanciaEndereco = ", param.getVigilanciaEndereco());
        
        if (param.getSortProp() != null) {
            hql.addToOrder(param.getSortProp() + " " + (param.isAscending() ? "asc" : "desc"));
        }
        
        hql.setTypeSelect(VigilanciaProfissional.class.getName());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
