/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.vigilancia.responsaveltecnico;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SaveResponsavelTecnico extends SaveVO<ResponsavelTecnico> {

    public SaveResponsavelTecnico(ResponsavelTecnico vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (vo.getNome() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_campo_X_deve_ser_definido",Bundle.getStringApplication("rotulo_nome")));
        }
        if(vo.getDataEmissaoRg() != null && Data.adjustRangeHour(vo.getDataEmissaoRg()).getDataInicial().after(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())){
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_emissao_rg_nao_pode_ser_maior_que_data_atual"));
        }
        
        if (vo.getDataCadastro() == null) {
            vo.setDataCadastro(Data.getDataAtual());
        }
        
        vo.setDataUsuario(Data.getDataAtual());
        vo.setUsuario(getSessao().<Usuario>getUsuario());

        if (this.vo.getCpf() != null) {
            if (!CpfCnpJValidator.CPFIsValid(StringUtilKsi.getDigits(vo.getCpf()))) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_cpf_invalido_responsavel_tecnico"));
            }

            List<ResponsavelTecnico> responsavelTecnico = LoadManager.getInstance(ResponsavelTecnico.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(ResponsavelTecnico.PROP_CPF, this.vo.getCpf()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ResponsavelTecnico.PROP_CODIGO, QueryParameter.DIFERENTE,this.vo.getCodigo()))
                    .start().getList();
            
            if (CollectionUtils.isNotNullEmpty(responsavelTecnico)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_responsavel_tecnico_com_cpf",vo.getCpf(),vo.getNome()));
            }
        }
    }
}
