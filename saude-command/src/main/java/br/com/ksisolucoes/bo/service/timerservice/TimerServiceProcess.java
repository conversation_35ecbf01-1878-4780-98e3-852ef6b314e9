package br.com.ksisolucoes.bo.service.timerservice;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.service.timerservice.TimerName;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.*;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.bo.prontuario.emergencia.interfaces.facade.ProntuarioEventosFacade;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoContext;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoServidor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.agendadorprocessosutil.AgendadorProcessosUtil;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AgendadorProcesso;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ParametroAtendimento;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.ProcessoAvaliacaoEstoque;
import br.com.ksisolucoes.vo.vigilancia.agravo.IntegracaoAgravoNotificacao;

import java.util.Date;
import java.util.GregorianCalendar;

/**
 * <AUTHOR>
 */
public class TimerServiceProcess extends AbstractCommandTransaction<TimerServiceProcess> {

    private final TimerName timerName;
    private String descTimeService = "TIMER-SERVICE: ";

    public TimerServiceProcess(TimerName timer) {
        this.timerName = timer;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Usuario usuario = (Usuario) getSession().get(Usuario.class, Usuario.USUARIO_ADMINISTRADOR);
        Empresa empresa = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("UnidadePadraoAgendadorProcessos");

        SessaoAplicacaoServidor sessaoAplicacao = SessaoAplicacaoServidor.getNewInstance(usuario, empresa, Bundle.getLocale());
        //Executar getUsuario() para evitar chamada recursiva do filtro LGPD
        sessaoAplicacao.getUsuario();
        SessaoAplicacaoContext.setContext(sessaoAplicacao);

        if (TimerName.SMS_ATUALIZACAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.SMS_ATUALIZACAO);
            BOFactory.getBO(SmsFacade.class).atualizarMensagensSms();
            BOFactory.getBO(SmsFacade.class).consultarRespostasSms();

        } else if (TimerName.ENVIO_SMS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIO_SMS);
            BOFactory.getBO(AgendamentoFacade.class).processoEnvioSMS();

        } else if (TimerName.CONFIRMACAO_PRESENCA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CONFIRMACAO_PRESENCA);
            BOFactory.getBO(SmsFacade.class).enviarSmsConfirmacaoPresencaAgenda();

        } else if (TimerName.REENVIO_AVISO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.REENVIO_AVISO);
            BOFactory.getBO(SmsFacade.class).enviarSmsReenvioAvisoAgendamento();

        } else if (TimerName.RESPOSTA_SMS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.RESPOSTA_SMS);
            BOFactory.getBO(AgendamentoFacade.class).processoRespostaSMS();

        } else if (TimerName.RETORNO_SMS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.RETORNO_SMS);
            BOFactory.getBO(AgendamentoFacade.class).processoRetornoSMS();

        } else if (TimerName.AVISO_AGENDAMENTOS_LOCAIS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.AVISO_AGENDAMENTOS_LOCAIS);
            BOFactory.getBO(AgendamentoFacade.class).processoAvisoAgendamentosLocaisSMS();

        } else if (TimerName.AVALIACAO_ESTOQUE_MINIMO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.AVALIACAO_ESTOQUE_MINIMO);
            BOFactory.getBO(EstoqueEmpresaFacade.class).processarConfiguracoesAvaliacaoEstoque(ProcessoAvaliacaoEstoque.TipoProcesso.ESTOQUE_MINIMO);
        } else if (TimerName.VALIDADE_PRODUTO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.VALIDADE_PRODUTO);
            BOFactory.getBO(EstoqueEmpresaFacade.class).processarConfiguracoesAvaliacaoEstoque(ProcessoAvaliacaoEstoque.TipoProcesso.VALIDADE_PRODUTO);
        } else if (TimerName.CID_NOTIFICAVEL.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CID_NOTIFICAVEL);
            BOFactory.getBO(BasicoFacade.class).processarConfiguracoesCidNotificavel(getDataInicio());

        } else if (TimerName.METAS_CADASTRO_FAMILIA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.METAS_CADASTRO_FAMILIA);
            BOFactory.getBO(BasicoFacade.class).processarMetasCadastroFamilia();

        } else if (TimerName.AVISO_AGENDAMENTOS_CANCELADOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.AVISO_AGENDAMENTOS_CANCELADOS);
            BOFactory.getBO(AgendamentoFacade.class).processoAvisoAgendamentosCanceladosSMS();

        } else if (TimerName.AVISO_AGENDAMENTOS_REMANEJADOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.AVISO_AGENDAMENTOS_REMANEJADOS);
            BOFactory.getBO(AgendamentoFacade.class).processoAvisoAgendamentosRemanejadosSMS();

        } else if (TimerName.REENVIO_MENSAGENS_SMS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.REENVIO_MENSAGENS_SMS);
            BOFactory.getBO(SmsFacade.class).processarReenvioMensagensSms();

        } else if (TimerName.GERA_AGENDAMENTO_ENCAMINHAMENTO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.GERA_AGENDAMENTO_ENCAMINHAMENTO);
            BOFactory.getBO(AgendamentoFacade.class).processarAgendamentoEncaminhamentosLote();

        } else if (TimerName.PACIENTES_DUPLICADOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.PACIENTES_DUPLICADOS);
            BOFactory.getBO(BasicoFacade.class).processarPacienteDuplicado();

        } else if (TimerName.CONSULTAR_VALIDACAO_XML_HORUS.equals(timerName)) {
//            Removido temporáriamente, será feito posteriormente a atualização dos novos WS
//            Loggable.log.info(descricaoTimeService + TimerName.CONSULTAR_VALIDACAO_XML_HORUS);
//            BOFactory.getBO(MaterialBasicoFacade.class).consultarValidacaoXmlHorus();
        } else if (TimerName.CANCELAMENTO_DAS_GUIAS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CANCELAMENTO_DAS_GUIAS);
            BOFactory.getBO(ConsorcioFacade.class).cancelarGuiaProcedimento(true);

        } else if (TimerName.NOTIFICACOES_PACIENTE.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACOES_PACIENTE);
            BOFactory.getBO(UsuarioCadsusFacade.class).atualizarNotificacoesUsuarioCadsus();

        } else if (TimerName.VACINAS_APLICADAS_ACS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.VACINAS_APLICADAS_ACS);
            BOFactory.getBO(VacinaFacade.class).notificarVacinasAplicadasAgentesComunitarias();

        } else if (TimerName.IMPORTACAO_SIGTAP.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.IMPORTACAO_SIGTAP);
            BOFactory.getBO(ProntuarioEventosFacade.class).importarRegistrosSigtapAgendadorProcesso();
        } else if (TimerName.INATIVAR_PROFISSIONAIS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.INATIVAR_PROFISSIONAIS);
            BOFactory.getBO(ProfissionalFacade.class).inativarProfissionais();
        } else if (TimerName.RELACAO_GESTANTES.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.RELACAO_GESTANTES);
            BOFactory.getBO(EstoqueEmpresaFacade.class).processarConfiguracoesAvaliacaoEstoque(ProcessoAvaliacaoEstoque.TipoProcesso.RELACAO_GESTANTES);
        } else if (TimerName.NOTIFICACAO_SOLICITACOES_AGENDADAS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_SOLICITACOES_AGENDADAS);
            BOFactory.getBO(UsuarioFacade.class).notificarSolicitacoesAgendadas(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_AGENDADAS.getValue()), false);
        } else if (TimerName.NOTIFICACAO_SOLICITACOES_DEVOLVIDAS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_SOLICITACOES_DEVOLVIDAS);
            BOFactory.getBO(UsuarioFacade.class).notificarSolicitacoesDevolvidas(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_DEVOLVIDAS.getValue()), false);
        } else if (TimerName.ENVIO_MENSAGEM_LAUDO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIO_MENSAGEM_LAUDO);
            BOFactory.getBO(AgendamentoFacade.class).enviaMensagemLaudoDisponivel();
//Inovamfri
        } else if (TimerName.NOTIFICACAO_SOLICITACOES_NEGADAS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_SOLICITACOES_NEGADAS);
            BOFactory.getBO(UsuarioFacade.class).notificarSolicitacoesNegada(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.NOTIFICACAO_SOLICITACOES_NEGADAS.getValue()), false);
//Inovamfri
        } else if (TimerName.VACINA_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.VACINA_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarVacina(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.VACINA_INTEGRACAO_INOVAMFRI.getValue()));
        } else if (TimerName.ATENDIMENTO_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATENDIMENTO_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarAtendimento(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.ATENDIMENTO_INTEGRACAO_INOVAMFRI.getValue()));
        } else if (TimerName.ESTOQUE_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ESTOQUE_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarEstoque(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_INOVAMFRI.getValue()));
        } else if (TimerName.AGENDAMENTO_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.AGENDAMENTO_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarAgendamento(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_INOVAMFRI.getValue()));
        } else if (TimerName.TFD_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.TFD_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarTfd(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.TFD_INTEGRACAO_INOVAMFRI.getValue()));
        } else if (TimerName.EXAME_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.EXAME_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarExame(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.EXAME_INTEGRACAO_INOVAMFRI.getValue()));
        } else if (TimerName.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarDispensacaoMedicamentos(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_INOVAMFRI.getValue()));
        } else if (TimerName.FILA_ESPERA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.FILA_ESPERA);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarFilaEspera(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.FILA_ESPERA.getValue()));
        } else if (TimerName.GERAIS_INTEGRACAO_INOVAMFRI.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.GERAIS_INTEGRACAO_INOVAMFRI);
            BOFactory.getBO(IntegracaoInovamfriFacade.class).gerarUnidades(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.GERAIS_INTEGRACAO_INOVAMFRI.getValue()));
//CS-Cidadao
        } else if (TimerName.VACINA_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.VACINA_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarVacina(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.VACINA_INTEGRACAO_CS_CIDADAO.getValue()));
        } else if (TimerName.ATENDIMENTO_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATENDIMENTO_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarAtendimento(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.ATENDIMENTO_INTEGRACAO_CS_CIDADAO.getValue()));
        } else if (TimerName.ESTOQUE_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ESTOQUE_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarEstoque(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_CS_CIDADAO.getValue()));
        } else if (TimerName.AGENDAMENTO_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.AGENDAMENTO_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarAgendamento(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.ESTOQUE_INTEGRACAO_CS_CIDADAO.getValue()));
        } else if (TimerName.TFD_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.TFD_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarTfd(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.TFD_INTEGRACAO_CS_CIDADAO.getValue()));
        } else if (TimerName.EXAME_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.EXAME_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarExame(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.EXAME_INTEGRACAO_CS_CIDADAO.getValue()));
        } else if (TimerName.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarDispensacaoMedicamentos(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.DISPENSACAO_MEDICAMENTOS_INTEGRACAO_CS_CIDADAO.getValue()));
        } else if (TimerName.FILA_ESPERA_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.FILA_ESPERA_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarFilaEspera(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.FILA_ESPERA_CS_CIDADAO.getValue()));
        } else if (TimerName.GERAIS_INTEGRACAO_CS_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.GERAIS_INTEGRACAO_CS_CIDADAO);
            BOFactory.getBO(IntegracaoCsCidadaoFacade.class).gerarUnidades(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.GERAIS_INTEGRACAO_CS_CIDADAO.getValue()));
//Fim de CS-Cidadao
        } else if (TimerName.VIGILANCIA_INTEGRACAO_DOCSPRIME.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.VIGILANCIA_INTEGRACAO_DOCSPRIME);
            BOFactory.getBO(VigilanciaFacade.class).processoVigilanciaIntegracaoDocsprime(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.VIGILANCIA_INTEGRACAO_DOCSPRIME.getValue()), false);
        } else if (TimerName.RELACAO_ESTABELECIMENTOS_SEM_RT.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.RELACAO_ESTABELECIMENTOS_SEM_RT);
            BOFactory.getBO(VigilanciaFacade.class).processoRelacaoEstabelecimentosSemRT(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.RELACAO_ESTABELECIMENTOS_SEM_RT.getValue()), false);
        } else if (TimerName.NOTIFICACAO_ALVARAS_PROVISORIOS_VENCIDOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_ALVARAS_PROVISORIOS_VENCIDOS);
            BOFactory.getBO(VigilanciaFacade.class).processoRelacaoEstabelecimentosSemRT(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.NOTIFICACAO_ALVARAS_PROVISORIOS_VENCIDOS.getValue()), false);
        } else if (TimerName.CONTROLE_TEMPERATURA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CONTROLE_TEMPERATURA);
            BOFactory.getBO(VacinaFacade.class).gerarRegistrosControleTemperatura();
        } else if (TimerName.EXAME_VENCIMENTO_VALIDADE_AUTORIZACAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.EXAME_VENCIMENTO_VALIDADE_AUTORIZACAO);
            BOFactory.getBO(ExameFacade.class).processarExamesComValidadeAutorizacaoVencida();
        } else if (TimerName.DESBLOQUEAR_CONSORCIADOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.DESBLOQUEAR_CONSORCIADOS);
            BOFactory.getBO(ConsorcioFacade.class).desbloquearConsorciados();
        } else if (TimerName.RELATORIO_DENUNCIAS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.RELATORIO_DENUNCIAS);
            BOFactory.getBO(VigilanciaFacade.class).enviarRelatorioDenuncias();
        } else if (TimerName.NOTIFICACAO_VENCIMENTO_CONTRATOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_VENCIMENTO_CONTRATOS);
            BOFactory.getBO(ConsorcioFacade.class).enviarRelatorioRelacaoPrestadores();
        } else if (TimerName.LOCALIZACAO_REGISTRO_AGRAVO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.LOCALIZACAO_REGISTRO_AGRAVO);
            BOFactory.getBO(VigilanciaFacade.class).atualizarLocalizacaoRegistroAgravo();
        } else if (TimerName.CONSULTAR_PRODUTOS_BRANET.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CONSULTAR_PRODUTOS_BRANET);
            BOFactory.getBO(IntegracaoBranetFacade.class).enviarRelatorioSincronizacaoProdutos();
        } else if (TimerName.CONSULTAR_EMPRESAS_BRANET.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CONSULTAR_EMPRESAS_BRANET);
            BOFactory.getBO(IntegracaoBranetFacade.class).enviarRelatorioSincronizacaoUnidades();
        } else if (TimerName.SINCRONIZAR_ITENS_INTEGRADOS_BRANET.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.SINCRONIZAR_ITENS_INTEGRADOS_BRANET);
            BOFactory.getBO(IntegracaoBranetFacade.class).sincronizarItensIntegrados(null);
        } else if (TimerName.FECHAMENTO_CANCELAMENTO_ATENDIMENTO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.FECHAMENTO_CANCELAMENTO_ATENDIMENTO);
            BOFactory.getBO(AtendimentoFacade.class).fecharCancelarAtendimentoAutomatico();
        } else if (TimerName.ALERTAR_DOSES_ATRASADAS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ALERTAR_DOSES_ATRASADAS);
            BOFactory.getBO(VacinaFacade.class).alertarDosesAtrasadas();
        } else if (TimerName.ATUALIZAR_SITUACAO_BOLETOS_WS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_SITUACAO_BOLETOS_WS);
            BOFactory.getBO(VigilanciaFacade.class).atualizarSituacaoBoletos();
        } else if (TimerName.ATUALIZAR_SITUACAO_BOLETOS_REMESSA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_SITUACAO_BOLETOS_REMESSA);
            BOFactory.getBO(VigilanciaFacade.class).consultarRetornoRemessaBoletos();
        } else if (TimerName.NOTIFICACAO_REQUERIMENTO_PARADO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_REQUERIMENTO_PARADO);
            BOFactory.getBO(VigilanciaFacade.class).notificarRequerimentosParados();
        } else if (TimerName.NOTIFICACAO_VCTO_VALIDADE_ALVARA_LICENCA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_VCTO_VALIDADE_ALVARA_LICENCA);
            BOFactory.getBO(VigilanciaFacade.class).notificarVctoValidadeAlvaraLicenca();
        } else if (TimerName.NOTIFICACAO_VCTO_PRAZO_DEFESA_RECURSO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_VCTO_PRAZO_DEFESA_RECURSO);
            BOFactory.getBO(VigilanciaFacade.class).notificarVctoPrazoDefesaRecurso();
        } else if (TimerName.ENVIO_AGRAVO_DATA_LIMITE.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIO_AGRAVO_DATA_LIMITE);
            BOFactory.getBO(VigilanciaFacade.class).enviarRelatorioRegistroAgravo();
        } else if (TimerName.INTEGRACAO_CNES.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.INTEGRACAO_CNES);
            BOFactory.getBO(BasicoFacade.class).processarIntegracaoCnes();
        } else if (TimerName.CANCELAR_AGENDAMENTOS_VENCIDOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CANCELAR_AGENDAMENTOS_VENCIDOS);
            BOFactory.getBO(AgendamentoFacade.class).cancelarAgendamentosVencidos(getSessao().getUsuario());
        } else if (TimerName.ATUALIZAR_RESUMO_MOVIMENTACAO_ESTOQUE.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_RESUMO_MOVIMENTACAO_ESTOQUE);
            BOFactory.getBO(EstoqueEmpresaFacade.class).atualizarResumoMovimentacaoEstoque();
        } else if (TimerName.ORDENAR_POSICAO_FILA_ESPERA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ORDENAR_POSICAO_FILA_ESPERA);
            BOFactory.getBO(AgendamentoFacade.class).calcularPosicoesFilaEspera();
        } else if (TimerName.ATUALIZAR_CONFIG_ANO_BASE_VIRADA_ANO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_CONFIG_ANO_BASE_VIRADA_ANO);
            BOFactory.getBO(VigilanciaFacade.class).atualizarConfiguracoesViradaAno();
        } else if (TimerName.ENVIA_NOTIFICACAO_2A_DOSE_VACINA_APP_CIDADAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIA_NOTIFICACAO_2A_DOSE_VACINA_APP_CIDADAO);
            BOFactory.getBO(VacinaFacade.class).processarEnvioMensagemNotificacaoVacinaAppCidadao();
        } else if (TimerName.DEVOLVER_SALDO_NAO_UTILIZADO_COTA_ANTERIOR.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.DEVOLVER_SALDO_NAO_UTILIZADO_COTA_ANTERIOR);
            BOFactory.getBO(AgendamentoFacade.class).resgatarSaldoPpi();
        } else if (TimerName.ATUALIZAR_SITUACAO_LEITO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_SITUACAO_LEITO);
            BOFactory.getBO(AgendamentoFacade.class).atualizarSituacaoLeito();
        } else if (TimerName.CANCELAR_AGENDAMENTOS_DEVOLVIDOS.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.CANCELAR_AGENDAMENTOS_DEVOLVIDOS);
        } else if (TimerName.INATIVAR_AGENDA_CONTRATO_VENCIDO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.INATIVAR_AGENDA_CONTRATO_VENCIDO);
            BOFactory.getBO(AgendamentoFacade.class).inativarAgendaContratoVencido();
        } else if (TimerName.ATUALIZAR_BNAFAR_ENTRADA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_BNAFAR_ENTRADA);
            BOFactory.getBO(MaterialBasicoFacade.class).enviarBnafarEntrada();
        } else if (TimerName.ATUALIZAR_BNAFAR_SAIDA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_BNAFAR_SAIDA);
            BOFactory.getBO(MaterialBasicoFacade.class).enviarBnafarSaida();
        } else if (TimerName.ATUALIZAR_BNAFAR_DISPENSACAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_BNAFAR_DISPENSACAO);
            BOFactory.getBO(MaterialBasicoFacade.class).atualizarBnafarDispensacao();
        } else if (TimerName.ATUALIZAR_BNAFAR_POS_ESTOQUE.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_BNAFAR_POS_ESTOQUE);
            BOFactory.getBO(MaterialBasicoFacade.class).gerarEnviarBnafarPosEstoque();
        } else if (TimerName.NOTIFICACAO_VENCIMENTO_FPO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_VENCIMENTO_FPO);
            BOFactory.getBO(AgendamentoFacade.class).notificacaoVencimentoFPO(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.NOTIFICACAO_VENCIMENTO_FPO.getValue()), false);
        } else if (TimerName.DEVOLVE_SOLICITACOES_FILA_ESPERA.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.DEVOLVE_SOLICITACOES_FILA_ESPERA);
            BOFactory.getBO(AgendamentoFacade.class).devolverSolicitacoesFilaEspera();
        } else if (TimerName.ENVIAR_EMAIL_AUTOS_APP_FRU.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIAR_EMAIL_AUTOS_APP_FRU);
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailAutosAppFru();
        } else if (TimerName.INTEGRACAO_ESTABELECIMENTO_SIM.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.INTEGRACAO_ESTABELECIMENTO_SIM);
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailAutosAppFru();
        } else if (TimerName.ENVIAR_DADOS_FASTMEDIC.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIAR_DADOS_FASTMEDIC);
            BOFactory.getBO(IntegracaoFastMedicFacade.class).enviarDadosFastmedic();
        } else if (TimerName.ENVIAR_DADOS_AGRAVO_NOYIFICACAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIAR_DADOS_AGRAVO_NOYIFICACAO);
            BOFactory.getBO(VigilanciaFacade.class).integracaoAgravoNotificacao(IntegracaoAgravoNotificacao.TipoRequisicao.POST.value());
        } else if (TimerName.ATUALIZAR_DADOS_AGRAVO_NOTIFICACAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ATUALIZAR_DADOS_AGRAVO_NOTIFICACAO);
            BOFactory.getBO(VigilanciaFacade.class).integracaoAgravoNotificacao(IntegracaoAgravoNotificacao.TipoRequisicao.PUT.value());
        } else if (TimerName.DELETAR_DADOS_AGRAVO_NOTIFICACAO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.DELETAR_DADOS_AGRAVO_NOTIFICACAO);
            BOFactory.getBO(VigilanciaFacade.class).integracaoAgravoNotificacao(IntegracaoAgravoNotificacao.TipoRequisicao.DELETE.value());
        } else if (TimerName.ENVIAR_INTEGRACAO_SMAR.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIAR_INTEGRACAO_SMAR);
            BOFactory.getBO(EstoqueEmpresaFacade.class).enviarAtualizacaoEstoqueSmar();
        } else if (TimerName.ENVIO_MENSAGEM_AGEN_VIAGEM_CANCEL.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.ENVIO_MENSAGEM_AGEN_VIAGEM_CANCEL);
            BOFactory.getBO(AgendamentoFacade.class).processaMensagensAgendaViagemCancelamento();
        } else if (TimerName.DEVOLVE_AIH_AGUARDANDO_ANALISE.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.DEVOLVE_AIH_AGUARDANDO_ANALISE);
            BOFactory.getBO(AgendamentoFacade.class).devolverAih();
        } else if (TimerName.NOTIFICACAO_DEVOLUCAO_AIH_ESTABELECIMENTO.equals(timerName)) {
            Loggable.log.info(descTimeService + TimerName.NOTIFICACAO_DEVOLUCAO_AIH_ESTABELECIMENTO);
            BOFactory.getBO(UsuarioFacade.class).notificarAihsDevolvidas(AgendadorProcessosUtil.getAgendadorProcessoUsuarios(AgendadorProcesso.Processo.NOTIFICACAO_DEVOLUCAO_AIH_ESTABELECIMENTO.getValue()));
        }
        else {
            Loggable.log.warn("TIMER-SERVICE: O agendador foi chamado, mas nenhum processo foi executado. Timer-name: " + timerName);
        }
    }

    private Date getDataInicio() {
        ParametroAtendimento parametroAtendimento = CargaBasicoPadrao.getInstance().getParametroAtendimento();
        if (parametroAtendimento.getDataProcessoCidNotificavel() == null) {
            return (new GregorianCalendar(1900, 1, 1)).getTime();
        }
        return parametroAtendimento.getDataProcessoCidNotificavel();
    }
}
