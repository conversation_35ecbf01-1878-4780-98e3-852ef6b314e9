package br.com.ksisolucoes.bo.prontuario.hospital.convenio;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaConvenioDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaConvenio extends CommandQueryPager<QueryConsultaConvenio> {

    private QueryConsultaConvenioDTOParam param;

    public QueryConsultaConvenio(QueryConsultaConvenioDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("c.codigo", true);
        hql.addToSelect("c.descricao", true);
        
        hql.setTypeSelect(Convenio.class.getName());
        hql.addToFrom("Convenio c");
        
        hql.addToWhereWhithAnd("c.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("c.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("c.codigo || ' ' || c.descricao",param.getKeyword()));
        
        if(CollectionUtils.isNotNullEmpty(param.getCodigoConvenioNotIn())){
            hql.addToWhereWhithAnd("c.codigo not in ", param.getCodigoConvenioNotIn());
        }
        
        if(param.getPropSort() != null){
            hql.addToOrder("c."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("c.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}