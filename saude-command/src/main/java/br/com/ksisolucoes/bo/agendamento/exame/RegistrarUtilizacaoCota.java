package br.com.ksisolucoes.bo.agendamento.exame;

import br.com.celk.agendamento.dto.RegistrarUtilizacaoCotaDTO;
import br.com.celk.agendamento.ppi.RegistrarUtilizacaoCotaPpi;
import br.com.celk.laboratorio.exames.dto.ProcedimentoHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoWQRException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.base.BaseEmpresa;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.basico.base.*;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;

import java.util.*;

import static br.com.celk.util.CollectionUtils.isNotNullEmpty;
import static br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario.STATUS_AGENDADO;
import static br.com.ksisolucoes.vo.agendamento.base.BaseAgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO;
import static br.com.ksisolucoes.vo.agendamento.base.BaseAgendaGradeAtendimentoHorario.PROP_STATUS;
import static br.com.ksisolucoes.vo.prontuario.basico.base.BaseSolicitacaoAgendamento.PROP_CODIGO;

public class RegistrarUtilizacaoCota extends AbstractCommandTransaction<RegistrarUtilizacaoCota> {

    private static final long serialVersionUID = 5509181474261423102L;

    private final TipoProcedimento tipoProcedimento;
    private final Date dataAtendimento;
    private final Empresa unidadeExecutante;
    private final List<SolicitacaoAgendamentoExame> examesSolicitacao;
    private final SolicitacaoAgendamento solicitacaoAgendamento;

    private RegistrarUtilizacaoCotaDTO registrarUtilizacaoCotaDTO;

    public RegistrarUtilizacaoCota(TipoProcedimento tipoProcedimento, Date dataAtendimento, Empresa unidadeExecutante, List<SolicitacaoAgendamentoExame> examesSolicitacao, SolicitacaoAgendamento solicitacaoAgendamento) {
        this.tipoProcedimento = tipoProcedimento;
        this.dataAtendimento = dataAtendimento;
        this.unidadeExecutante = unidadeExecutante;
        this.examesSolicitacao = examesSolicitacao;
        this.solicitacaoAgendamento = solicitacaoAgendamento;
    }

    @Override
    public void execute() throws ValidacaoException, DAOException {
        registrarUtilizacaoCotaDTO = new RegistrarUtilizacaoCotaDTO();
        TipoExame tipoExame;
        if (isNotNullEmpty(examesSolicitacao)) {
            if (!isCotaNaoRegistradaParaSolicitacao()) {
                return;
            }
            tipoExame = examesSolicitacao.get(0).getExameProcedimento().getTipoExame();
        } else {
            Loggable.log.info("Solicitacao sem exames (exameProcedimento de tipoProcedimento=null): " + solicitacaoAgendamento.getCodigo());
            if (tipoProcedimento.getExameProcedimento() == null) {
                Loggable.log.info("Tipo Procedimento sem Exame Procedimento na solicitacao: " + solicitacaoAgendamento.getCodigo());
                throw new ValidacaoWQRException(Bundle.getStringApplication("msg_configure_exame_tipo_procedimento_X", tipoProcedimento.getDescricao()), 8L);
            }
            tipoExame = tipoProcedimento.getExameProcedimento().getTipoExame();
        }
        int diaInicioCompetencia = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).<Long>getParametro("diaInicioCompetencia").intValue();
        Date competenciaAgendamento = Data.competenciaData(diaInicioCompetencia, dataAtendimento);
        Date competenciaAnualdoAgendamento = Data.getUltimoDiaUltimoMes(competenciaAgendamento);
        ExamePrestadorCompetencia examePrestadorCompetencia = getExamePrestadorCompetenciaPorTipoExameEDataCompetencia(tipoExame, competenciaAgendamento, competenciaAnualdoAgendamento);
        if (examePrestadorCompetencia == null) {
            examePrestadorCompetencia = gerarExamePrestadorCompetencia(tipoExame, competenciaAgendamento, competenciaAnualdoAgendamento);
        }
        ExameCotaPpi exameCotaPpi = getExameCotaPpi(tipoExame);
        if (exameCotaPpi == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_cota_nao_configurada_tipo_X", tipoExame.getDescricao()));
        }
        if (ExameCotaPpi.TipoTeto.FISICO.value().equals(exameCotaPpi.getTipoTeto())) {
            registrarUtilizacaoTetoFisico(examePrestadorCompetencia);
        } else if (ExameCotaPpi.TipoTeto.FINANCEIRO.value().equals(exameCotaPpi.getTipoTeto())) {
            registrarUtilizacaoTetoFinanceiro(tipoExame, examePrestadorCompetencia);
        }
        BOFactory.getBO(CadastroFacade.class).save(examePrestadorCompetencia);
        registrarUtilizacaoCotaDTO.setExamePrestadorCompetencia(examePrestadorCompetencia);
    }

    private boolean isCotaNaoRegistradaParaSolicitacao() {
        final SolicitacaoAgendamento solicitacaoAgendamento = examesSolicitacao.get(0).getSolicitacaoAgendamento();
        List<AgendaGradeAtendimentoHorario> agendas = buscarAgendaGradeAtendimentoHorariosPorSolicitacao(solicitacaoAgendamento);
        boolean isCotaNaoRegistradaParaSolicitacao = true;
        if (isNotNullEmpty(agendas)) {
            Double valorUtilizadoCota = agendas.stream().mapToDouble(this::getValorCotaUtilizadoHorario).sum();
            isCotaNaoRegistradaParaSolicitacao = valorUtilizadoCota.equals(0d);
        }
        return isCotaNaoRegistradaParaSolicitacao;
    }

    @SuppressWarnings("unchecked")
    private List<AgendaGradeAtendimentoHorario> buscarAgendaGradeAtendimentoHorariosPorSolicitacao(SolicitacaoAgendamento solicitacaoAgendamento) {
        return getSession().createCriteria(AgendaGradeAtendimentoHorario.class)
                .add(org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(PROP_SOLICITACAO_AGENDAMENTO, PROP_CODIGO), solicitacaoAgendamento.getCodigo()))
                .add(org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(PROP_STATUS), STATUS_AGENDADO))
                .list();
    }

    private double getValorCotaUtilizadoHorario(AgendaGradeAtendimentoHorario agah) {
        return Optional.ofNullable(agah.getValorCotaUtilizado()).orElse(0d);
    }

    private void registrarUtilizacaoTetoFinanceiro(TipoExame tipoExame, ExamePrestadorCompetencia examePrestadorCompetencia) throws ValidacaoException, DAOException {
        Map<Long, Double> valorExames = new HashMap<>();
        Dinheiro valorTotalUtilizado = new Dinheiro(0D);
        if (isNotNullEmpty(examesSolicitacao)) {
            for (SolicitacaoAgendamentoExame solicitacaoAgendamentoExame : examesSolicitacao) {
                if (prestadorRealizaProcedimento(solicitacaoAgendamentoExame.getExameProcedimento(), unidadeExecutante)) {
                    Double valorExameProcedimento = ProcedimentoHelper.getValorExameProcedimento(solicitacaoAgendamentoExame.getExameProcedimento(), unidadeExecutante);
                    valorTotalUtilizado = valorTotalUtilizado.somar(valorExameProcedimento);
                    valorExames.put(solicitacaoAgendamentoExame.getExameProcedimento().getCodigo(), valorExameProcedimento);
                } else {
                    Loggable.log.info("Prestador nao realiza exame na solicitacao (no calculo do teto financeiro): " + solicitacaoAgendamento.getCodigo());
                    throw new ValidacaoWQRException(
                        Bundle.getStringApplication(
                        "msg_prestador_X_nao_realiza_procedimento_Y",
                            getDescricaoEmpresa(unidadeExecutante),
                            solicitacaoAgendamentoExame.getExameProcedimento().getProcedimento().getDescricao()
                        ),
                        10L
                    );
                }
            }
        } else {
            Double valorExameProcedimento = ProcedimentoHelper.getValorExameProcedimento(tipoProcedimento.getExameProcedimento(), unidadeExecutante);
            valorTotalUtilizado = valorTotalUtilizado.somar(valorExameProcedimento);
        }
        registrarUtilizacaoCotaDTO.setValorCotaUtilizado(valorTotalUtilizado.doubleValue());
        Double tetoFinanceiroRealizado = new Dinheiro(Coalesce.asDouble(examePrestadorCompetencia.getTetoFinanceiroRealizado())).somar(valorTotalUtilizado).doubleValue();
        if (tetoFinanceiroRealizado > examePrestadorCompetencia.getTetoFinanceiro()) {
            Loggable.log.info("Cota nao suficiente (financeira): " + solicitacaoAgendamento.getCodigo() +
                    ", teto financeiro realizado: " + tetoFinanceiroRealizado +
                    " > teto financeiro realizado: " + examePrestadorCompetencia.getTetoFinanceiro() +
                    " para data: " + examePrestadorCompetencia.getDataCompetencia());
            throw new ValidacaoWQRException(
                Bundle.getStringApplication(
                    "msg_nao_possui_cota_suficiente_este_agendamento_prestador_X_tipo_Y",
                    getDescricaoEmpresa(examePrestadorCompetencia.getEmpresa()),
                    tipoExame.getDescricao()
                ),
                5L
            );
        }
        examePrestadorCompetencia.setTetoFinanceiroRealizado(tetoFinanceiroRealizado);
    }

    private void registrarUtilizacaoTetoFisico(ExamePrestadorCompetencia examePrestadorCompetencia) throws ValidacaoException {
        if (isNotNullEmpty(examesSolicitacao)) {
            int size = examesSolicitacao.size();
            registrarUtilizacaoCotaDTO.setValorCotaUtilizado((double) size);
            for (SolicitacaoAgendamentoExame solicitacaoAgendamentoExame : examesSolicitacao) {
                if (!prestadorRealizaProcedimento(solicitacaoAgendamentoExame.getExameProcedimento(), unidadeExecutante)) {
                    Loggable.log.info("Prestador nao realiza exame na solicitacao (no calculo do teto fisico): " + solicitacaoAgendamento.getCodigo());
                    throw new ValidacaoWQRException(
                        Bundle.getStringApplication(
                            "msg_prestador_X_nao_realiza_procedimento_Y",
                            getDescricaoEmpresa(unidadeExecutante),
                            solicitacaoAgendamentoExame.getExameProcedimento().getProcedimento().getDescricao()
                        ),
                        7L
                    );
                }
            }
        } else {
            registrarUtilizacaoCotaDTO.setValorCotaUtilizado(1D);
        }
        Double tetoFisicoRealizado = new Dinheiro(Coalesce.asDouble(examePrestadorCompetencia.getTetoFinanceiroRealizado())).somar(registrarUtilizacaoCotaDTO.getValorCotaUtilizado()).doubleValue();
        if (tetoFisicoRealizado > examePrestadorCompetencia.getTetoFinanceiro()) {
            Loggable.log.info("Cota nao suficiente (fisica): " + solicitacaoAgendamento.getCodigo() +
                    ", teto fisico realizado: " + tetoFisicoRealizado +
                    " > teto financeiro realizado: " + examePrestadorCompetencia.getTetoFinanceiro());
            throw new ValidacaoWQRException(
                Bundle.getStringApplication(
                    "msg_nao_possui_cota_suficiente_este_agendamento_prestador_X_tipo_Y",
                    getDescricaoEmpresa(examePrestadorCompetencia.getEmpresa()),
                    examePrestadorCompetencia.getTipoExame().getDescricao()
                ),
                9L
            );
        }
        examePrestadorCompetencia.setTetoFinanceiroRealizado(tetoFisicoRealizado);
    }

    private ExamePrestadorCompetencia gerarExamePrestadorCompetencia(TipoExame tipoExame, Date competenciaAgendamento, Date competenciaAnualdoAgendamento) throws ValidacaoException, DAOException {
        validarDataAtendimentoComMesAtual();
        ExamePrestador examePrestador = getExamePrestador(tipoExame);
        ExamePrestadorCompetencia examePrestadorCompetencia = new ExamePrestadorCompetencia();
        if (ExamePrestador.TipoCota.MENSAL.value().equals(examePrestador.getTipoCota())) {
            examePrestadorCompetencia.setDataCompetencia(competenciaAgendamento);
        } else if (ExamePrestador.TipoCota.ANUAL.value().equals(examePrestador.getTipoCota())) {
            examePrestadorCompetencia.setDataCompetencia(competenciaAnualdoAgendamento);
        }
        examePrestadorCompetencia.setEmpresa(examePrestador.getPrestador());
        examePrestadorCompetencia.setTetoFinanceiro(examePrestador.getTetoFinanceiro());
        examePrestadorCompetencia.setTetoFinanceiroRealizado(0D);
        examePrestadorCompetencia.setTetoRecursoProprio(examePrestador.getRecursoProprio());
        examePrestadorCompetencia.setTetoRecursoProprioRealizado(0D);
        examePrestadorCompetencia.setTipoExame(examePrestador.getTipoExame());
        return examePrestadorCompetencia;
    }

    private void validarDataAtendimentoComMesAtual() throws ValidacaoException, DAOException {
        DatePeriod datePeriodMesAtual = Data.adjustRangeMonth(DataUtil.getDataAtual());
        if (dataAtendimento.before(datePeriodMesAtual.getDataInicial())) {
                throw new ValidacaoWQRException(Bundle.getStringApplication("msg_cota_nao_definida_data_escolhida"), 6L);
        }
    }

    private ExamePrestador getExamePrestador(TipoExame tipoExame) throws ValidacaoException {
        ExamePrestador examePrestador = getExamePrestadorPorTipoExameETipoExameSecundario(tipoExame);
        if (examePrestador == null) {
            examePrestador = getExamePrestadorPorTipoExame();
            if (examePrestador == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_encontrado_registro_configuracao_cota_tipo_X_prestador_Y", tipoExame.getDescricao(), getDescricaoEmpresa(unidadeExecutante)));
            }
        }
        return examePrestador;
    }

    private boolean prestadorRealizaProcedimento(ExameProcedimento exameProcedimento, Empresa prestador) {
        return exameProcedimento != null
                && prestador != null
                && LoadManager.getInstance(ExamePrestadorProcedimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseExamePrestadorProcedimento.PROP_EXAME_PROCEDIMENTO, BaseExameProcedimento.PROP_CODIGO), exameProcedimento.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseExamePrestadorProcedimento.PROP_EXAME_PRESTADOR, BaseExamePrestador.PROP_PRESTADOR, BaseEmpresa.PROP_CODIGO), prestador.getCodigo()))
                .exists();
    }

    private ExameCotaPpi getExameCotaPpi(TipoExame tipoExame) {
        return (ExameCotaPpi) getSession().createCriteria(ExameCotaPpi.class)
                .add(org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(BaseExameCotaPpi.PROP_TIPO_EXAME, BaseTipoExame.PROP_CODIGO), tipoExame.getCodigo()))
                .setMaxResults(1)
                .uniqueResult();
    }

    private ExamePrestadorCompetencia getExamePrestadorCompetenciaPorDataCompetencia(Date competenciaAgendamento, Date competenciaAnualdoAgendamento) throws DAOException {
        return HibernateUtil.lockTable(ExamePrestadorCompetencia.class,
                org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(BaseExamePrestadorCompetencia.PROP_EMPRESA, BaseEmpresa.PROP_CODIGO), unidadeExecutante.getCodigo()),
                org.hibernate.criterion.Restrictions.isNull(BaseExamePrestadorCompetencia.PROP_TIPO_EXAME),
                org.hibernate.criterion.Restrictions.or(
                        org.hibernate.criterion.Restrictions.eq(BaseExamePrestadorCompetencia.PROP_DATA_COMPETENCIA, competenciaAgendamento),
                        org.hibernate.criterion.Restrictions.eq(BaseExamePrestadorCompetencia.PROP_DATA_COMPETENCIA, competenciaAnualdoAgendamento)
                )
        );
    }

    private ExamePrestador getExamePrestadorPorTipoExame() {
        return (ExamePrestador) getSession().createCriteria(ExamePrestador.class)
                .add(org.hibernate.criterion.Restrictions.eq(BaseExamePrestador.PROP_PRESTADOR, unidadeExecutante))
                .add(org.hibernate.criterion.Restrictions.isNull(BaseExamePrestador.PROP_TIPO_EXAME))
                .setMaxResults(1)
                .uniqueResult();
    }

    private ExamePrestador getExamePrestadorPorTipoExameETipoExameSecundario(TipoExame tipoExame) {
        return (ExamePrestador) getSession().createCriteria(ExamePrestador.class)
                .add(org.hibernate.criterion.Restrictions.eq(BaseExamePrestador.PROP_PRESTADOR, unidadeExecutante))
                .add(Restrictions.or(
                        Restrictions.eq(BaseExamePrestador.PROP_TIPO_EXAME, tipoExame),
                        Restrictions.eq(BaseExamePrestador.PROP_TIPO_EXAME_SECUNDARIO, tipoExame)))
                .setMaxResults(1)
                .uniqueResult();
    }

    private ExamePrestadorCompetencia getExamePrestadorCompetenciaPorTipoExameEDataCompetencia(TipoExame tipoExame, Date competenciaAgendamento, Date competenciaAnualdoAgendamento) throws DAOException {
        ExamePrestadorCompetencia examePrestadorCompetencia = lockTableExamePrestadorCompetencia(tipoExame, competenciaAgendamento, competenciaAnualdoAgendamento);
        if (examePrestadorCompetencia == null) {
            //verfica se existe configuracao para aquele tipo e nao misturar com a competencia que nao tem tipo de exame definido
            ExamePrestador examePrestador = getExamePrestadorPorTipoExameETipoExameSecundario(tipoExame);
            if (examePrestador == null) {
                examePrestadorCompetencia = getExamePrestadorCompetenciaPorDataCompetencia(competenciaAgendamento, competenciaAnualdoAgendamento);
            }
        }
        return examePrestadorCompetencia;
    }

    private ExamePrestadorCompetencia lockTableExamePrestadorCompetencia(TipoExame tipoExame, Date competenciaAgendamento, Date competenciaAnualdoAgendamento) throws DAOException {
        return HibernateUtil.lockTable(ExamePrestadorCompetencia.class,
                org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(BaseExamePrestadorCompetencia.PROP_EMPRESA, BaseEmpresa.PROP_CODIGO), unidadeExecutante.getCodigo()),
                org.hibernate.criterion.Restrictions.eq(VOUtils.montarPath(BaseExamePrestadorCompetencia.PROP_TIPO_EXAME, BaseTipoExame.PROP_CODIGO), tipoExame.getCodigo()),
                org.hibernate.criterion.Restrictions.or(
                        org.hibernate.criterion.Restrictions.eq(BaseExamePrestadorCompetencia.PROP_DATA_COMPETENCIA, competenciaAgendamento),
                        org.hibernate.criterion.Restrictions.eq(BaseExamePrestadorCompetencia.PROP_DATA_COMPETENCIA, competenciaAnualdoAgendamento)
                ));
    }

    private String getDescricaoEmpresa(Empresa empresa) {
        return Objects.isNull(empresa) ? ""
                : empresa.getDescricao();
    }

    public RegistrarUtilizacaoCotaDTO getRegistrarUtilizacaoCotaDTO() {
        return this.registrarUtilizacaoCotaDTO;
    }
}
