package br.com.ksisolucoes.bo.materiais.compras;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.AutorizacaoFornecimentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompra;
import br.com.ksisolucoes.vo.entradas.estoque.OrdemCompraItem;
import org.hibernate.LockMode;
import org.hibernate.criterion.Restrictions;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by sulivan on 08/02/18.
 */
public class CancelarAutorizacaoFornecimentoItem extends AbstractCommandTransaction {

    private AutorizacaoFornecimentoItem autorizacaoFornecimentoItem;

    public CancelarAutorizacaoFornecimentoItem(AutorizacaoFornecimentoItem autorizacaoFornecimentoItem) {
        this.autorizacaoFornecimentoItem = autorizacaoFornecimentoItem;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        OrdemCompraItem ordemCompraItem = (OrdemCompraItem) getSession().createCriteria(OrdemCompraItem.class)
                .add(Restrictions.eq(OrdemCompraItem.PROP_CODIGO, autorizacaoFornecimentoItem.getOrdemCompraItem().getCodigo()))
                .setLockMode(LockMode.PESSIMISTIC_WRITE)
                .uniqueResult();

        ordemCompraItem.setQuantidadeCompra(new Dinheiro(ordemCompraItem.getQuantidadeCompra()).subtrair(autorizacaoFornecimentoItem.getQtdItens().doubleValue()).bigDecimalValue());

        if(ordemCompraItem.getSaldo().compareTo(BigDecimal.ZERO) >= 0) {

            BOFactory.save(ordemCompraItem);

            autorizacaoFornecimentoItem.setSituacao(AutorizacaoFornecimentoItem.Situacao.CANCELADA.value());
            autorizacaoFornecimentoItem.setDataCancelamento(DataUtil.getDataAtual());
            autorizacaoFornecimentoItem.setUsuarioCancelamento(getSessao().<Usuario>getUsuario());

            BOFactory.save(autorizacaoFornecimentoItem);

            List<AutorizacaoFornecimentoItem> autorizacaoFornecimentoItemList = getSession().createCriteria(AutorizacaoFornecimentoItem.class)
                    .add(Restrictions.eq(AutorizacaoFornecimentoItem.PROP_AUTORIZACAO_FORNECIMENTO, autorizacaoFornecimentoItem.getAutorizacaoFornecimento()))
                    .add(Restrictions.ne(AutorizacaoFornecimentoItem.PROP_SITUACAO, AutorizacaoFornecimentoItem.Situacao.CANCELADA.value()))
                    .list();

            if(CollectionUtils.isEmpty(autorizacaoFornecimentoItemList)){
                OrdemCompra ordemCompra = (OrdemCompra) getSession().createCriteria(OrdemCompra.class)
                        .add(Restrictions.eq(OrdemCompra.PROP_CODIGO, autorizacaoFornecimentoItem.getOrdemCompraItem().getOrdemCompra().getCodigo()))
                        .setLockMode(LockMode.PESSIMISTIC_WRITE)
                        .uniqueResult();

            }
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_possivel_cancelar_item_autorizacao_fornecimento_nao_ha_saldo_recebimento_disponivel", autorizacaoFornecimentoItem.getOrdemCompraItem().getProduto().getDescricaoFormatado()));
        }
    }
}
