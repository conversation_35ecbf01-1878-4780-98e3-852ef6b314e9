package br.com.ksisolucoes.bo.prontuario.basico.solicitacaoagendamento;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.util.Modulos.AGENDAMENTO;
import static br.com.ksisolucoes.util.validacao.RepositoryComponentDefault.*;
import static br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento.TIPO_FILA_NORMAL;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaResumoAgendamentoListaEspera extends CommandQueryPager<QueryPagerConsultaSolicitacaoAgendamento> {

    private final AgendamentoListaEsperaDTOParam param;

    public QueryConsultaResumoAgendamentoListaEspera(AgendamentoListaEsperaDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        hql.setTypeSelect(AgendamentoListaEsperaDTO.class.getName());

        hql.addToSelect("solicitacaoAgendamento.codigo", "solicitacaoAgendamento.codigo");
        hql.addToSelect("solicitacaoAgendamento", new HQLProperties(SolicitacaoAgendamento.class, "solicitacaoAgendamento").getSingleProperties());

        hql.addToSelect("usuarioCadsus.codigo", "solicitacaoAgendamento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "solicitacaoAgendamento.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.apelido", "solicitacaoAgendamento.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "solicitacaoAgendamento.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.sexo", "solicitacaoAgendamento.usuarioCadsus.sexo");
        hql.addToSelect("usuarioCadsus.telefone", "solicitacaoAgendamento.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2", "solicitacaoAgendamento.usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.telefone3", "solicitacaoAgendamento.usuarioCadsus.telefone3");
        hql.addToSelect("usuarioCadsus.telefone4", "solicitacaoAgendamento.usuarioCadsus.telefone4");
        hql.addToSelect("usuarioCadsus.celular", "solicitacaoAgendamento.usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.email", "solicitacaoAgendamento.usuarioCadsus.email");
        hql.addToSelect("usuarioCadsus.dataNascimento", "solicitacaoAgendamento.usuarioCadsus.dataNascimento");

        hql.addToSelect("profissional.codigo", "solicitacaoAgendamento.profissional.codigo");
        hql.addToSelect("profissional.referencia", "solicitacaoAgendamento.profissional.referencia");
        hql.addToSelect("profissional.nome", "solicitacaoAgendamento.profissional.nome");

        hql.addToSelect("procedimento.codigo", "solicitacaoAgendamento.procedimento.codigo");
        hql.addToSelect("procedimento.descricao", "solicitacaoAgendamento.procedimento.descricao");

        hql.addToSelect("unidadeOrigem.codigo", "solicitacaoAgendamento.unidadeOrigem.codigo");
        hql.addToSelect("unidadeOrigem.descricao", "solicitacaoAgendamento.unidadeOrigem.descricao");

        hql.addToSelect("tipoProcedimento.codigo", "solicitacaoAgendamento.tipoProcedimento.codigo");
        hql.addToSelect("tipoProcedimento.descricao", "solicitacaoAgendamento.tipoProcedimento.descricao");
        hql.addToSelect("tipoProcedimento.tipoAgendamento", "solicitacaoAgendamento.tipoProcedimento.tipoAgendamento");
        hql.addToSelect("tipoProcedimento.tipoAgenda", "solicitacaoAgendamento.tipoProcedimento.tipoAgenda");
        hql.addToSelect("tipoProcedimento.flagAgendamentoGrupo", "solicitacaoAgendamento.tipoProcedimento.flagAgendamentoGrupo");
        hql.addToSelect("tipoProcedimento.controleCota", "solicitacaoAgendamento.tipoProcedimento.controleCota");

        hql.addToSelect("empresa.codigo", "solicitacaoAgendamento.empresa.codigo");
        hql.addToSelect("empresa.descricao", "solicitacaoAgendamento.empresa.descricao");

        hql.addToSelect("empresaPrestador.codigo", "solicitacaoAgendamento.examePrestadorCompetencia.empresa.codigo");
        hql.addToSelect("empresaPrestador.descricao", "solicitacaoAgendamento.examePrestadorCompetencia.empresa.descricao");

        hql.addToFrom("SolicitacaoAgendamento solicitacaoAgendamento"
                + " left join solicitacaoAgendamento.solicitacaoAgendamentoPosicaoFila solicitacaoAgendamentoPosicaoFila "
                + " left join solicitacaoAgendamento.tipoProcedimento tipoProcedimento "
                + " left join solicitacaoAgendamento.procedimento procedimento "
                + " left join solicitacaoAgendamento.empresa empresa "
                + " left join solicitacaoAgendamento.usuarioCadsus usuarioCadsus "
                + " left join solicitacaoAgendamento.examePrestadorCompetencia examePrestadorCompetencia "
                + " left join solicitacaoAgendamento.unidadeOrigem unidadeOrigem "
                + " left join examePrestadorCompetencia.empresa empresaPrestador "
                + " left join solicitacaoAgendamento.profissional profissional ");

        hql.addToWhereWhithAnd("tipoProcedimento = ", param.getTipoProcedimento());
        hql.addToWhereWhithAnd("unidadeOrigem = ", param.getOrigemSolicitacao());
        hql.addToWhereWhithAnd("solicitacaoAgendamento.flagBloqueado = ", paramBloqueado());

        if (param.isPrestador()) {
            hql.addToWhereWhithAnd("empresaPrestador in ", param.getEmpresas());
        } else {
            hql.addToWhereWhithAnd("empresa in ", param.getEmpresas());
        }

        if (param.getCodigoSolicitacao() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.codigo = ", new Long(param.getCodigoSolicitacao()));
        }

        if (!param.isPermissaoTipoProcedimento() && param.getInCodigosEmpresasUsuario() != null && !param.getInCodigosEmpresasUsuario().isEmpty()) {
            StringBuilder sb = new StringBuilder();
            sb.append("exists(select 1 from TipoProcedimentoAgenda tipoProcAgend left join tipoProcAgend.tipoProcedimento tipoProc left join tipoProcAgend.empresa emp ");
            sb.append("where tipoProc.codigo = tipoProcedimento.codigo ");
            sb.append("and emp.codigo in ");
            sb.append(param.getInCodigosEmpresasUsuario());
            sb.append(")");
            hql.addToWhereWhithAnd(sb.toString());
        }

        if (!param.isPermissaoVisualizarTipoProcAgend() && param.getInCodigosEmpresasUsuario() != null && !param.getInCodigosEmpresasUsuario().isEmpty()) {
            StringBuilder sb = new StringBuilder();
            sb.append("exists(select 1 from TipoProcedimentoEmpresaAgendar tipoProcEmpAgend left join tipoProcEmpAgend.tipoProcedimento tipoProc2 left join tipoProcEmpAgend.empresa emp2 ");
            sb.append("where tipoProc2.codigo = tipoProcedimento.codigo ");
            sb.append("and emp2.codigo in ");
            sb.append(param.getInCodigosEmpresasUsuario());
            sb.append(")");
            hql.addToWhereWhithAnd(sb.toString());
        }

        if (param.getPaciente() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" usuarioCadsus.nome", param.getPaciente(), true)
                    + " OR (usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("usuarioCadsus.apelido", param.getPaciente(), true) + "))");
        }
        hql.addToWhereWhithAnd(hql.getConsultaLiked("solicitacaoAgendamento.numeracaoAuxiliar ", param.getNumeracaoAuxiliar()));
        hql.addToWhereWhithAnd("usuarioCadsus.dataNascimento = ", param.getDataNascimento());
        hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", NAO);
        hql.addToWhereWhithAnd("procedimento = ", param.getProcedimento());
        if (param.getTipoConsulta() != null) {
            hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoConsulta in ", param.getTipoConsulta());
        }
        hql.addToWhereWhithAnd("solicitacaoAgendamento.status in ", param.getSituacaoList());
        hql.addToWhereWhithAnd("solicitacaoAgendamento.tipoFila = :tipoFila");

        String ordenarFaixaEtaria = BOFactory.getBO(CommomFacade.class).modulo(AGENDAMENTO).getParametro("OrdenarFaixaEtaria");
        FaixaEtaria faixaEtaria = null;
        if (Coalesce.asString(ordenarFaixaEtaria, NAO).equals(RepositoryComponentDefault.SIM)) {
            faixaEtaria = BOFactory.getBO(CommomFacade.class).modulo(AGENDAMENTO).getParametro("FaixaEtaria");
            if (faixaEtaria != null) {
                hql.addToFrom("FaixaEtariaItem faixaEtariaItem");
                hql.addToWhereWhithAnd("faixaEtariaItem.id.faixaEtaria = ", faixaEtaria);
                hql.addToWhereWhithAnd("extract(year from age(usuarioCadsus.dataNascimento)) * 12 + extract(month from age(usuarioCadsus.dataNascimento)) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal");
            }
        }

        if(param.getExameProcedimento() != null){
            hql.addToWhereWhithAnd(" exists(select 1 from SolicitacaoAgendamentoExame sae left join sae.solicitacaoAgendamento sa left join sae.exameProcedimento ep WHERE sa.codigo = solicitacaoAgendamento.codigo AND ep.codigo = "+ param.getExameProcedimento().getCodigo() +") ");
        }

        String orderType = Coalesce.asString(param.getTipoOrdenacao(), "asc");
        String orderField = param.getCampoOrdenacao();

        if (StringUtils.trimToNull(param.getCampoOrdenacao()) != null) {
            hql.addToOrder(orderField + " " + orderType);
        }

        hql.addToOrder("solicitacaoAgendamento.prioridade");
        if (faixaEtaria != null) {
            hql.addToOrder("faixaEtariaItem.prioridade");
        }
        hql.addToOrder("solicitacaoAgendamentoPosicaoFila.posicaoFilaEspera");
        hql.addToOrder("Coalesce(solicitacaoAgendamento.dataAutorizador, solicitacaoAgendamento.dataSolicitacao)");
        hql.addToOrder("solicitacaoAgendamento.codigo");

        param.setParametroGem(BOFactory.getBO(CommomFacade.class).modulo(AGENDAMENTO).getParametro("tipoControleRegulação"));
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void setParameters(Query query) {
        query.setParameter("tipoFila", TIPO_FILA_NORMAL);
    }

    @Override
    protected Object executeQuery(Query query) {
        try {
            Integer paramGemLimiteLista = BOFactory.getBO(CommomFacade.class).modulo(AGENDAMENTO).getParametro("limiteSolicitacaoAgendamento");
            if (paramGemLimiteLista > 0 ){
                return super.executeQuery(query.setMaxResults(paramGemLimiteLista));
            }
            else {
                return super.executeQuery(query);
            }
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        return null;
    }

    private Long paramBloqueado(){
        return param.isBloqueados() ? SIM_LONG : NAO_LONG;
    }
}
