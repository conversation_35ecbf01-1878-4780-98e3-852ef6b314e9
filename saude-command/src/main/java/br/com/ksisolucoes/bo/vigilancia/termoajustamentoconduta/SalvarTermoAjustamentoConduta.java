package br.com.ksisolucoes.bo.vigilancia.termoajustamentoconduta;

import br.com.celk.util.Coalesce;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.AutosHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.EspecificacaoAtoFatoConstitutivo;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.FiscalTermoAjustamentoConduta;
import br.com.ksisolucoes.vo.vigilancia.termoajustamentoconduta.TermoAjustamentoConduta;
import ch.lambdaj.Lambda;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.lang.StringUtils;

import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class SalvarTermoAjustamentoConduta extends AbstractCommandTransaction<TermoAjustamentoConduta> {

    private TermoAjustamentoConduta termoAjustamentoConduta;
    private SalvarTermoAjustamentoCondutaDTO dto;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarTermoAjustamentoConduta(SalvarTermoAjustamentoCondutaDTO salvarTermoAjustamentoCondutaDTO) {
        this.termoAjustamentoConduta = salvarTermoAjustamentoCondutaDTO.getTermoAjustamentoConduta();
        this.dto = salvarTermoAjustamentoCondutaDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        boolean cadastro = termoAjustamentoConduta.getCodigo() == null;

        carregarConfiguracaoVigilancia();
        if (configuracaoVigilancia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
        }
        configurarNumeracao();

        String mensagemOcorrencia;
        if (termoAjustamentoConduta.getCodigo() != null) {
            mensagemOcorrencia = criaMensagemAlteracaoOcorrencia();
        } else {
            mensagemOcorrencia = Bundle.getStringApplication("msg_termo_ajustamento_cadastrado");
        }

        Bundle.getStringApplication("msg_termo_ajustamento_editado");

        termoAjustamentoConduta = BOFactory.save(dto.getTermoAjustamentoConduta());
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaTermoAjustamentoConduta(mensagemOcorrencia, termoAjustamentoConduta);

        if (CollectionUtils.isNotNullEmpty(dto.getLstFiscal())) {
            Lambda.forEach(dto.getLstFiscal()).setTermoAjustamentoConduta(termoAjustamentoConduta);
            VOUtils.persistirListaVosModificados(FiscalTermoAjustamentoConduta.class, dto.getLstFiscal(), new QueryCustom.QueryCustomParameter(FiscalTermoAjustamentoConduta.PROP_TERMO_AJUSTAMENTO_CONDUTA, termoAjustamentoConduta));
        }

        if (CollectionUtils.isNotNullEmpty(dto.getLstEspecificacaoAtoFatoConstitutivo())) {
            Lambda.forEach(dto.getLstEspecificacaoAtoFatoConstitutivo()).setTermoAjustamentoConduta(termoAjustamentoConduta);
            VOUtils.persistirListaVosModificados(EspecificacaoAtoFatoConstitutivo.class, dto.getLstEspecificacaoAtoFatoConstitutivo(), new QueryCustom.QueryCustomParameter(EspecificacaoAtoFatoConstitutivo.PROP_TERMO_AJUSTAMENTO_CONDUTA, termoAjustamentoConduta));
        }

        // anexos do auto de infração
        if (dto.getPnlRequerimentoVigilanciaAnexoDTO() != null) {
            BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(termoAjustamentoConduta, dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoDTOList(), dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoExcluidoDTOList());
        }

        if (CollectionUtils.isNotNullEmpty(dto.getLstRequerimentoVigilanciaAnexo())) {
            Lambda.forEach(dto.getLstRequerimentoVigilanciaAnexo()).setTermoAjustamentoConduta(termoAjustamentoConduta);
            VOUtils.persistirListaVosModificados(RequerimentoVigilanciaAnexo.class, dto.getLstRequerimentoVigilanciaAnexo(), new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaAnexo.PROP_TERMO_AJUSTAMENTO_CONDUTA, termoAjustamentoConduta));
        }

        if (cadastro) {
            if (termoAjustamentoConduta.getProcessoAdministrativoAutenticacao() == null) {
                gerarChaveProcesso();
                BOFactory.save(termoAjustamentoConduta);
            }
        }

        if (termoAjustamentoConduta.getFlagFinalizado().equals(TermoAjustamentoConduta.FINALIZADO)) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailSituacaoTermoAjustamentoConduta(termoAjustamentoConduta, false);
        }
    }

    private String criaMensagemAlteracaoOcorrencia() {
        StringBuilder builder = new StringBuilder();

        validaOcorrenciaBaseLegal(builder);
        validaOcorrenciaFiscal(builder);
        validaOcorrenciaEspecificacao(builder);
        validaOcorrenciaRequerimentoAnexo(builder);

        return builder.toString();
    }

    private void validaOcorrenciaBaseLegal(StringBuilder builder) {
        boolean isBaseLegalDiferente = !dto.getTermoAjustamentoOriginal().getBaseLegalTac()
                .equals(dto.getTermoAjustamentoConduta().getBaseLegalTac());

        if (isBaseLegalDiferente) {
            builder.append("Alterado a descrição da BaseLegal. \n");
            builder.append("Antes: ");
            builder.append(dto.getTermoAjustamentoOriginal().getBaseLegalTac().concat("\n"));
            builder.append("Depois: ");
            builder.append(dto.getTermoAjustamentoConduta().getBaseLegalTac().concat("\n"));
        }
    }

    private void validaOcorrenciaFiscal(StringBuilder builder) {
        Set<FiscalTermoAjustamentoConduta> fiscaisRemovidos = new HashSet<>(dto.getLstFiscalOriginal());
        fiscaisRemovidos.removeAll(dto.getLstFiscal());

        Set<FiscalTermoAjustamentoConduta> fiscaisAdicionados = new HashSet<>(dto.getLstFiscal());
        fiscaisAdicionados.removeAll(dto.getLstFiscalOriginal());

        if (CollectionUtils.isNotNullEmpty(fiscaisRemovidos)) {
            for (FiscalTermoAjustamentoConduta removido : fiscaisRemovidos) {
                builder.append("\n");
                builder.append("Excluído fiscal (");
                builder.append(removido.getProfissional().getNome().concat(")"));
            }
        }

        if (CollectionUtils.isNotNullEmpty(fiscaisAdicionados)) {
            for (FiscalTermoAjustamentoConduta adicionado : fiscaisAdicionados) {
                builder.append("\n");
                builder.append("Adicionado fiscal (");
                builder.append(adicionado.getProfissional().getNome().concat(")"));
            }
        }
    }

    private void validaOcorrenciaEspecificacao(StringBuilder builder) {
        Set<EspecificacaoAtoFatoConstitutivo> especificacoesRemovidos = new HashSet<>(dto.getLstEspecificacaoAtoFatoConstitutivoOriginal());
        especificacoesRemovidos.removeAll(dto.getLstEspecificacaoAtoFatoConstitutivo());

        Set<EspecificacaoAtoFatoConstitutivo> especificacoesAdicionados = new HashSet<>(dto.getLstEspecificacaoAtoFatoConstitutivo());
        especificacoesAdicionados.removeAll(dto.getLstEspecificacaoAtoFatoConstitutivoOriginal());

        for (EspecificacaoAtoFatoConstitutivo especificacaoOriginal : especificacoesAdicionados) {
            for (EspecificacaoAtoFatoConstitutivo especificacaoNovo : especificacoesRemovidos) {
                if (especificacaoOriginal.getCodigo().equals(especificacaoNovo.getCodigo())) {

                    if (!especificacaoOriginal.getDescricaoCondutaAjustada().equals(especificacaoNovo.getDescricaoCondutaAjustada())) {
                        builder.append("Alterado a descrição da conduta ajustada. \n");
                        builder.append("Antes: ");
                        builder.append(especificacaoOriginal.getDescricaoCondutaAjustada().concat("\n"));
                        builder.append("Depois: ");
                        builder.append(especificacaoNovo.getDescricaoCondutaAjustada().concat("\n"));
                    }

                    if (!especificacaoOriginal.getObservacao().equals(especificacaoNovo.getObservacao())) {
                        builder.append("Alterado a observação da conduta ajustada. \n");
                        builder.append("Antes: ");
                        builder.append(especificacaoOriginal.getObservacao().concat("\n"));
                        builder.append("Depois: ");
                        builder.append(especificacaoNovo.getObservacao().concat("\n"));
                    }

                    if (!especificacaoOriginal.getLegislacao().equals(especificacaoNovo.getLegislacao())) {
                        builder.append("Alterado a legislação da conduta ajustada. \n");
                        builder.append("Antes: ");
                        builder.append(especificacaoOriginal.getObservacao().concat("\n"));
                        builder.append("Depois: ");
                        builder.append(especificacaoNovo.getObservacao().concat("\n"));
                    }
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(especificacoesRemovidos)) {
            for (EspecificacaoAtoFatoConstitutivo removido : especificacoesRemovidos) {
                builder.append("\n");
                builder.append("Excluído conduta ajustada (");
                builder.append(removido.getDescricaoCondutaAjustada().concat(")"));
            }
        }

        if (CollectionUtils.isNotNullEmpty(especificacoesAdicionados)) {
            for (EspecificacaoAtoFatoConstitutivo adicionado : especificacoesAdicionados) {
                builder.append("\n");
                builder.append("Adicionado conduta ajustada (");
                builder.append(adicionado.getDescricaoCondutaAjustada().concat(")"));
            }
        }
    }

    private void validaOcorrenciaRequerimentoAnexo(StringBuilder builder) {
        if (CollectionUtils.isNotNullEmpty(dto.getLstRequerimentoVigilanciaAnexoDTOListOriginal())) {
            Set<RequerimentoVigilanciaAnexoDTO> requerimentosRemovidos = new HashSet<>(dto.getLstRequerimentoVigilanciaAnexoDTOListOriginal());
            requerimentosRemovidos.removeAll(dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoDTOList());

            Set<RequerimentoVigilanciaAnexoDTO> requerimentosAdicionados = new HashSet<>(dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoDTOList());
            requerimentosAdicionados.removeAll(dto.getLstRequerimentoVigilanciaAnexoDTOListOriginal());

            if (CollectionUtils.isNotNullEmpty(requerimentosRemovidos)) {
                for (RequerimentoVigilanciaAnexoDTO removido : requerimentosRemovidos) {
                    builder.append("\n");
                    builder.append("Excluído anexo (");
                    builder.append(removido.getNomeArquivoOriginal().concat(")"));
                }
            }

            if (CollectionUtils.isNotNullEmpty(requerimentosAdicionados)) {
                for (RequerimentoVigilanciaAnexoDTO adicionado : requerimentosAdicionados) {
                    builder.append("\n");
                    builder.append("Adicionado anexo (");
                    builder.append(adicionado.getNomeArquivoOriginal().concat(")"));
                }
            }
        } else if (!CollectionUtils.isNotNullEmpty(dto.getLstRequerimentoVigilanciaAnexoDTOListOriginal())
                && CollectionUtils.isNotNullEmpty(dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoDTOList())) {
            for (RequerimentoVigilanciaAnexoDTO adicionado : dto.getPnlRequerimentoVigilanciaAnexoDTO().getRequerimentoVigilanciaAnexoDTOList()) {
                builder.append("\n");
                builder.append("Adicionado anexo (");
                builder.append(adicionado.getDescricaoAnexoFormatado().concat(")"));
            }
        }
    }


    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    private void configurarNumeracao() throws ValidacaoException, DAOException {
        if (termoAjustamentoConduta.getNumero() == null && termoAjustamentoConduta.getCodigo() == null) {
            if (configuracaoVigilancia.getAnoBaseGeral() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
            }
            if (configuracaoVigilancia.getNumTermoAjustamentoConduta() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numeracao_base_termo_ajustamento_conduta"));
            }

            long sequencial = 0L;
            if (configuracaoVigilancia != null && Coalesce.asLong(configuracaoVigilancia.getNumTermoAjustamentoConduta()) > 0L) {
                sequencial = configuracaoVigilancia.getNumTermoAjustamentoConduta();
            }
            sequencial++;
            String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilancia.getAnoBaseGeral()));
            Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
            termoAjustamentoConduta.setNumero(nextId);

            configuracaoVigilancia.setNumTermoAjustamentoConduta(sequencial);

            BOFactory.save(configuracaoVigilancia);
        }
    }

    private void gerarChaveProcesso() throws ValidacaoException, DAOException {

            GerarChaveProcessoAdministrativoDTO dto = new GerarChaveProcessoAdministrativoDTO();
            dto.setCodigoAuto(termoAjustamentoConduta.getCodigo());
            dto.setCpf(AutosHelper.getCpfAuto(termoAjustamentoConduta));
            dto.setNome(AutosHelper.getNomeResponsavel(termoAjustamentoConduta));
            dto.setEmail(AutosHelper.getEmailAutuado(termoAjustamentoConduta));
            dto.setTelefone(AutosHelper.getTelefoneAutuado(termoAjustamentoConduta));
            ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao = BOFactory.getBO(VigilanciaFacade.class).gerarChaveProcessoAdministrativo(dto);
            if (processoAdministrativoAutenticacao == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ocorreu_erro_gerar_chave_acesso_auto"));
            }
            this.termoAjustamentoConduta.setProcessoAdministrativoAutenticacao(processoAdministrativoAutenticacao);

    }

    public TermoAjustamentoConduta getTermoAjustamentoConduta() {
        return termoAjustamentoConduta;
    }
}
