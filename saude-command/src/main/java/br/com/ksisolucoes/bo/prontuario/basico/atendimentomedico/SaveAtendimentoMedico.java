package br.com.ksisolucoes.bo.prontuario.basico.atendimentomedico;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoMedico;

/**
 * <AUTHOR>
 * <AUTHOR>
 */
public class SaveAtendimentoMedico extends SaveVO {

    private static final long serialVersionUID = 1L;

    private AtendimentoMedico atendimentoMedico;

    public SaveAtendimentoMedico(Object vo) {
        super( vo );
        this.atendimentoMedico = (AtendimentoMedico) vo;
    }

    public AtendimentoMedico getAtendimentoMedico() {
        return atendimentoMedico;
    }

}
