package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoprojetohidrossanitario;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.AnexoPranchaDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioAnexo;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 * Created by sulivan on 26/02/19.
 */
public class SalvarAnexosPranchaProjetoHidrossanitario extends AbstractCommandTransaction {

    private RequerimentoProjetoHidrossanitario projeto;
    private List<AnexoPranchaDTO> anexosPranchas;
    private List<AnexoPranchaDTO> anexosPranchasExcluir;

    public SalvarAnexosPranchaProjetoHidrossanitario(
            RequerimentoProjetoHidrossanitario projeto,
            List<AnexoPranchaDTO> anexosPranchas,
            List<AnexoPranchaDTO> anexosPranchasExcluir
    ) {
        this.projeto = projeto;
        this.anexosPranchas = anexosPranchas;
        this.anexosPranchasExcluir = anexosPranchasExcluir;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(anexosPranchas)) {
            for (AnexoPranchaDTO anexoDTO : anexosPranchas) {
                RequerimentoProjetoHidrossanitarioAnexo anexo = new RequerimentoProjetoHidrossanitarioAnexo();
                if (projeto != null) {
                    anexo.setRequerimentoProjetoHidrossanitario(projeto);

                    if (anexoDTO.getRequerimentoProjetoHidrossanitarioAnexo() != null && anexoDTO.getRequerimentoProjetoHidrossanitarioAnexo().getCodigo() != null) {
                        continue;
                    }

                    if (anexoDTO.getOrigemArquivo() == null) {
                        anexoDTO.setOrigemArquivo(GerenciadorArquivo.OrigemArquivo.PRANCHA_PROJ_HIDROSSANITARIO);
                    }

                    GerenciadorArquivo ga = BOFactory.getBO(ComunicacaoFacade.class).enviarArquivoFtp(anexoDTO.getFile(), anexoDTO.getOrigemArquivo().value(), anexoDTO.getNomeArquivoOriginal());

                    anexo.setGerenciadorArquivo(ga);
                    anexo.setDescricao(anexoDTO.getDescricaoAnexo());
                    anexo.setSituacao(anexoDTO.getSituacao());
                    anexo.setUsuario(Coalesce.asObj(anexoDTO.getUsuarioCadastro(), getSessao().<Usuario>getUsuario()));
                    anexo.setDataCadastro(anexoDTO.getDataCadastro());

                    BOFactory.save(anexo);
                }
            }
        }
        excluirAnexos();

    }

    private void excluirAnexos() throws DAOException, ValidacaoException {
        if (CollectionUtils.isNotNullEmpty(anexosPranchasExcluir)) {
            for (AnexoPranchaDTO anexoExcluido : anexosPranchasExcluir) {
                List<RequerimentoProjetoHidrossanitarioAnexo> list = this.getSession().createCriteria(RequerimentoProjetoHidrossanitarioAnexo.class)
                        .add(Restrictions.eq(RequerimentoProjetoHidrossanitarioAnexo.PROP_CODIGO, anexoExcluido.getRequerimentoProjetoHidrossanitarioAnexo().getCodigo()))
                        .list();

                if (CollectionUtils.isNotNullEmpty(list)) {
                    for (RequerimentoProjetoHidrossanitarioAnexo anexo : list) {
                        BOFactory.delete(anexo);
                        BOFactory.delete(anexo.getGerenciadorArquivo());
                    }
                }
            }
        }
    }
}