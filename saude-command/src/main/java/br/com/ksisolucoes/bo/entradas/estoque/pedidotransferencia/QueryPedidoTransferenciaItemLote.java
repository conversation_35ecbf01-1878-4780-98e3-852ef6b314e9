package br.com.ksisolucoes.bo.entradas.estoque.pedidotransferencia;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.PedidoTransferenciaItemLoteDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItemLote;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryPedidoTransferenciaItemLote extends CommandQuery<QueryPedidoTransferenciaItemLote> {

    private PedidoTransferenciaItem pti;
    private List<PedidoTransferenciaItemLoteDTO> result;

    public QueryPedidoTransferenciaItemLote(PedidoTransferenciaItem pti) {
        this.pti = pti;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(PedidoTransferenciaItemLoteDTO.class.getName());
        
        for (String string : new HQLProperties(PedidoTransferenciaItemLote.class).getProperties()) {
            hql.addToSelect("pedidoTransferenciaItemLote." + string, "pedidoTransferenciaItemLote." + string);
        }
        
        hql.addToSelect("(select ge.dataValidade from GrupoEstoque ge"
                + "                     left join ge.id.estoqueEmpresa estoqueEmpresa "
                + "                     left join estoqueEmpresa.id.empresa empresa "
                + "                     left join estoqueEmpresa.id.produto produtoSub "
                + "             where empresa = empresaOrigem "
                + "             and produtoSub = produto "
                + "             and ge.id.grupo = pedidoTransferenciaItemLote.lote "
                + "             and ge.id.codigoDeposito = pedidoTransferencia.deposito.codigo)","dataValidade");
        
        hql.addToFrom("PedidoTransferenciaItemLote pedidoTransferenciaItemLote"
                + "                     join pedidoTransferenciaItemLote.pedidoTransferenciaItem pedidoTransferenciaItem"
                + "                     join pedidoTransferenciaItem.pedidoTransferencia pedidoTransferencia"
                + "                     join pedidoTransferenciaItem.produto produto"
                + "                     join pedidoTransferencia.empresaOrigem empresaOrigem");
        
        hql.addToWhereWhithAnd("pedidoTransferenciaItemLote.pedidoTransferenciaItem = ", pti);
        
        hql.setConvertToLeftJoin(true);
    }

    @Override
    public Collection getResult() {
        return this.result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }
    
}
