package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.unidadesaude.CiapHelper;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.covid19.FichaCovidUtils;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.AtendimentoHelper;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacaoV3;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.evolucaoprontuario.SalvarEvolucaoUnidade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EvolucaoUnidadeDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.esus.CboFichaEsusItem;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.grupos.EloGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.service.TempStore;
import org.hibernate.criterion.*;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.EVOLUCAO_MEDICO, refClass = EvolucaoUnidadeDTO.class)
public class ValidarEvolucaoUnidade extends AbstractCommandValidacaoV3<EvolucaoUnidadeDTO> {

    public ValidarEvolucaoUnidade(Atendimento atendimento) {
        super(atendimento, true, atendimento.getProfissional());
    }

    @Override
    public EvolucaoUnidadeDTO executarValidacao(EvolucaoUnidadeDTO object) throws DAOException, ValidacaoException {
        validarESUS(object);
        if (object != null) {

            if (FichaCovidUtils.isCondutaCovidInvalida(getAtendimento(), object.getCondutaCovid())) {
                validacao(Bundle.getStringApplication("msg_informe_conduta_covid"));
            }

            List<UsuarioCadsus> lstUsuarioCadsusValidacao = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperty(UsuarioCadsus.PROP_CODIGO)
                    .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                    .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, getAtendimento().getUsuarioCadsus()))
                    .setMaxResults(1).start().getList();


            Date dataNascimento = null;
            if (CollectionUtils.isNotNullEmpty(lstUsuarioCadsusValidacao)) {
                dataNascimento = lstUsuarioCadsusValidacao.get(0).getDataNascimento();
            }

            try {
                if (RepositoryComponentDefault.SIM_LONG.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("obrigatorioInformarCIAP")) && object.getCiap() == null) {
                    throw new ValidacaoException("Por favor, informe o CIAP.");
                }
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            }

            if (object.getDumGestante() != null && dataNascimento != null) {
                if (object.getDumGestante().before(dataNascimento)) {
                    validacao(Bundle.getStringApplication("msg_data_dum_menor_menstruacao"));
                }
            }

            if (object.getCiap() != null) {
                atendimento.setCiap(object.getCiap());
            }

            if (object.getDescricaoEvolucao() == null) {
                validarEvolucaoObrigatoria();
            }

            if (object.getClassificacaoAtendimento() == null) {
                validacao(Bundle.getStringApplication("msg_informe_classificacao_atendimendo"));
            }

            if (object.getCid() == null && !validarBloqueioDeCidPorCboEAtendimento(this.atendimento)) {
                validacao(Bundle.getStringApplication("msg_informe_cid"));
            } else {
                if (object.getDataPrimeirosSintomasCID() != null && object.getDataPrimeirosSintomasCID().after(DataUtil.getDataAtual())) {
                    validacao(Bundle.getStringApplication("msg_data_maior_que_atual_sintomas"));
                }
            }

            if (object.getTipoAtendimentoEsus() == null) {
                validacao(Bundle.getStringApplication("msg_informe_tipo_atendimento"));
            }

//            O campo conduta deve ser obrigatório quando o tipo do atendimento estiver configurado para o e-sus
//            tipo_atendimento.tp_atendimento_esus <> NULO
            if (atendimento != null && atendimento.getNaturezaProcuraTipoAtendimento() != null && atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento() != null
                    && atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoAtendimentoEsus() != null && object.getConduta() == null && Coalesce.asLong(object.getDiasRetorno()) == 0) {
                validacao(Bundle.getStringApplication("msg_informe_conduta"));
            } else {
                validarRetorno(object);
            }

            if (object.getProfissionalAuxiliar() != null) {
                if (object.getProfissionalAuxiliar().equals(atendimento.getProfissional())) {
                    validacao(Bundle.getStringApplication("msg_profissional_aux_nao_pode_ser_mesmo_profissional_atendimento"));
                } else if (object.getCboProfissionalAuxiliar() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_cbo_profissional_auxiliar"));
                }
            }

            if (RepositoryComponentDefault.SIM_LONG.equals(object.getDiarreia())) {
                if (object.getAtendimentoMDDA().getComSangue() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_diarreia_com_sangue"));
                }

                if (object.getAtendimentoMDDA().getDataPrimeirosSintomas() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_data_primeiros_sintomas"));
                }

                if (object.getAtendimentoMDDA().getPlanoTratamento() == null) {
                    validacao(Bundle.getStringApplication("msg_informe_plano_Tratamento"));
                }
            }


            validarCiapSexoPaciente(object.getCiap());

            PreNatal preNatal = UsuarioCadsusHelper.getPreNatal(getAtendimento().getUsuarioCadsus());
            if (preNatal != null) {
                if (preNatal.getDataUltimaMenstruacao() != null) {
                    object.setDumGestante(preNatal.getDataUltimaMenstruacao());
                }
            }
            AtendimentoHelper.validarCamposGestacao(atendimento, object.getDumGestante(), object.getIdadeGestacional(), this);

            if (object.getCid() != null && object.getCid().getFlagRegistroDiarreia() != null) {
                validarRelacaoCidComRegistroDiarreia(object.getCid(), object.getDiarreia());
            }

            if (object.getCidSecundario() != null && object.getCidSecundario().getFlagRegistroDiarreia() != null) {
                validarRelacaoCidComRegistroDiarreia(object.getCidSecundario(), object.getDiarreia());
            }
        } else {
            validarEvolucaoObrigatoria();
        }


        return object;
    }

    public boolean validarBloqueioDeCidPorCboEAtendimento(Atendimento atendimento) {
        return validaExistenciaCBO(atendimento) && validaTipoAtendimento(atendimento);
    }

    private boolean validaExistenciaCBO(Atendimento atendimento) {
        return LoadManager.getInstance(CboFichaEsusItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CboFichaEsusItem.PROP_TABELA_CBO), atendimento.getProfissional().getCboProfissional(atendimento.getEmpresa())))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CboFichaEsusItem.PROP_FLAG_INFORMAR_CID), RepositoryComponentDefault.SIM_LONG))
                .start().exists();
    }

    private boolean validaTipoAtendimento(Atendimento atendimento) {
        return LoadManager.getInstance(TipoAtendimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoAtendimento.PROP_SITUACAO), RepositoryComponentDefault.ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoAtendimento.PROP_TIPO_ATENDIMENTO_ESUS), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoAtendimento.PROP_DESCRICAO), atendimento.getTipoAtendimentoConcatenadoTipoProcedimento()))
                .start().exists();
    }

    public void validarCiapSexoPaciente(Ciap ciap) {
        boolean ciapInvalido = CiapHelper.isInvalidoCiapSexoPaciente(ciap, getAtendimento());
        if (ciapInvalido) {
            validacao(Bundle.getStringApplication("ciap_nao_valido_sexo_paciente"));
        }
    }

    private void validarEvolucaoObrigatoria() {
        DetachedCriteria dc = DetachedCriteria.forClass(EloGrupoAtendimentoCbo.class, "eloGrupo")
                .add(Restrictions.eq(EloGrupoAtendimentoCbo.PROP_TABELA_CBO, atendimento.getTabelaCbo()))
                .setProjection(Projections.property(EloGrupoAtendimentoCbo.PROP_CODIGO))
                .add(Property.forName("eloGrupo.grupoAtendimentoCbo").eqProperty("eloTipo.grupoAtendimentoCbo"));

        EloTipoAtendimentoGrupoAtendimentoCbo elo = (EloTipoAtendimentoGrupoAtendimentoCbo) getSession().createCriteria(EloTipoAtendimentoGrupoAtendimentoCbo.class, "eloTipo")
                .add(Restrictions.eq(EloTipoAtendimentoGrupoAtendimentoCbo.PROP_TIPO_ATENDIMENTO, atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                .add(Subqueries.exists(dc))
                .uniqueResult();
        if (elo != null && RepositoryComponentDefault.SIM_LONG.equals(elo.getExigeEvolucao())) {
            validacao(Bundle.getStringApplication("msg_nao_foi_feito_o_registro_evolucao_favor_registrar_para_concluir_atendimento"));
        }
    }

    private void validarRelacaoCidComRegistroDiarreia(Cid cid, Long registroDiarreia) {
        if (RepositoryComponentDefault.SIM_LONG.equals(cid.getFlagRegistroDiarreia()) && RepositoryComponentDefault.NAO_LONG.equals(registroDiarreia)) {
            validacao(Bundle.getStringApplication("msg_erro_cid_informado_exige_registro_diarreia"));
        }
    }


    private void validarRetorno(EvolucaoUnidadeDTO dto) {
        if (Coalesce.asLong(dto.getDiasRetorno()) > 0) {
            if (dto.getConduta() == null || RepositoryComponentDefault.NAO_LONG.equals(Coalesce.asLong(dto.getConduta().getFlagRetorno(), RepositoryComponentDefault.NAO_LONG))) {
                validacao(Bundle.getStringApplication("msg_informe_conduta_retorno"));
            }
        } else if (dto.getConduta() != null && RepositoryComponentDefault.SIM_LONG.equals(dto.getConduta().getFlagRetorno())) {
            validacao(Bundle.getStringApplication("msg_informe_dias_retorno"));
        }
    }

    private void validarESUS(EvolucaoUnidadeDTO object) throws ValidacaoException, DAOException {
        Long tipoClassificacao = getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getTipoClassificacao();
        if (TipoAtendimento.TipoClassificacao.ENFERMAGEM_MEDICA.value().equals(tipoClassificacao)) {
            if (TabelaCbo.NivelEnsino.SUPERIOR.value().equals(getAtendimento().getTabelaCbo().getNivelEnsino())) {
                getSessao().putClientProperty("VALIDACAO_ESUS_CONDUTA_" + getAtendimento().getCodigo(), true);
                getSessao().putClientProperty("VALIDACAO_ESUS_CLASS_ATEND_" + getAtendimento().getCodigo(), true);
                if (object == null) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta_tipo_atendimento"));
                } else {
                    if ((object.getCidSecundario() != null) && (object.getCidSecundario().equals(object.getCid()))) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_cidSec_nao_pode_ser_igual_cid"));
                    } else if (object.getConduta() == null && object.getClassificacaoAtendimento() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta_classificacao_atendimento"));
                    } else if (object.getConduta() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_conduta"));
                    } else if (object.getClassificacaoAtendimento() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_classificacao_atendimento"));
                    } else if (object.getTipoAtendimentoEsus() == null) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_faturamento_esus_necessario_preencher_tipo_atendimento"));
                    }
                }
            }
        }
    }

    @Override
    public void processar(EvolucaoUnidadeDTO object) throws DAOException, ValidacaoException {
    }

    @Override
    public void processar(EvolucaoUnidadeDTO object, boolean lastTemp, TempStore tempStore) throws DAOException, ValidacaoException {
        if (object != null) {
            new SalvarEvolucaoUnidade(atendimento, object, lastTemp, tempStore.getProfissional(), tempStore.getTabelaCbo()).start();
        }
    }

}
