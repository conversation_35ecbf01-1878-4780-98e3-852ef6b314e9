package br.com.ksisolucoes.bo.prontuario.basico.naturezaprocuratipoatendimento;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.EmpresaNaturezaTipoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NaturezaProcuraTipoAtendimentoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EloNaturezaTipoEncaminhamento;
import br.com.ksisolucoes.vo.prontuario.encaminhamento.EncaminhamentoTipo;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ClonarNaturezaProcuraTipoAtendimento extends AbstractCommandTransaction<ClonarNaturezaProcuraTipoAtendimento> {

    private Long codigo;
    private NaturezaProcuraTipoAtendimentoDTO naturezaProcuraTipoAtendimentoDTO;

    public ClonarNaturezaProcuraTipoAtendimento(Long codigo) {
        this.codigo = codigo;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        NaturezaProcuraTipoAtendimento nptaOriginal;
        nptaOriginal = carregarNaturezaProcuraTipoAtendimento();

        NaturezaProcuraTipoAtendimento nptaCopia;        
        nptaCopia = VOUtils.cloneObject(nptaOriginal);
        nptaCopia.setTipoAtendimento(null);
        nptaCopia.setNaturezaProcura(null);

        naturezaProcuraTipoAtendimentoDTO = new NaturezaProcuraTipoAtendimentoDTO();
        naturezaProcuraTipoAtendimentoDTO.setNaturezaProcuraTipoAtendimento(nptaCopia);
        
        naturezaProcuraTipoAtendimentoDTO.setEmpresaNaturezaTipoDTOList(clonarEmpresaNaturezaTipoAtendimento(nptaOriginal, nptaCopia));
        naturezaProcuraTipoAtendimentoDTO.setElosNaturezaTipoEncaminhamentoList(clonarElosNaturezaTipoEncaminhamento(nptaOriginal));
    }

    private NaturezaProcuraTipoAtendimento carregarNaturezaProcuraTipoAtendimento() {
        return LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                .addProperties(new HQLProperties(NaturezaProcuraTipoAtendimento.class).getProperties())
                .setId(codigo)
                .start().getVO();
    }

    private List<EmpresaNaturezaTipoDTO> clonarEmpresaNaturezaTipoAtendimento(NaturezaProcuraTipoAtendimento nptaOriginal, NaturezaProcuraTipoAtendimento nptaCopia) throws DAOException, ValidacaoException {
        List<EmpresaNaturezaProcuraTipoAtendimento> empresasList = LoadManager.getInstance(EmpresaNaturezaProcuraTipoAtendimento.class)
                .addProperties(new HQLProperties(EmpresaNaturezaProcuraTipoAtendimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA), nptaOriginal.getNaturezaProcura()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO), nptaOriginal.getTipoAtendimento()))
                .start().getList();

        List<EmpresaNaturezaTipoDTO> empresaNaturezaTipoDTOList = new ArrayList<EmpresaNaturezaTipoDTO>();
        for (EmpresaNaturezaProcuraTipoAtendimento enptaOriginal : empresasList) {
            EmpresaNaturezaTipoDTO dto = new EmpresaNaturezaTipoDTO();

            EmpresaNaturezaProcuraTipoAtendimento enptaCopia = VOUtils.cloneObject(enptaOriginal);
            enptaCopia.setNaturezaProcuraTipoAtendimento(nptaCopia);
            dto.setEmpresaNaturezaProcuraTipoAtendimento(enptaCopia);
            
            empresaNaturezaTipoDTOList.add(dto);
        }
        return empresaNaturezaTipoDTOList;
    }

    private List<EloNaturezaTipoEncaminhamento> clonarElosNaturezaTipoEncaminhamento(NaturezaProcuraTipoAtendimento nptaOriginal) {
        List<EloNaturezaTipoEncaminhamento> eloOriginalList = LoadManager.getInstance(EloNaturezaTipoEncaminhamento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO), nptaOriginal))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DESCRICAO)))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EloNaturezaTipoEncaminhamento.PROP_ENCAMINHAMENTO_TIPO, EncaminhamentoTipo.PROP_DESCRICAO)))
                .start().getList();

        List<EloNaturezaTipoEncaminhamento> eloCopiaList = new ArrayList<EloNaturezaTipoEncaminhamento>();
        for (EloNaturezaTipoEncaminhamento eloOriginal : eloOriginalList) {
            EloNaturezaTipoEncaminhamento eloCopia = VOUtils.cloneObject(eloOriginal);
            eloCopiaList.add(eloCopia);
        }

        return eloCopiaList;
    }

    public NaturezaProcuraTipoAtendimentoDTO getNaturezaProcuraTipoAtendimentoDTO() {
        return naturezaProcuraTipoAtendimentoDTO;
    }

    public void setNaturezaProcuraTipoAtendimentoDTO(NaturezaProcuraTipoAtendimentoDTO naturezaProcuraTipoAtendimentoDTO) {
        this.naturezaProcuraTipoAtendimentoDTO = naturezaProcuraTipoAtendimentoDTO;
    }
}