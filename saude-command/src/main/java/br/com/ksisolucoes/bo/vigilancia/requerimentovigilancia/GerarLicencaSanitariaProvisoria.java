package br.com.ksisolucoes.bo.vigilancia.requerimentovigilancia;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.GerarAlvaraProvisorioDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoAlvaraDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAlvara;

/**
 * Created by haila on 25/11/2024.
 */
public class GerarLicencaSanitariaProvisoria extends AbstractCommandTransaction {

    private GerarAlvaraProvisorioDTO dto;

    public GerarLicencaSanitariaProvisoria(GerarAlvaraProvisorioDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoAlvara requerimentoAlvara = (RequerimentoAlvara) getSession().createCriteria(RequerimentoAlvara.class)
                .add(Restrictions.eq(RequerimentoAlvara.PROP_REQUERIMENTO_VIGILANCIA, dto.getRequerimentoVigilancia()))
                .uniqueResult();

        requerimentoAlvara.setDataValidadeProvisoria(dto.getDataValidade());
        BOFactory.save(requerimentoAlvara);
    }

    public DataReport getReport() throws ReportException {
        return BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoLicencaSanitariaProvisoria(getImpressaoAlvaraDTOParam(dto.getRequerimentoVigilancia()));
    }

    private ImpressaoAlvaraDTOParam getImpressaoAlvaraDTOParam(RequerimentoVigilancia requerimentoVigilancia) {
        ImpressaoAlvaraDTOParam dtoParam = new ImpressaoAlvaraDTOParam();
        dtoParam.setRequerimentoVigilancia(requerimentoVigilancia);
        dtoParam.setEstabelecimento(requerimentoVigilancia.getEstabelecimento());
        dtoParam.setChaveQrCode(requerimentoVigilancia.getChaveQRcode());
        return dtoParam;
    }
}
