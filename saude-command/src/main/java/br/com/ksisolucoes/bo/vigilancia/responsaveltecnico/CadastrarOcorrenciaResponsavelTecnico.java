/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.vigilancia.responsaveltecnico;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.*;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CadastrarOcorrenciaResponsavelTecnico extends AbstractCommandTransaction {

    private OcorrenciaResponsavelTecnico ocorrencia;
    private ResponsavelTecnico responsavelTecnico;
    private Map<String, Map<String, String>> camposAlterados;

    public CadastrarOcorrenciaResponsavelTecnico(Map<String, Map<String, String>> camposAlterados, ResponsavelTecnico responsavelTecnico) {
        this.responsavelTecnico = responsavelTecnico;
        this.camposAlterados = camposAlterados;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (camposAlterados != null && camposAlterados.entrySet() != null){
            ocorrencia = new OcorrenciaResponsavelTecnico();
            ocorrencia.setDataAlteracao(DataUtil.getDataAtual());
            ocorrencia.setDescricaoAlteracao(criarAlteracoes());
            ocorrencia.setResponsavelTecnico(responsavelTecnico);

            if (getSessao() == null) {
                ocorrencia.setUsuario(new Usuario(Usuario.USUARIO_ADMINISTRADOR));
            } else {
                ocorrencia.setUsuario(getSessao().<Usuario>getUsuario());
            }

            ocorrencia = BOFactory.save(ocorrencia);
        }
    }

    private String criarAlteracoes() {
        StringBuilder sb = new StringBuilder();
        String motivo = responsavelTecnico.getVersion().equals(0L) ? Bundle.getStringApplication("msg_registro_cadastrado") : Bundle.getStringApplication("msg_registro_editado");
        sb.append(motivo);
        sb.append(System.lineSeparator());

        sb.append(getDescricaoCamposAlterados());

        return sb.toString();
    }

    private String getDescricaoCamposAlterados() {
        StringBuilder sb = new StringBuilder();

        for (Map.Entry<String, Map<String, String>> m : camposAlterados.entrySet()) {
            sb.append(m.getKey() + ": ");
            sb.append(m.getValue().get("before") + " ==> " + m.getValue().get("after"));
            sb.append(System.lineSeparator());
        }

        return sb.toString();
    }

    public OcorrenciaResponsavelTecnico getOcorrencia() {
        return ocorrencia;
    }

    public void setOcorrencia(OcorrenciaResponsavelTecnico ocorrencia) {
        this.ocorrencia = ocorrencia;
    }

    public ResponsavelTecnico getResponsavelTecnico() {
        return responsavelTecnico;
    }

    public void setResponsavelTecnico(ResponsavelTecnico responsavelTecnico) {
        this.responsavelTecnico = responsavelTecnico;
    }


}
