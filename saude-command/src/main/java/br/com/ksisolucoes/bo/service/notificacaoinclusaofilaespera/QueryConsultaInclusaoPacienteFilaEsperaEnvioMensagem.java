package br.com.ksisolucoes.bo.service.notificacaoinclusaofilaespera;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.service.sms.QueryConsultaAgendamentosNaoEntreguesSms;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import org.hibernate.Query;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class QueryConsultaInclusaoPacienteFilaEsperaEnvioMensagem extends CommandQuery {

    private List<SolicitacaoAgendamento> list;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(SolicitacaoAgendamento.class.getName());

        hql.addToSelect("sa.codigo", true);
        hql.addToSelect("sa.dataCadastro", true);
        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "usuarioCadsus.nome");
        hql.addToSelect("uc.celular", "usuarioCadsus.celular");
        hql.addToSelect("tp.codigo", "tipoProcedimento.codigo");
        hql.addToSelect("tp.descricao", "tipoProcedimento.descricao");

        hql.addToFrom("SolicitacaoAgendamento sa " +
                "join sa.usuarioCadsus uc " +
                "join sa.tipoProcedimento tp");

        hql.addToWhereWhithAnd("uc.celular is not null");
        hql.addToWhereWhithAnd("tp.enviaSms = ", RepositoryComponentDefault.SIM);
        hql.addToWhereWhithAnd("(sa.status = " + SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE);
        hql.addToWhereWhithOr("  sa.status = " + SolicitacaoAgendamento.STATUS_FILA_ESPERA + ")");
        hql.addToWhereWhithAnd("not exists (" +
                "select 1 " +
                "  from SmsControleIntegracao sci1 " +
                " where sci1.solicitacaoAgendamento = sa " +
                "  and sci1.tipoMensagem = " + SmsControleIntegracao.TipoMensagem.INCLUSAO_FILA_ESPERA.value() +
                "  and sci1.statusSms in (" + SmsControleIntegracao.StatusSms.ENCAMINHADO.value() + "," + SmsControleIntegracao.StatusSms.ENVIADO.value() + "))");
        hql.addToWhereWhithAnd("sa.dataCadastro >= :dataAtual");

    }

    @Override
    public List getResult() {
        return this.list;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result,false);
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setDate("dataAtual", DataUtil.getDataAtualSemHora());
    }
}
