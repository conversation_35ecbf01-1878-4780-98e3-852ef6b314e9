package br.com.ksisolucoes.bo.vacina.controletemperaturavacina;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.ControleTemperaturaVacina;

/**
 * Created by laudecir on 30/06/17.
 */
public class SaveControleTemperaturaVacina extends SaveVO<ControleTemperaturaVacina> {

    public SaveControleTemperaturaVacina(ControleTemperaturaVacina vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        vo.setUsuario(getSessao().getUsuario());
        vo.setDataUsuario(DataUtil.getDataAtual());

        if (vo.getEmpresa() == null) {
            vo.setEmpresa(getSessao().getEmpresa());
        }

        if (vo.getDataCadastro() == null) {
            vo.setDataCadastro(DataUtil.getDataAtual());
        }

        if (vo.getTurno() == null) {
            defineTurno();
        }
    }

    private void defineTurno() {
        Long turno = DataUtil.turnoData(vo.getHora());
        if (turno.equals(DataUtil.TURNO_MANHA)) {
            vo.setTurno(ControleTemperaturaVacina.Turno.MANHA.value());
        } else if (turno.equals(DataUtil.TURNO_TARDE)) {
            vo.setTurno(ControleTemperaturaVacina.Turno.TARDE.value());
        } else if (turno.equals(DataUtil.TURNO_NOITE)) {
            vo.setTurno(ControleTemperaturaVacina.Turno.NOITE.value());
        }
    }
}
