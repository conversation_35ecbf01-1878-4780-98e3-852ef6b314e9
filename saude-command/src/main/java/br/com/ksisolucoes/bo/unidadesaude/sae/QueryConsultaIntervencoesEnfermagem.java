package br.com.ksisolucoes.bo.unidadesaude.sae;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.sae.dto.QueryConsultaIntervencoesEnfermagemDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExameAgendamentoDTOParam;
import br.com.ksisolucoes.vo.prontuario.basico.IntervencaoEnfermagem;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaIntervencoesEnfermagem extends CommandQueryPager<QueryConsultaIntervencoesEnfermagemDTOParam> {

    private QueryConsultaIntervencoesEnfermagemDTOParam param;

    public QueryConsultaIntervencoesEnfermagem(QueryConsultaIntervencoesEnfermagemDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("intervencaoEnfermagem.codigo", "codigo");
        hql.addToSelect("intervencaoEnfermagem.referencia", "referencia");
        hql.addToSelect("intervencaoEnfermagem.descricao", "descricao");

        hql.setTypeSelect(IntervencaoEnfermagem.class.getName());
        hql.addToFrom("IntervencaoEnfermagem intervencaoEnfermagem");

        hql.addToWhereWhithAnd(hql.getConsultaLiked("intervencaoEnfermagem.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("intervencaoEnfermagem.referencia || ' ' || intervencaoEnfermagem.descricao", param.getKeyword()));

        if (param.getDiagnosticoEnfermagemSae() != null && param.getResultadoEsperado() != null) {
            hql.addToWhereWhithAnd("EXISTS (SELECT 1 FROM EloDiagEnfSaeResEsperIntervEnf elo WHERE intervencaoEnfermagem.codigo = elo.intervencaoEnfermagem and elo.resultado.codigo = " + param.getResultadoEsperado().getCodigo() + " AND elo.diagnostico.codigo = " + param.getDiagnosticoEnfermagemSae().getCodigo() + " )");
        }

        if (param.getPropSort() != null) {
            hql.addToOrder("intervencaoEnfermagem." + param.getPropSort() + " " + (param.isAscending() ? RelatorioResumoExameAgendamentoDTOParam.TipoOrdenacao.CRESCENTE.getCommand() : RelatorioResumoExameAgendamentoDTOParam.TipoOrdenacao.DECRESCENTE.getCommand()));
        } else {
            hql.addToOrder("intervencaoEnfermagem.descricao");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
