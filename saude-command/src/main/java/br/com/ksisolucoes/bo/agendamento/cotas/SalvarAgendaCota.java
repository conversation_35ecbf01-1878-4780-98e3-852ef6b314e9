package br.com.ksisolucoes.bo.agendamento.cotas;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.agendamento.cotas.dto.CadastroAgendaCotaDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.solicitacaoagendamento.SolicitacaoAgendamentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaCota;
import br.com.ksisolucoes.vo.agendamento.AgendaCotaProfissional;
import br.com.ksisolucoes.vo.basico.ParametroGem;
import br.com.ksisolucoes.vo.basico.ParametroGemPK;
import br.com.ksisolucoes.vo.prontuario.basico.UltimaAlteracaoAgendamento;

import static br.com.ksisolucoes.vo.agendamento.base.BaseAgendaCotaProfissional.PROP_AGENDA_COTA;
import static ch.lambdaj.Lambda.forEach;

/**
 *
 * <AUTHOR>
 */
public class SalvarAgendaCota extends AbstractCommandTransaction<SalvarAgendaCota>{

    private final CadastroAgendaCotaDTO dto;
    private AgendaCota agendaCota;

    public SalvarAgendaCota(CadastroAgendaCotaDTO dto) {
        this.dto = dto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        agendaCota = BOFactory.save(dto.getAgendaCota());
        if(resetFilaEsperaGem()) {
            BOFactory.getBO(SolicitacaoAgendamentoFacade.class).updateUltimaAlteracaoAgendamento(dto.getAgendaCota().getTipoProcedimento().getCodigo());
        } else {
            BOFactory.getBO(SolicitacaoAgendamentoFacade.class).updateUltimaAlteracaoAgendamento();
        }

        if (CollectionUtils.isNotNullEmpty(dto.getAgendaCotaProfissionalList())) {
            forEach(dto.getAgendaCotaProfissionalList()).setAgendaCota(agendaCota);
        }
    
        VOUtils.persistirListaVosModificados(AgendaCotaProfissional.class, dto.getAgendaCotaProfissionalList(), 
            new QueryCustom.QueryCustomParameter(PROP_AGENDA_COTA, agendaCota));
    }

    private boolean resetFilaEsperaGem() throws DAOException {
        return RepositoryComponentDefault.SIM_LONG.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("utilizaResetManualFilaEspera"));
    }

    public AgendaCota getAgendaCota() {
        return agendaCota;
    }
}
