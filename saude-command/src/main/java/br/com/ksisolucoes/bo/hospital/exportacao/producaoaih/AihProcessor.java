package br.com.ksisolucoes.bo.hospital.exportacao.producaoaih;

import br.com.ksisolucoes.bo.hospital.exportacao.producaoaih.dto.ProducaoAihDTO;
import br.com.ksisolucoes.util.Reflection;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import java.util.ArrayList;
import java.util.List;
import org.apache.camel.Exchange;
import org.apache.camel.Processor;

/**
 *
 * <AUTHOR>
 */
public class AihProcessor implements Processor {

    private ProducaoAihBind bind;

    @Override
    public void process(Exchange exchange) throws Exception {
        List<ProducaoAihDTO> list = (List<ProducaoAihDTO>) exchange.getIn().getBody();

        List<ProducaoAihBind> result = new ArrayList<ProducaoAihBind>();
        bind = new ProducaoAihBind();

        int i = 1;
        bind.setIdentificacaoAih(1L);
        for (ProducaoAihDTO producaoAihDTO : list) {

            bind.buildProperties(producaoAihDTO);

            if (i <= 9) {
                setItens(producaoAihDTO, i);
                i++;
            } else {
                bind = new ProducaoAihBind();

                setItens(producaoAihDTO, 1);
                bind.setIdentificacaoAih(3L);
                i = 2;
            }

            if (!result.contains(bind)) {
                result.add(bind);
            }
        }

        exchange.getOut().setBody(result);
    }

    private void setItens(ProducaoAihDTO producaoAihDTO, Integer posicao) throws Exception {
        if (producaoAihDTO.getItemContaPaciente() != null && producaoAihDTO.getItemContaPaciente().getIndicadorEquipeAih() != null) {
            Reflection.setValueByPattern(bind, producaoAihDTO.getItemContaPaciente().getIndicadorEquipeAih(), "pse" + posicao + "identificadorEquipe");
        }

        if (producaoAihDTO.getProfissionalItemConta() != null) {
            if (producaoAihDTO.getProfissionalItemConta().getCodigoCns() != null) {
                Reflection.setValueByPattern(bind, 2L, "pse" + posicao + "indicadorDocProfissional");
                Reflection.setValueByPattern(bind, producaoAihDTO.getProfissionalItemConta().getCodigoCns(), "pse" + posicao + "identificacaoProfissional");
            } else if (producaoAihDTO.getProfissionalItemConta().getCpf() != null) {
                Reflection.setValueByPattern(bind, 1L, "pse" + posicao + "indicadorDocProfissional");
                Reflection.setValueByPattern(bind, producaoAihDTO.getProfissionalItemConta().getCpf(), "pse" + posicao + "identificacaoProfissional");
            }
        }
        if (producaoAihDTO.getTabelaCbo() != null) {
            Reflection.setValueByPattern(bind, producaoAihDTO.getTabelaCbo().getCbo(), "pse" + posicao + "cboProfissional");
        }
        if (ItemContaPaciente.AtribuirValorPara.TERCEIRO.value().equals(producaoAihDTO.getItemContaPaciente().getAtribuirValorPara())) {
            if (producaoAihDTO.getEmpresaPrestador() != null) {
                Reflection.setValueByPattern(bind, 5L, "pse" + posicao + "indicadorDocumentoExecutor");
                Reflection.setValueByPattern(bind, producaoAihDTO.getEmpresaPrestador().getCnes(), "pse" + posicao + "identificadorExecutor");
            }
        } else {
            if (producaoAihDTO.getEmpresaPrincipal() != null) {
                Reflection.setValueByPattern(bind, 5L, "pse" + posicao + "indicadorDocumentoExecutor");
                Reflection.setValueByPattern(bind, producaoAihDTO.getEmpresaPrincipal().getCnes(), "pse" + posicao + "identificadorExecutor");
            }

        }
        if (producaoAihDTO.getEmpresaPrestador() != null && producaoAihDTO.getEmpresaPrestador().getCnes() != null) {
            Reflection.setValueByPattern(bind, 5L, "pse" + posicao + "identificadorPrestadorServico");
            Reflection.setValueByPattern(bind, producaoAihDTO.getEmpresaPrestador().getCnes(), "pse" + posicao + "identificacaoPrestadorServico");
        } else {
            Reflection.setValueByPattern(bind, 5L, "pse" + posicao + "identificadorPrestadorServico");
            Reflection.setValueByPattern(bind, producaoAihDTO.getEmpresaPrincipal().getCnes(), "pse" + posicao + "identificacaoPrestadorServico");
        }
        if (producaoAihDTO.getEmpresaPrincipal() != null) {
            Reflection.setValueByPattern(bind, Long.parseLong(producaoAihDTO.getItemProcedimento().getReferencia()), "pse" + posicao + "codigoProcedimento");
            Reflection.setValueByPattern(bind, producaoAihDTO.getItemContaPaciente().getQuantidade().longValue(), "pse" + posicao + "quantidadeProcedimento");
            Reflection.setValueByPattern(bind, producaoAihDTO.getItemContaPaciente().getDataLancamento(), "pse" + posicao + "competencia");
        }

        if (producaoAihDTO.getComplementarUtiNeonatal() != null) {
            Reflection.setValueByPattern(bind, producaoAihDTO.getComplementarUtiNeonatal().getDataCompetencia(), "pse" + posicao + "competencia");
            Reflection.setValueByPattern(bind, producaoAihDTO.getComplementarUtiNeonatal().getQuantidadeDias(), "pse" + posicao + "quantidadeProcedimento");
        }
    }
}
