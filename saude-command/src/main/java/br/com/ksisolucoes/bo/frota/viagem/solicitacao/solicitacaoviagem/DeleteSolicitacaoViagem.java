package br.com.ksisolucoes.bo.frota.viagem.solicitacao.solicitacaoviagem;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagem;
import br.com.ksisolucoes.vo.frota.viagem.solicitacao.SolicitacaoViagemOcorrencia;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class DeleteSolicitacaoViagem extends DeleteVO<SolicitacaoViagem> {

    public DeleteSolicitacaoViagem(SolicitacaoViagem vo) {
        super(vo);
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<SolicitacaoViagemOcorrencia> ocorrencias = getSession().createCriteria(SolicitacaoViagemOcorrencia.class)
                .add(Restrictions.eq(SolicitacaoViagemOcorrencia.PROP_SOLICITACAO_VIAGEM, this.vo))
                .list();

        for (SolicitacaoViagemOcorrencia solicitacaoViagemOcorrencia : ocorrencias) {
            BOFactory.delete(solicitacaoViagemOcorrencia);
        }
    }
}
