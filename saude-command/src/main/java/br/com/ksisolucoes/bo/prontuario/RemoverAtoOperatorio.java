package br.com.ksisolucoes.bo.prontuario;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.operacao.AtoOperatorio;
import br.com.ksisolucoes.vo.prontuario.operacao.AtoOperatorioItem;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RemoverAtoOperatorio extends AbstractCommandTransaction {

    private AtoOperatorio atoOperatorio;

    public RemoverAtoOperatorio(AtoOperatorio atoOperatorio) {
        this.atoOperatorio = atoOperatorio;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        List<AtoOperatorioItem> itens = LoadManager.getInstance(AtoOperatorioItem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AtoOperatorioItem.PROP_ATO_OPERATORIO, atoOperatorio))
                .start().getList();

        for (AtoOperatorioItem atoOperatorioItem : itens) {
            BOFactory.delete(atoOperatorioItem);
        }

        BOFactory.delete(atoOperatorio);
    }
}
