/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.agendamento.tfd.pedidotfd;

import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd;
import br.com.ksisolucoes.vo.cadsus.TipoOcorrencia;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class AtualizarNumeroPedidoTfd extends AbstractCommandTransaction<AtualizarNumeroPedidoTfd> {

    private Long codigoPedidoTfd;
    private Long version;
    private String numeroPedido;
    private Date dataPedido;

    public AtualizarNumeroPedidoTfd(Long codigoPedidoTfd, Long version, String numeroPedido, Date dataPedido) {
        this.codigoPedidoTfd = codigoPedidoTfd;
        this.version = version;
        this.numeroPedido = numeroPedido;
        this.dataPedido = dataPedido;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        PedidoTfd pedidoTfd = HibernateUtil.rechargeVO(PedidoTfd.class, this.codigoPedidoTfd, version);
        
        String numeroPedidoOld = pedidoTfd.getNumeroPedido();
        Date dataPedidoOld = pedidoTfd.getDataPedido();
        
        pedidoTfd.setNumeroPedido(this.numeroPedido);
        pedidoTfd.setDataPedido(this.dataPedido);
        
        BOFactory.getBO(CadastroFacade.class).save(pedidoTfd);
        
        String descricao = Bundle.getStringApplication("msg_alteracao_pedido_tfd_nr_pedido_X_para_X_e_data_pedido_X_para_X",numeroPedidoOld.trim(), pedidoTfd.getNumeroPedido().trim(), Data.formatar(dataPedidoOld), Data.formatar(pedidoTfd.getDataPedido()));

        BOFactory.getBO(UsuarioCadsusFacade.class).gerarOcorrenciaUsuarioCadsus(pedidoTfd.getLaudoTfd().getUsuarioCadsus(), TipoOcorrencia.TIPO_TFD, descricao, pedidoTfd.getLaudoTfd());
        
    }
    
}
