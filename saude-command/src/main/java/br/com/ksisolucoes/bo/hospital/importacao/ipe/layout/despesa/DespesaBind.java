package br.com.ksisolucoes.bo.hospital.importacao.ipe.layout.despesa;

import br.com.ksisolucoes.bo.hospital.importacao.ipe.IpeVoBind;
import br.com.ksisolucoes.vo.hospital.ipe.DespesasIpe;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import java.io.Serializable;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 192, crlf = "UNIX")
public class DespesaBind implements IpeVoBind<DespesasIpe>, Serializable {

    @DataField(pos = 1, length = 8, required = true)
    private String codigoDespesa;

    @DataField(pos = 2, length = 100, required = true, trim = true)
    private String descricaoDespesa;

    @DataField(pos = 3, length = 1, required = true)
    private String categoria;

    @DataField(pos = 4, length = 1, required = true)
    private Long tipoDespesa;

    @DataField(pos = 5, length = 1, required = true)
    private Long moeda;

    @DataField(pos = 6, length = 16, required = true, precision = 5, impliedDecimalSeparator = true)
    private Double valor;

    @DataField(pos = 7, length = 16, required = true, precision = 5, impliedDecimalSeparator = true)
    private Double valorProvativo;

    @DataField(pos = 8, length = 16, required = true, precision = 5, impliedDecimalSeparator = true)
    private Double valorPames;

    @DataField(pos = 9, length = 1, required = true)
    private String autorizacaoPrevia;

    @Override
    public Class getVoClass() {
        return DespesasIpe.class;
    }

    @Override
    public void setAttributesVO(DespesasIpe vo, Procedimento procedimento, Session session) {
        vo.setCategoria(categoria);
        vo.setTipoDespesa(tipoDespesa);
        vo.setTipoMoeda(moeda);
        vo.setValor(valor);
        vo.setValorPrivativo(valorProvativo);
        vo.setValorPames(valorPames);
        vo.setFlagAutorizacaoPrevia(autorizacaoPrevia);
        vo.setProcedimento(procedimento);
    }

    @Override
    public void setRestrictions(Criteria criteria) {
        criteria.add(Restrictions.eq(DespesasIpe.PROP_CATEGORIA, categoria));
        criteria.createCriteria(DespesasIpe.PROP_PROCEDIMENTO)
                .add(Restrictions.eq(Procedimento.PROP_REFERENCIA, codigoDespesa));
    }

    @Override
    public String getCodigo() {
        return this.codigoDespesa;
    }

    @Override
    public String getDescricao() {
        return this.descricaoDespesa;
    }
}
