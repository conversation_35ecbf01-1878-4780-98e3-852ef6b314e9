/*
 * Created on 26/08/2004
 *
 */
package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.comunicacao.GrupoMensagemUsuario;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.UsuarioEmpresa;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import java.util.List;


/**
 * <AUTHOR> Giordani
 *
 */
public class DeleteUsuario extends DeleteVO {

    private Usuario usuario;

    public DeleteUsuario(Object vo) {
        super( vo );

        this.usuario = (Usuario) vo;
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<UsuarioEmpresa> lstUsuarioEmpresa = LoadManager.getInstance(UsuarioEmpresa.class)
                .addProperties(new HQLProperties(UsuarioEmpresa.class).getProperties())
                .addProperty(VOUtils.montarPath(UsuarioEmpresa.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(UsuarioEmpresa.PROP_USUARIO, Usuario.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioEmpresa.PROP_USUARIO), this.usuario))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(lstUsuarioEmpresa)) {
            for (UsuarioEmpresa usuarioEmpresa : lstUsuarioEmpresa) {
                BOFactory.getBO(CadastroFacade.class).delete(usuarioEmpresa);
            }
        }

        List<UsuarioGrupo> usuarioGrupoCollection = LoadManager.getInstance(UsuarioGrupo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioGrupo.PROP_USUARIO, this.usuario))
                .start().getList();
        
        if (CollectionUtils.isNotNullEmpty(usuarioGrupoCollection)) {
            for (UsuarioGrupo usuarioGrupo : usuarioGrupoCollection) {
                BOFactory.getBO(CadastroFacade.class).delete(usuarioGrupo);
            }
        }

        List<GrupoMensagemUsuario> grupoMensagemUsuarios = LoadManager.getInstance(GrupoMensagemUsuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(GrupoMensagemUsuario.PROP_USUARIO, this.usuario))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(grupoMensagemUsuarios)) {
            for (GrupoMensagemUsuario grupoMensagemUsuario : grupoMensagemUsuarios) {
                BOFactory.getBO(CadastroFacade.class).delete(grupoMensagemUsuario);
            }
        }
    }
}