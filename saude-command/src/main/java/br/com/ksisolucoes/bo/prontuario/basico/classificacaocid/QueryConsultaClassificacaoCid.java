package br.com.ksisolucoes.bo.prontuario.basico.classificacaocid;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaClassificacaoCidDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.ClassificacaoCids;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaClassificacaoCid extends CommandQueryPager<QueryConsultaClassificacaoCid> {

    private QueryConsultaClassificacaoCidDTOParam param;

    public QueryConsultaClassificacaoCid(QueryConsultaClassificacaoCidDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("cc.codigo", true);
        hql.addToSelect("cc.descricao", true);

        if(param.isCarregarFichaInvestigacaoAgravo()){
            hql.addToSelect("fichaInvestigacaoAgravo", true);
        }

        hql.setTypeSelect(ClassificacaoCids.class.getName());
        if(param.isCarregarFichaInvestigacaoAgravo()){
            hql.addToFrom("ClassificacaoCids cc  left join cc.fichaInvestigacaoAgravo fichaInvestigacaoAgravo");
        }else{
            hql.addToFrom("ClassificacaoCids cc");
        }


        hql.addToWhereWhithAnd("cc.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("cc.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("cc.codigo || ' ' || cc.descricao", param.getKeyword()));

        if (param.getPropSort() != null) {
            hql.addToOrder("cc." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("cc.descricao");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
