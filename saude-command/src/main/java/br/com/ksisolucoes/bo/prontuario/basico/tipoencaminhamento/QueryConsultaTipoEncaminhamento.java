package br.com.ksisolucoes.bo.prontuario.basico.tipoencaminhamento;

import br.com.ksisolucoes.bo.agendamento.tipoprocedimento.QueryConsultaTipoProcedimento;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaTipoEncaminhamentoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */

public class QueryConsultaTipoEncaminhamento extends CommandQueryPager<QueryConsultaTipoProcedimento> {

    private QueryConsultaTipoEncaminhamentoDTOParam param;

    public QueryConsultaTipoEncaminhamento(QueryConsultaTipoEncaminhamentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect(new HQLProperties(TipoEncaminhamento.class, "tipoEncaminhamento").getProperties());
        hql.addToSelect("tipoProcedimento.codigo", "tipoProcedimento.codigo");

        hql.addToFrom("TipoEncaminhamento tipoEncaminhamento" +
                      " left join tipoEncaminhamento.tipoProcedimento tipoProcedimento ");
        hql.setTypeSelect(TipoEncaminhamento.class.getName());
        hql.setConvertToLeftJoin(true);

        hql.addToWhereWhithAnd("tipoEncaminhamento.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tipoEncaminhamento.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("tipoEncaminhamento.codigo || ' ' || tipoEncaminhamento.descricao", param.getKeyword()));

        if (!param.isIncluirInativos()){
            hql.addToWhereWhithAnd("tipoEncaminhamento.situacao = ", TipoEncaminhamento.Situacao.ATIVO.value());
        }

        if (param.getPropSort() != null) {
            hql.addToOrder("tipoEncaminhamento." + param.getPropSort() + " " + (param.isAscending() ? "asc" : "desc"));
        } else {
            hql.addToOrder("tipoEncaminhamento.descricao");
        }

        if (param.isApenasOdontologico()) {
            hql.addToWhereWhithAnd("tipoEncaminhamento.flagOdontologico = ", RepositoryComponentDefault.SIM_LONG);
        } else {
            hql.addToWhereWhithAnd("tipoEncaminhamento.flagOdontologico = ", RepositoryComponentDefault.NAO_LONG);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
