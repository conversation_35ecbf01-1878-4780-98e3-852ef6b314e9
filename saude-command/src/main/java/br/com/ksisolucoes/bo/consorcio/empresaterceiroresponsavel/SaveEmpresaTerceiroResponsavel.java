package br.com.ksisolucoes.bo.consorcio.empresaterceiroresponsavel;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.EmpresaTerceiroResponsavel;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 * <AUTHOR> 19/01/2018
 */

public class SaveEmpresaTerceiroResponsavel extends SaveVO<EmpresaTerceiroResponsavel> {

    public SaveEmpresaTerceiroResponsavel(EmpresaTerceiroResponsavel vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        this.vo.setDataCadastro(Data.getDataAtual());
    }
}
