/*
 * Created on 26/08/2004
 *
 */
package br.com.ksisolucoes.bo.entradas.estoque.localizacao;

import br.com.ksisolucoes.bo.command.SaveVO;


/**
 * <AUTHOR>
 *
 */
public class SaveLocalizacao extends SaveVO{

    /**
     * 
     */
    public SaveLocalizacao(Object vo) {
        super( vo );
    }
    
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.command.AbstractCommand#getInterfaceChave()
     */
    
}