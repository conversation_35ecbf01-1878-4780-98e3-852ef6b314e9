package br.com.ksisolucoes.bo.service.sms;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import org.hibernate.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaAgendamentosLocaisSms extends CommandQuery{
    
    private Collection<AgendaGradeAtendimentoHorario> list;

    public QueryConsultaAgendamentosLocaisSms() {
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(AgendaGradeAtendimentoHorario.class.getName());
        
        hql.addToSelectAndGroup("agah.codigo", "codigo");
        hql.addToSelectAndGroup("agah.dataAgendamento", "dataAgendamento");
        hql.addToSelectAndGroup("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelectAndGroup("uc.nome", "usuarioCadsus.nome");
        hql.addToSelectAndGroup("uc.celular", "usuarioCadsus.celular");
        hql.addToSelectAndGroup("la.descricao", "localAgendamento.descricao");
        hql.addToSelectAndGroup("tp.descricao", "tipoProcedimento.descricao");
        hql.addToSelectAndGroup("tp.descricaoAbreviada", "tipoProcedimento.descricaoAbreviada");
        
        hql.addToFrom("AgendaGradeAtendimentoHorario agah"
                + " left join agah.usuarioCadsus uc"
                + " left join agah.tipoProcedimento tp"
                + " left join agah.localAgendamento la");

        hql.addToWhereWhithAnd("agah.solicitacaoAgendamento is null ");
        hql.addToWhereWhithAnd("agah.status = ", AgendaGradeAtendimentoHorario.STATUS_AGENDADO);
        hql.addToWhereWhithAnd("uc.celular is not null ");
        hql.addToWhereWhithAnd("tp.enviaSms = ", RepositoryComponentDefault.SIM);
        hql.addToWhereWhithAnd("agah.agendaRemanejado is null");
        
        hql.addToWhereWhithAnd("(not exists(select 1"
                                         + " from SmsControleIntegracao sci1"
                                        + " where sci1.agendaGradeAtendimentoHorario = agah"
                                          + " and sci1.tipoMensagem = " + SmsControleIntegracao.TipoMensagem.AVISO_AGENDAMENTO_LOCAL.value()
                                          + " and sci1.statusSms <> " + SmsControleIntegracao.StatusSms.REENVIAR.value() + ")"
                                + " or exists(select 1"
                                         + " from SmsControleIntegracao sci1"
                                        + " where sci1.agendaGradeAtendimentoHorario = agah"
                                          + " and sci1.tipoMensagem = " + SmsControleIntegracao.TipoMensagem.AVISO_AGENDAMENTO_LOCAL.value()
                                          + " and sci1.statusSms = " + SmsControleIntegracao.StatusSms.REENVIAR.value() + "))");
        hql.addToWhereWhithAnd("agah.dataAgendamento >= :dataAtual");
        hql.addToWhereWhithAnd("(cast(agah.dataAgendamento as date) - :dataAtual) <= :diasAvisoAgendamentoLocal");
    }

    @Override
    public Collection getResult() {
        return list;
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        Date dataAtual = DataUtil.getDataAtual();
        Integer diasReaviso = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("diasAvisoAgendamentoLocal");

        query.setDate("dataAtual", dataAtual);
        query.setInteger("diasAvisoAgendamentoLocal", diasReaviso);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result);
    }
}
