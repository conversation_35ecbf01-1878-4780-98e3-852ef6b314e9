package br.com.ksisolucoes.bo.vacina.boletimdiarioinsumos;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.bo.vacina.interfaces.dto.BoletimDiarioInsumosUtilizadosDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumos;
import br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumosItem;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacaoInsumo;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class BaixarInsumosVacinasAplicadasBoletimDiario extends AbstractCommandTransaction {

    private final BoletimDiarioInsumosUtilizadosDTO dto;
    private List<VacinaAplicacaoInsumo> vacinaAplicacaoInsumoList;
    private List<Long> codigoVacinaAplicacaoInsumoList;

    public BaixarInsumosVacinasAplicadasBoletimDiario(BoletimDiarioInsumosUtilizadosDTO dto) {
        this.dto = dto;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(Coalesce.asLong(dto.getTotalInsumosUtilizado()) > 0L){
            TipoDocumento tipoDocumentoInsumosSaida = BOFactory.getBO(CommomFacade.class).modulo(Modulos.VACINAS).getParametro("TipoDocumentoInsumosSaida");
            gerarMovimentoEstoque(tipoDocumentoInsumosSaida);
        } else {
            for(VacinaAplicacaoInsumo vai : getVacinaAplicacaoInsumoList()){
                vai.setStatusBaixa(VacinaAplicacaoInsumo.StatusBaixa.BAIXADO.value());
                BOFactory.save(vai);
            }
        }
    }
    
    private void gerarMovimentoEstoque(TipoDocumento tipoDocumento) throws DAOException, ValidacaoException{
        MovimentoEstoque me = new MovimentoEstoque();

        me.setTipoDocumento(tipoDocumento);
        me.setProduto(dto.getVacinaAplicacaoInsumo().getProduto());
        me.setQuantidade(dto.getTotalInsumosUtilizado().doubleValue());
        me.setGrupoEstoque(dto.getVacinaAplicacaoInsumo().getLote());

        BOFactory.getBO(MovimentoEstoqueFacade.class).gerarMovimentoEstoque(me);
        
        gerarBoletimDiarioInsumos(me);
    }
    
    private void gerarBoletimDiarioInsumos(MovimentoEstoque me) throws DAOException, ValidacaoException{
        BoletimDiarioInsumos bdi = new BoletimDiarioInsumos();
        
        bdi.setMovimentoEstoque(me);
        bdi = BOFactory.save(bdi);
        
        gerarBoletimDiarioInsumosItem(bdi);
    }
    
    private void gerarBoletimDiarioInsumosItem(BoletimDiarioInsumos bdd) throws DAOException, ValidacaoException{
        for(VacinaAplicacaoInsumo vai : getVacinaAplicacaoInsumoList()){
            BoletimDiarioInsumosItem bdii = new BoletimDiarioInsumosItem();

            bdii.setBoletimDiarioInsumos(bdd);
            bdii.setVacinaAplicacaoInsumo(vai);
            BOFactory.save(bdii);
            
            if(VacinaAplicacaoInsumo.StatusBaixa.PENDENTE.value().equals(vai.getStatusBaixa())){
                vai.setStatusBaixa(VacinaAplicacaoInsumo.StatusBaixa.BAIXADO.value());
                BOFactory.save(vai);
            }
        }
    }

    public List<VacinaAplicacaoInsumo> getVacinaAplicacaoInsumoList() {
        if(CollectionUtils.isEmpty(vacinaAplicacaoInsumoList)) {
            vacinaAplicacaoInsumoList =  LoadManager.getInstance(VacinaAplicacaoInsumo.class)
                .addProperties(new HQLProperties(VacinaAplicacaoInsumo.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VacinaAplicacaoInsumo.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, getCodigoVacinaAplicacaoInsumoList()))
                .start().getList();
        }
        return vacinaAplicacaoInsumoList;
    }

    private List<Long> getCodigoVacinaAplicacaoInsumoList() {
        if(CollectionUtils.isEmpty(codigoVacinaAplicacaoInsumoList)){
            codigoVacinaAplicacaoInsumoList = Lists.transform(Arrays.asList(dto.getCodigoVacinaAplicacaoInsumoList().split(",")), Longs.stringConverter());
        }
        return codigoVacinaAplicacaoInsumoList;
    }
}