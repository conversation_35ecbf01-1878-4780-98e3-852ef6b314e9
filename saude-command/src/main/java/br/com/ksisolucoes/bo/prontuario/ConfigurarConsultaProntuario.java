package br.com.ksisolucoes.bo.prontuario;

import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NodoConsultaProntuarioDTO;
import br.com.ksisolucoes.vo.prontuario.basico.NodoConsultaProntuario;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.util.List;
import static ch.lambdaj.Lambda.*;
import ch.lambdaj.group.Group;

/**
 *
 * <AUTHOR>
 */
public class ConfigurarConsultaProntuario extends AbstractCommandTransaction {

    private GrupoAtendimentoCbo grupoAtendimentoCbo;
    private List<NodoConsultaProntuarioDTO> registros;

    public ConfigurarConsultaProntuario(GrupoAtendimentoCbo grupoAtendimentoCbo, List<NodoConsultaProntuarioDTO> registros) {
        this.grupoAtendimentoCbo = grupoAtendimentoCbo;
        this.registros = registros;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        removerNodosExistentes();

        Group<NodoConsultaProntuarioDTO> group = group(registros, by(on(NodoConsultaProntuarioDTO.class).getClassName()));

        Long i = 1L;
        for (Group<NodoConsultaProntuarioDTO> sub : group.subgroups()) {
            NodoConsultaProntuario nodo = new NodoConsultaProntuario();

            NodoConsultaProntuarioDTO first = sub.first();

            nodo.setOrdem(i);
            nodo.setClasseNodo(first.getClassName());
            nodo.setGrupoAtendimentoCbo(grupoAtendimentoCbo);
            BOFactory.save(nodo);

            i++;
        }
    }

    private void removerNodosExistentes() throws DAOException, ValidacaoException {
        List<NodoConsultaProntuario> itensExistentes = LoadManager.getInstance(NodoConsultaProntuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(NodoConsultaProntuario.PROP_GRUPO_ATENDIMENTO_CBO), grupoAtendimentoCbo))
                .start().getList();

        for (NodoConsultaProntuario nodoConsultaProntuarioWeb : itensExistentes) {
            BOFactory.delete(nodoConsultaProntuarioWeb);
        }
    }
}