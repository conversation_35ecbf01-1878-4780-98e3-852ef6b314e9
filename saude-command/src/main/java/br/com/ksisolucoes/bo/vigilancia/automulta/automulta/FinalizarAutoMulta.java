package br.com.ksisolucoes.bo.vigilancia.automulta.automulta;

import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;

/**
 * <AUTHOR>
 */
public class FinalizarAutoMulta extends AbstractCommandTransaction<FinalizarAutoMulta> {

    private AutoMulta autoMulta;

    public FinalizarAutoMulta(AutoMulta autoMulta) {
        this.autoMulta = autoMulta;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (AutoMulta.Situacao.CONCLUIDO.value().equals(autoMulta.getSituacao())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_registro_alterado_outro_processo"));
        }
        if (autoMulta.getDataRecebimento() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_data_recebimento_obrigatoria"));
        }

        this.autoMulta.setSituacao(AutoMulta.Situacao.CONCLUIDO.value());
        autoMulta = BOFactory.save(autoMulta);

        BOFactory.getBO(VigilanciaFacade.class).gerarProcessoAdministrativo(autoMulta);
    }

    public AutoMulta getAutoMulta() {
        return autoMulta;
    }
}