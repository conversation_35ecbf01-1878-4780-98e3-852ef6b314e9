package br.com.ksisolucoes.bo.basico.pesquisa;

import br.com.ksisolucoes.bo.basico.pesquisa.dto.CadastroPerguntaPesquisaDTO;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta;

/**
 *
 * <AUTHOR>
 */
public class SalvarPerguntaPesquisa extends AbstractCommandTransaction {

    private CadastroPerguntaPesquisaDTO dto;

    public SalvarPerguntaPesquisa(CadastroPerguntaPesquisaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        PerguntaPesquisa perguntaPesquisa = BOFactory.save(dto.getPerguntaPesquisa());
        
        VOUtils.persistirListaVosModificados(PerguntaResposta.class, dto.getRespostas(), new QueryCustom.QueryCustomParameter(PerguntaResposta.PROP_PERGUNTA_PESQUISA, perguntaPesquisa));
    }

}
