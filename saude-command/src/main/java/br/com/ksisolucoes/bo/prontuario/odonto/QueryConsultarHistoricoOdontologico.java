package br.com.ksisolucoes.bo.prontuario.odonto;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.HistoricoOdontologicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.HistoricoOdontologicoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultarHistoricoOdontologico extends CommandQuery {

    private HistoricoOdontologicoDTOParam param;
    private List<HistoricoOdontologicoDTO> result;

    public QueryConsultarHistoricoOdontologico(HistoricoOdontologicoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(HistoricoOdontologicoDTO.class.getName());

        hql.addToSelect("atendimentoOdontoPlano.codigo", "atendimentoOdontoPlano.codigo");
        hql.addToSelect("atendimentoOdontoPlano", new HQLProperties(AtendimentoOdontoPlano.class, "atendimentoOdontoPlano").getSingleProperties());

        hql.addToSelect("atendimentoOdontoExecucao.codigo", "atendimentoOdontoExecucao.codigo");
        hql.addToSelect("atendimentoOdontoExecucao.dataCadastro", "atendimentoOdontoExecucao.dataCadastro");
        hql.addToSelect("atendimentoOdontoExecucao.descricaoTrabalho", "atendimentoOdontoExecucao.descricaoTrabalho");

        hql.addToSelect("situacaoDente.codigo", "atendimentoOdontoPlano.situacaoDente.codigo");
        hql.addToSelect("situacaoDente.referencia", "atendimentoOdontoPlano.situacaoDente.referencia");
        hql.addToSelect("situacaoDente.descricao", "atendimentoOdontoPlano.situacaoDente.descricao");
        hql.addToSelect("situacaoDente.tipoSituacao", "atendimentoOdontoPlano.situacaoDente.tipoSituacao");
        hql.addToSelect("situacaoDente.obturadoRestaurado", "atendimentoOdontoPlano.situacaoDente.obturadoRestaurado");
        hql.addToSelect("situacaoDente.perdido", "atendimentoOdontoPlano.situacaoDente.perdido");
        hql.addToSelect("situacaoDente.cariado", "atendimentoOdontoPlano.situacaoDente.cariado");

        hql.addToSelect("dente.codigo", "atendimentoOdontoPlano.dente.codigo");
        hql.addToSelect("dente.nome", "atendimentoOdontoPlano.dente.nome");

        hql.addToSelect("unidadeExecucao.codigo", "atendimentoOdontoExecucao.atendimento.empresa.codigo");
        hql.addToSelect("unidadeExecucao.descricao", "atendimentoOdontoExecucao.atendimento.empresa.descricao");

        hql.addToSelect("profissionalExecucao.codigo", "atendimentoOdontoExecucao.atendimento.profissional.codigo");
        hql.addToSelect("profissionalExecucao.nome", "atendimentoOdontoExecucao.atendimento.profissional.nome");

        hql.addToSelect("unidade.codigo", "atendimentoOdontoPlano.atendimento.empresa.codigo");
        hql.addToSelect("unidade.descricao", "atendimentoOdontoPlano.atendimento.empresa.descricao");

        hql.addToSelect("profissional.codigo", "atendimentoOdontoPlano.atendimento.profissional.codigo");
        hql.addToSelect("profissional.nome", "atendimentoOdontoPlano.atendimento.profissional.nome");

        hql.addToSelect("coalesce(atendimentoOdontoExecucao.dataCadastro, atendimentoOdontoPlano.dataCadastro) ", "dataCadastro");

        hql.addToFrom("AtendimentoOdontoExecucao atendimentoOdontoExecucao "
                + "right join atendimentoOdontoExecucao.atendimentoOdontoPlano atendimentoOdontoPlano "
                + "left join atendimentoOdontoExecucao.atendimento atendimentoExecucao "
                + "left join atendimentoExecucao.empresa unidadeExecucao "
                + "left join atendimentoExecucao.profissional profissionalExecucao "
                + "join atendimentoOdontoPlano.atendimento atendimento "
                + "join atendimento.profissional profissional "
                + "join atendimento.empresa unidade "
                + "join atendimentoOdontoPlano.situacaoDente situacaoDente "
                + "left join atendimentoOdontoPlano.dente dente "
        );

        hql.addToWhereWhithAnd("atendimento.usuarioCadsus.codigo = ", this.param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("atendimentoOdontoPlano.status in ", Arrays.asList(AtendimentoOdontoPlano.Status.CONCLUIDO.value(), AtendimentoOdontoPlano.Status.HISTORICO.value()));

        hql.addToOrder("dataCadastro" + QueryCustom.QueryCustomSorter.DECRESCENTE);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<HistoricoOdontologicoDTO> getResult() {
        return result;
    }
}
