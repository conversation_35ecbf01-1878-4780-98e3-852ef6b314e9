package br.com.ksisolucoes.bo.prontuario.procedimento.procedimento;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.ProcedimentoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import static br.com.ksisolucoes.util.VOUtils.persistirListaVosModificados;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCboPK;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroEmpresa;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoTipoTabela;
import static ch.lambdaj.Lambda.forEach;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastrarProcedimento extends AbstractCommandTransaction {

    private ProcedimentoDTO procedimentoDTO;
    private List<ProcedimentoCbo> lista;

    public CadastrarProcedimento(ProcedimentoDTO procedimentoDTO) {
        this.procedimentoDTO = procedimentoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.save(procedimentoDTO.getProcedimento());
        BOFactory.save(procedimentoDTO.getProcedimentoCompetencia());

        persistirListaVosModificados(
                ProcedimentoCbo.class,
                procedimentoDTO.getProcedimentoCbos(),
                new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCbo.PROP_ID, ProcedimentoCboPK.PROP_PROCEDIMENTO_COMPETENCIA), procedimentoDTO.getProcedimentoCompetencia())
        );

        if (CollectionUtils.isNotNullEmpty(procedimentoDTO.getProcedimentoTipoTabelas())) {
            forEach(procedimentoDTO.getProcedimentoTipoTabelas()).setProcedimento(procedimentoDTO.getProcedimento());
        }

        persistirListaVosModificados(
                ProcedimentoTipoTabela.class,
                procedimentoDTO.getProcedimentoTipoTabelas(),
                new QueryCustom.QueryCustomParameter(ProcedimentoTipoTabela.PROP_PROCEDIMENTO, procedimentoDTO.getProcedimento())
        );

        if (CollectionUtils.isNotNullEmpty(procedimentoDTO.getProcedimentoRegistroEmpresas())) {
            forEach(procedimentoDTO.getProcedimentoRegistroEmpresas()).setProcedimento(procedimentoDTO.getProcedimento());
        }

        persistirListaVosModificados(
                ProcedimentoRegistroEmpresa.class,
                procedimentoDTO.getProcedimentoRegistroEmpresas(),
                new QueryCustom.QueryCustomParameter(ProcedimentoRegistroEmpresa.PROP_PROCEDIMENTO, procedimentoDTO.getProcedimento())
        );
    }

}
