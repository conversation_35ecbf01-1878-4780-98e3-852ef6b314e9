package br.com.ksisolucoes.bo.esus.procedimentoesus;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus;

/**
 *
 * <AUTHOR>
 */
public class SaveProcedimentoEsus extends SaveVO<ProcedimentoEsus> {

    public SaveProcedimentoEsus(ProcedimentoEsus vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getDescricaoProcedimento() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_descricao"));
        }

        if (this.vo.getFichaIntegracao() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ficha_integracao"));
        }

        if (this.vo.getCodigoClassificacao() == null && !ProcedimentoEsus.FichaIntegracao.CONSOLIDADO.value().equals(this.vo.getFichaIntegracao())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_classificacao"));
        }

        if (this.vo.getCodigoEsus() == null && !ProcedimentoEsus.FichaIntegracao.CONSOLIDADO.value().equals(this.vo.getFichaIntegracao())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_codigo_esus"));
        }
    }
}
