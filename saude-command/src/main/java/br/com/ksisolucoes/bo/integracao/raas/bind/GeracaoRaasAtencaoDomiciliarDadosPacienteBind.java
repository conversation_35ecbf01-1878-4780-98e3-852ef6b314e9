package br.com.ksisolucoes.bo.integracao.raas.bind;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.bo.integracao.raas.dto.GeracaoRaasDTO;
import java.util.Date;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;

/**
 *
 * <AUTHOR>
 */
@FixedLengthRecord(length = 306, crlf = "UNIX")
public class GeracaoRaasAtencaoDomiciliarDadosPacienteBind implements IBindVoExport<GeracaoRaasDTO> {
    
    @DataField(pos = 1, length = 2, paddingChar = '0', align = "R", required = true)
    private Long codigoLinhaAtencaoDomiciliarDadosPaciente;
    @DataField(pos = 2, length = 2, align = "R")
    private Long codigoUnidadeFederacao;
    @DataField(pos = 3, length = 6, align = "R", pattern = "yyyyMM")
    private Date anoMesAtencaoDomiciliarDadosPaciente;
    @DataField(pos = 4, length = 7, align = "R")
    private Long codigoUnidadePrestadoraServico;
    @DataField(pos = 5, length = 15, align = "R")
    private Long cartaoNacionalSaudePaciente;
    @DataField(pos = 6, length = 8, align = "R", pattern = "yyyyMMdd")
    private Date dataInicialValidade;
    @DataField(pos = 7, length = 8, align = "R", pattern = "yyyyMMdd")
    private Date dataFinalValidade;
    @DataField(pos = 8, length = 30, align = "L")
    private String nomePaciente;
    @DataField(pos = 9, length = 10, align = "R")
    private Long numeroProntuario;
    @DataField(pos = 10, length = 30, align = "L")
    private String nomeMaePaciente;
    @DataField(pos = 11, length = 30, align = "L")
    private String identificacaoLogradouroResidenciaPaciente;
    @DataField(pos = 12, length = 5, align = "L")
    private String numeroCorrespondenteResidenciaPaciente;
    @DataField(pos = 13, length = 10, align = "L")
    private String complementoLogradouroPaciente;
    @DataField(pos = 14, length = 8, align = "R")
    private Long codigoEnderecamentoPostalLogradouroPaciente;
    @DataField(pos = 15, length = 7, align = "R")
    private Long codigoMunicipioLogradouroPaciente;
    @DataField(pos = 16, length = 8, align = "R", pattern = "yyyyMMdd")
    private Date dataNascimentoPaciente;
    @DataField(pos = 17, length = 1, align = "L")
    private String sexoPaciente;
    @DataField(pos = 18, length = 2, paddingChar = '0', align = "R", required = true)
    private Long racaCorPaciente;
    @DataField(pos = 19, length = 30, align = "L")
    private String nomeResponsavelPaciente;
    @DataField(pos = 20, length = 3, align = "R")
    private Long codigoNacionalidade;
    @DataField(pos = 21, length = 4, align = "R")
    private Long etniaPaciente;
    @DataField(pos = 22, length = 11, align = "R")
    private Long numeroTelefonePaciente;
    @DataField(pos = 23, length = 11, align = "R")
    private Long numeroCelularPaciente;
    @DataField(pos = 24, length = 2, align = "R", required = true)
    private Long codigoMotivoSaidaPermanencia;
    @DataField(pos = 25, length = 8, align = "R", pattern = "yyyyMMdd")
    private Date dataOcorrenciaAltaTransferenciaObito;
    @DataField(pos = 26, length = 4, align = "L")
    private String cidPrincipal;
    @DataField(pos = 27, length = 4, align = "L")
    private String cidSecundario1;
    @DataField(pos = 28, length = 4, align = "L")
    private String cidSecundario2;
    @DataField(pos = 29, length = 4, align = "L")
    private String cidSecundario3;
    @DataField(pos = 30, length = 4, align = "L")
    private String cidCausasAssociadas;
    @DataField(pos = 31, length = 2, paddingChar = '0', align = "R")
    private Long caracterAtendimento;
    @DataField(pos = 32, length = 2, paddingChar = '0', align = "R")
    private Long codigoOrigemPaciente;
    @DataField(pos = 33, length = 1, align = "L")
    private String coberturaEstrategiaSaudeFamilia;
    @DataField(pos = 34, length = 7, align = "R")
    private Long codigoEstabelecimentoCoberturaEsf;
    @DataField(pos = 35, length = 5, align = "R")
    private Long totalProcedimentosRealizados;
    @DataField(pos = 36, length = 2, paddingChar = '0', align = "R")
    private Long codigoDestinoPaciente;
    @DataField(pos = 37, length = 3, align = "L")
    private String origemInformacoes;
    @DataField(pos = 38, length = 4, align = "L")
    private String reservado;
    @DataField(pos = 39, length = 2, align = "L")
    private String fimCabecalhoAtencaoDomiciliarDadosPaciente;

    @Override
    public void buildProperties(GeracaoRaasDTO vo) {
//        codigoLinhaAtencaoDomiciliarDadosPaciente = vo.getCodigoLinhaAtencaoDomiciliarDadosPaciente();
//        codigoUnidadeFederacao = vo.getCodigoUnidadeFederacao();
//        anoMesAtencaoDomiciliarDadosPaciente = vo.getAnoMesAtencaoDomiciliarDadosPaciente();
//        codigoUnidadePrestadoraServico = vo.getCodigoUnidadePrestadoraServico();
//        cartaoNacionalSaudePaciente = vo.getCartaoNacionalSaudePaciente();
//        dataInicialValidade = vo.getDataInicialValidade();
//        dataFinalValidade = vo.getDataFinalValidade();
//        nomePaciente = vo.getNomePaciente();
//        numeroProntuario = vo.getNumeroProntuario();
//        nomeMaePaciente = vo.getNomeMaePaciente();
//        identificacaoLogradouroResidenciaPaciente = vo.getIdentificacaoLogradouroResidenciaPaciente();
//        numeroCorrespondenteResidenciaPaciente = vo.getNumeroCorrespondenteResidenciaPaciente();
//        complementoLogradouroPaciente = vo.getComplementoLogradouroPaciente();
//        codigoEnderecamentoPostalLogradouroPaciente = vo.getCodigoEnderecamentoPostalLogradouroPaciente();
//        codigoMunicipioLogradouroPaciente = vo.getCodigoMunicipioLogradouroPaciente();
//        dataNascimentoPaciente = vo.getDataNascimentoPaciente();
//        sexoPaciente = vo.getSexoPaciente();
//        racaCorPaciente = vo.getRacaCorPaciente();
//        nomeResponsavelPaciente = vo.getNomeResponsavelPaciente();
//        codigoNacionalidade = vo.getCodigoNacionalidade();
//        etniaPaciente = vo.getEtniaPaciente();
//        numeroTelefonePaciente = vo.getNumeroTelefonePaciente();
//        numeroCelularPaciente = vo.getNumeroCelularPaciente();
//        codigoMotivoSaidaPermanencia = vo.getCodigoMotivoSaidaPermanencia();
//        dataOcorrenciaAltaTransferenciaObito = vo.getDataOcorrenciaAltaTransferenciaObito();
//        cidPrincipal = vo.getCidPrincipal();
//        cidSecundario1 = vo.getCidSecundario1();
//        cidSecundario2 = vo.getCidSecundario2();
//        cidSecundario3 = vo.getCidSecundario3();
//        cidCausasAssociadas = vo.getCidCausasAssociadas();
//        caracterAtendimento = vo.getCaracterAtendimento();
//        codigoOrigemPaciente = vo.getCodigoOrigemPaciente();
//        coberturaEstrategiaSaudeFamilia = vo.getCoberturaEstrategiaSaudeFamilia();
//        codigoEstabelecimentoCoberturaEsf = vo.getCodigoEstabelecimentoCoberturaEsf();
//        totalProcedimentosRealizados = vo.getTotalProcedimentosRealizados();
//        codigoDestinoPaciente = vo.getCodigoDestinoPaciente();
//        origemInformacoes = vo.getOrigemInformacoes();
//        reservado = vo.getReservado();
//        fimCabecalhoAtencaoDomiciliarDadosPaciente = vo.getFimCabecalhoAtencaoDomiciliarDadosPaciente();
    }
    
}
