package br.com.ksisolucoes.bo.vigilancia.estabelecimento;

import br.com.ksisolucoes.bo.command.DeleteVO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.EstabelecimentoCnae;
import java.util.List;


/**
 * <AUTHOR>
 *
 */
public class DeleteEstabelecimento extends DeleteVO<Estabelecimento> {
    
    private static final long serialVersionUID = 1L;
    
    public DeleteEstabelecimento(Estabelecimento vo) {
        super( vo );
    }

    @Override
    protected void antesDelete() throws DAOException, ValidacaoException {
        List<EstabelecimentoCnae> lstEstCnae = LoadManager.getInstance(EstabelecimentoCnae.class)
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoCnae.PROP_ESTABELECIMENTO, this.vo))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(lstEstCnae)) {
            for (EstabelecimentoCnae estabelecimentoCnae : lstEstCnae) {
                BOFactory.delete(estabelecimentoCnae);
            }
        }
    }
}
