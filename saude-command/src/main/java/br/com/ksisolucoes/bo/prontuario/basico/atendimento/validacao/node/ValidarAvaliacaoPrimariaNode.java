package br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.node;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.AbstractCommandValidacao;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.validacao.annotations.ValidacaoProntuarioNode;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR> felipe
 */
@ValidacaoProntuarioNode(value = NodesAtendimentoRef.AVALIACAO, refClass = AtendimentoPrimario.class, dependencias = {ValidarDadosEnfermagemNode.class, ValidarEvolucaoUnidade.class})
public class ValidarAvaliacaoPrimariaNode extends AbstractCommandValidacao<AtendimentoPrimario> {

    public ValidarAvaliacaoPrimariaNode(Atendimento atendimento) {
        super(atendimento);
    }

    @Override
    public AtendimentoPrimario executarValidacao(AtendimentoPrimario obj) throws DAOException, ValidacaoException {
        if (registraAvaliacao(obj)) {
            if (obj.getDataAvaliacao()==null) {
                validacao(Bundle.getStringApplication("campoXObrigatorio", Bundle.getStringApplication("rotulo_data_avaliacao")));
            }
            if (obj.getPeso() != null) {
                if (obj.getPeso() < 0D || obj.getPeso() > 500000) {
                    validacao(Bundle.getStringApplication("campoXValorInvalido", Bundle.getStringApplication("rotulo_peso")));
                }
            }
            if (obj.getTemperatura() != null) {
                if (obj.getTemperatura() != 0D && (obj.getTemperatura() < 25D || obj.getTemperatura() > 45D)) {
                    validacao(Bundle.getStringApplication("campoTemperaturaValorInvalido"));
                }
            }
            if (obj.getPressaoArterialSistolica() != null && !obj.getPressaoArterialSistolica().equals(0L)) {
                if (obj.getPressaoArterialSistolica() < 0 || obj.getPressaoArterialSistolica() > 999) {
                    validacao(Bundle.getStringApplication("campoXValorInvalidoPressao", Bundle.getStringApplication("rotulo_pas")));
                }
            }
            if (obj.getPressaoArterialDiastolica() != null && !obj.getPressaoArterialDiastolica().equals(0L)) {
                if (obj.getPressaoArterialDiastolica() < 0 || obj.getPressaoArterialDiastolica() > 999) {
                    validacao(Bundle.getStringApplication("campoXValorInvalidoPressao", Bundle.getStringApplication("rotulo_pad")));
                }
            }
        }
        return obj;
    }

    @Override
    public void processar(AtendimentoPrimario obj) throws DAOException, ValidacaoException {
        if(registraAvaliacao(obj)){
            obj.setAtendimento(atendimento);
            BOFactory.getBO(AtendimentoFacade.class).registrarAvaliacaoPrimaria(obj, atendimento.getProfissional());
            BOFactory.getBO(AtendimentoFacade.class).registrarProgramasSaude(atendimento, obj, false);
            
        }
        if(obj != null && Atendimento.PRIORIDADE_URGENTE.equals(obj.getPrioridade())){
            atendimento.setPrioridade(Atendimento.PRIORIDADE_URGENTE);
            BOFactory.save(atendimento);
        }
    }
    
    private boolean registraAvaliacao(AtendimentoPrimario atendimentoPrimario){
        if (atendimentoPrimario!=null) {
            return Coalesce.asDouble(atendimentoPrimario.getPeso()) > 0D
                    || Coalesce.asDouble(atendimentoPrimario.getAltura()) > 0D
                    || Coalesce.asDouble(atendimentoPrimario.getPerimetroCefalico()) > 0D
                    || Coalesce.asLong(atendimentoPrimario.getCintura()) > 0L
                    || Coalesce.asDouble(atendimentoPrimario.getTemperatura()) > 0D
                    || Coalesce.asLong(atendimentoPrimario.getGlicemia()) > 0L
                    || Coalesce.asLong(atendimentoPrimario.getFrequenciaCardiaca()) > 0L
                    || Coalesce.asLong(atendimentoPrimario.getPressaoArterialDiastolica()) > 0L
                    || Coalesce.asLong(atendimentoPrimario.getPressaoArterialSistolica()) > 0L
                    || Coalesce.asDouble(atendimentoPrimario.getTemperaturaRetal()) > 0D
                    || Coalesce.asLong(atendimentoPrimario.getFrequenciaRespiratoria()) > 0L
                    || Coalesce.asLong(atendimentoPrimario.getFrequenciaCardiacaFetal()) > 0L
                    || Coalesce.asLong(atendimentoPrimario.getDiurese()) > 0L
                    || Coalesce.asLong(atendimentoPrimario.getSaturacaoOxigenio()) > 0L
                    || StringUtils.trimToNull(atendimentoPrimario.getEvacuacao()) != null
                    || StringUtils.trimToNull(atendimentoPrimario.getObservacao()) != null
                    || atendimentoPrimario.getAtendimentoRN() != null
                    || atendimentoPrimario.getIndicadorAcidente() != null;
        }
        return false;
    }
}
