package br.com.ksisolucoes.bo.prontuario.basico;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerAtendimento extends CommandQueryPager<QueryPagerAtendimento> {

    private AtendimentoDTOParam param;

    public QueryPagerAtendimento(AtendimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect(AtendimentoDTO.PROP_ATENDIMENTO, new HQLProperties(Atendimento.class, "a").getProperties());
        hql.addToSelect(AtendimentoDTO.PROP_ATENDIMENTO, new HQLProperties(UsuarioCadsus.class, "a.usuarioCadsus").getProperties());
        hql.addToSelect(AtendimentoDTO.PROP_ATENDIMENTO, new HQLProperties(UsuarioCadsus.class, "a.responsavel").getProperties());
        hql.addToSelect(AtendimentoDTO.PROP_ATENDIMENTO, new HQLProperties(TipoAtendimento.class, "a.naturezaProcuraTipoAtendimento.tipoAtendimento").getProperties());
        hql.addToSelect(AtendimentoDTO.PROP_ATENDIMENTO, new HQLProperties(NaturezaProcura.class, "a.naturezaProcuraTipoAtendimento.naturezaProcura").getProperties());
        hql.addToSelect(AtendimentoDTO.PROP_ATENDIMENTO, new HQLProperties(Profissional.class, "a.profissional").getProperties());
        hql.addToSelect(AtendimentoDTO.PROP_ATENDIMENTO, new HQLProperties(Profissional.class, "a.profissionalResponsavel").getProperties());

        hql.addToSelect("a.leitoQuarto.quartoInternacao.codigo", "atendimento.leitoQuarto.quartoInternacao.codigo");
        hql.addToSelect("a.leitoQuarto.quartoInternacao.descricao", "atendimento.leitoQuarto.quartoInternacao.descricao");
        hql.addToSelect("a.empresa.codigo", "atendimento.empresa.codigo");
        hql.addToSelect("a.empresa.referencia", "atendimento.empresa.referencia");
        hql.addToSelect("a.empresa.descricao", "atendimento.empresa.descricao");
        hql.addToSelect("a.naturezaProcuraTipoAtendimento.imprimeTermoAutorizacao", "atendimento.naturezaProcuraTipoAtendimento.imprimeTermoAutorizacao");
        hql.addToSelect("a.naturezaProcuraTipoAtendimento.imprimeFichaPaciente", "atendimento.naturezaProcuraTipoAtendimento.imprimeFichaPaciente");

        hql.addToSelect("(select pront.numeroProntuario from UsuarioCadsusProntuario pront"
                + " join pront.id.usuarioCadsus usuarioCadsusPront "
                + " join pront.id.empresa empresaPront "
                + " where usuarioCadsusPront = a.usuarioCadsus"
                + " and empresaPront = a.empresa )","numeroProntuario");

        hql.addToFrom("Atendimento a ");
        hql.setTypeSelect(AtendimentoDTO.class.getName());

        hql.addToWhereWhithAnd("a.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo in ", this.param.getCodigosTipoAtendimento());
        hql.addToWhereWhithAnd("a.naturezaProcuraTipoAtendimento.tipoAtendimento.tipoAtendimento ", this.param.getTiposAtendimento());

        if (param.getSituacoes()!=null) {
            hql.addToWhereWhithAnd("a.status in ", this.param.getSituacoes());
        } else {
            hql.addToWhereWhithAnd("a.dataChegada ", Data.adjustRangeHour(Data.getDataAtual()));
        }

        if(param.isValidarEmpresa()){
            hql.addToWhereWhithAnd("a.empresa.codigo = ", getSessao().getCodigoEmpresa());
        }

        if(param.isPacienteSemCadastro()){
            hql.addToWhereWhithAnd("a.usuarioCadsus is null ");
        }
        hql.addToWhereWhithAnd("a.profissional =", param.getProfissional());
        hql.addToWhereWhithAnd("a.profissionalResponsavel =", param.getProfissionalResponsavel());
        hql.addToWhereWhithAnd("a.naturezaProcuraTipoAtendimento.tipoAtendimento =", param.getTipoAtendimento());
        hql.addToWhereWhithAnd("a.naturezaProcuraTipoAtendimento.naturezaProcura =", param.getNaturezaProcura());

        if (param.getConvenio() != null) {
            hql.addToWhereWhithAnd("a.convenio =", param.getConvenio());
        }

        if (param.isSomentePacientesInternados()) {
            hql.addToWhereWhithAnd("a.leitoQuarto is not null");
        }

        if (param.getUsuarioCadsus() != null) {
            hql.addToWhereWhithAnd("a.usuarioCadsus =", param.getUsuarioCadsus());
        }

        if (param.getNomePaciente() != null) {
            hql.addToWhereWhithAnd("(" + hql.getConsultaLiked(" a.nomePaciente", param.getNomePaciente(), true)
                    + " OR (a.usuarioCadsus.utilizaNomeSocial = 1 AND " + hql.getConsultaLiked("a.usuarioCadsus.apelido", param.getNomePaciente(), true) + "))");
        }

        if (param.getConfigureParam().getSorter() != null) {
            for (Map.Entry<String, String> entry : param.getConfigureParam().getSorter().entrySet()) {
                hql.addToOrder("a."+entry.getKey() + " " + entry.getValue());
            }
        } else {
            hql.addToOrder("a.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
            hql.addToOrder("a.profissional.codigo");
            hql.addToOrder("a.dataChegada");
        }

        hql.setConvertToLeftJoin(true);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result , false);
    }

}
