package br.com.ksisolucoes.bo.prontuario.basico.cotasExameUnidade;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadeCota;
import br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadeProcedimento;
import org.hibernate.criterion.Restrictions;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExcluirCotasExameUnidade extends AbstractCommandTransaction {

    private ExameUnidadeCota exameUnidadeCota;

    public ExcluirCotasExameUnidade(ExameUnidadeCota exameUnidadeCota) {
        this.exameUnidadeCota = exameUnidadeCota;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        List<ExameUnidadeProcedimento> List = (List<ExameUnidadeProcedimento>) getSession().createCriteria(ExameUnidadeProcedimento.class)
                .add(Restrictions.eq(ExameUnidadeProcedimento.PROP_EXAME_UNIDADE_COTA, exameUnidadeCota))
                .list();

        for(ExameUnidadeProcedimento exameUnidadeProcedimento : List){
            BOFactory.delete(exameUnidadeProcedimento);
        }
        
        BOFactory.delete(exameUnidadeCota);
    }
}
