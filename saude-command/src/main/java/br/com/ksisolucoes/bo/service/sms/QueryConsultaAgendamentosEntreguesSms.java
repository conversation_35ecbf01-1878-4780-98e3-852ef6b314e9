package br.com.ksisolucoes.bo.service.sms;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import org.hibernate.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * Monta uma lista dos agendamentos que foram entregues com a finalidade de enviar SMS para lembrar e confirmar a presença do paciente.
 */
public class QueryConsultaAgendamentosEntreguesSms extends CommandQuery<QueryConsultaAgendamentosEntreguesSms> {
    
    private Collection<AgendaGradeAtendimentoHorario> list;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(AgendaGradeAtendimentoHorario.class.getName());

        hql.addToSelect("agah.codigo", true);
        hql.addToSelect("agah.dataAgendamento", true);
        hql.addToSelect("agah.chaveValidacao", true);
        hql.addToSelect("sa.codigo", "solicitacaoAgendamento.codigo");
        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("uc.nome", "usuarioCadsus.nome");
        hql.addToSelect("uc.celular", "usuarioCadsus.celular");
        hql.addToSelect("la.descricao", "localAgendamento.descricao");
        hql.addToSelect("tp.descricao", "tipoProcedimento.descricao");
        hql.addToSelect("tp.descricaoAbreviada", "tipoProcedimento.descricaoAbreviada");

        hql.addToFrom("AgendaGradeAtendimentoHorario agah"
                + " join agah.usuarioCadsus uc"
                + " join agah.tipoProcedimento tp"
                + " join agah.solicitacaoAgendamento sa"
                + " join agah.localAgendamento la");

        hql.addToWhereWhithAnd("sa.dataEntrega is null");
        hql.addToWhereWhithAnd("agah.status = ", AgendaGradeAtendimentoHorario.STATUS_AGENDADO);
        hql.addToWhereWhithAnd("agah.dataConfirmacaoPaciente is null");
        hql.addToWhereWhithAnd("uc.celular is not null");
        hql.addToWhereWhithAnd("tp.enviaSms = ", RepositoryComponentDefault.SIM);
        hql.addToWhereWhithAnd("(not exists(select 1"
                                         + " from SmsControleIntegracao sci"
                                        + " where sci.agendaGradeAtendimentoHorario = agah"
                                          + " and sci.tipoMensagem = " + SmsControleIntegracao.TipoMensagem.CONFIRMACAO_AGENDAMENTO.value()
                                          + " and sci.statusSms <> " + SmsControleIntegracao.StatusSms.REENVIAR.value() + ")");
        hql.addToWhereWhithOr("exists(select 1"
                                         + " from SmsControleIntegracao sci"
                                        + " where sci.agendaGradeAtendimentoHorario = agah"
                                          + " and sci.tipoMensagem = " + SmsControleIntegracao.TipoMensagem.CONFIRMACAO_AGENDAMENTO.value()
                                          + " and sci.statusSms = " + SmsControleIntegracao.StatusSms.REENVIAR.value() + "))");
        hql.addToWhereWhithAnd("agah.dataAgendamento >= :dataAtual");
        hql.addToWhereWhithAnd("(cast(agah.dataAgendamento as date) - cast(agah.dataCadastro as date)) >= :diasMinimoAgendamento");
        hql.addToWhereWhithAnd("(cast(agah.dataAgendamento as date) - cast(:dataAtual as date)) <= :diasConfirmacao");
    }

    @Override
    public Collection getResult() {
        return list;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        list = hql.getBeanList((List<Map<String, Object>>) result);
    }
    
    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        Date dataAtual = DataUtil.getDataAtual();
        Integer diasMinimoAgendamento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("diasMinimoAgendamento");
        Integer diasConfirmacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("diasConfirmacao");

        query.setDate("dataAtual", dataAtual);
        query.setInteger("diasMinimoAgendamento", diasMinimoAgendamento);
        query.setInteger("diasConfirmacao", diasConfirmacao);
    }

}
