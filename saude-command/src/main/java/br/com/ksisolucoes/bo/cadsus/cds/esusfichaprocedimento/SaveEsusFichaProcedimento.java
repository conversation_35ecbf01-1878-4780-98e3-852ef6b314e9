package br.com.ksisolucoes.bo.cadsus.cds.esusfichaprocedimento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaProcedimento;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import br.com.ksisolucoes.vo.esus.helper.EsusIntegracaoHelper;
import br.com.ksisolucoes.util.esus.EsusValidacoesFichaProcedimentoHelper;

/**
 *
 * <AUTHOR>
 */
public class SaveEsusFichaProcedimento extends SaveVO<EsusFichaProcedimento> {

    public SaveEsusFichaProcedimento(EsusFichaProcedimento vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        EsusValidacoesFichasDTOParam paramValidacao = new EsusValidacoesFichasDTOParam();
        paramValidacao.setRetorno(EsusValidacoesFichasDTOParam.Retorno.EXCEPTION);
        paramValidacao.setEmpresa(vo.getEmpresa());
        paramValidacao.setProfissional(vo.getProfissional());
        paramValidacao.setTabelaCbo(vo.getCbo());
        paramValidacao.setEsusFichaProcedimento(vo);

        EsusValidacoesFichaProcedimentoHelper.validate(paramValidacao);

        EsusIntegracaoHelper.validarEquipeProfissional(vo.getEmpresa(), vo.getProfissional());

        this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        this.vo.setDataUsuario(DataUtil.getDataAtual());

        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
    }
}
