package br.com.ksisolucoes.bo.prontuario.hospital.prescricaoenfermagem;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoMedicamentoAplicacaoDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaCuidadosReceituario extends CommandQuery<QueryConsultaCuidadosReceituario> {

    private Atendimento atendimento;
    private List<AtendimentoMedicamentoAplicacaoDTO> result;

    public QueryConsultaCuidadosReceituario(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    @Override
    public List<AtendimentoMedicamentoAplicacaoDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(AtendimentoMedicamentoAplicacaoDTO.class.getName());

        hql.addToSelect("(select max(am.dataHoraAplicacao) from AtendimentoMedicamento am where am.cuidados.codigo = c.codigo)", "dataHoraAplicacao");
        hql.addToSelectAndGroup("c.codigo", "cuidados.codigo");
        hql.addToSelectAndGroup("c.flagFinalizadoAtendimento", "cuidados.flagFinalizadoAtendimento");
        hql.addToSelectAndGroup("c.descricao", "cuidados.descricao");
        hql.addToSelectAndGroup("c.frequencia", "cuidados.frequencia");
        hql.addToSelectAndGroup("c.version", "cuidados.version");
        hql.addToSelectAndGroup("a.codigo", "atendimentoMedicamento.atendimento.codigo");
        hql.addToSelectAndGroup("r.codigo", "cuidados.receituario.codigo");

        hql.addToSelectAndGroup("pe.codigo", "cuidados.prescricaoEnfermagem.codigo");
        hql.addToSelectAndGroup("pe.descricao", "cuidados.prescricaoEnfermagem.descricao");

        hql.addToFrom("Cuidados c"
                + " join c.receituario r"
                + " join r.atendimento a"
                + " join a.atendimentoPrincipal ap"
                + " left join c.prescricaoEnfermagem pe");

        hql.addToWhereWhithAnd("ap = ", atendimento);
        hql.addToWhereWhithAnd("coalesce(c.flagFinalizadoAtendimento,'" + RepositoryComponentDefault.NAO_LONG + "') = ", RepositoryComponentDefault.NAO_LONG);
        hql.addToOrder("1 desc nulls first");
    }

}
