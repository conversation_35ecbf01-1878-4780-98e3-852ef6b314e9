package br.com.ksisolucoes.bo.prontuario.basico.tuberculose;

import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.tuberculose.dto.TuberculoseResultadoSintomaticoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseSintomaticoExames;

/**
 * <AUTHOR>
 */
public class SalvarTuberculoseResultadoSintomatico extends AbstractCommandTransaction {

    private TuberculoseResultadoSintomaticoDTO dto;

    public SalvarTuberculoseResultadoSintomatico(TuberculoseResultadoSintomaticoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        VOUtils.persistirListaVosModificados(TuberculoseSintomaticoExames.class, dto.getTuberculoseSintomaticoExamesList(), new QueryCustom.QueryCustomParameter(TuberculoseSintomaticoExames.PROP_TUBERCULOSE_SINTOMATICO, dto.getTuberculoseSintomatico()));

        BOFactory.save(dto.getTuberculoseSintomatico());
    }
}