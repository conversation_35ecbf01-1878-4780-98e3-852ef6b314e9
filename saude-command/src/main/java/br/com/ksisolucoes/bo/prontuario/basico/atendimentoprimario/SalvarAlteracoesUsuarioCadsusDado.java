package br.com.ksisolucoes.bo.prontuario.basico.atendimentoprimario;

import br.com.ksisolucoes.bo.cadsus.interfaces.dto.SalvarAlteracoesUsuarioCadsusDadoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class SalvarAlteracoesUsuarioCadsusDado extends AbstractCommandTransaction {

    private AtendimentoPrimario atendimentoPrimario;
    private UsuarioCadsusDado usuarioCadsusDado;
    private SalvarAlteracoesUsuarioCadsusDadoDTO dadoDTO;

    public SalvarAlteracoesUsuarioCadsusDado(AtendimentoPrimario atendimentoPrimario) {
        this.atendimentoPrimario = atendimentoPrimario;
    }

    public SalvarAlteracoesUsuarioCadsusDado(SalvarAlteracoesUsuarioCadsusDadoDTO dadoDTO) {
        this.dadoDTO = dadoDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (atendimentoPrimario == null && dadoDTO.getUsuarioCadsusDado() != null) {
            usuarioCadsusDado = (UsuarioCadsusDado) getSession().createCriteria(UsuarioCadsusDado.class)
                    .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusDado.PROP_CODIGO), dadoDTO.getCodigoPaciente()))
                    .uniqueResult();
            if (usuarioCadsusDado == null) {
                usuarioCadsusDado = new UsuarioCadsusDado();
                usuarioCadsusDado.setCodigo(dadoDTO.getCodigoPaciente());
            }
            atualizarDados(dadoDTO.getUsuarioCadsusDado());

        } else {
            usuarioCadsusDado = (UsuarioCadsusDado) getSession().createCriteria(UsuarioCadsusDado.class)
                    .add(Restrictions.eq(VOUtils.montarPath(UsuarioCadsusDado.PROP_CODIGO), atendimentoPrimario.getAtendimento().getUsuarioCadsus().getCodigo()))
                    .uniqueResult();
            if (usuarioCadsusDado == null) {
                usuarioCadsusDado = new UsuarioCadsusDado();
                usuarioCadsusDado.setCodigo(atendimentoPrimario.getAtendimento().getUsuarioCadsus().getCodigo());
            }

            if (atendimentoPrimario.getPeso() != null) {
                usuarioCadsusDado.setPeso(atendimentoPrimario.getPeso());
            } else {
                Double ultimoPesoRegistrado = (Double) getSession().createCriteria(AtendimentoPrimario.class)
                        .setProjection(Projections.property(AtendimentoPrimario.PROP_PESO))
                        .add(Restrictions.isNotNull(AtendimentoPrimario.PROP_PESO))
                        .addOrder(Order.desc(AtendimentoPrimario.PROP_CODIGO))
                        .createCriteria(AtendimentoPrimario.PROP_ATENDIMENTO)
                        .createCriteria(Atendimento.PROP_ATENDIMENTO_PRINCIPAL)
                        .add(Restrictions.eq(Atendimento.PROP_CODIGO, atendimentoPrimario.getAtendimento().getAtendimentoPrincipal().getCodigo()))
                        .setMaxResults(1)
                        .uniqueResult();

                usuarioCadsusDado.setPeso(ultimoPesoRegistrado);
            }

            usuarioCadsusDado.setDataDados(atendimentoPrimario.getDataAvaliacao());
            usuarioCadsusDado.setPressaoArterialSistolica(atendimentoPrimario.getPressaoArterialSistolica());
            usuarioCadsusDado.setPressaoArterialDiastolica(atendimentoPrimario.getPressaoArterialDiastolica());
            usuarioCadsusDado.setTemperatura(atendimentoPrimario.getTemperatura());
            usuarioCadsusDado.setFrequenciaCardiaca(atendimentoPrimario.getFrequenciaCardiaca());
            usuarioCadsusDado.setGlicemia(atendimentoPrimario.getGlicemia());
            usuarioCadsusDado.setGlicemiaTipo(atendimentoPrimario.getGlicemiaTipo());
            usuarioCadsusDado.setAltura(atendimentoPrimario.getAltura());
            usuarioCadsusDado.setCintura(atendimentoPrimario.getCintura());
            usuarioCadsusDado.setPerimetroCefalico(atendimentoPrimario.getPerimetroCefalico());
            usuarioCadsusDado.setTemperaturaRetal(atendimentoPrimario.getTemperaturaRetal());
            usuarioCadsusDado.setFrequenciaRespiratoria(atendimentoPrimario.getFrequenciaRespiratoria());
            usuarioCadsusDado.setFrequenciaCardiacaFetal(atendimentoPrimario.getFrequenciaCardiacaFetal());
            usuarioCadsusDado.setDiurese(atendimentoPrimario.getDiurese());
            usuarioCadsusDado.setSaturacaoOxigenio(atendimentoPrimario.getSaturacaoOxigenio());
            usuarioCadsusDado.setIdadeGestacional(atendimentoPrimario.getIdadeGestacional());
            usuarioCadsusDado.setEdema(atendimentoPrimario.getEdema());
            usuarioCadsusDado.setAlturaUterina(atendimentoPrimario.getAlturaUterina());
            usuarioCadsusDado.setBcf(atendimentoPrimario.getBcf());
            usuarioCadsusDado.setMovFetal(atendimentoPrimario.getMovFetal());
            usuarioCadsusDado.setPesoNascer(atendimentoPrimario.getPesoNascer());
            usuarioCadsusDado.setDescricaoAlergico(atendimentoPrimario.getDescricaoAlergia());
            usuarioCadsusDado.setPeso(atendimentoPrimario.getPeso());
        }

        BOFactory.save(usuarioCadsusDado);
    }

    private void atualizarDados(UsuarioCadsusDado usuarioCadsusDado) {
        this.usuarioCadsusDado.setDataDados(usuarioCadsusDado.getDataDados());
        this.usuarioCadsusDado.setPressaoArterialSistolica(usuarioCadsusDado.getPressaoArterialSistolica());
        this.usuarioCadsusDado.setPressaoArterialDiastolica(usuarioCadsusDado.getPressaoArterialDiastolica());
        this.usuarioCadsusDado.setTemperatura(usuarioCadsusDado.getTemperatura());
        this.usuarioCadsusDado.setFrequenciaCardiaca(usuarioCadsusDado.getFrequenciaCardiaca());
        this.usuarioCadsusDado.setGlicemia(usuarioCadsusDado.getGlicemia());
        this.usuarioCadsusDado.setGlicemiaTipo(usuarioCadsusDado.getGlicemiaTipo());
        this.usuarioCadsusDado.setAltura(usuarioCadsusDado.getAltura());
        this.usuarioCadsusDado.setCintura(usuarioCadsusDado.getCintura());
        this.usuarioCadsusDado.setPerimetroCefalico(usuarioCadsusDado.getPerimetroCefalico());
        this.usuarioCadsusDado.setTemperaturaRetal(usuarioCadsusDado.getTemperaturaRetal());
        this.usuarioCadsusDado.setFrequenciaRespiratoria(usuarioCadsusDado.getFrequenciaRespiratoria());
        this.usuarioCadsusDado.setFrequenciaCardiacaFetal(usuarioCadsusDado.getFrequenciaCardiacaFetal());
        this.usuarioCadsusDado.setDiurese(usuarioCadsusDado.getDiurese());
        this.usuarioCadsusDado.setSaturacaoOxigenio(usuarioCadsusDado.getSaturacaoOxigenio());
        this.usuarioCadsusDado.setIdadeGestacional(usuarioCadsusDado.getIdadeGestacional());
        this.usuarioCadsusDado.setEdema(usuarioCadsusDado.getEdema());
        this.usuarioCadsusDado.setAlturaUterina(usuarioCadsusDado.getAlturaUterina());
        this.usuarioCadsusDado.setBcf(usuarioCadsusDado.getBcf());
        this.usuarioCadsusDado.setMovFetal(usuarioCadsusDado.getMovFetal());
        this.usuarioCadsusDado.setPesoNascer(usuarioCadsusDado.getPesoNascer());
        this.usuarioCadsusDado.setDescricaoAlergico(usuarioCadsusDado.getDescricaoAlergico());
        this.usuarioCadsusDado.setPeso(usuarioCadsusDado.getPeso());
    }

    public UsuarioCadsusDado getUsuarioCadsusDado() {
        return usuarioCadsusDado;
    }
}
