package br.com.ksisolucoes.bo.vigilancia.registroagravo;

import br.com.celk.util.CollectionUtils;
import br.com.celk.vigilancia.dto.LocalizacaoRegistroAgravoDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.enderecocoordenadas.LatitudeLongitudeEndereco;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaLocalizacaoRegistroAgravo extends CommandQuery<QueryConsultaLocalizacaoRegistroAgravo> {

    private List<RegistroAgravo> result;
    private final LocalizacaoRegistroAgravoDTOParam param;

    public QueryConsultaLocalizacaoRegistroAgravo(LocalizacaoRegistroAgravoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RegistroAgravo.class.getName());

        hql.addToSelect("registroAgravo.codigo", "codigo");
        hql.addToSelect("registroAgravo.latitude", "latitude");
        hql.addToSelect("registroAgravo.longitude", "longitude");
        hql.addToSelect("registroAgravo.dataRegistro", "dataRegistro");
        hql.addToSelect("registroAgravo.status", "status");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");

        hql.addToSelect("cid.codigo", "cid.codigo");
        hql.addToSelect("cid.descricao", "cid.descricao");


        hql.addToFrom("RegistroAgravo registroAgravo"
                + " left join registroAgravo.usuarioCadsus usuarioCadsus"
                + " left join registroAgravo.cid cid"
        );

        if (param.getStatus() == null) {
            hql.addToWhereWhithAnd("registroAgravo.status <> ", RegistroAgravo.Status.CANCELADO.value());
        } else {
            hql.addToWhereWhithAnd("registroAgravo.status = ", param.getStatus());
        }
        hql.addToWhereWhithAnd("cid = ", param.getCid());
        hql.addToWhereWhithAnd("registroAgravo.dataRegistro  ", param.getPeriodo());

        hql.addToWhereWhithAnd("registroAgravo.latitude is not null");
        hql.addToWhereWhithAnd("registroAgravo.longitude is not null");


        hql.addToOrder("registroAgravo.codigo desc");
    }

    @Override
    public List<RegistroAgravo> getResult() {
        return result;
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(param.getMaxLimit().intValue() + 1);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (LocalizacaoRegistroAgravoDTOParam.TipoEndereco.ENDERECO_ATUAL.value().equals(param.getTipoEndereco())
                && CollectionUtils.isNotNullEmpty(this.result)) {
            if (this.result.size() >= param.getMaxLimit()) {
                return;
            }
            for (RegistroAgravo registroAgravo : this.result) {
                EnderecoUsuarioCadsus enderecoUsuarioCadsus = UsuarioCadsusHelper.getEnderecoUsuarioCadsus(registroAgravo.getUsuarioCadsus(), false);
                LatitudeLongitudeEndereco latLng = new LatitudeLongitudeEndereco(enderecoUsuarioCadsus);
                if (latLng.getLongitude() != 0.0 && latLng.getLatitude() != 0.0) {
                    registroAgravo.setLatitude(latLng.getLatitude());
                    registroAgravo.setLongitude(latLng.getLongitude());
                }
            }
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
