package br.com.ksisolucoes.bo.controle.programaweb;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.controle.interfaces.dto.QueryBuscaProgramasWebUsuarioDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import br.com.ksisolucoes.vo.controle.web.ProgramaWeb;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryBuscaProgramasWebUsuario extends CommandQueryPager<QueryBuscaProgramasWebUsuario> {

    private QueryBuscaProgramasWebUsuarioDTOParam param;

    public QueryBuscaProgramasWebUsuario(QueryBuscaProgramasWebUsuarioDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelectAndGroup("pw.codigo", "codigo");
        hql.addToSelectAndGroupAndOrder("mw.descricao", "descricao");
        hql.addToSelectAndGroup("ppp.codigo", "programaPaginaPrincipal.codigo");
        hql.addToSelectAndGroup("ppp.caminhoPagina", "programaPaginaPrincipal.caminhoPagina");
        hql.addToSelectAndGroup("ppp.flagPublico", "programaPaginaPrincipal.flagPublico");
        
        hql.addToSelect("(SELECT t4.descricao || ':;' || t3.descricao || ':;' || t2.descricao || ':;' || t1.descricao"
                + " FROM MenuWeb t1, MenuWeb t2, MenuWeb t3, MenuWeb t4  "
                + " WHERE t1.programaWeb = pw"
                + "     AND t2.codigo = t1.menuWebPai.codigo"
                + "     AND t2.menuWebPai.codigo = t3.codigo"
                + "     AND t3.menuWebPai.codigo = t4.codigo and t1 = mw) "
                , "caminhoMenu");

        hql.setTypeSelect(ProgramaWeb.class.getName());

        if (!param.getUsuario().isNivelAdminOrMaster()) {
            hql.addToFrom("ControleProgramaWebGrupo cpwg "
                    + " right join cpwg.programaWeb pw "
                    + " left join pw.programaPaginaPrincipal ppp "
                    + " left join cpwg.grupo g ");
        
            hql.addToFrom("UsuarioGrupo ug "
                    + " left join ug.usuario u"
                    + " left join ug.grupo gru");
            
            hql.addToWhereWhithAnd("gru = g");
            hql.addToWhereWhithAnd("u = ", param.getUsuario());
        } else {
            //Programas do Painel de Controle

            hql.addToFrom("PainelControleProgramaWeb painelControleProgramaWeb"
                    + " right join painelControleProgramaWeb.programaWeb pw"
                    + " left join pw.programaPaginaPrincipal ppp ");
            
            String niveis = "'"+Usuario.NIVEL_NORMAL+"'";

            if (param.getUsuario().isNivelAdminOrMaster()) {
                niveis += ", '"+Usuario.NIVEL_ADMINISTRADOR+"'";
            }
            if (param.getUsuario().isNivelMaster()) {
                niveis += ", '"+Usuario.NIVEL_MASTER+"'";
            }

            hql.addToWhereWhithAnd("1 = (case when painelControleProgramaWeb.codigo is not null then (case when painelControleProgramaWeb.nivelUsuario in ("+niveis+") then 1 else 0 end) else 1 end)");
        }
        
        hql.addToFrom("MenuWeb mw "
                    + " left join mw.programaWeb pw1");
        
        Long layoutMenu = MenuWeb.LayoutMenu.PADRAO.value();
        try {
            layoutMenu = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("layoutMenu");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        
        if(MenuWeb.LayoutMenu.HOSPITAL.value().equals(layoutMenu)){
            hql.addToWhereWhithAnd(" not exists(select 1 from MenuWeb m left join m.programaWeb p where p = pw1 and m.codigo <> mw.codigo and m.layoutMenu = " 
                    + MenuWeb.LayoutMenu.HOSPITAL.value() + ")");
            hql.addToWhereWhithAnd("mw.layoutMenu <> " + MenuWeb.LayoutMenu.UNIDADE_SAUDE.value());
        } else if(MenuWeb.LayoutMenu.UNIDADE_SAUDE.value().equals(layoutMenu)){
            hql.addToWhereWhithAnd(" not exists(select 1 from MenuWeb m left join m.programaWeb p where p = pw1 and m.codigo <> mw.codigo and m.layoutMenu = " 
                    + MenuWeb.LayoutMenu.UNIDADE_SAUDE.value() + ")");
            hql.addToWhereWhithAnd("mw.layoutMenu <> " + MenuWeb.LayoutMenu.HOSPITAL.value());
        }
        
        hql.addToWhereWhithAnd("pw1 = pw ");
        hql.addToWhereWhithAnd(hql.getConsultaLiked("pw.codigo || ' ' || mw.descricao", param.getSearchCriteria()));
        if (!param.getUsuario().isNivelMaster()) {
            hql.addToWhereWhithAnd("pw.ativo = ", RepositoryComponentDefault.SIM);
        }
        hql.addToGroup("mw.codigo");
        hql.addToGroup("mw.layoutMenu");
        
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List) result);
    }
    
}