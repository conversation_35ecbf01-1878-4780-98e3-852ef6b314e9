package br.com.ksisolucoes.bo.vigilancia.atividadeestabelecimento;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QueryConsultaAtividadeEstabelecimentoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaAtividadeEstabelecimento extends CommandQueryPager<QueryConsultaAtividadeEstabelecimento> {

    private QueryConsultaAtividadeEstabelecimentoDTOParam param;

    public QueryConsultaAtividadeEstabelecimento(QueryConsultaAtividadeEstabelecimentoDTOParam param) {
        this.param = param;
    }
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("ae.codigo", true);
        hql.addToSelect("ae.descricao", true);
        hql.addToSelect("ae.exigeResponsavelTecnico", true);
        hql.addToSelect("ae.descricaoComplementar", true);

        hql.addToSelect("sv.codigo", "setorVigilancia.codigo");
        hql.addToSelect("sv.descricao", "setorVigilancia.descricao");

        hql.setTypeSelect(AtividadeEstabelecimento.class.getName());
        hql.addToFrom("AtividadeEstabelecimento ae" +
                " left join ae.setorVigilancia sv");
        
        hql.addToWhereWhithAnd("ae.codigo = ", param.getCodigo());
        hql.addToWhereWhithAnd("sv = ", param.getSetorVigilancia());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("ae.descricao", param.getDescricao()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("ae.descricaoComplementar", param.getDescricaoComplementar()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("ae.codigo || ' ' || ae.descricao",param.getKeyword()));
        
        if(param.getPropSort() != null){
            hql.addToOrder("ae."+param.getPropSort()+" "+ (param.isAscending()?"asc":"desc"));
        }else{
            hql.addToOrder("ae.descricao");
        }
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list =  hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
