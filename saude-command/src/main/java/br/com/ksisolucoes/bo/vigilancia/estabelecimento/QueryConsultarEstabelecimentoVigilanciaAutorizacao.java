package br.com.ksisolucoes.bo.vigilancia.estabelecimento;

import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ConsultaEstabelecimentoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultarEstabelecimentoVigilanciaAutorizacao extends CommandQueryPager<QueryConsultarEstabelecimentoVigilanciaAutorizacao> {

    private ConsultaEstabelecimentoDTOParam param;

    public QueryConsultarEstabelecimentoVigilanciaAutorizacao(ConsultaEstabelecimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("estabelecimento.codigo", "estabelecimento.codigo");
        hql.addToSelect("estabelecimento.razaoSocial", "estabelecimento.razaoSocial");
        hql.addToSelect("estabelecimento.fantasia", "estabelecimento.fantasia");
        hql.addToSelect("estabelecimento.dataCadastro", "estabelecimento.dataCadastro");
        hql.addToSelect("estabelecimento.tipoPessoa", "estabelecimento.tipoPessoa");
        hql.addToSelect("estabelecimento.situacao", "estabelecimento.situacao");
        hql.addToSelect("estabelecimento.cnpjCpf", "estabelecimento.cnpjCpf");
        hql.addToSelect("estabelecimento.matriz", "estabelecimento.matriz");
        hql.addToSelect("estabelecimento.numeroLogradouro", "estabelecimento.numeroLogradouro");
        hql.addToSelect("estabelecimento.complemento", "estabelecimento.complemento");
        hql.addToSelect("estabelecimento.pontoReferencia", "estabelecimento.pontoReferencia");
        hql.addToSelect("estabelecimento.telefone", "estabelecimento.telefone");
        hql.addToSelect("estabelecimento.celular", "estabelecimento.celular");
        hql.addToSelect("estabelecimento.fax", "estabelecimento.fax");
        hql.addToSelect("estabelecimento.email", "estabelecimento.email");
        hql.addToSelect("estabelecimento.site", "estabelecimento.site");
        hql.addToSelect("estabelecimento.inscricaoEstadual", "estabelecimento.inscricaoEstadual");
        hql.addToSelect("estabelecimento.inscricaoMunicipal", "estabelecimento.inscricaoMunicipal");
        hql.addToSelect("estabelecimento.dataInicioFuncionamento", "estabelecimento.dataInicioFuncionamento");
        hql.addToSelect("estabelecimento.certificacaoAnvisa", "estabelecimento.certificacaoAnvisa");
        hql.addToSelect("estabelecimento.autorizacaoAnvisa", "estabelecimento.autorizacaoAnvisa");
        hql.addToSelect("estabelecimento.outraCertificacao", "estabelecimento.outraCertificacao");
        hql.addToSelect("estabelecimento.nomeCertificacao", "estabelecimento.nomeCertificacao");
        hql.addToSelect("estabelecimento.numeroCertificacao", "estabelecimento.numeroCertificacao");
        hql.addToSelect("estabelecimento.importador", "estabelecimento.importador");
        hql.addToSelect("estabelecimento.observacao", "estabelecimento.observacao");
        hql.addToSelect("estabelecimento.representanteNome", "estabelecimento.representanteNome");
        hql.addToSelect("estabelecimento.representanteCpf", "estabelecimento.representanteCpf");
        hql.addToSelect("estabelecimento.representanteRg", "estabelecimento.representanteRg");
        hql.addToSelect("estabelecimento.representanteRgOrgao", "estabelecimento.representanteRgOrgao");
        hql.addToSelect("estabelecimento.representanteRgData", "estabelecimento.representanteRgData");
        hql.addToSelect("estabelecimento.representanteTelefone", "estabelecimento.representanteTelefone");
        hql.addToSelect("estabelecimento.representanteCelular", "estabelecimento.representanteCelular");
        hql.addToSelect("estabelecimento.dataUsuario", "estabelecimento.dataUsuario");
        hql.addToSelect("estabelecimento.protocolo", "estabelecimento.protocolo");
        hql.addToSelect("estabelecimento.alvara", "estabelecimento.alvara");
        hql.addToSelect("estabelecimento.numeroAutorizacaoSanitaria", "estabelecimento.numeroAutorizacaoSanitaria");
        hql.addToSelect("estabelecimento.validadeAlvara", "estabelecimento.validadeAlvara");
        hql.addToSelect("estabelecimento.validadeAutorizacaoSanitaria", "estabelecimento.validadeAutorizacaoSanitaria");
        hql.addToSelect("estabelecimento.dataBaixaFuncionamento", "estabelecimento.dataBaixaFuncionamento");
        hql.addToSelect("estabelecimento.numeroFuncionarios", "estabelecimento.numeroFuncionarios");
        hql.addToSelect("estabelecimento.horaInicioPrimeiroTurno", "estabelecimento.horaInicioPrimeiroTurno");
        hql.addToSelect("estabelecimento.horaFimPrimeiroTurno", "estabelecimento.horaFimPrimeiroTurno");
        hql.addToSelect("estabelecimento.horaInicioSegundoTurno", "estabelecimento.horaInicioSegundoTurno");
        hql.addToSelect("estabelecimento.horaFimSegundoTurno", "estabelecimento.horaFimSegundoTurno");
        hql.addToSelect("estabelecimento.tipoEmpresa", "estabelecimento.tipoEmpresa");
        hql.addToSelect("estabelecimento.descricaoTipoEmpresa", "estabelecimento.descricaoTipoEmpresa");
        hql.addToSelect("estabelecimento.emailContabilidade", "estabelecimento.emailContabilidade");
        hql.addToSelect("estabelecimento.numeroLogradouroRepresentante", "estabelecimento.numeroLogradouroRepresentante");
        hql.addToSelect("estabelecimento.complementoRepresentante", "estabelecimento.complementoRepresentante");
        hql.addToSelect("estabelecimento.gerarSequencialAutomatico", "estabelecimento.gerarSequencialAutomatico");
        hql.addToSelect("estabelecimento.especialidade", "estabelecimento.especialidade");
        hql.addToSelect("estabelecimento.realizaProcedimentoInvasivo", "estabelecimento.realizaProcedimentoInvasivo");
        hql.addToSelect("estabelecimento.procedimentoInvasivoRealizado", "estabelecimento.procedimentoInvasivoRealizado");
        hql.addToSelect("estabelecimento.origemCadastro", "estabelecimento.origemCadastro");
        hql.addToSelect("estabelecimento.observacaoDestaqueAlvara", "estabelecimento.observacaoDestaqueAlvara");
        hql.addToSelect("estabelecimento.flagHoraFuncionamento", "estabelecimento.flagHoraFuncionamento");
        hql.addToSelect("estabelecimento.dataSemResponsavelTecnico", "estabelecimento.dataSemResponsavelTecnico");
        hql.addToSelect("estabelecimento.trabalhaMedicamentoControlado", "estabelecimento.trabalhaMedicamentoControlado");
        hql.addToSelect("estabelecimento.emiteReceita", "estabelecimento.emiteReceita");
        hql.addToSelect("estabelecimento.formaFaturamento", "estabelecimento.formaFaturamento");
        hql.addToSelect("estabelecimento.version", "estabelecimento.version");
        hql.addToSelect("estabelecimento.flagSolicReceitaB1", "estabelecimento.flagSolicReceitaB1");
        hql.addToSelect("estabelecimento.flagSolicReceitaB2", "estabelecimento.flagSolicReceitaB2");
        hql.addToSelect("estabelecimento.flagSolicReceitaC2", "estabelecimento.flagSolicReceitaC2");
        hql.addToSelect("estabelecimento.flagSolicReceitaTalidomida", "estabelecimento.flagSolicReceitaTalidomida");
        hql.addToSelect("estabelecimento.tipoGrupoDrogariaSomatorio", "estabelecimento.tipoGrupoDrogariaSomatorio");
        hql.addToSelect("estabelecimento.tipoGrupoFarmaciaSomatorio", "estabelecimento.tipoGrupoFarmaciaSomatorio");
        hql.addToSelect("estabelecimento.tipoGrupoFarmaciaGrupoIIISomatorio", "estabelecimento.tipoGrupoFarmaciaGrupoIIISomatorio");
        hql.addToSelect("estabelecimento.flagDrogaria", "estabelecimento.flagDrogaria");
        hql.addToSelect("estabelecimento.flagFarmacia", "estabelecimento.flagFarmacia");
        hql.addToSelect("estabelecimento.dataValidadePrimeiraLicenca", "estabelecimento.dataValidadePrimeiraLicenca");
        hql.addToSelect("estabelecimento.dataValidadePrimeiroAlvara", "estabelecimento.dataValidadePrimeiroAlvara");
        hql.addToSelect("estabelecimento.dataValidadePrimeiroCredenciamento", "estabelecimento.dataValidadePrimeiroCredenciamento");
        hql.addToSelect("estabelecimento.dataValidadePrimeiraAutorizacaoSanitaria", "estabelecimento.dataValidadePrimeiraAutorizacaoSanitaria");
        hql.addToSelect("estabelecimento.flagMicroEmpresa", "estabelecimento.flagMicroEmpresa");
        hql.addToSelect("estabelecimento.tipoPorte", "estabelecimento.tipoPorte");
        hql.addToSelect("estabelecimento.tipoPorteOutros", "estabelecimento.tipoPorteOutros");
        hql.addToSelect("estabelecimento.cnpjCpf", "estabelecimento.cnpjCpf");


        hql.addToSelect("usuarioResponsavel.codigo", "estabelecimento.usuarioResponsavel.codigo");
        hql.addToSelect("usuarioResponsavel.nome", "estabelecimento.usuarioResponsavel.nome");

        hql.addToSelect("vigilanciaEndereco.codigo", "estabelecimento.vigilanciaEndereco.codigo");
        hql.addToSelect("vigilanciaEndereco.cep", "estabelecimento.vigilanciaEndereco.cep");
        hql.addToSelect("vigilanciaEndereco.bairro", "estabelecimento.vigilanciaEndereco.bairro");
        hql.addToSelect("vigilanciaEndereco.logradouro", "estabelecimento.vigilanciaEndereco.logradouro");
        
        hql.addToSelect("cidade.codigo", "estabelecimento.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("cidade.descricao", "estabelecimento.vigilanciaEndereco.cidade.descricao");
        
        hql.addToSelect("estado.descricao", "estabelecimento.vigilanciaEndereco.cidade.estado.descricao");
        hql.addToSelect("estado.sigla", "estabelecimento.vigilanciaEndereco.cidade.estado.sigla");
        
        hql.addToSelect("vigilanciaEnderecoRepresentante.codigo", "estabelecimento.vigilanciaEnderecoRepresentante.codigo");
        hql.addToSelect("vigilanciaEnderecoRepresentante.cep", "estabelecimento.vigilanciaEnderecoRepresentante.cep");
        hql.addToSelect("vigilanciaEnderecoRepresentante.bairro", "estabelecimento.vigilanciaEnderecoRepresentante.bairro");
        hql.addToSelect("vigilanciaEnderecoRepresentante.logradouro", "estabelecimento.vigilanciaEnderecoRepresentante.logradouro");
        
        hql.addToSelect("cidadeRepresentante.codigo", "estabelecimento.vigilanciaEnderecoRepresentante.cidade.codigo");
        hql.addToSelect("cidadeRepresentante.descricao", "estabelecimento.vigilanciaEnderecoRepresentante.cidade.descricao");
        
        hql.addToSelect("estadoRepresentante.descricao", "estabelecimento.vigilanciaEnderecoRepresentante.cidade.estado.descricao");
        hql.addToSelect("estadoRepresentante.sigla", "estabelecimento.vigilanciaEnderecoRepresentante.cidade.estado.sigla");
        
        hql.addToSelect("usuario.codigo", "estabelecimento.usuario.codigo");
        hql.addToSelect("usuario.nome", "estabelecimento.usuario.nome");
        
        hql.addToSelect("estabelecimentoPrincipal.codigo", "estabelecimento.estabelecimentoPrincipal.codigo");
        hql.addToSelect("estabelecimentoPrincipal.cnpjCpf", "estabelecimento.estabelecimentoPrincipal.cnpjCpf");
        hql.addToSelect("estabelecimentoPrincipal.razaoSocial", "estabelecimento.estabelecimentoPrincipal.razaoSocial");

        hql.addToSelect("(select ae.descricao from EstabelecimentoAtividade ea "
                + "left join ea.atividadeEstabelecimento ae "
                + "left join ea.estabelecimento e "
                + "where e.codigo = estabelecimento.codigo and ea.flagPrincipal = " + RepositoryComponentDefault.SIM_LONG + ")", "descricaoAtividadePrincipal");
        
        hql.addToFrom("Estabelecimento estabelecimento"
                + " left join estabelecimento.estabelecimentoPrincipal estabelecimentoPrincipal"
                + " left join estabelecimento.usuario usuario"
                + " left join estabelecimento.vigilanciaEndereco vigilanciaEndereco"
                + " left join vigilanciaEndereco.cidade cidade"
                + " left join cidade.estado estado"
                + " left join estabelecimento.vigilanciaEnderecoRepresentante vigilanciaEnderecoRepresentante"
                + " left join vigilanciaEnderecoRepresentante.cidade cidadeRepresentante"
                + " left join cidadeRepresentante.estado estadoRepresentante"
                + " left join estabelecimento.usuarioResponsavel usuarioResponsavel ");

//        hql.addToWhereWhithAnd("estabelecimento.usuarioResponsavel IS NOT NULL");

        hql.addToWhereWhithAnd("estabelecimento.usuarioResponsavel = ", this.param.getUsuarioResponsavel());
        hql.addToWhereWhithAnd("estabelecimento.cnpjCpf = ", param.getCpfCnpj());

        hql.addToWhereWhithAnd(hql.getConsultaLiked("estabelecimento.razaoSocial", param.getRazaoSocial()));
        hql.addToWhereWhithAnd(hql.getConsultaLiked("estabelecimento.fantasia", param.getFantasia()));
        if (param.getSituacao() == null) {
            hql.addToWhereWhithAnd("estabelecimento.situacao in ", Arrays.asList(Estabelecimento.Situacao.PROVISORIO.value(), Estabelecimento.Situacao.NAO_AUTORIZADO.value()));
        } else {
            hql.addToWhereWhithAnd("estabelecimento.situacao = ", param.getSituacao());
        }

        if(param.getAtividadeEstabelecimento() != null){
            hql.addToWhereWhithAnd("exists(select 1 from EstabelecimentoAtividade ea "
                + "left join ea.atividadeEstabelecimento ae "
                + "left join ea.estabelecimento e "
                + "where e.codigo = estabelecimento.codigo and ea.flagPrincipal = " + RepositoryComponentDefault.SIM_LONG 
                + " and ae.codigo = " + param.getAtividadeEstabelecimento().getCodigo() + ")");
        }
        
        if (param.getSortProp() != null) {
            hql.addToOrder(param.getSortProp() + " " + (param.isAscending() ? "asc" : "desc"));
        }
        
        hql.setTypeSelect(ConsultaEstabelecimentoDTO.class.getName());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}
