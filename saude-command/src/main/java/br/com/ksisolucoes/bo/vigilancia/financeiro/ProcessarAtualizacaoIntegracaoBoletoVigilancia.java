package br.com.ksisolucoes.bo.vigilancia.financeiro;

import br.com.celk.bo.integracao.boleto.IntegracaoBoletoHelper;
import br.com.celk.boleto.dto.caixa.HeaderConsumerDTO;
import br.com.celk.boleto.enumeration.Situacao;
import br.com.celk.boleto.exception.BoletoException;
import br.com.celk.boleto.integracao.consumer.caixa.ConsumerWS;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ProcessarSituacaoRecebimentoBoletoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.integracao.boleto.Boleto;
import br.com.ksisolucoes.vo.integracao.boleto.BoletoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;

import java.util.List;

public class ProcessarAtualizacaoIntegracaoBoletoVigilancia extends AbstractCommandTransaction {

    private List<Long> codigosFinanceiroList;
    private String descricaoOcorrencia;

    private Boleto boleto;
    private VigilanciaFinanceiro vigilanciaFinanceiro;
    private Usuario usuario;

    public ProcessarAtualizacaoIntegracaoBoletoVigilancia(List<Long> codigosFinanceiroList, String descricaoOcorrencia) {
        this.codigosFinanceiroList = codigosFinanceiroList;
        this.descricaoOcorrencia = descricaoOcorrencia;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        String tipoIntegracao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("TipoIntegracao_BoletoAPI");
        if ("REMESSA".equals(tipoIntegracao)) {
            return;
        }

        ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro = LoadManager.getInstance(ConfiguracaoVigilanciaFinanceiro.class)
                .addProperty(ConfiguracaoVigilanciaFinanceiro.PROP_CODIGO_BENEFICIARIO)
                .addProperty(ConfiguracaoVigilanciaFinanceiro.PROP_CPF_CNPJ_BENEFICIARIO)
                .addSorter(new QueryCustom.QueryCustomSorter(ConfiguracaoVigilanciaFinanceiro.PROP_CODIGO))
                .start().setMaxResults(1).getVO();

        usuario = new Usuario(Usuario.USUARIO_ADMINISTRADOR);
        if (getSessao() != null && getSessao().getUsuario() != null) {
            usuario = getSessao().getUsuario();
        }

        for (Long codigoFinanceiro : codigosFinanceiroList) {
            vigilanciaFinanceiro = HibernateUtil.lockTable(VigilanciaFinanceiro.class, codigoFinanceiro);
            if (vigilanciaFinanceiro.getBoleto() != null) {
                boleto = HibernateUtil.lockTable(Boleto.class, vigilanciaFinanceiro.getBoleto().getCodigo());
                Situacao situacao = null;
                try {
                    HeaderConsumerDTO headerConsumerDTO = new HeaderConsumerDTO();
                    headerConsumerDTO.setCodigoBeneficiario(configuracaoVigilanciaFinanceiro.getCodigoBeneficiario());
                    headerConsumerDTO.setCnpj(configuracaoVigilanciaFinanceiro.getCpfCnpjBeneficiario());
                    headerConsumerDTO.setNumero(Long.valueOf(boleto.getNumero()));

                    ConsumerWS consumer = IntegracaoBoletoHelper.getConsumerInstanceWS();

                    situacao = consumer.getSituacao(headerConsumerDTO);
                } catch (BoletoException e) {
                    String message = e.getValidation().getMessage();

                    gerarBoletoOcorrencia(situacao, "Retorno do Banco: " + message);
                    continue;
                }

                ProcessarSituacaoRecebimentoBoletoDTO dto = new ProcessarSituacaoRecebimentoBoletoDTO();
                dto.setBoleto(boleto);
                dto.setVigilanciaFinanceiro(vigilanciaFinanceiro);
                dto.setSituacao(situacao);
                BOFactory.getBO(VigilanciaFacade.class).processarSituacaoRecebimentoBoleto(dto);

                gerarBoletoOcorrencia(situacao, descricaoOcorrencia);
            }
        }
    }

    private void gerarBoletoOcorrencia(Situacao situacao, String descricaoOcorrencia) throws DAOException, ValidacaoException {
        BoletoOcorrencia boletoOcorrencia = new BoletoOcorrencia();

        boletoOcorrencia.setBoleto(boleto);
        boletoOcorrencia.setData(DataUtil.getDataAtual());
        boletoOcorrencia.setDescricao(descricaoOcorrencia);
        if (situacao != null) {
            boletoOcorrencia.resolverSituacao(situacao);
        }
        boletoOcorrencia.setUsuario(usuario);

        BOFactory.save(boletoOcorrencia);
    }

}
