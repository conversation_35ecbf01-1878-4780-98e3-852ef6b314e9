package br.com.ksisolucoes.bo.consorcio.consorcioprestador;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import br.com.ksisolucoes.vo.controle.Usuario;

/**
 *
 * <AUTHOR>
 */
public class SaveConsorcioPrestador extends SaveVO<ConsorcioPrestador> {

    public SaveConsorcioPrestador(ConsorcioPrestador vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        this.vo.setDataUsuario(Data.getDataAtual());
        this.vo.setUsuario(getSessao().<Usuario>getUsuario());
    }
}
