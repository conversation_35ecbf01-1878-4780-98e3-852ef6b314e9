package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentolicencatransporte;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoBaixaVeiculoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VeiculoEstabelecimentoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.EloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaVeiculo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoBaixaVeiculoItens;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SalvarRequerimentoBaixaVeiculo extends AbstractCommandTransaction<SalvarRequerimentoBaixaVeiculo> {

    private final RequerimentoBaixaVeiculoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private List<VeiculoEstabelecimentoDTO> veiculoEstabelecimentoDTOList;
    private ConfiguracaoVigilancia configuracaoVigilancia;
    private boolean gerarOcorrenciaCadastro = false;

    public SalvarRequerimentoBaixaVeiculo(RequerimentoBaixaVeiculoDTO dto) {
        this.dto = dto;
        this.veiculoEstabelecimentoDTOList = dto.getLstVeiculoEstabelecimentoDTO();
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        carregarConfiguracaoVigilancia();
        if (this.dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getDataRequerimento() == null) {
            this.dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setDataRequerimento(DataUtil.getDataAtual());
        }
        if (CollectionUtils.isAllEmpty(veiculoEstabelecimentoDTOList)) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_obrgatorio_ao_menos_um_veiculo"));
        }

        RequerimentoBaixaVeiculo requerimentoBaixaVeiculo = dto.getRequerimentoBaixaVeiculo();

        if (requerimentoBaixaVeiculo.getRequerimentoVigilancia().getCodigo() == null) {
            dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setEstabelecimento(dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().getEstabelecimento());
            dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.BAIXA_VEICULO.value());
            dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            if (ConfiguracaoVigilanciaEnum.TipoGestaoRequerimento.FISCAL.value().equals(configuracaoVigilancia.getFlagTipoGestaoRequerimento())) {
                dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.AGUARDANDO_INFORMAR_FISCAL.value());
            } else {
                dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia().setSituacaoAprovacao(RequerimentoVigilancia.SituacaoAprovacao.APROVADO.value());
            }
            gerarOcorrenciaCadastro = true;
        }

        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoBaixaVeiculo().getRequerimentoVigilancia()));

        requerimentoVigilancia = VigilanciaHelper.atualizarGestaoRequerimento(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), gerarOcorrenciaCadastro);

        if (gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);
        }

        dto.getRequerimentoBaixaVeiculo().setRequerimentoVigilancia(requerimentoVigilancia);


        requerimentoBaixaVeiculo = BOFactory.save(requerimentoBaixaVeiculo);
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);

        if (requerimentoBaixaVeiculo.getRequerimentoVigilancia().getVersion() == 0 && CollectionUtils.isEmpty(dto.getRequerimentoBaixaVeiculoItensList())) {
            for (VeiculoEstabelecimentoDTO veiculoEstabelecimentoDTO : veiculoEstabelecimentoDTOList) {
                RequerimentoBaixaVeiculoItens requerimentoBaixaVeiculoItens = new RequerimentoBaixaVeiculoItens();
                requerimentoBaixaVeiculoItens.setVeiculoEstabelecimento(veiculoEstabelecimentoDTO.getVeiculoEstabelecimento());
                requerimentoBaixaVeiculoItens.setRequerimentoBaixaVeiculo(requerimentoBaixaVeiculo);

                BOFactory.save(requerimentoBaixaVeiculoItens);
            }
        }

        if (CollectionUtils.isNotNullEmpty(dto.getEloRequerimentoVigilanciaSetorVigilanciaList())) {
            List<EloRequerimentoVigilanciaSetorVigilancia> eloList = new ArrayList<>();
            EloRequerimentoVigilanciaSetorVigilancia newElo;
            for (EloRequerimentoVigilanciaSetorVigilancia elo : dto.getEloRequerimentoVigilanciaSetorVigilanciaList()) {
                newElo = new EloRequerimentoVigilanciaSetorVigilancia();
                newElo.setSetorVigilancia(elo.getSetorVigilancia());

                eloList.add(newElo);
            }

            dto.getEloRequerimentoVigilanciaSetorVigilanciaList().clear();
            dto.getEloRequerimentoVigilanciaSetorVigilanciaList().addAll(eloList);
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());
        if (gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }


    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}