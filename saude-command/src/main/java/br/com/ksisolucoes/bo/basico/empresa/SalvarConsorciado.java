/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.basico.empresa;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.EstoqueEmpresaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaPK;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarConsorciado extends AbstractCommandTransaction {

    private Empresa empresa;

    public SalvarConsorciado(Empresa empresa) {
        this.empresa = empresa;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (empresa.getEmpresaMaterial() == null) {
            empresa.setEmpresaMaterial(new EmpresaMaterial(empresa.getCodigo()));
        }
        empresa.setTipoUnidade(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO);
        BOFactory.save(empresa);
        
        Long codigoEmpresaLogada = getSessao().getCodigoEmpresa();

        if(! empresa.getCodigo().equals(codigoEmpresaLogada)){
            List<EstoqueEmpresa> estoqueEmpresaList = LoadManager.getInstance(EstoqueEmpresa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA, Empresa.PROP_CODIGO), codigoEmpresaLogada))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                    .start().getList();
            
            BOFactory.getBO(EstoqueEmpresaFacade.class).copiarEstoqueUnidade(estoqueEmpresaList, Arrays.asList(empresa));
        }
    }

    public Empresa getEmpresa() {
        return empresa;
    }
    
}
