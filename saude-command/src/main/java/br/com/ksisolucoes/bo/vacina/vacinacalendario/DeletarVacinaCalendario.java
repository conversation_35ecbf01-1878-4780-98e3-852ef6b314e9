package br.com.ksisolucoes.bo.vacina.vacinacalendario;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import br.com.ksisolucoes.vo.vacina.VacinaCalendarioAprazamento;
import br.com.ksisolucoes.vo.vacina.VacinaCalendarioEsconder;
import static ch.lambdaj.Lambda.on;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class DeletarVacinaCalendario extends AbstractCommandTransaction {

    private VacinaCalendario vacinaCalendario;

    public DeletarVacinaCalendario(VacinaCalendario vacinaCalendario) {
        this.vacinaCalendario = vacinaCalendario;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        // Excluir as vacinas que devem ser aprazadas
        VacinaCalendarioAprazamento proxyAprazamento = on(VacinaCalendarioAprazamento.class);
        
        List<VacinaCalendarioAprazamento> aprazamentoList = getSession().createCriteria(VacinaCalendarioAprazamento.class)
                .add(Restrictions.eq(path(proxyAprazamento.getVacinaCalendarioOrigem()), vacinaCalendario))
                .list();
        
        if(CollectionUtils.isNotNullEmpty(aprazamentoList)){
            for(VacinaCalendarioAprazamento vca : aprazamentoList){
                BOFactory.delete(vca);                
            }
        }
        
        // Excluir as vacinas que devem ser escondidas
        VacinaCalendarioEsconder proxyVacinasEsconder = on(VacinaCalendarioEsconder.class);
        
        List<VacinaCalendarioEsconder> vacinasEsconderList = getSession().createCriteria(VacinaCalendarioEsconder.class)
                .add(Restrictions.eq(path(proxyVacinasEsconder.getVacinaCalendarioOrigem()), vacinaCalendario))
                .list();
        
        if(CollectionUtils.isNotNullEmpty(vacinasEsconderList)){
            for(VacinaCalendarioEsconder vca : vacinasEsconderList){
                BOFactory.delete(vca);                
            }
        }
        
        BOFactory.delete(vacinaCalendario);
    }
    
}