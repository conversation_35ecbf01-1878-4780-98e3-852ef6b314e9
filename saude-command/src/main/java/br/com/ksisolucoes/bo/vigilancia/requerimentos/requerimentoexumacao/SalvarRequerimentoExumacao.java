package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoexumacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioAutorizacaoExumacaoDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoExumacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaAtividades;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class SalvarRequerimentoExumacao extends AbstractCommandTransaction<SalvarRequerimentoExumacao> {
    
    private final RequerimentoExumacaoDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private boolean gerarOcorrenciaCadastro = false;
    private boolean gerarOcorrenciaFinalizado = false;

    public SalvarRequerimentoExumacao(RequerimentoExumacaoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if(dto.getRequerimentoExumacao().getRequerimentoVigilancia().getCodigo() == null){
            dto.getRequerimentoExumacao().getRequerimentoVigilancia().setTipoSolicitacao(dto.getTipoSolicitacao());
            dto.getRequerimentoExumacao().getRequerimentoVigilancia().setTipoDocumento(TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.value());
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(dto.getRequerimentoExumacao().getFlagExigeFiscal()) || RequerimentoVigilancia.Origem.EXTERNO.value().equals(dto.getRequerimentoExumacao().getRequerimentoVigilancia().getOrigem())){
            dto.getRequerimentoExumacao().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            gerarOcorrenciaCadastro = true;
        }else {
            dto.getRequerimentoExumacao().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.FINALIZADO.value());
            gerarOcorrenciaFinalizado = true;
        }

        dto.getRequerimentoExumacao().getRequerimentoVigilancia().setNome(dto.getRequerimentoExumacao().getNomeMorto());
        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoExumacao().getRequerimentoVigilancia()));
        
        if(gerarOcorrenciaFinalizado){
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_finalizado"), requerimentoVigilancia, null);
        } else if(gerarOcorrenciaCadastro){
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);
        }
        
        dto.getRequerimentoExumacao().setRequerimentoVigilancia(requerimentoVigilancia);
        BOFactory.save(dto.getRequerimentoExumacao());
        
        if(RequerimentoVigilancia.Situacao.FINALIZADO.value().equals(requerimentoVigilancia.getSituacao())){

            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoProtocoloSemBPA(requerimentoVigilancia, ConfiguracaoVigilanciaAtividades.TipoProcessoPadrao.EXUMACAO_RESTOS_MORTAIS_FINALIZACAO);

            try{
                RelatorioAutorizacaoExumacaoDTOParam paramExumacao = new RelatorioAutorizacaoExumacaoDTOParam();
                paramExumacao.setCodigoRequerimentoVigilancia(requerimentoVigilancia.getCodigo());
                DataReport dataReport = BOFactory.getBO(VigilanciaReportFacade.class).relatorioRequerimentoExumacao(paramExumacao);
                Long origemArquivo = GerenciadorArquivo.OrigemArquivo.REQUERIMENTO_EXUMACAO_RESTOS_MORTAIS.value();

                if(dataReport != null){
                    String descricaoAnexo = TipoSolicitacao.TipoDocumento.EXUMACAO_RESTOS_MORTAIS.descricao();
                    File file = File.createTempFile(Coalesce.asString(descricaoAnexo, "anexo").toUpperCase(), ".pdf");
                    JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

                    RequerimentoVigilanciaAnexoDTO anexoDTO = new RequerimentoVigilanciaAnexoDTO();
                    anexoDTO.setFile(file);
                    anexoDTO.setOrigem(origemArquivo);
                    anexoDTO.setNomeArquivoOriginal(Coalesce.asString(descricaoAnexo, "anexo").toUpperCase() + ".pdf");
                    anexoDTO.setDescricaoAnexo(Coalesce.asString(descricaoAnexo, "anexo").toUpperCase());

                    if(CollectionUtils.isEmpty(dto.getRequerimentoVigilanciaAnexoDTOList())){
                        dto.setRequerimentoVigilanciaAnexoDTOList(new ArrayList<RequerimentoVigilanciaAnexoDTO>());
                    }

                    dto.getRequerimentoVigilanciaAnexoDTOList().add(anexoDTO);
                }
            } catch (ReportException | IOException | JRException ex) {
                Logger.getLogger(SalvarRequerimentoExumacao.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());

        if(gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }
    }
    
    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}