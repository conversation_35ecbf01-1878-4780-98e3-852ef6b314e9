package br.com.ksisolucoes.bo.hospital.faturamento;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.LoadInterceptorTipoAtendimentoOnAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class GerarItemContaFromAih extends AbstractCommandTransaction<GerarItemContaFromAih> {

    private final Aih aih;

    public GerarItemContaFromAih(Aih aih) {
        this.aih = aih;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        final Atendimento atendimento = aih.getAtendimento();
        ContaPaciente contaPaciente = BOFactory.getBO(HospitalFacade.class).encontrarContaPaciente(atendimento);

        if (contaPaciente == null) {
            return;
        }

        ItemContaPaciente itemConta = new ItemContaPaciente();
        itemConta.setContaPaciente(contaPaciente);

        itemConta.setAtendimento(atendimento);
        
        itemConta.setProfissional(atendimento.getProfissionalResponsavel());
        
        Procedimento procedimento = null;
        if(aih.getProcedimentoRealizado() != null && aih.getProcedimentoRealizado().getCodigo() != null){
            procedimento = (Procedimento) this.getSession().createCriteria(Procedimento.class)
                .add(Restrictions.eq(Procedimento.PROP_CODIGO, aih.getProcedimentoRealizado().getCodigo()))
                .uniqueResult();
            
            itemConta.setProcedimento(procedimento);
            itemConta.setTipoMovimentoContaFinanceira(procedimento.getTipoMovimentoContaFinanceira());
            if(aih.getCidPrincipal() != null){
                itemConta.setCid(aih.getCidPrincipal());
            }
        }
                    

        TipoAtendimento tipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                .addProperty(TipoAtendimento.PROP_CODIGO)
                .addInterceptor(new LoadInterceptorTipoAtendimentoOnAtendimento(atendimento.getAtendimentoPrincipal().getCodigo()))
                .start().getVO();

        Double preco = BOFactory.getBO(HospitalFacade.class).getPrecoProcedimento(procedimento, atendimento.getConvenio(), tipoAtendimento.getCodigo());

        if (preco != null) {
            itemConta.setPrecoUnitario(preco);
        }

        itemConta.setQuantidade(1D);

        itemConta.setTipo(ItemContaPaciente.Tipo.PROCEDIMENTO.value());
        itemConta.setOrigemLancamento(ItemContaPaciente.OrigemLancamento.AIH.value());
        itemConta.setStatus(ItemContaPaciente.Status.ABERTO.value());
        itemConta.setUsuario(this.getSessao().getUsuario());
        itemConta.setDataUsuario(DataUtil.getDataAtual());

        BOFactory.save(itemConta);
    }

}
