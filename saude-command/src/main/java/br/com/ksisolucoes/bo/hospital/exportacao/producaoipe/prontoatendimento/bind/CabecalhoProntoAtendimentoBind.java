package br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.prontoatendimento.bind;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.bo.hospital.exportacao.producaoipe.dto.ProducaoIpeDTO;
import org.apache.camel.dataformat.bindy.annotation.DataField;
import org.apache.camel.dataformat.bindy.annotation.FixedLengthRecord;

/**
 *
 * <AUTHOR>
 */

@FixedLengthRecord(length = 99, crlf = "WINDOWS")
public class CabecalhoProntoAtendimentoBind implements IBindVoExport<ProducaoIpeDTO> {

    @DataField(pos = 1, length = 3, required = true)
    private String nomeSistema;
    @DataField(pos = 2, length = 14, align = "R", required = true)
    private String docPrestador;
    @DataField(pos = 3, length = 4, align = "R", paddingChar = '0', required = true)
    private Long quantNotas;
    @DataField(pos = 4, length = 5, align = "R", paddingChar = '0', required = true)
    private Long quantLancamentosReferencias;
    @DataField(pos = 5, length = 8, align = "R", required = true)
    private String nroPrestador;
    @DataField(pos = 6, length = 45, align = "R", clip = true, required = true)
    private String nomePrestador;
    @DataField(pos = 7, length = 20, align = "R", required = true)
    private String filler;
    
    @Override
    public void buildProperties(ProducaoIpeDTO vo) {
        
        nomeSistema = "SMH";
        if(vo.getEmpresaPrincipal().getCnpj() != null){
            docPrestador = vo.getEmpresaPrincipal().getCnpj();
        } else {
            docPrestador = vo.getEmpresaPrincipal().getCodigo().toString();
        }
        quantNotas = 1L;
        quantLancamentosReferencias = vo.getQuantLancamentosReferencias();
        nroPrestador = vo.getEmpresaPrincipal().getNumeroPrestadorIpe();
        nomePrestador = vo.getEmpresaPrincipal().getDescricao();
    }
}