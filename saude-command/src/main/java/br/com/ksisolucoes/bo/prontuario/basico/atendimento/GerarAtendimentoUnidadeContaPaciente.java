package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.celk.faturamento.FechamentoContaHelper;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.recepcao.interfaces.dto.GerarAtendimentoDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.methods.CoreMethods;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimentoCboProcedimento;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import ch.lambdaj.Lambda;
import java.util.List;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class GerarAtendimentoUnidadeContaPaciente extends AbstractCommandTransaction {

    private GerarAtendimentoDTO dto;
    private Atendimento atendimento;

    public GerarAtendimentoUnidadeContaPaciente(GerarAtendimentoDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        atendimento = BOFactory.getBO(AtendimentoFacade.class).inserirAtendimentoUnidade(dto);
        
        if(atendimento.getProfissional() != null){
            ProfissionalCargaHoraria proxyPCH = Lambda.on(ProfissionalCargaHoraria.class);
            TipoAtendimentoCboProcedimento proxyTACP = Lambda.on(TipoAtendimentoCboProcedimento.class);

            TipoAtendimentoCboProcedimento tacp = null;
            List<ProfissionalCargaHoraria> pchList = getSession().createCriteria(ProfissionalCargaHoraria.class)
                    .add(Restrictions.eq(CoreMethods.path(proxyPCH.getEmpresa()), atendimento.getEmpresa()))
                    .add(Restrictions.eq(CoreMethods.path(proxyPCH.getProfissional()), atendimento.getProfissional()))
                    .list();
            for (ProfissionalCargaHoraria pch : pchList) {
                tacp = (TipoAtendimentoCboProcedimento) getSession().createCriteria(TipoAtendimentoCboProcedimento.class)
                        .add(Restrictions.eq(CoreMethods.path(proxyTACP.getTipoAtendimento()), atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                        .add(Restrictions.eq(CoreMethods.path(proxyTACP.getTabelaCbo()), pch.getTabelaCbo()))
                        .uniqueResult();
                if (tacp != null) {
                    break;
                }
            }
            if (tacp != null) {
                ProcedimentoCompetencia pc = new AtendimentoHelper().validaProcedimentoCompetencia(tacp.getProcedimento(), atendimento.getUsuarioCadsus());
                atendimento.setProcedimentoCompetencia(pc);

                BOFactory.save(atendimento);

                ItemContaPaciente icp = (ItemContaPaciente) this.getSession().createCriteria(ItemContaPaciente.class)
                        .add(Restrictions.eq(ItemContaPaciente.PROP_ATENDIMENTO, atendimento))
                        .uniqueResult();

                if(icp != null){
                    icp.setProcedimento(tacp.getProcedimento());

                    //validacoes para setar status confirmado ou aberto
                    try {
                        FechamentoContaHelper.ajustarItemContaPaciente(icp);

                        if (RepositoryComponentDefault.NAO.equals(icp.getProcedimentoBpa().getFlagFaturavel())) {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_procedimento_nao_faturavel_nao_sera_confirmado"));
                        }

                        icp.setStatus(ItemContaPaciente.Status.CONFIRMADO.value());
                    } catch (ValidacaoException ex) {
                        icp.setStatus(ItemContaPaciente.Status.ABERTO.value());
                        Loggable.log.warn(ex.getMessage(), ex);
                    }

                    BOFactory.save(icp);
                }
            }
        }
        
    }
    
    public Atendimento getAtendimento() {
        return atendimento;
    }
    
}
