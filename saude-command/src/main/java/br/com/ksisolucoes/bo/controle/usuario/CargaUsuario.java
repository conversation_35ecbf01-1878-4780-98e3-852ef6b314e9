/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.controle.usuario;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.ProcessTime;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.*;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.LinkedHashSet;
import java.util.List;
import org.hibernate.Query;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Property;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class CargaUsuario extends AbstractCommandTransaction {

    private Usuario usuario;
    
    private List<Modulo> modulos;

    public List<Modulo> getModulos() {
        return modulos;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        this.modulos = new ArrayList<Modulo>();
        
        this.usuario = SessaoAplicacaoImp.getInstance().getUsuario();
        
        ProcessTime processTime = new ProcessTime();
        processTime.start();
        Collection<Programa> programas = getProgramas();
        
        for (Programa programa : programas) {
            Menu menu = programa.getMenu();
            List<Programa> programasMenu = menu.getProgramas();
            if (programasMenu == null) {
                programasMenu = new ArrayList<Programa>();
            }
            if (!programasMenu.contains(programa)) {
                programasMenu.add(programa);
            }
            menu.setProgramas(programasMenu);
            
            
            processTime.start();
            addMenu(menu);
            
        }

        carregarProgramas(programas);

        Loggable.log.info("tempo carga usuario: "+processTime.stop().toString());
        
        ordenarModulos();
        
    }
    
    public Collection<Programa> getProgramas(){
        
        Collection<Programa> programas = new LinkedHashSet<Programa>();
        
        if (this.usuario.isNivelMaster()) {
            List<Programa> programasMaster = this.getSession().createCriteria(Programa.class).
                    createAlias(Programa.PROP_MENU, Programa.PROP_MENU).list();
            programas.addAll(programasMaster);
        } else if(this.usuario.isNivelAdmin()) {
            List<Programa> programasMaster = this.getSession().createCriteria(Programa.class)
                    .add(Restrictions.eq(Programa.PROP_ATIVO, RepositoryComponentDefault.SIM))
                    .createAlias(Programa.PROP_MENU, Programa.PROP_MENU).list();
            programas.addAll(programasMaster);
        } else {
            
            DetachedCriteria subselectGrupos = DetachedCriteria.forClass(UsuarioGrupo.class)
                                           .setProjection(Projections.property(VOUtils.montarPath(UsuarioGrupo.PROP_GRUPO, Grupo.PROP_CODIGO)))
                                           .add(Restrictions.eq(VOUtils.montarPath(UsuarioGrupo.PROP_USUARIO), this.usuario));
        
            List<ControleProgramaGrupo> controleProgramaGrupos = this.getSession().createCriteria(ControleProgramaGrupo.class)
                    .add(Property.forName(VOUtils.montarPath(ControleProgramaGrupo.PROP_GRUPO, Grupo.PROP_CODIGO)).in(subselectGrupos))
                    .createCriteria(ControleProgramaGrupo.PROP_PROGRAMA)
                        .add(Restrictions.eq(Programa.PROP_ATIVO, RepositoryComponentDefault.SIM))
                    .setFetchSize(5)
                    .list();
            
                    
            for (ControleProgramaGrupo controleProgramaGrupo : controleProgramaGrupos) {
                programas.add(controleProgramaGrupo.getPrograma());
            }

        }
        
        return programas;
    }

    private void addMenu(Menu menu) {
        Long codigoPai = menu.getCodigoPai();
        if (codigoPai == null) {
            Modulo modulo = menu.getModulo();
            List<Menu> menus = modulo.getMenus();
            if (menus == null) {
                menus = new ArrayList<Menu>();
            }
            if (!menus.contains(menu)) {
                menus.add(menu);
            }
            modulo.setMenus(menus);
            if (!this.modulos.contains(modulo)) {
                this.modulos.add(modulo);
            }
            
        } else {
            Menu menupai = (Menu) this.getSession().get(Menu.class, codigoPai);
            List<Menu> menus = menupai.getMenus();
            if (menus == null) {
                menus = new ArrayList<Menu>();
            }
            if (!menus.contains(menu)) {
                menus.add(menu);
            }
            menupai.setMenus(menus);
            
            addMenu(menupai);
        }
        
    }
    
    private void carregarProgramas(Collection<Programa> programas) {

        HQLHelper hql = new HQLHelper();
        hql.addToSelect("prog.codigo");
        hql.addToSelect("per.numeroAcesso");
        hql.addToFrom("ControlePermissaoGrupo cpg "
                + " join cpg.permissao per "
                + " join cpg.controleProgramaGrupo cProg "
                + " join cProg.programa prog"
                + " join cProg.grupo grup ");
        hql.addToFrom("UsuarioGrupo usuarioGrupo "
                + " join usuarioGrupo.grupo grupo2"
                + " join usuarioGrupo.usuario usuario2"
                + " ");

        hql.addToWhereWhithAnd("grup = grupo2 ");
//        hql.addToWhereWhithAnd("prog in ", programas);
        hql.addToWhereWhithAnd("usuario2 = ", this.usuario);

        Query query = this.getSession().createQuery(hql.getQuery());

        hql.applyRestrictions(query);


        List<Object[]> values = query.list();

        for (Programa programa : programas) {
            List<Integer> permissoes = new ArrayList<Integer>();
            for (Object[] objects : values) {
                if (programa.getCodigo().equals(objects[0])) {
                    permissoes.add((Integer) objects[1]);
                }
            }
            programa.setCodigoPermissoes(permissoes);
        }
    }

    private void ordenarModulos() {
        Collections.sort(this.getModulos(), new Comparator<Modulo>() {

            @Override
            public int compare(Modulo o1, Modulo o2) {
                return o1.getNome().compareTo(o2.getNome());
            }
        });
        for (Modulo modulo : getModulos()) {
            ordenarMenus(modulo.getMenus());
        }
    }
    
    private void ordenarMenus(List<Menu> menus) {
        if (menus == null) {
            return;
        }
        Collections.sort(menus, new Comparator<Menu>() {

            @Override
            public int compare(Menu o1, Menu o2) {
                int ord = Coalesce.asLong(o1.getSequencia()).compareTo(Coalesce.asLong(o2.getSequencia()));
                if (ord == 0) {
                    ord = o1.getImagem().compareTo(o2.getImagem());
                }
                if (ord == 0) {
                    ord = o1.getNome().compareTo(o2.getNome());
                }
                return ord;
            }
        });
        for (Menu menu : menus) {
            ordenarMenus(menu.getMenus());
            if (menu.getProgramas() != null) {
                Collections.sort(menu.getProgramas(), new Comparator<Programa>() {

                    @Override
                    public int compare(Programa o1, Programa o2) {
                        int ord = Coalesce.asString(o1.getSequencia()).compareTo(Coalesce.asString(o2.getSequencia()));
                        if (ord == 0) {
                            ord = o1.getImagem().compareTo(o2.getImagem());
                        }
                        if (ord == 0) {
                            ord = o1.getNome().compareTo(o2.getNome());
                        }
                        return ord;
                    }
                });
            }
        }
    }
    
}
