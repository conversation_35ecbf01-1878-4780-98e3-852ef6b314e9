package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;

/**
 *
 * <AUTHOR>
 */
public class QueryNaoComparecimentoAgendas extends CommandQuery<QueryNaoComparecimentoAgendas>{

    private HashSet<Long> codigos;
    
    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        Integer diasNaoComp = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("DiasParaNotificacaoNaoComparecimento");
        hql.setDistinct(true);
        hql.addToSelect("agah.codigo");
        hql.addToFrom("AgendaGradeAtendimentoHorario agah");
        hql.addToWhereWhithAnd("agah.status in ", Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_AGENDADO, AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU, AgendaGradeAtendimentoHorario.STATUS_REMANEJADO));
        hql.addToWhereWhithAnd("agah.dataAgendamento ", new DatePeriod(Data.removeDias(DataUtil.getDataAtual(), diasNaoComp), DataUtil.getDataAtual()));
        hql.addToWhereWhithAnd("agah.usuarioCadsus is not null");
        hql.addToWhereWhithAnd("agah.motivoNaoComparecimento is null");
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        codigos = new HashSet<Long>((Collection<? extends Long>) result);
    }

    public HashSet<Long> getCodigos() {
        return codigos;
    }
}
