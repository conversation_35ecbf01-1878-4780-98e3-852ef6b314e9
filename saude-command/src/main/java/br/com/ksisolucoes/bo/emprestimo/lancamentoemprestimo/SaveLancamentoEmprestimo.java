package br.com.ksisolucoes.bo.emprestimo.lancamentoemprestimo;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo;

/**
 *
 * <AUTHOR>
 */
public class SaveLancamentoEmprestimo extends SaveVO<LancamentoEmprestimo> {

    public SaveLancamentoEmprestimo(LancamentoEmprestimo vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataUsuario() == null) {
            this.vo.setDataUsuario(DataUtil.getDataAtual());
        }
        if (this.vo.getStatus() == null) {
            this.vo.setStatus(LancamentoEmprestimo.Status.PENDENTE.value());
        }
    }
}