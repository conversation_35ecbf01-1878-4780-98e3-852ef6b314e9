/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.bo.cadsus.usuariocadsusdocumento.ExcluirUsuarioCadsusDocumento;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ExcluirUsuarioCadsus extends AbstractCommandTransaction {

    private UsuarioCadsus usuarioCadsus;

    public ExcluirUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        usuarioCadsus = HibernateUtil.rechargeVO(UsuarioCadsus.class, usuarioCadsus.getCodigo(), usuarioCadsus.getVersion());
        List<UsuarioCadsusEndereco> usuarioCadsusEnderecoList = LoadManager.getInstance(UsuarioCadsusEndereco.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusEndereco.PROP_ID, UsuarioCadsusEnderecoPK.PROP_USUARIO_CADSUS), usuarioCadsus))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(usuarioCadsusEnderecoList)) {
            for (UsuarioCadsusEndereco usuarioCadsusEndereco : usuarioCadsusEnderecoList) {
                BOFactory.delete(usuarioCadsusEndereco);
            }
        }

        List<UsuarioCadsusHistorico> usuarioCadsusHistoricoList = LoadManager.getInstance(UsuarioCadsusHistorico.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusHistorico.PROP_ID, UsuarioCadsusHistoricoPK.PROP_USUARIO_CADSUS), usuarioCadsus))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(usuarioCadsusHistoricoList)) {
            for (UsuarioCadsusHistorico usuarioCadsusHistorico : usuarioCadsusHistoricoList) {
                BOFactory.delete(usuarioCadsusHistorico);
            }
        }

        List<UsuarioCadsusProntuario> usuarioCadsusProntuarioList = LoadManager.getInstance(UsuarioCadsusProntuario.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusProntuario.PROP_ID, UsuarioCadsusProntuarioPK.PROP_USUARIO_CADSUS), usuarioCadsus))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(usuarioCadsusProntuarioList)) {
            for (UsuarioCadsusProntuario usuarioCadsusProntuario : usuarioCadsusProntuarioList) {
                BOFactory.delete(usuarioCadsusProntuario);
            }
        }

        List<UsuarioCadsusCns> usuarioCadsusCnsList = LoadManager.getInstance(UsuarioCadsusCns.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS), usuarioCadsus))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(usuarioCadsusCnsList)) {
            for (UsuarioCadsusCns usuarioCadsusCns : usuarioCadsusCnsList) {
                BOFactory.delete(usuarioCadsusCns);
            }
        }

        UsuarioCadsusEsus usuarioCadsusEsus = LoadManager.getInstance(UsuarioCadsusEsus.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS), usuarioCadsus))
                .start().getVO();
        if (usuarioCadsusEsus != null) {
            BOFactory.delete(usuarioCadsusEsus);
        }

        new ExcluirUsuarioCadsusDocumento(usuarioCadsus).start();
        BOFactory.delete(usuarioCadsus);
    }

}
