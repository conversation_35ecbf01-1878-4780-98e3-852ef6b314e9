package br.com.ksisolucoes.bo.exames.importacao.validacao;

import br.com.celk.importacaoexamecovid.dto.ImportacaoExameCovidDTO;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAvaliacaoElegebilidadeAdmissao;
import br.com.ksisolucoes.vo.prontuario.basico.ExameExternoCovid;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExameImportacao;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;

import java.util.List;

import static ch.lambdaj.Lambda.on;

public class ValidacaoCsvImportacaoCovid {

    private ValidacaoCsvImportacaoCovid() {}

    public static void validarRegistro(ImportacaoExameCovidDTO importacaoExameCovidDTO, List<TipoExameImportacao> tipoExameImportacaos) throws ValidacaoException {
        executaValidacao(isNullOrBlank(importacaoExameCovidDTO.getNomePaciente()), "rotulo_csv_erro_nome_paciente");
        executaValidacao(importacaoExameCovidDTO.getDataNascimento() == null, "rotulo_csv_erro_data_nascimento_paciente");
        executaValidacao(isNullOrBlank(importacaoExameCovidDTO.getMunicipioPaciente()), "rotulo_csv_erro_municipio_paciente");
        executaValidacao(isSexoInvalido(importacaoExameCovidDTO), "rotulo_csv_erro_sexo_paciente");
        executaValidacao(isRacaCorInvalida(importacaoExameCovidDTO), "rotulo_csv_erro_cor_raca_paciente");
        executaValidacao(isNullOrBlank(importacaoExameCovidDTO.getTelefone()), "rotulo_csv_erro_telefone_paciente");
        executaValidacao(isNullOrBlank(importacaoExameCovidDTO.getDescricaoExame()), "rotulo_csv_erro_descricao_exame");
        executaValidacao(isDescricaoExameInvalida(importacaoExameCovidDTO, tipoExameImportacaos), "rotulo_csv_erro_descricao_exame_invalida");
        executaValidacao(isNullOrBlank(importacaoExameCovidDTO.getResultado()), "rotulo_csv_erro_resultado_paciente");
        executaValidacao(isResultadoExameInvalido(importacaoExameCovidDTO), "rotulo_csv_erro_resultado_invalido");
        executaValidacao(importacaoExameCovidDTO.getDataExame() == null, "rotulo_csv_erro_data_exame");
    }

    private static boolean isResultadoExameInvalido(ImportacaoExameCovidDTO importacaoExameCovidDTO) {
        for (ExameExternoCovid.ResultadoExameExternoCovid resultadoExameExternoCovid : ExameExternoCovid.ResultadoExameExternoCovid.values()) {
            if (resultadoExameExternoCovid.toString().equalsIgnoreCase(importacaoExameCovidDTO.getResultado())) {
                return false;
            }
        }
        return true;
    }

    private static boolean isRacaCorInvalida(ImportacaoExameCovidDTO importacaoExameCovidDTO) {
        if (isNullOrBlank(importacaoExameCovidDTO.getRacaCor())){
            return false;
        }

        for (EsusFichaAvaliacaoElegebilidadeAdmissao.RacaCor racaCor : EsusFichaAvaliacaoElegebilidadeAdmissao.RacaCor.values()) {
            if (racaCor.descricao().equalsIgnoreCase(importacaoExameCovidDTO.getRacaCor())) {
                return false;
            }
        }
        return true;
    }

    private static boolean isSexoInvalido(ImportacaoExameCovidDTO importacaoExameCovidDTO) {
        return isNotNullOrBlank(importacaoExameCovidDTO.getSexo())
                && !RepositoryComponentDefault.Sexo.MASCULINO.descricao().equalsIgnoreCase(importacaoExameCovidDTO.getSexo())
                && !RepositoryComponentDefault.Sexo.FEMININO.descricao().equalsIgnoreCase(importacaoExameCovidDTO.getSexo());
    }

    private static boolean isDescricaoExameInvalida(ImportacaoExameCovidDTO importacaoExameCovidDTO, List<TipoExameImportacao> tiposExameImportacao) {
        return !Lambda.exists(tiposExameImportacao, Lambda.having(on(TipoExameImportacao.class).getDescricaoExameSemFormatacao(), Matchers.equalTo(importacaoExameCovidDTO.getDescricaoExameSemFormatacao())));
    }

    private static boolean isNotNullOrBlank(String prop) {
        return !isNullOrBlank(prop);
    }

    private static boolean isNullOrBlank(String prop) {
        return prop == null || prop.trim().equals("");
    }

    private static void executaValidacao(boolean condicao, String mensagem) throws ValidacaoException {
        if (condicao) {
            throw new ValidacaoException(Bundle.getStringApplication(mensagem));
        }
    }

    public static void validarValoresLinha(String linha, String[] valores) throws ValidacaoException {
        final int QUANTIDADE_CAMPOS_IMPORTACAO_CSV = 15;
        final int QUANTIDADE_CAMPOS_REIMPORTACAO_CSV = 18;

        // VERIFICA SE ÚLTIMO CAMPOS ESTÁ VAZIO
        if (valores.length == QUANTIDADE_CAMPOS_IMPORTACAO_CSV - 1 && linha.endsWith(";")) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_csv_erro_formato_data"));
        }
        if (valores.length != QUANTIDADE_CAMPOS_IMPORTACAO_CSV && valores.length != QUANTIDADE_CAMPOS_REIMPORTACAO_CSV) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_csv_erro_quantidade_colunas_incorreta"));
        }
    }

    public static boolean isRegistroDuplicado(ImportacaoExameCovidDTO importacaoExameCovid, List<ExameExternoCovid> exameExternoCovidsCadastrados) {
        if (importacaoExameCovid.getCodigo() != null) {
            return false;
        }
        return Lambda.exists(
                exameExternoCovidsCadastrados,
                Lambda.having(on(ExameExternoCovid.class).getNomePacienteSemFormatacao(), Matchers.equalTo(importacaoExameCovid.getNomePacienteSemFormatacao()))
                    .and(Lambda.having(on(ExameExternoCovid.class).getDataNascimento().getTime(), Matchers.equalTo(importacaoExameCovid.getDataNascimento().getTime())))
                    .and(Lambda.having(on(ExameExternoCovid.class).getDataExame().getTime(), Matchers.equalTo(importacaoExameCovid.getDataExame().getTime())))
                    .and(Lambda.having(on(ExameExternoCovid.class).getDescricaoExameSemFormatacao(), Matchers.equalTo(importacaoExameCovid.getDescricaoExameSemFormatacao())))
                    .and(Lambda.having(on(ExameExternoCovid.class).getEmpresa(), Matchers.equalTo(importacaoExameCovid.getEmpresa())))
        );
    }
}
