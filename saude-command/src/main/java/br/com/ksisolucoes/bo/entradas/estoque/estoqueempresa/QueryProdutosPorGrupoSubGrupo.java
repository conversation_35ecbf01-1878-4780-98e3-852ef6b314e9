package br.com.ksisolucoes.bo.entradas.estoque.estoqueempresa;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryProdutosPorGrupoSubGrupo extends CommandQuery<QueryProdutosPorGrupoSubGrupo> {

    private Empresa empresa;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private List<Produto> result;

    public QueryProdutosPorGrupoSubGrupo(Empresa empresa, GrupoProduto grupoProduto, SubGrupo subGrupo) {
        this.empresa = empresa;
        this.grupoProduto = grupoProduto;
        this.subGrupo = subGrupo;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(Produto.class.getName());

        hql.addToSelect("produto.codigo", "codigo");
        hql.addToSelect("produto.referencia", "referencia");
        hql.addToSelect("produto.descricao", "descricao");
        hql.addToSelect("unidade.descricao", "unidade.descricao");
        hql.addToSelect("unidade.unidade", "unidade.unidade");

        hql.addToFrom("DominioProduto dominioProduto"
                + " left join dominioProduto.produto produto"
                + " left join dominioProduto.empresa empresa"
                + " left join dominioProduto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto"
                + " left join produto.unidade unidade");

        hql.addToWhereWhithAnd("grupoProduto = ", grupoProduto);
        hql.addToWhereWhithAnd("subGrupo = ", subGrupo);
        hql.addToWhereWhithAnd("empresa = ", empresa);

        hql.addToWhereWhithAnd("produto.flagAtivo = ", RepositoryComponentDefault.SIM_LONG);

        hql.addToOrder("produto.descricao asc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<Produto> getResult() {
        return result;
    }

}
