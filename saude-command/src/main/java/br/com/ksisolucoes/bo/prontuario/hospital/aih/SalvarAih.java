package br.com.ksisolucoes.bo.prontuario.hospital.aih;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import org.hibernate.criterion.Restrictions;

/**
 *
 * <AUTHOR>
 */
public class SalvarAih extends AbstractCommandTransaction<SalvarAih> {

    private Aih aih;
    private Aih aihExistente;
    private Atendimento atendimento;

    public SalvarAih(Aih aih, Atendimento atendimento) {
        this.aih = aih;
        this.atendimento = atendimento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (aih.getCodigo() != null) {
            aih = (Aih) getSession().get(Aih.class, aih.getCodigo());
        }
        ContaPaciente contaPaciente = BOFactory.getBO(HospitalFacade.class).encontrarContaPacienteUltimoCiclo(atendimento);

        if (contaPaciente != null) {
            if (existsAihContaPaciente(contaPaciente)) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_aih_este_ciclo"));
            }
        }
        if (aih != null) {
            aih.setAtendimento(aih.getAtendimento());
            aih.setProfissional(aih.getProfissional());
            aih.setNomeProfissionalSolicitante(aih.getNomeProfissionalSolicitante());
            aih.setTpDocProfSol(aih.getTpDocProfSol());
            aih.setNroDocProfSol(aih.getNroDocProfSol());
            aih.setUsuarioCadSus(aih.getUsuarioCadSus());
            aih.setStatus(aih.getStatus());
            aih.setClassificacaoRisco(aih.getClassificacaoRisco());

            aih.setUsuarioCadastro(aih.getUsuarioCadastro());
            aih.setDiagnosticoInicial(aih.getDiagnosticoInicial());
            aih.setProfissionalResponsavel(aih.getProfissionalResponsavel());

            aih.setDataSolicitacao(aih.getDataSolicitacao());

            aih.setContaPaciente(contaPaciente);
            aih.setPrincipaisSinaisSintomasClinicos(aih.getPrincipaisSinaisSintomasClinicos());
            aih.setCondicoesJustificamInternacao(aih.getCondicoesJustificamInternacao());
            aih.setPrincipaisResultadosProvasDiagnosticas(aih.getPrincipaisResultadosProvasDiagnosticas());

            aih.setCidPrincipal(aih.getCidPrincipal());
            aih.setCidSecundario(aih.getCidSecundario());
            aih.setCidCausaAssociada(aih.getCidCausaAssociada());

            aih.setProcedimentoSolicitado(aih.getProcedimentoSolicitado());
            aih.setClinica(aih.getClinica());
            aih.setCaraterInternacao(aih.getCaraterInternacao());
            aih.setTipoFinanciamento(aih.getTipoFinanciamento());
        }

        aih = BOFactory.save(aih);
        salvarAtendimentoProntuario();
    }

    private void salvarAtendimentoProntuario() throws DAOException, ValidacaoException {
        Atendimento atendimento = this.atendimento;
        AtendimentoProntuario atendimentoProntuario = (AtendimentoProntuario) getSession().createCriteria(AtendimentoProntuario.class)
                .add(Restrictions.eq(AtendimentoProntuario.PROP_AIH,
                        aih)).add(Restrictions.eq(AtendimentoProntuario.PROP_ATENDIMENTO, atendimento)).uniqueResult();

        if (atendimentoProntuario != null) {
            BOFactory.getBO(AtendimentoFacade.class).removerAtendimentoProntuario(AtendimentoProntuario.TipoRegistro.LAUDO_AIH.value(), atendimento, aih.getCodigo());
        }

        atendimentoProntuario = new AtendimentoProntuario();
        atendimentoProntuario.setAtendimento(atendimento);
        atendimentoProntuario.setUsuarioCadsus(atendimento.getUsuarioCadsus());
        atendimentoProntuario.setEmpresa(atendimento.getEmpresa());
        atendimentoProntuario.setProfissional(atendimento.getProfissional());
        atendimentoProntuario.setTipoRegistro(AtendimentoProntuario.TipoRegistro.LAUDO_AIH.value());
        atendimentoProntuario.setAih(aih);
        atendimentoProntuario.setDescricao(getDescricaoExameAih());
        atendimentoProntuario.setData(DataUtil.getDataAtual());
        atendimentoProntuario.setTabelaCbo(atendimento.getTabelaCbo());

        BOFactory.save(atendimentoProntuario);
    }

    private String getDescricaoExameAih() {
        StringBuilder descricao = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(getSessao().getUsuario().getFlagUsuarioTemporario())) {
            descricao.append("<b>Registrado por: </b>");
            descricao.append(getSessao().getUsuario().getNome());
            descricao.append("\n<br/>");
        }

        descricao.append("<u>Laudo AIH - ");
        descricao.append(aih.getProcedimentoSolicitado());
        descricao.append("</u>\n<br />Justificativa da Internação: ");
        descricao.append(aih.getCondicoesJustificamInternacao());
        descricao.append("\n<br />Principais Sinais e Sintomas Clínicos: ");
        descricao.append(aih.getPrincipaisSinaisSintomasClinicos());
        descricao.append("\n<br />Condições que Justificam a Internação: ");
        descricao.append(aih.getCondicoesJustificamInternacao());
        descricao.append("\n<br />Principais Resultados de Provas Diagnósticas (Resultado de Exames Realizados): ");
        descricao.append(aih.getPrincipaisResultadosProvasDiagnosticas());

        if (aih.getCidPrincipal() != null) {
            descricao.append("\n<br />C.I.D Principal: " + aih.getCidPrincipal().getDescricao());
        }

        if (aih.getCidSecundario() != null) {
            descricao.append("\n<br />C.I.D Secundário: " + aih.getCidSecundario().getDescricao());
        }

        descricao.append("\n<br />Clínica: ");
        descricao.append(verificaUtilizaTabelaCustomizada()
                            ? aih.getProcedimentoTipoLeitoCustom().getDescricaoTipoLeitoCustom()
                            : aih.getProcedimentoTipoLeito().getDescricao());

        descricao.append("\n<br />Caráter de Internação: ");
        descricao.append(aih.getDescricaoCaraterInternacao());


        return descricao.toString();
    }


    private boolean existsAihContaPaciente(ContaPaciente contaPaciente) {
        return LoadManager.getInstance(Aih.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Aih.PROP_CONTA_PACIENTE, contaPaciente))
                .addParameter(new QueryCustom.QueryCustomParameter(Aih.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, Aih.Status.CANCELADA.value()))
                .setId(aih.getCodigo())
                .exists();
    }

    private boolean verificaUtilizaTabelaCustomizada() {
        try {
            return BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("utilizaTabelaCustomizadaParaInformacaoDaClinica").equals(RepositoryComponentDefault.SIM);
        } catch (DAOException e) {
            Loggable.log.error(e);
            return false;
        }
    }

    public Aih getAih() {
        return aih;
    }
}
