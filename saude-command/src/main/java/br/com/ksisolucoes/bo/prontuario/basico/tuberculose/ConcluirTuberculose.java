package br.com.ksisolucoes.bo.prontuario.basico.tuberculose;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento;

/**
 * <AUTHOR>
 */
public class ConcluirTuberculose extends AbstractCommandTransaction {

    private TuberculoseAcompanhamento tuberculoseAcompanhamento;

    public ConcluirTuberculose(TuberculoseAcompanhamento tuberculoseAcompanhamento) {
        this.tuberculoseAcompanhamento = tuberculoseAcompanhamento;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        TuberculoseAcompanhamento tuberculoseForSave = (TuberculoseAcompanhamento) getSession().get(TuberculoseAcompanhamento.class, tuberculoseAcompanhamento.getCodigo());
        tuberculoseForSave.setMotivoEncerramento(tuberculoseAcompanhamento.getMotivoEncerramento());
        tuberculoseForSave.setDataEncerramento(tuberculoseAcompanhamento.getDataEncerramento());
        TuberculoseAcompanhamento acompanhamento = BOFactory.save(tuberculoseForSave);

        acompanhamento.getTuberculoseSintomatico().setSituacao(acompanhamento.getMotivoEncerramento());

        BOFactory.save(acompanhamento.getTuberculoseSintomatico());
    }
}