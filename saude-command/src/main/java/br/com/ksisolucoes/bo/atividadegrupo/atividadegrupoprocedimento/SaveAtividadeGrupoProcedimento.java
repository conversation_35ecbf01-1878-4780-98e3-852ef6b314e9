/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.atividadegrupo.atividadegrupoprocedimento;

import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProcedimento;

/**
 * <AUTHOR>
 */
public class SaveAtividadeGrupoProcedimento extends SaveVO<AtividadeGrupoProcedimento> {

    public SaveAtividadeGrupoProcedimento(AtividadeGrupoProcedimento vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
    }
}
