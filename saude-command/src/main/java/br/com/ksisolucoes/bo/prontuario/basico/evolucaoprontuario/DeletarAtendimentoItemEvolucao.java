package br.com.ksisolucoes.bo.prontuario.basico.evolucaoprontuario;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoItem;
import br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DeletarAtendimentoItemEvolucao extends AbstractCommandTransaction {
    private Long codigoAtendimento;
    private Long codigoProfissional;
    private EvolucaoProntuario evolucaoProntuario;
    
    public DeletarAtendimentoItemEvolucao(Long codigoAtendimento,Long codigoProfissional, EvolucaoProntuario evolucaoProntuario) {
        this.codigoAtendimento = codigoAtendimento;
        this.codigoProfissional = codigoProfissional;
        this.evolucaoProntuario = evolucaoProntuario;
    }
    
    @Override
    public void execute() throws DAOException, ValidacaoException {
        BOFactory.delete(this.evolucaoProntuario);
        
        List<AtendimentoItem> itensNoBanco = LoadManager.getInstance(AtendimentoItem.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoItem.PROP_TIPO_ORIGEM, AtendimentoItem.TIPO_EVOLUCAO_PRONTUARIO_PROCEDIMENTO))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoItem.PROP_PROFISSIONAL, codigoProfissional))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoItem.PROP_PROFISSIONAL_APLICACAO, codigoProfissional))
                    .addParameter(new QueryCustom.QueryCustomParameter(AtendimentoItem.PROP_ATENDIMENTO, codigoAtendimento))
                    .start().getList();
        for (AtendimentoItem atendimentoItem : itensNoBanco) {
            BOFactory.delete(atendimentoItem);
        }
    }
}
