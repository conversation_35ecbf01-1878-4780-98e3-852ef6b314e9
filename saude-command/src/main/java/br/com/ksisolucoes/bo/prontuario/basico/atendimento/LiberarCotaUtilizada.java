package br.com.ksisolucoes.bo.prontuario.basico.atendimento;

import br.com.celk.laboratorio.exames.dto.ProcedimentoHelper;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;

import java.util.Optional;

public class LiberarCotaUtilizada {

    private LiberarCotaUtilizada() {
    }

    public static ExamePrestadorCompetencia liberarCotaUtilizada(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, 
                                                                 Empresa unidadeExecutante, 
                                                                 TipoProcedimento tipoProcedimento) throws ValidacaoException, DAOException {
        ExamePrestadorCompetencia examePrestadorCompetencia = findExamePrestadorCompetencia(agendaGradeAtendimentoHorario);
        return examePrestadorCompetencia != null ? calcularCotaUtilizada(agendaGradeAtendimentoHorario, examePrestadorCompetencia, unidadeExecutante, tipoProcedimento) : null;
    }

    private static ExamePrestadorCompetencia findExamePrestadorCompetencia(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) throws DAOException {
        return agendaGradeAtendimentoHorario.getExamePrestadorCompetencia() != null ? HibernateUtil.lockTable(ExamePrestadorCompetencia.class, agendaGradeAtendimentoHorario.getExamePrestadorCompetencia().getCodigo()) : null;
    }

    private static ExamePrestadorCompetencia calcularCotaUtilizada(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, ExamePrestadorCompetencia competencia, Empresa unidadeExecutante, TipoProcedimento tipoProcedimento) throws ValidacaoException {
        Double novoValorCotaUtilizado = calcularTetoFinanceiroRealizado(agendaGradeAtendimentoHorario, competencia);
        Double novoValorRecursoProprioUtilizado = calcularTetoRecursoProprioUtilizado(competencia, unidadeExecutante, tipoProcedimento);
        competencia.setTetoFinanceiroRealizado(novoValorCotaUtilizado);
        competencia.setTetoRecursoProprioRealizado(novoValorRecursoProprioUtilizado);
        return competencia;
    }

    private static Double calcularTetoFinanceiroRealizado(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario, ExamePrestadorCompetencia competencia){
        Double valorCotaUtilizado = Optional.ofNullable(agendaGradeAtendimentoHorario.getValorCotaUtilizado()).orElse(0.0);
        Double novoValorCotaUtilizado = new Dinheiro(competencia.getTetoFinanceiroRealizado()).subtrair(valorCotaUtilizado).doubleValue();
        return novoValorCotaUtilizado < 0 ? Double.valueOf(0) : novoValorCotaUtilizado;
    }

    private static Double calcularTetoRecursoProprioUtilizado(ExamePrestadorCompetencia competencia, Empresa unidadeExecutante, TipoProcedimento tipoProcedimento) throws ValidacaoException {
        if (competencia.getTetoRecursoProprio() != null && competencia.getTetoRecursoProprio() > 0) {
            return realizarCalculo(competencia, unidadeExecutante, tipoProcedimento);
        }
        return (double) 0;
    }

    private static Double realizarCalculo(ExamePrestadorCompetencia competencia, Empresa unidadeExecutante, TipoProcedimento tipoProcedimento) throws ValidacaoException {
        Double valorComplementar = ProcedimentoHelper.getRecursoProprioProcedimento(tipoProcedimento.getExameProcedimento(), unidadeExecutante);
        Double novoValorRecursoProprioUtilizado = new Dinheiro(Coalesce.asDouble(competencia.getTetoRecursoProprioRealizado())).subtrair(valorComplementar).doubleValue();
        return novoValorRecursoProprioUtilizado < 0 ? Double.valueOf(0) : novoValorRecursoProprioUtilizado;
    }

}
