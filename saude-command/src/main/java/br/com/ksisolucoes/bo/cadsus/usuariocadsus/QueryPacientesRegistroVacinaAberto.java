package br.com.ksisolucoes.bo.cadsus.usuariocadsus;

import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.RegistroVacina;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public class QueryPacientesRegistroVacinaAberto extends AbstractCommandTransaction{

    private Set<Long> codigos;

    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        List<Long> codigosPaciente = getSession().
                getNamedQuery("registroVacinasAberto")
                .setParameterList("situacoes", RegistroVacina.Situacao.valueList())
                .list();
        codigos = new HashSet<Long>(codigosPaciente);
    }

    public Set<Long> getCodigos() {
        return codigos;
    }
    
}
