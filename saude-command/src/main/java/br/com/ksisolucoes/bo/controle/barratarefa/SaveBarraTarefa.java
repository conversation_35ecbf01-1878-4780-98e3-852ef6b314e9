/*
 * Created on 26/08/2004
 *
 */
package br.com.ksisolucoes.bo.controle.barratarefa;

import br.com.ksisolucoes.bo.command.SaveVO;


/**
 * <AUTHOR>
 *
 */
public class SaveBarraTarefa extends SaveVO{

    /**
     * 
     */
    public SaveBarraTarefa(Object vo) {
        super( vo );
    }
    
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.command.AbstractCommand#getInterfaceChave()
     */
    
}