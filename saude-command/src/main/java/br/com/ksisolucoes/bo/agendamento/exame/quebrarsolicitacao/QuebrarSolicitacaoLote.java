package br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao;

import br.com.celk.agendamento.FiltrarAgendasParametroUnidadeOrigemESolicitante;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.AgendaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.GradeDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import ch.lambdaj.group.Group;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.util.CollectionUtils.isNotNullEmpty;
import static br.com.celk.util.DataUtil.zerarHora;
import static br.com.ksisolucoes.bo.agendamento.exame.CalcularHorariosSequenciaAgenda.quantidadeHorariosEmSequenciaMax;
import static br.com.ksisolucoes.bo.agendamento.exame.CalcularTipoAgenda.isTipoAgendaDiaria;
import static br.com.ksisolucoes.bo.agendamento.exame.quebrarsolicitacao.FiltrarExamesAtendidosQuebraSolicitacao.examesAtendidos;
import static ch.lambdaj.Lambda.*;
import static ch.lambdaj.group.Groups.by;

public class QuebrarSolicitacaoLote {

    private QuebrarSolicitacaoLote() {
    }

    public static List<SolicitacaoAgendamento> quebrarSolicitacao(SolicitacaoAgendamento solicitacaoAgendamento,
                                                                  List<AgendaDTO> agendasComExamesDaSolicitacao,
                                                                  List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames,
                                                                  Long tipoTeto,
                                                                  TipoProcedimento tipoProcedimento,
                                                                  List<ExamePrestadorProcedimento> examesPrestadorProcedimentos) throws ValidacaoException, DAOException {
        List<SolicitacaoAgendamento> solicitacoesAgendamento = new ArrayList<>();
        for (AgendaDTO agendaDTO : agendasComExamesDaSolicitacao) {
            solicitacoesAgendamento.addAll(filtrarEQuebrarSolicitacoes(agendaDTO, solicitacaoAgendamento, solicitacaoAgendamentoExames, tipoTeto, tipoProcedimento, examesPrestadorProcedimentos));
        }
        if (isNotNullEmpty(solicitacoesAgendamento)) {
            solicitacaoAgendamento.setSolicitacaoQuebrada(true);
            solicitacoesAgendamento.add(solicitacaoAgendamento);
        }
        return solicitacoesAgendamento;
    }

    private static List<SolicitacaoAgendamento> filtrarEQuebrarSolicitacoes(AgendaDTO agendaDTO,
                                                                            SolicitacaoAgendamento solicitacaoAgendamento,
                                                                            List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames,
                                                                            Long tipoTeto,
                                                                            TipoProcedimento tipoProcedimento,
                                                                            List<ExamePrestadorProcedimento> examesPrestadorProcedimentos) throws ValidacaoException, DAOException {
        List<SolicitacaoAgendamento> solicitacoesGeradas = new ArrayList<>();
        List<Date> datasAgendadas = new ArrayList<>();
        for (GradeDTO gradeDTO : agendaDTO.getGradesComVagasDisponiveis()) {
            List<SolicitacaoAgendamentoExame> exames =
                    examesAtendidos(gradeDTO, agendaDTO.getExamesPrestadorRealiza(), solicitacaoAgendamentoExames, agendaDTO.getExamePrestadorCompetencia(), tipoTeto, tipoProcedimento);
            List<SolicitacaoAgendamentoExame> examesAtendidosNaSolicitacao = filtrarExamesUnidadeOrigemESolicitacao(solicitacaoAgendamento, examesPrestadorProcedimentos, exames, agendaDTO.getCodigoUnidade());

            if (podeRealizarQuebra(datasAgendadas, gradeDTO, examesAtendidosNaSolicitacao, solicitacaoAgendamentoExames)) {
                final long procedimentosPorVaga = getProcedimentosPorVaga(solicitacaoAgendamentoExames, tipoProcedimento);
                final int maxHorariosEmSequencia = calcularVagasDisponiveisGrade(gradeDTO, agendaDTO.getExamePrestadorCompetencia().getEmpresa(), tipoProcedimento);
                final List<SolicitacaoAgendamentoExame> examesAtendidosPorVagasDisponiveis = getMaxExamesAtendidos(examesAtendidosNaSolicitacao, maxHorariosEmSequencia, (int) procedimentosPorVaga);
                if (isNotNullEmpty(examesAtendidosPorVagasDisponiveis) && (examesAtendidosPorVagasDisponiveis.size() < solicitacaoAgendamentoExames.size())) {
                    SolicitacaoAgendamento solicitacaoAgendamentoGerada = criarNovaSolicitacaoAgendamento(solicitacaoAgendamento, examesAtendidosPorVagasDisponiveis);
                    solicitacaoAgendamentoGerada.setSolicitacaoQuebrada(true);
                    solicitacaoAgendamentoExames.removeAll(examesAtendidosPorVagasDisponiveis);
                    solicitacoesGeradas.add(solicitacaoAgendamentoGerada);
                    datasAgendadas.add(zerarHora(gradeDTO.getData()));
                }
            }
            if (solicitacaoAgendamentoExames.size() <= 1) {
                return solicitacoesGeradas;
            }
        }
        return solicitacoesGeradas;
    }

    private static List<SolicitacaoAgendamentoExame> filtrarExamesUnidadeOrigemESolicitacao(SolicitacaoAgendamento solicitacaoAgendamento,
                                                                                            List<ExamePrestadorProcedimento> examesPrestadorProcedimentos,
                                                                                            List<SolicitacaoAgendamentoExame> examesSolicitacao,
                                                                                            Long codigoUnidadeAgenda) throws DAOException {
        boolean utilizaRegraUnidadeOrigemESolicitante = FiltrarAgendasParametroUnidadeOrigemESolicitante.utilizaRegraUnidadeOrigemESolicitante();
        if (utilizaRegraUnidadeOrigemESolicitante) {
            if (isUnidadeOrigem(solicitacaoAgendamento, codigoUnidadeAgenda) && unidadeOrigemAtendeTodosExames(solicitacaoAgendamento, examesPrestadorProcedimentos, examesSolicitacao)) {
                return examesSolicitacao;
            }

            List<ExamePrestadorProcedimento> examesUnidadeOrigemRealiza = FiltrarAgendasParametroUnidadeOrigemESolicitante.getExamesPrestadorRealiza(solicitacaoAgendamento.getUnidadeOrigem(), examesPrestadorProcedimentos);
            List<Long> examesUnidadeOrigemRealizaIds = extract(examesUnidadeOrigemRealiza, on(ExamePrestadorProcedimento.class).getExameProcedimento().getCodigo());

            List<ExamePrestadorProcedimento> examesUnidadeSolicRealiza = FiltrarAgendasParametroUnidadeOrigemESolicitante.getExamesPrestadorRealiza(solicitacaoAgendamento.getEmpresa(), examesPrestadorProcedimentos);
            List<Long> examesUnidadeSolicRealizaIds = extract(examesUnidadeSolicRealiza, on(ExamePrestadorProcedimento.class).getExameProcedimento().getCodigo());
            if (isUnidadeOrigem(solicitacaoAgendamento, codigoUnidadeAgenda)) {
                return getExamesUnidadeOrigem(examesSolicitacao, examesUnidadeOrigemRealizaIds);
            } else if (isUnidadeSolicitante(solicitacaoAgendamento, codigoUnidadeAgenda)) {
                return getExamesUnidadeSolicitante(examesSolicitacao, examesUnidadeOrigemRealizaIds, examesUnidadeSolicRealizaIds);
            } else {
                return getExamesOutrasUnidades(examesSolicitacao, examesUnidadeOrigemRealizaIds, examesUnidadeSolicRealizaIds);
            }
        }
        return examesSolicitacao;
    }

    private static List<SolicitacaoAgendamentoExame> getExamesOutrasUnidades(List<SolicitacaoAgendamentoExame> examesSolicitacao, List<Long> examesUnidadeOrigemRealizaIds, List<Long> examesUnidadeSolicRealizaIds) {
        List<SolicitacaoAgendamentoExame> examesRetorno = new ArrayList<>();
        for (SolicitacaoAgendamentoExame solicExame : examesSolicitacao) {
            if (!examesUnidadeSolicRealizaIds.contains(solicExame.getExameProcedimento().getCodigo()) && !examesUnidadeOrigemRealizaIds.contains(solicExame.getExameProcedimento().getCodigo())) {
                examesRetorno.add(solicExame);
            }
        }
        return examesRetorno;
    }

    private static List<SolicitacaoAgendamentoExame> getExamesUnidadeSolicitante(List<SolicitacaoAgendamentoExame> examesSolicitacao, List<Long> examesUnidadeOrigemRealizaIds, List<Long> examesUnidadeSolicRealizaIds) {
        List<SolicitacaoAgendamentoExame> examesRetorno = new ArrayList<>();
        for (SolicitacaoAgendamentoExame solicExame : examesSolicitacao) {
            if (examesUnidadeSolicRealizaIds.contains(solicExame.getExameProcedimento().getCodigo()) && !examesUnidadeOrigemRealizaIds.contains(solicExame.getExameProcedimento().getCodigo())) {
                examesRetorno.add(solicExame);
            }
        }
        return examesRetorno;
    }

    private static List<SolicitacaoAgendamentoExame> getExamesUnidadeOrigem(List<SolicitacaoAgendamentoExame> examesSolicitacao, List<Long> examesUnidadeOrigemRealizaIds) {
        List<SolicitacaoAgendamentoExame> examesRetorno = new ArrayList<>();
        for (SolicitacaoAgendamentoExame solicExame : examesSolicitacao) {
            if (examesUnidadeOrigemRealizaIds.contains(solicExame.getExameProcedimento().getCodigo())) {
                examesRetorno.add(solicExame);
            }
        }
        return examesRetorno;
    }

    private static boolean isUnidadeSolicitante(SolicitacaoAgendamento solicitacaoAgendamento, Long codigoUnidadeAgenda) {
        return codigoUnidadeAgenda.equals(solicitacaoAgendamento.getEmpresa().getCodigo());
    }

    private static boolean isUnidadeOrigem(SolicitacaoAgendamento solicitacaoAgendamento, Long codigoUnidadeAgenda) {
        return solicitacaoAgendamento.getUnidadeOrigem() != null && codigoUnidadeAgenda.equals(solicitacaoAgendamento.getUnidadeOrigem().getCodigo());
    }

    private static boolean unidadeOrigemAtendeTodosExames(SolicitacaoAgendamento solicitacaoAgendamento, List<ExamePrestadorProcedimento> examesPrestadorProcedimentos, List<SolicitacaoAgendamentoExame> examesSolicitacao) {
        return solicitacaoAgendamento.getUnidadeOrigem() != null && FiltrarAgendasParametroUnidadeOrigemESolicitante.unidadeExecutaTodosExames(solicitacaoAgendamento.getUnidadeOrigem(), examesSolicitacao, examesPrestadorProcedimentos);
    }

    private static boolean podeRealizarQuebra(List<Date> datasAgendadas,
                                              GradeDTO gradeDTO,
                                              List<SolicitacaoAgendamentoExame> examesAtendidosNaSolicitacao,
                                              List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames) {
        Group<SolicitacaoAgendamentoExame> examesDaSolicitacaoDistinctPorProcedimento =
                group(solicitacaoAgendamentoExames, by(on(SolicitacaoAgendamentoExame.class).getExameProcedimento().getProcedimento().getCodigo()));
        return isNotNullEmpty(examesAtendidosNaSolicitacao)
                && naoExisteQuebraAnteriorParaDiaGrade(datasAgendadas, gradeDTO)
                && examesDaSolicitacaoDistinctPorProcedimento.subgroups().size() > 1;
    }

    private static boolean naoExisteQuebraAnteriorParaDiaGrade(List<Date> datasAgendadas, GradeDTO gradeDTO) {
        return !datasAgendadas.contains(zerarHora(gradeDTO.getData()));
    }

    private static long getProcedimentosPorVaga(List<SolicitacaoAgendamentoExame> solicitacaoAgendamentoExames, TipoProcedimento tipoProcedimento) {
        return tipoProcedimento.getProcedimentoVaga() == 0L ? solicitacaoAgendamentoExames.size() : tipoProcedimento.getProcedimentoVaga();
    }

    private static List<SolicitacaoAgendamentoExame> getMaxExamesAtendidos(List<SolicitacaoAgendamentoExame> examesAtendidosNaSolicitacao,
                                                                           int maxHorariosEmSequencia,
                                                                           int procedimentosPorVaga) {
        Group<SolicitacaoAgendamentoExame> examesDaSolicitacaoDistinctPorProcedimento =
                group(examesAtendidosNaSolicitacao, by(on(SolicitacaoAgendamentoExame.class).getExameProcedimento().getProcedimento().getCodigo()));
        int maximoExamesAtendidos = maxHorariosEmSequencia * procedimentosPorVaga;
        if (maximoExamesAtendidos >= examesDaSolicitacaoDistinctPorProcedimento.findAll().size()) {
            return examesDaSolicitacaoDistinctPorProcedimento.findAll();
        }
        List<SolicitacaoAgendamentoExame> examesAtendidos = new ArrayList<>();
        for (Group<SolicitacaoAgendamentoExame> exameGroup : examesDaSolicitacaoDistinctPorProcedimento.subgroups()) {
            if (exameGroup.findAll().size() + examesAtendidos.size() <= maximoExamesAtendidos) {
                examesAtendidos.addAll(exameGroup.findAll());
            }
        }
        return examesAtendidos;
    }

    private static int calcularVagasDisponiveisGrade(GradeDTO gradeDTO,
                                                     Empresa empresa,
                                                     TipoProcedimento tipoProcedimento) throws ValidacaoException {
        Agenda agenda = new Agenda();
        agenda.setEmpresa(empresa);
        agenda.setTipoProcedimento(tipoProcedimento);
        return isTipoAgendaDiaria(agenda) ? gradeDTO.getQuantidadeVagasDisponivel() : quantidadeHorariosEmSequenciaMax(gradeDTO.getHorarios());
    }

    private static SolicitacaoAgendamento criarNovaSolicitacaoAgendamento(SolicitacaoAgendamento solicitacaoAgendamento,
                                                                          List<SolicitacaoAgendamentoExame> examesAtendidosNaSolicitacao) throws DAOException, ValidacaoException {
        return BOFactory.getBO(AgendamentoFacade.class).gerarSolicitacaoAgendamento(solicitacaoAgendamento, examesAtendidosNaSolicitacao);
    }
}
