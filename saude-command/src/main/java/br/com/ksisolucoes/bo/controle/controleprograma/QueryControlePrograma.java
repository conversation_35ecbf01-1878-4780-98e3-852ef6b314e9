package br.com.ksisolucoes.bo.controle.controleprograma;

import java.io.Serializable;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.Coalesce;

public class QueryControlePrograma extends CommandQuery<QueryControlePrograma> implements Serializable {
    
    private boolean acessoPermitido = false;
    
    private Long codigoUsuario;
    private Long codigoPrograma;
    
    public QueryControlePrograma( Long codigoUsuario, Long codigoPrograma) {
        this.codigoUsuario = codigoUsuario;
        this.codigoPrograma = codigoPrograma;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("count(*)");
        
        hql.addToFrom("ControleProgramaGrupo c");
        hql.addToWhereWhithAnd(" c.programa.codigo = ", codigoPrograma);
        hql.addToWhereWhithAnd(" c.grupo.codigo in (select ug.grupo.codigo from UsuarioGrupo ug where ug.usuario.codigo = "+codigoUsuario+")");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        Long count = (Long) result;
        acessoPermitido = Coalesce.asLong(count)>0;
    }
    
    public boolean isAcessoPermitido() {
        return this.acessoPermitido;
    }
    
}
