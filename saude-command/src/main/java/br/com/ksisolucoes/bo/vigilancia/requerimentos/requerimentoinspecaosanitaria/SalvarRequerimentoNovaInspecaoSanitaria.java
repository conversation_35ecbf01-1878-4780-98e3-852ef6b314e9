package br.com.ksisolucoes.bo.vigilancia.requerimentos.requerimentoinspecaosanitaria;

import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaSolicitacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.RequerimentoNovaInspecaoSanitariaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

public class SalvarRequerimentoNovaInspecaoSanitaria extends AbstractCommandTransaction<SalvarRequerimentoNovaInspecaoSanitaria> {

    private final RequerimentoNovaInspecaoSanitariaDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;
    private boolean gerarOcorrenciaCadastro = false;

    public SalvarRequerimentoNovaInspecaoSanitaria(RequerimentoNovaInspecaoSanitariaDTO dto) {
        this.dto = dto;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria = dto.getRequerimentoInspecaoSanitaria();

        if (requerimentoInspecaoSanitaria.getRequerimentoVigilancia().getCodigo() == null) {
            dto.getRequerimentoInspecaoSanitaria().getRequerimentoVigilancia().setEstabelecimento(dto.getRequerimentoInspecaoSanitaria().getEstabelecimento());
            dto.getRequerimentoInspecaoSanitaria().getRequerimentoVigilancia().setTipoDocumento(dto.getTipoSolicitacao().getTipoDocumento());
            dto.getRequerimentoInspecaoSanitaria().getRequerimentoVigilancia().setSituacao(RequerimentoVigilancia.Situacao.PENDENTE.value());
            gerarOcorrenciaCadastro = true;
        }
        requerimentoVigilancia = BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilancia(new RequerimentoVigilanciaSolicitacaoDTO(dto.getRequerimentoInspecaoSanitaria().getRequerimentoVigilancia()));

        requerimentoVigilancia = VigilanciaHelper.atualizarGestaoRequerimento(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), gerarOcorrenciaCadastro);

        if(gerarOcorrenciaCadastro){
            BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(Bundle.getStringApplication("msg_requerimento_cadastrado"), requerimentoVigilancia, null);
        }

        requerimentoInspecaoSanitaria.setDataInspecao(dto.getDataInspecao());
        dto.getRequerimentoInspecaoSanitaria().setRequerimentoVigilancia(requerimentoVigilancia);
        BOFactory.save(requerimentoInspecaoSanitaria);

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaAnexo(requerimentoVigilancia, dto.getRequerimentoVigilanciaAnexoDTOList(), dto.getRequerimentoVigilanciaAnexoExcluidoDTOList(), false);
        BOFactory.getBO(VigilanciaFacade.class).salvarEloRequerimentoVigilanciaSetorVigilancia(requerimentoVigilancia, dto.getEloRequerimentoVigilanciaSetorVigilanciaList(), dto.getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoVigilanciaFiscais(requerimentoVigilancia, dto.getRequerimentoVigilanciaFiscalList(), dto.getRequerimentoVigilanciaFiscalListExcluir());

        if(gerarOcorrenciaCadastro) {
            BOFactory.getBO(VigilanciaFacade.class).enviarEmailNovoRequerimentoVigilancia(this.requerimentoVigilancia);
        }

        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaProdutos(requerimentoInspecaoSanitaria, dto.getProdutos(), dto.getProdutosExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaRelacaoProdutos(requerimentoInspecaoSanitaria, dto.getRelacaoProdutos(), dto.getRelacaoProdutosExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaFuncionariosResponsaveis(requerimentoInspecaoSanitaria, dto.getFuncionariosResponsaveis(), dto.getFuncionariosResponsaveisExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaFuncionariosEnvolvidos(requerimentoInspecaoSanitaria, dto.getFuncionariosEnvolvidos(), dto.getFuncionariosEnvolvidosExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaAresFisicas(requerimentoInspecaoSanitaria, dto.getAreasFisicas(), dto.getAreasFisicasExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaPrestadores(requerimentoInspecaoSanitaria, dto.getPrestadores(), dto.getPrestadoresExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaTransportadoras(requerimentoInspecaoSanitaria, dto.getTransportadoras(), dto.getTransportadorasExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaFornecedores(requerimentoInspecaoSanitaria, dto.getFornecedores(), dto.getFornecedoresExcluir());
        BOFactory.getBO(VigilanciaFacade.class).salvarRequerimentoInspecaoSanitariaContatos(requerimentoInspecaoSanitaria, dto.getContatos(), dto.getContatosExcluir());

    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }
}