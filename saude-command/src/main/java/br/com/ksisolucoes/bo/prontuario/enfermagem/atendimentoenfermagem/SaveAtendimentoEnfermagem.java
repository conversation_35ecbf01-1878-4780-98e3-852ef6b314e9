package br.com.ksisolucoes.bo.prontuario.enfermagem.atendimentoenfermagem;

import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagem;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.SaveVO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

public class SaveAtendimentoEnfermagem extends SaveVO<AtendimentoEnfermagem> {

    public SaveAtendimentoEnfermagem(AtendimentoEnfermagem vo) {
        super(vo);
    }

    @Override
    protected void antesSave() throws ValidacaoException, DAOException {
        if (this.vo.getUsuario() == null) {
            this.vo.setUsuario(getSessao().<Usuario>getUsuario());
        }
        if (this.vo.getDataCadastro() == null) {
            this.vo.setDataCadastro(DataUtil.getDataAtual());
        }
        if (this.vo.getSituacao() == null) {
            this.vo.setSituacao(AtendimentoEnfermagem.Situacao.PENDENTE.value());
        }
    }
}
