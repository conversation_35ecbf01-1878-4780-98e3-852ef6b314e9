package br.com.ksisolucoes.bo.vigilancia.autodepenalidade.autopenalidade;

import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidadeFiscal;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidadeInfracao;
import br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidadeItem;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarAutoPenalidade extends AbstractCommandTransaction {

    private AutoPenalidade autoPenalidade;
    private List<AutoPenalidadeFiscal> lstFiscal;
    private List<AutoPenalidadeItem> lstItem;
    private List<AutoPenalidadeInfracao> lstInfracao;
    private ConfiguracaoVigilancia configuracaoVigilancia;

    public SalvarAutoPenalidade(AutoPenalidade autoPenalidade, List<AutoPenalidadeFiscal> lstFiscal, List<AutoPenalidadeItem> lstItem, List<AutoPenalidadeInfracao> lstInfracao) {
        this.autoPenalidade = autoPenalidade;
        this.lstFiscal = lstFiscal;
        this.lstItem = lstItem;
        this.lstInfracao = lstInfracao;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        boolean cadastro = false;
        if(autoPenalidade.getCodigo() == null){
            cadastro = true;
        }

        carregarConfiguracaoVigilancia();
        if (configuracaoVigilancia == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
        }
        configurarNumeracao();

        autoPenalidade = BOFactory.save(autoPenalidade);

        for (AutoPenalidadeFiscal item : lstFiscal) {
            item.setAutoPenalidade(autoPenalidade);
        }
        VOUtils.persistirListaVosModificados(AutoPenalidadeFiscal.class, lstFiscal, new QueryCustom.QueryCustomParameter(AutoPenalidadeFiscal.PROP_AUTO_PENALIDADE, autoPenalidade));

        for (AutoPenalidadeItem item : lstItem) {
            item.setAutoPenalidade(autoPenalidade);
        }
        VOUtils.persistirListaVosModificados(AutoPenalidadeItem.class, lstItem, new QueryCustom.QueryCustomParameter(AutoPenalidadeItem.PROP_AUTO_PENALIDADE, autoPenalidade));

        for (AutoPenalidadeInfracao infracao : lstInfracao) {
            infracao.setAutoPenalidade(autoPenalidade);
        }
        VOUtils.persistirListaVosModificados(AutoPenalidadeInfracao.class, lstInfracao, new QueryCustom.QueryCustomParameter(AutoPenalidadeInfracao.PROP_AUTO_PENALIDADE, autoPenalidade));

        if(cadastro){
            if(autoPenalidade.getRequerimentoVigilancia() != null && autoPenalidade.getRequerimentoVigilancia().getCodigo() != null) {
                gerarOcorrenciaRequerimentoVigilancia(autoPenalidade, autoPenalidade.getRequerimentoVigilancia());
            }
            BOFactory.getBO(VigilanciaFacade.class).gerarFaturamentoAutoPenalidade(autoPenalidade.getDataPenalidade(), autoPenalidade);
            gerarOcorrenciaProcessoAdministrativoPenalidadeCadastrada(autoPenalidade);
            if(autoPenalidade.getProcessoAdministrativoAutenticacao() == null){
                gerarChaveProcesso();
                BOFactory.save(autoPenalidade);
            }
        }
    }

    private void gerarOcorrenciaProcessoAdministrativoPenalidadeCadastrada(AutoPenalidade autoPenalidade) throws ValidacaoException, DAOException {
        ProcessoAdministrativoOcorrencia ocorrencia = new ProcessoAdministrativoOcorrencia();
        ocorrencia.setProcessoAdministrativo(autoPenalidade.getProcessoAdministrativo());
        ocorrencia.setDataOcorrencia(DataUtil.getDataAtual());
        ocorrencia.setDescricao(Bundle.getStringApplication("autoImposicaoPenalidadeCadastradoNAutoX", autoPenalidade.getNumeroFormatado()));
        ocorrencia.setUsuario(getSessao().getUsuario());
        ocorrencia.setTipo(ProcessoAdministrativoOcorrencia.Tipo.AUTO_PENALIDADE.value());
        ocorrencia.setDocumento(autoPenalidade.getCodigo());
        BOFactory.save(ocorrencia);
    }

    public AutoPenalidade getAutoPenalidade() {
        return autoPenalidade;
    }

    private void configurarNumeracao() throws ValidacaoException, DAOException {
        if(autoPenalidade.getNumero() == null && autoPenalidade.getCodigo() == null) {
            if (configuracaoVigilancia.getAnoBaseGeral() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_ano_base_configuracao_vigilancia"));
            }
            if (configuracaoVigilancia.getNumAutoPenalidade() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_numeracao_base_auto_penalidade"));
            }

            long sequencial = 0L;
            if (configuracaoVigilancia != null && Coalesce.asLong(configuracaoVigilancia.getNumAutoPenalidade()) > 0L) {
                sequencial = configuracaoVigilancia.getNumAutoPenalidade();
            }
            sequencial++;
            String montaId = String.valueOf(sequencial).concat(String.valueOf(configuracaoVigilancia.getAnoBaseGeral()));
            Long nextId = Long.valueOf(StringUtil.getDigits(montaId));
            autoPenalidade.setNumero(nextId);

            configuracaoVigilancia.setNumAutoPenalidade(sequencial);

            BOFactory.save(configuracaoVigilancia);
        }
    }

    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }

    private void gerarChaveProcesso() throws ValidacaoException, DAOException {
        if(this.autoPenalidade.getProcessoAdministrativo() != null){
            ProcessoAdministrativo processoAdministrativo = (ProcessoAdministrativo) this.getSession().get(ProcessoAdministrativo.class, this.autoPenalidade.getProcessoAdministrativo().getCodigo());
            if(processoAdministrativo.getAutoInfracao() != null) {
                this.autoPenalidade.setProcessoAdministrativoAutenticacao(processoAdministrativo.getAutoInfracao().getProcessoAdministrativoAutenticacao());
            }
        }
    }

    private void gerarOcorrenciaRequerimentoVigilancia(AutoPenalidade autoPenalidade, RequerimentoVigilancia requerimentoVigilancia) throws ValidacaoException, DAOException {
        String mensagemOcorrencia = "Registro do Auto de Penalidade Nº " + autoPenalidade.getNumeroFormatado();
        BOFactory.getBO(VigilanciaFacade.class).cadastrarOcorrenciaRequerimentoVigilancia(mensagemOcorrencia, requerimentoVigilancia, null);
    }
}
