package br.com.ksisolucoes.bo.vigilancia.agravo.registroAgravoTuberculose;

import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoTuberculoseDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.agravo.helper.FichaInvestigacaoHelper;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTuberculose;

public class SalvarFichaInvestigacaoAgravoTuberculose extends AbstractCommandTransaction {

    private FichaInvestigacaoAgravoTuberculoseDTO investigacaoAgravoDTO;

    public SalvarFichaInvestigacaoAgravoTuberculose(
            FichaInvestigacaoAgravoTuberculoseDTO dto
    ) {
        this.investigacaoAgravoDTO = dto;
    }

    @Override
    public void execute() throws ValidacaoException, DAOException {
        Empresa empresa = FichaInvestigacaoHelper.getInstance().getEmpresa(getSessao());
        RegistroAgravo registroAgravo = FichaInvestigacaoHelper.getInstance().getRegistroAgravo(investigacaoAgravoDTO.getRegistroAgravo().getCodigo());
        Profissional profissional = FichaInvestigacaoHelper.getInstance().getProfissional (getSessao(),registroAgravo);

        registroAgravo.setProfissionalInvestigacao(profissional);
        registroAgravo.setUnidadeProfissionalInvestigacao(empresa);
        registroAgravo.setEscolaridade(registroAgravo.getUsuarioCadsus().getNivelEscolaridade());
        registroAgravo.setEndereco(registroAgravo.getUsuarioCadsus().getEnderecoUsuarioCadsus());
        registroAgravo.setDataPrimeirosSintomas(investigacaoAgravoDTO.getRegistroAgravo().getDataPrimeirosSintomas());
        registroAgravo.setStatus(RegistroAgravo.Status.EM_INVESTIGACAO.value());
        registroAgravo = FichaInvestigacaoHelper.getInstance().getStatusEncerramentoFicha(investigacaoAgravoDTO.isEncerrarFicha(),registroAgravo);

        if (RegistroAgravo.Status.EM_INVESTIGACAO.value().equals(registroAgravo.getStatus())) {
            registroAgravo.setDataEncerramento(null);
        }

        InvestigacaoAgravoTuberculose investigacaoAgravo = investigacaoAgravoDTO.getInvestigacaoAgravoTuberculose();
        investigacaoAgravo.setRegistroAgravo(registroAgravo);
        if (!isTemFichas(investigacaoAgravo.getCodigo())) {
            BOFactory.save(investigacaoAgravo);
            BOFactory.save(registroAgravo);
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_um_registro_cadastrado_com_o_mesmo_registro_agravo"));
        }
    }

    private boolean isTemFichas(Long idInvestigacaoAgravo) {
        LoadManager loadManager = LoadManager.getInstance(InvestigacaoAgravoTuberculose.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoTuberculose.PROP_REGISTRO_AGRAVO,
                        RegistroAgravo.PROP_CODIGO), investigacaoAgravoDTO.getRegistroAgravo().getCodigo()));
        if (idInvestigacaoAgravo != null) {
            loadManager.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoTuberculose.PROP_CODIGO),
                    BuilderQueryCustom.QueryParameter.DIFERENTE, idInvestigacaoAgravo));
        }
        return loadManager.start().exists();
    }

}
