package br.com.ksisolucoes.report.relacaoexame.query;

import br.com.celk.unidadesaude.exames.relacaoExame.RelacaoExameDTO;
import br.com.celk.unidadesaude.exames.relacaoExame.RelacaoExameDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExameItem;
import org.hibernate.Session;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoExame extends CommandQuery implements ITransferDataReport<RelacaoExameDTOParam, RelacaoExameDTO> {

    private RelacaoExameDTOParam param;
    private List<RelacaoExameDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.setTypeSelect(RelacaoExameDTO.class.getName());

        hql.addToSelect("ae.codigo", "atendimento");
        hql.addToSelect("ae.dataExame", "dataExame");
        hql.addToSelect("ae.tipoExame.codigo", "tipoExame.codigo");
        hql.addToSelect("ae.tipoExame.descricao", "tipoExame.descricao");

        hql.addToSelect("ep.codigo", "exameProcedimento.codigo");
        hql.addToSelect("ep.descricaoProcedimento   ", "exameProcedimento.descricaoProcedimento");
        hql.addToSelect("ep.procedimento.codigo", "exameProcedimento.procedimento.codigo");

        hql.addToSelect("ep.procedimento.codigo", "procedimento.codigo");

        hql.addToSelect("paciente.codigo", "paciente.codigo");
        hql.addToSelect("paciente.nome", "paciente.nome");

        hql.addToSelect("c.codigo", "convenio.codigo");
        hql.addToSelect("c.descricao", "convenio.descricao");

        hql.addToSelect("COALESCE(ep.valorProcedimento, aei.precoUnitario)", "precoUnitario");

        hql.addToSelect("ps.codigo", "profissionalExecutante.codigo");
        hql.addToSelect("ps.nome", "profissionalExecutante.nome");

        hql.addToSelect("pl.codigo", "profissionalResponsavel.codigo");
        hql.addToSelect("pl.nome", "profissionalResponsavel.nome");

        hql.addToFrom("AtendimentoExameItem aei"
                + " left join aei.exameProcedimento ep"
                + " left join aei.atendimentoExame ae"
                + " left join ae.profissionalSolicitante ps"
                + " left join ae.profissionalLaudo pl"
                + " left join ae.atendimento a"
                + " left join a.convenio c"
                + " left join a.usuarioCadsus paciente");

        if (RelacaoExameDTOParam.FormaApresentacao.TIPO_EXAME.equals(param.getFormaApresentacao())) {
            hql.addToOrder("ae.tipoExame.descricao");
        } else if (RelacaoExameDTOParam.FormaApresentacao.PROFISSIONAL_EXECUTANTE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("ps.nome");
        }
        if (RelacaoExameDTOParam.FormaApresentacao.PROFISSIONAL_RESPONSAVEL.equals(param.getFormaApresentacao())) {
            hql.addToOrder("pl.nome");
        }

        hql.addToWhereWhithAnd("aei.status != ", AtendimentoExameItem.StatusAtendimentoExame.CANCELADO.value());
        hql.addToWhereWhithAnd("a.empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("c = ", param.getConvenio());
        hql.addToWhereWhithAnd("ae.tipoExame = ", param.getTipoExame());
        hql.addToWhereWhithAnd("ps = ", param.getProfissionalExecutante());
        hql.addToWhereWhithAnd("pl = ", param.getProfissionalResponsavel());
        hql.addToWhereWhithAnd("paciente = ", param.getPaciente());
        hql.addToWhereWhithAnd("ae.dataExame", param.getPeriodo());
        hql.addToWhereWhithAnd("ep =", param.getExameProcedimento());

        if (RelacaoExameDTOParam.Ordenacao.DATA.equals(param.getOrdenacao())) {
            hql.addToOrder("ae.dataExame " + param.getTipoOrdenacao().getCommand());
        } else if (RelacaoExameDTOParam.Ordenacao.EXAME.equals(param.getOrdenacao())) {
            hql.addToOrder("ep.descricaoProcedimento " + param.getTipoOrdenacao().getCommand());
        } else if (RelacaoExameDTOParam.Ordenacao.PACIENTE.equals(param.getOrdenacao())) {
            hql.addToOrder("paciente.nome " + param.getTipoOrdenacao().getCommand());
        }
        hql.addToOrder("paciente.nome");
        hql.addToOrder("ae.tipoExame.descricao");

    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        for (RelacaoExameDTO result1 : result) {
            if (result1.getPrecoUnitario() == null) {
                result1.setPrecoUnitario(0.0);
            }
        }
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result
    ) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelacaoExameDTOParam param
    ) {
        this.param = param;
    }

}
