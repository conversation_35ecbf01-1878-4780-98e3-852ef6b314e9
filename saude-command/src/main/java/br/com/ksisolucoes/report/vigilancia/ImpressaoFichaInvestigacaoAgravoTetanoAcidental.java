/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaRubeolaSarampo;
import br.com.celk.report.vigilancia.query.QueryFichaTetanoAcidental;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoFichaInvestigacaoAgravoTetanoAcidental extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaTetanoAcidental query;

    public ImpressaoFichaInvestigacaoAgravoTetanoAcidental(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaTetanoAcidental();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaTetanoAcidental)getQuery()).getMapeamentoPlanilhaBase());

        columnsMap.put("dataInvestigacao", "_31_data_investigacao");
        columnsMap.put("ocupacaoCbo", "_32_ocupacao");

        columnsMap.put("possivelCausa", "_33_possivel_causa");
        columnsMap.put("possivelCausaOutros", "_33_possivel_causa_outros");
        columnsMap.put("localLesao", "_34_local_lesao");

        columnsMap.put("numeroDosesAplicadas", "_35_num_doses_aplicadas");
        columnsMap.put("dataUltimaDose", "_36_data_ultima_dose");
        columnsMap.put("tratamentoEspecifico", "_37_tratamento_especifico");

        columnsMap.put("manifestacaoClinicaTrismo", "_38_manifestacao_clinica_trismo");
        columnsMap.put("manifestacaoClinicaRisoSardonico", "_38_manifestacao_clinica_riso_sardonico");
        columnsMap.put("manifestacaoClinicaOpistotono", "_38_manifestacao_clinica_opistotono");
        columnsMap.put("manifestacaoClinicaRigidezNuca", "_38_manifestacao_clinica_rigidez_nuca");
        columnsMap.put("manifestacaoClinicaRigidezAbominal", "_38_manifestacao_clinica_rigidez_abdominal");
        columnsMap.put("manifestacaoClinicaRigidezMembros", "_38_manifestacao_clinica_rigidez_membros");
        columnsMap.put("manifestacaoClinicaCrisesContraturas", "_38_manifestacao_clinica_crises_contraturas");
        columnsMap.put("manifestacaoClinicaOutros", "_38_manifestacao_clinica_outros");

        columnsMap.put("origemCaso", "_39_origem_caso");

        columnsMap.put("hospitalizacao", "_40_hospitalizacao");
        columnsMap.put("dataInternacao", "_41_data_internacao");
        columnsMap.put("estado", "_42_estado");
        columnsMap.put("cidade", "_43_cidade");
        columnsMap.put("ibge", "_43_ibge");

        columnsMap.put("medidaControleIdentificarPopulacao", "_44_medidas_controle_identificar_populacao");
        columnsMap.put("medidaControleVacinacaoPopulacao", "_44_medidas_controle_vacinacao_populacao");
        columnsMap.put("medidaControleAnaliseCoberturaVacinal", "_44_medidas_controle_analise_cobertura_vacinal");

        columnsMap.put("classificacaoFinal", "_45_classificacao_final");

        columnsMap.put("localProvavelInfeccao", "_46_local_provavel_infeccao");
        columnsMap.put("casoAutoctone", "_47_caso_autoctone");
        columnsMap.put("estado.sigla", "_48_estado");
        columnsMap.put("paisLocalInfeccao", "_49_pais");
        columnsMap.put("cidadeLocalInfeccao.descricao", "_50_cidade");
        columnsMap.put("ibge", "_50_ibge");
        columnsMap.put("distritoLocalInfeccao", "_51_distrito");
        columnsMap.put("bairroLocalInfeccao", "_52_bairro");

        columnsMap.put("evolucaoCaso", "_53_evolucao_caso");
        columnsMap.put("dataObito", "_54_data_obito");

        columnsMap.put("observacao", "_observacao");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_agravo_tetano_acidental.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_tetano_acidental");
    }

}
