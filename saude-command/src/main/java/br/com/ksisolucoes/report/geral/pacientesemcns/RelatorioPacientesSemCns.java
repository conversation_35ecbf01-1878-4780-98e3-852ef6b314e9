package br.com.ksisolucoes.report.geral.pacientesemcns;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.geral.interfaces.dto.QueryRelatorioPacientesSemCnsDTOParam;
import br.com.ksisolucoes.report.geral.pacientesemcns.query.QueryRelatorioPacientesSemCns;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPacientesSemCns extends AbstractReport<QueryRelatorioPacientesSemCnsDTOParam> {

    public RelatorioPacientesSemCns(QueryRelatorioPacientesSemCnsDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/geral/pacientesemcns/jrxml/relatorio_pacientes_sem_cns.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        return new QueryRelatorioPacientesSemCns();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("relatorio_pacientes_sem_cartao");
    }

}