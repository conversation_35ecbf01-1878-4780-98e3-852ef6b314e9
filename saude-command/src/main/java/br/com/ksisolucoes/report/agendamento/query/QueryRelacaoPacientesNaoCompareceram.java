package br.com.ksisolucoes.report.agendamento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoPacientesNaoCompareceramDTO;
import br.com.ksisolucoes.report.agendamento.dto.RelacaoPacientesNaoCompareceramDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.report.agendamento.dto.RelacaoPacientesNaoCompareceramDTOParam.FormaApresentacao;
import static br.com.ksisolucoes.report.agendamento.dto.RelacaoPacientesNaoCompareceramDTOParam.Ordenacao;

/**
 *
 * <AUTHOR>
 */
public class QueryRelacaoPacientesNaoCompareceram extends CommandQuery<QueryRelacaoPacientesNaoCompareceram> implements ITransferDataReport<RelacaoPacientesNaoCompareceramDTOParam, RelacaoPacientesNaoCompareceramDTO> {

    private RelacaoPacientesNaoCompareceramDTOParam param;
    private List<RelacaoPacientesNaoCompareceramDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("empresa.codigo", "agendaGradeAtendimentoHorario.empresaOrigem.codigo");
        hql.addToSelect("empresa.referencia", "agendaGradeAtendimentoHorario.empresaOrigem.referencia");
        hql.addToSelect("empresa.cnes", "agendaGradeAtendimentoHorario.empresaOrigem.cnes");
        hql.addToSelect("empresa.descricao", "agendaGradeAtendimentoHorario.empresaOrigem.descricao");

        hql.addToSelect("unidadeExecutante.codigo", "agendaGradeAtendimentoHorario.localAgendamento.codigo");
        hql.addToSelect("unidadeExecutante.referencia", "agendaGradeAtendimentoHorario.localAgendamento.referencia");
        hql.addToSelect("unidadeExecutante.descricao", "agendaGradeAtendimentoHorario.localAgendamento.descricao");
        hql.addToSelect("unidadeExecutante.cnes", "agendaGradeAtendimentoHorario.localAgendamento.cnes");

        hql.addToSelect("profissional.codigo", "agendaGradeAtendimentoHorario.profissional.codigo");
        hql.addToSelect("profissional.referencia", "agendaGradeAtendimentoHorario.profissional.referencia");
        hql.addToSelect("profissional.nome", "agendaGradeAtendimentoHorario.profissional.nome");

        hql.addToSelect("tipoProcedimento.codigo", "agendaGradeAtendimentoHorario.tipoProcedimento.codigo");
        hql.addToSelect("tipoProcedimento.descricao", "agendaGradeAtendimentoHorario.tipoProcedimento.descricao");

        hql.addToSelect("usuarioCadsus.codigo", "agendaGradeAtendimentoHorario.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "agendaGradeAtendimentoHorario.usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.apelido", "agendaGradeAtendimentoHorario.usuarioCadsus.apelido");
        hql.addToSelect("usuarioCadsus.utilizaNomeSocial", "agendaGradeAtendimentoHorario.usuarioCadsus.utilizaNomeSocial");
        hql.addToSelect("usuarioCadsus.dataNascimento", "agendaGradeAtendimentoHorario.usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.celular","agendaGradeAtendimentoHorario.usuarioCadsus.celular");
        hql.addToSelect("usuarioCadsus.telefone","agendaGradeAtendimentoHorario.usuarioCadsus.telefone");
        hql.addToSelect("usuarioCadsus.telefone2","agendaGradeAtendimentoHorario.usuarioCadsus.telefone2");
        hql.addToSelect("usuarioCadsus.telefone3","agendaGradeAtendimentoHorario.usuarioCadsus.telefone3");
        hql.addToSelect("usuarioCadsus.telefone4","agendaGradeAtendimentoHorario.usuarioCadsus.telefone4");

        hql.addToSelect("solicitacao.codigo", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.codigo");
        hql.addToSelect("agah.dataAgendamento", "agendaGradeAtendimentoHorario.dataAgendamento");
        hql.addToSelect("solicitacao.dataSolicitacao", "agendaGradeAtendimentoHorario.solicitacaoAgendamento.dataSolicitacao");

        hql.addToSelect("motivoNaoComparecimento.codigo", "agendaGradeAtendimentoHorario.motivoNaoComparecimento.codigo");
        hql.addToSelect("motivoNaoComparecimento.descricao", "agendaGradeAtendimentoHorario.motivoNaoComparecimento.descricao");

        hql.addToSelect("((CASE WHEN motivoNaoComparecimento.outros = :sim AND  agah.outroMotivo IS NOT NULL THEN agah.outroMotivo ELSE motivoNaoComparecimento.descricao END))", "motivo");

        hql.setTypeSelect(RelacaoPacientesNaoCompareceramDTO.class.getName());

        hql.addToFrom("AgendaGradeAtendimentoHorario agah"
                + " left join agah.solicitacaoAgendamento solicitacao"
                + " left join agah.empresaOrigem empresa"
                + " left join agah.motivoNaoComparecimento motivoNaoComparecimento"
                + " left join agah.localAgendamento unidadeExecutante"
                + " left join agah.profissional profissional"
                + " left join agah.tipoProcedimento tipoProcedimento"
                + " left join agah.usuarioCadsus usuarioCadsus"
        );

        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("unidadeExecutante = ", param.getUnidadeExecutante());
        hql.addToWhereWhithAnd("tipoProcedimento = ", param.getTipoProcedimento());
        hql.addToWhereWhithAnd("motivoNaoComparecimento = ", param.getMotivoNaoComparecimento());
        hql.addToWhereWhithAnd("agah.dataAgendamento", Data.adjustRangeHour(param.getPeriodo()));

        if (RepositoryComponentDefault.NAO.equals(param.getMostrarTfd())) {
            hql.addToWhereWhithAnd("tipoProcedimento.flagTfd = ", RepositoryComponentDefault.NAO);
        }

        hql.addToWhereWhithAnd("agah.status = ", AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU);

        if (FormaApresentacao.UNIDADE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("empresa.descricao");
        } else if (FormaApresentacao.UNIDADE_EXECUTANTE.equals(param.getFormaApresentacao())) {
            hql.addToOrder("unidadeExecutante.descricao");
        } else if (FormaApresentacao.TIPO_PROCEDIMENTO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("tipoProcedimento.descricao");
        } else if (FormaApresentacao.MOTIVO.equals(param.getFormaApresentacao())) {
            hql.addToOrder("motivoNaoComparecimento.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
        }

        if (Ordenacao.UNIDADE_EXECUTANTE.equals(param.getOrdenacao())) {
            hql.addToOrder("unidadeExecutante.descricao");
        } else if (Ordenacao.PACIENTE.equals(param.getOrdenacao())) {
            hql.addToOrder("usuarioCadsus.nome");
        }

        hql.addToOrder("solicitacao.codigo");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        query.setInteger("sim", RepositoryComponentDefault.SIM_INTEGER);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelacaoPacientesNaoCompareceramDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelacaoPacientesNaoCompareceramDTOParam param) {
        this.param = param;
    }

}
