<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="report name" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="49dcf7e9-c139-4737-b6fa-820a7e25b3fb">
	<property name="ireport.zoom" value="3.1384283767210324"/>
	<property name="ireport.x" value="719"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioRealizadoPrestConsoliCompDTOParam.FormaApresentacao"/>
	<field name="estabelecimentoSaude" class="java.lang.String"/>
	<field name="cnes" class="java.lang.String"/>
	<field name="valorTotalAnterior" class="java.lang.Double"/>
	<field name="valorTotalAtual" class="java.lang.Double"/>
	<field name="diferencaValoresCompt" class="java.lang.Double"/>
	<variable name="totalGeralMesAnterior" class="java.lang.Double" resetType="Group" resetGroup="default" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalAnterior}]]></variableExpression>
		<initialValueExpression><![CDATA[0L]]></initialValueExpression>
	</variable>
	<variable name="totalGeralMesAtual" class="java.lang.Double" resetType="Group" resetGroup="default" calculation="Sum">
		<variableExpression><![CDATA[$F{valorTotalAtual}]]></variableExpression>
		<initialValueExpression><![CDATA[0L]]></initialValueExpression>
	</variable>
	<variable name="totalGeralDiferencaValorComp" class="java.lang.Double" resetType="Group" resetGroup="default" calculation="Sum">
		<variableExpression><![CDATA[$F{diferencaValoresCompt}]]></variableExpression>
		<initialValueExpression><![CDATA[0L]]></initialValueExpression>
	</variable>
	<group name="default">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
	</group>
	<group name="formaApresentacao" keepTogether="true">
		<groupFooter>
			<band height="12">
				<textField isStretchWithOverflow="true" pattern="#,##0.00;-#,##0.00">
					<reportElement x="373" y="1" width="60" height="11" isPrintWhenDetailOverflows="true" uuid="22df475c-8d12-460a-a297-2e2f9e71fae9"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeralMesAnterior}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="273" y="1" width="100" height="11" uuid="b3d63b81-771c-437b-a9fc-b6860ab9c986"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="280" y="0" width="275" height="1" uuid="e9a08ec1-a0d4-4c34-b1c1-0bc56d7c2f69"/>
				</line>
				<textField pattern="#,##0.00;-#,##0.00">
					<reportElement x="433" y="1" width="60" height="11" uuid="45936c19-a202-4126-86a7-37ed709a0270"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeralMesAtual}]]></textFieldExpression>
				</textField>
				<textField pattern="#,##0.00;-#,##0.00">
					<reportElement x="495" y="1" width="60" height="11" uuid="642e615a-d3c8-4bad-9f11-a0037ebdb808"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="7"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{totalGeralDiferencaValorComp}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="formaApresentacaoTitle">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="13">
				<textField>
					<reportElement x="0" y="0" width="60" height="11" uuid="ad653cb6-246b-408d-a878-7ccfc5f8d6a6"/>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_cnes")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="373" y="0" width="60" height="11" isPrintWhenDetailOverflows="true" uuid="975d4b02-1277-4efb-ab11-055191129c73"/>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("valorMesAnterior")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="0" y="12" width="555" height="1" isPrintWhenDetailOverflows="true" uuid="9d0fd060-a82d-4f19-9177-bde5bb5c379e"/>
				</line>
				<textField>
					<reportElement positionType="Float" x="60" y="0" width="220" height="11" uuid="f744268d-5e52-46eb-bd3a-356e7f399668"/>
					<textElement verticalAlignment="Bottom">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("estabelecimentoSaude")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="false">
					<reportElement positionType="Float" x="433" y="0" width="60" height="11" isPrintWhenDetailOverflows="true" uuid="e3014d72-b061-4152-b6f3-ab69143b58f8"/>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("valorMesAtual")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true">
					<reportElement positionType="Float" x="495" y="0" width="60" height="11" isPrintWhenDetailOverflows="true" uuid="fb62b5d0-e697-4fef-8241-1bb5f862a849"/>
					<textElement textAlignment="Right" verticalAlignment="Bottom">
						<font fontName="Arial" size="7" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("diferencaMesAtualAnterior")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<detail>
		<band height="14" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="60" height="11" uuid="8e247cd9-34ce-42d0-adfc-c18549280120"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cnes}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00;-#,##0.00">
				<reportElement positionType="Float" x="373" y="0" width="60" height="11" uuid="9ae931b7-345f-44a0-a899-e3b600e44b1f"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorTotalAnterior}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement x="60" y="0" width="220" height="11" uuid="ae7d8849-7eee-4e85-b825-7e46c013e4ed"/>
				<textElement>
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{estabelecimentoSaude}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00">
				<reportElement positionType="Float" x="433" y="0" width="60" height="11" uuid="353eec13-d831-438a-98a2-aa198a5f02c9"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorTotalAtual}]]></textFieldExpression>
			</textField>
			<textField pattern="#,##0.00;-#,##0.00">
				<reportElement positionType="Float" x="495" y="0" width="60" height="11" uuid="0ebdb3f5-5d9c-4382-99d8-2a8ff5c659c0"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="7"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{diferencaValoresCompt}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
