/*
 * QueryRelatorioRMNRA.java
 *
 * Created on 09 de Outubro de 2006
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */
package br.com.ksisolucoes.report.entrada.dispensacao.query;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRMNRADTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioRMNRADTOParam;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioRMNRA extends CommandQuery<QueryRelatorioRMNRA> implements ITransferDataReport<RelatorioRMNRADTOParam, RelatorioRMNRADTO> {

    private RelatorioRMNRADTOParam param;
    private List<RelatorioRMNRADTO> list;

    @Override
    protected void createQuery(HQLHelper hql) {
        /*..........Select............*/
        hql.addToSelect("dm.receita", "numeroReceita");
        hql.addToSelect("coalesce(dm.profissional.codigo, profissionalSemVinculo.codigo)", "codigoProfissional");
        hql.addToSelect("coalesce(dm.nomeProfissional, dm.nomeProfissional, profissionalSemVinculo.nomeProfissional)", "nomeProfissional");
//        hql.addToSelect( "(select max(pch.numeroRegistro) " +
        hql.addToSelect("coalesce(dm.numeroRegistro, profissionalSemVinculo.numeroConselhoClasse)", "numeroRegistroProfissional");
//                " from ProfissionalCargaHoraria pch " +
//                " where pch.codigoEmpresa = dm.id.empresa.codigo " +
//                " and   pch.codigoProfissional = dm.profissional.codigo " +
//                " and   pch.dataAtualizacao = (select max(pch1.dataAtualizacao) from ProfissionalCargaHoraria pch1 " +
//                "                              where pch1.codigoEmpresa = pch.codigoEmpresa" +
//                "                              and   pch1.codigoProfissional = pch.codigoProfissional ))" );
        hql.addToSelect("dm.dataReceita", "dataReceita");
        hql.addToSelect("dm.empresa.empresaMaterial.numLicencaFuncionamento", "numLicencaFuncionamento");
        hql.addToSelect("dm.empresa.empresaMaterial.numeroAutorizacaoFuncionamento", "numeroAutorizacaoFuncionamento");
        hql.addToSelect("dmi.dataUltimaDispensacao", "dataUltimaDispensacao");
        hql.addToSelect("dmi.quantidadePrescrita", "quantidadePrescrita");
        hql.addToSelect("dmi.quantidadeDispensada", "quantidadeDispensada");
        hql.addToSelect("pr.codigoDcb", "codigoDcb");
        hql.addToSelect("pr.nomeDcb", "nomeDcb");
        hql.addToSelect("pr.descricao", "nomeProduto");
        hql.addToSelect("pr.concentracao", "concentracao");
//        hql.addToSelect( "pra.descricao" );

        // from
        hql.addToFrom(DispensacaoMedicamentoItem.class.getName() + " dmi " +
                "right join dmi.dispensacaoMedicamento dm " +
                "left join dm.usuarioCadsusDestino " +
                "left join dm.profissionalSemVinculo profissionalSemVinculo "
        );

        hql.addToFrom(Produto.class.getName() + " pr ");

        hql.setTypeSelect(RelatorioRMNRADTO.class.getName());

        // where
        hql.addToWhereWhithAnd(" dm.tipoReceita.tipoReceita = '" + TipoReceita.RECEITA_AMARELA + "'");
        hql.addToWhereWhithAnd(" dmi.produto = pr ");

        //empresa
        if (this.param.getEmpresa() != null) {
            hql.addToWhereWhithAnd("dm.empresa = ", this.param.getEmpresa());
        }
        //dataInicial
        if (this.param.getDataInicial() != null) {
            hql.addToWhereWhithAnd("dm.dataDispensacao >= ", this.param.getDataInicial());
        }
        //dataFinal
        if (this.param.getDataFinal() != null) {
            hql.addToWhereWhithAnd("dm.dataDispensacao <= ", DataUtil.finalHora(this.param.getDataFinal()));
        }

        /*if ( this.bean.getProfissionalResponsavel() != null ){
         hql.addToWhereWhithAnd("dm.profissional = :profissionalResponsavel" );
         }*/
        //crf
        /*if ( StringUtils.isNotBlank( this.bean.getCrf() ) ){
         hql.addToWhereWhithAnd("lower(dm.profissional.numeroRegistro) like lower(:numeroRegistro)" );
         }*/
        //Ordenacao
        hql.addToOrder("dm.dataReceita");

        /*....................................*/
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioRMNRADTO> getResult() {
        return this.list;
    }

    @Override
    public void setDTOParam(RelatorioRMNRADTOParam param) {
        this.param = param;
    }
}
