package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryFichaFebreMaculosa;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

public class ImpressaoFichaInvestigacaoAgravoFebreMaculosa extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaFebreMaculosa query;

    public ImpressaoFichaInvestigacaoAgravoFebreMaculosa(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if (query == null) {
            query = new QueryFichaFebreMaculosa();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaFebreMaculosa) getQuery()).getMapeamentoPlanilhaBase());

        //DADOS COMPLEMENTARES DO CASO - DADOS CLINICOS
        columnsMap.put(".dataInvestigacao", "_31_data_investigacao");
        columnsMap.put("descricao", "_32_ocupacao");
        columnsMap.put("sintomaFebre", "_33_sintoma_febre");
        columnsMap.put("sintomaNauseaVomito", "_33_sintoma_nausea_vomito");
        columnsMap.put("sintomaHiperemiaConjutival", "_33_sintoma_hiperemia_conjuntival");
        columnsMap.put("sintomaLinfadenopatia", "_33_sintoma_linfadenopatia");
        columnsMap.put("sintomaChoqueHipotensao", "_33_sintoma_choque_hipotensao");
        columnsMap.put("sintomaOliguriaAnuria", "_33_sintoma_oliguria_anuria");
        columnsMap.put("sintomaCefaleia", "_33_sintoma_cefaleia");
        columnsMap.put("sintomaExantema", "_33_sintoma_exantema");
        columnsMap.put("sintomaHepatomagaliaEsplenomegalia", "_33_sintoma_hepatomagalia");
        columnsMap.put("sintomaConvulsao", "_33_sintoma_convulsao");
        columnsMap.put("sintomaEstuporComa", "_33_sintoma_estupor_coma");
        columnsMap.put("sintomaOutros", "_33_sintoma_outros");
        columnsMap.put("sintomaDorAbdominal", "_33_sintoma_dor_abdominal");
        columnsMap.put("sintomaDiarreia", "_33_sintoma_diarreia");
        columnsMap.put("sintomaPetequias", "_33_sintoma_petequias");
        columnsMap.put("sintomaNecroseExtremidades", "_33_sintoma_necrose_extremidades");
        columnsMap.put("sintomaSufusaoHemorragica", "_33_sintoma_sufusao_hemorragica");
        columnsMap.put("sintomaMialgia", "_33_sintoma_mialgia");
        columnsMap.put("sintomaIctericia", "_33_sintoma_ictericia");
        columnsMap.put("sintomaManifestacoesHemorragicas", "_33_sintoma_manifestacoes_hemorragicas");
        columnsMap.put("sintomaProstracao", "_33_sintoma_prostracao");
        columnsMap.put("sintomaAlteracoesRespiratorias", "_33_sintoma_alteracoes_respiratorias");

        //EPIDEMILOGIA
        columnsMap.put("situacaoRiscoCarrapato", "_34_situacao_risco_carrapato");
        columnsMap.put("situacaoRiscoCapivara", "_34_situacao_risco_capivara");
        columnsMap.put("situacaoRiscoCaoGato", "_34_situacao_risco_cao_gato");
        columnsMap.put("situacaoRiscoBovinos", "_34_situacao_risco_bovinos");
        columnsMap.put("situacaoRiscoEquinos", "_34_situacao_risco_equinos");
        columnsMap.put("situacaoRiscoOutros", "_34_situacao_risco_outros");
        columnsMap.put("ambienteFloresta", "_35_frequentou_ambientes_floresta");

        //TRATAMENTO
        columnsMap.put("hospitalizacao", "_36_ocorreu_hospitalizacao");
        columnsMap.put("dataInternacao", "_37_data_internacao");
        columnsMap.put("dataAlta", "_38_data_alta");
        columnsMap.put("estadoHospitalTratamentoSigla", "_39_uf");
        columnsMap.put("cidadeHospitalTratamentoCescricao", "_40_municipio_hospital_tratamento");
        columnsMap.put("estadoHospitalTratamentoCodigo", "_40_codigo_ibge");
        columnsMap.put("hospitalTratamentoDdescricao", "_41_nome_hospital");
        columnsMap.put("hospitalTratamentoCodigo", "_41_codigo");

        //DADOS LABORATORIAIS ESPECIFICOS
        columnsMap.put("diagnosticoLaboratorial", "_42_diagnostico_laboratorial");
        columnsMap.put("sorologiaDataColetaS1", "_43_sorologia_data_coleta_s1");
        columnsMap.put("sorologiaIgmS1SN", "_43_sorologia_igm_s1_sn");
        columnsMap.put("sorologiaIgmS1Titulo", "_43_sorologia_igm_s1_titulo");
        columnsMap.put("sorologiaIggS1SN", "_43_sorologia_igg_s1_sn");
        columnsMap.put("sorologiaIggS1Titulo", "_43_sorologia_igg_s1_titulo");
        columnsMap.put("sorologiaDataColetaS2", "_43_sorologia_data_coleta_s2");
        columnsMap.put("sorologiaIgmS2SN", "_43_sorologia_igm_s2_sn");
        columnsMap.put("sorologiaIgmS2Titulo", "_43_sorologia_igm_s2_titulo");
        columnsMap.put("sorologiaIggS2SN", "_43_sorologia_igg_s2_sn");
        columnsMap.put("sorologiaIggS2Titulo", "_43_sorologia_igg_s2_titulo");
        columnsMap.put("IsolamentoData", "_44_data_coleta_isolamento");
        columnsMap.put("isolamentoResultado", "_45_resultado_isolamento");
        columnsMap.put("isolamentoAgente", "_46_agente_isolamento");
        columnsMap.put("histopatologiaResultado", "_47_resultado_histopatologia");
        columnsMap.put("imunohistoquimicaResultado", "_48_resultado_imunohistoquimica");

        //CONCLUSAO
        columnsMap.put("classificacaoFinal", "_49_classificacao_final");
        columnsMap.put("criterioConfirmacaoDescarte", "_50_criterio_confirmacao_descarte");
        columnsMap.put("diagnosticoEspecificado", "_51_diagnostico_especificado");
        columnsMap.put("casoAutoctone", "_52_caso_autoctone");
        columnsMap.put("estadoLocalInfeccaoSsigla", "_53_uf_local_infeccao");
        columnsMap.put("paisLocalInfeccaoDescricao", "_54_pais_local_infeccao");
        columnsMap.put("cidadeLocalInfeccaoDescricao", "_55_cidade_local_infeccao");
        columnsMap.put("cidadeLocalInfeccaoCodigo", "_55_ibge_local_infeccao");
        columnsMap.put("distritoLocalInfeccao", "_56_distrito_local_infeccao");
        columnsMap.put("bairroLocalInfeccao", "_57_bairro_local_infeccao");
        columnsMap.put("localProvavelInfeccaoZona", "_58_zona_local_infeccao");
        columnsMap.put("localProvavelInfeccaoAmbiente", "_59_ambiente_local_infeccao");
        columnsMap.put("relacionadoTrabalho", "_60_doenca_relacionada_trabalho");
        columnsMap.put("evolucaoCaso", "_61_evolucao_caso");
        columnsMap.put("dataObito", "_62_data_obito");
        columnsMap.put("dataEncerramento", "_63_data_encerramento");
        columnsMap.put("observacao", "observacoes");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_febre_maculosa.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("relatorioFichasFebreMaculosa");
    }

}