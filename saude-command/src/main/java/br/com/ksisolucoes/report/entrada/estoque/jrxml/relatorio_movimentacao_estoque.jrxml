<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_movimentacao_estoque" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20" uuid="9b0181b5-ec55-441b-a447-b0af9abc3b1a">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.853116706110003"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="totalTipoDocumentoList" class="java.util.List" isForPrompting="false"/>
	<parameter name="filtroLocalizacaoEstruturaInformado" class="java.lang.Boolean"/>
	<parameter name="filtroLoteInformado" class="java.lang.Boolean"/>
	<field name="grupoEstoque" class="java.lang.String"/>
	<field name="quantidade" class="java.lang.Double"/>
	<field name="estoqueFisico" class="java.lang.Double"/>
	<field name="precoMedio" class="java.lang.Double"/>
	<field name="nomePessoa" class="java.lang.String"/>
	<field name="tipoDocumento" class="java.lang.Object"/>
	<field name="usuario" class="java.lang.Object"/>
	<field name="id" class="java.lang.Object"/>
	<field name="dataLancamento" class="java.util.Date"/>
	<field name="numeroDocumento" class="java.lang.String"/>
	<field name="itemDocumento" class="java.lang.Long"/>
	<field name="produto" class="java.lang.Object"/>
	<field name="dataPortaria" class="java.util.Date"/>
	<field name="estoqueFisicoAnterior" class="java.lang.Double"/>
	<field name="empresaDestino" class="java.lang.Object"/>
	<field name="estoqueFisicoAnteriorRelatorio" class="java.lang.Double"/>
	<field name="deposito" class="br.com.ksisolucoes.vo.entradas.estoque.Deposito"/>
	<field name="estoqueDisponivel" class="java.lang.Double"/>
	<field name="centroCusto" class="br.com.ksisolucoes.vo.entradas.estoque.CentroCusto"/>
	<field name="estoqueFisicoGrupo" class="java.lang.Double"/>
	<field name="localizacaoEstrutura" class="br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="TIPODOCUMENTO" class="br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento">
		<variableExpression><![CDATA[$F{tipoDocumento}]]></variableExpression>
	</variable>
	<variable name="ESTOQUE" class="br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque"/>
	<variable name="USUARIO" class="br.com.ksisolucoes.vo.controle.Usuario">
		<variableExpression><![CDATA[$F{usuario}]]></variableExpression>
	</variable>
	<variable name="ID" class="br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoquePK">
		<variableExpression><![CDATA[$F{id}]]></variableExpression>
	</variable>
	<variable name="DATA" class="br.com.ksisolucoes.util.Data"/>
	<variable name="PRODUTO" class="br.com.ksisolucoes.vo.entradas.estoque.Produto">
		<variableExpression><![CDATA[$F{produto}]]></variableExpression>
	</variable>
	<variable name="ESTOQUE_ANTERIOR" class="java.lang.Double">
		<variableExpression><![CDATA[(($V{COLUMN_COUNT} == 1) && ($V{PAGE_NUMBER} == 1)) ? $F{estoqueFisicoAnterior} : $V{ESTOQUE_ANTERIOR}]]></variableExpression>
	</variable>
	<variable name="EMPRESA_DESTINO" class="br.com.ksisolucoes.vo.basico.Empresa">
		<variableExpression><![CDATA[$F{empresaDestino}]]></variableExpression>
	</variable>
	<group name="principal">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band splitType="Stretch"/>
		</groupHeader>
		<groupFooter>
			<band height="12" splitType="Stretch">
				<subreport isUsingCache="true">
					<reportElement key="subreport-1" mode="Opaque" x="0" y="2" width="133" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="6192fb29-58ad-42a8-92e5-f6660d07a5cf"/>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource( $P{totalTipoDocumentoList} )]]></dataSourceExpression>
					<subreportExpression><![CDATA["br/com/ksisolucoes/report/entrada/estoque/jrxml/sub_relatorio_movimentacao_estoque.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupFooter>
	</group>
	<group name="empresa" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$V{ID}.getEmpresa().getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="18" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Transparent" x="1" y="1" width="780" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="ee25f5d0-e5ce-4d5e-b45a-f02fb03d2774"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="empresa" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-43" mode="Opaque" x="4" y="2" width="765" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="3f7c727d-841c-44be-98de-90290dca8d32"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_unidade_movimentacao") +": " + $V{ID}.getEmpresa().getDescricaoFormatado()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<group name="DepositoGroup" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{deposito}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="18" splitType="Stretch">
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Transparent" x="1" y="1" width="780" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="16630a8d-00a4-4516-9c6e-d13e9ba2880c"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="DepositoGroup" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-43" mode="Opaque" x="4" y="2" width="765" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="46836f51-8fb8-4d63-983c-7e0aad4b3e59"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_deposito") +":" + " " +
(
$F{deposito}.getCodigo() != null ? $F{deposito}.getDescricaoFormatado() :
$V{BUNDLE}.getStringApplication("rotulo_sem_cadastro"))]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<group name="ProdutoGrupo" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$V{PRODUTO}.getCodigo()]]></groupExpression>
		<groupHeader>
			<band height="29" splitType="Stretch">
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-18" mode="Transparent" x="180" y="18" width="34" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="7ee21803-6904-479e-92c2-426a160c2df6"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Quantidade*/$V{BUNDLE}.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-19" mode="Transparent" x="213" y="18" width="46" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="be4c0233-6a36-4987-99bb-f0042acd92e7"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*EstoqueFisico*/$V{BUNDLE}.getStringApplication("rotulo_estoque_fisico_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-20" mode="Transparent" x="390" y="18" width="46" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="2fb227f4-f842-4a0a-bf20-fe394ba4aacd"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*PrecoMedio*/$V{BUNDLE}.getStringApplication("rotulo_preco_medio")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-22" mode="Transparent" x="439" y="18" width="100" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c6a7d5b4-7dfc-457c-ab98-899d672ad274"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Nome*/$V{BUNDLE}.getStringApplication("rotulo_pessoa_empresa")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-23" mode="Transparent" x="541" y="18" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c8fc2314-9096-4216-97c3-b24d206c77ab"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Usuario*/$V{BUNDLE}.getStringApplication("rotulo_usuario")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-24" mode="Transparent" x="638" y="18" width="35" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="201dcd62-64d1-4808-af2c-4d51f86ae68e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*NumLancamento*/$V{BUNDLE}.getStringApplication("rotulo_numero_lancamento_abv2")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-25" mode="Transparent" x="110" y="18" width="35" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3687cef4-6ad3-40ac-b67f-394b94d83b5e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*TipoDocumento*/$V{BUNDLE}.getStringApplication("rotulo_tipo_documento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-44" mode="Transparent" x="678" y="18" width="103" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="b0bfeebf-5565-4ca1-85c4-1257d5ffbcd0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Unidade Destino*/$V{BUNDLE}.getStringApplication("rotulo_unidade_destino_origem")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-34" mode="Transparent" x="0" y="18" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="edbd42c2-99bc-4751-b1fd-ca95254ae5ec"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*DtLanc*/$V{BUNDLE}.getStringApplication("rotulo_data_lancamento_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-37" mode="Transparent" x="48" y="18" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="3cb68b01-1398-48f0-92c6-5c59c13a4290"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*NrDoc*/$V{BUNDLE}.getStringApplication("rotulo_numero_documento_abv")]]></textFieldExpression>
				</textField>
				<rectangle radius="5">
					<reportElement key="rectangle-1" mode="Opaque" x="1" y="1" width="780" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="1709393c-3bf2-4410-9bd1-c188824542ca"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-38" mode="Transparent" x="91" y="18" width="19" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="9af81492-fa85-406d-9a97-781b117a8cad"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*ItDoc*/$V{BUNDLE}.getStringApplication("rotulo_item")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-41" mode="Transparent" x="593" y="18" width="45" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="fa29a4f5-0bb1-4703-926c-3d04cc1ebdb2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*DtEntrada*/$V{BUNDLE}.getStringApplication("rotulo_data_movimentacao_abv")]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" evaluationTime="Group" evaluationGroup="ProdutoGrupo" pattern="" isBlankWhenNull="true">
					<reportElement key="textField-32" mode="Opaque" x="4" y="2" width="765" height="14" forecolor="#000000" backcolor="#FFFFFF" uuid="272dbc2d-93b8-486a-8b10-255baedb1c4a"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="10" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_produto") +":" + " " + StringUtilKsi.extractString( $V{PRODUTO}.getDescricaoFormatado(), 88 ) + " - Saldo Anterior: " + Valor.adicionarFormatacaoMonetaria($F{estoqueFisicoAnteriorRelatorio})]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-4" mode="Opaque" x="0" y="27" width="782" height="1" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="8234e733-b80f-443a-a80e-59ffcdbb6bad"/>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-18" mode="Transparent" x="146" y="18" width="34" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d6e73b27-0896-4edb-9c33-3526727f9363"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Lote*/$V{BUNDLE}.getStringApplication("rotulo_lote")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-19" mode="Transparent" x="310" y="18" width="78" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="edeac8d0-98f9-4312-ae8e-71a03ba8f7b5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*CentroCusto*/$V{BUNDLE}.getStringApplication("rotulo_centro_custo")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-19" mode="Transparent" x="266" y="18" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="edeac8d0-98f9-4312-ae8e-71a03ba8f7b5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[/*Localização*/$V{BUNDLE}.getStringApplication("rotulo_localizacao")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band splitType="Stretch"/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="11" splitType="Stretch">
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-16" mode="Opaque" x="214" y="1" width="45" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="f5191e0f-a2ba-41c8-acce-225b521da581"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{filtroLocalizacaoEstruturaInformado} || $P{filtroLoteInformado}
    ?
        $F{estoqueFisicoGrupo}
    :
        $F{estoqueFisico}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.0000" isBlankWhenNull="true">
				<reportElement key="textField-26" mode="Opaque" x="390" y="1" width="46" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="7258c5bc-4229-495a-9869-093394889a5d"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{precoMedio}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-27" mode="Opaque" x="439" y="1" width="100" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="6b57e3ca-1ccf-4a6d-85f9-cf77aabde700"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomePessoa}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-29" mode="Opaque" x="110" y="1" width="35" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="53f69a2f-148a-4364-ac9a-aacf0fd9a0ab"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{TIPODOCUMENTO}.getFlagSigla()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-30" mode="Opaque" x="541" y="1" width="50" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="68e1feac-07e0-4e65-b303-b7d024f9e563"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{USUARIO}.getLogin()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-31" mode="Opaque" x="638" y="1" width="34" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="11a8d258-9de3-4312-8f81-259f152c93d8"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{ID}.getNumeroLancamento()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-33" mode="Opaque" x="0" y="1" width="49" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="b88c77c4-c9f7-401e-914d-dae27d84614a"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{DATA}.formatar($F{dataLancamento})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" mode="Opaque" x="49" y="1" width="41" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="8e80bb5b-8fde-4c3d-a7b3-dee7b1bbbb38"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numeroDocumento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-36" mode="Opaque" x="92" y="1" width="18" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="5ded1008-45fb-4c81-8f4c-86eaf51e9a27"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{itemDocumento}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-40" mode="Opaque" x="593" y="1" width="45" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="9fa0c448-8981-4843-8ec4-ab01c13a5d9f"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{DATA}.formatar($F{dataPortaria})]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="#,##0.00" isBlankWhenNull="true">
				<reportElement key="textField-42" mode="Opaque" x="180" y="1" width="34" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="17086599-0083-439c-b2fe-a8304d4b2d13"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField" mode="Transparent" x="678" y="1" width="103" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="5e33214f-f105-4a8e-bd41-8394045cbf02"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{EMPRESA_DESTINO}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-42" mode="Opaque" x="147" y="1" width="34" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="d7ae7910-ece0-4b65-b3e9-753675df7f60"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grupoEstoque}!=null
?
    $F{grupoEstoque}.trim()
:
    ""]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
				<reportElement key="textField-16" mode="Opaque" x="310" y="1" width="78" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c488c279-4bef-4167-ab65-b72efb795fbe"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{centroCusto}!=null
?
    $F{centroCusto}.getDescricaoFormatado()
:
    $V{BUNDLE}.getStringApplication("rotulo_nao_definido")]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-16" mode="Opaque" x="266" y="1" width="42" height="10" forecolor="#000000" backcolor="#FFFFFF" uuid="c488c279-4bef-4167-ab65-b72efb795fbe"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="SansSerif" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{localizacaoEstrutura}.getMascara()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
