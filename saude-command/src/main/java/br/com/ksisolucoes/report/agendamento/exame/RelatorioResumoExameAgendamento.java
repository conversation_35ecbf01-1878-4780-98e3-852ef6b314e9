/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.agendamento.exame;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExameAgendamentoDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExameAgendamentoDTOParam;
import br.com.ksisolucoes.report.agendamento.exame.query.QueryRelatorioResumoAgendamento;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoExameAgendamento extends AbstractReport<RelatorioResumoExameAgendamentoDTOParam> {

    public RelatorioResumoExameAgendamento(RelatorioResumoExameAgendamentoDTOParam param) {
        super(param);
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioResumoExameAgendamentoDTO proxy = Lambda.on(RelatorioResumoExameAgendamentoDTO.class);

        if (!this.param.getFormaApresentacao().toString().equals(this.param.getTipoResumo().toString())) {
            if (RelatorioResumoExameAgendamentoDTOParam.FormaApresentacao.UNIDADE.equals(this.param.getFormaApresentacao())) {
                columns.put((Bundle.getStringApplication("rotulo_empresa")), proxy.getEmpresa().getDescricaoFormatado());
            } else if (RelatorioResumoExameAgendamentoDTOParam.FormaApresentacao.UNIDADE_ORIGEM.equals(this.param.getFormaApresentacao())) {
                columns.put((Bundle.getStringApplication("rotulo_empresa_origem")), proxy.getEmpresaOrigem().getDescricaoFormatado());
            } else if (RelatorioResumoExameAgendamentoDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals(this.param.getFormaApresentacao())
                    || RelatorioResumoExameAgendamentoDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO_UNIDADE.equals(this.param.getFormaApresentacao())) {
                columns.put((Bundle.getStringApplication("rotulo_tipo_procedimento")), proxy.getTipoProcedimento().getDescricaoFormatado());
            } else if (RelatorioResumoExameAgendamentoDTOParam.FormaApresentacao.PROFISSIONAL.equals(this.param.getFormaApresentacao())) {
                columns.put((Bundle.getStringApplication("rotulo_profissional")), proxy.getProfissional().getDescricaoFormatado());
            } else if (RelatorioResumoExameAgendamentoDTOParam.FormaApresentacao.AREA.equals(this.param.getFormaApresentacao())) {
                columns.put((Bundle.getStringApplication("rotulo_area")), proxy.getDescricaoArea());
            }
        }

        if (RelatorioResumoExameAgendamentoDTOParam.TipoResumo.UNIDADE.equals(this.param.getTipoResumo())) {
            columns.put((Bundle.getStringApplication("rotulo_empresa")), proxy.getEmpresa().getDescricaoFormatado());
        } else if (RelatorioResumoExameAgendamentoDTOParam.TipoResumo.UNIDADE_ORIGEM.equals(this.param.getTipoResumo())) {
            columns.put((Bundle.getStringApplication("rotulo_empresa_origem")), proxy.getEmpresaOrigem().getDescricaoFormatado());
        } else if (RelatorioResumoExameAgendamentoDTOParam.TipoResumo.TIPO_PROCEDIMENTO.equals(this.param.getTipoResumo())) {
            columns.put((Bundle.getStringApplication("rotulo_tipo_procedimento")), proxy.getTipoProcedimento().getDescricaoFormatado());
        } else if (RelatorioResumoExameAgendamentoDTOParam.TipoResumo.PROFISSIONAL.equals(this.param.getTipoResumo())) {
            columns.put((Bundle.getStringApplication("rotulo_profissional")), proxy.getProfissional().getDescricaoFormatado());
        } else if (RelatorioResumoExameAgendamentoDTOParam.TipoResumo.AREA.equals(this.param.getTipoResumo())) {
            columns.put((Bundle.getStringApplication("rotulo_area")), proxy.getDescricaoArea());
        }

        columns.put((Bundle.getStringApplication("rotulo_agendados")), proxy.getAgendados());
        columns.put((Bundle.getStringApplication("rotulo_atendidos")), proxy.getEncaminhados());
        columns.put((Bundle.getStringApplication("rotulo_faltas")), proxy.getFaltas());
        columns.put((Bundle.getStringApplication("rotulo_total")), proxy.getTotal());
        columns.put((Bundle.getStringApplication("rotulo_cancelados")), proxy.getCancelados());

        return columns;
    }

    @Override
    public String getXML() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        addParametro("tipoResumo", getParam().getTipoResumo());

        return "/br/com/ksisolucoes/report/agendamento/exame/jrxml/relatorio_resumo_exame_agendamentos.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_resumo_dos_agendamentos");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioResumoAgendamento();
    }

    @Override
    protected void customDTOParam(RelatorioResumoExameAgendamentoDTOParam param) throws ValidacaoException {
        if (param.getPeriodo().getDataInicial() == null || param.getPeriodo().getDataFinal() == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_periodo"));
        }
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }
}
