package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryCidades extends QueryPerfilAtendimento {

    private List<RelatorioPerfilAtendimentoDTO> result;

    public QueryCidades(RelatorioPerfilAtendimentoDTOParam param) {
        super(param);
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("cidade.codigo", "codigo");
        hql.addToSelect("cidade.descricao", "descricao");
        hql.addToSelect("sum(1)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(cidade1.codigo)");
            hqlTotal.addToFrom("Atendimento a1"
                    + " left join a1.enderecoUsuarioCadsus ende1"
                    +"  left join ende1.cidade cidade1");
            addWhereAtendimento(hqlTotal, "a1", false);
            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("Atendimento a"
                + " left join a.enderecoUsuarioCadsus ende"
                +"  left join ende.cidade cidade");
        hql.setTypeSelect(RelatorioPerfilAtendimentoDTO.class.getName());
        addWhereAtendimento(hql, "a", false);
        hql.addToGroup("cidade.codigo");
        hql.addToGroup("cidade.descricao");
        hql.addToOrder("3 desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioPerfilAtendimentoDTO> getResult() {
        return result;
    }
}




