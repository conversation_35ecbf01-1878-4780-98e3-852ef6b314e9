package br.com.ksisolucoes.report.prontuario.procedimento;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioResumoExameAgendamentoDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoDTO;
import br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioProcedimentoResumidoDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioProcedimentoResumido;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR> laudecir
 */
public class RelatorioProcedimentoResumido extends AbstractReport<RelatorioProcedimentoResumidoDTOParam> {

    public RelatorioProcedimentoResumido(RelatorioProcedimentoResumidoDTOParam param) {
        super(param);
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioProcedimentoDTO proxy = Lambda.on(RelatorioProcedimentoDTO.class);

        columns.put((Bundle.getStringApplication("rotulo_procedimento")), proxy.getProcedimento().getDescricaoFormatado());
        columns.put((Bundle.getStringApplication("rotulo_quantidade")), proxy.getQuantidade());

        return columns;
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao().toString());
//        if (CollectionUtils.isNotNullEmpty(this.param.getEmpresas().getValue())) {
//            this.getParam().setAgruparUnidade(RepositoryComponentDefault.NAO);
//        } else {
//            this.getParam().setAgruparUnidade(RepositoryComponentDefault.SIM);
//        }
        this.addParametro("AGRUPAR_UNIDADE", this.getParam().getAgruparUnidade());
        this.addParametro("VISUALIZAR_VALOR", this.getParam().getVisualizarValor());
        return new QueryRelatorioProcedimentoResumido();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/procedimento/jrxml/relatorio_procedimentos_resumido.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_procedimentos_resumido");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }
    
    
}
