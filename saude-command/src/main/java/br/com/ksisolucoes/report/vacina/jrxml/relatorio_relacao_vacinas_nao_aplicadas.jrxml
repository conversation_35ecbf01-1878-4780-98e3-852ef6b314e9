<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_vacinas_nao_aplicadas" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="1.5"/>
	<property name="ireport.x" value="242"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.report.vacina.dto.RelacaoVacinasNaoAplicadasDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.report.vacina.dto.RelacaoVacinasNaoAplicadasDTOParam"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.vacina.dto.RelacaoVacinasNaoAplicadasDTOParam.FormaApresentacao"/>
	<queryString language="SQL">
		<![CDATA[]]>
	</queryString>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="equipeMicroArea" class="br.com.ksisolucoes.vo.basico.EquipeMicroArea"/>
	<field name="faixaEtariaItem" class="br.com.ksisolucoes.vo.basico.FaixaEtariaItem"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="faixaEtaria" class="br.com.ksisolucoes.vo.basico.FaixaEtaria"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="vacinaCalendario" class="br.com.ksisolucoes.vo.vacina.VacinaCalendario"/>
	<field name="equipeArea" class="br.com.ksisolucoes.vo.basico.EquipeArea"/>
	<field name="agenteComunitario" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<variable name="usuarioCadsus_1" class="java.lang.Integer" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}]]></variableExpression>
	</variable>
	<variable name="usuarioCadsus_2" class="java.lang.Integer" resetType="Column" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}]]></variableExpression>
	</variable>
	<variable name="usuarioCadsus_3" class="java.lang.Integer" resetType="Group" resetGroup="FADefault" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}]]></variableExpression>
	</variable>
	<group name="FADefault">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<textField>
					<reportElement x="747" y="2" width="54" height="11" uuid="ae4251ef-856e-42b3-809b-396a92c7e146"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{usuarioCadsus_3}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="704" y="2" width="43" height="11" uuid="8345a293-b452-4406-b95b-5ac985206b88"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="703" y="1" width="99" height="1" uuid="74011bb7-90cb-42e4-9848-1230bc7c15e7"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[FormaApresentacao.VACINA.equals($P{FORMA_APRESENTACAO})
?
    $F{vacinaCalendario}.getTipoVacina().getCodigo()
:
    1L]]></groupExpression>
		<groupHeader>
			<band height="25">
				<textField>
					<reportElement x="1" y="14" width="175" height="10" uuid="985826df-b42d-4bcf-9c57-0f7e63ed7560"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="184" y="14" width="60" height="10" uuid="bbc19e09-5458-4cd3-9e87-a164c4143f53"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="258" y="14" width="65" height="10" uuid="6c276fe5-00a1-4594-8445-1b6b67e65b0b"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_telefone")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="24" width="802" height="1" uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="0" y="1" width="621" height="14" uuid="e199520c-c239-4028-a5d2-b36929bfb86b"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.VACINA.equals($P{FORMA_APRESENTACAO})
?
    Bundle.getStringApplication("rotulo_vacina") + ": " + $F{vacinaCalendario}.getTipoVacina().getDescricao()
:
   null]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="339" y="14" width="159" height="11" uuid="93cd79fb-90d8-48d9-a7a0-bb5da4e94732"/>
					<textElement verticalAlignment="Top">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco") + ": "]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="510" y="14" width="111" height="10" uuid="ccc607ea-a466-4e2c-9c53-b6a4c06180bf"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_area")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="639" y="14" width="152" height="10" uuid="800adb59-e916-4adb-9304-b7fb311932c7"/>
					<textElement>
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_agente_comunitario")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="14">
				<textField>
					<reportElement x="747" y="2" width="54" height="11" uuid="61c7b548-9c05-4407-af90-365421551d7d"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{usuarioCadsus_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="727" y="2" width="20" height="11" uuid="2bae9521-d953-4c9f-ab88-b051edb9372b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="726" y="1" width="76" height="1" uuid="8bfe4a50-cb96-4e1d-a685-d65ba678c114"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="21" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="1" y="0" width="175" height="10" uuid="c2f57e7f-b180-4855-9012-7fa5d4f14afa"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="184" y="0" width="60" height="10" uuid="0304db16-c378-43e7-8139-ac3751cbacb7"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdadeSimples()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="258" y="0" width="65" height="21" uuid="55e6881a-bf43-4c1c-8e5b-3dbb6a53d6c5"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
					<paragraph lineSpacing="Proportional" lineSpacingSize="1.3"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCelular() != null && $F{usuarioCadsus}.getTelefone() != null
?
    $F{usuarioCadsus}.getCelularFormatado() + "\n" + $F{usuarioCadsus}.getTelefoneFormatado()
:
    $F{usuarioCadsus}.getCelular() != null
    ?
        $F{usuarioCadsus}.getCelularFormatado()
    :
        $F{usuarioCadsus}.getTelefone() != null
        ? $F{usuarioCadsus}.getTelefoneFormatado()
        : null]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="339" y="0" width="159" height="21" uuid="1de3f22a-3b67-439d-a99b-66f0188a6a40"/>
				<textElement verticalAlignment="Bottom">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="510" y="0" width="111" height="21" uuid="16de5513-142e-4265-bfc8-eb4039330f94"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{equipeArea}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="639" y="0" width="152" height="21" uuid="2038c8c7-fc31-42be-b410-ac33edc8e73b"/>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{agenteComunitario}.getNome()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
