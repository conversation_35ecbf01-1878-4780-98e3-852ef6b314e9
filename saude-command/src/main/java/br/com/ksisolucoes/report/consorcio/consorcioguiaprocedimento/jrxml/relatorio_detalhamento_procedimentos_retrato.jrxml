<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_detalhamento_procedimentos" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="13187eaf-9762-4ee1-b075-9a13164bbb79">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="2.9230756500000035"/>
	<property name="ireport.x" value="18"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento.StatusGuiaProcedimento"/>
	<import value="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoProcedimentosDTOParam.FormaApresentacao"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoProcedimentosDTOParam.TipoResumo"/>
	<import value="br.com.ksisolucoes.report.ReportProperties"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<import value="br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem.StatusGuiaProcedimentoItem"/>
	<parameter name="formaApresentacao" class="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoProcedimentosDTOParam.FormaApresentacao"/>
	<parameter name="tipoResumo" class="br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoProcedimentosDTOParam.TipoResumo"/>
	<parameter name="tipoData" class="java.lang.String"/>
	<parameter name="separarPrestadores" class="java.lang.String"/>
	<field name="consorciado" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="codigo" class="java.lang.Long"/>
	<field name="prestador" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="paciente" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="descricaoStatus" class="java.lang.String"/>
	<field name="descricaoStatusItem" class="java.lang.String"/>
	<field name="data" class="java.util.Date"/>
	<field name="consorcioProcedimento" class="br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento"/>
	<field name="valorProcedimento" class="java.lang.Double"/>
	<field name="quantidade" class="java.lang.Long"/>
	<field name="nomePaciente" class="java.lang.String"/>
	<field name="cidade" class="br.com.ksisolucoes.vo.basico.Cidade"/>
	<field name="valorSUS" class="java.lang.Double"/>
	<field name="valorDif" class="java.lang.Double"/>
	<field name="status" class="java.lang.Long"/>
	<field name="statusItem" class="java.lang.Long"/>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="countFA" class="java.lang.Long" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{codigo}]]></variableExpression>
	</variable>
	<variable name="count" class="java.lang.Long" calculation="Count">
		<variableExpression><![CDATA[$F{codigo}]]></variableExpression>
	</variable>
	<variable name="totalizar" class="java.lang.Boolean" resetType="Column">
		<variableExpression><![CDATA[StatusGuiaProcedimentoItem.CANCELADA.value().equals($F{statusItem})
?
    StatusGuiaProcedimento.CANCELADA.value().equals($F{status})
    ? true
    : false
:
    true]]></variableExpression>
		<initialValueExpression><![CDATA[false]]></initialValueExpression>
	</variable>
	<variable name="valorFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? new Dinheiro($F{valorProcedimento}).multiplicar($F{quantidade}).doubleValue()
: 0D]]></variableExpression>
	</variable>
	<variable name="valorTotal" class="java.lang.Double" resetType="Group" resetGroup="report" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? new Dinheiro($F{valorProcedimento}).multiplicar($F{quantidade}).doubleValue()
: 0D]]></variableExpression>
	</variable>
	<variable name="valorTipoResumo" class="java.lang.Double" resetType="Group" resetGroup="TIPO_RESUMO" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? new Dinheiro($F{valorProcedimento}).multiplicar($F{quantidade}).doubleValue()
: 0D]]></variableExpression>
	</variable>
	<variable name="valor_final" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? new Dinheiro($F{valorProcedimento}).multiplicar($F{quantidade}).doubleValue()
: 0D]]></variableExpression>
	</variable>
	<variable name="valorSUSFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorSUS}
: 0D]]></variableExpression>
	</variable>
	<variable name="valorSUSTotal" class="java.lang.Double" resetType="Group" resetGroup="report" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorSUS}
: 0D]]></variableExpression>
	</variable>
	<variable name="valorSUSTipoResumo" class="java.lang.Double" resetType="Group" resetGroup="TIPO_RESUMO" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorSUS}
: 0D]]></variableExpression>
	</variable>
	<variable name="valorSUS_final" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorSUS}
: 0D]]></variableExpression>
	</variable>
	<variable name="valorComplFA" class="java.lang.Double" resetType="Group" resetGroup="FA" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorDif}
: 0D]]></variableExpression>
	</variable>
	<variable name="valorComplTotal" class="java.lang.Double" resetType="Group" resetGroup="report" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorDif}
: 0D]]></variableExpression>
	</variable>
	<variable name="valorComplTipoResumo" class="java.lang.Double" resetType="Group" resetGroup="TIPO_RESUMO" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorDif}
: 0D]]></variableExpression>
	</variable>
	<variable name="valorCompl_final" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$V{totalizar}
? $F{valorDif}
: 0D]]></variableExpression>
	</variable>
	<group name="final">
		<groupFooter>
			<band height="14">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="347" y="2" width="82" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1a6a1877-af3b-4217-85ea-71eb2c9d3546"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="513" y="2" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="e2426471-675f-4409-b89d-864ba03b1af1"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valor_final}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="379" y="2" width="175" height="1" uuid="d3451f9c-8ee5-47bd-b193-75ca7b0af2f8"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="471" y="2" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="6fd4b700-b0e1-465b-ac93-fd9761e60235"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorCompl_final}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="429" y="2" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="18100f81-197b-460d-a327-95e864029ff4"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorSUS_final}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="report">
		<groupExpression><![CDATA[$P{separarPrestadores}.equals(RepositoryComponentDefault.SIM)
?
    $F{prestador}.getDescricao()
:
    null]]></groupExpression>
		<groupHeader>
			<band height="16">
				<printWhenExpression><![CDATA[$P{separarPrestadores}.equals(RepositoryComponentDefault.SIM)]]></printWhenExpression>
				<rectangle radius="8">
					<reportElement mode="Transparent" x="0" y="0" width="555" height="16" uuid="c91df7bb-2282-403b-b0e3-2a7d44c156f4"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="0" width="555" height="16" forecolor="#000000" backcolor="#FFFFFF" uuid="cd1137d0-1051-4a04-9b25-5e031c6ab602"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{prestador}.getDescricao()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<printWhenExpression><![CDATA[$P{separarPrestadores}.equals(RepositoryComponentDefault.SIM)]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="347" y="0" width="82" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="4aa0d8d3-bfef-445b-a330-9ce0af515f0b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")+
" "+Bundle.getStringApplication("rotulo_prestador")+":"]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="513" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="097fb7c5-932a-4188-9449-47a1a0a72dc2"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTotal}]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="379" y="0" width="175" height="1" uuid="079516bd-9bc8-4a3d-bd09-1598652d3833"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="471" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="eba27880-87ba-4b08-b069-576ac44b6c9b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorComplTotal}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="429" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="bbb087cd-6845-4eeb-b2b7-c4079c182955"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorSUSTotal}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="FA">
		<groupExpression><![CDATA[FormaApresentacao.GUIA.equals($P{formaApresentacao})?
$F{codigo}:
FormaApresentacao.PROCEDIMENTO.equals($P{formaApresentacao})?
$F{consorcioProcedimento}.getDescricaoProcedimento():
FormaApresentacao.CONSORCIADO.equals($P{formaApresentacao})?
$F{consorciado}.getDescricao():
FormaApresentacao.SITUACAO.equals($P{formaApresentacao})?
$F{descricaoStatus}:
FormaApresentacao.CIDADE.equals($P{formaApresentacao})?
$F{cidade}.getDescricaoCidadeUf():
FormaApresentacao.DIARIO.equals($P{formaApresentacao})?
new SimpleDateFormat("dd/MM/yyyy").format($F{data}):
FormaApresentacao.MENSAL.equals($P{formaApresentacao})?
new SimpleDateFormat("MMMM", Bundle.getLocale()).format($F{data}):
""]]></groupExpression>
		<groupHeader>
			<band height="20">
				<rectangle radius="6">
					<reportElement mode="Transparent" x="0" y="7" width="555" height="13" uuid="909546be-2f75-479b-8995-0fed1c455549"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="7" width="555" height="13" forecolor="#000000" backcolor="#FFFFFF" uuid="9ce2519c-a610-4283-83c6-36e3f28e5ea8"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[FormaApresentacao.GUIA.equals($P{formaApresentacao})?
$F{codigo}.toString():
FormaApresentacao.PROCEDIMENTO.equals($P{formaApresentacao})?
$F{consorcioProcedimento}.getDescricaoProcedimento():
FormaApresentacao.CONSORCIADO.equals($P{formaApresentacao})?
$F{consorciado}.getDescricao():
FormaApresentacao.SITUACAO.equals($P{formaApresentacao})?
$F{descricaoStatus}:
FormaApresentacao.CIDADE.equals($P{formaApresentacao})?
$F{cidade}.getDescricaoCidadeUf():
FormaApresentacao.DIARIO.equals($P{formaApresentacao})?
new SimpleDateFormat("dd/MM/yyyy").format($F{data}):
FormaApresentacao.MENSAL.equals($P{formaApresentacao})?
new SimpleDateFormat("MMMM", Bundle.getLocale()).format($F{data}):
""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="12">
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="513" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="66c8690e-cf98-4d4a-9b44-6ef70325c9f5"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorFA}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="347" y="0" width="82" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="85344442-1dbd-4689-b72c-9a5a4abcad77"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")+
" "+$P{formaApresentacao}.toString()+":"]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="379" y="0" width="175" height="1" uuid="cdf35ac4-7c36-45ac-88c5-422d1622e887"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="471" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="9f4d82e9-9527-4bdd-851b-520bdffc8aaa"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorComplFA}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="429" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="da8e7ea0-f218-4c6e-97e0-4f3cb9e5f785"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorSUSFA}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="TIPO_RESUMO">
		<groupExpression><![CDATA[TipoResumo.GUIA.equals($P{tipoResumo})?
$F{codigo}.toString():
TipoResumo.PROCEDIMENTO.equals($P{tipoResumo})?
$F{consorcioProcedimento}.getDescricaoProcedimento():
TipoResumo.CONSORCIADO.equals($P{tipoResumo})?
$F{consorciado}.getDescricao():
TipoResumo.SITUACAO.equals($P{tipoResumo})?
$F{descricaoStatus}:
FormaApresentacao.CIDADE.equals($P{formaApresentacao})?
$F{cidade}.getDescricaoCidadeUf():
TipoResumo.DIARIO.equals($P{tipoResumo})?
new SimpleDateFormat("dd/MM/yyyy").format($F{data}):
TipoResumo.MENSAL.equals($P{tipoResumo})?
new SimpleDateFormat("MMMM", Bundle.getLocale()).format($F{data}):
""]]></groupExpression>
		<groupHeader>
			<band height="12">
				<printWhenExpression><![CDATA[! $P{formaApresentacao}.toString().equals($P{tipoResumo}.toString())]]></printWhenExpression>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="0" width="250" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="05620014-6b00-477c-bd17-d7fb45d2f08b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="8" isBold="false" isItalic="true" isUnderline="true" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[TipoResumo.GUIA.equals($P{tipoResumo})?
$F{codigo}.toString():
TipoResumo.PROCEDIMENTO.equals($P{tipoResumo})?
$F{consorcioProcedimento}.getDescricaoProcedimento():
TipoResumo.CONSORCIADO.equals($P{tipoResumo})?
$F{consorciado}.getDescricao():
TipoResumo.SITUACAO.equals($P{tipoResumo})?
$F{descricaoStatus}:
TipoResumo.CIDADE.equals($P{tipoResumo})?
$F{cidade}.getDescricaoCidadeUf():
TipoResumo.DIARIO.equals($P{tipoResumo})?
new SimpleDateFormat("dd/MM/yyyy").format($F{data}):
TipoResumo.MENSAL.equals($P{tipoResumo})?
new SimpleDateFormat("MMMM", Bundle.getLocale()).format($F{data}):
""]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="13">
				<printWhenExpression><![CDATA[! $P{formaApresentacao}.toString().equals($P{tipoResumo}.toString())]]></printWhenExpression>
				<line>
					<reportElement x="379" y="1" width="175" height="1" uuid="3426d6ec-**************-84639e913614"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="513" y="1" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="52fead7a-a21c-43a0-8226-bdd5797a86bb"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorTipoResumo}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="347" y="1" width="82" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d5415aa2-ecb2-447c-9428-9c631635e566"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total")+
" "+$P{tipoResumo}.toString()+":"]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="471" y="1" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="c3a1ec3b-1750-4dde-9a49-f8da95261007"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorComplTipoResumo}]]></textFieldExpression>
				</textField>
				<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="429" y="1" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="aba69c66-195b-420b-a7e6-f4616a222e7e"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{valorSUSTipoResumo}]]></textFieldExpression>
				</textField>
			</band>
		</groupFooter>
	</group>
	<group name="CABECALHO">
		<groupHeader>
			<band height="16">
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="0" y="2" width="40" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="45c7e331-6638-4391-bdbc-f359a64ec663"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_n_guia")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="14" width="555" height="1" uuid="84be006e-ef7b-4fb5-b70c-13a0ccb0c732"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="40" y="2" width="55" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="875f9397-85df-42c3-a89c-e4c4be15328b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{tipoData}]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="215" y="2" width="115" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="6612a746-a983-4038-9c6f-36a90abc8f05"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="330" y="2" width="35" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="f2e16c4f-8d81-4949-b875-0c8b24001940"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_guia")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="95" y="2" width="120" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="b7378473-3b2b-41c1-b05a-db1c3448b0ad"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_procedimento")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="513" y="2" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="881c00a5-b958-4c17-8ef8-2ffc39c1cf3c"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor_total")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="399" y="2" width="30" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d30b016c-0af4-4c34-bce1-5ba8a724d20b"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_quantidade_abv")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="471" y="2" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="db7ac5fe-87c4-45ca-a0b1-29238be053b3"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="429" y="2" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="6ff6f3b6-2ff9-488b-b669-1eca1aa00b97"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_valor")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="true">
					<reportElement key="textField-45" mode="Transparent" x="364" y="2" width="35" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="8607ac28-f089-4d35-b27d-e741c79fbc8d"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
						<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
						<paragraph lineSpacing="Single"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_situacao_item")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="12" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="0" y="0" width="40" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="3fcde699-27d4-4631-a317-d1cb4efae9dc"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{codigo}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="215" y="0" width="115" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="4076e909-ec85-4263-8426-12c0bb1e328c"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{nomePaciente}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="330" y="0" width="35" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d8409210-c2a3-473f-b40f-7ab45a5bfa33"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoStatus}]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="40" y="0" width="55" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="cf54bfd8-bd8e-43ca-871d-c28f7317b87d"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{data}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="95" y="0" width="120" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d3250f0e-8e67-4c6f-a78b-c092ee4dcf8e"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{consorcioProcedimento}.getDescricaoProcedimentoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="513" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="e36341da-3554-4195-b840-9ebd1a74765b"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[new Dinheiro($F{valorProcedimento}).multiplicar($F{quantidade}).doubleValue()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="399" y="0" width="30" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="5467307c-6084-4c6c-a391-416ff72ced03"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{quantidade}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="471" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d22224c1-b21b-4f47-a3d1-756fae6cf038"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorDif}]]></textFieldExpression>
			</textField>
			<textField pattern="###0.00;-###0.00" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="429" y="0" width="42" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="5f49ff16-bbea-4bb4-988c-bced404aab3c"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{valorSUS}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-45" mode="Transparent" x="364" y="0" width="35" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="1b7baa7b-dff7-4dc3-82f5-b7613fb8c2d0"/>
				<box topPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Top" rotation="None">
					<font fontName="Arial" size="7" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{descricaoStatusItem}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
