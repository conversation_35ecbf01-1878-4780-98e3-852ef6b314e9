package br.com.ksisolucoes.report.frota;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioRelacaoViagensDTO;
import br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioRelacaoViagensDTOParam;
import br.com.ksisolucoes.report.frota.query.QueryRelacaoViagens;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class RelacaoViagens extends AbstractReport<RelatorioRelacaoViagensDTOParam> {

    public RelacaoViagens(RelatorioRelacaoViagensDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        return "/br/com/ksisolucoes/report/frota/jrxml/relacao_viagens.jrxml";
    }
    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioRelacaoViagensDTO proxy = Lambda.on(RelatorioRelacaoViagensDTO.class);
        columns.put(Bundle.getStringApplication("rotulo_codigo"), proxy.getRoteiroViagem().getCodigo());
        columns.put(Bundle.getStringApplication("rotulo_cidade_codigo"), proxy.getRoteiroViagem().getCidade().getCodigo());
        columns.put(Bundle.getStringApplication("rotulo_cidade"), proxy.getRoteiroViagem().getCidade().getDescricao());
        columns.put(Bundle.getStringApplication("rotulo_estado_codigo"), proxy.getRoteiroViagem().getCidade().getEstado().getCodigo());
        columns.put(Bundle.getStringApplication("rotulo_estado"), proxy.getRoteiroViagem().getCidade().getEstado().getSigla());
        columns.put(Bundle.getStringApplication("rotulo_destino"), proxy.getRoteiroViagemPassageiro().getDestino());
        columns.put(Bundle.getStringApplication("rotulo_veiculo_codigo"), proxy.getRoteiroViagem().getVeiculo().getCodigo());
        columns.put(Bundle.getStringApplication("rotulo_veiculo"), proxy.getRoteiroViagem().getVeiculo().getDescricaoFormatado());
        columns.put(Bundle.getStringApplication("rotulo_motorista"), proxy.getRoteiroViagem().getMotorista().getNome());
        columns.put(Bundle.getStringApplication("rotulo_codigo_motorista"), proxy.getRoteiroViagem().getMotorista().getCodigo());
        columns.put(Bundle.getStringApplication("rotulo_nome_paciente"), proxy.getRoteiroViagemPassageiro().getNomePaciente());
        columns.put(Bundle.getStringApplication("rotulo_hora_consulta"), proxy.getRoteiroViagemPassageiro().getHorarioFormatado());

        return columns;
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelacaoViagens();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_viagens");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return getParam().getTipoRelatorio();
    }
}