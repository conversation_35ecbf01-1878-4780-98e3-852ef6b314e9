/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.report.vigilancia.query.QueryFichaAcidenteTrabalhoTranstornoMental;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.FichaInvestigacaoAgravoDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;

import java.util.LinkedHashMap;

/**
 * <AUTHOR> Lucas
 */

public class ImpressaoFichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMental extends AbstractReport<FichaInvestigacaoAgravoDTOParam> {

    private QueryFichaAcidenteTrabalhoTranstornoMental query;

    public ImpressaoFichaInvestigacaoAgravoAcidenteTrabalhoTranstornoMental(FichaInvestigacaoAgravoDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        if(query == null){
            query = new QueryFichaAcidenteTrabalhoTranstornoMental();
        }
        return query;
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columnsMap = new LinkedHashMap<>(((QueryFichaAcidenteTrabalhoTranstornoMental)getQuery()).getMapeamentoPlanilhaBase());

        columnsMap.put("Ocupação", "_31_ocupacao");

        columnsMap.put("Situacao No Mercado de Trabalho", "_32_situacao_mercado_trab");
        columnsMap.put("Tempo de Trabalho na Ocupacao", "_33_tempo_trabalho");
        columnsMap.put("Tempo de Trabalho na Ocupacao Unidade de Medida", "_33_tempo_trabalho_um");

        columnsMap.put("CNPJ empresa", "_34_cnpj");
        columnsMap.put("Nome da empresa", "_35_empresa");
        columnsMap.put("Atividade/CNAE", "_36_cnae");

        columnsMap.put("Estado Sigla", "_37_uf");
        columnsMap.put("Município", "_38_municipio");
        columnsMap.put("Código IBGE", "_38_ibge");
        columnsMap.put("Distrito", "_39_distrito");
        columnsMap.put("Bairro", "_40_bairro");
        columnsMap.put("Endereço", "_41_endereco");
        columnsMap.put("Número", "_42_numero");
        columnsMap.put("Ponto de Referência ", "_43_ponto_ref");
        columnsMap.put("Telefone da Empresa", "_44_telefone");

        columnsMap.put("Empresa Tercerizada", "_45_empresa_terceirizada");

        columnsMap.put("Tempo de Exposição Agente de Risco", "_46_tempo_exposicao_agente_risco");
        columnsMap.put("Tempo de Exposição Agente de Risco Unidade de Medida", "_46_tempo_exposicao_agente_risco_um");
        columnsMap.put("Regime de Tratamento", "_47_regime_tratamento");
        columnsMap.put("C.I.D.", "_48_cid");
        columnsMap.put("Hábitos Alcoólicos", "_49_habitos_alcool");
        columnsMap.put("Hábitos com Drogas Psicoativas", "_49_habitos_drogas_psicoativas");
        columnsMap.put("Hábitos com Psicofármacos", "_49_habitos_psicofarmacos");
        columnsMap.put("Hábito de  Fumar", "_50_habitos_fumar");
        columnsMap.put("Tempo de Exposição ao Tabaco", "_51_tempo_exposicao_tabaco");
        columnsMap.put("Tempo de Exposição ao Tabaco Unidade de Medida", "_51_tempo_exposicao_tabaco_um");

        columnsMap.put("Afastamento da Situação de Desgaste Mental", "_52_conduta_geral_afastamente_situacao_desgaste_mental");
        columnsMap.put("Adoção de proteção Individual", "_52_conduta_geral_potecao_individual");
        columnsMap.put("Mudança na Organização do Trabalho", "_52_conduta_geral_mudanca_organizacao_trabalho");
        columnsMap.put("Nenhuma medida", "_52_conduta_geral_nenhum");
        columnsMap.put("Adoção de proteção Coletiva", "_52_conduta_geral_protecao_coletiva");
        columnsMap.put("Afastamento do Local de Trabalho", "_52_conduta_geral_afastamento_local_trabalho");
        columnsMap.put("Outros", "_52_conduta_geral_outros");
        columnsMap.put("Outros do Trabalho Com a Mesma Doença", "_53_outros_mesma_doenca_trabalho");
        columnsMap.put("O paciente foi encaminhado a um Centro de Atenção Psicossocial (CAPES) ou no SUS", "_54_capes");
        columnsMap.put("Evolução do Caso", "_55_evolucao_caso");
        columnsMap.put("Data do Óbito", "_56_obito");
        columnsMap.put("Foi emitido o CAT", "_57_cat");
        columnsMap.put("Observação", "_observacao");

        return columnsMap;
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/pdf/ficha_investigacao_acidente_trabalho_transtorno_mental.pdf";
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_acidente_trabalho_tanstorno_mental");
    }

}
