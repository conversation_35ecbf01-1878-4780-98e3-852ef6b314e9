/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioResumoPedidoEstoqueDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioResumoPedidoEstoqueDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.entradas.estoque.PedidoTransferenciaItem;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioResumoPedidoEstoque extends CommandQuery<QueryRelatorioResumoPedidoEstoque>
        implements ITransferDataReport<QueryRelatorioResumoPedidoEstoqueDTOParam, QueryRelatorioResumoPedidoEstoqueDTO>{

    private Collection<QueryRelatorioResumoPedidoEstoqueDTO> result;
    private QueryRelatorioResumoPedidoEstoqueDTOParam param;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelectAndGroup("empresaOrigem.codigo","unidadeOrigem.codigo");
        hql.addToSelectAndGroup("empresaOrigem.referencia","unidadeOrigem.referencia");
        hql.addToSelectAndGroup("empresaOrigem.descricao","unidadeOrigem.descricao");

        hql.addToSelectAndGroup("grupoProduto.codigo","subGrupo.roGrupoProduto.codigo");
        hql.addToSelectAndGroup("grupoProduto.descricao","subGrupo.roGrupoProduto.descricao");

        hql.addToSelectAndGroup("grupoProduto.codigo","subGrupo.id.codigoGrupoProduto");
        hql.addToSelectAndGroup("subGrupo.id.codigo","subGrupo.id.codigo");
        hql.addToSelectAndGroup("subGrupo.descricao","subGrupo.descricao");

        
        hql.addToSelectAndGroup("deposito.codigo","deposito.codigo");
        hql.addToSelectAndGroup("deposito.descricao","deposito.descricao");

        hql.addToSelectAndGroup("produto.codigo","produto.codigo");
        hql.addToSelectAndGroup("produto.referencia","produto.referencia");
        hql.addToSelectAndGroup("produto.descricao","produto.descricao");
        
        hql.addToSelectAndGroup("unidade.codigo","produto.unidade.codigo");
        hql.addToSelectAndGroup("unidade.unidade","produto.unidade.unidade");
        hql.addToSelectAndGroup("unidade.descricao","produto.unidade.descricao");

        hql.addToSelect("sum(pedidoTransferenciaItem.quantidade)","quantidadeSolicitada");
        hql.addToSelect("sum(estoqueEmpresa.estoqueFisico - estoqueEmpresa.estoqueReservado)","estoqueDisponivel");

        hql.setTypeSelect(QueryRelatorioResumoPedidoEstoqueDTO.class.getName());
        hql.addToFrom("PedidoTransferenciaItem  pedidoTransferenciaItem "
                + " left join pedidoTransferenciaItem.pedidoTransferencia pedidoTransferencia"
                + " left join pedidoTransferencia.empresaOrigem empresaOrigem"
                + " left join pedidoTransferencia.deposito deposito"
                + " left join pedidoTransferenciaItem.produto produto"
                + " left join produto.unidade unidade"
                + " left join produto.subGrupo subGrupo"
                + " left join subGrupo.roGrupoProduto grupoProduto ");
        hql.addToFrom("EstoqueEmpresa estoqueEmpresa "
                + " left join estoqueEmpresa.id.produto produtoEstoque "
                + " left join estoqueEmpresa.id.empresa empresaEstoque ");

        hql.addToWhereWhithAnd("produto = produtoEstoque");
        hql.addToWhereWhithAnd("empresaOrigem = empresaEstoque");

        hql.addToWhereWhithAnd("empresaOrigem in ",this.param.getEmpresas());
        hql.addToWhereWhithAnd("grupoProduto = ",this.param.getGrupoProdutoSubGrupo());
        hql.addToWhereWhithAnd("subGrupo = ",this.param.getSubGrupo());
        hql.addToWhereWhithAnd("deposito in ",this.param.getDepositos());

        hql.addToWhereWhithAnd("pedidoTransferenciaItem.status = ",PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value());

        hql.addToOrder("empresaOrigem.referencia");

        if (QueryRelatorioResumoPedidoEstoqueDTOParam.FormApresentacao.GRUPO_SUB_GRUPO_PRODUTO.equals(this.param.getFormApresentacao())) {
            hql.addToOrder("grupoProduto.codigo");
            hql.addToOrder("subGrupo.id.codigo");
        } else if (QueryRelatorioResumoPedidoEstoqueDTOParam.FormApresentacao.DEPOSITO.equals(this.param.getFormApresentacao())) {
            hql.addToOrder("deposito.codigo");
        }

        if (RepositoryComponentDefault.SIM.equals(this.param.getProdutoSemEstoque())) {
            hql.addToHavingAnd("sum(estoqueEmpresa.estoqueFisico - estoqueEmpresa.estoqueReservado) - sum(pedidoTransferenciaItem.quantidade) < 0");
        } else if (RepositoryComponentDefault.NAO.equals(this.param.getProdutoSemEstoque())) {
            hql.addToHavingAnd("sum(estoqueEmpresa.estoqueFisico - estoqueEmpresa.estoqueReservado) - sum(pedidoTransferenciaItem.quantidade) > 0");
        }


    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public void setDTOParam(QueryRelatorioResumoPedidoEstoqueDTOParam param) {
        this.param = param;
    }

    @Override
    public Collection<QueryRelatorioResumoPedidoEstoqueDTO> getResult() {
        return this.result;
    }

}
