package br.com.ksisolucoes.report.cadsus;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryRelacaoFamiliasDTO;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.QueryRelacaoFamiliasDTOParam;
import br.com.ksisolucoes.report.cadsus.query.QueryRelacaoFamilias;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 */
public class RelacaoFamilias extends AbstractReport<QueryRelacaoFamiliasDTOParam> {

    public RelacaoFamilias(QueryRelacaoFamiliasDTOParam param) {
        super(param);
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();

        QueryRelacaoFamiliasDTO proxy = Lambda.on(QueryRelacaoFamiliasDTO.class);

        if (QueryRelacaoFamiliasDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(this.param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_faixa_etaria"), proxy.getFaixaEtariaItem().getDescricao());
        } else if (QueryRelacaoFamiliasDTOParam.FormaApresentacao.DOENCA.equals(this.param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_doenca"), proxy.getDoenca().getDescricao());
        } else if (QueryRelacaoFamiliasDTOParam.FormaApresentacao.ENDERECO.equals(this.param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_endereco"), proxy.getEnderecoUsuarioCadsus().getEnderecoFormatadoSemNumero());
            map.put(Bundle.getStringApplication("rotulo_numero_abv"), proxy.getEnderecoUsuarioCadsus().getNumeroLogradouro());
        } else if (QueryRelacaoFamiliasDTOParam.FormaApresentacao.AREA.equals(this.param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_area"), proxy.getEquipeMicroArea().getEquipeArea().getDescricao());
            map.put(Bundle.getStringApplication("rotulo_microarea"), proxy.getEquipeMicroArea().getMicroArea());
            map.put(Bundle.getStringApplication("rotulo_profissional"), proxy.getProfissional().getDescricaoFormatado());
        } else if (QueryRelacaoFamiliasDTOParam.FormaApresentacao.CONDICAO_SITUACAO_ESUS.equals(this.param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_condicao_situacao_esus"), proxy.getDoenca().getDescricaoCondicaoEsus());
        }

        map.put(Bundle.getStringApplication("rotulo_paciente"), proxy.getUsuarioCadsus().getNomeSocial());
        map.put(Bundle.getStringApplication("rotulo_data_nascimento_abv"), proxy.getUsuarioCadsus().getDataNascimentoFormatado());
        map.put(Bundle.getStringApplication("rotulo_idade"), proxy.getUsuarioCadsus().getDescricaoIdade());
        map.put(Bundle.getStringApplication("rotulo_sexo"), proxy.getUsuarioCadsus().getSexoFormatado());
        map.put(Bundle.getStringApplication("rotulo_telefone"), proxy.getUsuarioCadsus().getTelefoneFormatado());
        map.put(Bundle.getStringApplication("rotulo_celular"), proxy.getUsuarioCadsus().getCelularFormatado());
        map.put(Bundle.getStringApplication("rotulo_mae"), proxy.getUsuarioCadsus().getNomeMae());
        map.put(Bundle.getStringApplication("rotulo_situacao"), proxy.getUsuarioCadsus().getDescricaoSituacao());

        if (!QueryRelacaoFamiliasDTOParam.FormaApresentacao.AREA.equals(this.param.getFormaApresentacao())) {
            map.put(Bundle.getStringApplication("rotulo_area"), proxy.getEquipeMicroArea().getEquipeArea().getDescricao());
            map.put(Bundle.getStringApplication("rotulo_microarea"), proxy.getEquipeMicroArea().getMicroArea());
            map.put(Bundle.getStringApplication("rotulo_profissional"), proxy.getProfissional().getDescricaoFormatado());
        }

        return map;
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/cadsus/jrxml/relacao_familias.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        return new QueryRelacaoFamilias();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("relacao_familias");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return this.param.getTipoArquivo();
    }
}
