package br.com.ksisolucoes.report.hospital.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioFaturamentoDTO;
import br.com.ksisolucoes.report.hospital.interfaces.dto.RelatorioFaturamentoDTOParam;
import br.com.ksisolucoes.report.prontuario.procedimento.query.QueryRelatorioConferenciaBPA;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoFinanciamento;
import static ch.lambdaj.Lambda.on;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioFaturamento extends CommandQuery implements ITransferDataReport<RelatorioFaturamentoDTOParam, RelatorioFaturamentoDTO> {

    private RelatorioFaturamentoDTOParam param;
    private List<RelatorioFaturamentoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        RelatorioFaturamentoDTO proxy = on(RelatorioFaturamentoDTO.class);

        hql.addToSelect("icp.codigo", path(proxy.getItemContaPaciente().getCodigo()));
        hql.addToSelect("icp.quantidade", path(proxy.getItemContaPaciente().getQuantidade()));
        hql.addToSelect("icp.precoUnitario", path(proxy.getItemContaPaciente().getPrecoUnitario()));

        hql.addToSelect("pc.codigo", path(proxy.getProcedimento().getCodigo()));
        hql.addToSelect("pc.referencia", path(proxy.getProcedimento().getReferencia()));
        hql.addToSelect("pc.descricao", path(proxy.getProcedimento().getDescricao()));

        boolean isDetalhado = RelatorioFaturamentoDTOParam.TipoRelatorio.DETALHADO.equals(this.param.getTipoRelatorio());
        if (isDetalhado) {
            hql.addToSelect("at.codigo", path(proxy.getAtendimento().getCodigo()));
            hql.addToSelect("atp.codigo", path(proxy.getItemContaPaciente().getContaPaciente().getAtendimentoInformacao().getAtendimentoPrincipal().getCodigo()));

            hql.addToSelect("paciente.codigo", path(proxy.getPaciente().getCodigo()));
            hql.addToSelect("paciente.nome", path(proxy.getPaciente().getNome()));

            hql.addToSelect("icp.dataLancamento", path(proxy.getItemContaPaciente().getDataLancamento()));
        }

        hql.setTypeSelect(RelatorioFaturamentoDTO.class.getName());

        hql.addToFrom("ItemContaPaciente icp "
                + " left join icp.contaPaciente cp"
                + " left join cp.atendimentoInformacao ai"
                + " left join ai.atendimentoPrincipal atp"
                + " left join cp.usuarioCadsus paciente"
                + " left join cp.atendimentoInformacao ati"
                + " left join cp.usuarioCadsus paciente"
                + " left join icp.procedimento pc"
                + " left join icp.profissional profissional"
                + " left join icp.cbo cbo"
                + " left join icp.empresaFaturamento empresaFaturamento"
                + " left join icp.atendimento at"
                + " left join at.empresa setor");

        hql.addToWhereWhithAnd("cp.competenciaAtendimento = ", this.param.getDataCompetencia());
        hql.addToWhereWhithAnd("profissional = ", this.param.getProfissional());
        hql.addToWhereWhithAnd("empresaFaturamento = ", this.param.getPrestador());
        hql.addToWhereWhithAnd("setor = ", this.param.getSetor());
        hql.addToWhereWhithAnd("cp.convenio = ", this.param.getConvenio());
        hql.addToWhereWhithAnd("cbo.cbo = ", this.param.getTabelaCbo());

        if (!param.getSituacao().equals(RelatorioFaturamentoDTOParam.Status.TODOS.value())) {
            hql.addToWhereWhithAnd("cp.status = ", this.param.getSituacao());
        }

        hql.addToWhereWhithAnd("icp.status <> ", ItemContaPaciente.Status.CANCELADO.value());

        if (!BpaProcesso.TipoFinanciamento.AMBOS.equals(param.getTipoFinanciamento())) {
            try {
                Date dataCompProcedimento = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);

                HQLHelper hqlSub = hql.getNewInstanceSubQuery();
                hqlSub.addToSelect("1");
                hqlSub.addToFrom("ProcedimentoCompetencia procedimentoCompetencia left join procedimentoCompetencia.financiamento financiamento");
                hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.procedimento.codigo = pc.codigo");
                hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.dataCompetencia = ", dataCompProcedimento);
                hqlSub.addToWhereWhithAnd("financiamento.dataCompetencia = ", dataCompProcedimento);

                if (BpaProcesso.TipoFinanciamento.PAB.equals(param.getTipoFinanciamento())) {
                    hqlSub.addToWhereWhithAnd("financiamento.codigo = ", ProcedimentoFinanciamento.PAB);
                } else if (BpaProcesso.TipoFinanciamento.MAC.equals(param.getTipoFinanciamento())) {
                    hqlSub.addToWhereWhithAnd("financiamento.codigo <> ", ProcedimentoFinanciamento.PAB);
                }

                hql.addToWhereWhithAnd("exists(" + hqlSub.getQuery() + ")");
            } catch (ValidacaoException ex) {
                Logger.getLogger(QueryRelatorioConferenciaBPA.class.getName()).log(Level.SEVERE, null, ex);
            }
        }

        if (!this.param.getFormaApresentacao().equals(RelatorioFaturamentoDTOParam.FormaApresentacao.GERAL)) {
            if (this.param.getFormaApresentacao().equals(RelatorioFaturamentoDTOParam.FormaApresentacao.SETOR)) {
                hql.addToSelect("setor.codigo", path(proxy.getSetor().getCodigo()));
                hql.addToSelect("setor.descricao", path(proxy.getSetor().getDescricao()));
                hql.addToOrder("setor.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
            } else if (this.param.getFormaApresentacao().equals(RelatorioFaturamentoDTOParam.FormaApresentacao.PROFISSIONAL)) {
                hql.addToSelect("profissional.codigo", path(proxy.getProfissional().getCodigo()));
                hql.addToSelect("profissional.nome", path(proxy.getProfissional().getNome()));
                hql.addToOrder("profissional.nome" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
            } else if (this.param.getFormaApresentacao().equals(RelatorioFaturamentoDTOParam.FormaApresentacao.PRESTADOR)) {
                hql.addToSelect("empresaFaturamento.codigo", path(proxy.getPrestador().getCodigo()));
                hql.addToSelect("empresaFaturamento.descricao", path(proxy.getPrestador().getDescricao()));
                hql.addToOrder("empresaFaturamento.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
            } else if (this.param.getFormaApresentacao().equals(RelatorioFaturamentoDTOParam.FormaApresentacao.PROCEDIMENTO)) {
                hql.addToOrder("pc.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
            } else if (this.param.getFormaApresentacao().equals(RelatorioFaturamentoDTOParam.FormaApresentacao.CBO)) {
                hql.addToSelect("cbo.cbo", path(proxy.getTabelaCbo().getCbo()));
                hql.addToSelect("cbo.descricao", path(proxy.getTabelaCbo().getDescricao()));
                hql.addToOrder("cbo.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
            }
        } else if (this.param.getFormaApresentacao().equals(RelatorioFaturamentoDTOParam.FormaApresentacao.PACIENTE)) {
            if (!isDetalhado) {
                hql.addToSelect("paciente.codigo", path(proxy.getPaciente().getCodigo()));
                hql.addToSelect("paciente.nome", path(proxy.getPaciente().getNome()));
            }
            hql.addToOrder("paciente.nome" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
        }

        if (isDetalhado) {
            if (param.getOrdenacao() != null && param.getOrdenacao().equals(RelatorioFaturamentoDTOParam.Ordenacao.DATAATENDIMENTO.getDescricao())) {
                hql.addToOrder("icp.dataLancamento");
            } else if (param.getOrdenacao() != null && param.getOrdenacao().equals(RelatorioFaturamentoDTOParam.Ordenacao.PACIENTE.getDescricao())) {
                hql.addToOrder("paciente.nome");
            }
            hql.addToOrder("at.codigo");
            hql.addToOrder("icp.quantidade");
            hql.addToOrder("icp.codigo");
        } else {
            hql.addToOrder("pc.descricao" + QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_FIRST);
        }

    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioFaturamentoDTOParam param) {
        this.param = param;
    }
}
