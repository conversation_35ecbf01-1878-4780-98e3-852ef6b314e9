/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento.exame;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.GraficoDistribuicaoExameAgendamentosDTOParam;
import br.com.ksisolucoes.report.agendamento.exame.query.QueryGraficoDistribuicaoExameAgendamentos;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public class GraficoDistribuicaoExameAgendamentos extends AbstractReport<GraficoDistribuicaoExameAgendamentosDTOParam>{

    public GraficoDistribuicaoExameAgendamentos(GraficoDistribuicaoExameAgendamentosDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        addParametro("tipoGrafico", getParam().getTipoGrafico());
        addParametro("tipoDado", getParam().getTipoDado());

        return "/br/com/ksisolucoes/report/agendamento/exame/jrxml/grafico_distribuicao_exame_agendamentos.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_distribuicao_agendamentos");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryGraficoDistribuicaoExameAgendamentos();
    }

    @Override
    protected void customDTOParam(GraficoDistribuicaoExameAgendamentosDTOParam param) throws ValidacaoException {
        if(param.getPeriodo().getDataInicial() == null || param.getPeriodo().getDataFinal() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_periodo"));
        }
        if(param.getFormaApresentacao().equals(GraficoDistribuicaoExameAgendamentosDTOParam.FormaApresentacao.FAIXA_ETARIA)
                && param.getFaixaEtaria() == null){
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_faixa_etaria"));
        }
    }

}
