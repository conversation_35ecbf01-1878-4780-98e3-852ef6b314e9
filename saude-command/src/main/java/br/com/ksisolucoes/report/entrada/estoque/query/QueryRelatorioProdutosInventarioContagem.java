/*
 * QueryRelacaoEstoqueMinimo.java
 *
 * Created on 21 de Outubro de 2005, 14:30
 *
 * To change this template, choose Tools | Options and locate the template under
 * the Source Creation and Management node. Right-click the template and choose
 * Open. You can then make changes to the template in the Source Editor.
 */
package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioProdutosInventarioContagemDTO;
import br.com.ksisolucoes.vo.entradas.estoque.ControleInventario;
import br.com.ksisolucoes.vo.entradas.estoque.Inventario;

import java.util.List;
import java.util.Map;


/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioProdutosInventarioContagem extends CommandQuery<QueryRelatorioProdutosInventarioContagem> implements ITransferDataReport<Inventario, RelatorioProdutosInventarioContagemDTO> {

    private Inventario param;
    private List<RelatorioProdutosInventarioContagemDTO> listRelatorioRelacaoInventario;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("empresa.referencia", "codigoEmpresa");
        hql.addToSelect("empresa.descricao", "descricaoEmpresa");

        hql.addToSelect("grupoProduto.codigo", "codigoGrupoProduto");
        hql.addToSelect("grupoProduto.descricao", "descricaoGrupoProduto");

        hql.addToSelect("subGrupo.id.codigo", "codigoSubGrupo");
        hql.addToSelect("subGrupo.descricao", "descricaoSubGrupo");

        hql.addToSelect("produto.referencia", "codigoProduto");
        hql.addToSelect("produto.descricao", "descricaoProduto");

        hql.addToSelect("unidade.unidade", "unidadeProduto");

        hql.addToSelect("localizacaoEstrutura.codigo", "codigoLocalizacao");
        hql.addToSelect("localizacaoEstrutura.mascara", "descricaoLocalizacao");

        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        hqlSub.addToSelect("ee.flagAtivo");
        hqlSub.addToFrom("EstoqueEmpresa ee");
        hqlSub.addToWhereWhithAnd("ee.id.empresa = empresa");
        hqlSub.addToWhereWhithAnd("ee.id.produto = produto");
        hql.addToSelect("("+hqlSub.getQuery()+")","flagAtivoEstoqueEmpresa");

//        HQLHelper hqlSub1 = hql.getNewInstanceSubQuery();
//        hqlSub1.addToSelect("ge.estoqueFisico");
//        hqlSub1.addToFrom("GrupoEstoque ge");
//        hqlSub1.addToWhereWhithAnd("ge.id.codigoDeposito = ci.deposito");
//        hqlSub1.addToWhereWhithAnd("ge.id.grupo = ci.grupoEstoque");
//        hqlSub1.addToWhereWhithAnd("ge.id.estoqueEmpresa.id.empresa = empresa");
//        hqlSub1.addToWhereWhithAnd("ge.id.estoqueEmpresa.id.produto = produto");

        hql.addToFrom(" ControleInventario ci"
                + " left join ci.id.empresa empresa "
                + " left join ci.produto produto "
                + " left join produto.subGrupo subGrupo "
                + " left join subGrupo.roGrupoProduto grupoProduto "
                + " left join produto.unidade unidade "
                + " left join ci.inventario inventario "
                + " left join ci.inventario inventario "
                + " left join ci.localizacaoEstrutura localizacaoEstrutura ");

        hql.setTypeSelect(RelatorioProdutosInventarioContagemDTO.class.getName());

        hql.addToWhereWhithAnd("ci.id.empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("upper(localizacaoEstrutura.descricaoEstrutura) like '" + param.getLocalizacaoEstrutura().getDescricaoEstrutura().toUpperCase() + "%'");
        hql.addToWhereWhithAnd("inventario.codigo = ", this.param.getCodigo());
        hql.addToWhereWhithAnd("ci.status= ", ControleInventario.STATUS_ABERTO);

//        hql.addToWhereWhithAnd("SUM("+hqlSub.getQuery()+") != ","ci.quantidade");

        hql.addToOrder("empresa.descricao");
        hql.addToOrder("grupoProduto.codigo");
        hql.addToOrder("subGrupo.id.codigo");
        hql.addToOrder("produto.descricao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.listRelatorioRelacaoInventario = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioProdutosInventarioContagemDTO> getResult() {
        return listRelatorioRelacaoInventario;
    }

    @Override
    public void setDTOParam(Inventario param) {
        this.param = param;
    }

}

