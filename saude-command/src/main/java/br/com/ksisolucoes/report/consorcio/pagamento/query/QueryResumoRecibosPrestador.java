package br.com.ksisolucoes.report.consorcio.pagamento.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoRecibosPrestadorDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioResumoRecibosPrestadorDTOParam;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.consorcio.ReciboConsorcio;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryResumoRecibosPrestador extends CommandQuery<QueryResumoRecibosPrestador> implements ITransferDataReport<RelatorioResumoRecibosPrestadorDTOParam, RelatorioResumoRecibosPrestadorDTO> {

    private RelatorioResumoRecibosPrestadorDTOParam param;
    private List<RelatorioResumoRecibosPrestadorDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        try {
            Date dataCompProcedimento = (Date) CargaBasicoPadrao.getInstance().getParametroPadrao().getPropertyValue(Parametro.PROP_DATA_COMPETENCIA_PROCEDIMENTO);

            hqlSub.addToSelect("(case when procedimentoCompetencia.valorServicoAmbulatorial > 0 then procedimentoCompetencia.valorServicoAmbulatorial else procedimentoCompetencia.valorServicoProfissional end)");
            hqlSub.addToFrom("ProcedimentoCompetencia procedimentoCompetencia");
            hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.procedimento.codigo = procedimento.codigo");
            hqlSub.addToWhereWhithAnd("procedimentoCompetencia.id.dataCompetencia = ", dataCompProcedimento);

        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        hql.addToSelectAndGroup("consorciado.codigo", "consorciado.codigo");
        hql.addToSelectAndGroupAndOrder("consorciado.descricao", "consorciado.descricao");
        hql.addToSelectAndGroup("empresaPrestador.codigo", "prestador.codigo");
        hql.addToSelectAndGroupAndOrder("empresaPrestador.descricao", "prestador.descricao");
        hql.addToSelectAndGroup("empresaPrestador.flagFisicaJuridica", "prestador.flagFisicaJuridica");

        hql.addToSelect("sum(consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidadeAplicacao + coalesce(consorcioGuiaProcedimentoItem.valorProcedimentoImposto, 0))", "totalPrestador");
        hql.addToSelect("sum(consorcioGuiaProcedimentoItem.quantidadeAplicacao)", "quantidade");

        hql.addToSelect("sum(coalesce((" + hqlSub.getQuery() + "), 0)* consorcioGuiaProcedimentoItem.quantidadeAplicacao)", "valorSUS");

        hql.addToSelect("sum(CASE WHEN empresaPrestador.flagFisicaJuridica = 'F'"
                + "               THEN (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidadeAplicacao + coalesce(consorcioGuiaProcedimentoItem.valorProcedimentoImposto, 0))"
                + "               ELSE 0 END)", "totalPf");

        hql.addToSelect("sum(CASE WHEN empresaPrestador.flagFisicaJuridica = 'J'"
                + "               THEN (consorcioGuiaProcedimentoItem.valorProcedimento * consorcioGuiaProcedimentoItem.quantidadeAplicacao + coalesce(consorcioGuiaProcedimentoItem.valorProcedimentoImposto, 0))"
                + "               ELSE 0 END)", "totalPj");

        hql.setTypeSelect(RelatorioResumoRecibosPrestadorDTO.class.getName());

        hql.addToFrom("ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem" +
                " left join consorcioGuiaProcedimentoItem.consorcioGuiaProcedimento consorcioGuiaProcedimento" +
                " left join consorcioGuiaProcedimentoItem.consorcioProcedimento consorcioProcedimento" +
                " left join consorcioProcedimento.procedimento procedimento" +
                " left join consorcioGuiaProcedimento.reciboConsorcio reciboConsorcio" +
                " left join consorcioGuiaProcedimento.consorcioPrestador consorcioPrestador" +
                " left join consorcioPrestador.empresaPrestador empresaPrestador" +
                " left join reciboConsorcio.empresa consorciado");

        hql.addToWhereWhithAnd("reciboConsorcio.status = ", ReciboConsorcio.Status.EMITIDO.value());
        hql.addToWhereWhithAnd("consorciado = ", param.getConsorciado());
        hql.addToWhereWhithAnd("empresaPrestador = ", param.getPrestador());
        hql.addToWhereWhithAnd("empresaPrestador.flagFisicaJuridica = ", param.getTipoPessoaPrestador());
        hql.addToWhereWhithAnd("reciboConsorcio.dataRecibo ", param.getPeriodo());

        hql.addToWhereWhithAnd("EXISTS(SELECT 1"
                + "                      FROM ReciboConsorcioGuia reciboConsorcioGuia"
                + "                     WHERE reciboConsorcioGuia.reciboConsorcio.codigo = reciboConsorcio.codigo)");
    }

    @Override
    public void setDTOParam(RelatorioResumoRecibosPrestadorDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioResumoRecibosPrestadorDTO> getResult() {
        return this.result;
    }

}
