package br.com.ksisolucoes.report.unidadesaude.relatorio;

import br.com.celk.report.unidadesaude.interfaces.dto.RelatorioPlanilhaDiarreiaDTO;
import br.com.celk.report.unidadesaude.interfaces.dto.RelatorioPlanilhaDiarreiaDTOParam;
import br.com.celk.report.unidadesaude.interfaces.dto.RelatorioPlanilhaDiarreiaDTOResumido;
import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.unidadesaude.relatorio.query.QueryRelatorioPlanilhaDiarreia;
import br.com.ksisolucoes.report.unidadesaude.relatorio.query.QueryRelatorioPlanilhaDiarreiaResumido;
import br.com.ksisolucoes.util.Bundle;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPlanilhaDiarreia extends AbstractReport<RelatorioPlanilhaDiarreiaDTOParam> {

    public RelatorioPlanilhaDiarreia(RelatorioPlanilhaDiarreiaDTOParam param) {
        super(param);
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();

        if (param.getTipoRelatorio() != null && ReportProperties.DETALHADO == param.getTipoRelatorio()) {
            RelatorioPlanilhaDiarreiaDTO proxy = Lambda.on(RelatorioPlanilhaDiarreiaDTO.class);

            if (RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_faixa_etaria"), proxy.getFaixaEtariaItem().getDescricao());
            } else if (RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.CID.equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_cid"), proxy.getAtendimentoMDDA().getAtendimento().getCidPrincipal().getDescricaoFormatado());
            } else if (RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.UNIDADE.equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_unidade"), proxy.getAtendimentoMDDA().getAtendimento().getEmpresa().getDescricao());
            } else if (RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.PLANO_TRATAMENTO.equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_plano_tratamento"), proxy.getAtendimentoMDDA().getPlanoTratamentoFormatado());
            }

            map.put(Bundle.getStringApplication("rotulo_data_atendimento_abv2"), proxy.getAtendimentoMDDA().getAtendimento().getDescricaoDataHoraAtendimento());
            map.put(Bundle.getStringApplication("rotulo_paciente"), proxy.getAtendimentoMDDA().getAtendimento().getUsuarioCadsus().getNomeSocial());
            map.put(Bundle.getStringApplication("rotulo_idade"), proxy.getIdadePacienteFormatada());
            map.put(Bundle.getStringApplication("rotulo_endereco"), proxy.getAtendimentoMDDA().getAtendimento().getEnderecoUsuarioCadsus().getEnderecoFormatadoComCidade());
            map.put(Bundle.getStringApplication("rotulo_zona"), proxy.getSegmentoTerritorial().getDescricao());

            if (!RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.CID.equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_cid"), proxy.getAtendimentoMDDA().getAtendimento().getCidPrincipal().getDescricaoFormatado());
            }

            map.put(Bundle.getStringApplication("rotulo_diarreia_com_sangue"), proxy.getAtendimentoMDDA().getDescricaoComSangue());
            map.put(Bundle.getStringApplication("rotulo_data_primeiros_sintomas"), proxy.getAtendimentoMDDA().getDataPrimeirosSintomasFormatado());

            if (!RelatorioPlanilhaDiarreiaDTOParam.FormaApresentacao.PLANO_TRATAMENTO.equals(param.getFormaApresentacao())) {
                map.put(Bundle.getStringApplication("rotulo_plano_tratamento"), proxy.getAtendimentoMDDA().getPlanoTratamentoFormatado());
            }

            map.put(Bundle.getStringApplication("rotulo_resultado_exame_laboratorial"), proxy.getAtendimentoMDDA().getResultadoExameLaboratorial());
        } else {
            RelatorioPlanilhaDiarreiaDTOResumido proxy = Lambda.on(RelatorioPlanilhaDiarreiaDTOResumido.class);
            map.put(Bundle.getStringApplication("rotulo_unidade"), proxy.getCodigoDescricaoEmpresaFormatado());
            map.put(Bundle.getStringApplication("rotulo_plano_tratamento"), proxy.getPlanoTratamento());
            map.put(Bundle.getStringApplication("rotulo_total_casos"), proxy.getQuantidade());
        }

        return map;
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("formaApresentacao", getParam().getFormaApresentacao());
        if (param.getTipoRelatorio() != null && ReportProperties.DETALHADO == param.getTipoRelatorio()) {
            return new QueryRelatorioPlanilhaDiarreia();
        }
        return new QueryRelatorioPlanilhaDiarreiaResumido();
    }

    @Override
    public String getXML() {
        if (param.getTipoRelatorio() != null && ReportProperties.DETALHADO == param.getTipoRelatorio()) {
            return "/br/com/ksisolucoes/report/unidadesaude/relatorio/jrxml/relatorio_planilha_diarreia.jrxml";
        }
        return "/br/com/ksisolucoes/report/unidadesaude/relatorio/jrxml/relatorio_planilha_diarreia_resumido.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_planilha_diarreia");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return this.param.getTipoArquivo();
    }
}
