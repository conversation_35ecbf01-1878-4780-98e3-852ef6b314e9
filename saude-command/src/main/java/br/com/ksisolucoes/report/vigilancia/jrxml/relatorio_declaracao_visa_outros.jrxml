<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_declaracao_visa_outros" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a1b57cfd-9055-4cac-88e7-9178d405e0e1">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.****************"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="500"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="br.com.ksisolucoes.util.Util"/>
	<import value="java.util.*"/>
	<import value="br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper"/>
	<import value="br.com.ksisolucoes.util.Data"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<parameter name="textoAutorizacaoExumacao1" class="java.lang.String"/>
	<parameter name="textoAutorizacaoExumacao2" class="java.lang.String"/>
	<parameter name="cidade" class="java.lang.String"/>
	<field name="requerimentoDeclaracaoVisaOutros" class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoDeclaracaoVisaOutros"/>
	<field name="profissionalImpressao" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[profissionalImpressao]]></fieldDescription>
	</field>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<detail>
		<band height="541" splitType="Stretch">
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="34" y="68" width="487" height="42" uuid="e4ce5ec0-b924-45bd-9dae-e18bd5c31be9"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="16"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoDeclaracaoVisaOutros}.getTitulo()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="34" y="131" width="487" height="401" uuid="d519313e-974e-49ca-9b71-a422fb229f68"/>
				<textElement textAlignment="Justified">
					<font fontName="Arial" size="12"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{requerimentoDeclaracaoVisaOutros}.getObservacao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true">
				<reportElement positionType="Float" x="34" y="5" width="487" height="42" uuid="b5fbcb0d-4ea6-43ed-8434-ca7989b446d6"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="18"/>
					<paragraph lineSpacing="Double"/>
				</textElement>
				<textFieldExpression><![CDATA["Declaração: " + $F{requerimentoDeclaracaoVisaOutros}.getRequerimentoVigilancia().getProtocoloFormatado()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="127" splitType="Stretch">
			<elementGroup>
				<textField pattern="EEEEE dd MMMMM yyyy">
					<reportElement positionType="FixRelativeToBottom" isPrintRepeatedValues="false" x="79" y="112" width="166" height="15" uuid="7f51c0b1-b29a-415a-afc6-0fa7f4d91dbd"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[$P{cidade} + ", "]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" pattern="EEEEE dd &apos;de&apos; MMMMM &apos;de&apos; yyyy">
					<reportElement positionType="FixRelativeToBottom" isPrintRepeatedValues="false" x="248" y="112" width="249" height="15" uuid="d3624a12-be7b-4940-9ebc-2ba41b59d8d7"/>
					<textElement>
						<font fontName="Arial" size="10"/>
					</textElement>
					<textFieldExpression><![CDATA[DataUtil.getDataAtual()]]></textFieldExpression>
				</textField>
			</elementGroup>
			<elementGroup>
				<textField isBlankWhenNull="true">
					<reportElement mode="Transparent" x="146" y="60" width="275" height="15" isPrintWhenDetailOverflows="true" uuid="03deef34-c264-462e-a87d-608ffe978532"/>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="12" isBold="true" pdfFontName="Helvetica-Bold"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{profissionalImpressao} != null ? $F{profissionalImpressao}.getNome() : "Assinatura da Vigilância Sanitária"]]></textFieldExpression>
				</textField>
				<textField isStretchWithOverflow="true" isBlankWhenNull="true">
					<reportElement stretchType="RelativeToTallestObject" x="146" y="75" width="275" height="13" uuid="e2623774-4b92-41c8-a5cc-40aeca316b87"/>
					<box topPadding="1" leftPadding="0" bottomPadding="0" rightPadding="0">
						<topPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Top">
						<font fontName="Arial" size="10" isBold="false"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{profissionalImpressao}.getReferenciaRegistroFormatado()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement positionType="Float" x="146" y="59" width="275" height="1" isPrintWhenDetailOverflows="true" uuid="896ba8e4-0e5d-4eb3-b79f-e5a06766f168">
						<printWhenExpression><![CDATA[VigilanciaHelper.exibirLinhaAssinatura()]]></printWhenExpression>
					</reportElement>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</elementGroup>
		</band>
	</columnFooter>
	<pageFooter>
		<band/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
