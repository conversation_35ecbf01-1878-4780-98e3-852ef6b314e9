package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EstabelecimentoAtividadeSecundariaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoAlvaraDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoAlvaraDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.grupoestabelecimento.GrupoEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import ch.lambdaj.Lambda;
import com.ibm.icu.lang.UCharacter;
import com.ibm.icu.text.BreakIterator;
import org.hamcrest.Matchers;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.select;

public class QueryRelatorioRequerimentoLicencaSanitaria  extends CommandQuery<QueryRelatorioRequerimentoLicencaSanitaria> implements ITransferDataReport<Object, RequerimentoAlvaraDTO> {

    private ImpressaoAlvaraDTOParam dtoParam;
    private List<RequerimentoAlvaraDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RequerimentoAlvaraDTO.class.getName());
        hql.addToSelect("rai.codigo", "requerimentoAlvara.codigo");
        hql.addToSelect("rai.numeroRegin", "requerimentoAlvara.numeroRegin");
        hql.addToSelect("rai.dataValidade", "requerimentoAlvara.dataValidade");
        hql.addToSelect("rai.dataValidadeProvisoria", "requerimentoAlvara.dataValidadeProvisoria");
        hql.addToSelect("rai.numeroAlvara", "requerimentoAlvara.numeroAlvara");
        hql.addToSelect("setores.descricaoSetor", "requerimentoAlvara.estabelecimentoSetores.descricaoSetor");
        hql.addToSelect("e.razaoSocial", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.razaoSocial");
        hql.addToSelect("e.cnpjCpf", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.cnpjCpf");
        hql.addToSelect("e.fantasia", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.fantasia");
        hql.addToSelect("tl.descricao", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.tipoLogradouro.descricao");
        hql.addToSelect("e.numeroLogradouro", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.numeroLogradouro");
        hql.addToSelect("ve.cep", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.cep");
        hql.addToSelect("ve.bairro", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.bairro");
        hql.addToSelect("ve.logradouro", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.logradouro");
        hql.addToSelect("c.descricao", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.cidade.descricao");
        hql.addToSelect("es.descricao", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.cidade.estado.descricao");
        hql.addToSelect("es.sigla", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.cidade.estado.sigla");
        hql.addToSelect("tl.codigo", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.vigilanciaEndereco.tipoLogradouro.codigo");
        hql.addToSelect("e.complemento", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.complemento");
        hql.addToSelect("e.telefone", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.telefone");
        hql.addToSelect("e.representanteNome", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.representanteNome");
        hql.addToSelect("e.horaInicioPrimeiroTurno", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.horaInicioPrimeiroTurno");
        hql.addToSelect("e.horaFimPrimeiroTurno", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.horaFimPrimeiroTurno");
        hql.addToSelect("e.horaInicioSegundoTurno", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.horaInicioSegundoTurno");
        hql.addToSelect("e.horaFimSegundoTurno", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.horaFimSegundoTurno");
        hql.addToSelect("setores.codigo", "requerimentoAlvara.estabelecimentoSetores.codigo");
        hql.addToSelect("atvdEstabSetor.codigo", "requerimentoAlvara.estabelecimentoSetores.atividadeEstabelecimento.codigo");
        hql.addToSelect("rt.codigo", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.codigo");
        hql.addToSelect("rt.nome", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.nome");
        hql.addToSelect("rt.cpf", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.cpf");
        hql.addToSelect("rt.unidadeFederacaoRegistro", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.unidadeFederacaoRegistro");
        hql.addToSelect("rt.numeroRegistro", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.numeroRegistro");
        hql.addToSelect("oe.codigo", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.orgaoEmissor.codigo");
        hql.addToSelect("oe.descricao", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.orgaoEmissor.descricao");
        hql.addToSelect("oe.sigla", "requerimentoAlvara.estabelecimentoSetores.responsavelTecnico.orgaoEmissor.sigla");
        hql.addToSelect("e.codigo", "requerimentoAlvara.requerimentoVigilancia.estabelecimento.codigo");
        hql.addToSelect("rai.observacaoDestaqueAlvara", "requerimentoAlvara.observacaoDestaqueAlvara");
        hql.addToSelect("uf.codigo", "requerimentoAlvara.requerimentoVigilancia.usuarioFinalizacao.codigo");
        hql.addToSelect("uf.nome", "requerimentoAlvara.requerimentoVigilancia.usuarioFinalizacao.nome");
        hql.addToSelect("rv.protocolo", "requerimentoAlvara.requerimentoVigilancia.protocolo");
        hql.addToSelect("rv.codigo", "requerimentoAlvara.requerimentoVigilancia.codigo");

        if(VigilanciaHelper.usaAtividadeslicenciaveisaoDeferir()){
            hql.addToSelect("(select ae.descricao "
                    + "from EstabelecimentoAtividade ea "
                    + "left join ea.atividadeEstabelecimento ae "
                    + "where ea.estabelecimento = e AND (ea.atividadeLicenciavelPrincipal = :sim OR (ea.flagPrincipal = :sim AND ea.atividadeLicenciavelPrincipal is null)))", "descricaoAtividadePrincipal");
        } else {
            hql.addToSelect("(select ae.descricao "
                    + "from EstabelecimentoAtividade ea "
                    + "left join ea.atividadeEstabelecimento ae "
                    + "where ea.estabelecimento = e AND ea.flagPrincipal = :sim)", "descricaoAtividadePrincipal");
        }
        StringBuilder from = new StringBuilder("RequerimentoAlvara rai ");
        from.append(" LEFT JOIN rai.requerimentoVigilancia rv ");
        from.append(" LEFT JOIN rv.usuarioFinalizacao uf ");
        from.append(" LEFT JOIN rv.estabelecimentoSetores setores ");
        from.append(" LEFT JOIN setores.responsavelTecnico rt ");
        from.append(" LEFT JOIN setores.atividadeEstabelecimento atvdEstabSetor ");
        from.append(" LEFT JOIN rt.orgaoEmissor oe ");
        from.append(" LEFT JOIN rv.estabelecimento e ");
        from.append(" LEFT JOIN e.vigilanciaEndereco ve ");
        from.append(" LEFT JOIN ve.cidade c ");
        from.append(" LEFT JOIN ve.tipoLogradouro tl ");
        from.append(" LEFT JOIN c.estado es ");
        hql.addToFrom(from.toString());
        if (this.dtoParam.getRequerimentoVigilancia() != null) {
            hql.addToWhereWhithAnd("rv.codigo = ", this.dtoParam.getRequerimentoVigilancia().getCodigo());
        } else if (this.dtoParam.getEstabelecimento() != null) {
            hql.addToWhereWhithAnd("e.codigo = ", this.dtoParam.getEstabelecimento().getCodigo());
            hql.addToWhereWhithAnd("rv.situacao = ", RequerimentoVigilancia.Situacao.DEFERIDO.value());
            hql.addToWhereWhithAnd("rv.estabelecimentoSetores IS NULL");
            hql.addToOrder("rai.codigo desc");
        } else {
            hql.addToWhereWhithAnd("e.codigo = ", this.dtoParam.getEstabelecimento().getCodigo());
            if (this.dtoParam.getEstabelecimentoSetores() != null) {
                hql.addToWhereWhithAnd("setores.codigo = ", this.dtoParam.getEstabelecimentoSetores().getCodigo());
            }
        }
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result, false);
    }

    @Override
    protected Object executeQuery(Query query) {
        return super.executeQuery(query.setMaxResults(1));
    }

    @Override
    public void setDTOParam(Object param) {
        this.dtoParam = (ImpressaoAlvaraDTOParam) param;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        if (CollectionUtils.isNotNullEmpty(result)) {
            Long codigoEstab = result.get(0).getRequerimentoAlvara().getRequerimentoVigilancia().getEstabelecimento().getCodigo();
            Long codigoEstabSetores = null;

            if (result.get(0).getRequerimentoAlvara().getEstabelecimentoSetores() != null) {
                codigoEstabSetores = result.get(0).getRequerimentoAlvara().getEstabelecimentoSetores().getCodigo();
                result.get(0).setResponsavelTecnicoList(Arrays.asList(result.get(0).getRequerimentoAlvara().getEstabelecimentoSetores().getResponsavelTecnico()));
                result.get(0).setAtividadeEstabelecimentoSetorList(getAtividadeEstabelecimentoSetorList(codigoEstabSetores));
            } else {
                List<EstabelecimentoResponsavelTecnico> list = LoadManager.getInstance(EstabelecimentoResponsavelTecnico.class)
                        .addProperties(new HQLProperties(EstabelecimentoResponsavelTecnico.class).getProperties())
                        .addProperties(new HQLProperties(Estabelecimento.class, EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO).getProperties())
                        .addProperties(new HQLProperties(ResponsavelTecnico.class, EstabelecimentoResponsavelTecnico.PROP_RESPONSAVEL_TECNICO).getProperties())
                        .addProperties(new HQLProperties(OrgaoEmissor.class, VOUtils.montarPath(EstabelecimentoResponsavelTecnico.PROP_RESPONSAVEL_TECNICO, ResponsavelTecnico.PROP_ORGAO_EMISSOR)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoResponsavelTecnico.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), codigoEstab))
                        .setMaxResults(8).start().getList();
                List<ResponsavelTecnico> responsavelTecnicoList = Lambda.extract(list, on(EstabelecimentoResponsavelTecnico.class).getResponsavelTecnico());
                result.get(0).setResponsavelTecnicoList(responsavelTecnicoList);
            }

            List<EstabelecimentoCnae> listCnae = LoadManager.getInstance(EstabelecimentoCnae.class)
                    .addProperties(new HQLProperties(EstabelecimentoCnae.class).getProperties())
                    .addProperties(new HQLProperties(TabelaCnae.class, EstabelecimentoCnae.PROP_CNAE).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoCnae.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), codigoEstab))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(listCnae)) {
                List<TabelaCnae> tabelaCnae = Lambda.extract(listCnae, on(EstabelecimentoCnae.class).getCnae());
                result.get(0).setCnaeList(tabelaCnae);
            }

            List<EstabelecimentoAtividade> estabelecimentoAtividadeList = carregaEstabelecimentoAtividades(codigoEstab, codigoEstabSetores);

            if (CollectionUtils.isNotNullEmpty(estabelecimentoAtividadeList)) {
                List<EstabelecimentoAtividade> atividadePrincipal;
                List<EstabelecimentoAtividade> atividadesSecundarias;
                List<AtividadeEstabelecimento> atividadeEstabelecimentoList = new ArrayList<>();
                if (estabelecimentoAtividadeList.size() > 1) {
                    if(VigilanciaHelper.usaAtividadeslicenciaveisaoDeferir()){
                        atividadePrincipal = select(estabelecimentoAtividadeList, Lambda.having(on(EstabelecimentoAtividade.class).getAtividadeLicenciavelPrincipal(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG)));
                        atividadesSecundarias = select(estabelecimentoAtividadeList, Lambda.having(on(EstabelecimentoAtividade.class).getAtividadeLicenciavel(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG)));
                        if (!CollectionUtils.isNotNullEmpty(atividadePrincipal) && !CollectionUtils.isNotNullEmpty(atividadesSecundarias)) {
                            atividadePrincipal = select(estabelecimentoAtividadeList, Lambda.having(on(EstabelecimentoAtividade.class).getFlagPrincipal(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG)));
                            atividadesSecundarias = select(estabelecimentoAtividadeList, Lambda.having(on(EstabelecimentoAtividade.class).getFlagPrincipal(), Matchers.not(RepositoryComponentDefault.SIM_LONG)));
                        }
                    } else {
                        atividadePrincipal = select(estabelecimentoAtividadeList, Lambda.having(on(EstabelecimentoAtividade.class).getFlagPrincipal(), Matchers.equalTo(RepositoryComponentDefault.SIM_LONG)));
                        atividadesSecundarias = select(estabelecimentoAtividadeList, Lambda.having(on(EstabelecimentoAtividade.class).getFlagPrincipal(), Matchers.not(RepositoryComponentDefault.SIM_LONG)));
                    }
                    atividadeEstabelecimentoList = Lambda.extract(atividadesSecundarias, on(EstabelecimentoAtividade.class).getAtividadeEstabelecimento());
                } else {
                    atividadePrincipal = estabelecimentoAtividadeList;
                    result.get(0).setDescricaoAtividadePrincipal(atividadePrincipal.get(0).getAtividadeEstabelecimento().getDescricaoVO().toUpperCase());

                }
                result.get(0).setGrupoEstabelecimento(CollectionUtils.isNotNullEmpty(atividadePrincipal) ? atividadePrincipal.get(0).getAtividadeEstabelecimento().getGrupoEstabelecimento() : null);

                List<EstabelecimentoAtividadeSecundariaDTO> atvdSecundariaAux = new ArrayList<>();
                for (AtividadeEstabelecimento atividadeEstabelecimento : atividadeEstabelecimentoList) {
                    List<AtividadeEstabelecimentoTipoServico> atividadeEstabelecimentoTipoServicoList = new ArrayList<>();

                    if (result.get(0).getRequerimentoAlvara().getEstabelecimentoSetores() != null
                            && result.get(0).getRequerimentoAlvara().getEstabelecimentoSetores().getAtividadeEstabelecimento() != null) {
                        if (result.get(0).getRequerimentoAlvara().getEstabelecimentoSetores().getAtividadeEstabelecimento().getCodigo().equals(atividadeEstabelecimento.getCodigo())) {
                            atividadeEstabelecimentoTipoServicoList = getAtividadeEstabelecimentoTipoServicoList(atividadeEstabelecimento);
                        }
                    }
                    EstabelecimentoAtividadeSecundariaDTO dtoSecundaria = new EstabelecimentoAtividadeSecundariaDTO();
                    if (CollectionUtils.isNotNullEmpty(atividadeEstabelecimentoTipoServicoList)) {
                        for (AtividadeEstabelecimentoTipoServico atividadeEstabelecimentoTipoServico : atividadeEstabelecimentoTipoServicoList) {
                            dtoSecundaria = new EstabelecimentoAtividadeSecundariaDTO();
                            dtoSecundaria.setDescricaoAtividadeSecundaria(atividadeEstabelecimento.getDescricao());
                            dtoSecundaria.setAtividadeEstabelecimentoTipoServicoSecundario(atividadeEstabelecimentoTipoServico);

                            atvdSecundariaAux.add(dtoSecundaria);
                        }
                    } else {
                        dtoSecundaria.setDescricaoAtividadeSecundaria(atividadeEstabelecimento.getDescricao());
                        atvdSecundariaAux.add(dtoSecundaria);
                    }
                }
                result.get(0).setAtividadeEstabelecimentoList(atvdSecundariaAux);

                if (atividadePrincipal != null && !atividadePrincipal.isEmpty()) {
                    List<AtividadeEstabelecimentoTipoServico> list = getAtividadeEstabelecimentoTipoServicoList(atividadePrincipal.get(0).getAtividadeEstabelecimento());

                    if (CollectionUtils.isNotNullEmpty(list)) {
                        result.get(0).setAtividadeEstabelecimentoTipoServicoList(list);
                    }
                }
            }
            if(VigilanciaHelper.isGrauRiscoUm(dtoParam.getEstabelecimento())){
                result.get(0).setObservacao(Bundle.getStringApplication("observacao_informativo"));
                result.get(0).setComentarioFinal(Bundle.getStringApplication("comentario_final", TenantContext.getRealContext()));
            } else if (VigilanciaHelper.isGrauDois(dtoParam.getEstabelecimento())) {
                result.get(0).setObservacao(Bundle.getStringApplication("observacao_media"));
                result.get(0).setObservacaoLei(Bundle.getStringApplication("observacao_grau"));
                result.get(0).setComentarioFinal(Bundle.getStringApplication("comentario_final", TenantContext.getRealContext()));
            } else {
                result.get(0).setObservacao(result.get(0).getRequerimentoAlvara().getObservacaoDestaqueAlvara());
                result.get(0).setObservacaoLei(Bundle.getStringApplication("observacao_grau"));
                result.get(0).setComentarioFinal(Bundle.getStringApplication("comentario_final", TenantContext.getRealContext()));
            }

            if (result.get(0).getRequerimentoAlvara().getRequerimentoVigilancia().getUsuarioFinalizacao() != null) {
                Long codigoProfissional = null;
                try {
                    codigoProfissional = (Long) HibernateSessionFactory.getSession().createCriteria(Usuario.class)
                            .add(Restrictions.idEq(result.get(0).getRequerimentoAlvara().getRequerimentoVigilancia().getUsuarioFinalizacao().getCodigo()))
                            .setProjection(Projections.property(VOUtils.montarPath(Usuario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO)))
                            .uniqueResult();
                } catch (DAOException e) {
                    br.com.ksisolucoes.util.log.Loggable.log.error(e);
                }
                if (codigoProfissional != null) {
                    Profissional profissional = VigilanciaHelper.reloadProfissionalImpressao(codigoProfissional);
                    profissional.setNome((UCharacter.toTitleCase(profissional.getNome().toLowerCase(), BreakIterator.getTitleInstance())));
                    this.result.get(0).setProfissionalFinalizacao(profissional);
                }
            }
        }
    }

    private List<EstabelecimentoAtividade> carregaEstabelecimentoAtividades(Long codigoEstab, Long codigoEstabSetores) {
        if (codigoEstab != null && codigoEstabSetores == null) {
            return LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                    .addProperties(new HQLProperties(AtividadeEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(GrupoEstabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO, AtividadeEstabelecimento.PROP_GRUPO_ESTABELECIMENTO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), codigoEstab))
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_IMPRESSAO_ALVARA, RepositoryComponentDefault.SIM_LONG))
                    .start().getList();
        } else {
            List<EstabelecimentoAtividade> estabelecimentoAtividades =
                    LoadManager.getInstance(EstabelecimentoAtividade.class)
                            .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                            .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(EstabelecimentoAtividade.PROP_ESTABELECIMENTO)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, Estabelecimento.PROP_CODIGO), codigoEstab))
                            .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_IMPRESSAO_ALVARA, RepositoryComponentDefault.SIM_LONG))
                            .start().getList();

            List<EstabelecimentoSetores> setoresList =
                    LoadManager.getInstance(EstabelecimentoSetores.class)
                            .addProperties(new HQLProperties(EstabelecimentoSetores.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoSetores.PROP_CODIGO), codigoEstabSetores))
                            .start().getList();

            List<EstabelecimentoAtividade> retorno = new ArrayList<>();
            for (EstabelecimentoAtividade ea : estabelecimentoAtividades) {
                for (EstabelecimentoSetores es : setoresList) {
                    if (ea.getAtividadeEstabelecimento().equals(es.getAtividadeEstabelecimento())) {
                        retorno.add(ea);
                    }
                }
            }
            return retorno;
        }
    }

    private List<AtividadeEstabelecimentoTipoServico> getAtividadeEstabelecimentoTipoServicoList(AtividadeEstabelecimento atividadeEstabelecimento) {
        AtividadeEstabelecimentoTipoServico proxy = on(AtividadeEstabelecimentoTipoServico.class);
        List<EstabelecimentoTipoServicoAtividade> listEstabelecimentoTipoServicoAtividade = LoadManager.getInstance(EstabelecimentoTipoServicoAtividade.class)
                .addProperties(new HQLProperties(EstabelecimentoTipoServicoAtividade.class).getProperties())
                .addProperties(new HQLProperties(AtividadeEstabelecimentoTipoServico.class, EstabelecimentoTipoServicoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO_TIPO_SERVICO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoTipoServicoAtividade.PROP_ESTABELECIMENTO), dtoParam.getEstabelecimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoTipoServicoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO), atividadeEstabelecimento))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EstabelecimentoTipoServicoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO_TIPO_SERVICO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(listEstabelecimentoTipoServicoAtividade)) {
            return Lambda.extract(listEstabelecimentoTipoServicoAtividade, on(EstabelecimentoTipoServicoAtividade.class).getAtividadeEstabelecimentoTipoServico());
        }
        return LoadManager.getInstance(AtividadeEstabelecimentoTipoServico.class)
                .addProperties(new HQLProperties(AtividadeEstabelecimentoTipoServico.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtividadeEstabelecimento().getCodigo()), atividadeEstabelecimento.getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getTipoServico()), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
    }

    private List<AtividadeEstabelecimento> getAtividadeEstabelecimentoSetorList(Long codigoEstabSetores) {
        List<EstabelecimentoSetoresAtividade> list = LoadManager.getInstance(EstabelecimentoSetoresAtividade.class)
                .addProperties(new HQLProperties(EstabelecimentoSetoresAtividade.class).getProperties())
                .addProperties(new HQLProperties(EstabelecimentoAtividade.class, VOUtils.montarPath(EstabelecimentoSetoresAtividade.PROP_ESTABELECIMENTO_ATIVIDADE)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoSetoresAtividade.PROP_ESTABELECIMENTO_SETORES, EstabelecimentoSetores.PROP_CODIGO), codigoEstabSetores))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstabelecimentoSetoresAtividade.PROP_ESTABELECIMENTO_ATIVIDADE, EstabelecimentoAtividade.PROP_IMPRESSAO_ALVARA), RepositoryComponentDefault.SIM_LONG)    )
                .start().getList();
        if (CollectionUtils.isNotNullEmpty(list)) {
            return Lambda.extract(list, on(EstabelecimentoSetoresAtividade.class).getEstabelecimentoAtividade().getAtividadeEstabelecimento());
        }
        return null;
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setLong("sim", RepositoryComponentDefault.SIM_LONG);
    }
}
