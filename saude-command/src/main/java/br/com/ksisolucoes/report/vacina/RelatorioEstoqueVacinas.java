/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.vacina;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vacina.dto.RelatorioEstoqueVacinasDTO;
import br.com.ksisolucoes.report.vacina.dto.RelatorioEstoqueVacinasDTOParam;
import br.com.ksisolucoes.report.vacina.query.QueryRelatorioEstoqueVacinas;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import ch.lambdaj.Lambda;

import java.util.LinkedHashMap;

/**
 *
 * <AUTHOR>
 */
public class RelatorioEstoqueVacinas extends AbstractReport<RelatorioEstoqueVacinasDTOParam> {

    private RelatorioEstoqueVacinasDTOParam param;

    public RelatorioEstoqueVacinas(RelatorioEstoqueVacinasDTOParam param) {
        super(param);
        this.param = param;
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vacina/jrxml/relatorio_estoque_vacinas.jrxml";
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioEstoqueVacinasDTO proxy = Lambda.on(RelatorioEstoqueVacinasDTO.class);

        columns.put((Bundle.getStringApplication("rotulo_tipo_vacina")), proxy.getTipoVacina().getDescricao());
        columns.put((Bundle.getStringApplication("rotulo_grupo_produto")), proxy.getGrupoProduto().getDescricao());
        columns.put((Bundle.getStringApplication("rotulo_fabricante")), proxy.getFabricante().getDescricao());
        columns.put((Bundle.getStringApplication("rotulo_empresa")), proxy.getEmpresa().getDescricaoFormatado());

        if (RepositoryComponentDefault.SIM.equals(param.getExibirLotes())) {
            columns.put((Bundle.getStringApplication("rotulo_grupo_estoque")), proxy.getGrupoEstoque());
            columns.put((Bundle.getStringApplication("rotulo_data_validade")), proxy.getDataValidadeFormatada());
        }

        columns.put((Bundle.getStringApplication("rotulo_un")), proxy.getUnidade().getUnidade());
        columns.put((Bundle.getStringApplication("rotulo_doses")), proxy.getQuantidadeDose());

        columns.put((Bundle.getStringApplication("rotulo_estoque_fisico")), proxy.getEstoqueFisico());
        columns.put((Bundle.getStringApplication("rotulo_estoque_indisponivel")), proxy.getEstoqueReservado());
        columns.put((Bundle.getStringApplication("rotulo_total_doses_abv")), proxy.getQuantidadeTotalDose());
        columns.put((Bundle.getStringApplication("rotulo_estoque")), proxy.getDeposito().getDescricao());

        return columns;
    }

     @Override
    public ITransferDataReport getQuery(){
         addParametro("formaApresentacao", param.getFormaApresentacao());
         addParametro("exibirLote", getParam().getExibirLotes());
         return new QueryRelatorioEstoqueVacinas();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_estoque_vacinas");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return param.getTipoArquivo();
    }
}
