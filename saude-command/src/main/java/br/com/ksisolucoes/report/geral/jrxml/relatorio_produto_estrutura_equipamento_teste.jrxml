<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_produto_estrutura_equipamento" pageWidth="595" pageHeight="842" columnWidth="535" leftMargin="30" rightMargin="30" topMargin="20" bottomMargin="20">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="ReturnedValuesMap" class="java.util.Map" isForPrompting="false">
		<defaultValueExpression><![CDATA[new java.util.HashMap()]]></defaultValueExpression>
	</parameter>
	<parameter name="RESUMIDO" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="listarPeso" class="java.lang.Boolean" isForPrompting="false"/>
	<parameter name="COD_PROCEDENCIA" class="java.lang.Long" isForPrompting="false"/>
	<parameter name="codigoEmpresa" class="java.lang.Long" isForPrompting="false"/>
	<field name="codigo" class="java.lang.String"/>
	<field name="descricao" class="java.lang.String"/>
	<field name="estruturaEquipamentoList" class="java.lang.Object"/>
	<field name="revisaoEstrutura" class="java.lang.Long">
		<fieldDescription><![CDATA[revisaoEstrutura]]></fieldDescription>
	</field>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="GRUPO_PRODUTO" isReprintHeaderOnEachPage="true">
		<groupExpression><![CDATA[$F{codigo}]]></groupExpression>
		<groupHeader>
			<band height="21" splitType="Stretch">
				<printWhenExpression><![CDATA[((java.util.List)$F{estruturaEquipamentoList}).size() > 0]]></printWhenExpression>
				<rectangle radius="5">
					<reportElement key="rectangle-2" stretchType="RelativeToBandHeight" mode="Transparent" x="2" y="7" width="532" height="14"/>
				</rectangle>
				<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="false">
					<reportElement key="textField-2" mode="Transparent" x="10" y="8" width="519" height="12" forecolor="#000000" backcolor="#FFFFFF"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="true" isItalic="true" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica-BoldOblique" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_produto")+ ": "+$F{descricao} + " (" + $F{codigo} + ")" + "   " +  $V{BUNDLE}.getStringApplication("rotulo_revisao") + ": " + $F{revisaoEstrutura}]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="20" splitType="Stretch">
				<textField pattern="#,##0.00" isBlankWhenNull="false">
					<reportElement key="textField-3" positionType="Float" mode="Opaque" x="464" y="3" width="70" height="14" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{COD_PROCEDENCIA} == null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.Double"><![CDATA[$P{ReturnedValuesMap}.get("total")]]></textFieldExpression>
				</textField>
				<textField pattern="" isBlankWhenNull="false">
					<reportElement key="textField-4" positionType="Float" mode="Opaque" x="327" y="3" width="129" height="14" forecolor="#FF3333" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{COD_PROCEDENCIA} == null]]></printWhenExpression>
					</reportElement>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" lineSpacing="Single">
						<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					</textElement>
					<textFieldExpression class="java.lang.String"><![CDATA[$V{BUNDLE}.getStringApplication("rotulo_total")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement key="line-1" mode="Opaque" x="329" y="2" width="205" height="1" forecolor="#000000" backcolor="#FFFFFF">
						<printWhenExpression><![CDATA[$P{COD_PROCEDENCIA} == null]]></printWhenExpression>
					</reportElement>
					<graphicElement fill="Solid">
						<pen lineWidth="0.5" lineStyle="Solid"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="20" splitType="Stretch">
			<printWhenExpression><![CDATA[((java.util.List)$F{estruturaEquipamentoList}).size() > 0]]></printWhenExpression>
			<subreport isUsingCache="true">
				<reportElement key="subreport-1" mode="Opaque" x="2" y="2" width="532" height="16" forecolor="#000000" backcolor="#FFFFFF"/>
				<subreportParameter name="ReturnedValuesMap">
					<subreportParameterExpression><![CDATA[$P{ReturnedValuesMap}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="RESUMIDO">
					<subreportParameterExpression><![CDATA[$P{RESUMIDO}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="COD_PROCEDENCIA">
					<subreportParameterExpression><![CDATA[$P{COD_PROCEDENCIA}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="codigoEmpresa">
					<subreportParameterExpression><![CDATA[$P{codigoEmpresa}]]></subreportParameterExpression>
				</subreportParameter>
				<subreportParameter name="listarApenasPeso">
					<subreportParameterExpression><![CDATA[$P{listarPeso}]]></subreportParameterExpression>
				</subreportParameter>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource((java.util.List)$F{estruturaEquipamentoList})]]></dataSourceExpression>
				<subreportExpression class="java.lang.String"><![CDATA["br/com/ksisolucoes/report/geral/jrxml/relatorio_estrutura_equipamento.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
