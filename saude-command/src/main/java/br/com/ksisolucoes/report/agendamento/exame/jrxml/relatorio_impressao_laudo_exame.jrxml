<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_impressao_laudo_exame" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="812a5b70-40d5-4117-bc4f-81ca6e8acef4">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.6528925619834867"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="br.com.celk.util.DataUtil"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="atendimentoExame" class="br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="tipoExame" class="br.com.ksisolucoes.vo.prontuario.basico.TipoExame"/>
	<field name="profissionalLaudo" class="br.com.ksisolucoes.vo.cadsus.Profissional"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio"/>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"/>
	<field name="atendimentoExameList" class="java.util.List"/>
	<group name="Exame" isStartNewPage="true" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band height="69">
				<textField isBlankWhenNull="true">
					<reportElement key="textField-9" mode="Opaque" x="-1" y="0" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
					<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
						<pen lineWidth="1.0"/>
						<topPen lineWidth="1.0"/>
						<leftPen lineWidth="0.0"/>
						<bottomPen lineWidth="1.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_exame").toUpperCase()]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="555" y="0" width="1" height="66" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="16" width="60" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atendimento") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="-1" y="0" width="1" height="66" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
				</line>
				<line>
					<reportElement x="-1" y="65" width="557" height="1" uuid="9228121a-ceb0-4907-8533-7786499316cb"/>
				</line>
				<textField isBlankWhenNull="true">
					<reportElement x="63" y="16" width="60" height="12" uuid="e3a6e1b5-2e36-4c08-8da1-bedee253571a"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoExame}.getCodigo()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="32" width="61" height="12" uuid="6d5f89eb-be79-4ac2-a18f-e19db8eac01f"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_responsavel") + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="64" y="32" width="304" height="12" uuid="da11e5f4-5389-410b-8663-e6a4ae7cfd47"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{profissionalLaudo}.getNome()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="2" y="48" width="50" height="12" uuid="c2047ab8-217c-478d-8cf4-df2c50d627dc"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitante") + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="53" y="48" width="315" height="12" uuid="20fc3137-ff20-497f-93ec-4cdb4c0fedf8"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoExame}.getNomeProfissional()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="132" y="16" width="54" height="12" uuid="8622c5ea-d4d3-4738-9780-5ece548eed79"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_exame") + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="187" y="16" width="181" height="12" uuid="cac74ff2-c968-492b-a23c-bec1f62b5bdb"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{tipoExame}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement x="433" y="16" width="123" height="12" uuid="e6048be1-066e-49ce-9767-57a2a484a62b"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="387" y="16" width="46" height="12" uuid="79fb5bbf-d129-4b17-a5a8-8ecb38844b56"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio") + ": "]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="387" y="32" width="68" height="12" uuid="501bd95c-855d-4ad8-9124-26dcd1e501d0"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_exame") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="457" y="32" width="88" height="12" uuid="45421a54-869b-4f7a-8315-9d7115a82e76"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoExame}.getDataExame()]]></textFieldExpression>
				</textField>
				<textField isBlankWhenNull="true">
					<reportElement key="textField-7" x="387" y="48" width="68" height="12" uuid="d18f793b-9b4c-47df-b35f-52126364a483"/>
					<box>
						<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
						<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_laudo") + ": "]]></textFieldExpression>
				</textField>
				<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
					<reportElement x="457" y="48" width="88" height="12" uuid="0c7e4bff-e399-4561-b8a8-cd9d7c27a712"/>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="9"/>
					</textElement>
					<textFieldExpression><![CDATA[$F{atendimentoExame}.getDataLaudo()]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band/>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="24" splitType="Stretch">
			<textField isBlankWhenNull="false">
				<reportElement x="-1" y="0" width="555" height="15" uuid="4422d061-79c3-4e6a-b35c-a022939dbead"/>
				<textElement textAlignment="Center" verticalAlignment="Top">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_laudo_exame")]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="50" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement key="textField-9" mode="Opaque" x="-1" y="1" width="557" height="15" backcolor="#F6F4F2" uuid="541d9f2e-4a88-4ae1-a4ea-96489b8b6c04"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.0"/>
					<topPen lineWidth="1.0"/>
					<leftPen lineWidth="0.0"/>
					<bottomPen lineWidth="1.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_paciente").toUpperCase()]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="-1" y="1" width="1" height="49" uuid="e57a2bc0-a544-43bf-a524-bb95eed0e8d2"/>
			</line>
			<line>
				<reportElement x="555" y="1" width="1" height="49" uuid="4d1dc53e-3bde-4bcf-a1a9-103c9ca97f00"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="17" width="30" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nome")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="408" y="17" width="35" height="12" uuid="a93f00ca-f36a-4956-a25b-3059e6827e33"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_sexo")+": "]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="33" y="17" width="365" height="12" uuid="4b749928-4e7f-4aee-a05c-7780d31f4dc5"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="445" y="17" width="100" height="12" uuid="73b7a8ac-b76b-4e3e-8c83-29d1d459d949"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="2" y="33" width="77" height="12" uuid="d0c0043b-476b-49a6-9092-4ce3f81e6c12"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_nascimento")+": "]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement key="textField-7" x="203" y="33" width="28" height="12" uuid="ebcabd1c-5b8f-472b-bd14-85d96e1f60e0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")+": "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="80" y="33" width="100" height="12" uuid="8cee1975-9d3e-40c3-a04f-22871b8a3cf1"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="232" y="33" width="267" height="12" uuid="95bb37e1-96ed-459b-b20c-1bfe6f102473"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdadeAnoMesDia()]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="14" splitType="Stretch">
			<subreport>
				<reportElement x="2" y="3" width="552" height="10" uuid="ec3256c1-f4ea-49de-9195-c904b1cd6524"/>
				<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{atendimentoExameList})]]></dataSourceExpression>
				<subreportExpression><![CDATA["/br/com/ksisolucoes/report/agendamento/exame/jrxml/sub_relatorio_impressao_laudo_exame.jasper"]]></subreportExpression>
			</subreport>
		</band>
	</detail>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
