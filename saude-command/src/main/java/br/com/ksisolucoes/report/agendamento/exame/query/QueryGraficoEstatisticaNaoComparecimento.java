package br.com.ksisolucoes.report.agendamento.exame.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.agendamento.exame.dto.GraficoEstatisticasNaoComparecimentoDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.GraficoEstatisticasNaoComparecimentoDTOParam;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryGraficoEstatisticaNaoComparecimento extends CommandQuery<QueryGraficoEstatisticaNaoComparecimento> 
implements ITransferDataReport<GraficoEstatisticasNaoComparecimentoDTOParam, GraficoEstatisticasNaoComparecimentoDTO>{

    private GraficoEstatisticasNaoComparecimentoDTOParam param;
    private List<GraficoEstatisticasNaoComparecimentoDTO> dtoList;

    @Override
    public void setDTOParam(GraficoEstatisticasNaoComparecimentoDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("count(agah.codigo)", "quantidade");

        if (GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.UNIDADE.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("agah.empresaOrigem.descricao", "descricaoFormaApresentacao");
        } else if (GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.TIPO_PROCEDIMENTO.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("agah.tipoProcedimento.descricao", "descricaoFormaApresentacao");
        } else if (GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("agah.localAgendamento.descricao", "descricaoFormaApresentacao");
        } else if (GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.PROFISSIONAL.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("agah.profissional.nome", "descricaoFormaApresentacao");
        } else if (GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.GRUPO_PROCEDIMENTO.equals(param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("agah.tipoProcedimento.tipoProcedimentoClassificacao.descricao", "descricaoFormaApresentacao");
        } else if (GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.AREA.equals(param.getFormaApresentacao())) {
            hql.addToSelect("coalesce( ea.descricao,'AREA NAO DEFINIDA')", "descricaoFormaApresentacao");
            hql.addToGroup("ea.id.codigo");
            hql.addToGroup("ea.id.cidade");
            hql.addToGroup("ea.descricao");
        } else if (GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.MICRO_AREA.equals(param.getFormaApresentacao())) {
            hql.addToSelect("cast(ema.microArea as string)", "descricaoFormaApresentacao");
            hql.addToGroup("ema.codigo");
            hql.addToGroup("ema.microArea");
            hql.addToWhereWhithAnd("ema is not null");
        }else if(GraficoEstatisticasNaoComparecimentoDTOParam.FormaApresentacao.MOTIVO.equals(param.getFormaApresentacao())){
            hql.addToSelectAndGroup("agah.motivoNaoComparecimento.descricao", "descricaoFormaApresentacao");
        }

/** CALCULO DO TOTAL **/
        HQLHelper hqlSub = hql.getNewInstanceSubQuery();
        hqlSub.addToSelect("count(agah2.codigo)");

        if (param.getEquipeArea() != null) {
            hqlSub.addToFrom("AgendaGradeAtendimentoHorario agah2"
                + "             left join agah2.usuarioCadsus usuarioCadsus2 "
                + "             left join usuarioCadsus2.enderecoDomicilio enderecoDomicilio2"
                + "             left join enderecoDomicilio2.equipeMicroArea ema2"
                + "             left join ema2.equipeArea ea2");
            
            hqlSub.addToWhereWhithAnd("ea2 = ", param.getEquipeArea());
            if (param.getMicroArea() != null) {
                hqlSub.addToWhereWhithAnd("ema2.microArea = ", param.getMicroArea());
            }
        } else {
            hqlSub.addToFrom("AgendaGradeAtendimentoHorario agah2");
        } 
        hqlSub.addToWhereWhithAnd("agah2.empresaOrigem ", param.getEmpresa());
        hqlSub.addToWhereWhithAnd("agah2.tipoProcedimento ", param.getTipoProcedimento());
        if (!GraficoEstatisticasNaoComparecimentoDTOParam.TipoAgendamento.AMBOS.equals(param.getTipoAgendamento())) {
            if (GraficoEstatisticasNaoComparecimentoDTOParam.TipoAgendamento.TFD.equals(param.getTipoAgendamento())) {
                hqlSub.addToWhereWhithAnd("agah2.tipoProcedimento.flagTfd = ", RepositoryComponentDefault.SIM);
            } else if (GraficoEstatisticasNaoComparecimentoDTOParam.TipoAgendamento.ENCAMINHAMENTO.equals(param.getTipoAgendamento())) {
                hqlSub.addToWhereWhithAnd("agah2.tipoProcedimento.flagTfd <> ", RepositoryComponentDefault.SIM);
            }
        }
        hqlSub.addToWhereWhithAnd("agah2.localAgendamento in ", param.getEmpresaExecutante());
        hqlSub.addToWhereWhithAnd("agah2.profissional ", param.getProfissional());
        hqlSub.addToWhereWhithAnd("agah2.tipoProcedimento.tipoProcedimentoClassificacao ", param.getTipoProcedimentoClassificacaos());
        hqlSub.addToWhereWhithAnd("agah2.dataAgendamento", param.getPeriodo());
        hqlSub.addToWhereWhithAnd("agah2.status =", AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU);
        hqlSub.addToWhereWhithAnd("agah2.motivoNaoComparecimento =", param.getMotivoNaoComparecimento());

        hql.addToSelect("(" + hqlSub.getQuery() + ")", "total");
/** ********************* **/

        hql.setTypeSelect(GraficoEstatisticasNaoComparecimentoDTO.class.getName());
        
//        hql.setConvertToLeftJoin(true);

        hql.addToFrom("AgendaGradeAtendimentoHorario agah "
                + "             left join agah.usuarioCadsus usuarioCadsus "
                + "             left join usuarioCadsus.enderecoDomicilio enderecoDomicilio"
                + "             left join enderecoDomicilio.equipeMicroArea ema"
                + "             left join ema.equipeArea ea");

        hql.addToWhereWhithAnd("agah.empresaOrigem ", param.getEmpresa());
        hql.addToWhereWhithAnd("agah.tipoProcedimento ", param.getTipoProcedimento());
        if (!GraficoEstatisticasNaoComparecimentoDTOParam.TipoAgendamento.AMBOS.equals(param.getTipoAgendamento())) {
            if (GraficoEstatisticasNaoComparecimentoDTOParam.TipoAgendamento.TFD.equals(param.getTipoAgendamento())) {
                hql.addToWhereWhithAnd("agah.tipoProcedimento.flagTfd = ", RepositoryComponentDefault.SIM);
            } else if (GraficoEstatisticasNaoComparecimentoDTOParam.TipoAgendamento.ENCAMINHAMENTO.equals(param.getTipoAgendamento())) {
                hql.addToWhereWhithAnd("agah.tipoProcedimento.flagTfd <> ", RepositoryComponentDefault.SIM);
            }
        }
        hql.addToWhereWhithAnd("agah.localAgendamento in ", param.getEmpresaExecutante());
        hql.addToWhereWhithAnd("agah.profissional ", param.getProfissional());
        hql.addToWhereWhithAnd("agah.tipoProcedimento.tipoProcedimentoClassificacao ", param.getTipoProcedimentoClassificacaos());
        hql.addToWhereWhithAnd("agah.dataAgendamento", param.getPeriodo());
        hql.addToWhereWhithAnd("agah.motivoNaoComparecimento =", param.getMotivoNaoComparecimento());
        
        if (param.getEquipeArea() != null) {
            hql.addToWhereWhithAnd("ea = ",param.getEquipeArea());
            if (param.getMicroArea() != null) {
                hql.addToWhereWhithAnd("ema.microArea = ",param.getMicroArea());
            }
        }
    

        hql.addToWhereWhithAnd("agah.status =", AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU);

        if (param.getTipoGrafico().equals(GraficoEstatisticasNaoComparecimentoDTOParam.TipoGrafico.PIZZA)) {
        hql.addToOrder("1 desc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        dtoList = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection getResult() {
        return dtoList;
    }

}
