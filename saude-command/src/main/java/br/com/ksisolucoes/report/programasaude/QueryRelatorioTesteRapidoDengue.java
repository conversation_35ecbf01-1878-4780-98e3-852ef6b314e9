package br.com.ksisolucoes.report.programasaude;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.geral.interfaces.dto.RelatorioTesteRapidoDengueDTO;
import br.com.ksisolucoes.report.geral.interfaces.dto.RelatorioTesteRapidoDengueDTOParam;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.TipoTesteRapido;

import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;

import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class QueryRelatorioTesteRapidoDengue extends CommandQuery<QueryRelatorioTesteRapidoDengue> implements ITransferDataReport<RelatorioTesteRapidoDengueDTOParam, RelatorioTesteRapidoDengueDTO> {

    private RelatorioTesteRapidoDengueDTOParam param;
    private List<RelatorioTesteRapidoDengueDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.setTypeSelect(RelatorioTesteRapidoDengueDTO.class.getName());

        hql.addToSelect("uc.nome", "nomeUsuario");
        hql.addToSelect("ttr.descricao", "descricaoProcedimento");
        hql.addToSelect("trr.resultado", "resultado");
        hql.addToSelect("trr.dataPrimeirosSintomas", "dataPrimeirosSintomas");
        hql.addToSelect("ar.dataFechamento", "dataCadastro");
        hql.addToSelect("e.descricao", "descricaoEmpresa");
        hql.addToSelect("equipeArea.descricao", "descricaoEquipeArea");
        hql.addToSelect("uc.apelido", "apelido");
        hql.addToSelect("uc.utilizaNomeSocial", "flagNomeSocial");

        hql.addToWhereWhithAnd("ttr.tipoTeste =", TipoTesteRapido.TipoTeste.DENGUE.value());

        if (param.getEmpresa() != null) {
            hql.addToWhereWhithAnd("e.codigo =", param.getEmpresa().getCodigo());
        }

        if (param.getPeriodo() != null) {
            hql.addToWhereWhithAnd("ar.dataFechamento >= ", this.param.getPeriodo().getDataInicial());
            hql.addToWhereWhithAnd("ar.dataFechamento <= ", this.param.getPeriodo().getDataFinal());
        }

        if (RelatorioTesteRapidoDengueDTOParam.FormaApresentacao.UNIDADE.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("e.descricao");
        }
        else if (RelatorioTesteRapidoDengueDTOParam.FormaApresentacao.EQUIPE_REFERENCIA.equals(this.param.getFormaApresentacao())) {
;            hql.addToOrder("equipeArea.descricao");
        }

        hql.addToOrder("ar.dataFechamento");

        hql.addToFrom(" TesteRapidoRealizado trr"
                + " left join trr.atendimentoResultado ar"
                + " left join ar.usuarioCadsus uc"
                + " left join trr.testeRapidoConjunto trc"
                + " left join ar.empresa e"
                + " left join trr.tipoTesteRapido ttr"
                + " left join uc.equipe equipe"
                + " left join equipe.equipeArea equipeArea");

    }

    public static LinkedHashMap<String, Object> getColumnMapping() {
        LinkedHashMap<String, Object> columns = new LinkedHashMap<>();
        RelatorioTesteRapidoDengueDTO proxy = on(RelatorioTesteRapidoDengueDTO.class);

        columns.put((Bundle.getStringApplication("rotulo_nome_paciente")), proxy.getNomeUsuario());
        columns.put((Bundle.getStringApplication("rotulo_tipo_exame")), proxy.getDescricaoProcedimento());
        columns.put((Bundle.getStringApplication("rotulo_resultado")), proxy.getDescricaoResultado());
        columns.put((Bundle.getStringApplication("rotulo_data_primeiros_sintomas")), proxy.getDataPrimeirosSintomas());
        columns.put((Bundle.getStringApplication("rotulo_unidade_saude")), proxy.getDescricaoEmpresa());
        columns.put((Bundle.getStringApplication("rotulo_equipe_area")), proxy.getDescricaoEquipeArea());

        return columns;
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioTesteRapidoDengueDTOParam param) {
        this.param = param;
    }
}
