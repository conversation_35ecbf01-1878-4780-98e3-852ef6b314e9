/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.report.prontuario.basico;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteEntregaPedidoTfdDTOParam;
import br.com.ksisolucoes.report.prontuario.basico.query.QueryRelatorioImprimirComprovanteEntregaPedidoTfd;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Parametro;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImprimirComprovanteEntregaPedidoTfd extends AbstractReport<RelatorioImprimirComprovanteEntregaPedidoTfdDTOParam> {

    public RelatorioImprimirComprovanteEntregaPedidoTfd(RelatorioImprimirComprovanteEntregaPedidoTfdDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/jrxml/relatorio_impressao_comprovante_entrega_pedido_tfd.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_entrga_pedido_tfd");
    }

    @Override
    protected void customDTOParam(RelatorioImprimirComprovanteEntregaPedidoTfdDTOParam param) throws ValidacaoException {
        Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();
        this.addParametro("nomePrefeitura", parametro.getPropertyValue(Parametro.PROP_NOME_PREFEITURA));
        this.addParametro("descricaoEmpresa", SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getDescricao());
        this.addParametro("nomeEmpresaRelatorio", getNomeEmpresaRelatorio());
        this.addParametro("nomeUsuario", getNomeUsuario());
        this.addParametro("titulo2", Bundle.getStringApplication("rotulo_ficha_cadastral"));
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioImprimirComprovanteEntregaPedidoTfd();
    }

    public String getNomeEmpresaRelatorio() {
        Empresa empresa = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (StringUtils.trimToNull(empresa.getNomeRelatorio()) != null) {
            return empresa.getNomeRelatorio();
        }

        return empresa.getDescricao();
    }

    public String getNomeUsuario() {
        Usuario usuario = SessaoAplicacaoImp.getInstance().getUsuario();

        return usuario.getNome();
    }
}
