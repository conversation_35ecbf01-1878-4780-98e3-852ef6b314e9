/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento.solicitacaoagendamento;

import br.com.celk.util.CollectionUtils;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.integracao.util.AgendamentoUtils;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.agendamento.solicitacaoagendamento.dto.RelatorioComprovanteSolicitacaoAgendamentoDTOParam;
import br.com.ksisolucoes.report.agendamento.solicitacaoagendamento.query.QueryImpressaoComprovanteSolicitacaoAgendamento;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturado;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturadoLogradouro;
import br.com.ksisolucoes.vo.esus.helper.EsusIntegracaoHelper;
import org.apache.commons.lang.StringUtils;

import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class RelatorioComprovanteSolicitacaoAgendamento extends AbstractReport<RelatorioComprovanteSolicitacaoAgendamentoDTOParam> {

    private Empresa empresa;
    private Usuario usuario;

    public RelatorioComprovanteSolicitacaoAgendamento(RelatorioComprovanteSolicitacaoAgendamentoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/agendamento/solicitacaoagendamento/jrxml/relatorio_comprovante_solicitacao_agendamento.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_solicitacao_agendamento");
    }

    @Override
    public ITransferDataReport getQuery() {
        try{
            Parametro parametro = CargaBasicoPadrao.getInstance().getParametroPadrao();
            this.addParametro("nomePrefeitura", parametro.getPropertyValue(Parametro.PROP_NOME_PREFEITURA));
            this.addParametro("descricaoEmpresa", SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getDescricao());
            this.addParametro("nomeEmpresaRelatorio", getNomeEmpresaRelatorio());
            this.addParametro("nomeUsuario", getNomeUsuario());
            this.addParametro("utilizaEnderecoEstruturado", getUtilizaEnderecoEstruturado());
            this.addParametro("equipeReferenciaEnderecoEstruturado", AgendamentoUtils.getEquipeReferenciaEnderecoEstruturado(param.getUsuarioCadsus(), getUtilizaEnderecoEstruturado()));
        }catch(ValidacaoException | DAOException ex){
            throw new RuntimeException(ex.getMessage(), ex);
        }

        return new QueryImpressaoComprovanteSolicitacaoAgendamento();
    }

    public Usuario getUsuario() {
        if(usuario == null){
            usuario = SessaoAplicacaoImp.getInstance().getUsuario();
        }

        return usuario;
    }

    private Empresa getEmpresa() {
        if(empresa == null){
            empresa = SessaoAplicacaoImp.getInstance().getEmpresa();
        }

        return empresa;
    }

    private Boolean getUtilizaEnderecoEstruturado() throws DAOException {
        return RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("utilizaEnderecoEstruturado"));
    }
    public String getNomeEmpresaRelatorio(){
        if (StringUtils.trimToNull(getEmpresa().getNomeRelatorio())!=null) {
            return getEmpresa().getNomeRelatorio();
        }

        return getEmpresa().getDescricao();
    }

    public String getNomeUsuario(){
        return getUsuario().getNome();
    }
}
