/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.estoque;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryRelatorioResumoPedidoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.query.QueryRelatorioResumoPedidoEstoque;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoPedidoEstoque extends AbstractReport<QueryRelatorioResumoPedidoEstoqueDTOParam> {

    public RelatorioResumoPedidoEstoque(QueryRelatorioResumoPedidoEstoqueDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_resumo_pedido_estoque.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_resumo_pedido_x_estoque");
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("formaApresentacao", this.getParam().getFormApresentacao());
        return new QueryRelatorioResumoPedidoEstoque();
    }

}
