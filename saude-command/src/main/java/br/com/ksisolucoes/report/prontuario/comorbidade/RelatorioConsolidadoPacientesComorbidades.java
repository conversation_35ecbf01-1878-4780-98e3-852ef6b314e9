package br.com.ksisolucoes.report.prontuario.comorbidade;

import br.com.celk.unidadesaude.comorbidade.interfaces.dto.QueryRelatorioConsolidadoPacientesComorbidadesDTOParam;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.comorbidade.query.QueryRelatorioConsolidadoPacientesComorbidades;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioConsolidadoPacientesComorbidades extends AbstractReport<QueryRelatorioConsolidadoPacientesComorbidadesDTOParam> {

    public RelatorioConsolidadoPacientesComorbidades(QueryRelatorioConsolidadoPacientesComorbidadesDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
            return "/br/com/ksisolucoes/report/prontuario/comorbidade/jrxml/relatorio_consolidado_pacientes_comorbidades.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        this.addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        return new QueryRelatorioConsolidadoPacientesComorbidades();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("relatorio_consolidado_condicao_saude");
    }
}
