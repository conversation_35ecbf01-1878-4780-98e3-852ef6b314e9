package br.com.ksisolucoes.report.frota;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.frota.interfaces.dto.RelacaoDiarioBordoDTOParam;
import br.com.ksisolucoes.report.frota.query.QueryRelacaoDiarioBordo;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelacaoDiarioBordo extends AbstractReport<RelacaoDiarioBordoDTOParam> {

    public RelacaoDiarioBordo(RelacaoDiarioBordoDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        addParametro("FORMA_APRESENTACAO", this.getParam().getFormaApresentacao());
        return "/br/com/ksisolucoes/report/frota/jrxml/relacao_diario_bordo.jrxml";
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelacaoDiarioBordo();
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_diario_bordo");
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return getParam().getTipoRelatorio();
    }
}
