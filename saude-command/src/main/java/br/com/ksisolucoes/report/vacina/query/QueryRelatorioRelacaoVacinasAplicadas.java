package br.com.ksisolucoes.report.vacina.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.vacina.dto.RelatorioRelacaoVacinasAplicadasDTO;
import br.com.ksisolucoes.report.vacina.dto.RelatorioRelacaoVacinasAplicadasDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.*;

import static br.com.ksisolucoes.bo.command.BuilderQueryCustom.QuerySorter.CRESCENTE_NULLS_FIRST;
import static br.com.ksisolucoes.vo.basico.base.BaseFaixaEtariaItem.PROP_ID;
import static br.com.ksisolucoes.vo.basico.base.BaseFaixaEtariaItemPK.PROP_FAIXA_ETARIA;

/**
 * <AUTHOR>
 */
public class QueryRelatorioRelacaoVacinasAplicadas extends CommandQuery<QueryRelatorioRelacaoVacinasAplicadas> implements ITransferDataReport<RelatorioRelacaoVacinasAplicadasDTOParam, RelatorioRelacaoVacinasAplicadasDTO> {

    private RelatorioRelacaoVacinasAplicadasDTOParam param;
    private List<RelatorioRelacaoVacinasAplicadasDTO> dtoList;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioRelacaoVacinasAplicadasDTO.class.getName());

        hql.addToSelect("vacinaAplicacao.codigo", "vacinaAplicacao.codigo");
        hql.addToSelect("vacinaAplicacao.dataAplicacao", "vacinaAplicacao.dataAplicacao");
        hql.addToSelect("vacinaAplicacao.dataCadastro", "vacinaAplicacao.dataCadastro");
        hql.addToSelect("vacinaAplicacao.dose", "vacinaAplicacao.dose");
        hql.addToSelect("vacinaAplicacao.lote", "vacinaAplicacao.lote");
        hql.addToSelect("vacinaAplicacao.grupoAtendimento", "vacinaAplicacao.grupoAtendimento");

        hql.addToSelect("estrategia.codigo", "vacinaAplicacao.estrategia.codigo");
        hql.addToSelect("estrategia.estrategiaEsus", "vacinaAplicacao.estrategia.estrategiaEsus");

        hql.addToSelect("tipoVacina.codigo", "vacinaAplicacao.tipoVacina.codigo");
        hql.addToSelect("tipoVacina.descricao", "vacinaAplicacao.tipoVacina.descricao");
        hql.addToSelect("tipoVacina.tipoEsus", "vacinaAplicacao.tipoVacina.tipoEsus");

        hql.addToSelect("profissionalAplicacao.nome", "nomeProfissional");

        hql.addToSelect("usuarioCadsus.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "usuarioCadsus.nome");
        hql.addToSelect("usuarioCadsus.dataNascimento", "usuarioCadsus.dataNascimento");
        hql.addToSelect("usuarioCadsus.sexo", "usuarioCadsus.sexo");

        hql.addToSelect("tipoVacina.codigo", "tipoVacina.codigo");
        hql.addToSelect("tipoVacina.descricao", "tipoVacina.descricao");
        hql.addToSelect("tipoVacina.tipoEsus", "tipoVacina.tipoEsus");

        hql.addToSelect("usuario.codigo", "usuario.codigo");
        hql.addToSelect("usuario.nome", "usuario.nome");

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.referencia", "empresa.referencia");
        hql.addToSelect("empresa.descricao", "empresa.descricao");

        hql.addToSelect("enderecoUsuarioCadsus.codigo", "enderecoUsuarioCadsus.codigo");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "enderecoUsuarioCadsus.numeroLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "enderecoUsuarioCadsus.bairro");
        hql.addToSelect("tipoLogradouro.codigo", "enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
        hql.addToSelect("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
        hql.addToSelect("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
        hql.addToSelect("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
        hql.addToSelect("equipeMicroArea.microArea", "equipeMicroArea.microArea");
        hql.addToSelect("profissional.codigo", "equipeMicroArea.equipeProfissional.profissional.codigo");
        hql.addToSelect("profissional.nome", "equipeMicroArea.equipeProfissional.profissional.nome");
        hql.addToSelect("profissional.referencia", "equipeMicroArea.equipeProfissional.profissional.referencia");
        hql.addToSelect("(select ia.eventoAdverso from InvestigacaoVacinaAplicada iva "
                + " LEFT JOIN iva.investigacaoAgravo ia"
                + " WHERE iva.vacinaAplicacao.codigo = vacinaAplicacao.codigo)", "eventoAdverso");

        hql.addToFrom("VacinaAplicacao vacinaAplicacao"
                + " LEFT JOIN vacinaAplicacao.profissionalAplicacao profissionalAplicacao "
                + " LEFT JOIN vacinaAplicacao.enderecoUsuarioCadsus enderecoUsuarioCadsus "
                + " LEFT JOIN vacinaAplicacao.usuarioCadsus usuarioCadsus"
                + " LEFT JOIN vacinaAplicacao.enderecoDomicilio enderecoDomicilio "
                + " LEFT JOIN enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro "
                + " LEFT JOIN enderecoDomicilio.equipeMicroArea equipeMicroArea "
                + " LEFT JOIN equipeMicroArea.equipeArea equipeArea"
                + " LEFT JOIN equipeMicroArea.equipeProfissional equipeProfissional"
                + " LEFT JOIN equipeProfissional.profissional profissional"
                + " LEFT JOIN vacinaAplicacao.usuario usuario"
                + " LEFT JOIN vacinaAplicacao.empresa empresa"
                + " LEFT JOIN vacinaAplicacao.tipoVacina tipoVacina"
                + " LEFT JOIN vacinaAplicacao.estrategia estrategia"
                + " left join vacinaAplicacao.produtoVacina produtoVacina"
                + " left join produtoVacina.produto produto"
                + " left join produto.fabricante fabricante");

        if (param.getFaixaEtaria() != null) {
            if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(param.getFormaApresentacao())
                    || (param.getFaixaEtariaItem() != null)) {
                hql.addToFrom(" FaixaEtariaItem faixaEtariaItem join faixaEtariaItem.id.faixaEtaria faixaEtaria");
                hql.addToWhereWhithAnd("cast(extract(years from age(vacinaAplicacao.dataAplicacao, usuarioCadsus.dataNascimento)) * 12 + extract(months from age(vacinaAplicacao.dataAplicacao, usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal ");

                hql.addToWhereWhithAnd("faixaEtaria = ", param.getFaixaEtaria());
            }
            if (param.getFaixaEtariaItem() != null) {
                hql.addToWhereWhithAnd("faixaEtariaItem.id.sequencia = ", this.param.getFaixaEtariaItem().getId().getSequencia());
            }
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(this.param.getSomenteComEventosAdversos())) {
            hql.addToWhereWhithAnd("exists(select 1 from InvestigacaoVacinaAplicada iva where iva.vacinaAplicacao.codigo = vacinaAplicacao.codigo)");
        }

        hql.addToWhereWhithAnd("equipeArea = ", this.param.getEquipeArea());
        hql.addToWhereWhithAnd("equipeMicroArea = ", this.param.getEquipeMicroArea());
        hql.addToWhereWhithAnd("vacinaAplicacao.status <> ", VacinaAplicacao.StatusVacinaAplicacao.CANCELADA.value());
        hql.addToWhereWhithAnd("vacinaAplicacao.dataAplicacao", this.param.getPeriodo());
        hql.addToWhereWhithAnd("vacinaAplicacao.flagHistorico = ", param.getHistorico());
        hql.addToWhereWhithAnd("usuarioCadsus = ", param.getUsuarioCadsus());
        hql.addToWhereWhithAnd("tipoVacina = ", param.getTipoVacina());
        hql.addToWhereWhithAnd("empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("usuario = ", param.getUsuario());
        if (this.param.getTabelaCbo() != null ){
            hql.addToWhereWhithAnd("(EXISTS(SELECT 1 FROM ProfissionalCargaHoraria pch " +
                    "WHERE pch.tabelaCbo.cbo = :cbo " +
                    "AND (pch.dataDesativacao is null OR pch.dataDesativacao > current_date) " +
                    "AND coalesce(pch.dataAtivacao, current_date) <= current_date " +
                    "AND coalesce(pch.tipoRegistro, 'I') = 'I' " +
                    "))");
        }
        if (!VacinaCalendario.Doses.NENHUMA_OPCAO.value().equals(param.getDose())) {
            hql.addToWhereWhithAnd("vacinaAplicacao.dose = ", param.getDose());
        }

        if (param.getGrupoAtendimento() != null) {
            hql.addToWhereWhithAnd("vacinaAplicacao.grupoAtendimento = ", param.getGrupoAtendimento());
        }

        if (param.getCalendario() != null) {
            hql.addToWhereWhithAnd("estrategia.codigo = ", param.getCalendario().getCodigo());
        }

        if(RepositoryComponentDefault.SIM_LONG.equals(this.param.getDataValidadeVacina())){
            hql.addToSelect("vacinaAplicacao.dataValidade", "vacinaAplicacao.dataValidade");
        }
        if(RepositoryComponentDefault.SIM_LONG.equals(this.param.getFabricanteVacina())){
            hql.addToSelect("fabricante.descricao", "fabricante.descricao");
        }

        if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.FAIXA_ETARIA.equals(this.param.getFormaApresentacao())) {
            hql.addToSelect("faixaEtariaItem.descricao", "faixaEtariaItem.descricao");
            hql.addToSelect("faixaEtariaItem.id", "faixaEtariaItem.id");
            hql.addToOrder("faixaEtariaItem.idadeInicial");
        } else if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.PACIENTE.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("usuarioCadsus.nome");
        } else if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.TIPO_VACINA.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("tipoVacina.descricao");
        } else if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.UNIDADE.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("empresa.descricao");
        } else if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.AREA.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("equipeArea.descricao" + CRESCENTE_NULLS_FIRST);
        } else if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.MICROAREA.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("equipeArea.descricao" + CRESCENTE_NULLS_FIRST);
            hql.addToOrder("equipeMicroArea.microArea" + CRESCENTE_NULLS_FIRST);
        } else if (RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.PROFISSIONAL.equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("profissionalAplicacao.codigo");
        }else if(RelatorioRelacaoVacinasAplicadasDTOParam.FormaApresentacao.CBO.equals(this.param.getFormaApresentacao())){
            if (this.param.getTabelaCbo() != null ){
                hql.addToSelect("(SELECT min(concat('(', pch.tabelaCbo.cbo,') ',pch.tabelaCbo.descricao)) FROM ProfissionalCargaHoraria pch " +
                                "WHERE (pch.dataDesativacao is null OR pch.dataDesativacao > current_date) " +
                                "AND  pch.tabelaCbo.cbo = :cbo " +
                                "AND pch.empresa.codigo = empresa.codigo " +
                                "AND pch.profissional.codigo = profissionalAplicacao.codigo " +
                                "AND coalesce(pch.dataAtivacao, current_date) <= current_date " +
                                "AND coalesce(pch.tipoRegistro, 'I') = 'I' )"
                        , "cboProfissional");
            } else {
                hql.addToSelect("(SELECT min(concat('(', pch.tabelaCbo.cbo,') ',pch.tabelaCbo.descricao)) FROM ProfissionalCargaHoraria pch " +
                                "WHERE (pch.dataDesativacao is null OR pch.dataDesativacao > current_date) " +
                                "AND pch.empresa.codigo = empresa.codigo " +
                                "AND pch.profissional.codigo = profissionalAplicacao.codigo " +
                                "AND coalesce(pch.dataAtivacao, current_date) <= current_date " +
                                "AND coalesce(pch.tipoRegistro, 'I') = 'I' )"
                        , "cboProfissional");
            }
            hql.addToOrder("cboProfissional");
        }

        if (RelatorioRelacaoVacinasAplicadasDTOParam.TipoRelatorio.RESUMIDO.equals(this.param.getTipoRelatorio())) {
            hql.addToOrder("tipoVacina.descricao");
        }

        if (RelatorioRelacaoVacinasAplicadasDTOParam.Ordenacao.DATA_APLICACAO.equals(this.param.getOrdenacao())) {
            hql.addToOrder("vacinaAplicacao.dataAplicacao");
        } else if (RelatorioRelacaoVacinasAplicadasDTOParam.Ordenacao.PACIENTE.equals(this.param.getOrdenacao())) {
            hql.addToOrder("usuarioCadsus.nome");
        }
    }

    @Override
    public List<RelatorioRelacaoVacinasAplicadasDTO> getResult() {
        return dtoList;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        dtoList = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.dtoList)) {
            if (param.getFaixaEtaria() != null && RepositoryComponentDefault.SIM_LONG.equals(param.getExibirGruposVazios())) {
                List<FaixaEtariaItem> listaFaixaEtariaItens = this.obterFaixaEtariaItem();
                List<FaixaEtariaItem> listaFaixaEtariaItensNaoListadas = this.filtrarFaixaEtariaItemNaoListadas(listaFaixaEtariaItens);
                for (FaixaEtariaItem item : listaFaixaEtariaItensNaoListadas) {
                    RelatorioRelacaoVacinasAplicadasDTO dto = new RelatorioRelacaoVacinasAplicadasDTO();
                    dto.setFaixaEtariaItem(item);
                    dto.setTotalVacina(0L);
                    this.dtoList.add(dto);
                }
                this.dtoList = Lambda.sort(this.dtoList, Lambda.on(RelatorioRelacaoVacinasAplicadasDTO.class).getFaixaEtariaItem().getId().getSequencia());
            }
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        super.setParameters(hql, query);
        if (this.param.getTabelaCbo() != null ) {
            query.setParameter("cbo", param.getTabelaCbo().getCbo());
        }
    }

    private List<TabelaCbo> obterTabelaCBO(HashMap<String, List<TabelaCbo>> memoTabelaCbo, RelatorioRelacaoVacinasAplicadasDTO dto) throws ValidacaoException, DAOException {
        QueryConsultaProfissionalCargaHorariaDTOParam paramConsulta = new QueryConsultaProfissionalCargaHorariaDTOParam();
        paramConsulta.setEmpresa(dto.getEmpresa());
        paramConsulta.setProfissional(dto.getEquipeMicroArea().getEquipeProfissional().getProfissional());
        String key = paramConsulta.getEmpresa().getCodigo() + ":" + paramConsulta.getProfissional().getCodigo();
        if (memoTabelaCbo.containsKey(key)) return memoTabelaCbo.get(key);
        List<TabelaCbo> listTabelaCbo = BOFactory.getBO(ProfissionalFacade.class).consultaCbosProfissional(paramConsulta);
        memoTabelaCbo.put(key, listTabelaCbo);
        return listTabelaCbo;
    }

    private List<FaixaEtariaItem> obterFaixaEtariaItem() {
        QueryCustom.QueryCustomParameter parameter = new QueryCustom
                .QueryCustomParameter(
                VOUtils.montarPath(PROP_ID, PROP_FAIXA_ETARIA),
                param.getFaixaEtaria()
        );
        return LoadManager.getInstance(FaixaEtariaItem.class)
                .addParameter(parameter)
                .start().getList();
    }

    private List<FaixaEtariaItem> filtrarFaixaEtariaItemNaoListadas(List<FaixaEtariaItem> listFaixaEtariaItem) {
        return Lambda.select(
                listFaixaEtariaItem,
                Lambda.having(
                        Lambda.on(FaixaEtariaItem.class).getDescricao(),
                        Matchers.not(
                                Matchers.isIn(
                                        Lambda.selectDistinct(
                                                Lambda.extract(
                                                        this.dtoList,
                                                        Lambda.on(RelatorioRelacaoVacinasAplicadasDTO.class).getFaixaEtariaItem().getDescricao()
                                                )
                                        )
                                )
                        )
                )
        );
    }

    @Override
    public void setDTOParam(RelatorioRelacaoVacinasAplicadasDTOParam param) {
        this.param = param;
    }
}
