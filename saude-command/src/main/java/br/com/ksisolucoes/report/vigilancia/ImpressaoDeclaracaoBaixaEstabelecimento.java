package br.com.ksisolucoes.report.vigilancia;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryImpressaoBaixaEstabelecimento;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoDeclaracaoBaixaEstabelecimento extends AbstractReport<Object> {

    public ImpressaoDeclaracaoBaixaEstabelecimento(Long codigo) {
        super(codigo);
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("EXIBIR_LOGO_SUS", true);

        try {
            String gerarDocumentoComAssinaturaFiscal = BOFactory.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("gerarDocumentoComAssinaturaFiscal");
            if (RepositoryComponentDefault.SIM.equals(gerarDocumentoComAssinaturaFiscal)) {
                addParametro("gerarDocumentoComAssinaturaFiscal", true);
            } else {
                addParametro("gerarDocumentoComAssinaturaFiscal", false);
            }
        } catch (DAOException e) {
            Loggable.log.error(e);
        }

        return new QueryImpressaoBaixaEstabelecimento();
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/declaracao_baixa_estabelecimento.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_declaracao_baixa_alvara_sanitario");
    }
}
