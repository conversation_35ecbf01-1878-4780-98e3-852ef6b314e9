package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioSindromeGripalConsolidadoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioSindromeGripalDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioSindromeGripalDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import ch.lambdaj.Lambda;
import ch.lambdaj.group.Group;
import org.hibernate.Session;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR> S. Schmoeller
 */
public class QueryRelatorioSindromeGripal extends CommandQuery<QueryRelatorioSindromeGripal> implements ITransferDataReport<RelatorioSindromeGripalDTOParam, RelatorioSindromeGripalConsolidadoDTO> {

    private RelatorioSindromeGripalDTOParam param;
    private List<RelatorioSindromeGripalDTO> result;
    private List<RelatorioSindromeGripalConsolidadoDTO> resultConsolidado = new ArrayList<>();

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.setTypeSelect(RelatorioSindromeGripalDTO.class.getName());
        hql.addToSelect("sum(case when uc.sexo = 'M' and cast(extract(years from age(current_date, a.usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, a.usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal then 1 else 0 end)", "totalAtendimentosMasculino");
        hql.addToSelect("sum(case when uc.sexo = 'F' and cast(extract(years from age(current_date, a.usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, a.usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal then 1 else 0 end)", "totalAtendimentosFeminino");
        hql.addToSelect("sum(case when a.cidPrincipal.codigo between 'J09' and 'J189' and uc.sexo = 'M' and cast(extract(years from age(current_date, a.usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, a.usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal then 1 else 0 end)", "totalAtendimentosGripalMasculino");
        hql.addToSelect("sum(case when a.cidPrincipal.codigo between 'J09' and 'J189' and uc.sexo = 'F' and cast(extract(years from age(current_date, a.usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, a.usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal then 1 else 0 end)", "totalAtendimentosGripalFeminino");

        hql.addToSelectAndGroup("c.codigo", "empresa.cidade.codigo");
        hql.addToSelectAndGroup("c.descricao", "empresa.cidade.descricao");

        hql.addToSelectAndGroup("est.codigo", "empresa.cidade.estado.codigo");
        hql.addToSelectAndGroup("est.sigla", "empresa.cidade.estado.sigla");


        if (RelatorioSindromeGripalDTOParam.FormaApresentacao.ESTABELECIMENTO.equals(this.param.getFormaApresentacao())) {
            hql.addToSelectAndGroup("e.codigo", "empresa.codigo");
            hql.addToSelectAndGroupAndOrder("e.descricao", "empresa.descricao");
            hql.addToSelectAndGroup("e.cnes", "empresa.cnes");
        }
        hql.addToSelectAndGroup("faixaEtariaItem.descricao", "faixaEtaria");
        hql.addToGroup("faixaEtariaItem.id.sequencia");

        StringBuilder from = new StringBuilder();
        from.append("Atendimento a , FaixaEtariaItem faixaEtariaItem ");

        hql.addToWhereWhithAnd("faixaEtaria = ", param.getFaixaEtaria());


        if (this.param.getFaixaEtariaItem() != null){
            hql.addToWhereWhithAnd("faixaEtariaItem = ", this.param.getFaixaEtariaItem());
        }
        from.append("JOIN faixaEtariaItem.id.faixaEtaria faixaEtaria ");
        from.append("JOIN a.usuarioCadsus uc ");
        from.append("JOIN a.empresa e ");
        from.append("JOIN e.cidade c ");
        from.append("JOIN c.estado est");

        hql.addToFrom(from.toString());

        if (this.param.getPeriodo() != null){
            hql.addToWhereWhithAnd("a.dataAtendimento ", Data.adjustRangeHour(this.param.getPeriodo()));
        }

        if (param.getUnidade() != null && RelatorioSindromeGripalDTOParam.FormaApresentacao.ESTABELECIMENTO.equals(this.param.getFormaApresentacao())) {
            hql.addToWhereWhithAnd("a.empresa = ", this.param.getUnidade());
        }

        hql.addToOrder("faixaEtariaItem.id.sequencia asc");

        hql.addToWhereWhithAnd("a.status = ", Atendimento.STATUS_FINALIZADO);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(this.result)) {
            Group<RelatorioSindromeGripalDTO> group = Lambda.group(this.result, Lambda.by(Lambda.on(RelatorioSindromeGripalDTO.class).getEmpresa().getCodigo()), Lambda.by(Lambda.on(RelatorioSindromeGripalDTO.class).getFaixaEtaria()));
            RelatorioSindromeGripalConsolidadoDTO consolidadoDTO;
            for (Group<RelatorioSindromeGripalDTO> byEmpresa : group.subgroups()) {
                Empresa empresa = byEmpresa.first().getEmpresa();
                for (RelatorioSindromeGripalDTO dto : this.result) {
                    consolidadoDTO = new RelatorioSindromeGripalConsolidadoDTO();
                    consolidadoDTO.setEmpresa(empresa);
                    consolidadoDTO.setFaixaEtaria(dto.getFaixaEtaria());
                    consolidadoDTO.setTotalAtendimentosFeminino(dto.getTotalAtendimentosFeminino());
                    consolidadoDTO.setTotalAtendimentosGripalFeminino(dto.getTotalAtendimentosGripalFeminino());
                    consolidadoDTO.setTotalAtendimentosMasculino(dto.getTotalAtendimentosMasculino());
                    consolidadoDTO.setTotalAtendimentosGripalMasculino(dto.getTotalAtendimentosGripalMasculino());
                    if (RelatorioSindromeGripalDTOParam.FormaApresentacao.ESTABELECIMENTO.equals(this.param.getFormaApresentacao())){
                        if (dto.getEmpresa() != null){
                            if(empresa.getCodigo().equals(dto.getEmpresa().getCodigo())){
                                resultConsolidado.add(consolidadoDTO);
                            }
                        }
                    }else{
                        resultConsolidado.add(consolidadoDTO);
                    }
                }

            }

        }
    }

    @Override
    public Collection getResult() {
        return resultConsolidado;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    public void setDTOParam(RelatorioSindromeGripalDTOParam param) {
        this.param = param;
    }
}
