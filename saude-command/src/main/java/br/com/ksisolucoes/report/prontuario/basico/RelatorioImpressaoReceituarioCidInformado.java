package br.com.ksisolucoes.report.prontuario.basico;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.ReceituarioMedicoHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImpressaoDeclaracaoReceitaDTO;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImpressaoDeclaracaoReceitasCidDTO;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public class RelatorioImpressaoReceituarioCidInformado extends AbstractReport<Receituario> {

    private List<ReceituarioItem> receituarioItems;

    public RelatorioImpressaoReceituarioCidInformado(List<ReceituarioItem> receituarioItems) {
        this.receituarioItems = receituarioItems;
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/prontuario/jrxml/relatorio_impressao_receituario_limite_excedido.jrxml";
    }

    @Override
    public String getTitulo() {
        return "";
    }

    @Override
    public Collection getCollection() throws DAOException, ValidacaoException {
        RelatorioImpressaoDeclaracaoReceitaDTO dto = new RelatorioImpressaoDeclaracaoReceitaDTO();
        if(getSessao().getEmpresa().getCidade() != null) {
            dto.setDataDeclaracao(getSessao().getEmpresa().getCidade().getDescricao().concat(", ").concat(DataUtil.getDataFormatadaMesString(receituarioItems.get(0).getReceituario().getDataReceituario())));
        } else {
            dto.setDataDeclaracao(DataUtil.getDataFormatadaMesString(receituarioItems.get(0).getReceituario().getDataReceituario()));
        }
        dto.setNomeUsuarioCadsus(receituarioItems.get(0).getReceituario().getUsuarioCadsus().getNomeSocial());

        List<RelatorioImpressaoDeclaracaoReceitasCidDTO> descricaoMedicamentoList = new ArrayList<>();
        Integer count = 1;
        for (ReceituarioItem receituarioItem : receituarioItems) {
            String posologia = new ReceituarioMedicoHelper().gerarDescricaoPosologia(receituarioItem.getProduto(), receituarioItem.getFrequencia(), receituarioItem.getIntervalo(), receituarioItem.getQuantidade(), receituarioItem.getDiasTratamento(), null);
            StringBuilder sb = new StringBuilder(count.toString());
            sb.append(" - ").append(receituarioItem.getProduto().getDescricao());
            sb.append(", ").append(posologia);
            sb.append(" Com o diagnóstico de CID ").append(receituarioItem.getCid().getCodigo()).append(".");
            descricaoMedicamentoList.add(new RelatorioImpressaoDeclaracaoReceitasCidDTO(sb.toString().trim()));
            count++;
        }
        dto.setDescricaoMedicamentoList(descricaoMedicamentoList);
        return Arrays.asList(dto);
    }

}
