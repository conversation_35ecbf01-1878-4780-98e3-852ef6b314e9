package br.com.ksisolucoes.report.consorcio.licitacao.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoLicitacoesDTO;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoLicitacoesDTOParam;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoLicitacoesDTOParam.FormaApresentacao;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoLicitacoesDTOParam.TipoResumo;
import br.com.ksisolucoes.report.consorcio.dto.RelatorioDetalhamentoLicitacoesDTOParam.Ordenacao;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioDetalhamentoLicitacoes extends CommandQuery implements ITransferDataReport<RelatorioDetalhamentoLicitacoesDTOParam, RelatorioDetalhamentoLicitacoesDTO> {

    private RelatorioDetalhamentoLicitacoesDTOParam param;
    private List<RelatorioDetalhamentoLicitacoesDTO> result;
    
    @Override
    protected void createQuery(HQLHelper hql) {
        
        hql.addToSelect("licitacao.codigo", "codigoLicitacao");
        hql.addToSelect("licitacao.dataValidade", "dataValidade");
        hql.addToSelect("produto.codigo", "produto.codigo");
        hql.addToSelect("produto.descricao", "produto.descricao");
        hql.addToSelect("licitacaoItem.precoUnitario", "precoUnitario");
        hql.addToSelect("licitacaoItem.quantidadeLicitada", "quantidadeLicitada");
        hql.addToSelect("licitacaoItem.quantidadeRecebida", "quantidadeRecebida");
        hql.addToSelect("licitacaoItem.precoTotal", "precoTotal");
        hql.addToSelect("licitacaoItem.precoTotalRecebido", "precoTotalRecebido");
        hql.addToSelect("licitacaoItem.status", "status");
        hql.addToSelect("pessoa.codigo", "pessoa.codigo");
        hql.addToSelect("pessoa.descricao", "pessoa.descricao");
        
        hql.setTypeSelect(RelatorioDetalhamentoLicitacoesDTO.class.getName());
        hql.addToFrom("LicitacaoItem licitacaoItem"
                + " left join licitacaoItem.licitacao licitacao"
                + " left join licitacaoItem.pessoa pessoa"
                + " left join licitacaoItem.produto produto");
        
        hql.addToWhereWhithAnd("produto =", param.getProduto());
        hql.addToWhereWhithAnd("pessoa =", param.getPessoa());
        hql.addToWhereWhithAnd("licitacao.codigo =", param.getCodigoLicitacao());
        hql.addToWhereWhithAnd("licitacao.numeroPregao =", param.getNumeroPregao());
        hql.addToWhereWhithAnd("licitacao.dataCadastro ", param.getPeriodo());
        hql.addToWhereWhithAnd("licitacaoItem.status =", param.getStatus());
        
        if(FormaApresentacao.FORNECEDOR.equals(param.getFormaApresentacao())){
            hql.addToOrder("pessoa.descricao");
        }else if(FormaApresentacao.LICITACAO.equals(param.getFormaApresentacao())){
            hql.addToOrder("licitacao.codigo");
        }else if(FormaApresentacao.PRODUTO.equals(param.getFormaApresentacao())){
            hql.addToOrder("produto.descricao");
        }
        
        if(TipoResumo.FORNECEDOR.equals(param.getTipoResumo())){
            hql.addToOrder("pessoa.descricao");
        }else if(TipoResumo.LICITACAO.equals(param.getTipoResumo())){
            hql.addToOrder("licitacao.codigo");
        }else if(TipoResumo.PRODUTO.equals(param.getTipoResumo())){
            hql.addToOrder("produto.descricao");
        }
        
        if(Ordenacao.FORNECEDOR.equals(param.getOrdenacao())){
            hql.addToOrder("pessoa.descricao");
        }else if(Ordenacao.SALDO.equals(param.getOrdenacao())){
            hql.addToOrder("coalesce(licitacaoItem.precoTotal,0) - coalesce(licitacaoItem.precoTotalRecebido,0)");
        }else if(Ordenacao.PRODUTO.equals(param.getOrdenacao())){
            hql.addToOrder("produto.descricao");
        }

    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List)result);
    }
    
    @Override
    public void setDTOParam(RelatorioDetalhamentoLicitacoesDTOParam param) {
        this.param = param;
    }
}
