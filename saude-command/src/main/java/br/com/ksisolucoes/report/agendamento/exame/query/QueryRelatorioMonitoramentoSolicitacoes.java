package br.com.ksisolucoes.report.agendamento.exame.query;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioMonitoramentoSolicitacoesDTO;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioMonitoramentoSolicitacoesDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import ch.lambdaj.Lambda;
import org.hibernate.Query;

import java.util.*;

public class QueryRelatorioMonitoramentoSolicitacoes extends CommandQuery<QueryRelatorioMonitoramentoSolicitacoes> implements ITransferDataReport<RelatorioMonitoramentoSolicitacoesDTOParam, RelatorioMonitoramentoSolicitacoesDTO> {

    private RelatorioMonitoramentoSolicitacoesDTOParam param;
    private Collection<RelatorioMonitoramentoSolicitacoesDTO> list;
    private String tipoControleRegulacao;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(RelatorioMonitoramentoSolicitacoesDTO.class.getName());

        hql.addToSelectAndGroup("tp.codigo", "tipoProcedimento.codigo");
        hql.addToSelectAndGroup("tp.descricao", "tipoProcedimento.descricao");

        hql.addToSelect("sum( case when (sa.status in(:statusList) AND tp.flagTfd = :nao AND sa.tipoFila = :tipoFilaNormal) then 1 else 0 end )", "totalAgendamentoListaEspera");
        hql.addToSelect("sum( case when (sa.status = :statusRegulacaoPendente) then 1 else 0 end )", "totalRegulacaoSolicitacao");
        hql.addToSelect("sum( case when (sa.status = :statusFilaEspera AND tp.flagTfd = :nao AND sa.tipoFila = :tipoFilaRegulacao) then 1 else 0 end )", "totalAgendamentoListaEsperaRegulacao");
        hql.addToSelect("sum( case when (sa.status = :statusRegulacaoDevolvido AND tp.flagTfd = :nao) then 1 else 0 end )", "totalRegulacoesDevolvidas");

        if (!RelatorioMonitoramentoSolicitacoesDTOParam.FormaApresentacao.GERAL.equals(this.param.getFormaApresentacao())) {
            if (RelatorioMonitoramentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_EXECUTANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("empresa.descricao", "unidadeExecutante.descricao");
            } else if (RelatorioMonitoramentoSolicitacoesDTOParam.FormaApresentacao.UNIDADE_SOLICITANTE.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("unidadeOrigem.descricao", "unidadeSolicitante.descricao");
            } else if (RelatorioMonitoramentoSolicitacoesDTOParam.FormaApresentacao.CLASSIFICACAO.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("tpc.descricao", "tipoProcedimentoClassificacao.descricao");
            } else if (RelatorioMonitoramentoSolicitacoesDTOParam.FormaApresentacao.PROCEDIMENTO.equals(this.param.getFormaApresentacao())) {
                hql.addToSelectAndGroupAndOrder("procedimento.descricao", "procedimento.descricao");
                hql.addToSelectAndGroupAndOrder("procedimento.referencia", "procedimento.referencia");
            }
        }
        hql.addToOrder("tp.descricao");

        hql.addToFrom("SolicitacaoAgendamento sa"
                + " left join sa.tipoProcedimento tp"
                + " left join sa.profissional p"
                + " left join tp.tipoProcedimentoClassificacao tpc"
                + " left join sa.empresa empresa"
                + " left join sa.unidadeOrigem unidadeOrigem"
                + " left join sa.usuarioCadsus u"
                + " left join u.enderecoDomicilio ed"
                + " left join ed.equipeMicroArea ema"
                + " left join ema.equipeProfissional ep"
                + " left join ema.equipeArea ea"
                + " left join ep.profissional profissional"
                + " left join sa.procedimento procedimento"
                + " left join procedimento.procedimentoFormaOrganizacao pfo "
                + " left join pfo.roProcedimentoSubGrupo psg "
                + " left join psg.roGrupo pg ");

        hql.addToWhereWhithAnd("empresa ", this.param.getEmpresa());
        if (this.param.getEmpresaOrigem() != null && CollectionUtils.isNotNullEmpty(this.param.getEmpresaOrigem().getValue()) && this.param.getEmpresaOrigem().getValue().size() > 1) {
            hql.addToWhereWhithAnd("(unidadeOrigem.codigo is null OR unidadeOrigem.codigo in(:unidadeOrigemList))");
        }
        hql.addToWhereWhithAnd("tp ", this.param.getTipoProcedimento());

        if (this.param.getProcedimento() != null) {
            hql.addToWhereWhithAnd("procedimento.codigo = ", this.param.getProcedimento().getCodigo());
        }

        hql.addToWhereWhithAnd("tpc ", this.param.getTipoProcedimentoClassificacao());
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        if (RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PRIORIDADE.equals(getTipoControleRegulacao())) {
            query.setParameterList("statusList", Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA, SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE, SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO, SolicitacaoAgendamento.STATUS_REGULACAO_NEGADO));
        } else {
            query.setParameterList("statusList", Arrays.asList(SolicitacaoAgendamento.STATUS_FILA_ESPERA));
        }

        if (this.param.getEmpresaOrigem() != null && CollectionUtils.isNotNullEmpty(this.param.getEmpresaOrigem().getValue()) && this.param.getEmpresaOrigem().getValue().size() > 1) {
            query.setParameterList("unidadeOrigemList", Lambda.extract(this.param.getEmpresaOrigem().getValue(), Lambda.on(Empresa.class).getCodigo()));
        }
        query.setParameter("nao", RepositoryComponentDefault.NAO);
        query.setParameter("tipoFilaNormal", SolicitacaoAgendamento.TIPO_FILA_NORMAL);
        query.setParameter("tipoFilaRegulacao", SolicitacaoAgendamento.TIPO_FILA_REGULACAO);

        query.setParameter("statusFilaEspera", SolicitacaoAgendamento.STATUS_FILA_ESPERA);
        query.setParameter("statusRegulacaoPendente", SolicitacaoAgendamento.STATUS_REGULACAO_PENDENTE);
        query.setParameter("statusRegulacaoDevolvido", SolicitacaoAgendamento.STATUS_REGULACAO_DEVOLVIDO);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<RelatorioMonitoramentoSolicitacoesDTO> getResult() {
        return this.list;
    }

    @Override
    public void setDTOParam(RelatorioMonitoramentoSolicitacoesDTOParam param) {
        this.param = param;
    }

    public String getTipoControleRegulacao() {
        if (tipoControleRegulacao == null) {
            try {
                tipoControleRegulacao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }
}