<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_guias_tiss_portrait" pageWidth="595" pageHeight="842" columnWidth="565" leftMargin="15" rightMargin="15" topMargin="15" bottomMargin="15" uuid="9211d19c-9cb7-4699-a35b-b3ac889c3b2f">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.zoom" value="2.000000000000002"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<parameter name="SUBREPORT_DIR" class="java.lang.String" isForPrompting="false">
		<defaultValueExpression><![CDATA["/home/<USER>/projetos/git/saude-2/saude-command/src/main/java/br/com/ksisolucoes/report/hospital/tiss/jrxml/"]]></defaultValueExpression>
	</parameter>
	<parameter name="LOGO_CONVENIO" class="java.lang.String"/>
	<field name="guiaSolicitacaoInternacao" class="java.util.List"/>
	<group name="Header"/>
	<group name="guias" isStartNewPage="true" minHeightToStartNewPage="300" keepTogether="true">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band height="20">
				<printWhenExpression><![CDATA[$F{guiaSolicitacaoInternacao} != null && !$F{guiaSolicitacaoInternacao}.isEmpty()]]></printWhenExpression>
				<subreport>
					<reportElement x="0" y="0" width="565" height="20" isRemoveLineWhenBlank="true" uuid="eedaa115-e0b4-48f8-969c-55a6fb392336"/>
					<subreportParameter name="LOGO">
						<subreportParameterExpression><![CDATA[$P{LOGO_CONVENIO}]]></subreportParameterExpression>
					</subreportParameter>
					<dataSourceExpression><![CDATA[new net.sf.jasperreports.engine.data.JRBeanCollectionDataSource($F{guiaSolicitacaoInternacao})]]></dataSourceExpression>
					<subreportExpression><![CDATA["/br/com/ksisolucoes/report/hospital/tiss/jrxml/sub_relatorio_guia_solicitacao_internacao.jasper"]]></subreportExpression>
				</subreport>
			</band>
		</groupHeader>
	</group>
	<background>
		<band height="812">
			<rectangle>
				<reportElement mode="Transparent" x="0" y="0" width="565" height="812" uuid="54934ab6-1199-47af-880e-9e33c596426b"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
		</band>
	</background>
</jasperReport>
