package br.com.ksisolucoes.report.entrada.estoque;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioTransferenciaEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.query.QueryRelatorioTransferenciaEstoque;
import br.com.ksisolucoes.util.Bundle;

public class RelatorioTransferenciaEstoque extends AbstractReport<RelatorioTransferenciaEstoqueDTOParam> implements ReportProperties {

    public RelatorioTransferenciaEstoque(RelatorioTransferenciaEstoqueDTOParam param){
        super(param);
    }
    
    @Override
    public String getTitulo() {
        return Bundle.getStringApplication( "rotulo_transferencia_estoque" );
    }
    
    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/entrada/estoque/jrxml/relatorio_transferencia_estoque.jrxml";
    }

    @Override
    public ITransferDataReport getQuery(){
        return new QueryRelatorioTransferenciaEstoque();
    }
    
}
