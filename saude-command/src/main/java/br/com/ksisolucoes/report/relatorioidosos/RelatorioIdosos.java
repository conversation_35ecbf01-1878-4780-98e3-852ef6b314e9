package br.com.ksisolucoes.report.relatorioidosos;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.relacaoidosos.dto.RelatorioIdososDTOParam;
import br.com.ksisolucoes.report.relatorioidosos.query.QueryRelatorioIdosos;
import br.com.ksisolucoes.util.Bundle;

/**
 *
 * <AUTHOR>
 */
public class RelatorioIdosos extends AbstractReport<RelatorioIdososDTOParam> {

    public RelatorioIdosos(RelatorioIdososDTOParam param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/relatorioidosos/jrxml/relatorio_idosos.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relatorio_idosos");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioIdosos();
    }
}
