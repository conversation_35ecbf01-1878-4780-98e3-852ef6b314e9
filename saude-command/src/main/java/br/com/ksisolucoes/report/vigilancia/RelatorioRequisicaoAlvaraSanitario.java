package br.com.ksisolucoes.report.vigilancia;

import br.com.celk.io.FtpImageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoAlvaraDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QRCodeGenerateDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioRequerimentoAlvaraSanitario;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 */
public class RelatorioRequisicaoAlvaraSanitario extends AbstractReport<Object> {

    private boolean alvaraCompra;
    private boolean exibirCnae;
    private boolean marcaDagua;
    private boolean provisorio;
    private String pathMarcaDagua;
    private boolean exibirGrupo;
    private boolean gestaoAtividadeEstabelecimentoPorCnae;
    private ImpressaoAlvaraDTOParam dtoParam;

    public RelatorioRequisicaoAlvaraSanitario(ImpressaoAlvaraDTOParam param) {
        super(param);
        this.dtoParam = param;
    }

    @Override
    public ITransferDataReport getQuery() throws ValidacaoException {
        ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        exibirCnae = ConfiguracaoVigilanciaEnum.TipoAtividadeAlvara.CNAE.value().equals(configuracaoVigilancia.getTipoAtividadeAlvara())
                && !ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(configuracaoVigilancia.getFlagTipoGestaoAtividade());
        exibirGrupo = RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getExibirGrupoAlvara());
        gestaoAtividadeEstabelecimentoPorCnae = ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(configuracaoVigilancia.getFlagTipoGestaoAtividade());
        if (!ConfiguracaoVigilanciaEnum.TipoMarcaDagua.NAO_EXIBIR.value().equals(configuracaoVigilancia.getMarcaDaguaAlvara())) {
            marcaDagua = true;
            if (ConfiguracaoVigilanciaEnum.TipoMarcaDagua.ALVARA_SANITARIO.value().equals(configuracaoVigilancia.getMarcaDaguaAlvara())) {
                pathMarcaDagua = "/br/com/ksisolucoes/imagens/marcadagua/alvara_sanitario.png";
                if (provisorio) {
                    pathMarcaDagua = "/br/com/ksisolucoes/imagens/marcadagua/alvara_sanitario_provisorio.png";
                }
            } else {
                try {
                    GerenciadorArquivo brasaoMarcaDagua = configuracaoVigilancia.getBrasaoMarcaDagua();
                    if (brasaoMarcaDagua != null && StringUtils.trimToNull(brasaoMarcaDagua.getCaminho()) != null) {
                        pathMarcaDagua = new FtpImageUtil().downloadImage(brasaoMarcaDagua.getCaminho());
                    } else {
                        marcaDagua = false;
                        Loggable.log.warn("BRASÃO NAO DEFINIDO!");
                    }
                } catch (Throwable ex) {
                    marcaDagua = false;
                    Loggable.log.warn("BRASÃO NAO ENCONTRADO!", ex);
                }
            }
        }
        addParametro("alvaraCompra", this.alvaraCompra);
        addParametro("exibirCnae", this.exibirCnae);
        addParametro("marcaDagua", this.marcaDagua);
        addParametro("provisorio", this.provisorio);
        addParametro("pathMarcaDagua", this.pathMarcaDagua);
        addParametro("exibirGrupo", this.exibirGrupo);
        addParametro("gestaoAtividadeEstabelecimentoPorCnae", this.gestaoAtividadeEstabelecimentoPorCnae);
        addParametro("textoAutorizacaoExumacao1", configuracaoVigilancia.getTextoReqExumacao1());
        addParametro("textoAutorizacaoExumacao2", configuracaoVigilancia.getTextoReqExumacao2());
        addParametro("dataProvissorio", DataUtil.getDataAtual());
        //addParametro("cidade", SessaoAplicacaoImp.getInstance().getEmpresa().getCidade().getDescricao());
        //addParametro("uf", SessaoAplicacaoImp.getInstance().getEmpresa().getCidade().getEstado().getSigla());

        QRCodeGenerateDTOParam qrCodeParamAlvara = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageAlvara(), dtoParam.getChaveQrCode());
        if (qrCodeParamAlvara != null) {
            addParametro("urlQRcode", qrCodeParamAlvara.generateURL());
        } else {
            addParametro("urlQRcode", "");
        }

        Empresa empresa = LoadManager.getInstance(Empresa.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, configuracaoVigilancia.getEmpresa().getCodigo()))
                .setMaxResults(1).start().getVO();

        Cidade cidade = LoadManager.getInstance(Cidade.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Cidade.PROP_CODIGO, empresa.getCidade().getCodigo()))
                .setMaxResults(1).start().getVO();

        addParametro("cidade", empresa.getCidade().getDescricao());
        addParametro("uf", cidade.getEstado().getSigla());

        StringBuilder enderecoVigilanciaBuilder = new StringBuilder(Coalesce.asString(empresa.getEnderecoCidadeBairroFormatado()));
        enderecoVigilanciaBuilder.append(" - CEP: ");
        enderecoVigilanciaBuilder.append(empresa.getCepFormatado());
        enderecoVigilanciaBuilder.append("\n");
        enderecoVigilanciaBuilder.append(" Fone: ");
        enderecoVigilanciaBuilder.append(empresa.getTelefoneFormatado());
        addParametro("ENDERECO_VIGILANCIA", enderecoVigilanciaBuilder.toString());
        try {
            String gerarDocumentoComAssinaturaFiscal = BOFactory.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("gerarDocumentoComAssinaturaFiscal");
            if (RepositoryComponentDefault.SIM.equals(gerarDocumentoComAssinaturaFiscal)) {
                addParametro("gerarDocumentoComAssinaturaFiscal", true);
            } else {
                addParametro("gerarDocumentoComAssinaturaFiscal", false);
            }
        } catch (DAOException e) {
            br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        return new QueryRelatorioRequerimentoAlvaraSanitario().setAlvaraCompra(alvaraCompra, exibirCnae);
    }

    @Override
    public String getXML() {
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/requerimento_alvara_inicial.jrxml";
    }

    @Override
    public String getTitulo() {
        String titulo = Bundle.getStringApplication("rotulo_alvara_sanitario_municipal");
        if (provisorio) {
            titulo = Bundle.getStringApplication("rotulo_alvara_sanitario_municipal_provisorio");
        }
        return titulo;
    }

    public RelatorioRequisicaoAlvaraSanitario setProvisorio(boolean provisorio) {
        this.provisorio = provisorio;
        return this;
    }
}
