package br.com.ksisolucoes.report.vigilancia;

import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRelacaoEstabelecimentosSemRTDTOParam;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.report.vigilancia.query.QueryRelatorioRelacaoEstabelecimentosSemRT;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoEstabelecimentosSemRT extends AbstractReport<RelatorioRelacaoEstabelecimentosSemRTDTOParam> {

    public RelatorioRelacaoEstabelecimentosSemRT(RelatorioRelacaoEstabelecimentosSemRTDTOParam param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryRelatorioRelacaoEstabelecimentosSemRT();
    }

    @Override
    public String getXML() {
        this.addParametro("LABEL_ATIVIDADE", VigilanciaHelper.isGestaoAtividadeCnae() ? "CNAE" : "Atividade");
        return "/br/com/ksisolucoes/report/vigilancia/jrxml/relatorio_relacao_estabelecimentos_sem_rt.jrxml";
    }

    @Override
    public void addParametro(String name, Object value) {
        super.addParametro(name, value);
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_relacao_estabelecimentos_sem_rt");
    }

}
