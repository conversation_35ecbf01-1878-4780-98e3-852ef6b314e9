package br.com.ksisolucoes.report.prontuario.comorbidade.query;

import br.com.celk.unidadesaude.comorbidade.interfaces.dto.QueryRelatorioConsolidadoPacientesComorbidadesDTO;
import br.com.celk.unidadesaude.comorbidade.interfaces.dto.QueryRelatorioConsolidadoPacientesComorbidadesDTOParam;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.vo.basico.FaixaEtaria;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryRelatorioConsolidadoPacientesComorbidades extends CommandQuery<QueryRelatorioConsolidadoPacientesComorbidades> implements ITransferDataReport<QueryRelatorioConsolidadoPacientesComorbidadesDTOParam, QueryRelatorioConsolidadoPacientesComorbidadesDTO> {

    private QueryRelatorioConsolidadoPacientesComorbidadesDTOParam param;
    private Collection<QueryRelatorioConsolidadoPacientesComorbidadesDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {

        hql.addToSelect("count(usuarioCadsus.codigo)", "quantidade");

        hql.addToSelectAndGroup("doenca.codigo", "doenca.codigo");
        hql.addToSelectAndGroup("doenca.sigla", "doenca.sigla");
        hql.addToSelectAndGroup("doenca.descricao", "doenca.descricao");

        hql.addToSelectAndGroup("faixaEtaria.codigo", "faixaEtariaItem.id.faixaEtaria.codigo");
        hql.addToSelectAndGroup("faixaEtariaItem.id.sequencia", "faixaEtariaItem.id.sequencia");
        hql.addToSelectAndGroup("faixaEtariaItem.descricao", "faixaEtariaItem.descricao");

        hql.setTypeSelect(QueryRelatorioConsolidadoPacientesComorbidadesDTO.class.getName());
        hql.addToFrom("UsuarioCadsusDoenca usuarioCadsusDoenca "
                + " left join usuarioCadsusDoenca.id.doenca doenca"
                + " left join usuarioCadsusDoenca.id.usuarioCadsus usuarioCadsus"
                + " left join usuarioCadsus.enderecoDomicilio enderecoDomicilio"
                + " left join enderecoDomicilio.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.empresa empresa"
                + " left join equipeMicroArea.equipeProfissional equipeProfissional"
                + " left join equipeProfissional.profissional profissional"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join enderecoDomicilio.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro"
        );

        hql.addToFrom("FaixaEtariaItem faixaEtariaItem "
                + " join faixaEtariaItem.id.faixaEtaria faixaEtaria ");

        hql.addToWhereWhithAnd("faixaEtaria.codigo = ", FaixaEtaria.FAIXA_ETARIA_SIAB);

        hql.addToWhereWhithAnd("cast(extract(years from age(current_date, usuarioCadsus.dataNascimento)) * 12 + extract(months from age(current_date, usuarioCadsus.dataNascimento)) as long) between faixaEtariaItem.idadeInicial and faixaEtariaItem.idadeFinal ");

        hql.addToWhereWhithAnd("equipeArea = ", this.param.getArea());

        if (this.param.getEstabelecimento().size() == 1) {
            hql.addToWhereWhithAnd("empresa in ", param.getEstabelecimento());
        }
        hql.addToWhereWhithAnd("doenca = ", this.param.getDoenca());
        hql.addToWhereWhithAnd("equipeMicroArea = ", this.param.getEquipeMicroArea());
        hql.addToWhereWhithAnd("profissional = ", this.param.getProfissional());
        hql.addToWhereWhithAnd("usuarioCadsus.situacao not in ", Arrays.asList(UsuarioCadsus.SITUACAO_INATIVO, UsuarioCadsus.SITUACAO_EXCLUIDO));

        String tipoOrdenacao = "asc nulls first";
        if (QueryRelatorioConsolidadoPacientesComorbidadesDTOParam.FormaApresentacao.SEXO.value().equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("usuarioCadsus.sexo " + tipoOrdenacao);
            hql.addToSelectAndGroup("usuarioCadsus.sexo", "sexo");
        } else if (QueryRelatorioConsolidadoPacientesComorbidadesDTOParam.FormaApresentacao.AREA.value().equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("equipeArea.descricao " + tipoOrdenacao);
            hql.addToSelectAndGroup("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
            hql.addToSelectAndGroup("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
        } else if (QueryRelatorioConsolidadoPacientesComorbidadesDTOParam.FormaApresentacao.MICRO_AREA.value().equals(this.param.getFormaApresentacao())) {
            hql.addToOrder("equipeArea.descricao " + tipoOrdenacao);
            hql.addToOrder("equipeMicroArea.microArea " + tipoOrdenacao);
            hql.addToSelectAndGroup("equipeArea.descricao", "equipeMicroArea.equipeArea.descricao");
            hql.addToSelectAndGroup("equipeMicroArea.microArea", "equipeMicroArea.microArea");
            hql.addToSelectAndGroup("equipeArea.codigo", "equipeMicroArea.equipeArea.codigo");
        }

        hql.addToOrder("doenca.descricao " + tipoOrdenacao);
    }

    @Override
    public void setDTOParam(QueryRelatorioConsolidadoPacientesComorbidadesDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public Collection<QueryRelatorioConsolidadoPacientesComorbidadesDTO> getResult() {
        return this.result;
    }
}
