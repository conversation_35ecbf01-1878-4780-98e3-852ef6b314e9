<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_carteira_vacinacao" pageWidth="845" pageHeight="595" orientation="Landscape" columnWidth="815" leftMargin="20" rightMargin="10" topMargin="20" bottomMargin="20" uuid="e6e2dd2e-4c8c-4851-b179-0139236a2fb7">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.771561000000018"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.ksisolucoes.report.vacina.dto.RelatorioCarteiraVacinacaoDTO"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.report.vacina.dto.Dose"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<style name="BandStyle" mode="Opaque" fontName="Arial" fontSize="9">
		<conditionalStyle>
			<conditionExpression><![CDATA[$V{ROW_COUNT}%2==0]]></conditionExpression>
			<style backcolor="#DFDFDF"/>
		</conditionalStyle>
	</style>
	<style name="Crosstab Data Text" backcolor="#DFDFDF" hAlign="Center" fontName="Arial"/>
	<parameter name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="tipoVacina" class="br.com.ksisolucoes.vo.vacina.TipoVacina"/>
	<field name="doseTemporaria" class="br.com.ksisolucoes.report.vacina.dto.Dose"/>
	<field name="descricaoVacina" class="java.lang.String"/>
	<field name="siglaDose" class="java.lang.String">
		<fieldDescription><![CDATA[siglaDose]]></fieldDescription>
	</field>
	<field name="descricaoTipoVacina" class="java.lang.String">
		<fieldDescription><![CDATA[descricaoTipoVacina]]></fieldDescription>
	</field>
	<field name="descricaoDoseFormatada" class="java.lang.String">
		<fieldDescription><![CDATA[descricaoDoseFormatada]]></fieldDescription>
	</field>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<group name="Group" isReprintHeaderOnEachPage="true">
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="63" splitType="Stretch">
				<crosstab columnBreakOffset="20" ignoreWidth="false">
					<reportElement x="0" y="1" width="815" height="62" uuid="08121ffc-4391-4f80-9782-76d6f095a392"/>
					<box>
						<pen lineColor="#FFFFFF"/>
						<topPen lineColor="#FFFFFF"/>
						<leftPen lineColor="#FFFFFF"/>
						<bottomPen lineColor="#FFFFFF"/>
						<rightPen lineColor="#FFFFFF"/>
					</box>
					<crosstabHeaderCell>
						<cellContents>
							<staticText>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="12" uuid="c16645e7-90da-4627-81a0-c9003412ed6e"/>
								<textElement>
									<font size="9" isBold="true"/>
								</textElement>
								<text><![CDATA[Vacina]]></text>
							</staticText>
						</cellContents>
					</crosstabHeaderCell>
					<rowGroup name="descricaoVacina" width="50" headerPosition="Middle">
						<bucket class="java.lang.String">
							<bucketExpression><![CDATA[$F{descricaoVacina}]]></bucketExpression>
						</bucket>
						<crosstabRowHeader>
							<cellContents mode="Opaque" style="BandStyle">
								<box>
									<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
								</box>
								<textField>
									<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="50" backcolor="#FFFFFF" uuid="a5fe74d9-6762-430d-98fb-58fc254578ac"/>
									<box>
										<pen lineWidth="0.0"/>
										<topPen lineWidth="0.0"/>
										<leftPen lineWidth="0.0"/>
										<bottomPen lineWidth="0.0"/>
										<rightPen lineWidth="0.0"/>
									</box>
									<textElement verticalAlignment="Middle">
										<font fontName="Arial" size="6"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{descricaoVacina}.substring(4)]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabRowHeader>
						<crosstabTotalRowHeader>
							<cellContents/>
						</crosstabTotalRowHeader>
					</rowGroup>
					<columnGroup name="siglaDose" height="12" headerPosition="Center">
						<bucket class="br.com.ksisolucoes.report.vacina.dto.Dose">
							<bucketExpression><![CDATA[$F{doseTemporaria}]]></bucketExpression>
							<comparatorExpression><![CDATA[new br.com.ksisolucoes.report.vacina.dto.Dose.ComparatorDose()]]></comparatorExpression>
						</bucket>
						<crosstabColumnHeader>
							<cellContents backcolor="#F0F8FF" mode="Opaque">
								<textField>
									<reportElement style="BandStyle" x="0" y="0" width="45" height="12" uuid="65eaefec-aeba-46c8-91ac-9f13122bc7e4"/>
									<textElement textAlignment="Center">
										<font fontName="Arial" size="9" isBold="true"/>
									</textElement>
									<textFieldExpression><![CDATA[$V{siglaDose}.getSigla()]]></textFieldExpression>
								</textField>
							</cellContents>
						</crosstabColumnHeader>
						<crosstabTotalColumnHeader>
							<cellContents/>
						</crosstabTotalColumnHeader>
					</columnGroup>
					<measure name="descricaoDoseFormatadaMeasure" class="java.lang.String">
						<measureExpression><![CDATA[$F{doseTemporaria}.doseFormatada()]]></measureExpression>
					</measure>
					<crosstabCell width="45" height="50">
						<cellContents mode="Opaque" style="BandStyle">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField isBlankWhenNull="true">
								<reportElement style="Crosstab Data Text" x="0" y="0" width="45" height="50" backcolor="#DFDFDF" uuid="d7b286e2-5088-431b-877f-e1debdadc0f3"/>
								<box>
									<pen lineWidth="0.0"/>
									<topPen lineWidth="0.0"/>
									<leftPen lineWidth="0.0"/>
									<bottomPen lineWidth="0.0"/>
									<rightPen lineWidth="0.0"/>
								</box>
								<textElement verticalAlignment="Middle" markup="styled">
									<font fontName="Arial" size="6"/>
								</textElement>
								<textFieldExpression><![CDATA[$V{descricaoDoseFormatadaMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell height="25" rowTotalGroup="descricaoVacina">
						<cellContents backcolor="#BFE1FF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="e30397f3-bbab-4167-a6b0-6454c6624ea7"/>
								<textFieldExpression><![CDATA[$V{siglaDose}.getSigla()]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell width="50" columnTotalGroup="siglaDose">
						<cellContents backcolor="#BFE1FF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="ed3a256e-9d7b-464b-9351-6368c6b87cb3"/>
								<textFieldExpression><![CDATA[$V{descricaoDoseFormatadaMeasure}]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
					<crosstabCell rowTotalGroup="descricaoVacina" columnTotalGroup="siglaDose">
						<cellContents backcolor="#BFE1FF" mode="Opaque">
							<box>
								<pen lineWidth="0.5" lineStyle="Solid" lineColor="#000000"/>
							</box>
							<textField>
								<reportElement style="Crosstab Data Text" x="0" y="0" width="50" height="25" uuid="e11aa3bc-1ac3-43f8-9c22-c19453e4e7a6"/>
								<textFieldExpression><![CDATA[$V{siglaDose}.doseFormatada()]]></textFieldExpression>
							</textField>
						</cellContents>
					</crosstabCell>
				</crosstab>
			</band>
		</groupFooter>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="54" splitType="Stretch">
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="4" y="11" width="30" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="d5e79b13-9c3d-4f85-881a-cc1d70b915ab"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Nome*/$V{BUNDLE}.getStringApplication("rotulo_nome")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="34" y="11" width="319" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="6fb0a948-ce42-4966-b1be-c574640a52d9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="366" y="32" width="80" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="8b9f19f6-b5a8-44a6-814a-ab2d3e380ff2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*datanascimento*/$V{BUNDLE}.getStringApplication("rotulo_data_nascimento")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="446" y="32" width="140" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="aae091fe-6946-43cf-84fd-b192ee20afc3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="366" y="11" width="30" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="358bb9cf-818e-4afe-9f3b-5609d8c3b87e"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Sexo*/
$V{BUNDLE}.getStringApplication("rotulo_sexo")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="396" y="11" width="84" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="61b5aa38-ef8f-439d-bc83-e9ab63731fa9"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="4" y="32" width="97" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="bccc1b13-a3a4-4e84-b149-ed83d237b948"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Nome Social*/$V{BUNDLE}.getStringApplication("rotulo_nome_social")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-35" positionType="Float" mode="Transparent" x="64" y="32" width="289" height="12" forecolor="#000000" backcolor="#FFFFFF" uuid="56647426-a379-4bf4-8f90-fb931951cfd2">
					<printWhenExpression><![CDATA[RepositoryComponentDefault.SIM_LONG.equals($P{usuarioCadsus}.getUtilizaNomeSocial())]]></printWhenExpression>
				</reportElement>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{usuarioCadsus}.getApelido()]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<columnFooter>
		<band splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
</jasperReport>
