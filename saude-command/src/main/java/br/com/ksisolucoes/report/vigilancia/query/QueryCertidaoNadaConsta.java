package br.com.ksisolucoes.report.vigilancia.query;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioCertidaoNadaConstaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioCertidaoNadaConstaDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.system.consulta.Projections;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import com.ibm.icu.lang.UCharacter;
import com.ibm.icu.text.BreakIterator;
import org.hibernate.Session;
import org.hibernate.criterion.Restrictions;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryCertidaoNadaConsta extends CommandQuery<QueryCertidaoNadaConsta> implements ITransferDataReport<RelatorioCertidaoNadaConstaDTOParam, RelatorioCertidaoNadaConstaDTO> {

    private RelatorioCertidaoNadaConstaDTOParam param;
    private List<RelatorioCertidaoNadaConstaDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("requerimentoNadaConsta.codigo", "requerimentoNadaConsta.codigo");

        hql.addToSelect("estabelecimento.codigo", "requerimentoNadaConsta.estabelecimento.codigo");
        hql.addToSelect("estabelecimento.razaoSocial", "requerimentoNadaConsta.estabelecimento.razaoSocial");
        hql.addToSelect("estabelecimento.cnpjCpf", "requerimentoNadaConsta.estabelecimento.cnpjCpf");
        hql.addToSelect("estabelecimento.numeroLogradouro", "requerimentoNadaConsta.estabelecimento.numeroLogradouro");

        hql.addToSelect("vigilanciaEndereco.codigo", "requerimentoNadaConsta.estabelecimento.vigilanciaEndereco.codigo");
        hql.addToSelect("vigilanciaEndereco.logradouro", "requerimentoNadaConsta.estabelecimento.vigilanciaEndereco.logradouro");
        hql.addToSelect("vigilanciaEndereco.bairro", "requerimentoNadaConsta.estabelecimento.vigilanciaEndereco.bairro");

        hql.addToSelect("cidadeEstabelecimento.codigo", "requerimentoNadaConsta.estabelecimento.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("cidadeEstabelecimento.descricao", "requerimentoNadaConsta.estabelecimento.vigilanciaEndereco.cidade.descricao");

        hql.addToSelect("estadoEstabelecimento.codigo", "requerimentoNadaConsta.estabelecimento.vigilanciaEndereco.cidade.estado.codigo");
        hql.addToSelect("estadoEstabelecimento.sigla", "requerimentoNadaConsta.estabelecimento.vigilanciaEndereco.cidade.estado.sigla");

        hql.addToSelect("responsavelTecnico.codigo", "requerimentoNadaConsta.responsavelTecnico.codigo");
        hql.addToSelect("responsavelTecnico.nome", "requerimentoNadaConsta.responsavelTecnico.nome");
        hql.addToSelect("responsavelTecnico.numeroRegistro", "requerimentoNadaConsta.responsavelTecnico.numeroRegistro");
        hql.addToSelect("responsavelTecnico.unidadeFederacaoRegistro", "requerimentoNadaConsta.responsavelTecnico.unidadeFederacaoRegistro");

        hql.addToSelect("orgaoEmissorInscricaoResponsavel.codigo", "requerimentoNadaConsta.responsavelTecnico.orgaoEmissor.codigo");
        hql.addToSelect("orgaoEmissorInscricaoResponsavel.sigla", "requerimentoNadaConsta.responsavelTecnico.orgaoEmissor.sigla");

        hql.addToSelect("requerimentoVigilancia.codigo", "requerimentoNadaConsta.requerimentoVigilancia.codigo");
        hql.addToSelect("requerimentoVigilancia.protocolo", "requerimentoNadaConsta.requerimentoVigilancia.protocolo");
        hql.addToSelect("requerimentoVigilancia.dataRequerimento", "requerimentoNadaConsta.requerimentoVigilancia.dataRequerimento");

        hql.addToSelect("uf.codigo", "requerimentoNadaConsta.requerimentoVigilancia.usuarioFinalizacao.codigo");
        hql.addToSelect("uf.nome", "requerimentoNadaConsta.requerimentoVigilancia.usuarioFinalizacao.nome");
        hql.setTypeSelect(RelatorioCertidaoNadaConstaDTO.class.getName());

        StringBuilder from = new StringBuilder();
        from.append("RequerimentoNadaConsta requerimentoNadaConsta");
        from.append(" left join requerimentoNadaConsta.requerimentoVigilancia requerimentoVigilancia ");
        from.append(" left join requerimentoVigilancia.usuarioFinalizacao uf ");
        from.append(" left join requerimentoNadaConsta.estabelecimento estabelecimento ");
        from.append(" left join estabelecimento.vigilanciaEndereco vigilanciaEndereco ");
        from.append(" left join vigilanciaEndereco.cidade cidadeEstabelecimento ");
        from.append(" left join cidadeEstabelecimento.estado estadoEstabelecimento ");
        from.append(" left join requerimentoNadaConsta.responsavelTecnico responsavelTecnico ");
        from.append(" left join responsavelTecnico.orgaoEmissor orgaoEmissorInscricaoResponsavel ");

        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("requerimentoVigilancia.codigo = ", this.param.getCodigoRequerimentoVigilancia());
    }

    @Override
    public Collection getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List) result);
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if(CollectionUtils.isNotNullEmpty(this.result)) {
            Long codigoProfissional = null;
            Usuario usuarioFinalizacao = this.result.get(0).getRequerimentoNadaConsta().getRequerimentoVigilancia().getUsuarioFinalizacao();
            if(usuarioFinalizacao != null && usuarioFinalizacao.getCodigo() != null) {
                codigoProfissional = (Long) HibernateSessionFactory.getSession().createCriteria(Usuario.class)
                        .add(Restrictions.idEq(usuarioFinalizacao.getCodigo()))
                        .setProjection(Projections.property(VOUtils.montarPath(Usuario.PROP_PROFISSIONAL, Profissional.PROP_CODIGO)))
                        .uniqueResult();
            }
            if (codigoProfissional != null) {
                Profissional profissional = VigilanciaHelper.reloadProfissionalImpressao(codigoProfissional);
                profissional.setNome((UCharacter.toTitleCase(profissional.getNome().toLowerCase(), BreakIterator.getTitleInstance())));
                this.result.get(0).setProfissionalFinalizacao(profissional);
            }
        }
    }

    @Override
    public void setDTOParam(RelatorioCertidaoNadaConstaDTOParam param) {
        this.param = param;
    }

}