package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoHospitalDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoHospitalDTOParam;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItem;
import br.com.ksisolucoes.vo.basico.FaixaEtariaItemPK;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import org.hibernate.Criteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

public class QueryUsuariosAtendidosFaixaEtariaHospital extends QueryPerfilAtendimentoHospital {

    private String prop;
    private List<RelatorioPerfilAtendimentoHospitalDTO> result;

    public QueryUsuariosAtendidosFaixaEtariaHospital(RelatorioPerfilAtendimentoHospitalDTOParam param, String prop) {
        super(param);
        this.prop = prop;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        String descricao = "";
        try {
            Criteria cFaixaEtariaItem = getSession().createCriteria(FaixaEtariaItem.class);
            cFaixaEtariaItem.add(Restrictions.eq(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_FAIXA_ETARIA), getParam().getFaixaEtaria()));
            cFaixaEtariaItem.addOrder(Order.desc(VOUtils.montarPath(FaixaEtariaItem.PROP_ID, FaixaEtariaItemPK.PROP_SEQUENCIA)));
            List<FaixaEtariaItem> faixas = cFaixaEtariaItem.list();
            int count = 0;
            if (CollectionUtils.isNotNullEmpty(faixas)) {
                for (FaixaEtariaItem faixaEtariaItem : faixas) {
                    descricao += " (case when ((extract(years from age(a.dataAtendimento,  usu.dataNascimento)) * 12 + extract(months from age(a.dataAtendimento,  usu.dataNascimento))) >= " + faixaEtariaItem.getIdadeInicial() + " " + " and (extract(years from age(a.dataAtendimento,  usu.dataNascimento)) * 12 + extract(months from age(a.dataAtendimento,  usu.dataNascimento))) <= " + faixaEtariaItem.getIdadeFinal() + " ) " + " then '" + faixaEtariaItem.getDescricao()+"%*"+faixaEtariaItem.getId().getSequencia() + "' " + " else ";
                    count++;
                    if (count == faixas.size()) {
                        descricao += " 'outra' ";
                    }
                }
                for (FaixaEtariaItem faixaEtariaItem : faixas) {
                    descricao += " end) ";
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        hql.addToSelect(descricao, "descricao");
        hql.addToSelect("'" + prop + "'", "custom");
        hql.addToSelect("count(usu.codigo)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(usu1.codigo)");
            hqlTotal.addToFrom("Atendimento a1" + " join a1.usuarioCadsus usu1");
            hqlTotal.addToWhereWhithAnd("a1.codigo = a1.atendimentoPrincipal.codigo");
            addWhereAtendimento(hqlTotal, "a1");
            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("Atendimento a" + " join a.usuarioCadsus usu");
        hql.setTypeSelect(RelatorioPerfilAtendimentoHospitalDTO.class.getName());
        addWhereAtendimento(hql, "a");
        hql.addToWhereWhithAnd("a.codigo = a.atendimentoPrincipal.codigo");
        hql.addToGroup("1");
        hql.addToOrder("3 desc");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
        for (RelatorioPerfilAtendimentoHospitalDTO dto : this.result) {
            if (dto.getDescricao() != null) {
                int a;
                Long sequencia;
                String des = dto.getDescricao().trim();
                a = des.indexOf("%*");
                if (a > 0) {
                    sequencia = Long.parseLong(des.substring(a + 2, des.length()));
                    dto.setSequencia(sequencia);
                    dto.setDescricao(des.substring(0, a));
                }
            } else {
                dto.setDescricao(Bundle.getStringApplication("rotulo_nao_informada"));
            }
        }
        if (CollectionUtils.isNotNullEmpty(this.result) ){
            Collections.sort(this.result, new CollectionUtils.BeanComparator("sequencia"));
        }
    }

    @Override
    public List<RelatorioPerfilAtendimentoHospitalDTO> getResult() {
        return result;
    }
}
