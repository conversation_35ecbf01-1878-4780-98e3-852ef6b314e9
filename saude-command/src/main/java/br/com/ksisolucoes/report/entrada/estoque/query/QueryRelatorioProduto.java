/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioProdutoDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioProdutoParam;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaSetor;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoEstoque;
import ch.lambdaj.Lambda;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryRelatorioProduto extends CommandQuery<QueryRelatorioProduto> {

    private List<RelatorioProdutoDTO> result;
    private RelatorioProdutoParam param;

    public QueryRelatorioProduto(RelatorioProdutoParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("empresa.codigo", "estoqueEmpresa.id.empresa.codigo");
        hql.addToSelect("empresa.referencia", "estoqueEmpresa.id.empresa.referencia");
        hql.addToSelect("empresa.descricao", "estoqueEmpresa.id.empresa.descricao");

        hql.addToSelect("produto.codigo", "estoqueEmpresa.id.produto.codigo");
        hql.addToSelect("produto.referencia", "estoqueEmpresa.id.produto.referencia");
        hql.addToSelect("produto.descricao", "estoqueEmpresa.id.produto.descricao");
        hql.addToSelect("produto.usoContinuo", "estoqueEmpresa.id.produto.usoContinuo");
        hql.addToSelect("produto.flagControleMinimo", "estoqueEmpresa.id.produto.flagControleMinimo");
        hql.addToSelect("produto.dataCadastro", "estoqueEmpresa.id.produto.dataCadastro");

        hql.addToSelect("produto.unidade.codigo", "estoqueEmpresa.id.produto.unidade.codigo");
        hql.addToSelect("produto.unidade.descricao", "estoqueEmpresa.id.produto.unidade.descricao");

        hql.addToSelect("tipoReceita.codigo", "estoqueEmpresa.id.produto.tipoReceita.codigo");
        hql.addToSelect("tipoReceita.tipoReceita", "estoqueEmpresa.id.produto.tipoReceita.tipoReceita");

        hql.addToSelect("produto.subGrupo.id.codigo", "estoqueEmpresa.id.produto.subGrupo.id.codigo");
        hql.addToSelect("produto.subGrupo.descricao", "estoqueEmpresa.id.produto.subGrupo.descricao");

        hql.addToSelect("produto.subGrupo.roGrupoProduto.codigo", "estoqueEmpresa.id.produto.subGrupo.roGrupoProduto.codigo");
        hql.addToSelect("produto.subGrupo.roGrupoProduto.descricao", "estoqueEmpresa.id.produto.subGrupo.roGrupoProduto.descricao");

        hql.addToSelect("ee.estoqueMinimo", "estoqueEmpresa.estoqueMinimo");
        hql.addToSelect("ee.estoqueMaximo", "estoqueEmpresa.estoqueMaximo");
        hql.addToSelect("ee.precoMedio", "estoqueEmpresa.precoMedio");
        hql.addToSelect("ee.precoCusto", "estoqueEmpresa.precoCusto");
        hql.addToSelect("ee.ultimoPreco", "estoqueEmpresa.ultimoPreco");
        hql.addToSelect("ee.flagAtivo", "estoqueEmpresa.flagAtivo");
        hql.addToSelect("ee.estoqueFisico", "estoqueEmpresa.estoqueFisico");

        hql.addToSelect("(select sum(ge.estoqueFisico)"
                        + " from GrupoEstoque ge "
                        + "where ge.id.estoqueEmpresa = ee "
                        + "  and ge.roDeposito = empresa.empresaMaterial.depositoVencido)", "estoqueEmpresa.estoqueReservado");

        hql.addToFrom("EstoqueEmpresa ee"
                + "  LEFT JOIN ee.id.empresa empresa"
                + " RIGHT JOIN ee.id.produto produto"
                + "  LEFT JOIN produto.tipoReceita tipoReceita"
        );

        hql.setTypeSelect(RelatorioProdutoDTO.class.getName());

        List<EmpresaSetor> empresaSetorList = LoadManager.getInstance(EmpresaSetor.class)
                .addProperty(VOUtils.montarPath(EmpresaSetor.PROP_SETOR, Empresa.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_EMPRESA, param.getEmpresa()))
                .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_SITUACAO, EmpresaSetor.Situacao.ATIVO.value()))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(empresaSetorList)) {
            List<Empresa> setores = Lambda.extract(empresaSetorList, Lambda.on(EmpresaSetor.class).getSetor());
            setores.add(param.getEmpresa());

            hql.addToWhereWhithAnd("empresa in ", setores);
        } else {
            hql.addToWhereWhithAnd("empresa = ", this.param.getEmpresa());
        }

        hql.addToWhereWhithAnd("produto = ", this.param.getProduto());

        hql.addToOrder("empresa.descricao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioProdutoDTO> getResult() {
        return result;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            for (RelatorioProdutoDTO relatorioProdutoDTO : result) {
                EstoqueEmpresa estoqueEmpresa = relatorioProdutoDTO.getEstoqueEmpresa();
                List<GrupoEstoque> grupoEstoqueList = new QueryRelatorioProdutoGrupoEstoque(param, estoqueEmpresa).start().getResult();
                relatorioProdutoDTO.setGrupoEstoqueList(grupoEstoqueList);
            }
        }
    }

    public class QueryRelatorioProdutoGrupoEstoque extends CommandQuery<QueryRelatorioProdutoGrupoEstoque> {

        private List<GrupoEstoque> result;
        private RelatorioProdutoParam param;
        private EstoqueEmpresa estoqueEmpresa;

        public QueryRelatorioProdutoGrupoEstoque(RelatorioProdutoParam param, EstoqueEmpresa estoqueEmpresa) {
            this.param = param;
            this.estoqueEmpresa = estoqueEmpresa;
        }

        @Override
        protected void createQuery(HQLHelper hql) {
            if (RepositoryComponentDefault.SIM.equals(this.param.getExibirLotes())) {
                hql.addToSelect("ge.id.grupo", true);
                hql.addToSelect("ge.id.codigoDeposito", true);
                hql.addToSelect("ge.dataValidade", true);
                hql.addToSelect("ge.laboratorioFabricante", true);
                hql.addToSelect("ge.estoqueEncomendado", true);
                hql.addToSelect("ge.estoqueReservado", true);
                hql.addToSelect("ge.estoqueFisico", true);
                hql.addToSelect("ge.id.localizacaoEstrutura.mascara", true);
            } else {
                hql.addToSelect("sum(ge.estoqueEncomendado)", "estoqueEncomendado");
                hql.addToSelect("sum(ge.estoqueReservado)", "estoqueReservado");
                hql.addToSelect("sum(ge.estoqueFisico)", "estoqueFisico");
            }
            hql.addToSelect("ge.roDeposito.codigo", true);
            hql.addToSelect("ge.roDeposito.descricao", true);

            hql.addToFrom("GrupoEstoque ge");
            hql.setTypeSelect(GrupoEstoque.class.getName());

            hql.addToWhereWhithAnd("ge.id.estoqueEmpresa = ", estoqueEmpresa);

            hql.addToWhereWhithAnd("(ge.estoqueFisico + ge.estoqueEncomendado + ge.estoqueReservado) <> 0");

            hql.addToOrder("ge.id.codigoDeposito");

            if (RepositoryComponentDefault.NAO.equals(this.param.getExibirLotes())) {
                hql.addToGroup("ge.roDeposito.codigo");
                hql.addToGroup("ge.roDeposito.descricao");
            }
        }

        @Override
        protected void result(HQLHelper hql, Object result) {
            this.result = hql.getBeanList((List<Map<String, Object>>) result);
        }

        @Override
        public List<GrupoEstoque> getResult() {
            return result;
        }

    }

}
