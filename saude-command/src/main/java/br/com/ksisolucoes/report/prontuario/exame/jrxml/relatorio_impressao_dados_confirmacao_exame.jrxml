<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_ficha_paciente" columnCount="2" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="381" columnSpacing="40" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="70a3c586-d4d9-42bb-b9f3-c789422c5944">
	<property name="ireport.scriptlethandling" value="0"/>
	<property name="ireport.encoding" value="UTF-8"/>
	<property name="ireport.zoom" value="1.593154088551801"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="19"/>
	<import value="br.com.ksisolucoes.util.validacao.*"/>
	<import value="net.sf.jasperreports.engine.*"/>
	<import value="java.util.*"/>
	<import value="net.sf.jasperreports.engine.data.*"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="br.com.ksisolucoes.util.*"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.atendimento.usuarioCadsus]]></fieldDescription>
	</field>
	<field name="identidade" class="java.lang.String"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="tipoAtendimento" class="br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento]]></fieldDescription>
	</field>
	<field name="cns" class="java.lang.String"/>
	<field name="tipoCertidao" class="java.lang.String"/>
	<field name="folha" class="java.lang.String"/>
	<field name="termo" class="java.lang.String"/>
	<field name="livro" class="java.lang.String"/>
	<field name="cartorio" class="java.lang.String"/>
	<field name="dataEmissaoCertidao" class="java.util.Date"/>
	<field name="orgaoEmissor" class="br.com.ksisolucoes.vo.basico.OrgaoEmissor"/>
	<field name="convenio" class="br.com.ksisolucoes.vo.prontuario.basico.Convenio">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.atendimento.convenio]]></fieldDescription>
	</field>
	<field name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.atendimento]]></fieldDescription>
	</field>
	<field name="atendimentoExame" class="br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame]]></fieldDescription>
	</field>
	<field name="profissional" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.atendimento.profissional]]></fieldDescription>
	</field>
	<field name="exameProcedimento" class="br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento">
		<fieldDescription><![CDATA[atendimentoExameItem.exameProcedimento]]></fieldDescription>
	</field>
	<field name="profissionalSolicitante" class="br.com.ksisolucoes.vo.cadsus.Profissional">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.profissionalSolicitante]]></fieldDescription>
	</field>
	<field name="valor" class="java.lang.Double">
		<fieldDescription><![CDATA[atendimentoExameItem.precoUnitario]]></fieldDescription>
	</field>
	<field name="numeroRegistro" class="java.lang.String">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.atendimento.numeroRegistroConvenio]]></fieldDescription>
	</field>
	<field name="tipoExame" class="br.com.ksisolucoes.vo.prontuario.basico.TipoExame">
		<fieldDescription><![CDATA[atendimentoExameItem.atendimentoExame.tipoExame]]></fieldDescription>
	</field>
	<variable name="BUNDLE" class="br.com.ksisolucoes.util.Bundle"/>
	<variable name="UTIL" class="br.com.ksisolucoes.util.Util"/>
	<variable name="TOTAL" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[$F{valor}]]></variableExpression>
	</variable>
	<group name="Total">
		<groupExpression><![CDATA[null]]></groupExpression>
	</group>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band splitType="Stretch"/>
	</title>
	<pageHeader>
		<band splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="219">
			<rectangle radius="10">
				<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="0" y="105" width="380" height="85" uuid="96d2d33c-e530-4ddb-af10-c71d9c7efbe7"/>
			</rectangle>
			<rectangle radius="10">
				<reportElement stretchType="RelativeToBandHeight" x="0" y="5" width="380" height="83" uuid="eaed94ec-622a-4cca-8819-44a4efdf69a4"/>
			</rectangle>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="4" y="193" width="160" height="12" uuid="1812e76b-b54d-4ea5-be16-03fab692baec"/>
				<textElement>
					<font fontName="Arial" size="9" isBold="true" isUnderline="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Exames Realizados*/$V{BUNDLE}.getStringApplication("rotulo_exames_realizados")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="4" y="206" width="72" height="12" uuid="1812e76b-b54d-4ea5-be16-03fab692baec"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*codigo*/$V{BUNDLE}.getStringApplication("rotulo_codigo")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="82" y="206" width="72" height="12" uuid="1812e76b-b54d-4ea5-be16-03fab692baec"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*exame*/$V{BUNDLE}.getStringApplication("rotulo_exame")]]></textFieldExpression>
			</textField>
			<line>
				<reportElement positionType="FixRelativeToBottom" x="0" y="218" width="380" height="1" uuid="a62a7379-59b0-41ed-832d-ae4d8dfe323f"/>
			</line>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="55" y="151" width="89" height="12" uuid="15bbdce3-07e0-4914-ba5b-ccdb0f8e0a93"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getTelefoneFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="22" y="109" width="32" height="12" uuid="38d54bb1-8d4e-48ac-8d2b-89ed94da99bb"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*nome*/$V{BUNDLE}.getStringApplication("rotulo_nome")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" mode="Transparent" x="259" y="136" width="25" height="12" uuid="1bdeeb77-4e29-41fe-b2ba-a6c63a151329"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*cns*/$V{BUNDLE}.getStringApplication("rotulo_cns")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="8" y="179" width="46" height="12" uuid="613bce7d-2515-4d37-9ea4-3d4d5058ddc5"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*cidade*/$V{BUNDLE}.getStringApplication("rotulo_cidade")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="55" y="165" width="324" height="12" uuid="0374a83b-eb26-4613-a578-30194b1fe2fe"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="189" y="151" width="115" height="12" uuid="6351f741-f57b-4e38-b156-be8a7d4006d9"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getCelularFormatado()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" positionType="FixRelativeToBottom" mode="Transparent" x="123" y="136" width="29" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="285" y="136" width="95" height="12" uuid="db7b5256-2b8a-4898-a677-c6680af2e07b"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{cns}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" mode="Opaque" x="21" y="99" width="95" height="12" uuid="b3e2e3f5-a090-41c5-8ed1-5af10e361a62"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*dadosPaciente*/$V{BUNDLE}.getStringApplication("rotulo_dados_paciente")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="8" y="151" width="46" height="12" uuid="10bf1cc4-116c-47dc-9904-300b230ea49d"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*telefone*/$V{BUNDLE}.getStringApplication("rotulo_telefone")+":"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="144" y="151" width="45" height="12" uuid="8baba926-b1e7-456d-9192-f36f15f24112"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*celular*/$V{BUNDLE}.getStringApplication("rotulo_celular")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="55" y="109" width="223" height="12" uuid="d92e26df-293b-4108-bdf3-0e73a1795df1"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome() + " (" + $F{usuarioCadsus}.getCodigo() + ")"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="4" y="136" width="50" height="12" uuid="f466982f-5b6b-40b6-a0a5-ce7d0ea92651"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*dt nasc*/$V{BUNDLE}.getStringApplication("rotulo_data_nascimento_abv2")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="55" y="179" width="324" height="12" uuid="0eb43f1f-ad5e-4145-984d-8cb597dde4e6"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getCidadeFormatado()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="310" y="109" width="64" height="12" uuid="d92e26df-293b-4108-bdf3-0e73a1795df1"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getSexoFormatado()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="279" y="109" width="30" height="12" uuid="38d54bb1-8d4e-48ac-8d2b-89ed94da99bb"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*sexo*/$V{BUNDLE}.getStringApplication("rotulo_sexo")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" positionType="FixRelativeToBottom" mode="Transparent" x="153" y="136" width="105" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDescricaoIdadeAnoMesDia()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="4" y="165" width="50" height="12" uuid="281374f7-1e78-44e9-9b04-46517f4a67fe"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*endereco*/$V{BUNDLE}.getStringApplication("rotulo_endereco")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="55" y="136" width="70" height="12" uuid="cdfb2f82-329a-4a91-aeeb-139b3557bc26"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="4" y="38" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="62ccd2df-ed6d-4a6c-a5d5-9ca98adb6500"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitante")+":"]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="66" y="66" width="312" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3e310078-08a4-4e40-a451-14df81af5cd1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getObservacao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="18" y="24" width="47" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="4" y="10" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atendimento")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="4" y="52" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tecnico")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="284" y="11" width="90" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getDataExame()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="66" y="10" width="140" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getCodigo()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="236" y="24" width="138" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoExame}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="4" y="66" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="e9eb35c2-e310-4cf6-a21e-dfc8080df4c2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_observacao")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Opaque" x="21" y="-2" width="95" height="13" isRemoveLineWhenBlank="true" forecolor="#000000" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box leftPadding="2">
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dados_exame")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="66" y="38" width="312" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getNomeProfissional()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="165" y="24" width="70" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_tipo_de_exame")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="66" y="24" width="97" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="66" y="52" width="312" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{profissionalSolicitante}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="222" y="11" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_exame")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement positionType="FixRelativeToBottom" x="66" y="122" width="223" height="12" uuid="e8ec29d0-44ec-4f52-9101-4d157ecf74e7"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNomeMae()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="FixRelativeToBottom" x="5" y="122" width="58" height="12" uuid="c51f28fa-d85f-4ec3-b29d-c0e6b5996284"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*nome da mae*/$V{BUNDLE}.getStringApplication("rotulo_nome_mae")+":"]]></textFieldExpression>
			</textField>
		</band>
	</columnHeader>
	<detail>
		<band height="13" splitType="Stretch">
			<textField isBlankWhenNull="true">
				<reportElement x="4" y="1" width="72" height="12" uuid="0592b8d0-1cab-437f-a6cd-0eb7c3d14d55"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameProcedimento}.getProcedimento().getCodigoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="83" y="1" width="291" height="12" uuid="0592b8d0-1cab-437f-a6cd-0eb7c3d14d55"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{exameProcedimento}.getDescricaoProcedimento()]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<pageFooter>
		<band splitType="Stretch"/>
	</pageFooter>
	<lastPageFooter>
		<band height="118">
			<rectangle radius="10">
				<reportElement mode="Transparent" x="0" y="67" width="379" height="50" uuid="99390016-3c3e-4d89-b241-7722c305db5c"/>
			</rectangle>
			<line>
				<reportElement x="0" y="2" width="380" height="1" uuid="033227ee-e6f6-47bd-858d-39916b09fe48"/>
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<textField>
				<reportElement mode="Opaque" x="13" y="61" width="165" height="12" uuid="97382ffc-7342-49a8-b45b-ffef78d0217a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Protocolo de entrega de exames*/$V{BUNDLE}.getStringApplication("rotulo_protocolo_entrega_exames")]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="22" y="75" width="34" height="12" uuid="e744ef60-89ab-464a-86f6-349c7ecb24c3"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Nome*/$V{BUNDLE}.getStringApplication("rotulo_nome")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="57" y="75" width="168" height="12" uuid="0592b8d0-1cab-437f-a6cd-0eb7c3d14d55"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="143" y="102" width="75" height="12" uuid="e744ef60-89ab-464a-86f6-349c7ecb24c3"/>
				<textElement textAlignment="Right">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[/*Tipo Exame*/$V{BUNDLE}.getStringApplication("rotulo_tipo_de_exame")+":"]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="219" y="102" width="159" height="12" uuid="0592b8d0-1cab-437f-a6cd-0eb7c3d14d55"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tipoExame}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="288" y="88" width="65" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getDataEntrega()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="213" y="88" width="74" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_entrega")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="57" y="102" width="85" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f644d319-0be3-4703-affa-c7ab941fa848"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{convenio}.getDescricao()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="9" y="102" width="47" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="792cedce-243e-449e-b3a9-2e042db7f8a3"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_convenio")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="226" y="75" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="fde4e4f8-5dd3-470c-a1c9-998948d2a5c1"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_exame")+":"]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy HH:mm:ss" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="287" y="75" width="87" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="b9336134-4f5c-4cdd-80b5-5c5b11a835f0"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getDataExame()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="57" y="88" width="155" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="a29be505-a32c-4080-ae2b-1ec836d61f54"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getNomeProfissional()]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="1" y="88" width="55" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="f62280a7-1eb7-4a1e-85c8-d17bf3cd3fb2"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Right" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="8" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_solicitante")+":"]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="2" y="6" width="58" height="53" uuid="1aeb4a90-0a52-4d3f-b7ec-6fbd3fad5525"/>
				<imageExpression><![CDATA[$P{CAMINHO_IMAGEM_PADRAO}]]></imageExpression>
			</image>
			<textField>
				<reportElement x="65" y="6" width="214" height="13" uuid="908d568b-eb55-4836-888b-18fd0a06cff3"/>
				<textElement>
					<font fontName="Arial" size="10" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{DESC_CABECALHO_PADRAO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="65" y="19" width="214" height="11" uuid="4def841e-599c-46d8-b9b0-3338e1e1c22b"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{UNIDADE_ATENDIMENTO}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="65" y="30" width="214" height="11" uuid="a57a4ac5-3b6c-4536-b56c-a5f6c6744b54"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_1}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="65" y="41" width="214" height="11" uuid="a45d8426-6501-4486-9f0d-f5df751709e9"/>
				<textElement>
					<font fontName="Arial" size="8" isBold="true" isUnderline="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{CABECALHO_ADICIONAL_2}]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="false">
				<reportElement key="textField-76" mode="Transparent" x="302" y="7" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#CCCCCC" uuid="a2c6fc33-c5dc-4c0d-8af5-f02e8f4bed42"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Justified" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="true" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_atendimento")]]></textFieldExpression>
			</textField>
			<textField pattern="" isBlankWhenNull="true">
				<reportElement key="textField-77" mode="Transparent" x="302" y="23" width="61" height="12" isRemoveLineWhenBlank="true" forecolor="#000000" backcolor="#FFFFFF" uuid="3b418d2f-9154-405f-8aef-dffa4387cc04"/>
				<box>
					<topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle" rotation="None" markup="none">
					<font fontName="Arial" size="9" isBold="false" isItalic="false" isUnderline="false" isStrikeThrough="false" pdfFontName="Helvetica" pdfEncoding="Cp1252" isPdfEmbedded="false"/>
					<paragraph lineSpacing="Single"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{atendimentoExame}.getCodigo()]]></textFieldExpression>
			</textField>
		</band>
	</lastPageFooter>
	<summary>
		<band splitType="Stretch"/>
	</summary>
</jasperReport>
