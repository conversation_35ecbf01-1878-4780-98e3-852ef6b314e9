package br.com.ksisolucoes.report.prontuario.basico.query;

import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTO;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioPerfilAtendimentoDTOParam;
import br.com.ksisolucoes.util.Coalesce;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

public class QueryCid extends QueryPerfilAtendimento {

    private List<RelatorioPerfilAtendimentoDTO> result;

    public QueryCid(RelatorioPerfilAtendimentoDTOParam param) {
        super(param);
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("a.cidPrincipal.codigo", "codigoString");
        hql.addToSelect("a.cidPrincipal.descricao", "descricao");
        hql.addToSelect("sum(1)", "quantidade");
        {
            //subSelect para o total
            HQLHelper hqlTotal = hql.getNewInstanceSubQuery();
            hqlTotal.addToSelect("count(a1.cidPrincipal.codigo)");
            hqlTotal.addToFrom("Atendimento a1 ");
            addWhereAtendimento(hqlTotal, "a1", false);
            hql.addToSelect("(" + hqlTotal.getQuery() + ")", "total");
        }
        hql.addToFrom("Atendimento a ");
        hql.setTypeSelect(RelatorioPerfilAtendimentoDTO.class.getName());
        addWhereAtendimento(hql, "a", false);
        hql.addToGroup("a.cidPrincipal.codigo");
        hql.addToGroup("a.cidPrincipal.descricao");
        hql.addToOrder("3 desc");
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(Coalesce.asLong(this.getParam().getNumeroCids(), 10L).intValue());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioPerfilAtendimentoDTO> getResult() {
        return result;
    }
}
