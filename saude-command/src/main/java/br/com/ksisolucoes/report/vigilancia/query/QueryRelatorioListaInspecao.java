package br.com.ksisolucoes.report.vigilancia.query;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioListaInspecaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioListaInspecaoDTOParam;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 * Created by sulivan on 31/05/17.
 */
public class QueryRelatorioListaInspecao extends CommandQuery<QueryRelatorioListaInspecao> implements ITransferDataReport<RelatorioListaInspecaoDTOParam, RelatorioListaInspecaoDTO> {

    private RelatorioListaInspecaoDTOParam param;
    private List<RelatorioListaInspecaoDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelect("rv.codigo", "requerimentoVigilancia.codigo");
        hql.addToSelect("rv.nome", "requerimentoVigilancia.nome");
        hql.addToSelect("rv.numero", "requerimentoVigilancia.numero");
        hql.addToSelect("rv.complemento", "requerimentoVigilancia.complemento");
        hql.addToSelect("rv.telefone", "requerimentoVigilancia.telefone");
        hql.addToSelect("rv.protocolo", "requerimentoVigilancia.protocolo");

        hql.addToSelect("estab.codigo", "requerimentoVigilancia.estabelecimento.codigo");
        hql.addToSelect("estab.telefone", "requerimentoVigilancia.estabelecimento.telefone");
        hql.addToSelect("estab.representanteNome", "requerimentoVigilancia.estabelecimento.representanteNome");

        hql.addToSelect("ve.codigo", "requerimentoVigilancia.vigilanciaEndereco.codigo");
        hql.addToSelect("ve.bairro", "requerimentoVigilancia.vigilanciaEndereco.bairro");
        hql.addToSelect("ve.cep", "requerimentoVigilancia.vigilanciaEndereco.cep");
        hql.addToSelect("ve.logradouro", "requerimentoVigilancia.vigilanciaEndereco.logradouro");

        hql.addToSelect("tl.codigo", "requerimentoVigilancia.vigilanciaEndereco.tipoLogradouro.codigo");
        hql.addToSelect("tl.descricao", "requerimentoVigilancia.vigilanciaEndereco.tipoLogradouro.descricao");
        hql.addToSelect("tl.sigla", "requerimentoVigilancia.vigilanciaEndereco.tipoLogradouro.sigla");

        hql.addToSelect("c.codigo", "requerimentoVigilancia.vigilanciaEndereco.cidade.codigo");
        hql.addToSelect("c.descricao", "requerimentoVigilancia.vigilanciaEndereco.cidade.descricao");

        hql.addToSelect("e.codigo", "requerimentoVigilancia.vigilanciaEndereco.cidade.estado.codigo");
        hql.addToSelect("e.descricao", "requerimentoVigilancia.vigilanciaEndereco.cidade.estado.descricao");
        hql.addToSelect("e.sigla", "requerimentoVigilancia.vigilanciaEndereco.cidade.estado.sigla");

        hql.setTypeSelect(RelatorioListaInspecaoDTO.class.getName());

        hql.addToFrom("RequerimentoVigilancia rv "
                + " left join rv.estabelecimento estab"
                + " left join rv.vigilanciaEndereco ve"
                + " left join ve.tipoLogradouro tl "
                + " left join ve.cidade c "
                + " left join c.estado e "
        );

        hql.addToWhereWhithAnd("rv.codigo in :codigoRequerimentoList");

        hql.addToOrder("rv.nome asc");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoRequerimentoList", param.getCodigoRequerimentoList());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<RelatorioListaInspecaoDTO> getResult() {
        return result;
    }

    @Override
    public void setDTOParam(RelatorioListaInspecaoDTOParam param) {
        this.param = param;
    }

}