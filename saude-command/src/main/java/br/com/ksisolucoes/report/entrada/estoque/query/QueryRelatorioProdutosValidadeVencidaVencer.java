package br.com.ksisolucoes.report.entrada.estoque.query;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.*;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryProdutosValidadeVencidaVencerDTO;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.QueryProdutosValidadeVencidaVencerDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaSetor;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresaHelper;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import ch.lambdaj.Lambda;
import org.hibernate.Session;

import java.util.*;

/**
 * <AUTHOR>
 */
public class QueryRelatorioProdutosValidadeVencidaVencer extends CommandQuery<QueryRelatorioProdutosValidadeVencidaVencer> implements ITransferDataReport<QueryProdutosValidadeVencidaVencerDTOParam, QueryProdutosValidadeVencidaVencerDTO> {

    private QueryProdutosValidadeVencidaVencerDTOParam param;
    private List<QueryProdutosValidadeVencidaVencerDTO> result;

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.addToSelectAndOrder("emp.codigo", "codigoEmpresa");
        hql.addToSelectAndOrder("emp.descricao", "empresaDescricao");
        hql.addToSelectAndOrder("emp.referencia", "empresaCodigo");

        hql.addToSelectAndOrder("d.codigo", "codigoDeposito");
        hql.addToSelectAndOrder("d.descricao", "descricaoDeposito");

        hql.addToSelectAndOrder("gru.codigo", "grupoCodigo");
        hql.addToSelectAndOrder("gru.descricao", "grupoDescricao");
        hql.addToSelectAndOrder("sub.id.codigo", "subGrupoCodigo");
        hql.addToSelectAndOrder("sub.descricao", "subGrupoDescricao");

        hql.addToSelect("pro.referencia", "produtoCodigo");
        hql.addToSelect("pro.descricao", "produtoDescricao");

        hql.addToSelect("uni.unidade", "unidadeUnidade");

        hql.addToSelect("ge.id.grupo", "grupoEstoque");

        hql.addToSelect("ge.dataValidade", "dataValidade");

        hql.addToSelect("localizacaoEstrutura.codigo", "codigoLocalizacaoEstrutura");
        hql.addToSelect("localizacaoEstrutura.mascara", "mascaraLocalizacaoEstrutura");

        hql.addToSelect("ge.estoqueFisico", "estoqueFisico");

//        String sumQuantidade = "(select (sum(case when me.tipoDocumento.flagTipoMovimento = :isEntrada then "
//                + "               (me.quantidade * -1)"
//                + "          else"
//                + "               me.quantidade"
//                + "          end)) from MovimentoEstoque me"
//                + " where me.produto = pro"
//                + " and me.roEmpresa = emp";
//
//        sumQuantidade += " and me.tipoDocumento.flagConsumo = :isConsumo"
//                + " and me.dataLancamento between :novaData and :novaDataFinal)";
//
//        hql.addToSelect(sumQuantidade + "/" + this.param.getMesesCalcMediaConsumo(), "quantidade");
//
//        hql.addToSelect("coalesce(sum(ge.estoqueFisico) / (" + sumQuantidade + " / " + this.param.getMesesCalcMediaConsumo() + "),0)", "previsao");

        if (QueryProdutosValidadeVencidaVencerDTOParam.Ordenacao.PRODUTO.equals(this.param.getOrdenacao())) {
            hql.addToOrder("pro.descricao " + this.param.getTipoOrdenacao());
        } else if (QueryProdutosValidadeVencidaVencerDTOParam.Ordenacao.DT_VALIDADE.equals(this.param.getOrdenacao())) {
            hql.addToOrder("ge.dataValidade " + this.param.getTipoOrdenacao());
        }

        hql.addToFrom("GrupoEstoque ge"
                + " right join ge.id.estoqueEmpresa ee"
                + " left join ge.roDeposito d"
                + " left join ee.id.empresa emp"
                + " left join ge.id.localizacaoEstrutura localizacaoEstrutura"
                + " right join ee.id.produto pro"
                + " left join pro.unidade uni"
                + " left join pro.subGrupo sub"
                + " left join sub.roGrupoProduto gru");

        hql.setTypeSelect(QueryProdutosValidadeVencidaVencerDTO.class.getName());

        hql.addToWhereWhithAnd("sub = ", this.param.getSubGrupo());
        hql.addToWhereWhithAnd("gru = ", this.param.getGrupoProdutoSubGrupo());
        hql.addToWhereWhithAnd("d = ", this.param.getDeposito());
        hql.addToWhereWhithAnd("localizacaoEstrutura = ", this.param.getLocalizacaoEstrutura());

        try {
            String utilizaLocalizacaoEstoque = BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("utilizaLocalizacaoEstoque");
            if (RepositoryComponentDefault.SIM.equals(utilizaLocalizacaoEstoque)) {
                List<Empresa> empresas = this.param.getEmpresaList();
                if (CollectionUtils.isEmpty(empresas)) {
                    Empresa empresa = param.getEmpresa();
                    if (empresa == null) {
                        empresa = getSessao().getEmpresa();
                    }
                    empresas = Arrays.asList(empresa);
                }

                List<EmpresaSetor> empresaSetorList = LoadManager.getInstance(EmpresaSetor.class)
                        .addProperty(VOUtils.montarPath(EmpresaSetor.PROP_SETOR, Empresa.PROP_CODIGO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_EMPRESA, BuilderQueryCustom.QueryParameter.IN, empresas))
                        .addParameter(new QueryCustom.QueryCustomParameter(EmpresaSetor.PROP_SITUACAO, EmpresaSetor.Situacao.ATIVO.value()))
                        .start().getList();

                if (CollectionUtils.isNotNullEmpty(empresaSetorList)) {
                    Collection<Empresa> setores = Lambda.extract(empresaSetorList, Lambda.on(EmpresaSetor.class).getSetor());
                    setores.addAll(empresas);
                    empresas = new ArrayList<>(setores);
                }

                hql.addToWhereWhithAnd("emp in ", Lambda.selectDistinct(empresas));
            } else {
                hql.addToWhereWhithAnd("emp = ", this.param.getEmpresa());

                if (CollectionUtils.isNotNullEmpty(this.param.getEmpresaList())) {
                    hql.addToWhereWhithAnd("emp in ", this.param.getEmpresaList());
                }
            }

        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        if (this.param.getValidadeAte() != null) {
            Date validadeAte = this.param.getValidadeAte();
            if (validadeAte == null) {
                validadeAte = DataUtil.getDataAtual();
            }
            hql.addToWhereWhithAnd("ge.dataValidade <= ", validadeAte);
        } else {
            hql.addToWhereWhithAnd("ge.dataValidade ", param.getPeriodo());
        }
        hql.addToWhereWhithAnd("ge.estoqueFisico > 0");
        hql.addToWhereWhithAnd("sub.flagControlaGrupoEstoque = ", RepositoryComponentDefault.SIM);
    }

    @Override
    public void setDTOParam(QueryProdutosValidadeVencidaVencerDTOParam param) {
        this.param = param;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<QueryProdutosValidadeVencidaVencerDTO> getResult() {
        return result;
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (CollectionUtils.isNotNullEmpty(result)) {
            for (QueryProdutosValidadeVencidaVencerDTO dto : result) {
                if (dto.getProdutoCodigo() != null) {
                    Produto produto = (Produto) getSession().get(Produto.class, dto.getProdutoCodigo());
                    Empresa empresa = (Empresa) getSession().get(Empresa.class, new Long(dto.getCodigoEmpresa()));

                    Date dataInicial = Data.getDataParaPrimeiroDiaMes(Data.removeMeses(DataUtil.getDataAtual(), param.getMesesCalcMediaConsumo().intValue()));
                    // Subtrair 1 mês da data final para não considerar o mês atual e pegar último dia do mês
                    Date dataFinal = Data.getDataParaUltimoDiaMes(Data.removeMeses(DataUtil.getDataAtual(), 1));

                    DatePeriod periodo = new DatePeriod(dataInicial, dataFinal);

                    Double consumoProduto = Coalesce.asDouble(EstoqueEmpresaHelper.getConsumoProduto(empresa, produto, periodo), 0D);

                    Dinheiro calcConsumo = new Dinheiro(consumoProduto).dividir(this.param.getMesesCalcMediaConsumo());

                    dto.setConsumo(calcConsumo.doubleValue());
                }

            }

        }

    }
}
