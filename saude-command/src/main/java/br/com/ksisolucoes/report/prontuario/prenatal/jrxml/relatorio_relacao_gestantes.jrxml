<?xml version="1.0" encoding="UTF-8"?>
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="relatorio_relacao_gestantes" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="904ef39c-8037-443b-8222-af49929dd456">
	<property name="ireport.zoom" value="3.543122000000023"/>
	<property name="ireport.x" value="0"/>
	<property name="ireport.y" value="0"/>
	<import value="br.com.ksisolucoes.util.validacao.RepositoryComponentDefault"/>
	<import value="br.com.celk.util.Coalesce"/>
	<import value="br.com.ksisolucoes.util.Bundle"/>
	<import value="org.apache.commons.lang.StringUtils"/>
	<parameter name="FORMA_APRESENTACAO" class="br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoGestantesDTOParam.FormaApresentacao"/>
	<field name="usuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"/>
	<field name="preNatal" class="br.com.ksisolucoes.vo.prontuario.basico.PreNatal"/>
	<field name="idadeGestacional" class="java.lang.Long"/>
	<field name="enderecoUsuarioCadsus" class="br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus"/>
	<field name="descricaoUnidade" class="java.lang.String"/>
	<field name="descricaoFaixaEtaria" class="java.lang.String"/>
	<field name="descricaoArea" class="java.lang.String"/>
	<field name="descricaoMicroArea" class="java.lang.String"/>
	<field name="empresa" class="br.com.ksisolucoes.vo.basico.Empresa"/>
	<field name="faixaEtariaItem" class="br.com.ksisolucoes.vo.basico.FaixaEtariaItem"/>
	<field name="equipeMicroArea" class="br.com.ksisolucoes.vo.basico.EquipeMicroArea"/>
	<field name="imc" class="java.lang.String"/>
	<field name="vacinaEmDia" class="java.lang.String"/>
	<field name="dataPuerperio" class="java.util.Date"/>
	<variable name="usuarioCadsus_1" class="java.lang.Integer" resetType="Group" resetGroup="FA" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}]]></variableExpression>
	</variable>
	<variable name="usuarioCadsus_3" class="java.lang.Integer" resetType="Group" resetGroup="FADefault" calculation="Count">
		<variableExpression><![CDATA[$F{usuarioCadsus}]]></variableExpression>
	</variable>
	<variable name="FA" class="br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto.RelatorioRelacaoGestantesDTOParam.FormaApresentacao"/>
	<group name="FADefault">
		<groupExpression><![CDATA[null]]></groupExpression>
		<groupHeader>
			<band/>
		</groupHeader>
		<groupFooter>
			<band height="18">
				<textField>
					<reportElement x="764" y="4" width="36" height="11" uuid="ae4251ef-856e-42b3-809b-396a92c7e146"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{usuarioCadsus_3}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="599" y="4" width="165" height="11" uuid="8345a293-b452-4406-b95b-5ac985206b88"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_geral_pacientes") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="668" y="3" width="134" height="1" uuid="74011bb7-90cb-42e4-9848-1230bc7c15e7"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<group name="FA" isReprintHeaderOnEachPage="true" keepTogether="true">
		<groupExpression><![CDATA[$V{FA}.UNIDADE.equals($P{FORMA_APRESENTACAO}) ?
    $F{empresa}.getCodigo()
:
    $V{FA}.FAIXA_ETARIA.equals($P{FORMA_APRESENTACAO}) ?
        $F{faixaEtariaItem}.getId().getSequencia()
    :
        $V{FA}.AREA.equals($P{FORMA_APRESENTACAO}) ?
            $F{equipeMicroArea}.getEquipeArea().getCodigo()
        :
            $V{FA}.MICRO_AREA.equals($P{FORMA_APRESENTACAO}) ?
                $F{equipeMicroArea}.getEquipeArea().getCodigo() + "" + $F{equipeMicroArea}.getMicroArea()
            :
                null]]></groupExpression>
		<groupHeader>
			<band height="38">
				<rectangle radius="10">
					<reportElement x="0" y="0" width="802" height="20" uuid="9acf27c7-80b0-40ee-98f8-a67674c92b9c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</rectangle>
				<textField>
					<reportElement x="0" y="25" width="228" height="11" uuid="985826df-b42d-4bcf-9c57-0f7e63ed7560"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_paciente")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="294" y="25" width="23" height="10" uuid="bbc19e09-5458-4cd3-9e87-a164c4143f53"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_idade")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="318" y="25" width="42" height="10" uuid="90c7a641-b12a-4e67-bf8e-6407c4f7d5a2"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dum")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="361" y="25" width="42" height="11" uuid="a4c0356d-a7ca-4e0c-bc2b-b4f8ff1f54a9"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_dpp")]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="0" y="35" width="802" height="1" uuid="fb44fa67-4ef7-47e8-b53f-ca21c6313e0c"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
				<textField>
					<reportElement x="8" y="4" width="782" height="13" uuid="e199520c-c239-4028-a5d2-b36929bfb86b"/>
					<textElement>
						<font fontName="Arial" size="10" isBold="true" isUnderline="true"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{FA}.UNIDADE.equals($P{FORMA_APRESENTACAO}) ?
    $F{descricaoUnidade}
:
    $V{FA}.FAIXA_ETARIA.equals($P{FORMA_APRESENTACAO}) ?
        $F{descricaoFaixaEtaria}
    :
        $V{FA}.AREA.equals($P{FORMA_APRESENTACAO}) ?
            $F{descricaoArea}
        :
            $V{FA}.MICRO_AREA.equals($P{FORMA_APRESENTACAO}) ?
                $F{descricaoMicroArea}
            :
                null]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="233" y="25" width="60" height="10" uuid="f564cc3b-5f8a-42e3-8aae-0a3f43e953b4"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_data_nascimento_abv3")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="404" y="25" width="51" height="11" uuid="ce72fcdc-9cef-4efb-9a7d-9fdab062b230"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ult_consulta")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="456" y="25" width="18" height="11" uuid="8ff020cb-2401-441a-a0ea-846b3cb77183"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_ig")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="475" y="25" width="77" height="11" uuid="5ae89910-48cf-42b4-ba3e-cd04b6dcff6f"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_nr_sisprenatal")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="554" y="26" width="23" height="10" uuid="faf8e6ab-4d2f-40bc-b5a8-6e6c2874e339"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[//Risco
Bundle.getStringApplication("rotulo_risco")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="579" y="26" width="35" height="10" uuid="148f078a-2033-41f9-981b-b2fb74f31324"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_num_consulta_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="616" y="26" width="66" height="10" uuid="b574a263-231d-4874-99b6-eba06def3364"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_imc")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="684" y="26" width="40" height="10" uuid="972c3628-ecb6-483d-a919-101e1c8ff994"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_vacina_em_dia_abv")]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="726" y="26" width="72" height="10" uuid="90566d53-e817-4257-997d-c29e783f27ca"/>
					<box>
						<leftPen lineWidth="0.0"/>
						<rightPen lineWidth="0.0"/>
					</box>
					<textElement textAlignment="Center" verticalAlignment="Middle">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_aten_puerperio")]]></textFieldExpression>
				</textField>
			</band>
		</groupHeader>
		<groupFooter>
			<band height="17">
				<textField>
					<reportElement x="764" y="4" width="36" height="11" uuid="61c7b548-9c05-4407-af90-365421551d7d"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8"/>
					</textElement>
					<textFieldExpression><![CDATA[$V{usuarioCadsus_1}]]></textFieldExpression>
				</textField>
				<textField>
					<reportElement x="687" y="4" width="77" height="11" uuid="2bae9521-d953-4c9f-ab88-b051edb9372b"/>
					<textElement textAlignment="Right">
						<font fontName="Arial" size="8" isBold="true"/>
					</textElement>
					<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_total_pacientes") + ": "]]></textFieldExpression>
				</textField>
				<line>
					<reportElement x="702" y="3" width="100" height="1" uuid="8bfe4a50-cb96-4e1d-a685-d65ba678c114"/>
					<graphicElement>
						<pen lineWidth="0.5"/>
					</graphicElement>
				</line>
			</band>
		</groupFooter>
	</group>
	<detail>
		<band height="23" splitType="Stretch">
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="228" height="11" uuid="c2f57e7f-b180-4855-9012-7fa5d4f14afa"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getNome()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="318" y="0" width="42" height="11" uuid="728b2e70-e30f-410c-8e6b-c16f46d657b0"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{preNatal}.getDataUltimaMenstruacao()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="361" y="0" width="42" height="11" uuid="52fae1d9-fe11-4dc5-9d16-5adee2dde633"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{preNatal}.getDataProvavelParto()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="294" y="0" width="23" height="11" uuid="0304db16-c378-43e7-8139-ac3751cbacb7"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getIdade()]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="233" y="0" width="60" height="11" uuid="2c27c4e4-d5fb-47b5-8a16-8a7c736691b1"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{usuarioCadsus}.getDataNascimento()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" isBlankWhenNull="true">
				<reportElement positionType="Float" x="65" y="11" width="338" height="11" uuid="ce4520e6-555f-4bd9-9b0d-b9268ddff410"/>
				<textElement>
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{enderecoUsuarioCadsus}.getEnderecoFormatado()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="404" y="0" width="51" height="11" uuid="7dc0e53f-9a18-48d0-ab8e-dadbb4ff0f34"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{preNatal}.getDataUltimaConsulta()]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="456" y="0" width="18" height="11" uuid="9f14ab62-699f-417d-8f88-7422f22d8a39"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{idadeGestacional}]]></textFieldExpression>
			</textField>
			<textField isStretchWithOverflow="true" pattern="" isBlankWhenNull="true">
				<reportElement x="475" y="0" width="77" height="11" uuid="ff465bdd-d9d2-4c89-a863-5d3d2cf8ed1d"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{preNatal}.getNumeroSisprenatal()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="554" y="0" width="23" height="11" uuid="1e951aad-c707-43d3-b7a3-3f94b38b273f"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{preNatal}.getDescricaoGravidezRisco()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="579" y="0" width="35" height="11" uuid="3c0a8c2a-4848-45fc-a498-d9324b291313"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{preNatal}.getNumeroConsulta()]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="616" y="0" width="66" height="11" uuid="51d34f5f-d903-402e-90cb-e17c6bb0c166"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{imc}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="684" y="0" width="40" height="11" uuid="62cbe76b-f9c4-4ffd-b0cc-387e49dc9d9e"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{vacinaEmDia}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement positionType="Float" x="19" y="11" width="46" height="11" uuid="d0cd4000-2713-4fa4-9017-04aaf7984f01"/>
				<box>
					<leftPen lineWidth="0.0"/>
					<rightPen lineWidth="0.0"/>
				</box>
				<textElement verticalAlignment="Top">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[Bundle.getStringApplication("rotulo_endereco") + ": "]]></textFieldExpression>
			</textField>
			<textField pattern="dd/MM/yyyy" isBlankWhenNull="true">
				<reportElement x="726" y="0" width="73" height="11" uuid="d67643ea-c83d-4807-a60b-819df17fc72a"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dataPuerperio}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
