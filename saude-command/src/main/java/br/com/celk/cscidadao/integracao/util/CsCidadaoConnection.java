package br.com.celk.cscidadao.integracao.util;

import br.com.celk.cscidadao.integracao.interfaces.IIntegrationProcess;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * <AUTHOR>
 */

public class CsCidadaoConnection<T> {

    private IIntegrationProcess iIntegrationProcess;

    public CsCidadaoConnection(IIntegrationProcess iIntegrationProcess){
        this.iIntegrationProcess = iIntegrationProcess;
    }

    public Response conectarRecurso(T dto) throws ValidacaoException, DAOException {

        if (CollectionUtils.isEmpty(iIntegrationProcess.getUsuariosList())) {

            throw new ValidacaoException(Bundle.getStringApplication("inclua_usuario_agendador_processos"));
        }

        Response response = new CsCidadaoHelper().startConnection(Entity.entity(dto, MediaType.APPLICATION_JSON_TYPE), iIntegrationProcess.getResources());

        if (Response.Status.OK.getStatusCode() != response.getStatus() &&
                Response.Status.INTERNAL_SERVER_ERROR.getStatusCode() != response.getStatus() &&
                Response.Status.BAD_REQUEST.getStatusCode() != response.getStatus()) {

            String jsonErro = response.readEntity(String.class);

            Loggable.log.error(jsonErro);

            if (Response.Status.NOT_FOUND.getStatusCode() != response.getStatus() &&
                    Response.Status.BAD_GATEWAY.getStatusCode() != response.getStatus() &&
                    Response.Status.SERVICE_UNAVAILABLE.getStatusCode() != response.getStatus() &&
                    Response.Status.GATEWAY_TIMEOUT.getStatusCode() != response.getStatus()) {

                iIntegrationProcess.notificarUsuariosErro(jsonErro);
            }

            return null;
        }

        return response;
    }
}
