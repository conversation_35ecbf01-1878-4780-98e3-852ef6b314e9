package br.com.celk.cscidadao.integracao.util;

import br.com.celk.cscidadao.integracao.dto.OAuthDTO;
import br.com.celk.singleton.LocalCacheInovamfriSingleton;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyClientBuilder;

import javax.naming.NamingException;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class CsCidadaoHelper {

    public static final String GRANT_TYPE = "client_credentials";
    public static final String SCOPE = "integration";
    public static final String OAUTH_ENTITY = "grant_type=%s&client_id=%s&client_secret=%s&scope=%s";

    private Response response;
    private Date dataValidadeToken;
    private String urlConexao;
    private String clientSecret;
    private String clientId;
    private String lastParameter = "";

    public String getUrlFormat(String parametro) throws DAOException, ValidacaoException {
        buildParameters(parametro);
        return urlConexao;
    }

    public Response startConnection(Entity<?> entity, Resources resource) throws ValidacaoException, DAOException {
        return startConnection(entity, resource, MediaType.APPLICATION_JSON_TYPE);
    }

    public Response startPutConnection(Entity<?> entity, Resources resource, String param) throws ValidacaoException, DAOException {
        return startPutConnection(entity, resource, MediaType.APPLICATION_JSON_TYPE, param);
    }

    public Response startConnection(Entity<?> entity, Resources resource, MediaType mediaType) throws ValidacaoException, DAOException {
        ResteasyClient client = new ResteasyClientBuilder().build();
        WebTarget target = client.target(getUrlFormat(resource.getDescricao()));
        return target.request(mediaType).header("Authorization", "Bearer " + getToken().getAccessToken()).buildPost(entity).invoke();
    }

    public Response startPutConnection(Entity<?> entity, Resources resource, MediaType mediaType, String param) throws ValidacaoException, DAOException {
        ResteasyClient client = new ResteasyClientBuilder().build();
        WebTarget target = client.target(getUrlFormat(resource.getDescricao()) + "/" + param);
        lastParameter = null;
        return target.request(mediaType).header("Authorization", "Bearer " + getToken().getAccessToken()).buildPut(null).invoke();
    }

    public enum Resources {
        VACINA("vacinacoes"),
        TFD("tfds"),
        ESTOQUE("estoques"),
        LOTE("lotes"),
        FILA_ESPERA("filasEspera"),
        AGENDAMENTO("agendamentos"),
        ATENDIMENTO("atendimentos"),
        DISPENSACAO_MEDICAMENTO("dispensasMedicamento"),
        EXAMES("exames"),
        UNIDADES("unidade-especialidades"),
        OAUTH_TOKEN("oauth2/token"),
        PROCEDIMENTOS("procedimentos"),;

        private String descricao;

        Resources(String descricao) {
            this.descricao = descricao;
        }

        public String getDescricao() {
            return descricao;
        }

        public void setDescricao(String descricao) {
            this.descricao = descricao;
        }
    }

    private OAuthDTO getToken() throws ValidacaoException, DAOException {
        OAuthDTO cache = null;
        try {
            cache = (OAuthDTO) LocalCacheInovamfriSingleton.lookup().getCache(TenantContext.getContext());
            if (cache == null || (cache != null && DataUtil.getDataAtual().after(cache.getExpiresDate()))) {
                ResteasyClient client = new ResteasyClientBuilder().build();
                WebTarget target = client.target(getUrlFormat(Resources.OAUTH_TOKEN.getDescricao()));
                Date dataAtual = DataUtil.getDataAtual();
                response = target.request().buildPost(Entity.entity(String.format(OAUTH_ENTITY, GRANT_TYPE, encodeURI(clientId), encodeURI(clientSecret), SCOPE), MediaType.APPLICATION_FORM_URLENCODED_TYPE)).invoke();
                if (Response.Status.OK.getStatusCode() == response.getStatus()) {
                    cache = response.readEntity(OAuthDTO.class);
                }
                if (cache == null) {
                    throw new ValidacaoException("Problema na autenticação do serviço");
                }
                long minutos = TimeUnit.SECONDS.toMinutes(cache.getExpires());
                cache.setExpiresDate(Data.addMinutos(dataAtual, (int) minutos));
                LocalCacheInovamfriSingleton.lookup().saveCache(TenantContext.getContext(), cache);
            }
        } catch (NamingException e) {
            Loggable.log.error(e);
        }
        return cache;
    }

    private static String encodeURI(String s) {
        String result;

        try {
            result = URLEncoder.encode(s, "UTF-8")
                    .replaceAll("\\+", "%20")
                    .replaceAll("\\%21", "!")
                    .replaceAll("\\%27", "'")
                    .replaceAll("\\%28", "(")
                    .replaceAll("\\%29", ")")
                    .replaceAll("\\%7E", "~");
        } catch (UnsupportedEncodingException e) {
            result = s;
        }

        return result;
    }

    private void buildParameters(String parametro) throws DAOException, ValidacaoException {
        if (!parametro.equals(lastParameter)) {
            urlConexao = String.format(BOFactory.getBO(CommomFacade.class).modulo(Modulos.INOVAMFRI).getParametro("urlConexao") + "%s", parametro);
//            urlConexao = "http://inovamfri.celk.com.br:8080/api/1.2/" + parametro;
            clientId = BOFactory.getBO(CommomFacade.class).modulo(Modulos.INOVAMFRI).getParametro("clientId");
            clientSecret = BOFactory.getBO(CommomFacade.class).modulo(Modulos.INOVAMFRI).getParametro("clientSecret");
            lastParameter = parametro;
            validaParametros();
        }
    }

    private void validaParametros() throws ValidacaoException {
        if (urlConexao == null || clientId == null || clientSecret == null) {
            throw new ValidacaoException("Por favor, preencha os parâmetros da integração na tela de parâmetros GEM");
        }
    }
}