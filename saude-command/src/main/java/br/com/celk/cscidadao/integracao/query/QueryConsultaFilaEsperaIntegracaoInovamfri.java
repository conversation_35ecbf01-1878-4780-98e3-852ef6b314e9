package br.com.celk.cscidadao.integracao.query;

import br.com.celk.cscidadao.integracao.dto.FilaEsperaIntegrationDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import org.hibernate.Query;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class QueryConsultaFilaEsperaIntegracaoInovamfri extends CommandQuery<QueryConsultaFilaEsperaIntegracaoInovamfri> {

    private List<FilaEsperaIntegrationDTO> result;
    private int limit;
    private int first;

    public QueryConsultaFilaEsperaIntegracaoInovamfri(int limit, int first) {
        this.limit = limit;
        this.first = first;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(FilaEsperaIntegrationDTO.class.getName());

        hql.addToSelectAndGroup("cast(sa.codigo as text)", "idRegistroClient");
        hql.addToSelectAndGroup("uc.nome", "nomePaciente");
        hql.addToSelectAndGroup("uc.codigo", "codigoUsuarioCadsus");
        hql.addToSelect("(select cast(min(ucc.numeroCartao) as text) from UsuarioCadsusCns ucc where ucc.usuarioCadsus = uc and ucc.excluido = 0)", "cnsPaciente");
        hql.addToSelectAndGroup("to_char(uc.dataNascimento, 'dd/MM/yyyy')", "dataNascimentoPaciente");
        hql.addToSelectAndGroup("cast(tp.codigo as text)", "idRegistroClientItemSolicitado");
        hql.addToSelectAndGroup("tp.descricao", "descricaoItemSolicitado");
        hql.addToSelectAndGroup("cast(sa.prioridade as text)", "prioridade");
        hql.addToSelectAndGroup("cast(sa.status as text)", "situacao");
        hql.addToSelectAndGroup("cast(empresa.codigo as text)", "idRegistroClientUnidade");
        hql.addToSelectAndGroup("empresa.descricao", "estabelecimento");
        hql.addToSelectAndGroup("to_char(sa.dataSolicitacao, 'dd/MM/yyyy HH:mi')", "dataSolicitacao");

        StringBuilder from = new StringBuilder("SolicitacaoAgendamento sa");
        from.append(" LEFT JOIN sa.empresa empresa ");
        from.append(" LEFT JOIN sa.usuarioCadsus uc ");
        from.append(" LEFT JOIN sa.tipoProcedimento tp");

        hql.addToFrom(from.toString());

        hql.addToWhereWhithAnd("exists(select 1 from UsuarioCadsusCns ucc where ucc.usuarioCadsus = uc and ucc.excluido = 0) ");
        hql.addToWhereWhithAnd("sa.dataIntegracaoInovamfri IS NULL");

        //Deve sempre enviar todos os registros para fila ficar integra com a do sistema, pois pode haver solicitações com data menor que 2015
        //hql.addToWhereWhithAnd("sa.dataSolicitacao >= '2015-01-01'");

        hql.addToWhereWhithAnd("tp.flagTfd = ", RepositoryComponentDefault.NAO);
        hql.addToWhereWhithAnd("tp.flagExibeFilaEsperaPublica = ", RepositoryComponentDefault.SIM_LONG);
        // Somente deve ser integrado registros após esta data
    }

    @Override
    protected void customQuery(Query query) {
        super.customQuery(query.setFirstResult(this.first).setMaxResults(this.limit));
    }

    @Override
    public List<FilaEsperaIntegrationDTO> getResult() {
        return result;
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}