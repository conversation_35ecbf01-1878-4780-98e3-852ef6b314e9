package br.com.celk.cscidadao.integracao.integrar;

import br.com.celk.cscidadao.integracao.abstracts.AbstractIntegrationProcess;
import br.com.celk.cscidadao.integracao.dto.ExameIntegrationDTO;
import br.com.celk.cscidadao.integracao.util.CsCidadaoHelper;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Query;

import java.util.List;

/**
 * <AUTHOR>
 */
public class IntegrarExameProcess extends AbstractIntegrationProcess<ExameIntegrationDTO> {

    public IntegrarExameProcess(List<Usuario> usuariosList, List<ExameIntegrationDTO> dto) {
        super(usuariosList, dto);
    }

    @Override
    public void executeUpdate(ExameIntegrationDTO dto) {
        String update = "UPDATE exame_requisicao SET dt_integracao_inovamfri = ?  WHERE cd_exame_requisicao = ? ";
        Query query1 = getSession().createSQLQuery(update);
        query1.setTimestamp(0, DataUtil.getDataAtual());
        query1.setLong(1, new Long(dto.getIdRegistroClient()));

        query1.executeUpdate();
    }


    @Override
    public CsCidadaoHelper.Resources getResources() {
        return CsCidadaoHelper.Resources.EXAMES;
    }
}