package br.com.celk.cscidadao.integracao.integrar;

import br.com.celk.cscidadao.integracao.abstracts.AbstractIntegrationProcess;
import br.com.celk.cscidadao.integracao.dto.TfdIntegrationDTO;
import br.com.celk.cscidadao.integracao.util.CsCidadaoHelper;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.hibernate.Query;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class IntegrarTfdProcess extends AbstractIntegrationProcess<TfdIntegrationDTO> {

    public IntegrarTfdProcess(List<Usuario> usuariosList, List<TfdIntegrationDTO> dto) {
        super(usuariosList, dto);
    }

    @Override
    public void executeUpdate(TfdIntegrationDTO dto) {
        String update = "UPDATE laudo_tfd "
                + " SET dt_integracao_inovamfri = ? "
                + " WHERE cd_laudo_tfd = ? ";

        Query query = getSession().createSQLQuery(update);
        query.setTimestamp(0, DataUtil.getDataAtual());
        query.setLong(1, new Long(dto.getIdRegistroClient()));

        query.executeUpdate();
    }

    @Override
    public CsCidadaoHelper.Resources getResources() {
        return CsCidadaoHelper.Resources.TFD;
    }

}