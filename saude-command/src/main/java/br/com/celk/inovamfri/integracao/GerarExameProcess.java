package br.com.celk.inovamfri.integracao;

import br.com.celk.inovamfri.integracao.dto.ExameIntegrationDTO;
import br.com.celk.inovamfri.util.InovamfriHelper;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.basico.interfaces.facade.IntegracaoInovamfriFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.List;

/**
 * Created by roger on 10/08/16.
 */
public class GerarExameProcess extends AbstractCommandTransaction {

    private List<ExameIntegrationDTO> exameIntegrationDTOList;
    private List<Usuario> usuariosList;
    private StringBuilder mensagemBuilder = new StringBuilder();

    public GerarExameProcess(List<Usuario> usuariosList) throws ValidacaoException, DAOException {
        this.usuariosList = usuariosList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        int first = 0;
        int limit = 100;
        do {
            this.exameIntegrationDTOList = BOFactory.getBO(IntegracaoInovamfriFacade.class).consultarExameIntegracaoInovamfri(first, limit);
            if (CollectionUtils.isNotNullEmpty(this.exameIntegrationDTOList)) {
                this.mensagemBuilder.append(BOFactory.getBO(IntegracaoInovamfriFacade.class).integrarExame(usuariosList, this.exameIntegrationDTOList));
            }
        } while (CollectionUtils.isNotNullEmpty(this.exameIntegrationDTOList));
        if (!this.mensagemBuilder.toString().isEmpty()) {
            MensagemDTO mensagemDTO = new MensagemDTO();
            mensagemDTO.setAssunto(Bundle.getStringApplication("msg_problema_integracao_inovamfri_X", InovamfriHelper.Resources.EXAMES.getDescricao()));
            mensagemDTO.setUsuarios(this.usuariosList);
            mensagemDTO.setMensagem(mensagemBuilder.toString());
            BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
        }
    }
}