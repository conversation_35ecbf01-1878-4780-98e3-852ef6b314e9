package br.com.celk.inovamfri.integracao;

import br.com.celk.inovamfri.integracao.dto.VacinaIntegrationDTO;
import br.com.celk.inovamfri.util.InovamfriHelper;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.basico.interfaces.facade.IntegracaoInovamfriFacade;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.MensagemDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.util.List;

/**
 * Created by roger on 10/08/16.
 */
public class GerarVacinaProcess extends AbstractCommandTransaction {

    private List<VacinaIntegrationDTO> vacinaIntegrationDTOList;
    private List<Usuario> usuariosList;
    private StringBuilder mensagemBuilder = new StringBuilder();

    public GerarVacinaProcess(List<Usuario> usuariosList) throws ValidacaoException, DAOException {
        this.usuariosList = usuariosList;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        int first = 0;
        int limit = 100;
        do {
            this.vacinaIntegrationDTOList = BOFactory.getBO(IntegracaoInovamfriFacade.class).consultarVacinaIntegracaoInovamfri(first, limit);
            if (CollectionUtils.isNotNullEmpty(this.vacinaIntegrationDTOList)) {
                this.mensagemBuilder.append(BOFactory.getBO(IntegracaoInovamfriFacade.class).integrarVacina(usuariosList, this.vacinaIntegrationDTOList));
            }
        } while (CollectionUtils.isNotNullEmpty(this.vacinaIntegrationDTOList));
        if (!this.mensagemBuilder.toString().isEmpty()) {
            MensagemDTO mensagemDTO = new MensagemDTO();
            mensagemDTO.setAssunto(Bundle.getStringApplication("msg_problema_integracao_inovamfri_X", InovamfriHelper.Resources.VACINA.getDescricao()));
            mensagemDTO.setUsuarios(this.usuariosList);
            mensagemDTO.setMensagem(mensagemBuilder.toString());
            BOFactory.getBO(ComunicacaoFacade.class).enviarMensagem(mensagemDTO);
        }
    }
}