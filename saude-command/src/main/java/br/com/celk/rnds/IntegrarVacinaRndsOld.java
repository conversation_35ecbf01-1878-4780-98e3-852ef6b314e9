package br.com.celk.rnds;

import br.com.celk.rnds.builder.ImmunizationParam;
import br.com.celk.rnds.builder.ImmunizationParamBuilder;
import br.com.celk.rnds.helper.IntegracaoFCSHelper;
import br.com.celk.rnds.helper.ValidacoesIntegracaoVacinaRNDSHelper;
import br.com.celk.rnds.provider.IntegracaoRNDSProvider;
import br.com.celk.rnds.rnds.IntegracaoVacinaRNDSHelperOld;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.rnds.RndsUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.rnds.RndsIntegracaoVacina;

/**
 * <AUTHOR>
 * @deprecated usar nova integração, parametro UsaNovoModeloIntegracaoRnds
 */
@Deprecated
public class IntegrarVacinaRndsOld extends AbstractCommandTransaction<IntegrarVacinaRndsOld> {

    private VacinaAplicacao vacinaAplicacao;
    private RndsIntegracaoVacina rndsIntegracaoVacina = new RndsIntegracaoVacina();

    public IntegrarVacinaRndsOld(VacinaAplicacao vacinaAplicacao) {
        this.vacinaAplicacao = vacinaAplicacao;
        rndsIntegracaoVacina.setGrupoAtendimento(vacinaAplicacao.getGrupoAtendimentoRnds());
        rndsIntegracaoVacina.setVacinaAplicacao(vacinaAplicacao);
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        ImmunizationParam immunizationParam = getImmunizationParam();
        if (immunizationParam == null) return;

        String jsonParam = RndsUtil.convertDtoToJson(immunizationParam);

        String fhirHl7Pattern = IntegracaoFCSHelper.getFhirHl7Pattern(jsonParam, rndsIntegracaoVacina);
        if (fhirHl7Pattern == null) return;

        RNDS rndsapi = IntegracaoRNDSProvider.getRNDSAPI();
        RNDS.Resposta documentoEnviado = rndsapi.enviarDocumento(fhirHl7Pattern);

        IntegracaoVacinaRNDSHelperOld.gerarRetornoIntegracaoVacina(rndsIntegracaoVacina, documentoEnviado, documentoEnviado.code);
    }

    public ImmunizationParam getImmunizationParam() {
        ImmunizationParam immunizationParam = ImmunizationParamBuilder.builder()
                .vacinaAplicacao(vacinaAplicacao)
                .rndsIntegracaoVacina(rndsIntegracaoVacina)
                .build();

        rndsIntegracaoVacina.setUuidOrigem(immunizationParam.getIdOrigem());
        if (ValidacoesIntegracaoVacinaRNDSHelper.validarDadosIntegracao(rndsIntegracaoVacina, immunizationParam)) {
            return immunizationParam;
        }

        return null;
    }
}


