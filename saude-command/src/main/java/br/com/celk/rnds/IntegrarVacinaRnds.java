package br.com.celk.rnds;

import br.com.celk.rnds.dto.ImmunizationPayloadDTO;
import br.com.celk.rnds.rnds.IntegracaoVacinaRNDSHelper;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.AwsUtils;
import br.com.ksisolucoes.util.rnds.RndsUtil;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import br.com.ksisolucoes.vo.vacina.rnds.RndsIntegracaoVacina;
import br.com.ksisolucoes.vo.vacina.rnds.RndsVacinaOcorrencias;
import com.amazonaws.services.sqs.AmazonSQS;
import org.apache.commons.lang3.StringUtils;

import javax.ws.rs.HttpMethod;

/**
 * <AUTHOR>
 */
public class IntegrarVacinaRnds extends AbstractCommandTransaction<IntegrarVacinaRnds> {

    private RndsIntegracaoVacina rndsIntegracaoVacina;
    private VacinaAplicacao vacinaAplicacao;
    private final String descricaoOcorrencia;

    public IntegrarVacinaRnds(VacinaAplicacao vacinaAplicacao) {
        this.vacinaAplicacao = vacinaAplicacao;
        descricaoOcorrencia = "Aplicação - Registro enviado para a fila de processamento.";
    }

    public IntegrarVacinaRnds(RndsIntegracaoVacina rndsIntegracaoVacina) {
        this.rndsIntegracaoVacina = rndsIntegracaoVacina;
        this.vacinaAplicacao = rndsIntegracaoVacina.getVacinaAplicacao();
        descricaoOcorrencia = "Reenvio - Registro enviado para a fila de processamento.";
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        if (rndsIntegracaoVacina != null && rndsIntegracaoVacina.getCodigo() != null) {
            rndsIntegracaoVacina = (RndsIntegracaoVacina) getSession().get(RndsIntegracaoVacina.class, rndsIntegracaoVacina.getCodigo());
        } else {
            rndsIntegracaoVacina = new RndsIntegracaoVacina();
        }
        if (vacinaAplicacao != null && vacinaAplicacao.getCodigo() != null) {
            rndsIntegracaoVacina.setVacinaAplicacao(vacinaAplicacao);
            String grupoAtendimento = IntegracaoVacinaRNDSHelper.getGrupoAtendimentoRnds(rndsIntegracaoVacina);
            rndsIntegracaoVacina.setGrupoAtendimento(String.valueOf(grupoAtendimento));
            rndsIntegracaoVacina.setEstrategiaRia(IntegracaoVacinaRNDSHelper.getRia(vacinaAplicacao));
            rndsIntegracaoVacina.setUuidOrigem(IntegracaoVacinaRNDSHelper.getIdOrigem(rndsIntegracaoVacina));
        }

        this.rndsIntegracaoVacina.setSituacao(RndsIntegracaoVacina.Situacao.FILA_PROCESSAMENTO.value());

        RndsIntegracaoVacina rndsIntegracaoVacinaSave = BOFactory.save(this.rndsIntegracaoVacina);
        getSession().flush();

        ImmunizationPayloadDTO dto = IntegracaoVacinaRNDSHelper.getPayloadDTO(rndsIntegracaoVacina);
        String msgValidacao = validarDTO(dto);
        String mensagemSQS = IntegracaoVacinaRNDSHelper.getMensagemSQS(rndsIntegracaoVacinaSave, HttpMethod.POST, dto);

        if (StringUtils.isNotEmpty(msgValidacao)) {
            addOcorrencias(rndsIntegracaoVacina, msgValidacao, mensagemSQS);
            rndsIntegracaoVacina.setSituacao(RndsIntegracaoVacina.Situacao.FALHA.value());
            BOFactory.save(this.rndsIntegracaoVacina);
        } else {
            AmazonSQS amazonSQS = AwsUtils.getSQSClient();
            amazonSQS.sendMessage(RndsUtil.getSqsQueue(), mensagemSQS);
            addOcorrencias(rndsIntegracaoVacinaSave, descricaoOcorrencia, mensagemSQS);
        }
    }

    private String validarDTO(ImmunizationPayloadDTO dto) {
        StringBuilder msg = new StringBuilder();

        if (StringUtils.isEmpty(dto.getEstrategiaRia())) {
            msg.append("- Estratégia RIA não informada!");
        }
        if (StringUtils.isEmpty(dto.getCnsProfisisonalSaude())) {
            msg.append("- Profissional sem CNS!");
        }
        if (StringUtils.isEmpty(dto.getNomeFabricanteImunobiologico())) {
            msg.append("- Nome do fabricante do Imunobiológico não preenchido!");
        }
        if (StringUtils.isEmpty(dto.getCodigoPni())) {
            msg.append("- Código PNI do fabricante não preenchido!");
        }
        if (StringUtils.isEmpty(dto.getCnsPaciente()) && StringUtils.isEmpty(dto.getCpfPaciente())) {
            msg.append("- Paciente sem CNS ou CPF!");
        }
        if (StringUtils.isEmpty(dto.getCnesEstabelecimento())) {
            msg.append("- Estabelecimento sem CNES!");
        }
        if (String.valueOf(VacinaCalendario.Doses.NENHUMA_OPCAO.value()).equals(dto.getCodigoDoseVacina()) ||
                StringUtils.isEmpty(dto.getCodigoDoseVacina())) {
            msg.append("- Vacina sem dose informada!");
        }
        if (StringUtils.isEmpty(dto.getCodigoImunobiologico())) {
            msg.append("- Vacina sem código do imunobiológico!");
        }
        if (StringUtils.isEmpty(dto.getGrupoAtendimento())) {
            msg.append("- Grupo de atendimento não informado!");
        }
        if (RepositoryComponentDefault.NAO_LONG.equals(dto.getFlagHistorico()) &&
                StringUtils.isEmpty(dto.getNumeroLoteImunobiologico())
        ) {
            msg.append("- Lote da Vacina não informado!");
        }
        if (RndsIntegracaoVacina.Ria.RIA_C.value().equals(dto.getEstrategiaRia()) &&
                RepositoryComponentDefault.SIM_LONG.equals(dto.getFlagHistorico())) {
            msg.append("- Vacinas RIA-C não podem ser histórico!");
        }
        if (RndsIntegracaoVacina.Ria.RIA_R.value().equals(dto.getEstrategiaRia())) {
            if (StringUtils.isEmpty(dto.getLocalAplicacao())) {
                msg.append("- Local de aplicação não informado!");
            }
            if (StringUtils.isEmpty(dto.getViaAdministracao())) {
                msg.append("- Via de administração não informada!");
            }
        }

        return msg.toString();
    }

    private void addOcorrencias(RndsIntegracaoVacina rndsIntegracaoVacina, String descricaoOcorrencia, String mensagemSQS) throws DAOException, ValidacaoException {
        RndsVacinaOcorrencias ocorrenciasPadrao = new RndsVacinaOcorrencias();
        ocorrenciasPadrao.setRndsIntegracaoVacina(rndsIntegracaoVacina);
        ocorrenciasPadrao.setDataOcorrencia(DataUtil.getDataAtual());
        ocorrenciasPadrao.setDescricao(descricaoOcorrencia);
        ocorrenciasPadrao.setDocumentoEnviado(mensagemSQS);
        BOFactory.save(ocorrenciasPadrao);
    }

}


