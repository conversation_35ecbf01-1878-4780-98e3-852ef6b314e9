package br.com.celk.aih.query;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AutorizacaoInternacaoHospitalarDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaContaPacienteCadastroAih extends CommandQuery<QueryConsultaContaPacienteCadastroAih> {

    AutorizacaoInternacaoHospitalarDTO param;
    List<ContaPaciente> result;

    public QueryConsultaContaPacienteCadastroAih(AutorizacaoInternacaoHospitalarDTO param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        hql.addToSelect("cpp.codigo", "codigo");

        hql.addToSelect("u.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("u.nome", "usuarioCadsus.nome");

        hql.addToSelect("e.codigo", "atendimentoInformacao.empresa.codigo");
        hql.addToSelect("e.descricao", "atendimentoInformacao.empresa.descricao");

        hql.addToSelect("ap.codigo", "atendimentoInformacao.atendimentoPrincipal.codigo");
        
        hql.addToSelect("profissionalResponsavel.codigo", "atendimentoInformacao.atendimentoPrincipal.profissionalResponsavel.codigo");
        hql.addToSelect("profissionalResponsavel.nome", "atendimentoInformacao.atendimentoPrincipal.profissionalResponsavel.nome");
        hql.addToSelect("profissionalResponsavel.codigoCns", "atendimentoInformacao.atendimentoPrincipal.profissionalResponsavel.codigoCns");

        hql.addToSelect("taf.codigo", "atendimentoInformacao.tipoAtendimentoFaturamento.codigo");
        hql.addToSelect("taf.descricao", "atendimentoInformacao.tipoAtendimentoFaturamento.descricao");

        hql.setTypeSelect(ContaPaciente.class.getName());

        hql.addToFrom("ContaPaciente contaPaciente"
                + " left join contaPaciente.contaPacientePrincipal cpp"
                + " left join cpp.usuarioCadsus u"
                + " left join cpp.atendimentoInformacao ai"
                + " left join ai.empresa e"
                + " left join ai.atendimentoPrincipal ap"
                + " left join ap.profissionalResponsavel profissionalResponsavel"
                + " left join ai.tipoAtendimentoFaturamento taf");

        hql.addToWhereWhithAnd("contaPaciente.codigo = cpp.codigo");
        hql.addToWhereWhithAnd("u.codigo = ", param.getAutorizacaoInternacaoHospitalar().getUsuarioCadSus().getCodigo());
        hql.addToWhereWhithAnd("cpp.status in ", Arrays.asList(ContaPaciente.Status.ABERTA.value()));

        hql.addToWhereWhithAnd("not exists(select 1 from Aih aih left join aih.contaPaciente cp where cp.codigo = cpp.codigo)");
        Convenio convenioSus = (Convenio) BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("convenioSUS");

        hql.addToWhereWhithAnd("cpp.convenio = ", convenioSus.getCodigo());
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<ContaPaciente> getResult() {
        return result;
    }

}
