package br.com.celk.esus.query;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 14/08/17.
 */
public class QueryDetalhesEsusIntegracaoCdsDomicilio extends CommandQueryPager<QueryDetalhesEsusIntegracaoCdsDomicilio> {

    private DetalhesItensIntegracaoEsusDTOParam param;

    public QueryDetalhesEsusIntegracaoCdsDomicilio(DetalhesItensIntegracaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        DetalhesItensIntegracaoEsusDTO proxy = on(DetalhesItensIntegracaoEsusDTO.class);
        hql.setTypeSelect(DetalhesItensIntegracaoEsusDTO.class.getName());

        hql.addToSelect("esusIntegracaoCds.codigo", "esusIntegracaoCds.codigo");
        hql.addToSelect("esusIntegracaoCds.uuid", "esusIntegracaoCds.uuid");
        hql.addToSelect("esusIntegracaoCds.descricaoInconsistenciaEsus", "esusIntegracaoCds.descricaoInconsistenciaEsus");

        hql.addToSelect("enderecoUsuarioCadsus.codigo", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.enderecoUsuarioCadsus.codigo");
        hql.addToSelect("enderecoUsuarioCadsus.logradouro", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.enderecoUsuarioCadsus.logradouro");
        hql.addToSelect("enderecoUsuarioCadsus.complementoLogradouro", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.enderecoUsuarioCadsus.complementoLogradouro");
        hql.addToSelect("enderecoUsuarioCadsus.bairro", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.enderecoUsuarioCadsus.bairro");
        hql.addToSelect("enderecoUsuarioCadsus.numeroLogradouro", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.enderecoUsuarioCadsus.numeroLogradouro");

        hql.addToSelect("enderecoDomicilio.codigo", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.codigo");
        hql.addToSelect("enderecoDomicilio.numeroFamilia", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.numeroFamilia");

        hql.addToSelect("tipoLogradouro.codigo", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.enderecoUsuarioCadsus.tipoLogradouro.descricao");

        hql.addToSelect("equipeArea.descricao", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.equipeMicroArea.equipeArea.descricao");

        hql.addToSelect("equipeMicroArea.microArea", "esusIntegracaoCds.esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus.enderecoDomicilio.equipeMicroArea.microArea");

        hql.addToSelect("(select min(uc.nome) from UsuarioCadsus uc left join uc.enderecoDomicilio ed "
                + " where uc.flagResponsavelFamiliar = " + RepositoryComponentDefault.SIM_LONG
                + " and ed = enderecoDomicilio"
                + " and ed.excluido = "+RepositoryComponentDefault.NAO_EXCLUIDO+")", "responsavel");

        hql.addToSelect("(select min(uc.codigo) from UsuarioCadsus uc left join uc.enderecoDomicilio ed "
                + " where uc.flagResponsavelFamiliar = " + RepositoryComponentDefault.SIM_LONG
                + " and ed = enderecoDomicilio"
                + " and ed.excluido = "+RepositoryComponentDefault.NAO_EXCLUIDO+")", "codigoResponsavel");

        hql.addToFrom("EsusIntegracaoCds esusIntegracaoCds "
                + " left join esusIntegracaoCds.exportacaoEsusProcesso exportacaoEsusProcesso "
                + " left join esusIntegracaoCds.esusFichaEnderecoDomicilioEsus esusFichaEnderecoDomicilioEsus "
                + " left join esusFichaEnderecoDomicilioEsus.enderecoDomicilioEsus enderecoDomicilioEsus "
                + " left join enderecoDomicilioEsus.enderecoDomicilio enderecoDomicilio"
                + " left join enderecoDomicilio.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.equipeArea equipeArea"
                + " left join enderecoDomicilio.enderecoUsuarioCadsus enderecoUsuarioCadsus"
                + " left join enderecoUsuarioCadsus.cidade cidade"
                + " left join enderecoUsuarioCadsus.tipoLogradouro tipoLogradouro");

        hql.addToWhereWhithAnd("esusIntegracaoCds.tipo = ", EsusIntegracaoCds.Tipo.DOMICILIAR.value());
        hql.addToWhereWhithAnd("exportacaoEsusProcesso.codigo = ", param.getCodigoExportacaoEsusProcesso());

        hql.addToWhereWhithAnd(hql.getConsultaLiked("enderecoUsuarioCadsus.keyword", param.getEndereco()));
        hql.addToWhereWhithAnd("enderecoDomicilio.numeroFamilia =", param.getNumeroFamilia());
        hql.addToWhereWhithAnd("enderecoDomicilio.equipeMicroArea =", param.getEquipeMicroArea());

        if (RepositoryComponentDefault.SIM_LONG.equals(param.getForaDeArea())) {
            hql.addToWhereWhithAnd("enderecoDomicilioEsus.flagForaArea = ", RepositoryComponentDefault.SIM_LONG);
        }

        if (param.getArea() != null) {
            hql.addToWhereWhithAnd("equipeArea.codigo =", param.getArea().getCodigo());
            hql.addToWhereWhithAnd("cidade =", param.getArea().getCidade());
        }

        if (param.getCodigoDomicilio() != null) {
            hql.addToWhereWhithAnd("enderecoDomicilio.codigo = ", param.getCodigoDomicilio());
        }

        if(param.getCodigoUsuarioCadsus() != null){
            hql.addToWhereWhithAnd("exists(select 1 from UsuarioCadsus uc where uc.enderecoDomicilio = enderecoDomicilio and uc.codigo = " + param.getCodigoUsuarioCadsus() + ")");
        }

        if(param.getPaciente() != null){
            hql.addToWhereWhithAnd("exists(select 1 from UsuarioCadsus uc where uc.enderecoDomicilio = enderecoDomicilio and " + hql.getConsultaLiked("uc.nome", param.getPaciente()) + ")");
        }
        hql.addToWhereWhithAnd("esusIntegracaoCds.uuid = ", param.getUuid());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusIntegracaoCds.descricaoInconsistenciaEsus", this.param.getInconsistencia()));

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getComInconsistencia())) {
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is not null");
        } else if(RepositoryComponentDefault.NAO_LONG.equals(param.getComInconsistencia())){
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is null");
        }

        String orderType;
        String orderField = param.getPropSort();

        if(param.isAscending()){
            orderType = "asc";
        } else {
            orderType = "desc";
        }

        if (StringUtils.trimToNull(param.getPropSort()) != null) {
            if (param.getPropSort().startsWith(path(proxy.getEsusIntegracaoCds().getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus()))) {
                hql.addToOrder(orderField + " " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEnderecoUsuarioCadsus().getCidade().getDescricao()))) {
                hql.addToOrder("cidade.descricao " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEquipeMicroArea().getEquipeArea().getDescricao()))) {
                hql.addToOrder("enderecoDomicilio.area " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getEquipeMicroArea().getMicroArea()))) {
                hql.addToOrder("enderecoDomicilio.equipeMicroArea.microArea " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getRua()))) {
                hql.addToOrder("tipoLogradouro.descricao " + orderType);
                hql.addToOrder("enderecoUsuarioCadsus.logradouro " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getEsusFichaEnderecoDomicilioEsus().getEnderecoDomicilioEsus().getEnderecoDomicilio().getNumeroFamilia()))) {
                hql.addToOrder("enderecoDomicilio.numeroFamilia " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getUuid()))) {
                hql.addToOrder("esusIntegracaoCds.uuid " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getDescricaoInconsistenciaEsus()))) {
                hql.addToOrder("esusIntegracaoCds.descricaoInconsistenciaEsus " + orderType);
            } else {
                hql.addToOrder("enderecoDomicilio." + orderField + " " + orderType);
            }
        } else {
            hql.addToOrder("esusIntegracaoCds.dataCadastro desc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}