package br.com.celk.esus.query;

import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTO;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.DetalhesItensIntegracaoEsusDTOParam;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by laudecir on 16/10/17.
 */
public class QueryDetalhesEsusIntegracaoCdsTermoRecusaDomiciliar extends CommandQueryPager<QueryDetalhesEsusIntegracaoCdsTermoRecusaDomiciliar> {

    private DetalhesItensIntegracaoEsusDTOParam param;

    public QueryDetalhesEsusIntegracaoCdsTermoRecusaDomiciliar(DetalhesItensIntegracaoEsusDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {
        DetalhesItensIntegracaoEsusDTO proxy = on(DetalhesItensIntegracaoEsusDTO.class);
        hql.setTypeSelect(DetalhesItensIntegracaoEsusDTO.class.getName());

        hql.addToSelect("esusIntegracaoCds.codigo", "esusIntegracaoCds.codigo");
        hql.addToSelect("esusIntegracaoCds.uuid", "esusIntegracaoCds.uuid");
        hql.addToSelect("esusIntegracaoCds.descricaoInconsistenciaEsus", "esusIntegracaoCds.descricaoInconsistenciaEsus");

        hql.addToSelect("termoRecusaCadastroDomiciliar.codigo", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.codigo");
        hql.addToSelect("termoRecusaCadastroDomiciliar.logradouro", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.logradouro");
        hql.addToSelect("termoRecusaCadastroDomiciliar.complementoLogradouro", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.complementoLogradouro");
        hql.addToSelect("termoRecusaCadastroDomiciliar.bairro", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.bairro");
        hql.addToSelect("termoRecusaCadastroDomiciliar.numeroLogradouro", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.numeroLogradouro");

        hql.addToSelect("tipoLogradouro.codigo", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.tipoLogradouro.codigo");
        hql.addToSelect("tipoLogradouro.descricao", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.tipoLogradouro.descricao");

        hql.addToSelect("equipeMicroArea.codigo", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.equipeMicroArea.codigo");
        hql.addToSelect("equipeMicroArea.microArea", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.equipeMicroArea.microArea");

        hql.addToSelect("equipeArea.codigo", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.equipeMicroArea.equipeArea.codigo");
        hql.addToSelect("equipeArea.descricao", "esusIntegracaoCds.termoRecusaCadastroDomiciliar.equipeMicroArea.equipeArea.descricao");

        hql.addToFrom("EsusIntegracaoCds esusIntegracaoCds "
                + " left join esusIntegracaoCds.exportacaoEsusProcesso exportacaoEsusProcesso "
                + " left join esusIntegracaoCds.termoRecusaCadastroDomiciliar termoRecusaCadastroDomiciliar "
                + " left join termoRecusaCadastroDomiciliar.cidade cidade"
                + " left join termoRecusaCadastroDomiciliar.tipoLogradouro tipoLogradouro"
                + " left join termoRecusaCadastroDomiciliar.equipeMicroArea equipeMicroArea"
                + " left join equipeMicroArea.equipeArea equipeArea"
        );

        hql.addToWhereWhithAnd("esusIntegracaoCds.tipo = ", EsusIntegracaoCds.Tipo.TERMO_RECUSA_CADASTRO_DOMICILIAR.value());
        hql.addToWhereWhithAnd("exportacaoEsusProcesso.codigo = ", param.getCodigoExportacaoEsusProcesso());

        hql.addToWhereWhithAnd("termoRecusaCadastroDomiciliar.codigo = ", this.param.getCodigoTermoRecusa());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("(tipoLogradouro.descricao || ' ' || termoRecusaCadastroDomiciliar.logradouro)", param.getEndereco()));
        hql.addToWhereWhithAnd("termoRecusaCadastroDomiciliar.equipeMicroArea =", param.getEquipeMicroArea());

        if (param.getArea() != null) {
            hql.addToWhereWhithAnd("equipeArea.codigo =", param.getArea().getCodigo());
            hql.addToWhereWhithAnd("cidade =", param.getArea().getCidade());
        }

        hql.addToWhereWhithAnd("esusIntegracaoCds.uuid = ", param.getUuid());
        hql.addToWhereWhithAnd(hql.getConsultaLiked("esusIntegracaoCds.descricaoInconsistenciaEsus", this.param.getInconsistencia()));

        if(RepositoryComponentDefault.SIM_LONG.equals(param.getComInconsistencia())) {
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is not null");
        } else if(RepositoryComponentDefault.NAO_LONG.equals(param.getComInconsistencia())){
            hql.addToWhereWhithAnd("esusIntegracaoCds.descricaoInconsistenciaEsus is null");
        }

        String orderType;
        String orderField = param.getPropSort();

        if(param.isAscending()){
            orderType = "asc";
        } else {
            orderType = "desc";
        }

        if (StringUtils.trimToNull(param.getPropSort()) != null) {
            if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getTermoRecusaCadastroDomiciliar().getEquipeMicroArea().getEquipeArea().getDescricao()))) {
                hql.addToOrder("equipeArea.descricao " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getTermoRecusaCadastroDomiciliar().getEquipeMicroArea().getMicroArea()))) {
                hql.addToOrder("equipeMicroArea.microArea " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getTermoRecusaCadastroDomiciliar().getRuaFormatadaSemNumero()))) {
                hql.addToOrder("tipoLogradouro.descricao " + orderType);
                hql.addToOrder("termoRecusaCadastroDomiciliar.logradouro " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getUuid()))) {
                hql.addToOrder("esusIntegracaoCds.uuid " + orderType);
            } else if (param.getPropSort().equals(path(proxy.getEsusIntegracaoCds().getDescricaoInconsistenciaEsus()))) {
                hql.addToOrder("esusIntegracaoCds.descricaoInconsistenciaEsus " + orderType);
            } else {
                hql.addToOrder(orderField + " " + orderType);
            }
        } else {
            hql.addToOrder("esusIntegracaoCds.dataCadastro desc");
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }
}