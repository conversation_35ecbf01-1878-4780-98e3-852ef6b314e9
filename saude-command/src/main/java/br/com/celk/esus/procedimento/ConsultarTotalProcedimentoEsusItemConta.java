package br.com.celk.esus.procedimento;

import br.com.celk.esus.interfaces.dto.ConsultarTotalProcedimentoEsusParam;
import br.com.celk.esus.interfaces.dto.TotalProcedimentoEsusDTO;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus;

import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConsultarTotalProcedimentoEsusItemConta extends CommandQuery<ConsultarTotalProcedimentoEsusItemConta> {

    private List<TotalProcedimentoEsusDTO> resultado;
    private ConsultarTotalProcedimentoEsusParam param;

    public ConsultarTotalProcedimentoEsusItemConta(ConsultarTotalProcedimentoEsusParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("procedimentoEsus.procedimentoConsolidado", "codigoProcedimentoConsolidado");
        hql.addToSelect("count(*)", "quantidade");

        hql.setTypeSelect(TotalProcedimentoEsusDTO.class.getName());

        hql.addToFrom(" ItemContaPaciente itemContaPaciente "
                + " left join itemContaPaciente.procedimento procedimento " +
                " join itemContaPaciente.contaPaciente contaPaciente " +
                " left join contaPaciente.atendimentoInformacao atendimentoInformacao " +
                " left join atendimentoInformacao.atendimentoPrincipal atendimento ");

        hql.addToFrom("ProcedimentoEloEsus elo"
                + " left join elo.procedimentoEsus procedimentoEsus");

        hql.addToWhereWhithAnd("procedimento = elo.procedimento");
        if (param.getDataAtendimento() != null) {
            DatePeriod periodo = Data.adjustRangeHour(param.getDataAtendimento());
            hql.addToWhereWhithAnd("atendimento.dataAtendimento >= ", periodo.getDataInicial());
            hql.addToWhereWhithAnd("atendimento.dataAtendimento <= ", periodo.getDataFinal());
        }
        hql.addToWhereWhithAnd("itemContaPaciente.profissional = ", param.getProfissional());
        hql.addToWhereWhithAnd("atendimento.empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("procedimentoEsus.fichaIntegracao = ", ProcedimentoEsus.FichaIntegracao.CONSOLIDADO.value());

        hql.addToWhereWhithAnd("exists (select 1 from ItemContaEsus itemContaEsus" +
                " left join itemContaEsus.contaPaciente cp" +
                " where cp.codigo = contaPaciente.codigo)");

        hql.addToGroup("procedimentoEsus.procedimentoConsolidado");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        resultado = hql.getBeanList((List) result);
    }

    public List<TotalProcedimentoEsusDTO> getResultado() {
        return resultado;
    }

    @Override
    public Collection getResult() {
        return resultado;
    }

}
