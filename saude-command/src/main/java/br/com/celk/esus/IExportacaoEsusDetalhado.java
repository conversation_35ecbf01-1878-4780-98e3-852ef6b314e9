package br.com.celk.esus;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.gov.saude.esus.transport.common.generated.thrift.DadoTransporteThrift;
import org.hibernate.Session;

import java.util.List;

/**
 * Created by sulivan on 01/09/17.
 */
public interface IExportacaoEsusDetalhado {

    boolean hasIntegratedRecord();

    List<DadoTransporteThrift> getArquivoListRefatorado();

    List<String> getInconsistencyList();

    void executar(Session session) throws DAOException, ValidacaoException;

    enum TipoDadoSerializadoEsus {

        FICHA_CADASTRO_INDIVIDUAL(2L),
        FICHA_CADASTRO_DOMICILIAR(3L),
        FICHA_ATENDIMENTO_INDIVIDUAL(4L),
        FICHA_ATENDIMENTO_ODONTOLOGICO(5L),
        FICHA_ATIVIDADE_COLETIVA(6L),
        FICHA_PROCEDIMENTOS(7L),
        FICHA_VISITA_DOMICILIAR(8L),
        ATENDIMENTO_SOFTWARE_PRONTUARIO(9L),
        FICHA_ATENDIMENTO_DOMICILAR(10L),
        FICHA_AVALIACAO_ELEGEBILIDADE(11L),
        MARCADORES_CONSUMO_ALIMENTAR(12L),
        SINDROME_NEUROLOGICA_ZIKA_MICROCEFALIA(13L),
        ;

        private final Long value;

        public Long getValue() {
            return value;
        }

        TipoDadoSerializadoEsus(Long value) {
            this.value = value;
        }

    }

}
