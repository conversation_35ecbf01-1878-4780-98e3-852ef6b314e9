package br.com.celk.esus;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.consulta.Restrictions;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.ExportacaoEsusProcesso;
import br.com.ksisolucoes.vo.cadsus.cds.EsusFichaUsuarioCadsusEsus;
import br.com.ksisolucoes.vo.esus.EsusIntegracaoCds;
import br.com.ksisolucoes.vo.esus.LoadInterceptorExistsEsusFichaEnderecoDomicilioEsusAtualizado;
import br.com.ksisolucoes.vo.esus.LoadInterceptorExistsEsusFichaUsuarioCadsusEsusAtualizado;
import ch.lambdaj.Lambda;
import org.hibernate.Query;
import org.hibernate.SQLQuery;

import java.util.List;

import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import org.hibernate.criterion.Order;

/**
 *
 * <AUTHOR>
 */
public class RemoverProcessoExportacaoEsus extends AbstractCommandTransaction {

    private Long codigoProcesso;
    private int cont;
    private int contFlush;

    public RemoverProcessoExportacaoEsus(Long codigoProcesso) {
        this.codigoProcesso = codigoProcesso;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {

        ExportacaoEsusProcesso processo = HibernateUtil.lockTable(ExportacaoEsusProcesso.class, codigoProcesso);

        excluirEsusFichaUsuarioCadsusEsus(processo.getCodigo());
        excluirEsusFichaEnderecoDomicilioEsus(processo.getCodigo());
        atualizarEsusIntegracao(processo);

        String path = processo.getPath();
        if(path != null){
            FileUtils.excluirArquivoFtpSeExiste(path);
        }

        String deleteProcesso = "delete from exportacao_esus_processo where cd_exp_proc = :codigoprocesso";
        SQLQuery sqlQuerProcesso = getSession().createSQLQuery(deleteProcesso);
        sqlQuerProcesso.setParameter("codigoprocesso", processo.getCodigo());
        sqlQuerProcesso.executeUpdate();

        String deleteAsyncProcess = "delete from async_process where cd_process = :codigoprocess";
        SQLQuery sqlQuerAsyncProcess = getSession().createSQLQuery(deleteAsyncProcess);
        sqlQuerAsyncProcess.setParameter("codigoprocess", processo.getAsyncProcess().getCodigo());
        sqlQuerAsyncProcess.executeUpdate();

//        BOFactory.delete(processo.getAsyncProcess());
//        BOFactory.delete(processo);
    }

    private void excluirEsusFichaUsuarioCadsusEsus(Long codigoExportacaoEsusProcesso){
        /*
         ********* INÍCIO *********
         * As regras abaixo servem para manter apenas um esus_ficha_usuario_cadsus_esus por usuario_cadsus_esus, pois senão serão integrados dados duplicados.
         * Nesse caso, se encontrar um registro de esus_ficha_usuario_cadsus_esus com data de cadastro maior que a do que já foi integrado, ele deleta o que já foi integrado e deixa o mais novo.
         */
        List<EsusIntegracaoCds> esusIntegracaoCdsList = LoadManager.getInstance(EsusIntegracaoCds.class)
                .addProperty(VOUtils.montarPath(EsusIntegracaoCds.PROP_ESUS_FICHA_USUARIO_CADSUS_ESUS, EsusFichaUsuarioCadsusEsus.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EsusIntegracaoCds.PROP_TIPO, EsusIntegracaoCds.Tipo.INDIVIDUAL.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EsusIntegracaoCds.PROP_EXPORTACAO_ESUS_PROCESSO, ExportacaoEsusProcesso.PROP_CODIGO), codigoExportacaoEsusProcesso))
                .addInterceptor(new LoadInterceptorExistsEsusFichaUsuarioCadsusEsusAtualizado()).start().getList();

        if(CollectionUtils.isNotNullEmpty(esusIntegracaoCdsList)){
            List<Long> codigoEsusFichaUsuarioCadsusEsusList = Lambda.extract(esusIntegracaoCdsList, on(EsusIntegracaoCds.class).getEsusFichaUsuarioCadsusEsus().getCodigo());

            String deleteEsusIntegracaoCds = "delete from esus_integracao_cds where cd_esus_ficha_usu_cadsus_esus in(:codigoEsusFichaUsuarioCadsusEsusList)";
            SQLQuery sqlQueryEsusIntegracaoCds = getSession().createSQLQuery(deleteEsusIntegracaoCds);
            sqlQueryEsusIntegracaoCds.setParameterList("codigoEsusFichaUsuarioCadsusEsusList", codigoEsusFichaUsuarioCadsusEsusList);
            sqlQueryEsusIntegracaoCds.executeUpdate();

            String deleteEsusFichaUsuarioCadsusEsus = "delete from esus_ficha_usuario_cadsus_esus where cd_esus_ficha_usu_cadsus_esus in(:codigoEsusFichaUsuarioCadsusEsusList)";
            SQLQuery sqlEsusFichaUsuarioCadsusEsus = getSession().createSQLQuery(deleteEsusFichaUsuarioCadsusEsus);
            sqlEsusFichaUsuarioCadsusEsus.setParameterList("codigoEsusFichaUsuarioCadsusEsusList", codigoEsusFichaUsuarioCadsusEsusList);
            sqlEsusFichaUsuarioCadsusEsus.executeUpdate();
        }
        /*
         ********* FIM *********
         */
    }

    private void excluirEsusFichaEnderecoDomicilioEsus(Long codigoExportacaoEsusProcesso){
        /*
         ********* INÍCIO *********
         * As regras abaixo servem para manter apenas um esus_ficha_endereco_domicilio_esus por endereco_domicilio_esus, pois senão serão integrados dados duplicados.
         * Nesse caso, se encontrar um registro de esus_ficha_endereco_domicilio_esus com data de cadastro maior que a do que já foi integrado, ele deleta o que já foi integrado e deixa o mais novo.
         */
        List<EsusIntegracaoCds> esusIntegracaoCdsList = LoadManager.getInstance(EsusIntegracaoCds.class)
                .addProperty(VOUtils.montarPath(EsusIntegracaoCds.PROP_ESUS_FICHA_ENDERECO_DOMICILIO_ESUS, EsusFichaUsuarioCadsusEsus.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EsusIntegracaoCds.PROP_TIPO, EsusIntegracaoCds.Tipo.DOMICILIAR.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EsusIntegracaoCds.PROP_EXPORTACAO_ESUS_PROCESSO, ExportacaoEsusProcesso.PROP_CODIGO), codigoExportacaoEsusProcesso))
                .addInterceptor(new LoadInterceptorExistsEsusFichaEnderecoDomicilioEsusAtualizado()).start().getList();

        if(CollectionUtils.isNotNullEmpty(esusIntegracaoCdsList)){
            List<Long> codigoEsusFichaEnderecoDomicilioEsusList = Lambda.extract(esusIntegracaoCdsList, on(EsusIntegracaoCds.class).getEsusFichaEnderecoDomicilioEsus().getCodigo());

            String deleteEsusIntegracaoCds = "delete from esus_integracao_cds where cd_esus_ficha_endereco_domicilio_esus in(:codigoEsusFichaEnderecoDomicilioEsusList)";
            SQLQuery sqlQueryEsusIntegracaoCds = getSession().createSQLQuery(deleteEsusIntegracaoCds);
            sqlQueryEsusIntegracaoCds.setParameterList("codigoEsusFichaEnderecoDomicilioEsusList", codigoEsusFichaEnderecoDomicilioEsusList);
            sqlQueryEsusIntegracaoCds.executeUpdate();

            String deleteEsusFichaUsuarioCadsusDomicilio = "delete from esus_ficha_usuario_cadsus_domicilio where cd_esus_ficha_endereco_domicilio_esus in(:codigoEsusFichaEnderecoDomicilioEsusList)";
            SQLQuery sqlEsusFichaUsuarioCadsusDomicilio = getSession().createSQLQuery(deleteEsusFichaUsuarioCadsusDomicilio);
            sqlEsusFichaUsuarioCadsusDomicilio.setParameterList("codigoEsusFichaEnderecoDomicilioEsusList", codigoEsusFichaEnderecoDomicilioEsusList);
            sqlEsusFichaUsuarioCadsusDomicilio.executeUpdate();

            String deleteEsusFichaEnderecoDomicilioEsus = "delete from esus_ficha_endereco_domicilio_esus where cd_esus_ficha_endereco_domicilio_esus in(:codigoEsusFichaEnderecoDomicilioEsusList)";
            SQLQuery sqlEsusFichaEnderecoDomicilioEsus = getSession().createSQLQuery(deleteEsusFichaEnderecoDomicilioEsus);
            sqlEsusFichaEnderecoDomicilioEsus.setParameterList("codigoEsusFichaEnderecoDomicilioEsusList", codigoEsusFichaEnderecoDomicilioEsusList);
            sqlEsusFichaEnderecoDomicilioEsus.executeUpdate();
        }
        /*
         ********* FIM *********
         */
    }

    private void atualizarEsusIntegracao(ExportacaoEsusProcesso processo) {
        int first = 0;
        int limit = 500;
        List<EsusIntegracaoCds> esusIntegracaoCdsList;

        do {
            esusIntegracaoCdsList = getSession().createCriteria(EsusIntegracaoCds.class)
                    .add(Restrictions.eq(VOUtils.montarPath(EsusIntegracaoCds.PROP_EXPORTACAO_ESUS_PROCESSO, ExportacaoEsusProcesso.PROP_CODIGO), processo.getCodigo()))
                    .addOrder(Order.asc(VOUtils.montarPath(EsusIntegracaoCds.PROP_CODIGO)))
                    .setFirstResult(first)
                    .setMaxResults(limit)
                    .list();

            if (CollectionUtils.isNotNullEmpty(esusIntegracaoCdsList)) {
                List<Long> codigoFichasList = Lambda.extract(esusIntegracaoCdsList, on(EsusIntegracaoCds.class).getCodigo());

                String update = "UPDATE esus_integracao_cds SET ds_inconsistencia_esus = null, cd_exp_proc = null WHERE cd_esus_integracao_cds in(:codigoFichasList) ";

                Query queryItemContaPaciente = getSession().createSQLQuery(update);
                queryItemContaPaciente.setParameterList("codigoFichasList", codigoFichasList);
                queryItemContaPaciente.executeUpdate();

                Loggable.log.info("registro numero " + (first+esusIntegracaoCdsList.size()) + " processado (Individual)");
            }
            first += limit;
            getSession().flush();
            getSession().clear();
        } while (esusIntegracaoCdsList.size() >= limit);
    }
}
