package br.com.celk.esus.procedimento;

import br.com.celk.esus.interfaces.dto.ConsultarTotalProcedimentoEsusParam;
import br.com.celk.esus.interfaces.dto.TotalProcedimentoEsusDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.ProcedimentoEsus;
import java.util.Collection;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ConsultarTotalProcedimentoEsus extends CommandQuery<ConsultarTotalProcedimentoEsus> {

    private List<TotalProcedimentoEsusDTO> resultado;
    private ConsultarTotalProcedimentoEsusParam param;

    public ConsultarTotalProcedimentoEsus(ConsultarTotalProcedimentoEsusParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException, ValidacaoException {

        hql.addToSelect("p.procedimentoConsolidado", "codigoProcedimentoConsolidado");
        hql.addToSelect("count(*)", "quantidade");

        hql.setTypeSelect(TotalProcedimentoEsusDTO.class.getName());

        hql.addToFrom("AtendimentoItem ai"
                + " left join ai.atendimento a"
                + " left join ai.procedimentoCompetencia pc");

        hql.addToFrom("ProcedimentoEloEsus elo"
                + " left join elo.procedimentoEsus p");

        hql.addToWhereWhithAnd("pc.id.procedimento = elo.procedimento");
        if (param.getDataAtendimento() != null) {
            DatePeriod periodo = Data.adjustRangeHour(param.getDataAtendimento());
            hql.addToWhereWhithAnd("a.dataAtendimento >= ", periodo.getDataInicial());
            hql.addToWhereWhithAnd("a.dataAtendimento <= ", periodo.getDataFinal());
        }
        if (param.getAtendimento() != null) {
            hql.addToWhereWhithAnd("a.codigo = ", param.getAtendimento().getCodigo());
        }
        hql.addToWhereWhithAnd("a.profissional = ", param.getProfissional());
        hql.addToWhereWhithAnd("a.empresa = ", param.getEmpresa());
        hql.addToWhereWhithAnd("p.fichaIntegracao = ", ProcedimentoEsus.FichaIntegracao.CONSOLIDADO.value());

        hql.addToGroup("p.procedimentoConsolidado");

    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        resultado = hql.getBeanList((List) result);
    }

    public List<TotalProcedimentoEsusDTO> getResultado() {
        return resultado;
    }

    @Override
    public Collection getResult() {
        return resultado;
    }

}
