package br.com.celk.helper;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;

/**
 * <AUTHOR>
 */
public class SmsHelper {

    private SmsHelper() {
    }

    public static String getURLProvider() throws DAOException {
        return BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("urlServicoSMS");
    }

    public static String getKeyProvider() throws DAOException {
        return BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("chaveServicoSMS");
    }

}
