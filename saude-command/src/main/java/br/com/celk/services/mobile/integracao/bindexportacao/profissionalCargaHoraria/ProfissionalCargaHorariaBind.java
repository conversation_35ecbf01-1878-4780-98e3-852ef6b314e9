package br.com.celk.services.mobile.integracao.bindexportacao.profissionalCargaHoraria;

import br.com.celk.services.mobile.integracao.exportarrecurso.IBindVoExport;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.cadsus.VinculacaoSubTipo;
import br.com.ksisolucoes.vo.esus.TipoAtividadePublico;
import org.apache.camel.dataformat.bindy.annotation.CsvRecord;
import org.apache.camel.dataformat.bindy.annotation.DataField;

import java.util.Date;

/**
 * <AUTHOR>
 */
@CsvRecord(separator = "\\|", crlf = "UNIX")
public class ProfissionalCargaHorariaBind implements IBindVoExport<ProfissionalCargaHoraria> {

    @DataField(pos = 1)
    private Long codigoSistema;
    @DataField(pos = 2)
    private Long versao;
    @DataField(pos = 3, required = true)
    private Long cd_prof_carga_horaria;
    @DataField(pos = 4)
    private String tp_registro;
    @DataField(pos = 5)
    private Date dt_ativacao;
    @DataField(pos = 6)
    private Date dt_desativacao;
    @DataField(pos = 7)
    private Date competencia_inicio;
    @DataField(pos = 8)
    private Date competencia_fim;
    @DataField(pos = 9)
    private String profissional_id_cnes;
    @DataField(pos = 10)
    private String tp_sus_nao_sus;
    @DataField(pos = 11)
    private String nr_registro;
    @DataField(pos = 12)
    private Long carga_hr_amb;
    @DataField(pos = 13)
    private Long carga_hr_hosp;
    @DataField(pos = 14)
    private Long carga_hr_outros;
    @DataField(pos = 15)
    private Date dt_atualizacao;
    @DataField(pos = 16)
    private Long empresa;
    @DataField(pos = 17, required = true)
    private Long cd_profissional;
    @DataField(pos = 18, required = true)
    private String cd_cbo;
    @DataField(pos = 19)
    private Long cd_orgao_emissor;
    @DataField(pos = 20)
    private Long cd_mot_desligamento;
    @DataField(pos = 21)
    private Long cd_cnes_processo;

    @Override
    public void buildProperties(ProfissionalCargaHoraria vo) {
        codigoSistema = vo.getCodigo();
        versao = vo.getVersionAll();
        cd_prof_carga_horaria = vo.getCodigo();
        tp_registro = vo.getTipoRegistro();
        dt_ativacao = vo.getDataAtivacao();
        dt_desativacao = vo.getDataDesativacao();
        competencia_inicio = vo.getCompetenciaInicio();
        competencia_fim = vo.getCompetenciaFim();
        profissional_id_cnes = vo.getProfissionalIdCnes();
        tp_sus_nao_sus = vo.getTipoSusNaoSus();
        nr_registro = vo.getNumeroRegistro();
        carga_hr_amb = vo.getCargaHorariaAmbulatorial();
        carga_hr_hosp = vo.getCargaHorariaHospitalar();
        carga_hr_outros = vo.getCargaHorariaOutros();
        dt_atualizacao = vo.getDataAtualizacao();
        if (vo.getEmpresa() != null) {
            empresa = vo.getEmpresa().getCodigo();
        }
        if (vo.getProfissional() != null) {
            cd_profissional = vo.getProfissional().getCodigo();
        }
        if (vo.getTabelaCbo() != null) {
            cd_cbo = vo.getTabelaCbo().getCbo();
        }
        if (vo.getOrgaoEmissor() != null) {
            cd_orgao_emissor = vo.getOrgaoEmissor().getCodigo();
        }
        if (vo.getMotivoDesligamento() != null) {
            cd_mot_desligamento = vo.getMotivoDesligamento().getCodigo();
        }
        if (vo.getCnesProcesso() != null) {
            cd_cnes_processo = vo.getCnesProcesso().getCodigo();
        }
    }
}
