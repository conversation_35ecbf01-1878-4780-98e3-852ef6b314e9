package br.com.celk.services.mobile.integracao.bindimportacao.usuariocadsuscns.custom;

import br.com.celk.services.mobile.integracao.importarrecurso.DefaultImportacaoCommand;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDomicilio;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OnImportUsuarioCadsusCns extends DefaultImportacaoCommand<UsuarioCadsusCns> {

    /**
     * Efetua as regras de negócio relacionadas a importacao do UsuarioCadsusCns (Mobile)<br />
     * Regras do sistemas encontradas em {@link br.com.ksisolucoes.bo.cadsus.usuariocadsuscns.SaveUsuarioCadsusCns}
     *
     * @param object O UsuarioCadsusCns a ser processado
     */
    public OnImportUsuarioCadsusCns(UsuarioCadsusCns object) {
        super(object);
    }


    @Override
    protected UsuarioCadsusCns execute(UsuarioCadsusCns object) throws DAOException, ValidacaoException {
        return onSave(object);
    }

    /**
     * Quando está salvando o CNS
     *
     * @param object o UsuarioCadsusCns vindo da importacao
     */
    private UsuarioCadsusCns onSave(UsuarioCadsusCns object) throws ValidacaoException, DAOException {
        if(object.getUsuarioCadsus() == null){
            throw new ValidacaoException("O CNS " + object.getNumeroCartaoFormatado()+ " já foi atualizado");
        }
        if (object.getCodigo() == null) {
            List<UsuarioCadsusCns> listaUsuarioCadususCns = LoadManager.getInstance(UsuarioCadsusCns.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), object.getUsuarioCadsus().getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_NUMERO_CARTAO), QueryCustom.QueryCustomParameter.DIFERENTE, object.getNumeroCartao()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_EXCLUIDO), QueryCustom.QueryCustomParameter.DIFERENTE, 1))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_DATA_ATRIBUICAO), QueryCustom.QueryCustomParameter.MENOR, object.getDataAtribuicao()))
                    .start()
                    .getList();
            if (CollectionUtils.isNotEmpty(listaUsuarioCadususCns)) {
                for (UsuarioCadsusCns item : listaUsuarioCadususCns) {
                    item.setExcluido(RepositoryComponentDefault.EXCLUIDO);
                    BOFactory.save(item);
                }
            }
        }

//        getSession().clear();
//        getSession().flush();

        UsuarioCadsusCns save = BOFactory.save(object);

        List<UsuarioCadsusDomicilio> usuarioCadsusDomicilioList = LoadManager.getInstance(UsuarioCadsusDomicilio.class)
                .addProperties(new HQLProperties(UsuarioCadsusDomicilio.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDomicilio.PROP_USUARIO_CADSUS, save.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDomicilio.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, UsuarioCadsusDomicilio.STATUS_EXCLUIDO))
                .start().getList();

        if  (CollectionUtils.isNotEmpty(usuarioCadsusDomicilioList)) {
            for (UsuarioCadsusDomicilio usuarioCadsusDomicilio : usuarioCadsusDomicilioList) {
                if (usuarioCadsusDomicilio != null) {
                    BOFactory.getBO(UsuarioCadsusFacade.class).gerarAtualizarEsusFichaUsuarioCadsusDomicilio(usuarioCadsusDomicilio);
                }
            }
        }
        return save;
    }
}
