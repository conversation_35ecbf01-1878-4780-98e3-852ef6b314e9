package br.com.celk.horus.envio;

import br.com.celk.bo.hospital.endereco.EnderecoHelper;
import br.com.celk.helper.HorusHelper;
import br.com.celk.horus.dto.ProcessarEnvioHorusDTO;
import br.com.celk.horus.dto.QueryXmlHorusDispensacaoDTO;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.materiais.horus.RegistroHorusProcesso;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcessoEnvio;
import br.gov.saude.horus_ws.HorusWSAsync;
import br.gov.saude.horus_ws.HorusWSService;
import br.gov.saude.horus_ws.InconsistenciaFault;
import br.gov.saude.horus_ws.schemas.v1.horustypes.InformarDispensacaoMedicamentoEmLoteType;
import br.gov.saude.horus_ws.schemas.v1.horustypes.RespostaProcessamentoLoteType;
import br.gov.saude.horus_ws.schemas.v1.identificacao.EstabelecimentoCNESCNPJType;
import br.gov.saude.horus_ws.schemas.v1.identificacao.IdentificacaoType;
import br.gov.saude.horus_ws.schemas.v1.identificacao.ObjectFactory;
import br.gov.saude.horus_ws.schemas.v1.paciente.PacienteType;
import br.gov.saude.horus_ws.schemas.v1.prescritor.PrescritorType;
import br.gov.saude.horus_ws.schemas.v1.produto.ProdutoDispensacaoType;
import ch.lambdaj.Lambda;
import com.amazonaws.util.Base64;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;

import javax.xml.bind.JAXBElement;
import javax.xml.ws.BindingProvider;
import javax.xml.ws.Holder;
import javax.xml.ws.handler.MessageContext;
import javax.xml.ws.soap.SOAPFaultException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;

/**
 * Created by Leonardo.
 */
public class ProcessarEnvioHorusDispensacao extends AbstractCommandTransaction {

    private final ProcessarEnvioHorusDTO dto;
    private final List<QueryXmlHorusDispensacaoDTO> itens;
    private RegistroHorusProcesso registroHorusProcesso;

    public ProcessarEnvioHorusDispensacao(ProcessarEnvioHorusDTO dto, List<QueryXmlHorusDispensacaoDTO> itens) {
        this.dto = dto;
        this.itens = itens;
    }
    @Override
    public void execute() throws DAOException, ValidacaoException {
        try {
            br.gov.saude.horus_ws.schemas.v1.horustypes.ObjectFactory facType = new br.gov.saude.horus_ws.schemas.v1.horustypes.ObjectFactory();
            ObjectFactory facIdentificacao = new ObjectFactory();
            br.gov.saude.horus_ws.schemas.v1.produto.ObjectFactory facProduto = new br.gov.saude.horus_ws.schemas.v1.produto.ObjectFactory();
            br.gov.saude.horus_ws.schemas.v1.paciente.ObjectFactory facPaciente = new br.gov.saude.horus_ws.schemas.v1.paciente.ObjectFactory();
            br.gov.saude.horus_ws.schemas.v1.prescritor.ObjectFactory facPrescritor = new br.gov.saude.horus_ws.schemas.v1.prescritor.ObjectFactory();

            InformarDispensacaoMedicamentoEmLoteType dispensacao = facType.createInformarDispensacaoMedicamentoEmLoteType();
            IdentificacaoType identificacao = new IdentificacaoType();
            identificacao.setIdOrigem(RepositoryComponentDefault.ESFERA_MUNICIPAL);
            dispensacao.setIdentificacao(identificacao);

            InformarDispensacaoMedicamentoEmLoteType.Registro registro;
            EstabelecimentoCNESCNPJType estabelecimento;
            ProdutoDispensacaoType produtoDispensacao;
            PacienteType paciente;
            PrescritorType prescritor;
            boolean itemInconsistente;

            for (QueryXmlHorusDispensacaoDTO item : itens) {
                itemInconsistente = false;

                if (item.getEmpresa().getCnes() == null) {
                    addInconsistenciaEnvio("DISPENSAÇÃO: CNES da empresa " + item.getEmpresa().getDescricao() + " não definido.");
                    itemInconsistente = true;
                }

                if (item.getProduto().getTipoProdutoCatmat() == null) {
                    addInconsistenciaEnvio("DISPENSAÇÃO: Tipo do CATMAT do produto " + item.getProduto().getDescricaoFormatado() + " não definido.");
                    itemInconsistente = true;
                }

                if (item.getProduto().getMedicamentoCatmat() == null || item.getProduto().getMedicamentoCatmat().getCatmat() == null) {
                    addInconsistenciaEnvio("DISPENSAÇÃO: Código CATMAT do produto " + item.getProduto().getDescricaoFormatado() + " não definido.");
                    itemInconsistente = true;
                }

                if (item.getProduto().getTipoProdutoCatmat() == null) {
                    addInconsistenciaEnvio("DISPENSAÇÃO: Tipo CATMAT do produto " + item.getProduto().getDescricaoFormatado() + " não definido.");
                    itemInconsistente = true;
                }

                if (item.getGrupoEstoque().getId().getGrupo() == null) {
                    addInconsistenciaEnvio("DISPENSAÇÃO: Lote não definido. Produto: " + item.getProduto().getDescricaoFormatado() + ".");
                    itemInconsistente = true;
                }

                if (item.getGrupoEstoque().getDataValidade() == null) {
                    String inconsistenciaValidade = "DISPENSAÇÃO: Data de validade vazia ou nula. Produto: " + item.getProduto().getDescricaoFormatado();
                    if (item.getGrupoEstoque().getId().getGrupo() != null) {
                        inconsistenciaValidade += " LOTE: " + item.getGrupoEstoque().getId().getGrupo() + ".";
                    }
                    addInconsistenciaEnvio(inconsistenciaValidade);
                    itemInconsistente = true;
                }

                if (item.getDispensacaoMedicamentoItem().getQuantidadeDispensada() == null) {
                    String inconsistenciaLote = "DISPENSAÇÃO: Quantidade não definida. Produto: " + item.getProduto().getDescricaoFormatado() + ".";
                    if (item.getDispensacaoMedicamento().getCodigo() != null) {
                        inconsistenciaLote += " CÓDIGO DA DISPENSAÇÃO: " + item.getDispensacaoMedicamento().getCodigo() + ".";
                    }
                    addInconsistenciaEnvio(inconsistenciaLote);
                    itemInconsistente = true;
                }

                if (item.getDispensacaoMedicamento().getDataDispensacao() == null) {
                    String inconsistenciaLote = "DISPENSAÇÃO: Data da dispensação não definida. Produto: " + item.getProduto().getDescricaoFormatado() + ".";
                    if (item.getDispensacaoMedicamento().getCodigo() != null) {
                        inconsistenciaLote += " CÓDIGO DA DISPENSAÇÃO: " + item.getDispensacaoMedicamento().getCodigo() + ".";
                    }
                    addInconsistenciaEnvio(inconsistenciaLote);
                    itemInconsistente = true;
                }

                if (item.getNumeroCns() == null) {
                    String inconsistenciaCNS = "DISPENSAÇÃO: CNS do paciente não definido.";
                    if (item.getUsuarioCadsus() != null) {
                        inconsistenciaCNS += " PACIENTE: " + item.getUsuarioCadsus().getDescricaoFormatado() + ".";
                    }
                    addInconsistenciaEnvio(inconsistenciaCNS);
                    itemInconsistente = true;
                } else if (Long.parseLong(item.getNumeroCns()) <= 0) {
                    String inconsistenciaCNS = "DISPENSAÇÃO: CNS do paciente inválido.";
                    if (item.getUsuarioCadsus() != null) {
                        inconsistenciaCNS += " PACIENTE: " + item.getUsuarioCadsus().getDescricaoFormatado() + ".";
                    }
                    addInconsistenciaEnvio(inconsistenciaCNS);
                    itemInconsistente = true;
                }

                if (Produto.TipoProdutoCatmat.ESPECIALIZADO.value().equals(item.getProduto().getTipoProdutoCatmat()) && (item.getPesoUsuarioCadsus() == null || item.getAlturaUsuarioCadsus() == null)) {
                    String inconsistenciaCNS = "DISPENSAÇÃO: Peso e Altura do paciente não definido(s).";
                    if (item.getUsuarioCadsus() != null) {
                        inconsistenciaCNS += " PACIENTE: " + item.getUsuarioCadsus().getDescricaoFormatado() + ".";
                    }
                    addInconsistenciaEnvio(inconsistenciaCNS);
                    itemInconsistente = true;
                }

                if (item.getProfissional() != null) {
                    if (item.getProfissional().getNumeroRegistro() != null && !"".equals(Coalesce.asString(StringUtil.getDigits(item.getProfissional().getNumeroRegistro())))) {
                        if (StringUtil.getDigits(item.getProfissional().getNumeroRegistro()).length() > 8) {
                            String inconsistenciaCNS = "DISPENSAÇÃO: CRM (" + StringUtil.getDigits(item.getProfissional().getNumeroRegistro()) + ") do profissional " + item.getProfissional().getNome() + " não deve ser maior que 8 dígitos.";

                            addInconsistenciaEnvio(inconsistenciaCNS);
                            itemInconsistente = true;
                        }
                    }
                } else {
                    if (item.getDispensacaoMedicamento().getNumeroRegistro() != null && !"".equals(Coalesce.asString(StringUtil.getDigits(item.getDispensacaoMedicamento().getNumeroRegistro())))) {
                        if (StringUtil.getDigits(item.getDispensacaoMedicamento().getNumeroRegistro()).length() > 8) {
                            String inconsistenciaCNS = "DISPENSAÇÃO: CRM (" + StringUtil.getDigits(item.getDispensacaoMedicamento().getNumeroRegistro()) + ") da dispensação " + item.getDispensacaoMedicamento().getCodigo() + " não deve ser maior que 8 dígitos. Favor entrar em contato com o suporte.";

                            addInconsistenciaEnvio(inconsistenciaCNS);
                            itemInconsistente = true;
                        }
                    }
                }

                if (itemInconsistente) {
                    continue;
                }


                if (identificacao.getCoIBGE() <= 0 && item.getCidade() != null) {
                    identificacao.setCoIBGE(EnderecoHelper.getCodigoIbgeComDV(item.getCidade().getCodigo()).intValue());
                }

                registro = facType.createInformarDispensacaoMedicamentoEmLoteTypeRegistro();
                estabelecimento = facIdentificacao.createEstabelecimentoCNESCNPJType();
                estabelecimento.setIdIdentificacao(RepositoryComponentDefault.ID_IDENTIFICACAO_ESTABELECIMENTO_DESTINO_CNES);
                ObjectFactory objectFactory = new ObjectFactory();
                JAXBElement<String> coCnes = objectFactory.createEstabelecimentoTypeCoCNES(item.getEmpresa().getCnes());
                estabelecimento.setCoCNES(coCnes);

                produtoDispensacao = facProduto.createProdutoDispensacaoType();
                produtoDispensacao.setCoRegistroOrigem(item.getDispensacaoItemLote().getCodigo().toString());
                produtoDispensacao.setNuProduto(item.getProduto().getTipoProdutoCatmat().concat(item.getProduto().getMedicamentoCatmat().getCatmat()));
                produtoDispensacao.setNuLote(item.getGrupoEstoque().getId().getGrupo());
                produtoDispensacao.setDtValidade(DataUtil.getFormatarDiaMesAnoTraco(item.getGrupoEstoque().getDataValidade()));
                produtoDispensacao.setQtProduto(item.getDispensacaoMedicamentoItem().getQuantidadeDispensada().longValue());
                produtoDispensacao.setDtRegistro(DataUtil.getFormatarDiaMesAnoTraco(item.getDispensacaoMedicamento().getDataDispensacao()));
//                    produto.setSgProgramaSaude();
//                    produto.setCoIUM();
                produtoDispensacao.setDtCompetencia(DataUtil.getFormatarMesAnoTraco(item.getDispensacaoMedicamento().getDataDispensacao()));

                paciente = facPaciente.createPacienteType();
//                    paciente.setCid10();
                paciente.setNuCNS(item.getNumeroCns());
                if (item.getPesoUsuarioCadsus() != null) {
                    paciente.setPeso(new BigDecimal(item.getPesoUsuarioCadsus()).setScale(2, RoundingMode.HALF_EVEN));
                }

                if (item.getAlturaUsuarioCadsus() != null) {
                    paciente.setAltura(new BigInteger(item.getAlturaUsuarioCadsus().toString()));
                }

                prescritor = facPrescritor.createPrescritorType();
                prescritor.setCoCNES(item.getEmpresa().getCnes());

                if (item.getProfissional() != null) {
                    if (item.getProfissional().getNumeroRegistro() != null && !"".equals(Coalesce.asString(StringUtil.getDigits(item.getProfissional().getNumeroRegistro())))) {
                        prescritor.setNuCRM(new BigInteger(Coalesce.asString(StringUtil.getDigits(item.getProfissional().getNumeroRegistro()))));
                    }

                    prescritor.setUfCRM(item.getProfissional().getUnidadeFederacaoConselhoRegistro());
                } else {
                    if (item.getDispensacaoMedicamento().getNumeroRegistro() != null && !"".equals(Coalesce.asString(StringUtil.getDigits(item.getDispensacaoMedicamento().getNumeroRegistro())))) {
                        prescritor.setNuCRM(new BigInteger(Coalesce.asString(StringUtil.getDigits(item.getDispensacaoMedicamento().getNumeroRegistro()))));
                    }

                    prescritor.setUfCRM(item.getDispensacaoMedicamento().getUfConselhoRegional());
                }

                registro.setEstabelecimento(estabelecimento);
                registro.setProduto(produtoDispensacao);
                registro.setPaciente(paciente);
                registro.setPrescritor(prescritor);
                dispensacao.getRegistro().add(registro);

                /**
                 * Cria vinculo do movimento com o processo
                 */
                item.getDispensacaoItemLote().setSincronizacaoHorusProcessoEnvio(dto.getSincronizacaoHorusProcessoEnvio());
                getSession().saveOrUpdate(item.getDispensacaoItemLote());

                adicionarRegistroHorus(item);
            }

            if (CollectionUtils.isNotNullEmpty(dispensacao.getRegistro())) {
                Holder<String> numeroProtocoloDispensacao = new Holder<>();
                Holder<String> dataRecebimento = new Holder<>();

                URL url = new URL(dto.getEnderecoEnvioHorus());

                HorusWSService wsService = new HorusWSService(url);
                HorusWSAsync ws = wsService.getHorusWSAsyncPort();


                /**
                 * Como são as credencias de acesso do web service?
                 * As credenciais de acesso dos sistemas externos deverão ser fornecidas seguindo o padrão HTTP BASIC que consiste em envio do header HTTP "Authorization” construído da seguinte forma:
                 * 1 - O usuário e a senha devem ser combinados pelo caractere “:”, ex: joao.silva:123456
                 * 2 - A string resultante é codificada na base 64, ex: am9hby5zaWx2YToxMjM0NTY=
                 * 3 - O header “Authorization” deverá ser atribuído com o método “Basic”, um espaço em branco e a string codificada, ex: Authorization: Basic am9hby5zaWx2YToxMjM0NTY=
                 */
                Map<String, List<String>> credentials = new HashMap<>();
                credentials.put("Authorization", Collections.singletonList("Basic " + new String(Base64.encode((dto.getUsuario() + ":" + dto.getSenha()).getBytes()))));

                ((BindingProvider) ws).getRequestContext().put(MessageContext.HTTP_REQUEST_HEADERS, credentials);

                ws.informarDispensacaoMedicamentoEmLote(identificacao, dispensacao.getRegistro(), numeroProtocoloDispensacao, dataRecebimento);

                //                Utillizado par auditoria
                convertListToJSON(dispensacao.getRegistro(), InformarDispensacaoMedicamentoEmLoteType.Registro.class);

                dto.getSincronizacaoHorusProcessoEnvio().setStatus(SincronizacaoHorusProcessoEnvio.Status.CONCLUIDO.value());

                RespostaProcessamentoLoteType respostaProcessamentoLoteType = HorusHelper.consultarStatusProcessamento(url, dto.getUsuario(), dto.getSenha(), numeroProtocoloDispensacao.value, dataRecebimento.value);

                if (numeroProtocoloDispensacao != null && numeroProtocoloDispensacao.value != null) {
                    dto.getSincronizacaoHorusProcessoEnvio().setNumeroProtocoloRecebimento(numeroProtocoloDispensacao.value);
                }

                if (dataRecebimento != null && dataRecebimento.value != null) {
                    dto.getSincronizacaoHorusProcessoEnvio().setDataProtocoloRecebimento(dataRecebimento.value);
                }

                if (respostaProcessamentoLoteType == null || "AGUARDANDO".equals(respostaProcessamentoLoteType.getSituacaoProcessamento()) || "AGUARDANDO_REPROCESSAMENTO".equals(respostaProcessamentoLoteType.getSituacaoProcessamento())) {
                    dto.getSincronizacaoHorusProcessoEnvio().setStatus(SincronizacaoHorusProcessoEnvio.Status.AGUARDANDO_VALIDACAO.value());
                } else {
                    String inconsistenciaIntegracao = HorusHelper.consultarInconsistencias(url, dto.getUsuario(), dto.getSenha(), numeroProtocoloDispensacao.value, dataRecebimento.value, "DISPENSAÇÃO: ");

                    if (inconsistenciaIntegracao != null) {
                        dto.getLstInconsistencia().add(inconsistenciaIntegracao);
//                        dto.getSincronizacaoHorusProcessoEnvio().setMensagemErro(concatMensagemInconsistencia());
                        dto.getSincronizacaoHorusProcessoEnvio().setStatus(SincronizacaoHorusProcessoEnvio.Status.ERRO.value());

                        if (CollectionUtils.isNotNullEmpty(respostaProcessamentoLoteType.getRegistro())) {
                            List<Long> listDispensacaoItemLote = null;

                            if (CollectionUtils.isNotNullEmpty(respostaProcessamentoLoteType.getRegistro())) {
                                listDispensacaoItemLote = new ArrayList<>();

                                for (RespostaProcessamentoLoteType.Registro item : respostaProcessamentoLoteType.getRegistro()) {
                                    listDispensacaoItemLote.add(Coalesce.asLong(StringUtil.getDigits(item.getProduto().getValue().getCoRegistroOrigem())));
                                }
                            }

                            HorusHelper.gerarOcorrenciaProcessoHorusDispensacao(dto.getSincronizacaoHorusProcessoEnvio(), listDispensacaoItemLote);
                            dto.getSincronizacaoHorusProcessoEnvio().setStatus(SincronizacaoHorusProcessoEnvio.Status.CONCLUIDO_ERRO.value());
                        }
                    }
                }
            } else {
                atualizaLoteEnvioSemRegistro(dto.getSincronizacaoHorusProcessoEnvio());
            }

        } catch (IllegalArgumentException ex) {
            atualizarErro(ex);
        } catch (IOException ex) {
            atualizarErro(ex);
        } catch (SOAPFaultException ex) {
            atualizarErro(ex);
        } catch (DAOException ex) {
            atualizarErro(ex);
        } catch (ValidacaoException ex) {
            dto.getLstInconsistencia().add(ex.getMessage());
            throw new ValidacaoException(ex.getMessage(), ex);
        } catch (InconsistenciaFault ex) {
            atualizarErro(ex);
        } catch (Exception ex) {
            dto.getLstInconsistencia().add(ex.getMessage());
            throw new ValidacaoException(Bundle.getStringApplication("msg_erro_inesperado_na_comunicacao_webservice_horus"), ex);
        }
    }

    private void adicionarRegistroHorus(QueryXmlHorusDispensacaoDTO item) throws ValidacaoException, DAOException {
        registroHorusProcesso = new RegistroHorusProcesso();
        registroHorusProcesso.setTipoSincronizacao(SincronizacaoHorusProcesso.TipoSincronizacao.DISPENSASAO.value());
        registroHorusProcesso.setNuProduto(item.getProduto().getTipoProdutoCatmat().concat(item.getProduto().getMedicamentoCatmat().getCatmat()));
        registroHorusProcesso.setQuantidade(item.getDispensacaoMedicamentoItem().getQuantidadeDispensada());
        registroHorusProcesso.setCompetencia(DataUtil.adjustDateToCompetencia(item.getDispensacaoMedicamento().getDataDispensacao()));
        registroHorusProcesso.setDataRegistro(item.getDispensacaoMedicamento().getDataDispensacao());
        registroHorusProcesso.setDataValidade(item.getGrupoEstoque().getDataValidade());
        registroHorusProcesso.setLote(item.getGrupoEstoque().getId().getGrupo());
        UsuarioCadsus usuarioCadsus = (UsuarioCadsus) getSession().get(UsuarioCadsus.class, item.getUsuarioCadsus().getCodigo());
        registroHorusProcesso.setUsuarioCadsus(usuarioCadsus);
        Produto produto = (Produto) getSession().get(Produto.class, item.getProduto().getCodigo());
        registroHorusProcesso.setProduto(produto);
        Empresa empresa = null;
        if(Objects.nonNull(item.getEmpresa())) {
            empresa = (Empresa) getSession().get(Empresa.class, item.getEmpresa().getCodigo());
        }
        registroHorusProcesso.setEmpresa(empresa);
        registroHorusProcesso.setSincronizacaoHorusProcesso(dto.getSincronizacaoHorusProcesso());
        BOFactory.newTransactionSave(registroHorusProcesso);
    }

    private void atualizarErro(Exception ex) throws DAOException {
        dto.getLstInconsistencia().add(ex.getMessage());
        throw new DAOException(ex);
    }

    private void atualizaLoteEnvioSemRegistro(SincronizacaoHorusProcessoEnvio sincronizacaoHorusProcessoEnvio) {
        sincronizacaoHorusProcessoEnvio.setStatus(SincronizacaoHorusProcessoEnvio.Status.SEM_REGISTRO.value());
        Loggable.log.warn(Bundle.getStringApplication("msg_nao_existem_itens_para_exportar"));
    }

    private void addInconsistenciaEnvio(String str) {
        if (!dto.getLstInconsistencia().contains(str)) {
            dto.getLstInconsistencia().add(str);
        }
    }

    private String getMensagemInconsistenciaEnvioLote() {
        String mensagem = "";
        if (CollectionUtils.isNotNullEmpty(dto.getLstInconsistencia())) {
            mensagem = Lambda.join(dto.getLstInconsistencia(), "\n");
        }
        return mensagem;
    }

    public ProcessarEnvioHorusDTO getDto() {
        return this.dto;
    }

    private String convertListToJSON(List list, Class typeList) {
        String json = null;

        if (br.com.ksisolucoes.util.CollectionUtils.isNotNullEmpty(list)) {

            try {
                ObjectMapper objectMapper = new ObjectMapper();

                CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, typeList);

                json = objectMapper
                        .writerWithType(collectionType)
                        .writeValueAsString(list);

            } catch ( IOException e) {
                Loggable.log.error(e);
            }

        }

        return json;
    }
}
