package br.com.celk.bo.materiais.bnafar.dispensacao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.bnafar.dispensacao.BnafarDispensacao;
import br.com.ksisolucoes.vo.materiais.bnafar.interfaces.BnafarHelper;
import org.hibernate.Query;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Prado
 */
public class QueryBnafarDispensacaoStatusGerado extends CommandQuery<QueryBnafarDispensacaoStatusGerado> {
    private List<BnafarDispensacao> result;
    private Date dateInicioIntegracaoBnafar;
    private int limit;

    public QueryBnafarDispensacaoStatusGerado(Date dateInicioIntegracaoBnafar, int limit) {
        this.dateInicioIntegracaoBnafar = dateInicioIntegracaoBnafar;
        this.limit = limit;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(BnafarDispensacao.class.getName());
        hql.addToSelect("bd.codigo", "codigo");
        hql.addToSelect("dm.numeroRegistro", "dispensacaoMedicamento.numeroRegistro");

        //Estabelecimento
        hql.addToSelect("em.codigo", "empresa.codigo");
        hql.addToSelect("em.descricao", "empresa.descricao");
        hql.addToSelect("em.cnes", "empresa.cnes");

        //Caracterizacao
        hql.addToSelect("dm.dataDispensacao", "dispensacaoMedicamento.dataDispensacao");
        hql.addToSelect("bd.dataDispensacao", "dataDispensacao");

        //UsuarioCadsus
        hql.addToSelect("uc.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("bd.numeroCns", "numeroCns");
        hql.addToSelect("uc.cpf", "usuarioCadsus.cpf");
        hql.addToSelect("bd.alturaUsuarioCadsus", "alturaUsuarioCadsus");
        hql.addToSelect("bd.pesoUsuarioCadsus", "pesoUsuarioCadsus");

        //Itens
        hql.addToSelect("pr.codigo", "produto.codigo");
        hql.addToSelect("pr.tipoProdutoCatmat", "produto.tipoProdutoCatmat");
        hql.addToSelect("pr.codigoDcb", "produto.codigoDcb");
        hql.addToSelect("pr.flagExportaHorus", "produto.flagExportaHorus");
        hql.addToSelect("mc.tipoCatmat", "medicamentoCatmat.tipoCatmat");
        hql.addToSelect("mc.catmat", "medicamentoCatmat.catmat");
        hql.addToSelect("mc.descricao", "medicamentoCatmat.descricao");
        hql.addToSelect("bd.grupoEstoque", "grupoEstoque");
        hql.addToSelect("bd.dataValidade", "dataValidade");
        hql.addToSelect("bd.cnpjFabricante", "cnpjFabricante");
        hql.addToSelect("bd.descricaoFabricante", "descricaoFabricante");
        hql.addToSelect("bd.quantidade", "quantidade");
        hql.addToSelect("f.cnpj", "fabricante.cnpj");
        hql.addToSelect("f.descricao", "fabricante.descricao");
        hql.addToSelect("bd.codigoCid", "codigoCid");

        //ProfissionalPrescritor
        hql.addToSelect("p.profissionalIdCnes", "profissional.profissionalIdCnes");
        hql.addToSelect("p.codigoCns", "profissional.codigoCns");
        hql.addToSelect("p.cpf", "profissional.cpf");
        hql.addToSelect("p.numeroRegistro", "profissional.numeroRegistro");
        hql.addToSelect("p.unidadeFederacaoConselhoRegistro", "profissional.unidadeFederacaoConselhoRegistro");

        //ProfissionalSemVinculo
        hql.addToSelect("psv.numeroConselhoClasse", "dispensacaoMedicamento.profissionalSemVinculo.numeroConselhoClasse");
        hql.addToSelect("psv.ufConselho", "dispensacaoMedicamento.profissionalSemVinculo.ufConselho");

        //profissionalDispensador
        hql.addToSelect("usuarioDispensador.codigo", "usuarioDispensador.codigo");
        hql.addToSelect("profDispensador.codigo", "usuarioDispensador.profissional.codigo");
        hql.addToSelect("profDispensador.codigoCns", "usuarioDispensador.profissional.codigoCns");
        hql.addToSelect("profDispensador.cpf", "usuarioDispensador.profissional.cpf");
        hql.addToSelect("profDispensador.numeroRegistro", "usuarioDispensador.profissional.numeroRegistro");
        hql.addToSelect("profDispensador.unidadeFederacaoConselhoRegistro", "usuarioDispensador.profissional.unidadeFederacaoConselhoRegistro");

        hql.addToFrom("BnafarDispensacao bd "
                + "left join bd.fabricante f "
                + "left join bd.UsuarioCadsus uc "
                + "left join bd.usuarioDispensador usuarioDispensador "
                + "left join usuarioDispensador.profissional profDispensador "
                + "left join bd.dispensacaoMedicamento dm "
                + "left join dm.profissionalSemVinculo psv "
                + "left join bd.Empresa em "
                + "left join bd.Profissional p "
                + "left join bd.Produto pr "
                + "left join bd.MedicamentoCatmat mc "
        );
        //Status Gerado ou Enviados a mais de 7 dias(casos que a mensagem por algum motivo não é consumida)
        hql.addToWhereWhithAnd("(bd.statusRegistro = :statusGerado or (bd.statusRegistro = :statusEnviado and (current_date - cast(bd.dataUltimoEnvio as date)) >= 4))");
        hql.addToWhereWhithAnd("bd.dataDispensacao >= ", dateInicioIntegracaoBnafar);
        hql.addToOrder("bd.codigo, bd.dataDispensacao");
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<BnafarDispensacao> getResult() {
        return this.result;
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameter("statusGerado", BnafarHelper.StatusRegistro.GERADO.value());
        query.setParameter("statusEnviado", BnafarHelper.StatusRegistro.ENVIADO.value());
    }

    @Override
    protected void customQuery(Query query) {
        query.setMaxResults(this.limit);
    }
}



