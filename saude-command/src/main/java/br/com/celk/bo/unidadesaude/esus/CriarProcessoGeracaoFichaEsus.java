package br.com.celk.bo.unidadesaude.esus;

import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.unidadesaude.esus.cds.interfaces.dto.RegerarFichaVacinaDTO;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.HibernateUtil;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.esus.ProcessoGeracaoFichaEsus;
import br.com.ksisolucoes.vo.esus.base.BaseProcessoGeracaoFichaEsus;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;

/**
 * <AUTHOR>
 */
public class CriarProcessoGeracaoFichaEsus extends AbstractCommandTransaction<CriarProcessoGeracaoFichaEsus> {

    private RegerarFichaVacinaDTO regerarFichaVacinaDTO;

    public CriarProcessoGeracaoFichaEsus(RegerarFichaVacinaDTO regerarFichaVacinaDTO) {
        this.regerarFichaVacinaDTO = regerarFichaVacinaDTO;
    }

    @Override
    public void execute() throws DAOException, ValidacaoException {
        Long countProcessoProcessamento = (Long) getSession().createCriteria(ProcessoGeracaoFichaEsus.class)
                .add(Restrictions.eq(BaseProcessoGeracaoFichaEsus.PROP_STATUS, ProcessoGeracaoFichaEsus.Status.PROCESSAMENTO.value()))
                .setProjection(Projections.count(BaseProcessoGeracaoFichaEsus.PROP_CODIGO)).uniqueResult();

        if (countProcessoProcessamento > 0L) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_ja_existe_atualizacao_processamento_favor_aguardar"));
        }

        ProcessoGeracaoFichaEsus processoGeracaoFichaEsus = new ProcessoGeracaoFichaEsus();
        processoGeracaoFichaEsus.setUsuario(getSessao().getUsuario());
        processoGeracaoFichaEsus.setDataProcesso(DataUtil.getDataAtual());
        processoGeracaoFichaEsus.setStatus(ProcessoGeracaoFichaEsus.Status.PROCESSAMENTO.value());
        processoGeracaoFichaEsus.setDataInicio(regerarFichaVacinaDTO.getPeriodo().getDataInicial());
        processoGeracaoFichaEsus.setDataFim(regerarFichaVacinaDTO.getPeriodo().getDataFinal());
        processoGeracaoFichaEsus.setTipoFicha(regerarFichaVacinaDTO.getTipoFicha().value());

        BOFactory.newTransactionSave(processoGeracaoFichaEsus);
        processoGeracaoFichaEsus = HibernateUtil.lockTable(ProcessoGeracaoFichaEsus.class, processoGeracaoFichaEsus.getCodigo());
        AsyncProcess asyncProcess = BOFactory.getBO(EsusFacade.class).processarFichasEsusVacinas(processoGeracaoFichaEsus, regerarFichaVacinaDTO);
        processoGeracaoFichaEsus.setAsyncProcess(asyncProcess);
        getSession().saveOrUpdate(processoGeracaoFichaEsus);
    }

}
