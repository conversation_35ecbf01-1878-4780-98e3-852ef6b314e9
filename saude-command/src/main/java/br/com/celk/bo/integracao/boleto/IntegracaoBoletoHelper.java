package br.com.celk.bo.integracao.boleto;

import br.com.celk.boleto.dto.boletocloud.request.AuthenticationDTO;
import br.com.celk.boleto.enumeration.Ambiente;
import br.com.celk.boleto.integracao.consumer.boletocloud.ConsumerAPI;
import br.com.celk.boleto.integracao.consumer.caixa.ConsumerWS;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.parametrogem.IParameterModuleContainer;

public class IntegracaoBoletoHelper {

//    private static final String CONTEXT = "/boleto/api";
//    private static final String BOLETO_RESOURCE = "/v1/boleto";
//    private static final String RETORNO_RESOURCE = "/v1/retorno";
//
//    private static final Logger log = Logger.getLogger(IntegracaoBoletoHelper.class);
//
//    private String URL;
//    private AuthenticationDTO authentication;

    public static ConsumerAPI getConsumerInstanceAPI() throws DAOException {
        IParameterModuleContainer moduloGeral = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL);

        AuthenticationDTO authentication = new AuthenticationDTO();

        String ambienteAPIParamGEM = moduloGeral.getParametro("Ambiente_BoletoAPI");
        String ambienteParamJBoss = System.getProperty("ambiente");
        if ("producao".equals(ambienteParamJBoss) && Ambiente.OFICIAL.equals(Ambiente.valueOf(ambienteAPIParamGEM))) {
            authentication.setAmbiente(Ambiente.OFICIAL);
            authentication.setTokenAPI((String) moduloGeral.getParametro("TokenAcessoProducao"));
            authentication.setPasswordAPI((String) moduloGeral.getParametro("SenhaAcessoProducao"));
        } else {
            authentication.setAmbiente(Ambiente.HOMOLOGACAO);
            authentication.setTokenAPI((String) moduloGeral.getParametro("TokenAcessoHomologacao"));
            authentication.setPasswordAPI((String) moduloGeral.getParametro("SenhaAcessoHomologacao"));
        }

        String URL = moduloGeral.getParametro("URL_BoletoAPI");

        return new ConsumerAPI(authentication, URL);
    }

    public static ConsumerWS getConsumerInstanceWS() throws DAOException {
        return new ConsumerWS();
    }

//    public static IntegracaoBoletoHelper getInstance() throws DAOException {
//        IParameterModuleContainer moduloGeral = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL);
//
//        AuthenticationDTO authentication = new AuthenticationDTO();
//
//        String ambienteAPIParamGEM = moduloGeral.getParametro("Ambiente_BoletoAPI");
//        String ambienteParamJBoss = System.getProperty("ambiente");
//        if ("producao".equals(ambienteParamJBoss) && Ambiente.OFICIAL.equals(Ambiente.valueOf(ambienteAPIParamGEM))) {
//            authentication.setAmbiente(Ambiente.OFICIAL);
//            authentication.setTokenAPI((String) moduloGeral.getParametro("TokenAcessoProducao"));
//            authentication.setPasswordAPI((String) moduloGeral.getParametro("SenhaAcessoProducao"));
//        } else {
//            authentication.setAmbiente(Ambiente.HOMOLOGACAO);
//            authentication.setTokenAPI((String) moduloGeral.getParametro("TokenAcessoHomologacao"));
//            authentication.setPasswordAPI((String) moduloGeral.getParametro("SenhaAcessoHomologacao"));
//        }
//
//        String URL = moduloGeral.getParametro("URL_BoletoAPI");
//
//        return new IntegracaoBoletoHelper(authentication, URL);
//    }
//
//    private IntegracaoBoletoHelper(AuthenticationDTO authentication, String URL) {
//        this.authentication = authentication;
//        this.URL = URL;
//    }
//
//    private ResteasyClient clientInstance() {
//        ResteasyClient client = new ResteasyClientBuilder()
//                .establishConnectionTimeout(5, TimeUnit.SECONDS)
//                .socketTimeout(30, TimeUnit.SECONDS)
//                .build();
//        return client;
//    }
//
//    private ResteasyWebTarget targetInstance(String resource, String requestPath) throws DAOException {
//
//        String uri = URL.concat(CONTEXT)
//                .concat(resource);
//
//        ResteasyWebTarget target = clientInstance().target(uri).path(requestPath);
//
//        log.info("Client pronto para realizar a requisição ao endpoint " + target.getUri());
//
//        return target;
//
//    }
//
//    public BoletoDTO consultar(String token) throws DAOException, ValidacaoException {
//        ConsultarBoletoDTO dto = new ConsultarBoletoDTO();
//        dto.setAuthentication(this.authentication);
//        dto.setTokenBoleto(token);
//
//        ResteasyWebTarget target = targetInstance(BOLETO_RESOURCE, "/consultar");
//
//        Response response = buildResponse(target, dto);
//
//        BoletoDTO boletoDTO = saveBankSlipFromResponse(response);
//
//        return boletoDTO;
//    }
//
//    public BoletoDTO gerar(DadosGeracaoBoletoDTO dadosGeracaoBoletoDTO) throws DAOException, ValidacaoException {
//        GeracaoBoletoDTO dto = new GeracaoBoletoDTO();
//        dto.setAuthentication(this.authentication);
//        dto.setDadosGeracaoBoleto(dadosGeracaoBoletoDTO);
//
//        ResteasyWebTarget target = targetInstance(BOLETO_RESOURCE, "/gerar");
//
//        Response response = buildResponse(target, dto);
//
//        BoletoDTO boletoDTO = saveBankSlipFromResponse(response);
//
//        return boletoDTO;
//    }
//
//    public void cancelar(String tokenBoleto, String motivo) throws DAOException, ValidacaoException {
//        CancelarBoletoDTO dto = new CancelarBoletoDTO();
//        dto.setAuthentication(this.authentication);
//        dto.setMotivo(motivo);
//        dto.setTokenBoleto(tokenBoleto);
//
//        ResteasyWebTarget target = targetInstance(BOLETO_RESOURCE, "/cancelar");
//
//        Response response = buildResponse(target, dto);
//
//        if (response.getStatus() != Response.Status.OK.getStatusCode()) {
//            throwingExceptionsFromResponse(response);
//        }
//    }
//
//    public List<RetornoBoletosRemessaDTO> consultarRetornoRemessa(DadosConsultaRetornoRemessaDTO dadosConsultaRetornoRemessaDTO) throws DAOException, ValidacaoException {
//        ConsultarRetornoRemessaDTO dto = new ConsultarRetornoRemessaDTO();
//        dto.setAuthentication(this.authentication);
//        dto.setDadosConsultaRetorno(dadosConsultaRetornoRemessaDTO);
//
//        ResteasyWebTarget target = targetInstance(RETORNO_RESOURCE, "/remessa");
//
//        Response response = buildResponse(target, dto);
//
//        if (response.getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
//            return Collections.EMPTY_LIST;
//        }
//
//        if (response.getStatus() != Response.Status.OK.getStatusCode()) {
//            throwingExceptionsFromResponse(response);
//        }
//
//        log.info("Convertendo a resposta da requisição...");
//
//        List<RetornoBoletosRemessaDTO> retornoBoletosRemessa = response.readEntity(
//                new GenericType<List<RetornoBoletosRemessaDTO>>() {
//                }
//        );
//
//        return retornoBoletosRemessa;
//    }
//
//    private Response buildResponse(ResteasyWebTarget target, Serializable dto) {
//
//        Response response = target
//                .request(MediaType.WILDCARD)
//                .post(Entity.entity(dto, MediaType.APPLICATION_JSON_TYPE));
//
//        return response;
//
//    }
//
//    private BoletoDTO saveBankSlipFromResponse(Response response) throws DAOException, ValidacaoException {
//
//        if (response.getStatus() != Response.Status.OK.getStatusCode()) {
//            throwingExceptionsFromResponse(response);
//        }
//
//
//        File file = null;
//        String token = null;
//        InputStream inputStream = null;
//
//        try {
//
//                token = response.getHeaderString("x-token-boleto");
//
//            log.info("Convertendo a resposta da requisição...");
//
//            inputStream = response.readEntity(InputStream.class);
//
//            log.info("Salvando o Boleto em disco...");
//
//            Path path = Paths.get(System.getProperty("java.io.tmpdir"), token);
//
//            Files.copy(inputStream, path, StandardCopyOption.REPLACE_EXISTING);
//
//            file = path.toFile();
//
//            log.info("Boleto salvo em " + path.toString());
//
//        } catch (Throwable ex) {
//
//            log.trace(ex.getMessage(), ex);
//
//            if (token == null) {
//                throw new DAOException(ex);
//            }
//
//        } finally {
//
//            IOUtils.closeQuietly(inputStream);
//
//            BoletoDTO boletoDTO = new BoletoDTO();
//            boletoDTO.setBoleto(file);
//            boletoDTO.setToken(token);
//
//            return boletoDTO;
//
//        }
//    }
//
//    private void throwingExceptionsFromResponse(Response response) throws DAOException {
//
//        log.error("O serviço retornou a seguinte resposta: (" + response.getStatusInfo().getStatusCode() + ") " + response.getStatusInfo().getReasonPhrase());
//
//        ExceptionDTO errorDTO = response.readEntity(ExceptionDTO.class);
//
//        List<String> messages = Lambda.extract(errorDTO.getCausas(), Lambda.on(CauseDTO.class).getMensagem());
//
//        String exceptions = Lambda.join(messages, "\n");
//
//        throw new DAOException(exceptions);
//
//    }
//
//    public AuthenticationDTO getAuthentication() {
//        return authentication;
//    }
}
