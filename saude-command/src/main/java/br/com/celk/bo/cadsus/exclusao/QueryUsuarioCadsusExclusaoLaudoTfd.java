package br.com.celk.bo.cadsus.exclusao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioCadsusExclusaoLaudoTfd extends CommandQuery {

    private final List<Long> codigoProcessoList;
    private List<LaudoTfd> list;

    public QueryUsuarioCadsusExclusaoLaudoTfd(List<Long> codigoProcessoList) {
        this.codigoProcessoList = codigoProcessoList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(LaudoTfd.class.getName());
        
        hql.addToSelect("lt.codigo", "codigo");
        hql.addToSelect("lt.dataLaudo", "dataLaudo");
        hql.addToSelect("lt.dataCadastro", "dataCadastro");
        hql.addToSelect("lt.complemento", "complemento");
        hql.addToSelect("lt.status", "status");
        hql.addToSelect("lt.caraterAtendimento", "caraterAtendimento");
        hql.addToSelect("lt.historicoDoenca", "historicoDoenca");
        hql.addToSelect("lt.version", "version");
        
        hql.addToSelect("e.codigo", "empresa.codigo");
        hql.addToSelect("e.descricao", "empresa.descricao");
        
        hql.addToSelect("pr.codigo", "profissional.codigo");
        hql.addToSelect("pr.nome", "profissional.nome");
        
        hql.addToSelect("tp.codigo", "tipoProcedimento.codigo");
        hql.addToSelect("tp.descricao", "tipoProcedimento.descricao");
        
        hql.addToSelect("u.codigo", "usuarioCadsus.codigo");
        hql.addToSelect("u.nome", "usuarioCadsus.nome");
        hql.addToSelect("u.version", "usuarioCadsus.version");
        
        hql.addToSelect("p.codigo", "procedimento.codigo");
        hql.addToSelect("p.descricao", "procedimento.descricao");
        
        hql.addToSelect("pt.codigo", "pedidoTfd.codigo");
        hql.addToSelect("pt.tipoTfd", "pedidoTfd.tipoTfd");
        
        hql.addToSelect("sa.codigo", "pedidoTfd.solicitacaoAgendamento.codigo");
        hql.addToSelect("sa.dataAgendamento", "pedidoTfd.solicitacaoAgendamento.dataAgendamento");
        
        hql.addToSelect("c.codigo", "cid.codigo");
        hql.addToSelect("c.descricao", "cid.descricao");
        
        hql.addToFrom("LaudoTfd lt "
                + " left join lt.tipoProcedimento tp"
                + " left join lt.procedimento p"
                + " left join lt.profissional pr"
                + " left join lt.cid c"
                + " left join lt.empresa e"
                + " left join lt.usuarioCadsus u"
                + " left join lt.atendimento a"
                + " left join lt.pedidoTfd pt"
                + " left join pt.solicitacaoAgendamento sa"
        );

        hql.addToWhereWhithAnd("lt.codigo in :codigoProcessoList");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoProcessoList", codigoProcessoList);        
    }
    
    @Override
    protected void result(HQLHelper hql, Object result) {
        this.list = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    @Override
    public List<LaudoTfd> getResult() {
        return this.list;
    }
    
}