package br.com.celk.bo.emprestimo.devolucaoemprestimo;

import br.com.celk.bo.emprestimo.interfaces.dto.CadastroRegistroDevolucaoEmprestimoDTO;
import br.com.celk.bo.emprestimo.interfaces.dto.DevolucaoEmprestimoItensEloDTO;
import br.com.celk.bo.emprestimo.interfaces.facade.EmprestimoFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.MovimentoEstoqueFacade;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem;
import br.com.ksisolucoes.vo.emprestimo.TipoDevolucao;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;

/**
 *
 * <AUTHOR>
 */
public class SalvarRegistroDevolucaoEmprestimo extends AbstractCommandTransaction {

    private CadastroRegistroDevolucaoEmprestimoDTO dto;
    private TipoDocumento tipoDocumento;
    private TipoDocumento tipoDocumentoEstorno;
    private DevolucaoEmprestimo devolucaoEmprestimo;

    public SalvarRegistroDevolucaoEmprestimo(CadastroRegistroDevolucaoEmprestimoDTO dto) {
        this.dto = dto;
    }
        
    @Override
    public void execute() throws DAOException, ValidacaoException {
        
        // Salvar DevolucaoEmprestimo
        if(TipoDevolucao.TipoSignatario.ESTABELECIMENTO.value().equals(dto.getDevolucaoEmprestimo().getTipoDevolucao().getTipoSignatario())){
            dto.getDevolucaoEmprestimo().setNomePacienteEstabelecimento(dto.getDevolucaoEmprestimo().getEmpresaDevolucao().getDescricao());
        } else if(TipoDevolucao.TipoSignatario.PACIENTE.value().equals(dto.getDevolucaoEmprestimo().getTipoDevolucao().getTipoSignatario())){
            dto.getDevolucaoEmprestimo().setNomePacienteEstabelecimento(dto.getDevolucaoEmprestimo().getUsuarioCadsus().getNomeSocial());
        }
        
        devolucaoEmprestimo = BOFactory.save(dto.getDevolucaoEmprestimo());
        
        // Salvar DevolucaoEmprestimoItem Cancelados
        for(DevolucaoEmprestimoItensEloDTO item : dto.getDevolucaoEmprestimoItemEloCanceladosList()){
            DevolucaoEmprestimoItem devolucaoEmprestimoItem = BOFactory.save(item.getDevolucaoEmprestimoItem());
            
            gerarMovimentoEstoque(item, true);
            
            BOFactory.getBO(EmprestimoFacade.class).cancelarDevolucaoEmprestimoElo(devolucaoEmprestimoItem, item.getDevolucaoEmprestimoEloList());
        }
        
        // Salvar DevolucaoEmprestimoItem
        for(DevolucaoEmprestimoItensEloDTO item : dto.getDevolucaoEmprestimoItemEloAdicionadosList()){
            if(item.getDevolucaoEmprestimoItem().getDevolucaoEmprestimo() == null){
                item.getDevolucaoEmprestimoItem().setDevolucaoEmprestimo(devolucaoEmprestimo);
            }
            DevolucaoEmprestimoItem devolucaoEmprestimoItem = BOFactory.save(item.getDevolucaoEmprestimoItem());
            
            gerarMovimentoEstoque(item, false);
            
            BOFactory.getBO(EmprestimoFacade.class).salvarDevolucaoEmprestimoElo(devolucaoEmprestimoItem, item.getDevolucaoEmprestimoEloList());
        }
    }
    
    private void gerarMovimentoEstoque(DevolucaoEmprestimoItensEloDTO devolucaoEmprestimoItensEloDTO, boolean estorno) throws DAOException, ValidacaoException{
        DevolucaoEmprestimoItem devolucaoEmprestimoItem = devolucaoEmprestimoItensEloDTO.getDevolucaoEmprestimoItem();
        MovimentoEstoque me = new MovimentoEstoque();
            
        if(estorno){
            me.setTipoDocumento(getTipoDocumentoEstorno());
        } else {
            me.setTipoDocumento(getTipoDocumento());            
        }
        me.setNumeroDocumento(devolucaoEmprestimo.getCodigo().toString());
        me.setProduto(devolucaoEmprestimoItem.getProduto());
        me.setQuantidade(devolucaoEmprestimoItem.getQuantidade());
        me.setGrupoEstoque(devolucaoEmprestimoItem.getGrupoEstoque());
        me.setDataValidadeGrupoEstoque(devolucaoEmprestimoItem.getDataValidade());

        BOFactory.getBO(MovimentoEstoqueFacade.class).gerarMovimentoEstoque(me);
    }
    
    private TipoDocumento getTipoDocumento() throws DAOException{
        if(this.tipoDocumento == null){
            TipoDevolucao tipoDevolucao = (TipoDevolucao) getSession().get(TipoDevolucao.class, dto.getDevolucaoEmprestimo().getTipoDevolucao().getCodigo());
            this.tipoDocumento = tipoDevolucao.getTipoDocumentoEstoque();

            return this.tipoDocumento;
        } else {
            return this.tipoDocumento;
        }
    }
    
    private TipoDocumento getTipoDocumentoEstorno() throws DAOException{
        if(this.tipoDocumentoEstorno == null){
            TipoDevolucao tipoDevolucao = (TipoDevolucao) getSession().get(TipoDevolucao.class, dto.getDevolucaoEmprestimo().getTipoDevolucao().getCodigo());
            this.tipoDocumentoEstorno = tipoDevolucao.getTipoDocumentoCancelamento();

            return this.tipoDocumentoEstorno;
        } else {
            return this.tipoDocumentoEstorno;
        }
    }

    public DevolucaoEmprestimo getDevolucaoEmprestimo() {
        return devolucaoEmprestimo;
    }

}