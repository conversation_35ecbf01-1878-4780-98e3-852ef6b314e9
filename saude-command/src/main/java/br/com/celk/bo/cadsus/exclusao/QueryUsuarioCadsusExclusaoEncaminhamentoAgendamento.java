package br.com.celk.bo.cadsus.exclusao;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.EncaminhamentoAgendamento;
import java.util.List;
import java.util.Map;
import org.hibernate.Query;

/**
 *
 * <AUTHOR>
 */
public class QueryUsuarioCadsusExclusaoEncaminhamentoAgendamento extends CommandQuery {

    private final List<Long> codigoProcessoList;
    private List<EncaminhamentoAgendamento> result;

    public QueryUsuarioCadsusExclusaoEncaminhamentoAgendamento(List<Long> codigoProcessoList) {
        this.codigoProcessoList = codigoProcessoList;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(EncaminhamentoAgendamento.class.getName());

        hql.addToSelect("encaminhamentoAgendamento.codigo", "codigo");
        hql.addToSelect("encaminhamentoAgendamento.dataAgendamento", "dataAgendamento");
        hql.addToSelect("encaminhamentoAgendamento.version", "version");
        
        hql.addToSelect("profissional.nome", "profissional.nome");
        
        hql.addToSelect("tipoEncaminhamento.descricao", "encaminhamento.tipoEncaminhamento.descricao");

        hql.addToSelect("localAgendamento.descricao", "localAgendamento.descricao");

        hql.addToSelect("encaminhamento.codigo", "encaminhamento.codigo");
        hql.addToSelect("encaminhamento.dataInicioAgendamento", "encaminhamento.dataInicioAgendamento");
        hql.addToSelect("encaminhamento.status", "encaminhamento.status");
        hql.addToSelect("encaminhamento.version", "encaminhamento.version");
        
        hql.addToSelect("encaminhamentoAgendamento.codigo", "encaminhamento.encaminhamentoAgendamento.codigo");
        hql.addToSelect("encaminhamentoAgendamento.dataAgendamento", "encaminhamento.encaminhamentoAgendamento.dataAgendamento");
        hql.addToSelect("encaminhamentoAgendamento.version", "encaminhamento.encaminhamentoAgendamento.version");
        
        hql.addToSelect("usuarioCadsus.codigo", "encaminhamento.usuarioCadsus.codigo");
        hql.addToSelect("usuarioCadsus.nome", "encaminhamento.usuarioCadsus.nome");

        hql.addToFrom("EncaminhamentoAgendamento encaminhamentoAgendamento"
                + " left join encaminhamentoAgendamento.encaminhamento encaminhamento"
                + " left join encaminhamento.tipoEncaminhamento tipoEncaminhamento"
                + " left join encaminhamento.usuarioCadsus usuarioCadsus"
                + " left join encaminhamentoAgendamento.profissional profissional"
                + " left join encaminhamentoAgendamento.localAgendamento localAgendamento"
        );
        hql.addToWhereWhithAnd("encaminhamentoAgendamento.codigo in :codigoProcessoList");
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) throws ValidacaoException, DAOException {
        query.setParameterList("codigoProcessoList", codigoProcessoList);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result);
    }

    @Override
    public List<EncaminhamentoAgendamento> getResult() {
        return this.result;
    }
}
