package br.com.celk.report.vigilancia.roteiroinspecao;

import br.com.celk.report.vigilancia.roteiroinspecao.query.QueryImpressaoRoteiroInspecao;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.OrgaoEmissor;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecaoFiscais;
import org.hibernate.Session;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ImpressaoRoteiroInspecao extends AbstractReport<RegistroInspecao> {

    public ImpressaoRoteiroInspecao(RegistroInspecao param) {
        super(param);
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryImpressaoRoteiroInspecao() {
            @Override
            public void useSession(Session session) {
                List<RegistroInspecaoFiscais> fiscais = LoadManager.getInstance(RegistroInspecaoFiscais.class)
                        .addProperties(new HQLProperties(RegistroInspecaoFiscais.class).getProperties())
                        .addProperties(new HQLProperties(Profissional.class, RegistroInspecaoFiscais.PROP_PROFISSIONAL).getProperties())
                        .addProperties(new HQLProperties(OrgaoEmissor.class, VOUtils.montarPath(RegistroInspecaoFiscais.PROP_PROFISSIONAL, Profissional.PROP_CONSELHO_CLASSE)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(RegistroInspecaoFiscais.PROP_REGISTRO_INSPECAO, ImpressaoRoteiroInspecao.this.param))
                        .start().getList();

                if (fiscais.isEmpty()) {
                    fiscais = Arrays.asList(new RegistroInspecaoFiscais(), new RegistroInspecaoFiscais());
                }

                addParametro("fiscais", fiscais);
            }
        };
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/roteiroinspecao/jrxml/roteiro_inspecao.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_roteiro_inspecao");
    }

}
