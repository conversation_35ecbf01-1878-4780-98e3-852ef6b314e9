package br.com.celk.report.vigilancia.denuncia;

import br.com.celk.report.vigilancia.query.QueryImpressaoTermoDenuncia;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia;

public class RelatorioImpressaoTermoDenuncia extends AbstractReport<Denuncia> {

    public RelatorioImpressaoTermoDenuncia(Denuncia param) {
        super(param);
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/jrxml/termo_denuncia.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_termo_denuncia_reclamacao");
    }

    @Override
    public ITransferDataReport getQuery() {
        return new QueryImpressaoTermoDenuncia();
    }

}
