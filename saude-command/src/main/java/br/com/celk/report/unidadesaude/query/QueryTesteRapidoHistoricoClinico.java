package br.com.celk.report.unidadesaude.query;

import br.com.celk.atendimento.prontuario.interfaces.dto.ProntuariosDemmandPaggingDTOParam;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.prontuario.basico.atendimento.AtendimentoHelper;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import ch.lambdaj.Lambda;
import org.hamcrest.Matchers;
import org.hibernate.Query;
import org.hibernate.Session;

import java.util.List;
import java.util.Map;

import static ch.lambdaj.Lambda.on;

public class QueryTesteRapidoHistoricoClinico extends CommandQuery<ProntuariosDemmandPaggingDTOParam> {

    private ProntuariosDemmandPaggingDTOParam param;
    private List<TesteRapidoRealizado> testesRapidosRealizados;

    public QueryTesteRapidoHistoricoClinico(ProntuariosDemmandPaggingDTOParam param) {
        this.param = param;
    }

    @Override
    protected void createQuery(HQLHelper hql) {
        hql.setTypeSelect(TesteRapidoRealizado.class.getName());

        hql.addToSelect("testeRapidoRealizado.codigo", "codigo");
        hql.addToSelect("testeRapidoRealizado.resultado", "resultado");
        hql.addToSelect("testeRapidoRealizado.status", "status");
        hql.addToSelect("testeRapidoRealizado.descricaoCancelamento", "descricaoCancelamento");
        hql.addToSelect("atendimentoResultado.codigo", "atendimentoResultado.codigo");
        hql.addToSelect("tipoTesteRapido.codigo", "tipoTesteRapido.codigo");
        hql.addToSelect("tipoTesteRapido.descricao", "tipoTesteRapido.descricao");
        hql.addToSelect("tipoTesteRapido.tipoTeste", "tipoTesteRapido.tipoTeste");
        hql.addToSelect("testeRapido.codigo", "testeRapido.codigo");
        hql.addToSelect("atendimento.codigo", "testeRapido.atendimento.codigo");
        hql.addToSelect("atendimento.dataAtendimento", "testeRapido.atendimento.dataAtendimento");
        hql.addToSelect("empresa.codigo", "testeRapido.atendimento.empresa.codigo");
        hql.addToSelect("empresa.descricao", "testeRapido.atendimento.empresa.descricao");
        hql.addToSelect("profissional.codigo", "testeRapido.atendimento.profissional.codigo");
        hql.addToSelect("profissional.nome", "testeRapido.atendimento.profissional.nome");
        hql.addToSelect("atendimentoPrincipal.codigo", "testeRapido.atendimento.atendimentoPrincipal.codigo");
        hql.addToSelect("naturezaProcura.descricao", "testeRapido.atendimento.naturezaProcuraTipoAtendimento.naturezaProcura.descricao");
        hql.addToSelect("tipoAtendimento.codigo", "testeRapido.atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.codigo");
        hql.addToSelect("tipoAtendimento.descricao", "testeRapido.atendimento.naturezaProcuraTipoAtendimento.tipoAtendimento.descricao");

        hql.addToFrom("TesteRapidoRealizado testeRapidoRealizado"
                    + " left join testeRapidoRealizado.atendimentoResultado atendimentoResultado"
                    + " left join testeRapidoRealizado.tipoTesteRapido tipoTesteRapido"
                    + " left join testeRapidoRealizado.testeRapido testeRapido"
                    + " left join testeRapido.atendimento atendimento"
                    + " left join atendimento.empresa empresa"
                    + " left join atendimento.usuarioCadsus usuarioCadsus"
                    + " left join atendimento.profissional profissional"
                    + " left join atendimento.atendimentoPrincipal atendimentoPrincipal"
                    + " left join atendimento.naturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento"
                    + " left join naturezaProcuraTipoAtendimento.naturezaProcura naturezaProcura"
                    + " left join naturezaProcuraTipoAtendimento.tipoAtendimento tipoAtendimento"
        );

        hql.addToWhereWhithAnd("usuarioCadsus.codigo = ", param.getAtendimento().getUsuarioCadsus().getCodigo());

        if (param.isMeusAtendimentos() && param.getAtendimento() != null && param.getAtendimento().getProfissional() != null && param.getAtendimento().getProfissional().getCodigo() != null) {
            hql.addToWhereWhithAnd("profissional.codigo = ", param.getAtendimento().getProfissional().getCodigo());
        }
        if (param.getTipoAtendimento() != null) {
            hql.addToWhereWhithAnd("tipoAtendimento.codigo = ", param.getTipoAtendimento().getCodigo());
        }
        if (param.getPeriodo() != null || (param.getDataInicialBuscaGeral() != null && param.getDataFinalBuscaGeral() != null)) {
            hql.addToWhereWhithAnd("(atendimento.dataAtendimento >= :dataInicial and atendimento.dataAtendimento <= :dataFinal)");
        }
        if (Coalesce.asString(param.getDescricao()).trim().length() > 0) {
            hql.addToWhereWhithAnd("( testeRapidoRealizado.resultado = :valueResultado" +
                    " OR " + hql.getConsultaLiked("testeRapidoRealizado.descricaoCancelamento", param.getDescricao()) + ")");
        }

        hql.addToOrder("atendimento.dataAtendimento desc");
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        if (param.getGrupoAtendimentoCbo() != null) {
            List<EloTipoAtendimentoGrupoAtendimentoCbo> eloTipoAtendimentoGrupoAtendimentoCbos = AtendimentoHelper.getEloTipoAtendimentoGrupoAtendimentoCbos(param);
            List<Long> codigosTipoAtendimento = Lambda.extract(eloTipoAtendimentoGrupoAtendimentoCbos, Lambda.on(EloTipoAtendimentoGrupoAtendimentoCbo.class).getTipoAtendimento().getCodigo());

            testesRapidosRealizados =
                    Lambda.filter(
                            Lambda.having(
                                    on(TesteRapidoRealizado.class).getTesteRapido().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getCodigo(),
                                    Matchers.isIn(codigosTipoAtendimento)
                            ),
                            testesRapidosRealizados
                    );
        }
    }

    @Override

    protected void customQuery(Query query) {
        if (AtendimentoProntuario.TipoRegistro.TESTE_RAPIDO.value().equals(param.getTipoRegistroProntuario()) || param.getTipoRegistroProntuario() == null){
            super.customQuery(query.setFirstResult(param.getStartFirst()).setMaxResults(param.getMaxResultsLoading()));
        }
    }

    @Override
    protected void setParameters(HQLHelper hql, Query query) {
        if (param.getPeriodo() != null) {
            DatePeriod datePeriod = Data.adjustRangeHour(new DatePeriod(Data.removeMeses(DataUtil.getDataAtual(), param.getPeriodo()), DataUtil.getDataAtual()));
            query.setParameter("dataInicial", datePeriod.getDataInicial());
            query.setParameter("dataFinal", datePeriod.getDataFinal());
        } else if (param.getDataInicialBuscaGeral() != null && param.getDataFinalBuscaGeral() != null) {
            DatePeriod datePeriod = Data.adjustRangeHour(new DatePeriod(param.getDataInicialBuscaGeral(), (param.getStartFirst() == 0) ? DataUtil.getDataAtual() : param.getDataFinalBuscaGeral()));
            query.setParameter("dataInicial", datePeriod.getDataInicial());
            query.setParameter("dataFinal", datePeriod.getDataFinal());
        }

        if (param.getDescricao() != null) {
            TesteRapidoRealizado.Resultado resultado = TesteRapidoRealizado.Resultado.valeuOf(param.getDescricao());
            query.setParameter("valueResultado", resultado != null ? resultado.value() : null);
        }
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.testesRapidosRealizados = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    public List<TesteRapidoRealizado> getTestesRapidosRealizados() {
        return testesRapidosRealizados;
    }
}
