package br.com.celk.report.vigilancia.autoinfracao;

import br.com.celk.report.vigilancia.autoinfracao.query.QueryImpressaoComprovanteAutoInfracao;
import br.com.celk.util.Coalesce;
import br.com.celk.util.FiscalNaRuaUtil;
import br.com.celk.vigilancia.dto.AutoInfracaoDTO;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.QRCodeBuilderAutos;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.IOException;

/**
 * <AUTHOR>
 */
public class ImpressaoComprovanteAutoInfracao extends AbstractReport<Long> {

    Long param = null;
    String numeroFormatado = "";
    Long situacao = null;

    public ImpressaoComprovanteAutoInfracao(Long param) {
        this(param,null,null);
    }
    public ImpressaoComprovanteAutoInfracao(Long param,String numeroFormatado, Long situacao) {
        super(param);
        this.param = param;
        this.numeroFormatado = numeroFormatado;
        this.situacao = situacao;
    }

    @Override
    public ITransferDataReport getQuery() throws ValidacaoException {
        String cienciaAutoInfracao = VigilanciaHelper.getConfiguracaoVigilancia().getCienciaAutoInfracao();

        addParametro("EMPRESA_LOGADA", sessao.getEmpresa().getDescricaoFormatado());
        addParametro("EMPRESA_LOGADA_ENDERECO", sessao.getEmpresa().getEnderecoCidadeBairroFormatado());
        addParametro("CIENCIA", cienciaAutoInfracao);
        addParametro("EXIBIR_TITULO_PRIMEIRA_PAGINA", true);
        addParametro("exibirNomeResponsavel", VigilanciaHelper.exibirNomeResponsavelAuto());

        return new QueryImpressaoComprovanteAutoInfracao() {
            protected void addParam(AutoInfracaoDTO autoInfracaoDTO) {
                String realContext = TenantContext.getRealContext();
                if (realContext.equals("localhost")) {
                    realContext = realContext.concat(":8080/vigilancia");
                } else {
                    realContext = realContext.concat("/vigilancia");
                }
                autoInfracaoDTO.getAutoInfracao().setAutoIntimacao(autoInfracaoDTO.getAutoIntimacao());

                String sb = "Acesse o link " + realContext + ", clique no botão Recurso/Prorrogação e informe a chave " + autoInfracaoDTO.getAutoInfracao().getProcessoAdministrativoAutenticacao().getChave();

                addParametro("MENSAGEM_CHAVE_CONSULTA_REQUERIMENTO", sb);

                QRCodeBuilderAutos qrCodeBuilder = new QRCodeBuilderAutos();
                qrCodeBuilder.setCodigo(autoInfracaoDTO.getAutoInfracao().getCodigo());
                qrCodeBuilder.setTipo(QRCodeBuilderAutos.Tipo.INFRACAO);
                qrCodeBuilder.setChave(autoInfracaoDTO.getAutoInfracao().getProcessoAdministrativoAutenticacao().getChave());

                addParametro("urlQRCode", qrCodeBuilder.getURL());
                try{
                    addParametro("ASSINATURA_RESPONSAVEL",
                            (CollectionUtils.isNotNullEmpty(FiscalNaRuaUtil.carregarAssinaturasInfracao(autoInfracaoDTO.getAutoInfracao(), RequerimentoVigilanciaAnexo.TipoAssinatura.ASSINATURA_RESPONSAVEL))) ?
                                    FiscalNaRuaUtil.criarArquivoAssinaturasInfracao(autoInfracaoDTO.getAutoInfracao(), RequerimentoVigilanciaAnexo.TipoAssinatura.ASSINATURA_RESPONSAVEL).get(0).getFile() : null
                    );

                    addParametro("ASSINATURA_FISCAL",
                            (CollectionUtils.isNotNullEmpty(FiscalNaRuaUtil.carregarAssinaturasInfracao(autoInfracaoDTO.getAutoInfracao(), RequerimentoVigilanciaAnexo.TipoAssinatura.ASSINATURA_FISCAL))) ?
                                    FiscalNaRuaUtil.criarArquivoAssinaturasInfracao(autoInfracaoDTO.getAutoInfracao(), RequerimentoVigilanciaAnexo.TipoAssinatura.ASSINATURA_FISCAL) : null
                    );

                    addParametro("ASSINATURA_TESTEMUNHA1",
                            (CollectionUtils.isNotNullEmpty(FiscalNaRuaUtil.carregarAssinaturasInfracao(autoInfracaoDTO.getAutoInfracao(), RequerimentoVigilanciaAnexo.TipoAssinatura.ASSINATURA_TESTEMUNHA_1))) ?
                                    FiscalNaRuaUtil.criarArquivoAssinaturasInfracao(autoInfracaoDTO.getAutoInfracao(), RequerimentoVigilanciaAnexo.TipoAssinatura.ASSINATURA_TESTEMUNHA_1) : null
                    );

                } catch(IOException | ValidacaoException ignored){
                    throw new RuntimeException(ignored);
                }

            }
        };
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/vigilancia/autoinfracao/jrxml/comprovante_auto_infracao.jrxml";
    }

    @Override
    public String getTitulo() {

        String titulo = "";
        titulo += Bundle.getStringApplication("rotulo_auto_infracao");

        if (!Coalesce.asString(numeroFormatado).isEmpty()) {
            titulo = titulo.concat(" Nº " + numeroFormatado);
        }

        return titulo;
    }

}
