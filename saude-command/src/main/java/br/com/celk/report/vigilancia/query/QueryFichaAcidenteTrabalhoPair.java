package br.com.celk.report.vigilancia.query;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoDoencaTrabalhoPair;
import org.hibernate.Session;

import java.util.HashMap;
import java.util.Map;

public class QueryFichaAcidenteTrabalhoPair extends QueryFichaInvestigacaoBase {

    @Override
    protected Class getClasseFichaInvestigacao() { return InvestigacaoAgravoDoencaTrabalhoPair.class; }

    @Override
    protected Map<String, String> getCamposInvestigacaoAgravo() {
        Map<String, String> campos = new HashMap<>();

        campos.put("ocupacaoCbo.descricao", "_31_ocupacao");

        campos.put("investigacaoAgravo.situacaoMercadoTrabalho", "_32_situacao_mercado_trab");
        campos.put("investigacaoAgravo.tempoTrabalhoOcupacao", "_33_tempo_trabalho");
        campos.put("investigacaoAgravo.tempoTrabalhoOcupacaoUnidadeMedida", "_33_tempo_trabalho_um");

        campos.put("empresaContratante.cnpj", "_34_cnpj");
        campos.put("empresaContratante.descricao", "_35_empresa");
        campos.put("atividade.descricao", "_36_cnae");

        campos.put("cidade.estado.sigla", "_37_uf");
        campos.put("empresaContratante.cidade.descricao", "_38_municipio");
        campos.put("empresaContratante.cidade.codigo", "_38_ibge");
        campos.put("investigacaoAgravo.empresaDistrito", "_39_distrito");
        campos.put("empresaContratante.bairro", "_40_bairro");
        campos.put("empresaContratante.rua", "_41_endereco");
        campos.put("empresaContratante.numero", "_42_numero");
        campos.put("investigacaoAgravo.empresaPontoReferencia", "_43_ponto_ref");
        campos.put("investigacaoAgravo.empresaTelefone", "_44_telefone");
        campos.put("investigacaoAgravo.empresaTerceirizada", "_45_empresa_terceirizada");

        campos.put("investigacaoAgravo.agravosAssociadosHipertensao", "_46_hipertensao_arterial");
        campos.put("investigacaoAgravo.agravosAssociadosTuberculose", "_46_tuberculose");
        campos.put("investigacaoAgravo.agravosAssociadosDiabetes", "_46_diabetes_mellitus");
        campos.put("investigacaoAgravo.agravosAssociadosAsma", "_46_asma");
        campos.put("investigacaoAgravo.agravosAssociadosHanseniase", "_46_hanseniase");
        campos.put("investigacaoAgravo.agravosAssociadosTranstornoMental", "_46_transtorno_mental");
        campos.put("investigacaoAgravo.agravosAssociadosOutros", "_46_outro_descricao");
        campos.put("investigacaoAgravo.tempoExposicaoAgenteRisco", "_47_tempo_exposicao");
        campos.put("investigacaoAgravo.tempoExposicaoAgenteRiscoUnidadeMedida", "_47_tempo_exposicao_um");
        campos.put("investigacaoAgravo.regimeTratamento", "_48_regime_tratamento");

        campos.put("investigacaoAgravo.tipoRuidoPredominante", "_49_tipo_ruido_predominante");

        campos.put("investigacaoAgravo.ruidoSolventeTolueno", "_50_ruido_solvente_tolueno");
        campos.put("investigacaoAgravo.ruidoGasesToxicos", "_50_ruido_gases_toxicos");
        campos.put("investigacaoAgravo.ruidoMetaisPesados", "_50_ruido_metais_pesados");
        campos.put("investigacaoAgravo.ruidoMedicamentosOtotoxicos", "_50_ruido_medicamentos_ototoxicos");
        campos.put("investigacaoAgravo.ruidosOutros", "_50_ruido_outros");

        campos.put("investigacaoAgravo.sintomasZumbido", "_51_sintomas_zumbido");
        campos.put("investigacaoAgravo.sintomasCefaleia", "_51_sintomas_cefaleia");
        campos.put("investigacaoAgravo.sintomasTontura", "_51_sintomas_tontura");
        campos.put("investigacaoAgravo.sintomasDificuldadeFala", "_51_sintomas_dificuldade_fala");
        campos.put("investigacaoAgravo.sintomasOutros", "_51_sintomas_outros");

        campos.put("diagnosticoEspecifico.codigo", "_52_cid");

        campos.put("investigacaoAgravo.afastamentoParaTratamento", "_53_afastamento_trabalho");
        campos.put("investigacaoAgravo.tempoAfastamento", "_54_tempo_afastamento_trabalho");
        campos.put("investigacaoAgravo.tempoAfastamentoUnidadeMedida", "_54_tempo_afastamento_trabalho_um");
        campos.put("investigacaoAgravo.conclusaoAfastamento", "_55_tempo_melhora_afastamento");
        campos.put("investigacaoAgravo.outrosTrabalhadoresMesmaDoenca", "_56_outros_trabalhadores");

        campos.put("investigacaoAgravo.condutaGeralAfastamentoAgenteRisco", "_57_conduta_geral_afastamento_risco");
        campos.put("investigacaoAgravo.condutaGeralMudancaOrganizacaoTrabalho", "_57_conduta_geral_mudanca_organizacao");
        campos.put("investigacaoAgravo.condutaGeralProtecaoColetiva", "_57_conduta_geral_protecao_coletiva");
        campos.put("investigacaoAgravo.condutaGeralProtecaoIndividual", "_57_conduta_geral_afastamento_local");
        campos.put("investigacaoAgravo.condutaGeralNenhum", "_57_conduta_geral_protecao_individual");
        campos.put("investigacaoAgravo.condutaGeralAfastamentoLocalTrabalho", "_57_conduta_geral_nenhum");
        campos.put("investigacaoAgravo.condutaGeralOutros", "_57_conduta_geral_outros");

        campos.put("investigacaoAgravo.evolucaoCaso", "_58_evolucao_caso");
        campos.put(formatarData("investigacaoAgravo.dataObito"), "_59_data_obito");
        campos.put("investigacaoAgravo.emitidaCat", "_60_emitida_cat");
        campos.put("investigacaoAgravo.observacao", "_observacao");

        return campos;
    }

    @Override
    protected String getJuncoesFicha() {
        return "left join investigacaoAgravo.ocupacaoCbo ocupacaoCbo "
                + "left join investigacaoAgravo.empresaContratante empresaContratante "
                + "left join empresaContratante.atividade atividade "
                + "left join empresaContratante.cidade cidade "
                + "left join cidade.estado estado "
                + "left join investigacaoAgravo.diagnosticoEspecifico diagnosticoEspecifico ";
    }

    @Override
    protected void customProcess(Session session) throws ValidacaoException, DAOException {
        super.customProcess(session);
        for (Map<String, Object> map : getResult()) {
            addTabelaCBO(map);
        }
    }

    private void addTabelaCBO(Map<String, Object> map) {
        TabelaCbo cbo = (TabelaCbo) map.get("ocupacaoCbo.descricao");
        if (cbo != null) {
            map.put("ocupacaoCbo.descricao", cbo.getDescricaoFormatado());
        }
    }

}
