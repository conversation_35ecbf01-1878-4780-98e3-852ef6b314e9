package br.com.celk.report.vigilancia.requerimento;

import br.com.celk.report.HtmlReport;
import br.com.celk.report.HtmlReportTemplateType;
import br.com.celk.report.templatebuilder.IHtmlTemplateBuilder;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoParecerProjetoHidrossanitarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.util.Collection;

public class ParecerProjetoHidrossanitarioHtmlReport extends HtmlReport {

    private IHtmlTemplateBuilder template;
    private HtmlTemplateUtil templateUtil;
    private ImpressaoParecerProjetoHidrossanitarioDTO impressaoParecerProjetoHidrossanitarioDTO;
    public ParecerProjetoHidrossanitarioHtmlReport(Collection<ImpressaoParecerProjetoHidrossanitarioDTO> impressaoParecerProjetoHidrossanitarioDTOList) {
        super(HtmlReportTemplateType.CAB_ROD_TIMPRADO_VIGILANCIA);
        this.template = HtmlReportTemplateType.PARECER_PROJETO_HIDROSSANITARIO_VIGILANCIA.templateBuilderInstance();
        this.templateUtil = new HtmlTemplateUtil(this.template);
        this.impressaoParecerProjetoHidrossanitarioDTO = impressaoParecerProjetoHidrossanitarioDTOList.iterator().next();
    }

    @Override
    public void customizeReport(IHtmlTemplateBuilder templateBuilder) {
        templateUtil.preencherCampo("titulo", impressaoParecerProjetoHidrossanitarioDTO.getDescricaoStatus().toUpperCase(), "");
        templateUtil.preencherCampo("rotulo_protocolo", Bundle.getStringApplication("rotulo_protocolo").toUpperCase() + ": " +
                impressaoParecerProjetoHidrossanitarioDTO.getRequerimentoVigilancia().getProtocoloFormatado(), "");
        templateUtil.preencherCampo("rotulo_data_cadastro_abv", Bundle.getStringApplication("rotulo_data_cadastro_abv").toUpperCase() + ": " +
                Data.formatar(impressaoParecerProjetoHidrossanitarioDTO.getDataCadastro()), "");
        templateUtil.preencherCampo("data_parecer", Data.formatar(impressaoParecerProjetoHidrossanitarioDTO.getDataParecer()), "");
        templateUtil.preencherCampo("descricao_parecer", impressaoParecerProjetoHidrossanitarioDTO.getDescricaoParecer(), "");
        templateUtil.preencherCampo("data_retorno", Data.formatar(impressaoParecerProjetoHidrossanitarioDTO.getDataRetorno()), "");
        templateUtil.preencherCampo("resposta", impressaoParecerProjetoHidrossanitarioDTO.getResposta(), "");
        templateUtil.preencherCampo("anexo", impressaoParecerProjetoHidrossanitarioDTO.getAnexos(), "");
        if (CollectionUtils.isNotNullEmpty(impressaoParecerProjetoHidrossanitarioDTO.getFiscais())){
            try {
                templateUtil.preencherCampo("fiscais", templateUtil.montarFiscais(impressaoParecerProjetoHidrossanitarioDTO.getFiscais(),
                        VigilanciaHelper.exibirLinhaAssinatura() && RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class)
                                .modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("gerarDocumentoComAssinaturaFiscal"))), "display_rotulo_assinaturas");
            } catch (DAOException e) {
                Loggable.log.error(e);
            }
        }
        templateUtil.preencherCampo("qrCode",templateUtil.gerarQrCode(impressaoParecerProjetoHidrossanitarioDTO.getUrlQrCode()),"");
    }
}
