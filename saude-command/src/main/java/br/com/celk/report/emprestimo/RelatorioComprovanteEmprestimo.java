package br.com.celk.report.emprestimo;

import br.com.celk.report.emprestimo.query.QueryComprovanteEmprestimo;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.report.AbstractReport;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo;

/**
 *
 * <AUTHOR>
 */
public class RelatorioComprovanteEmprestimo extends AbstractReport<LancamentoEmprestimo> {

    public RelatorioComprovanteEmprestimo(LancamentoEmprestimo lancamentoEmprestimo) {
        super(lancamentoEmprestimo);
    }

    @Override
    public ITransferDataReport getQuery() {
        addParametro("EXIBIR_NUMERO_PAGINAS", true);
        return new QueryComprovanteEmprestimo();
    }

    @Override
    public String getXML() {
        return "/br/com/celk/report/emprestimo/jrxml/relatorio_comprovante_emprestimo.jrxml";
    }

    @Override
    public String getTitulo() {
        return Bundle.getStringApplication("rotulo_comprovante_emprestimo");
    }

}
