package br.com.celk.agendamento.ppi;

import br.com.celk.agendamento.ppi.dto.PpiOcorrenciaDTO;
import br.com.celk.agendamento.ppi.dto.PpiTipoExameDTO;
import br.com.ksisolucoes.command.AbstractCommandTransaction;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.PpiExame;
import br.com.ksisolucoes.vo.prontuario.basico.PpiOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.PpiTipoExame;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;

import java.util.Date;

public class GerarOcorrenciaPpi extends AbstractCommandTransaction<GerarOcorrenciaPpi> {

    private final PpiOcorrenciaDTO dto;
    private final PpiTipoExame ppiTipoExame;
    private final PpiExame ppiExame;

    public GerarOcorrenciaPpi(PpiOcorrenciaDTO dto) {
        this(dto, null, null);
    }

    public GerarOcorrenciaPpi(PpiOcorrenciaDTO dto, PpiTipoExame ppiTipoExame) {
        this(dto, ppiTipoExame, null);
    }

    public GerarOcorrenciaPpi(PpiOcorrenciaDTO dto, PpiTipoExame ppiTipoExame, PpiExame ppiExame) {
        this.dto = dto;
        this.ppiTipoExame = ppiTipoExame;
        this.ppiExame = ppiExame;
    }


    @Override
    public void execute() throws DAOException, ValidacaoException {
        PpiOcorrencia ppiOcorrencia = new PpiOcorrencia();
        ppiOcorrencia.setUsuario(getUsuarioSistema());
        ppiOcorrencia.setDtOcorrencia(new Date());
        ppiOcorrencia.setTpOcorrencia(dto.getTpOcorrencia());
        ppiOcorrencia.setCdPpiSecretaria(dto.getCdPpiSecretaria());
        ppiOcorrencia.setCdSolicitacaoAgendamento(dto.getCdSolicitacaoAgendamento());
        ppiOcorrencia.setPpiTipoExame(ppiTipoExame != null ? ppiTipoExame : BuscarDadosPpi.getPpiTipoExame(dto.getCdPpiTipoExame()));
        ppiOcorrencia.setPpiExame(ppiExame != null ? ppiExame : BuscarDadosPpi.getPpiExame(dto.getCdPpiExame()));
        ppiOcorrencia.setDsOcorrencia(dto.getDsOcorrencia());
        BOFactory.save(ppiOcorrencia);
    }

    private Usuario getUsuarioSistema() {
        if (SessaoAplicacaoImp.getInstance() != null) return SessaoAplicacaoImp.getInstance().getUsuario();
        return (Usuario) getSession().get(Usuario.class, Usuario.USUARIO_ADMINISTRADOR);
    }
}
