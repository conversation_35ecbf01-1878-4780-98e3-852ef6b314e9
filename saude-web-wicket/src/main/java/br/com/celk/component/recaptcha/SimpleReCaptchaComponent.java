package br.com.celk.component.recaptcha;

import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.HiddenField;
import org.apache.wicket.model.Model;

/**
 * Componente reCAPTCHA simplificado para debug
 */
public class SimpleReCaptchaComponent extends WebMarkupContainer {

    private HiddenField recaptchaResponse;
    private String siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste do Google

    public SimpleReCaptchaComponent(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        // Campo oculto para armazenar a resposta do reCAPTCHA
        recaptchaResponse = new HiddenField("recaptchaResponse", Model.of(""));
        recaptchaResponse.setOutputMarkupId(true);
        add(recaptchaResponse);

        // Label para debug
        add(new Label("debugLabel", "reCAPTCHA Component Loaded"));
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        // Adicionar script do Google reCAPTCHA
        response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js"));

        // Script para inicializar o reCAPTCHA
        String initScript = String.format(
                "function onRecaptchaCallback(token) { " +
                "  document.getElementById('%s').value = token; " +
                "  console.log('reCAPTCHA completed: ' + token); " +
                "} " +
                "function onRecaptchaExpired() { " +
                "  document.getElementById('%s').value = ''; " +
                "  console.log('reCAPTCHA expired'); " +
                "} " +
                "$(document).ready(function() { " +
                "  console.log('Setting reCAPTCHA site key: %s'); " +
                "  $('.g-recaptcha').attr('data-sitekey', '%s'); " +
                "});",
                recaptchaResponse.getMarkupId(),
                recaptchaResponse.getMarkupId(),
                siteKey,
                siteKey
        );

        response.render(OnLoadHeaderItem.forScript(initScript));
    }

    public String getRecaptchaResponse() {
        return (String) recaptchaResponse.getModelObject();
    }

    public boolean isCompleted() {
        String response = getRecaptchaResponse();
        return response != null && !response.trim().isEmpty();
    }
}
