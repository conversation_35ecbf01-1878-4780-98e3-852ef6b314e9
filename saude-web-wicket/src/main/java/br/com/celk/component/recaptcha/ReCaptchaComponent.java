package br.com.celk.component.recaptcha;

import br.com.celk.component.interfaces.IComponent;
import br.com.ksisolucoes.util.log.Loggable;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.HiddenField;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.validation.IValidatable;
import org.apache.wicket.validation.IValidator;
import org.apache.wicket.validation.ValidationError;

/**
 * Componente reCAPTCHA para validação anti-bot
 *
 * <AUTHOR>
 */
public class ReCaptchaComponent extends WebMarkupContainer implements IComponent<String>, Loggable {

    private HiddenField<String> recaptchaResponse;
    private String siteKey;
    private boolean enabled = true;

    public ReCaptchaComponent(String id) {
        this(id, Model.of(""));
    }

    public ReCaptchaComponent(String id, IModel<String> model) {
        super(id, model);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        // Para debug - usar chave de teste do Google
        siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste oficial do Google
        enabled = true;

        // Campo oculto para armazenar a resposta do reCAPTCHA
        recaptchaResponse = new HiddenField<String>("recaptchaResponse", Model.of(""));
        recaptchaResponse.setOutputMarkupId(true);

        add(recaptchaResponse);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (enabled) {
            // Adicionar script do Google reCAPTCHA
            response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js"));

            // Script para inicializar o reCAPTCHA
            String initScript = String.format(
                    "function onRecaptchaCallback(token) { " +
                            "  document.getElementById('%s').value = token; " +
                            "} " +
                            "function onRecaptchaExpired() { " +
                            "  document.getElementById('%s').value = ''; " +
                            "} " +
                            "$(document).ready(function() { " +
                            "  $('.g-recaptcha').attr('data-sitekey', '%s'); " +
                            "});",
                    recaptchaResponse.getMarkupId(),
                    recaptchaResponse.getMarkupId(),
                    siteKey
            );

            response.render(OnLoadHeaderItem.forScript(initScript));
        }
    }

    @Override
    public String getComponentValue() {
        return recaptchaResponse.getModelObject();
    }

    @Override
    public void setComponentValue(String value) {
        recaptchaResponse.setModelObject(value);
    }

    @Override
    public void addAjaxUpdateValue() {

    }

    @Override
    public void limpar(AjaxRequestTarget target) {

    }

    public String getSiteKey() {
        return siteKey;
    }

    public boolean isEnabled() {
        return enabled;
    }

    /**
     * Validador para o reCAPTCHA
     */
    private class ReCaptchaValidator implements IValidator<String> {

        @Override
        public void validate(IValidatable<String> validatable) {
            String response = validatable.getValue();

            if (response == null || response.trim().isEmpty()) {
                validatable.error(new ValidationError().addKey("recaptcha.required"));
                return;
            }

            try {
                boolean isValid = ReCaptchaUtil.verifyRecaptcha(response);
                if (!isValid) {
                    validatable.error(new ValidationError().addKey("recaptcha.invalid"));
                }
            } catch (Exception e) {
                log.error("Erro ao validar reCAPTCHA", e);
                validatable.error(new ValidationError().addKey("recaptcha.error"));
            }
        }
    }
}
