package br.com.celk.component.recaptcha;

import br.com.ksisolucoes.util.log.Loggable;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;

/**
 * Utilitário para validação do Google reCAPTCHA
 * 
 * <AUTHOR>
 */
public class ReCaptchaUtil implements Loggable {

    private static final String RECAPTCHA_VERIFY_URL = "https://www.google.com/recaptcha/api/siteverify";
    
    /**
     * Verifica se a resposta do reCAPTCHA é válida
     * 
     * @param recaptchaResponse Resposta do reCAPTCHA do cliente
     * @return true se válido, false caso contrário
     * @throws Exception em caso de erro na validação
     */
    public static boolean verifyRecaptcha(String recaptchaResponse) throws Exception {
        if (recaptchaResponse == null || recaptchaResponse.trim().isEmpty()) {
            return false;
        }
        
        String secretKey = getSecretKey();
        if (secretKey == null || secretKey.trim().isEmpty()) {
            log.warn("Chave secreta do reCAPTCHA não configurada");
            return false;
        }
        
        try {
            URL url = new URL(RECAPTCHA_VERIFY_URL);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // Configurar conexão
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            connection.setDoOutput(true);
            
            // Preparar dados para envio
            String postData = "secret=" + URLEncoder.encode(secretKey, "UTF-8") +
                             "&response=" + URLEncoder.encode(recaptchaResponse, "UTF-8");
            
            // Enviar dados
            try (DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream())) {
                outputStream.writeBytes(postData);
                outputStream.flush();
            }
            
            // Ler resposta
            StringBuilder response = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }
            
            // Analisar resposta JSON
            JsonParser parser = new JsonParser();
            JsonObject jsonResponse = parser.parse(response.toString()).getAsJsonObject();
            
            boolean success = jsonResponse.get("success").getAsBoolean();
            
            if (!success && jsonResponse.has("error-codes")) {
                log.warn("Erro na validação do reCAPTCHA: " + jsonResponse.get("error-codes").toString());
            }
            
            return success;
            
        } catch (IOException e) {
            log.error("Erro ao conectar com o serviço de validação do reCAPTCHA", e);
            throw new Exception("Erro na validação do reCAPTCHA", e);
        }
    }
    
    /**
     * Obtém a chave secreta do reCAPTCHA dos parâmetros do sistema
     * 
     * @return Chave secreta ou null se não configurada
     */
    private static String getSecretKey() {
        return "6LfUB2MrAAAAALWp-gXKQCeS9UBLMG6yePu1LtPO";
//        try {
//            return BOFactoryWicket.getBO(CommomFacade.class)
//                .modulo(Modulos.SISTEMA)
//                .getParametro("GoogleReCaptchaSecretKey");
//        } catch (DAOException e) {
//            log.error("Erro ao buscar chave secreta do reCAPTCHA", e);
//            return null;
//        }
    }
}
