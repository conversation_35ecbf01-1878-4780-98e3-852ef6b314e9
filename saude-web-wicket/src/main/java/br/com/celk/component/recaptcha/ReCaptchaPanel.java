package br.com.celk.component.recaptcha;

import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.form.HiddenField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 * Componente reCAPTCHA funcional
 */
public class ReCaptchaPanel extends Panel {

    private HiddenField<String> recaptchaResponse;
    private String siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste do Google
    private boolean enabled = true;

    public ReCaptchaPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        // Campo oculto para armazenar a resposta do reCAPTCHA
        recaptchaResponse = new HiddenField<>("recaptchaResponse", Model.of(""));
        recaptchaResponse.setOutputMarkupId(true);
        add(recaptchaResponse);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (enabled) {
            // Adicionar script do Google reCAPTCHA
            response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js"));

            // Script para inicializar o reCAPTCHA
            String initScript = String.format(
                    "function onRecaptchaCallback(token) { " +
                    "  document.getElementById('%s').value = token; " +
                    "  console.log('reCAPTCHA completed'); " +
                    "} " +
                    "function onRecaptchaExpired() { " +
                    "  document.getElementById('%s').value = ''; " +
                    "  console.log('reCAPTCHA expired'); " +
                    "} " +
                    "$(document).ready(function() { " +
                    "  $('.g-recaptcha').attr('data-sitekey', '%s'); " +
                    "  console.log('reCAPTCHA initialized with key: %s'); " +
                    "});",
                    recaptchaResponse.getMarkupId(),
                    recaptchaResponse.getMarkupId(),
                    siteKey,
                    siteKey
            );

            response.render(OnLoadHeaderItem.forScript(initScript));
        }
    }

    public String getRecaptchaResponse() {
        return recaptchaResponse.getModelObject();
    }

    public boolean isCompleted() {
        String response = getRecaptchaResponse();
        return response != null && !response.trim().isEmpty();
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }
}
