package br.com.celk.component.recaptcha;

import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.form.HiddenField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 * Componente reCAPTCHA funcional
 */
public class ReCaptchaPanel extends Panel {

    private HiddenField<String> recaptchaResponse;
    private String siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste do Google
    private boolean enabled = true;

    public ReCaptchaPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        // Campo oculto para armazenar a resposta do reCAPTCHA
        recaptchaResponse = new HiddenField<>("recaptchaResponse", Model.of(""));
        recaptchaResponse.setOutputMarkupId(true);
        recaptchaResponse.setOutputMarkupPlaceholderTag(true);
        add(recaptchaResponse);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (enabled) {
            // Usar abordagem mais simples - carregar automaticamente sem callback personalizado
            response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js"));

            // Script para verificar e renderizar quando a API estiver pronta
            String initScript = String.format(
                    "function checkAndRenderRecaptcha() { " +
                    "  if (typeof grecaptcha !== 'undefined' && grecaptcha.render) { " +
                    "    console.log('Google reCAPTCHA API ready'); " +
                    "    var recaptchaElement = document.getElementById('recaptcha-div'); " +
                    "    var statusElement = document.getElementById('recaptcha-status'); " +
                    "    if (recaptchaElement && !recaptchaElement.hasChildNodes()) { " +
                    "      try { " +
                    "        var widgetId = grecaptcha.render('recaptcha-div', { " +
                    "          'sitekey': '%s', " +
                    "          'callback': function(token) { " +
                    "            document.getElementById('%s').value = token; " +
                    "            console.log('reCAPTCHA completed'); " +
                    "            if (statusElement) statusElement.innerHTML = 'Verificação concluída com sucesso!'; " +
                    "          }, " +
                    "          'expired-callback': function() { " +
                    "            document.getElementById('%s').value = ''; " +
                    "            console.log('reCAPTCHA expired'); " +
                    "            if (statusElement) statusElement.innerHTML = 'Verificação expirada. Complete novamente.'; " +
                    "          } " +
                    "        }); " +
                    "        console.log('reCAPTCHA widget rendered with ID: ' + widgetId); " +
                    "        if (statusElement) statusElement.innerHTML = 'Complete a verificação acima para continuar'; " +
                    "      } catch (e) { " +
                    "        console.error('Erro ao renderizar reCAPTCHA: ', e); " +
                    "        if (statusElement) statusElement.innerHTML = 'Erro ao carregar verificação'; " +
                    "      } " +
                    "    } " +
                    "  } else { " +
                    "    console.log('Google reCAPTCHA API not ready yet, retrying...'); " +
                    "    setTimeout(checkAndRenderRecaptcha, 500); " +
                    "  } " +
                    "} " +
                    "setTimeout(checkAndRenderRecaptcha, 1000);",
                    siteKey,
                    recaptchaResponse.getMarkupId(),
                    recaptchaResponse.getMarkupId()
            );

            response.render(OnLoadHeaderItem.forScript(initScript));
        }
    }

    public String getRecaptchaResponse() {
        return recaptchaResponse.getModelObject();
    }

    public boolean isCompleted() {
        String response = getRecaptchaResponse();
        return response != null && !response.trim().isEmpty();
    }

    public boolean isEnabled() {
        return enabled;
    }

//    public void setEnabled(boolean enabled) {
//        this.enabled = enabled;
//    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }
}
