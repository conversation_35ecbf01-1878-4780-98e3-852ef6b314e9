package br.com.celk.component.recaptcha;

import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.form.HiddenField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

/**
 * Componente reCAPTCHA funcional
 */
public class ReCaptchaPanel extends Panel {

    private HiddenField<String> recaptchaResponse;
    private String siteKey = "6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI"; // Chave de teste do Google
    private boolean enabled = true;

    public ReCaptchaPanel(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        // Campo oculto para armazenar a resposta do reCAPTCHA
        recaptchaResponse = new HiddenField<>("recaptchaResponse", Model.of(""));
        recaptchaResponse.setOutputMarkupId(true);
        add(recaptchaResponse);
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);

        if (enabled) {
            // Primeiro, definir as funções globais
            String globalFunctions = String.format(
                    "window.recaptchaWidgetId = null; " +
                    "window.onRecaptchaLoad = function() { " +
                    "  console.log('Google reCAPTCHA API loaded'); " +
                    "  var recaptchaElement = document.getElementById('recaptcha-div'); " +
                    "  var statusElement = document.getElementById('recaptcha-status'); " +
                    "  if (recaptchaElement) { " +
                    "    try { " +
                    "      window.recaptchaWidgetId = grecaptcha.render('recaptcha-div', { " +
                    "        'sitekey': '%s', " +
                    "        'callback': 'onRecaptchaCallback', " +
                    "        'expired-callback': 'onRecaptchaExpired' " +
                    "      }); " +
                    "      console.log('reCAPTCHA widget rendered with ID: ' + window.recaptchaWidgetId); " +
                    "      if (statusElement) statusElement.innerHTML = 'Complete a verificação acima para continuar'; " +
                    "    } catch (e) { " +
                    "      console.error('Erro ao renderizar reCAPTCHA: ', e); " +
                    "      if (statusElement) statusElement.innerHTML = 'Erro ao carregar verificação'; " +
                    "    } " +
                    "  } else { " +
                    "    console.log('reCAPTCHA element not found'); " +
                    "    if (statusElement) statusElement.innerHTML = 'Elemento reCAPTCHA não encontrado'; " +
                    "  } " +
                    "}; " +
                    "window.onRecaptchaCallback = function(token) { " +
                    "  document.getElementById('%s').value = token; " +
                    "  console.log('reCAPTCHA completed: ' + token); " +
                    "  var statusElement = document.getElementById('recaptcha-status'); " +
                    "  if (statusElement) statusElement.innerHTML = 'Verificação concluída com sucesso!'; " +
                    "}; " +
                    "window.onRecaptchaExpired = function() { " +
                    "  document.getElementById('%s').value = ''; " +
                    "  console.log('reCAPTCHA expired'); " +
                    "  var statusElement = document.getElementById('recaptcha-status'); " +
                    "  if (statusElement) statusElement.innerHTML = 'Verificação expirada. Complete novamente.'; " +
                    "};",
                    siteKey,
                    recaptchaResponse.getMarkupId(),
                    recaptchaResponse.getMarkupId()
            );

            // Renderizar as funções globais primeiro
            response.render(OnLoadHeaderItem.forScript(globalFunctions));

            // Depois carregar o script do Google reCAPTCHA
            response.render(JavaScriptHeaderItem.forUrl("https://www.google.com/recaptcha/api.js?onload=onRecaptchaLoad&render=explicit"));
        }
    }

    public String getRecaptchaResponse() {
        return recaptchaResponse.getModelObject();
    }

    public boolean isCompleted() {
        String response = getRecaptchaResponse();
        return response != null && !response.trim().isEmpty();
    }

    public boolean isEnabled() {
        return enabled;
    }

//    public void setEnabled(boolean enabled) {
//        this.enabled = enabled;
//    }

    public void setSiteKey(String siteKey) {
        this.siteKey = siteKey;
    }
}
