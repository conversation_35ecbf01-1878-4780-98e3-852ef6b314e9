package br.com.celk.view.unidadesaude.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.report.unidadesaude.interfaces.dto.RelacaoPacientesAtendimentoAtrasoDTOParam;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.atendimento.tipoatendimento.autocomplete.AutoCompleteConsultaTipoAtendimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelacaoPacientesAtendimentoAtrasoPage extends RelatorioPage<RelacaoPacientesAtendimentoAtrasoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;

    @Override
    public void init(Form form) {
        RelacaoPacientesAtendimentoAtrasoDTOParam proxy = on(RelacaoPacientesAtendimentoAtrasoDTOParam.class);
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);

        form.add(new AutoCompleteConsultaTipoAtendimento(path(proxy.getTipoAtendimento())).setIncluirInativos(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(new AutoCompleteConsultaTabelaCbo(path(proxy.getCbo())));
        form.add(DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelacaoPacientesAtendimentoAtrasoDTOParam.FormaApresentacao.values()));
    }

    @Override
    public Class getDTOParamClass() {
        return RelacaoPacientesAtendimentoAtrasoDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelacaoPacientesAtendimentoAtrasoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relacaoPacientesAtendimentoAtraso(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoPacientesAtendimentoAtraso");
    }
}
