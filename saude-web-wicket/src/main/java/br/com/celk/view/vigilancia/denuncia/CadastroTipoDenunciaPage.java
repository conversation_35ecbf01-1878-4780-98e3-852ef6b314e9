package br.com.celk.view.vigilancia.denuncia;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.vigilancia.setorvigilancia.autocomplete.AutoCompleteConsultaSetorVigilancia;
import br.com.ksisolucoes.vo.vigilancia.denuncia.TipoDenuncia;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
public class CadastroTipoDenunciaPage extends CadastroPage<TipoDenuncia> {
    
    private InputField txtDescricao;

    public CadastroTipoDenunciaPage(TipoDenuncia object,boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }
    
    public CadastroTipoDenunciaPage(TipoDenuncia object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroTipoDenunciaPage(TipoDenuncia object) {
        this(object, false);
    }

    public CadastroTipoDenunciaPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(txtDescricao = new RequiredInputField<String>(TipoDenuncia.PROP_DESCRICAO));
        form.add(new AutoCompleteConsultaSetorVigilancia(TipoDenuncia.PROP_SETOR_VIGILANCIA));
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<TipoDenuncia> getReferenceClass() {
        return TipoDenuncia.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaTipoDenunciaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroTipoDenuncia");
    }
    
}

