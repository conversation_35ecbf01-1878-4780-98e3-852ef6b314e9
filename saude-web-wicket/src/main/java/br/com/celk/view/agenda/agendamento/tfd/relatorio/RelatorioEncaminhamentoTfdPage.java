package br.com.celk.view.agenda.agendamento.tfd.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.encaminhamento.dto.RelatorioLaudoTfdDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.tfd.interfaces.facade.TfdReportFacade;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.Arrays;

/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioEncaminhamentoTfdPage extends RelatorioPage<RelatorioLaudoTfdDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private DropDown<String> dropDownFormaApresentacao;
    private DropDown<String> dropDownTipoPeriodo;
    private DropDown<String> dropDownOrdenacao;

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE))
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaProfissional("profissional")
                .setMultiplaSelecao(true));
        form.add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento"));
        autoCompleteConsultaTipoProcedimento.setMultiplaSelecao(true);
        autoCompleteConsultaTipoProcedimento.setTfd(true);
        autoCompleteConsultaTipoProcedimento.setIncluirInativos(true);
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(getDropDownTipoPeriodo());
        form.add(DropDownUtil.getEnumDropDown("situacao", RelatorioLaudoTfdDTOParam.Situacao.values()));
        form.add(getDropDownFormaApresentacao());
        form.add(getDropDownOrdenacao());
    }

    public DropDown getDropDownOrdenacao() {
        if (dropDownOrdenacao == null) {
            dropDownOrdenacao = new DropDown<String>("ordenacao");
            dropDownOrdenacao.addChoice(LaudoTfd.PROP_DATA_CADASTRO, BundleManager.getString("data"));
            dropDownOrdenacao.addChoice(LaudoTfd.PROP_USUARIO_CADSUS, BundleManager.getString("paciente"));
        }
        return dropDownOrdenacao;
    }

    public DropDown getDropDownTipoPeriodo() {
        if (dropDownTipoPeriodo == null) {
            dropDownTipoPeriodo = new DropDown<String>("tipoPeriodo");
            dropDownTipoPeriodo.addChoice(LaudoTfd.PROP_DATA_CADASTRO, BundleManager.getString("cadastro"));
            dropDownTipoPeriodo.addChoice(SolicitacaoAgendamento.PROP_DATA_AGENDAMENTO, BundleManager.getString("agendamento"));
        }
        return dropDownTipoPeriodo;
    }

    public DropDown getDropDownFormaApresentacao() {
        if (dropDownFormaApresentacao == null) {
            dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
            dropDownFormaApresentacao.addChoice(null, BundleManager.getString("geral"));
            dropDownFormaApresentacao.addChoice(Empresa.REF, BundleManager.getString("empresa"));
            dropDownFormaApresentacao.addChoice(Profissional.REF, BundleManager.getString("profissional"));
            dropDownFormaApresentacao.addChoice(TipoProcedimento.REF, BundleManager.getString("tipoProcedimento"));
        }
        return dropDownFormaApresentacao;
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioLaudoTfdDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(RelatorioLaudoTfdDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(TfdReportFacade.class).relatorioEncaminhamentoTfd(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoTfds");
    }
}
