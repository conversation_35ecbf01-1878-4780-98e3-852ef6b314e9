package br.com.celk.view.vigilancia.registroagravo.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoCancelamentoRegistroAgravo extends Window {

    private PnlMotivoCancelamentoRegistroAgravo pnlMotivoCancelamentoRegistroAgravo;

    public DlgMotivoCancelamentoRegistroAgravo(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("motivoCancelamento");
            }
        });

        setInitialWidth(600);
        setInitialHeight(280);
        setResizable(false);

        setContent(pnlMotivoCancelamentoRegistroAgravo = new PnlMotivoCancelamentoRegistroAgravo(getContentId()) {
            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, RegistroAgravo registroAgravo) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoCancelamentoRegistroAgravo.this.onConfirmar(target, registroAgravo);
            }
        });
    }

    public void show(AjaxRequestTarget target, RegistroAgravo registroAgravo) {
        pnlMotivoCancelamentoRegistroAgravo.setRegistroAgravo(target, registroAgravo);
        show(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, RegistroAgravo registroAgravo) throws ValidacaoException, DAOException;

}
