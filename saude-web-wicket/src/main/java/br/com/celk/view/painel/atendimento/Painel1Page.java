package br.com.celk.view.painel.atendimento;

import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.notification.NotificationPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.LineBreakColumn;
import br.com.celk.component.table.column.TimeColumn;
import br.com.celk.component.template.footer.FooterPanel;
import br.com.celk.io.FtpImageUtil;
import br.com.celk.io.LogoHelper;
import br.com.celk.resources.Resources;
import br.com.celk.system.Application;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.painel.PainelDTO;
import br.com.celk.view.atendimento.prontuario.tablecolumn.ClassificacaoRiscoColumnPainel;
import br.com.celk.view.sessaoexpirada.SessaoExpiradaPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.temp.v2.TempConverterV2;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.painel.Painel;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.ClassificacaoRisco;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import org.apache.commons.codec.binary.Base64;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.RestartResponseAtInterceptPageException;
import org.apache.wicket.ajax.AbstractDefaultAjaxBehavior;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.CallbackParameter;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.JavaScriptHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebPage;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.StatelessForm;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.image.NonCachingImage;
import org.apache.wicket.markup.html.link.ResourceLink;
import org.apache.wicket.model.*;
import org.apache.wicket.request.cycle.RequestCycle;
import org.apache.wicket.request.http.WebRequest;
import org.apache.wicket.request.resource.CssResourceReference;
import org.apache.wicket.request.resource.IResource;
import org.apache.wicket.request.resource.ResourceReference;
import org.apache.wicket.request.resource.ResourceStreamResource;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.string.StringValue;
import org.wicketstuff.annotation.mount.MountPath;
import org.wicketstuff.html5.media.MediaSource;
import org.wicketstuff.html5.media.audio.Html5Audio;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.Cookie;
import java.io.File;
import java.security.Key;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@MountPath("painel1")
public class Painel1Page extends WebPage {

    private NotificationPanel notificationPanel;
    private Model<String> modelTituloAba;

    private AbstractDefaultAjaxBehavior behavior;
    private PainelDTO dto;
    private final List<PainelDTO> ultimosChamados = new ArrayList<PainelDTO>();
    private Form form;
    private Label paciente;
    private Label profissional;
    private Label sala;
    private Label tipoAtendimento;
    private Image imgClassRisco;
    private Table table;
    private String chave;
    private Html5Audio html5Audio;
    private Html5Audio html5Audio2;
    private AbstractDefaultAjaxBehavior behaviorPing;
    private final String URL = "/rest/fala";
    private String urlPapaleguas;
    private Date ultimaConsulta;
    private Long tipoDispositivo;
    private boolean usaClassificacaoRisco;
    private String paramChamarUtilizando;
    @Override
    protected void onInitialize() {
        super.onInitialize();

        try {
            paramChamarUtilizando = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("chamarUtilizando");
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(),e);
        }

        WebRequest webRequest = (WebRequest) RequestCycle.get().getRequest();
        Cookie cookie = webRequest.getCookie("panelid");
        if (cookie == null) {
            throw new RestartResponseAtInterceptPageException(SessaoExpiradaPage.class);
        } else {
            chave = cookie.getValue();
        }
        final Painel painel = LoadManager.getInstance(Painel.class)
                .addParameter(new QueryCustom.QueryCustomParameter(Painel.PROP_CHAVE, chave))
                .start().getVO();
        if (painel == null) {
            throw new RestartResponseAtInterceptPageException(SessaoExpiradaPage.class);
        }
        tipoDispositivo = painel.getTipoDispositivo();

        form = new StatelessForm("form");

        setStatelessHint(true);

        ResourceLink<ResourceReference> rlnkFavicon = null;
        try {
            File logoFaviconFile = LogoHelper.getLogoFavicon();
            if (logoFaviconFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoFaviconFile);
                rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", new ResourceStreamResource(fileResourceStream)){
                    @Override
                    protected boolean getStatelessHint() {
                        return true;
                    }
                };
            }
        } catch (Throwable ex){
            Loggable.log.debug("FAVICON NAO ENCONTRADO!");
        } finally {
            if (rlnkFavicon == null){
                rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", Resources.Images.FAVICON.resourceReference());
            }
        }

        setStatelessHint(true);
        rlnkFavicon = new ResourceLink<ResourceReference>("lnkFavicon", Resources.Images.GEM_COLORIDO.resourceReference());
        add(rlnkFavicon);

        try {
            String title = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("tituloPaginaLogin");
            add(new Label("tituloAba", modelTituloAba = Model.of(title)));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        Image logo = null;
        try {
            File logoSistemaFile = LogoHelper.getLogoLogin();
            if (logoSistemaFile != null) {
                FileResourceStream fileResourceStream = new FileResourceStream(logoSistemaFile);
                logo = new NonCachingImage("logo", new ResourceStreamResource(fileResourceStream));
            } else {
                Loggable.log.error("Imagem do painel nao encontrada!");
            }
        } catch (Throwable ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } finally {
            if (logo == null) {
                logo = new Image("logo", Resources.Images.GEM.resourceReference());
            }
        }

        add(logo);
        verificaUsoClassificacaoRisco();

        form.add(notificationPanel = new NotificationPanel("notificationPanel"));
        try {
            String caminhoFtp = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("AlertaPainel");
            String caminho = new FtpImageUtil().downloadImage(caminhoFtp);
            if (caminho != null) {
                CharSequence resourceHref = urlFor(new ResourceReference("audioReference.ogg") {
                    @Override
                    public IResource getResource() {
                        String caminhoFtp = null;
                        try {
                            caminhoFtp = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("AlertaPainel");
                        } catch (DAOException e) {
                            Loggable.log.error(e.getMessage());
                        }
                        String caminho = new FtpImageUtil().downloadImage(caminhoFtp);
                        FileResourceStream fileResourceStream = new FileResourceStream(new File(caminho));
                        ResourceStreamResource rsr = new ResourceStreamResource(fileResourceStream);
                        return rsr;
                    }
                }, getPageParameters());

                final List<MediaSource> ms = new ArrayList<MediaSource>();
                ms.add(new MediaSource(resourceHref.toString(), "audio/ogg"));
                IModel<List<MediaSource>> mediaSourceList = new AbstractReadOnlyModel<List<MediaSource>>() {
                    @Override
                    public List<MediaSource> getObject() {
                        return ms;
                    }
                };
                form.add(html5Audio = new Html5Audio("audio", mediaSourceList));
                html5Audio.setOutputMarkupId(true);

                form.add(html5Audio2 = new Html5Audio("audio2", new Model()));
                html5Audio2.setOutputMarkupId(true);
                html5Audio2.setOutputMarkupPlaceholderTag(true);
                html5Audio2.add(new AttributeModifier("type", "audio/ogg"));
            } else {
                form.add(html5Audio = new Html5Audio("audio", new Model()));
                throw new ValidacaoException(bundle("erroCarregarAudio"));

            }
        } catch (Throwable ex) {
            getSession().error(ex.getMessage());
            Loggable.log.error(ex.getMessage(), ex);
        }

        form.setOutputMarkupId(true);
        form.add(new FooterPanel("footer"));

        add(new Label("titulo1", new Model(Coalesce.asString(painel.getTitulo1()))));
        add(new Label("titulo2", new Model(Coalesce.asString(painel.getTitulo2()))));
        form.add(profissional = new Label("profissional", new PropertyModel(this, "dto.profissional")));
        form.add(paciente = new Label("paciente", new PropertyModel(this, "dto.paciente")));
        if(Atendimento.ParamGemAtedimento.SENHA.descricao().equalsIgnoreCase(paramChamarUtilizando) || RepositoryComponentDefault.NAO_LONG.equals(painel.getFlagExibirProfissional())) {
            profissional.setVisible(false);
        }else{
            profissional.setVisible(true);
        }
        form.add(tipoAtendimento = new Label("tipoAtendimento", new PropertyModel(this, "dto.tipoAtendimento")));
        form.add(sala = new Label("sala", new PropertyModel(this, "dto.descricaoSala")));
//        form.add(local = new Label("local", new PropertyModel(this, "dto.local")));
        form.add(imgClassRisco = new Image("classRisco", Resources.Images.BALL_GRAY.resourceReference()));
        imgClassRisco.setOutputMarkupId(true);
        imgClassRisco.setVisible(usaClassificacaoRisco);
        form.add(new Label("painel", new LoadableDetachableModel() {
            @Override
            protected Object load() {
                return painel.getDescricao();
            }
        }));

        behaviorPing = new AbstractDefaultAjaxBehavior() {
            @Override
            protected void respond(AjaxRequestTarget target) {
                //utilizado somente para não expirar a sessao
            }
        };
        form.add(behaviorPing);

        behavior = new AbstractDefaultAjaxBehavior() {
            @Override
            protected void respond(AjaxRequestTarget target) {
                RequestCycle cycle = RequestCycle.get();
                WebRequest webRequest = (WebRequest) cycle.getRequest();
                StringValue message = webRequest.getQueryParameters().getParameterValue("message");
                if(Coalesce.asString(message).trim().length() > 0L){
                    PainelDTO dtoChamado = new TempConverterV2().toType(PainelDTO.class, message.toString());
                    if (dtoChamado != null && chave.equals(dtoChamado.getChave())) {
                        PainelDTO dtoAnterior = dto;
                        dto = dtoChamado;
                        ultimosChamados.remove(dto);
                        if (dtoAnterior != null && !dtoAnterior.equals(dto)) {
                            ultimosChamados.remove(dtoAnterior);
                            ultimosChamados.add(0, dtoAnterior);
                        }
                        if (ultimosChamados.size() == 4) {
                            ultimosChamados.remove(ultimosChamados.size() - 1);
                        }

                        if (dto.getClassificacaoRisco() != null) {
                            if (ClassificacaoRisco.NivelGravidade.VERDE.value().equals(dto.getClassificacaoRisco().getNivelGravidade())) {
                                imgClassRisco.setImageResourceReference(Resources.Images.BALL_GREEN.resourceReference());
                            } else if (ClassificacaoRisco.NivelGravidade.AMARELO.value().equals(dto.getClassificacaoRisco().getNivelGravidade())) {
                                imgClassRisco.setImageResourceReference(Resources.Images.BALL_YELLOW.resourceReference());
                            } else if (ClassificacaoRisco.NivelGravidade.VERMELHO.value().equals(dto.getClassificacaoRisco().getNivelGravidade())) {
                                imgClassRisco.setImageResourceReference(Resources.Images.BALL_RED.resourceReference());
                            } else if (ClassificacaoRisco.NivelGravidade.LARANJA.value().equals(dto.getClassificacaoRisco().getNivelGravidade())) {
                                imgClassRisco.setImageResourceReference(Resources.Images.BALL_ORANGE.resourceReference());
                            } else if (ClassificacaoRisco.NivelGravidade.AZUL.value().equals(dto.getClassificacaoRisco().getNivelGravidade())) {
                                imgClassRisco.setImageResourceReference(Resources.Images.BALL_BLUE.resourceReference());
                            } else if (ClassificacaoRisco.NivelGravidade.CINZA.value().equals(dto.getClassificacaoRisco().getNivelGravidade())) {
                                imgClassRisco.setImageResourceReference(Resources.Images.BALL_GRAY.resourceReference());
                            }
                        } else {
                            imgClassRisco.setImageResourceReference(Resources.Images.BALL_GRAY.resourceReference());
                        }
                        target.add(imgClassRisco);

                        target.add(form);

                        String evento = html5Audio.getMarkupId() + ".play();"
                                + "var atencao = setInterval(function() {"
                                + "$('#" + paciente.getMarkupId() + "').animate({opacity:0}, 100, \"linear\", function(){"
                                + "    $(this).delay(200);"
                                + "    $(this).animate({opacity:1}, 300, function(){});"
                                + "    $(this).delay(600);"
                                + "});"
                                + "},1500);"
                                + "setTimeout(function( ) { clearInterval(atencao); }, 15000);";

                        target.appendJavaScript(evento);

                        try {
                            String utilizaVozPainel = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("utilizaVozPainel");

                            if (RepositoryComponentDefault.SIM.equals(utilizaVozPainel)) {

                                String chave = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("chaveAcessoIdPainel");
                                String senha = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("senhaAcessoIdPainel");

                                String vozPainel = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("vozPainel");

                                if (RepositoryComponentDefault.SEXO_MASCULINO.equals(vozPainel)) {

                                    vozPainel = "Ricardo";
                                } else {
                                    vozPainel = "Vitoria";
                                }

                                StringBuilder sbTexto = new StringBuilder();

                                sbTexto.append("Paciente, ");
                                sbTexto.append(dto.getPaciente());
                                sbTexto.append(", compareça a ");
                                sbTexto.append(dto.getDescricaoSala());

                                Algorithm algorithm = Algorithm.HMAC256("#<EMAIL>!");

                                String valueJWT = JWT.create()
                                        .withClaim("chave", chave)
                                        .withClaim("senha", senha)
                                        .withClaim("texto", sbTexto.toString())
                                        .withClaim("voz", vozPainel)
                                        .sign(algorithm);

                                StringBuilder sbPapagaio = new StringBuilder();

                                sbPapagaio.append((String)BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("URL_Papagaio"));
                                sbPapagaio.append("/papagaio/fala/falar");
                                sbPapagaio.append("?value=");
                                sbPapagaio.append(encryptAESPapagaio(valueJWT));

                                html5Audio2.add(new AttributeModifier("src", sbPapagaio.toString()));

                                target.add(html5Audio2);

                                target.appendJavaScript(html5Audio2.getMarkupId() + ".play();");
                            } else {
                                html5Audio2.add(new AttributeModifier("src", ""));
                                target.appendJavaScript(html5Audio2.getMarkupId());
                            }
                        } catch (Exception ex) {
                            Logger.getLogger(Painel1Page.class.getName()).log(Level.SEVERE, null, ex);
                        }
                    }
                }
            }
        };

        form.add(behavior);
        form.add(table = new Table("table", getColumns(), getCollectionProvider()));

        table.populate();

        add(form);
    }

    private void verificaUsoClassificacaoRisco() {
        String verificaParametroClassificacaoRisco = "";
        try {
            verificaParametroClassificacaoRisco = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("exibeClassificaoRiscoPainel");
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        usaClassificacaoRisco = RepositoryComponentDefault.SIM.equals(verificaParametroClassificacaoRisco);
    }

    private String encryptAESPapagaio(String value) throws Exception {

        byte[] SECRET_KEY_VALUE = new byte[] { '#', 'C', 'L', 'K', '2', '0', '1', '8', '@', 'P', 'p', 'g', '.', 'a', 'e','s' };
        String ALGORITHM = "AES";

        Key key = new SecretKeySpec(SECRET_KEY_VALUE, ALGORITHM);

        Cipher c = Cipher.getInstance(ALGORITHM);

        c.init(Cipher.ENCRYPT_MODE, key);

        byte[] encVal = c.doFinal(value.getBytes());

        return new String(Base64.encodeBase64URLSafe(encVal));
    }

    public List<IColumn> getColumns() {
        PainelDTO on = on(PainelDTO.class);

        List columns = new ArrayList<IColumn>();
        if (usaClassificacaoRisco) {
            columns.add(new ClassificacaoRiscoColumnPainel("CR", path(on.getClassificacaoRisco())));

            if(Atendimento.ParamGemAtedimento.SENHA.descricao().equalsIgnoreCase(paramChamarUtilizando)){
                columns.add(createColumn("SENHAS CHAMADAS", on.getPaciente().replaceAll("SENHA","")));
            }else{
                columns.add(createColumn("PACIENTES CHAMADOS", on.getPaciente()));
            }
            columns.add(new LineBreakColumn<String>("", path(on.getDescricaoAtendimento())));
            columns.add(new TimeColumn("HORA CHAMADA", path(on.getDataChamado())));
        } else {
            if(Atendimento.ParamGemAtedimento.SENHA.descricao().equalsIgnoreCase(paramChamarUtilizando)){
                columns.add(createColumn("SENHAS CHAMADAS", on.getPaciente().replaceAll("SENHA","")));
            }else{
                columns.add(createColumn("PACIENTES CHAMADOS", on.getPaciente()));
            }
            columns.add(new LineBreakColumn<String>("", path(on.getDescricaoAtendimento())));
        }
        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return ultimosChamados;
            }
        };
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(JavaScriptHeaderItem.forReference(Application.get().getJavaScriptLibrarySettings().getJQueryReference()));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_MIGRATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DATA_TABLE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_VALIDATE));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_MASKEDINPUT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_SHORTCUTS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_TREEVIEW));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JGROWL));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_JQUERY_PRINT_ELEMENT));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_DIRTY_FORMS));
        response.render(JavaScriptHeaderItem.forReference(Resources.JS_FORM));
        response.render(CssHeaderItem.forReference(Resources.CSS_GEM_SAUDE));
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), "painel1.css")));

        if (Painel.TipoDispositivo.COMPUTADOR.value().equals(tipoDispositivo)){
            response.render(JavaScriptHeaderItem.forReference(Resources.JS_IDENTIFY_INTERNET_CONNECTION));
            response.render(JavaScriptHeaderItem.forReference(Resources.JS_REGISTER_WEB_SOCKET_PANEL));

            response.render(OnLoadHeaderItem.forScript(scriptWebSocketPainelComputador()));
            response.render(OnLoadHeaderItem.forScript("new IdentifyInternetConnection();"));

        } else if (Painel.TipoDispositivo.SMART_TV.value().equals(tipoDispositivo)) {
            response.render(OnLoadHeaderItem.forScript(geraScriptWebSocketSmartTV()));
        }
    }

    public String scriptWebSocketPainelComputador(){
        String urlPapaleguas = getUrlPapaleguas();
        urlPapaleguas = Coalesce.asString(urlPapaleguas).replaceAll("http", "ws");
        String clientId = TenantContext.getContext();

        String behaviorCallBackFunction = behavior.getCallbackFunction(CallbackParameter.explicit("message")).toString();

        String callbackFunctionBody = behaviorPing.getCallbackFunctionBody().toString();
        String paramsBehaviorPing = callbackFunctionBody.substring(callbackFunctionBody.indexOf("{"), callbackFunctionBody.indexOf("}") + 1);

        String script =
                "new RegisterWebSocketPanel(" +
                        "'" + urlPapaleguas + "'," +
                        "'" + clientId + "'," +
                        "'" + chave + "'," +
                        behaviorCallBackFunction +
                        "," + paramsBehaviorPing +
                        ");";

        return script;
    }

    public String getUrlPapaleguas(){
        if(ultimaConsulta == null || ultimaConsulta.before(Data.removeMinutos(DataUtil.getDataAtual(), 30))){
            try {
                urlPapaleguas = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("URL_Papaleguas");
            } catch (DAOException ex) {
                urlPapaleguas = "";
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return urlPapaleguas;
    }

    public String geraScriptWebSocketSmartTV(){
        String urlPapaleguas = getUrlPapaleguas();
        urlPapaleguas = Coalesce.asString(urlPapaleguas).replaceAll("http", "ws");
        String clientId = TenantContext.getContext();

        String script = ""
                + " var url = '"+ urlPapaleguas +"/papaleguas/websocket/PAINEL/" + clientId + "/"+chave+"';"
                + " var ws = new WebSocket(url);"
                + " var fila = [];"
                + " function esvaziaFila() {"
                + "     while ( fila.length > 0 ) {"
                + "         if ( !podeProcessar )"
                + "             return;"
                + "         var proximo = fila.shift();"
                + "         fila.unshift(proximo); "
                + "         if ( proximo.recebimento )"
                + "              processar(proximo.evento);"
                + "         setTimeout(function(){ fila.shift(); }, 7000);"
                + "     }"
                + " }"
                + " var podeProcessar = false;"
                + " var podeProcessarPrimeiraVez = true;"

                + " ws.onopen = function () { "
                + "     if (podeProcessarPrimeiraVez) {"
                + "         podeProcessar = true;"
                + "         podeProcessarPrimeiraVez = false;"
                + "     }"
                + " };"

                + " ws.onmessage = function (message) {"
                + "     var permiteInserir = true;"
                + "     for(var i = 0; i < fila.length; i++) {"
                + "         if (fila[i].evento.data === { recebimento:true, evento:message }.evento.data) {"
                + "            permiteInserir = false; "
                + "         }"
                + "     }"
                + "     if(permiteInserir) {"
                + "         fila.push({ recebimento:true, evento:message });"
                + "         esvaziaFila();"
                + "     }"
                + " };"

                + " function processar(evento) {"
                + "     var behavior = " + behavior.getCallbackFunction(CallbackParameter.explicit("message")).toString()
                + "     behavior(evento.data);"
                + "     podeProcessar = false;"
                + "     tarefaDemorada(evento);"
                + " }"

                + " setInterval(function() { ws.onopen(); }, 500);"
                + " setInterval(function() { ws.send('a'); }, 50000);"
                + " setInterval(function( ) {" + behaviorPing.getCallbackScript() + "}, 15000);" //A cada 15s. faz uma requisicao para nao cair a sessao.

                + " function tarefaDemorada(evento) {"
                + "    setTimeout(function() {"
                + "        podeProcessar = true;"
                + "        esvaziaFila();"
                + "    }, 10000);"
                + " }";

        return script;
    }

}
