package br.com.celk.view.hospital.quartosleitos;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroQuartosLeitosDetalhes extends BasePage {

    private QuartoInternacao quartoInternacao;
    private Table<LeitoQuarto> table;
    private List<LeitoQuarto> lstLeitoQuarto = new ArrayList<LeitoQuarto>();

    public CadastroQuartosLeitosDetalhes(QuartoInternacao quartoInternacao) {
        this.quartoInternacao = quartoInternacao;
        init();
        carregaLeitos();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel<QuartoInternacao>(new LoadableObjectModel<QuartoInternacao>(QuartoInternacao.class, quartoInternacao)));

        form.add(new DisabledInputField(QuartoInternacao.PROP_REFERENCIA));
        form.add(new DisabledInputField(QuartoInternacao.PROP_DESCRICAO));
        form.add(new DisabledInputField(VOUtils.montarPath(QuartoInternacao.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        form.add(new DisabledInputField(VOUtils.montarPath(QuartoInternacao.PROP_DESCRICAO_TIPO_QUARTO)));

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));
        table.populate();
        form.add(new VoltarButton("btnVoltar"));


        add(form);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroLeitoQuarto");
    }

    private void carregaLeitos() {
            lstLeitoQuarto = LoadManager.getInstance(LeitoQuarto.class)
                    .addProperties(new HQLProperties(LeitoQuarto.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(LeitoQuarto.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.DIFERENTE, LeitoQuarto.Situacao.EXCLUIDO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(LeitoQuarto.PROP_QUARTO_INTERNACAO, quartoInternacao))
                    .start().getList();
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstLeitoQuarto;
            }
        };
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        ColumnFactory columnFactory = new ColumnFactory(LeitoQuarto.class);
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("leito"), VOUtils.montarPath(LeitoQuarto.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(LeitoQuarto.PROP_SITUACAO), VOUtils.montarPath(LeitoQuarto.PROP_SITUACAO_DESCRICAO)));
        return columns;
    }
}
