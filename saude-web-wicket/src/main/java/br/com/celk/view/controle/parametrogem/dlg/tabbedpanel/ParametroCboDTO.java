package br.com.celk.view.controle.parametrogem.dlg.tabbedpanel;

import br.com.ksisolucoes.util.parametrogem.ParametroGemHelper;
import br.com.ksisolucoes.vo.basico.ParametroGem;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ParametroCboDTO implements Serializable {

    private TabelaCbo tabelaCbo;
    private ParametroGem parametro;

    public void setValor(Object valor) {
        if (parametro != null) {
            parametro.setValueCache(valor);
            if (valor != null) {
                if (valor instanceof CodigoManager) {
                    parametro.setValor(((CodigoManager) valor).getCodigoManager().toString());
                } else {
                    parametro.setValor(valor.toString());
                }
            } else {
                parametro.setValor(null);
            }
        }
    }

    public Object getValor() {
        if (parametro != null && parametro.getValor() != null) {
            return ParametroGemHelper.getValueTyped(parametro.getType(), parametro.getValor());
        }
        return null;
    }

    public TabelaCbo getTabelaCbo() {
        return tabelaCbo;
    }

    public void setTabelaCbo(TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
    }

    public ParametroGem getParametro() {
        return parametro;
    }

    public void setParametro(ParametroGem parametro) {
        this.parametro = parametro;
    }

}
