package br.com.celk.view.unidadesaude.receituario.customcolumn;

import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.ActionsEnum;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class EmissaoReceituarioColumnPanel extends Panel implements PermissionContainer{

    private AjaxLink btnCancelar;
    
    @Permission(type=Permissions.CONFIRMAR, action=ActionsEnum.RENDER)
    private AjaxLink btnConfirmar;
    
    private AtendimentoDTO atendimentoDTO;
    
    public EmissaoReceituarioColumnPanel(String id, AtendimentoDTO atendimentoDTO) {
        super(id);
        this.atendimentoDTO = atendimentoDTO;
        init();
    }

    private void init() {
        add(btnConfirmar = new AbstractAjaxLink("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onConfirmar(target);
            }

            @Override
            public boolean isEnabled() {
                return true;
            }
            
        });
        
        add(btnCancelar = new AbstractAjaxLink("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                onCancelar(target);
            }
            
            @Override
            public boolean isEnabled() {
                return Atendimento.STATUS_AGUARDANDO.equals(atendimentoDTO.getAtendimento().getStatus()) || Atendimento.STATUS_PENDENTE_LANCAMENTO_PRODUCAO.equals(atendimentoDTO.getAtendimento().getStatus());
            }
        });

        btnConfirmar.add(new AttributeModifier("title", BundleManager.getString("gerarReceita")));
        btnCancelar.add(new AttributeModifier("title", BundleManager.getString("cancelar")));
    }
    
    public AjaxLink getBtnConfirmar() {
        return btnConfirmar;
    }
    
    public abstract void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
}
