package br.com.celk.view.unidadesaude.exames.ppi;

import br.com.celk.agendamento.ppi.dto.PpiSecretariaDTO;
import br.com.celk.agendamento.ppi.dto.QueryPagerPpiDTOParam;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.microrregiao.autocomplete.AutoCompleteConsultaMicrorregiao;
import br.com.celk.view.basico.regionalsaude.autocomplete.AutoCompleteConsultaRegionalSaude;
import br.com.celk.view.unidadesaude.exames.ppi.dialog.DlgAbrirCompetencias;
import br.com.celk.view.unidadesaude.exames.ppi.dialog.DlgAnteciparSaldo;
import br.com.celk.view.unidadesaude.exames.ppi.dialog.DlgCopiarPpi;
import br.com.celk.view.unidadesaude.exames.ppi.dialog.DlgDecrementarSaldo;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Microrregiao;
import br.com.ksisolucoes.vo.basico.RegionalSaude;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> | Beth
 */
@Private
public class ConsultaPpiPage extends ConsultaPage<PpiSecretariaDTO, QueryPagerPpiDTOParam> {

    private Empresa empresa;
    private Date competencia;
    private String cnes;
    private Double valor;
    private PpiSecretariaDTO dto;
    private Long classificacao;
    private DlgCopiarPpi dlgCopiarPpi;
    private DlgAnteciparSaldo dlgAnteciparSaldo;
    private DlgDecrementarSaldo dlgDecrementarSaldo;
    private PageParameters parameters;
    private DatePeriod periodo;
    private DlgAbrirCompetencias dlgAbrirCompetencias;
    private Component abrirCompetencias;
    private RegionalSaude regionalSaude;
    private Microrregiao microrregiao;
    private AutoCompleteConsultaRegionalSaude autoCompleteConsultaRegionalSaude;
    private AutoCompleteConsultaMicrorregiao autoCompleteConsultaMicrorregiao;
    private String descricao;

    public ConsultaPpiPage(PageParameters parameters) {
        super(parameters);
        this.parameters = parameters;
    }

    public ConsultaPpiPage() {
        this(new PageParameters());
    }

    @Override
    public void initForm(Form form) {
        getLinkNovo().setEnabled(isActionPermitted(Permissions.CADASTRAR));
        getControls().add(abrirCompetencias = new AbstractAjaxButton(getControls().newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                initDlgAbrirCompetencia(target);
            }
        });
        abrirCompetencias.add(new AttributeModifier("value", BundleManager.getString("abrirCompetencias")));
        abrirCompetencias.setEnabled(isActionPermitted(Permissions.ABRIR_COMPETENCIAS));
        form.setDefaultModel(new CompoundPropertyModel(this));
        boolean isPermissaoEmpresa = isActionPermitted(Permissions.EMPRESA);
        form.add(new AutoCompleteConsultaEmpresa("empresa")
                .setValidaUsuarioEmpresa(!isPermissaoEmpresa)
                .setTiposEstabelecimento(Collections.singletonList(TipoEstabelecimento.SECRETARIA_SAUDE.value()))
        );
        form.add(new PnlDatePeriod("periodo"));
        form.add(DropDownUtil.getIEnumDropDown("classificacao",
                                                    TipoProcedimentoClassificacao.Classificacao.values(),
                                            true,
                                                    BundleManager.getString("todos")));

        form.add(autoCompleteConsultaRegionalSaude = new AutoCompleteConsultaRegionalSaude("regionalSaude"));
        form.add(autoCompleteConsultaMicrorregiao = new AutoCompleteConsultaMicrorregiao("microrregiao"));

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        PpiSecretariaDTO proxy = on(PpiSecretariaDTO.class);
        columns.add(getActionColumn());
        DateColumn dateColumn = (DateColumn) createSortableColumn(bundle("competencia"), proxy.getDtCompetencia());
        columns.add(dateColumn.setPattern("MM/yyyy"));
        columns.add(createSortableColumn(bundle("rotulo_cnes"), proxy.getCnesSecretaria()));
        columns.add(createSortableColumn(bundle("secretaria"), proxy.getNomeSecretaria(), proxy.getNomeSecretaria()));
        columns.add(createSortableColumn(bundle("classificacao"), proxy.getDescricaoClassificacao()));
        columns.add(new DoubleColumn(bundle("vlr_global_financeiro"), path(proxy.getVlGlobal())));
        columns.add(new DoubleColumn(bundle("vlr_compartilhado"), path(proxy.getVlCompartilhado())));
        columns.add(new DoubleColumn(bundle("vlr_compartilhado_usado"), path(proxy.getVlCompartilhadoUsado())));
        columns.add(new DoubleColumn(bundle("vlr_compartilhado_saldo"), path(proxy.getSaldoCompartilhado())));
        columns.add(new DoubleColumn(bundle("vlr_alocado"), path(proxy.getVlAlocado())));
        columns.add(new DoubleColumn(bundle("vlr_alocado_usado"), path(proxy.getVlAlocadoUsado())));
        columns.add(new DoubleColumn(bundle("vlr_alocado_saldo"), path(proxy.getSaldoAlocado())));
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<PpiSecretariaDTO>() {
            @Override
            public void customizeColumn(PpiSecretariaDTO ppiSecretariaDTO) {
                addAction(ActionType.CLONAR, ppiSecretariaDTO, new IModelAction<PpiSecretariaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, PpiSecretariaDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgCopiarPpi(target, modelObject);
                    }
                }).setTitleBundleKey("copiar")
                .setEnabled(hasPermissao(Permissions.CLONAR));

                addAction(ActionType.CONSULTAR, ppiSecretariaDTO, new IModelAction<PpiSecretariaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, PpiSecretariaDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPpiPage(ppiSecretariaDTO, true));
                    }
                }).setEnabled(hasPermissao(Permissions.CONSULTAR));

                addAction(ActionType.EDITAR, ppiSecretariaDTO, new IModelAction<PpiSecretariaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, PpiSecretariaDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPpiPage(ppiSecretariaDTO, false));
                    }
                }).setTitleBundleKey("editar")
                .setEnabled(ehCompetenciaAtualOuPosterior(ppiSecretariaDTO.getDtCompetencia()) && hasPermissao(Permissions.EDITAR));

                addAction(ActionType.REMOVER, ppiSecretariaDTO, new IModelAction<PpiSecretariaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, PpiSecretariaDTO modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(ExameFacade.class).deletarPpiSecretaria(ppiSecretariaDTO);
                        getPageableTable().populate(target);
                    }
                }).setEnabled(semAgendamento(ppiSecretariaDTO) && hasPermissao(Permissions.DELETAR));

                addAction(ActionType.HISTORICO, ppiSecretariaDTO, new IModelAction<PpiSecretariaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, PpiSecretariaDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new ConsultaOcorrenciaPpiPage(parameters, ppiSecretariaDTO));
                    }
                }).setTitleBundleKey("ocorrencias")
                  .setEnabled(hasPermissao(Permissions.OCORRENCIA));

                addAction(ActionType.ANTECIPAR_SALDO, ppiSecretariaDTO, new IModelAction<PpiSecretariaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, PpiSecretariaDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgAnteciparSaldo(target, modelObject);
                    }
                }).setTitleBundleKey("anteciparSaldo")
                        .setEnabled(ehCompetenciaAtualOuPosterior(ppiSecretariaDTO.getDtCompetencia()) && hasPermissao(Permissions.ANTECIPAR_SALDO));

                addAction(ActionType.DECREMENTAR_SALDO, ppiSecretariaDTO, new IModelAction<PpiSecretariaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, PpiSecretariaDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgDecrementarSaldo(target, modelObject);
                    }
                }).setTitleBundleKey("decrementarSaldo")
                        .setEnabled(ehCompetenciaAtualOuPosterior(ppiSecretariaDTO.getDtCompetencia()) && hasPermissao(Permissions.DECREMENTAR_SALDO));
            }
        };
    }

    private void initDlgCopiarPpi(AjaxRequestTarget target, PpiSecretariaDTO ppiSecretariaDTO) {
        addModal(target, dlgCopiarPpi = new DlgCopiarPpi(newModalId(), ppiSecretariaDTO) {
            @Override
            public void onOk(AjaxRequestTarget target, PpiSecretariaDTO dto, Date dataInicioCopia, Date dataFimCopia) throws DAOException, ValidacaoException {
                copiarPpi(dto, dataInicioCopia, dataFimCopia);
                getPageableTable().populate(target);
                dlgCopiarPpi = null;
                this.close(target);
            }
        });
        dlgCopiarPpi.show(target);
    }

    private void initDlgAnteciparSaldo(AjaxRequestTarget target, PpiSecretariaDTO ppiSecretariaDTO) {
        addModal(target, dlgAnteciparSaldo = new DlgAnteciparSaldo(newModalId(), ppiSecretariaDTO) {
            @Override
            public void onOk(AjaxRequestTarget target, PpiSecretariaDTO model, Double valor) throws ValidacaoException, DAOException {
                try {
                    String mensagem = anteciparSaldo(model, valor);
                    MessageUtil.info(target, this, mensagem);
                    getPageableTable().populate(target);
                    dlgAnteciparSaldo = null;
                } catch (ValidacaoException e) {
                    MessageUtil.error(target, this, e.getMessage());
                }

                this.close(target);
            }
        });

        dlgAnteciparSaldo.show(target);
    }

    private void initDlgDecrementarSaldo(AjaxRequestTarget target, PpiSecretariaDTO ppiSecretariaDTO) {
        addModal(target, dlgDecrementarSaldo = new DlgDecrementarSaldo(newModalId(), ppiSecretariaDTO) {
            @Override
            public void onOk(AjaxRequestTarget target, PpiSecretariaDTO model, Double valor, String descricao) throws ValidacaoException, DAOException {
                String mensagem = decrementarSaldo(model, valor, descricao);
                MessageUtil.info(target, this, mensagem);
                getPageableTable().populate(target);
                dlgDecrementarSaldo = null;

                this.close(target);
            }
        });

        dlgDecrementarSaldo.show(target);
    }

    private void initDlgAbrirCompetencia(AjaxRequestTarget target) {
        addModal(target, dlgAbrirCompetencias = new DlgAbrirCompetencias(newModalId()) {
            @Override
            public void onOk(AjaxRequestTarget target, Date dataReferenciaCopia, Long classificacao, Date dataInicioCopia, Date dataFimCopia) throws DAOException, ValidacaoException {
                copiarPpis(dataReferenciaCopia, classificacao, dataInicioCopia, dataFimCopia);
                getPageableTable().populate(target);
                dlgAbrirCompetencias = null;
                this.close(target);
            }
        });
        dlgAbrirCompetencias.show(target);
    }

    private String anteciparSaldo(PpiSecretariaDTO dto, Double valor) throws DAOException, ValidacaoException {
        return BOFactoryWicket.getBO(ExameFacade.class).anteciparSaldo(dto, valor);
    }

    private String decrementarSaldo(PpiSecretariaDTO dto, Double valor, String descricao) throws DAOException, ValidacaoException {
        return BOFactoryWicket.getBO(ExameFacade.class).decrementarSaldo(dto, valor, descricao);
    }

    private void copiarPpi(PpiSecretariaDTO dto, Date dataInicioCopia, Date dataFimCopia) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(ExameFacade.class).copiarPpi(dto, dataInicioCopia, dataFimCopia);
    }

    private void copiarPpis(Date dataReferenciaCopia, Long classificacao, Date dataInicioCopia, Date dataFimCopia) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(ExameFacade.class).copiarPpis(dataReferenciaCopia, classificacao, dataInicioCopia, dataFimCopia);
    }
    private boolean ehCompetenciaAtualOuPosterior(Date competencia) {
        return !Data.competenciaData(1, competencia).before(Data.competenciaData(1, DataUtil.getDataAtualSemHora()));
    }

    private boolean hasPermissao(Permissions permissao) {
        return isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), permissao, ConsultaPpiPage.class);
    }

    private boolean semAgendamento(PpiSecretariaDTO ppiSecretariaDTO) {
        return ppiSecretariaDTO.getVlUsadoTotal() <= 0D;
    }

    private void dataInputValidation(QueryPagerPpiDTOParam param) throws ValidacaoException {
        if (param.getPeriodo() != null && param.getPeriodo().getDataInicial() == null && param.getPeriodo().getDataFinal() != null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informar_data_inicial"));
        }
    }

    @Override
    public void getAntesProcurar() throws ValidacaoException {
        super.getAntesProcurar();
        dataInputValidation(this.getParameters());
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<PpiSecretariaDTO, QueryPagerPpiDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging<QueryPagerPpiDTOParam> dataPaging) throws DAOException, ValidacaoException {
                boolean isVisualizaSomenteUnidade = !isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.EMPRESA, ConsultaPpiPage.class);
                if (isVisualizaSomenteUnidade) dataPaging.getParam().setSecretaria(SessaoAplicacaoImp.getInstance().getEmpresa());
                return BOFactoryWicket.getBO(ExameFacade.class).consultarPpiPager(dataPaging);
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam("ppiSecretaria.dtCompetencia", true);
            }

            @Override
            public void customizeParam(QueryPagerPpiDTOParam param) {
                param.setPropSort(getSort().getProperty());
                param.setAscending(getSort().isAscending());
            }
        };
    }

    @Override
    public QueryPagerPpiDTOParam getParameters() {
        return new QueryPagerPpiDTOParam(empresa, competencia, cnes, periodo, classificacao, microrregiao, regionalSaude);
    }

    @Override
    public Class getCadastroPage() {
        return CadastroPpiPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("" +
                "consultaCotaExameRecebidoPpi");
    }
}
