package br.com.celk.view.comunicacao.mensagem.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.panel.ViewPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.IPagingBar;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.resources.Icon16;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.command.QueryCustom;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.comunicacao.mensagem.IMensagemController;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import br.com.celk.view.comunicacao.mensagem.table.MessagingPagingBar;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTO;
import br.com.ksisolucoes.bo.comunicacao.interfaces.dto.QueryConsultaMensagensDTOParam;
import br.com.ksisolucoes.bo.comunicacao.interfaces.facade.ComunicacaoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.Mensagem;
import br.com.ksisolucoes.vo.comunicacao.MensagemAnexo;
import br.com.ksisolucoes.vo.controle.Usuario;
import static ch.lambdaj.Lambda.*;
import java.util.Collection;
import java.util.Collections;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public class EnviadosPanel extends ViewPanel implements IMensagemPanel{

    private final String VIEW_PANEL_ID = "viewPanel";
    
    private WebMarkupContainer viewContainer;
    private SelectionPageableTable<QueryConsultaMensagensDTO> pageableTable;
    private Table<QueryConsultaMensagensDTO> tblUsuarios;
    private Panel activePanel;
    private IMensagemController mensagemController;
    
    private String usuario;
    
    public EnviadosPanel(String id) {
        super(id);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        
        Form form = new Form("form");
        
        form.add(new InputField("filtroUsuario", new PropertyModel<String>(this, "usuario")));
        
        form.add(pageableTable = new SelectionPageableTable("tblMensagens", getColumns(), getPagerProvider(), 50){

            @Override
            public IPagingBar newPagingBar(String id, PageableTable pageableTable) {
                return new MessagingPagingBar(id, pageableTable);
            }

            @Override
            public void update(AjaxRequestTarget target) {
                super.update(target); //To change body of generated methods, choose Tools | Templates.
                tblUsuarios.limpar(target);
                Component get = viewContainer.get(VIEW_PANEL_ID);
                get.replaceWith(new EmptyPanel(VIEW_PANEL_ID));
                target.add(viewContainer);
            }

        });
        pageableTable.setScrollY("200px");
        pageableTable.setScrollCollapse(false);
        pageableTable.populate();
        
        form.add(tblUsuarios = new Table("tblUsuarios", getColumnsUsuarios(), getCollectionProviderUsuarios()));
        tblUsuarios.setScrollY("225px");
        tblUsuarios.setScrollCollapse(false);
        
        pageableTable.addSelectionAction(new ISelectionAction<QueryConsultaMensagensDTO>() {

            @Override
            public void onSelection(AjaxRequestTarget target, QueryConsultaMensagensDTO object) {
                tblUsuarios.populate(target);
                visualizarMensagem(target, object);
            }
        });
        
        ProcurarButton procurarButton;
        form.add(procurarButton = new ProcurarButton("btnProcurar", pageableTable) {
            @Override
            public Object getParam() {
                return getParameter();
            }
        });
        procurarButton.procurar();
        
        add(viewContainer = new WebMarkupContainer("viewContainer"));
        viewContainer.setOutputMarkupId(true);
        
        if (activePanel==null) {
            activePanel = new EmptyPanel(VIEW_PANEL_ID);
        }
        
        viewContainer.add(activePanel);
        
        if (activePanel instanceof VisualizarMensagemPanel) {
            pageableTable.setSelectedObject(((VisualizarMensagemPanel)activePanel).getDTO());
        }
        
        add(form);
    }
    
    public void setMensagem(QueryConsultaMensagensDTO dto){
        VisualizarMensagemPanel visualizarMensagemPanel = new VisualizarMensagemPanel(VIEW_PANEL_ID, dto);
        activePanel = visualizarMensagemPanel;
        visualizarMensagemPanel.setMensagemController(mensagemController);
    }
    
    private void visualizarMensagem(QueryConsultaMensagensDTO dto){
        Component get = viewContainer.get(VIEW_PANEL_ID);
        VisualizarMensagemPanel panel;
        
        dto.setTipoMensagem(QueryConsultaMensagensDTO.PROP_ENVIADOS);
        get.replaceWith(panel = new VisualizarMensagemPanel(VIEW_PANEL_ID, dto));
        
        panel.setMensagemController(mensagemController);
    }
    
    private void visualizarMensagem(AjaxRequestTarget target, QueryConsultaMensagensDTO dto){
        visualizarMensagem(dto);

        target.add(viewContainer);
    }

    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        QueryConsultaMensagensDTO on = on(QueryConsultaMensagensDTO.class);
        
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("assunto"), on.getMensagem().getAssunto()));
        columns.add(new DateColumn(bundle("data"), path(on.getMensagem().getData()), path(on.getMensagem().getData())).setPattern("dd/MM/yyyy - HH:mm"));
        
        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<QueryConsultaMensagensDTO>() {

            @Override
            public void customizeColumn(QueryConsultaMensagensDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<QueryConsultaMensagensDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, QueryConsultaMensagensDTO modelObject) throws ValidacaoException, DAOException {
                        modelObject.getMensagem().setExcluida(RepositoryComponentDefault.SimNaoLong.SIM.value());
                        modelObject.setMensagem(BOFactoryWicket.save(modelObject.getMensagem()));
                        mensagemController.notificarMensagem(target, modelObject);
                        atualizarMensagens(target);
                    }
                }).setIcon(Icon16.cross);
            }
        };
    }
    
    private IPagerProvider getPagerProvider(){
        return new QueryPagerProvider<QueryConsultaMensagensDTO, QueryConsultaMensagensDTOParam>() {

            @Override
            public DataPagingResult executeQueryPager(DataPaging<QueryConsultaMensagensDTOParam> dataPaging) throws DAOException, ValidacaoException {
                dataPaging.getParam().setPropOrdenacao(((SingleSortState<String>)getSortState()).getSort().getProperty());
                dataPaging.getParam().setAsc(((SingleSortState<String>)getSortState()).getSort().isAscending());
                
                return BOFactoryWicket.getBO(ComunicacaoFacade.class).consultarMensagens(dataPaging);
            }
            
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(path(on(QueryConsultaMensagensDTO.class).getMensagem().getData()), false);
            }
        };
    }
    
    private QueryConsultaMensagensDTOParam getParameter(){
        QueryConsultaMensagensDTOParam param = new QueryConsultaMensagensDTOParam();
        
        param.setTipo(Mensagem.Tipo.SAIDA);
        param.setPara(usuario);
        param.setExcluida(false);
        param.setUsuario(ApplicationSession.get().getSession().<Usuario>getUsuario());
        
        return param;
    }

    private List<IColumn> getColumnsUsuarios(){
        List<IColumn> columns = new ArrayList<IColumn>();
        QueryConsultaMensagensDTO on = on(QueryConsultaMensagensDTO.class);
        
        columns.add(createSortableColumn(bundle("usuario"), on.getMensagem().getUsuario().getNome()));
        columns.add(createSortableColumn(bundle("lido"), on.getMensagem().getLida(), on.getMensagem().getLidoFormatado()));
        columns.add(new DateColumn(bundle("data"), path(on.getMensagem().getDataLeitura()), path(on.getMensagem().getDataLeitura())).setPattern("dd/MM/yyyy - HH:mm"));
        
        return columns;
    }
    
    private ICollectionProvider getCollectionProviderUsuarios(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (pageableTable.getSelectedObject()!=null) {
                    String propSort = ((SingleSortState<String>)getSortState()).getSort().getProperty();
                    boolean asc = ((SingleSortState<String>)getSortState()).getSort().isAscending();
                    List<Mensagem> mensagemList =  LoadManager.getInstance(Mensagem.class)
                            .addProperties(new HQLProperties(Mensagem.class).getProperties())
                            .addProperties(new HQLProperties(Mensagem.class, Mensagem.PROP_MENSAGEM_ORIGEM).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(Mensagem.PROP_MENSAGEM_ORIGEM, pageableTable.getSelectedObject().getMensagem()))
                            .addSorter(new QueryCustom.QueryCustomSorter(propSort, asc? BuilderQueryCustom.QuerySorter.CRESCENTE : BuilderQueryCustom.QuerySorter.DECRESCENTE))
                            .start().getList();
                    
                    QueryConsultaMensagensDTO dto;
                    List<QueryConsultaMensagensDTO> dtoList = new ArrayList<QueryConsultaMensagensDTO>();
                    
                    for(Mensagem m : mensagemList){
                        Long qtdMensagemAnexo = LoadManager.getInstance(MensagemAnexo.class)
                            .addGroup(new QueryCustom.QueryCustomGroup(MensagemAnexo.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                            .addParameter(new QueryCustom.QueryCustomParameter(MensagemAnexo.PROP_MENSAGEM, m.getMensagemOrigem()))
                            .start().getVO();
                        
                        dto = new QueryConsultaMensagensDTO();
                        dto.setMensagem(m);
                        dto.setQuantidadeAnexo(qtdMensagemAnexo);
                        
                        dtoList.add(dto);
                    }
                    return dtoList;
                }
                return Collections.EMPTY_LIST;
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam<String>(path(on(QueryConsultaMensagensDTO.class).getMensagem().getUsuario().getNome()), true);
            }
        };
    }

    @Override
    public void setMensagemController(IMensagemController mensagemController) {
        this.mensagemController = mensagemController;
    }

    @Override
    public void atualizarMensagens(AjaxRequestTarget target) {
        pageableTable.update(target);
    }
    
    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
    }
    
}
