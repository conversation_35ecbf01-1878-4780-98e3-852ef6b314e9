package br.com.celk.view.hospital.faturamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.LoadInterceptorExistsOrNotExistsSubContas;
import br.com.ksisolucoes.bo.hospital.interfaces.dto.SepararContasMescladasPacienteDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class SepararContasMescladasPacienteStep2Page extends BasePage {

    private SepararContasMescladasPacienteDTO separarContasMescladasPacienteDTO;
    private Form<SepararContasMescladasPacienteDTO> form;
    private SelectionTable<ContaPaciente> table;

    public SepararContasMescladasPacienteStep2Page(SepararContasMescladasPacienteDTO separarContasMescladasPacienteDTO) {
        this.separarContasMescladasPacienteDTO = separarContasMescladasPacienteDTO;
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(separarContasMescladasPacienteDTO));

        form.add(table = new SelectionTable("table", getColumns(), getCollectionProvider()));
        table.populate();

        table.addSelectionAction(new ISelectionAction<ContaPaciente>() {
            @Override
            public void onSelection(AjaxRequestTarget target, ContaPaciente contaPaciente) {
                avancar(contaPaciente);
            }
        });

        form.add(new VoltarButton("btnVoltar"));

        add(form);
    }

    private List<IColumn> getColumns() {
        ContaPaciente proxy = on(ContaPaciente.class);

        List<IColumn> columns = new ArrayList();

        columns.add(createColumn(bundle("atendimento"), proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getCodigo()));
        columns.add(createColumn(bundle("conta"), proxy.getCodigo()));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataChegada"), path(proxy.getAtendimentoInformacao().getDataChegada())));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataAbertura"), path(proxy.getDataAbertura())));
        columns.add(createColumn(bundle("convenio"), proxy.getConvenio().getDescricao()));
        columns.add(new DateColumn(bundle("competencia"), path(proxy.getCompetenciaAtendimento())).setPattern("MM/yyyy"));
        columns.add(createColumn(bundle("tipoAtendimento"), proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getDescricao()));
        columns.add(new DateTimeColumn<AtendimentoInformacao>(bundle("dataSaida"), path(proxy.getAtendimentoInformacao().getDataSaida())));

        return columns;
    }

    private void avancar(ContaPaciente contaPacienteSelecionada) {
        separarContasMescladasPacienteDTO.setContaPacientePrincipal(contaPacienteSelecionada);
        setResponsePage(new SepararContasMescladasPacienteStep3Page(separarContasMescladasPacienteDTO));
    }

    private List<ContaPaciente> getLstContasPaciente() {
        List<ContaPaciente> lstContaPaciente;

        ContaPaciente proxy = on(ContaPaciente.class);
        lstContaPaciente = LoadManager.getInstance(ContaPaciente.class)
                .addProperties(new HQLProperties(ContaPaciente.class).getProperties())
                .addProperty(path(proxy.getAtendimentoAlta().getDataAlta()))
                .addProperty(path(proxy.getAtendimentoInformacao().getDataSaida()))
                .addProperty(path(proxy.getAtendimentoInformacao().getDataChegada()))
                .addProperty(path(proxy.getAtendimentoInformacao().getConvenio().getCodigo()))
                .addProperty(path(proxy.getAtendimentoInformacao().getConvenio().getDescricao()))
                .addProperty(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getNome()))
                .addProperty(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getApelido()))
                .addProperty(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getUtilizaNomeSocial()))
                .addProperty(path(proxy.getAtendimentoInformacao().getUsuarioCadsus().getCodigo()))
                .addProperty(path(proxy.getAtendimentoInformacao().getAtendimentoAlta().getCodigo()))
                .addProperty(path(proxy.getAtendimentoInformacao().getAtendimentoPrincipal().getCodigo()))
                .addProperty(path(proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getCodigo()))
                .addProperty(path(proxy.getAtendimentoInformacao().getTipoAtendimentoFaturamento().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getUsuarioCadsus()), separarContasMescladasPacienteDTO.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryRelationKeys(path(proxy.getCodigo()), path(proxy.getContaPacientePrincipal().getCodigo()))))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), QueryCustom.QueryCustomParameter.IN, Arrays.asList(ContaPaciente.Status.ABERTA.value())))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataAbertura()), QueryCustom.QueryCustomSorter.DECRESCENTE_NULLS_FIRST))
                .addInterceptor(new LoadInterceptorExistsOrNotExistsSubContas(true))
                .start().getList();

        return lstContaPaciente;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return getLstContasPaciente();
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("separarContasMescladasPaciente");
    }

    @Override
    protected void onBeforeRender() {
        table.clearSelection();
        super.onBeforeRender();
    }
}
