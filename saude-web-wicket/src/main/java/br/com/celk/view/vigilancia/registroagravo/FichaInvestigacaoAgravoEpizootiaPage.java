package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoEpizootiaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoEpizootia;
import br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoEpizootiaEnum;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class FichaInvestigacaoAgravoEpizootiaPage extends FichaInvestigacaoAgravoBasePage {

    private final String CSSFILE = "FichaInvestigacaoAgravoEpizootiaPage.css";

    private InvestigacaoAgravoEpizootia investigacaoAgravoEpizootia;

    private WebMarkupContainer containerDadosOcorrencia;
    private InputField fonteInformacao;
    private InputField telefoneFonteInformacao;
    private AutoCompleteConsultaCidade autoCompleteMunicipioOcorrencia;
    private DisabledInputField codMunicipioOcorrencia;
    private DisabledInputField estadoMunicipioOcorrencia;
    private InputField distrito;
    private InputField bairro;
    private InputField logradouro;
    private InputField numero;
    private InputField complemento;
    private InputField geocampo1;
    private InputField geocampo2;
    private InputField pontoReferencia;
    private InputField cep;
    private InputField telefone;
    private DropDown ddZona;
    private DropDown ddAmbiente;
    private InputField ambienteOutro;

    private DropDown ddColetaMaterial;
    private DateChooser dataColeta;

    private DropDown ddColetaMaterialFigado;
    private DropDown ddColetaMaterialRim;
    private DropDown ddColetaMaterialBaco;
    private DropDown ddColetaMaterialCerebro;
    private DropDown ddColetaMaterialCoracao;
    private DropDown ddColetaMaterialFezes;
    private DropDown ddColetaMaterialSoro;
    private DropDown ddColetaMaterialSangueTotal;
    private InputField coletaMaterialOutro;

    private DropDown ddAnimaisAcometidosOpcao1;
    private InputField animaisAcometidosOpcao1Doentes;
    private InputField animaisAcometidosOpcao1Mortos;
    private InputField animaisAcometidosOpcao1Especificar;

    private DropDown ddAnimaisAcometidosOpcao2;
    private InputField animaisAcometidosOpcao2Doentes;
    private InputField animaisAcometidosOpcao2Mortos;
    private InputField animaisAcometidosOpcao2Especificar;

    private DropDown ddSuspeitaDiagnostica1;
    private InputField suspeitaDiagnostica1Especificar;

    private DropDown ddSuspeitaDiagnostica2;
    private InputField suspeitaDiagnostica2Especificar;

    private DropDown ddSuspeitaDiagnostica3;
    private InputField suspeitaDiagnostica3Especificar;

    private DropDown ddResultadoLaboralRaiva;
    private DropDown ddResultadoLaboralEncefaliteEquina;
    private DropDown ddResultadoLaboralFebreNilo;
    private DropDown ddResultadoLaboralEncefaliteEspongiformeBovina;
    private DropDown ddResultadoLaboralFebreAmarela;
    private DropDown ddResultadoLaboralInfluenzaAviaria;
    private InputField resultadoLaboralEspecificar;

    private WebMarkupContainer containerObservacoes;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;

    public FichaInvestigacaoAgravoEpizootiaPage(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        investigacaoAgravoEpizootia = InvestigacaoAgravoEpizootia.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (investigacaoAgravoEpizootia == null) {
            investigacaoAgravoEpizootia = new InvestigacaoAgravoEpizootia();
            investigacaoAgravoEpizootia.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            investigacaoAgravoEpizootia.setRegistroAgravo(getAgravo());
        }

        setForm(new Form("form", new CompoundPropertyModel(investigacaoAgravoEpizootia)));
    }

    @Override
    public Object getFichaDTO() {
        FichaInvestigacaoAgravoEpizootiaDTO fichaDTO = new FichaInvestigacaoAgravoEpizootiaDTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.setInvestigacaoAgravoEpizootia(investigacaoAgravoEpizootia);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return investigacaoAgravoEpizootia.getFlagInformacoesComplementares() == null
                ? "S" : investigacaoAgravoEpizootia.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        InvestigacaoAgravoEpizootia proxy = on(InvestigacaoAgravoEpizootia.class);
        criarDadosOcorrencia(proxy);
        criarObservacoes(proxy);
        criarUsuarioDataEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarDadosOcorrencia();
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        FichaInvestigacaoAgravoEpizootiaDTO dto = (FichaInvestigacaoAgravoEpizootiaDTO) fichaDTO;

        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFichaInvestigacaoAgravoEpizootia(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        FichaInvestigacaoAgravoEpizootiaDTO fichaDTO = (FichaInvestigacaoAgravoEpizootiaDTO) getFichaDTO();

        if (investigacaoAgravoEpizootia.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            investigacaoAgravoEpizootia.setUsuarioEncerramento(usuarioLogado);
            investigacaoAgravoEpizootia.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
                CssHeaderItem.forReference(new CssResourceReference(FichaInvestigacaoAgravoEpizootiaPage.class, CSSFILE))
        );
    }

    private void criarDadosOcorrencia(InvestigacaoAgravoEpizootia proxy) {
        containerDadosOcorrencia = new WebMarkupContainer("containerDadosOcorrencia");
        containerDadosOcorrencia.setOutputMarkupId(true);

        fonteInformacao = new InputField(path(proxy.getFonteInformacao()));
        telefoneFonteInformacao = new InputField(path(proxy.getTelefoneFonteInformacao()));
        autoCompleteMunicipioOcorrencia = new AutoCompleteConsultaCidade(path(proxy.getMunicipioOcorrencia()));
        codMunicipioOcorrencia = new DisabledInputField(path(proxy.getMunicipioOcorrencia().getCodigo()));
        estadoMunicipioOcorrencia= new DisabledInputField(path(proxy.getMunicipioOcorrencia().getEstado().getSigla()));
        distrito = new InputField(path(proxy.getDistrito()));
        bairro = new InputField(path(proxy.getBairro()));
        logradouro = new InputField(path(proxy.getLogradouro()));
        numero = new InputField(path(proxy.getNumero()));
        complemento = new InputField(path(proxy.getComplemento()));
        geocampo1 = new InputField(path(proxy.getGeocampo1()));
        geocampo2 = new InputField(path(proxy.getGeocampo2()));
        pontoReferencia = new InputField(path(proxy.getPontoReferencia()));
        cep = new InputField(path(proxy.getCep()));
        telefone = new InputField(path(proxy.getTelefone()));

        ddZona = DropDownUtil.getIEnumDropDown(path(proxy.getZona()), InvestigacaoAgravoEpizootiaEnum.ZonaEnum.values(), true);
        ddAmbiente = DropDownUtil.getIEnumDropDown(path(proxy.getAmbiente()), InvestigacaoAgravoEpizootiaEnum.AmbienteEnum.values(), true);
        ambienteOutro = new InputField(path(proxy.getAmbienteOutro()));

        ddColetaMaterial = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterial()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        dataColeta = new DateChooser(path(proxy.getDataColeta()));
        dataColeta.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        ddColetaMaterialFigado = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialFigado()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        ddColetaMaterialRim = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialRim()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        ddColetaMaterialBaco = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialBaco()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        ddColetaMaterialCerebro = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialCerebro()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        ddColetaMaterialCoracao = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialCoracao()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        ddColetaMaterialFezes = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialFezes()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        ddColetaMaterialSoro = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialSoro()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        ddColetaMaterialSangueTotal = DropDownUtil.getIEnumDropDown(path(proxy.getColetaMaterialSangueTotal()), InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.values(), true);
        coletaMaterialOutro = new InputField(path(proxy.getColetaMaterialOutro()));

        ddAnimaisAcometidosOpcao1 = DropDownUtil.getIEnumDropDown(path(proxy.getAnimaisAcometidosOpcao1()), InvestigacaoAgravoEpizootiaEnum.AnimaisAcometidosEnum.values(), true);
        animaisAcometidosOpcao1Doentes = new InputField(path(proxy.getAnimaisAcometidosOpcao1Doentes()));
        animaisAcometidosOpcao1Mortos = new InputField(path(proxy.getAnimaisAcometidosOpcao1Mortos()));
        animaisAcometidosOpcao1Especificar = new InputField(path(proxy.getAnimaisAcometidosOpcao1Especificar()));

        ddAnimaisAcometidosOpcao2 = DropDownUtil.getIEnumDropDown(path(proxy.getAnimaisAcometidosOpcao2()), InvestigacaoAgravoEpizootiaEnum.AnimaisAcometidosEnum.values(), true);
        animaisAcometidosOpcao2Doentes = new InputField(path(proxy.getAnimaisAcometidosOpcao2Doentes()));
        animaisAcometidosOpcao2Mortos = new InputField(path(proxy.getAnimaisAcometidosOpcao2Mortos()));
        animaisAcometidosOpcao2Especificar = new InputField(path(proxy.getAnimaisAcometidosOpcao2Especificar()));

        ddSuspeitaDiagnostica1 = DropDownUtil.getIEnumDropDown(path(proxy.getSuspeitaDiagnostica1()), InvestigacaoAgravoEpizootiaEnum.SuspeitaDiagnosticaEnum.values(), true);
        suspeitaDiagnostica1Especificar = new InputField(path(proxy.getSuspeitaDiagnostica1Especificar()));

        ddSuspeitaDiagnostica2 = DropDownUtil.getIEnumDropDown(path(proxy.getSuspeitaDiagnostica2()), InvestigacaoAgravoEpizootiaEnum.SuspeitaDiagnosticaEnum.values(), true);
        suspeitaDiagnostica2Especificar = new InputField(path(proxy.getSuspeitaDiagnostica2Especificar()));

        ddSuspeitaDiagnostica3 = DropDownUtil.getIEnumDropDown(path(proxy.getSuspeitaDiagnostica3()), InvestigacaoAgravoEpizootiaEnum.SuspeitaDiagnosticaEnum.values(), true);
        suspeitaDiagnostica3Especificar = new InputField(path(proxy.getSuspeitaDiagnostica3Especificar()));

        ddResultadoLaboralRaiva = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoLaboralRaiva()), InvestigacaoAgravoEpizootiaEnum.PositivoNegativoEnum.values(), true);
        ddResultadoLaboralEncefaliteEquina = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoLaboralEncefaliteEquina()), InvestigacaoAgravoEpizootiaEnum.PositivoNegativoEnum.values(), true);
        ddResultadoLaboralFebreNilo = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoLaboralFebreNilo()), InvestigacaoAgravoEpizootiaEnum.PositivoNegativoEnum.values(), true);
        ddResultadoLaboralEncefaliteEspongiformeBovina = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoLaboralEncefaliteEspongiformeBovina()), InvestigacaoAgravoEpizootiaEnum.PositivoNegativoEnum.values(), true);
        ddResultadoLaboralFebreAmarela = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoLaboralFebreAmarela()), InvestigacaoAgravoEpizootiaEnum.PositivoNegativoEnum.values(), true);
        ddResultadoLaboralInfluenzaAviaria = DropDownUtil.getIEnumDropDown(path(proxy.getResultadoLaboralInfluenzaAviaria()), InvestigacaoAgravoEpizootiaEnum.PositivoNegativoEnum.values(), true);
        resultadoLaboralEspecificar = new InputField(path(proxy.getResultadoLaboralEspecificar()));

        containerDadosOcorrencia.add(fonteInformacao, telefoneFonteInformacao, autoCompleteMunicipioOcorrencia, codMunicipioOcorrencia, estadoMunicipioOcorrencia,
                distrito, bairro, logradouro, numero, complemento, geocampo1, geocampo2, pontoReferencia, cep, telefone, ddZona, ddAmbiente, ambienteOutro, ddColetaMaterial,
                dataColeta, ddColetaMaterialFigado, ddColetaMaterialRim, ddColetaMaterialBaco, ddColetaMaterialCerebro, ddColetaMaterialCoracao, ddColetaMaterialFezes,
                ddColetaMaterialSoro, ddColetaMaterialSangueTotal, coletaMaterialOutro, ddAnimaisAcometidosOpcao1, animaisAcometidosOpcao1Doentes, animaisAcometidosOpcao1Mortos, animaisAcometidosOpcao1Especificar,
                ddAnimaisAcometidosOpcao2, animaisAcometidosOpcao2Doentes, animaisAcometidosOpcao2Mortos, animaisAcometidosOpcao2Especificar, ddSuspeitaDiagnostica1,
                suspeitaDiagnostica1Especificar, ddSuspeitaDiagnostica2, suspeitaDiagnostica2Especificar, ddSuspeitaDiagnostica3, suspeitaDiagnostica3Especificar,
                ddResultadoLaboralRaiva, ddResultadoLaboralEncefaliteEquina, ddResultadoLaboralFebreNilo, ddResultadoLaboralEncefaliteEspongiformeBovina,
                ddResultadoLaboralFebreAmarela, ddResultadoLaboralInfluenzaAviaria, resultadoLaboralEspecificar
        );
        getContainerInformacoesComplementares().add(containerDadosOcorrencia);
    }

    private void carregarDadosOcorrencia() {
        if (investigacaoAgravoEpizootia.getAmbienteOutro() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(ambienteOutro, false, false, null);
        }

        if (investigacaoAgravoEpizootia.getDataColeta() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDates(dataColeta, false, false, null);
        }

        if (investigacaoAgravoEpizootia.getColetaMaterialFigado() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialFigado, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialRim() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialRim, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialBaco() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialBaco, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialCerebro() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialCerebro, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialCoracao() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialCoracao, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialFezes() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialFezes, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialSoro() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialSoro, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialSangueTotal() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDropDown(ddColetaMaterialSangueTotal, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getColetaMaterialOutro() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(coletaMaterialOutro, false, false, null);
        }

        if (investigacaoAgravoEpizootia.getAnimaisAcometidosOpcao1Especificar() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(animaisAcometidosOpcao1Especificar, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getAnimaisAcometidosOpcao2Especificar() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(animaisAcometidosOpcao2Especificar, false, false, null);
        }

        if (investigacaoAgravoEpizootia.getSuspeitaDiagnostica1Especificar() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(suspeitaDiagnostica1Especificar, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getSuspeitaDiagnostica2Especificar() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(suspeitaDiagnostica2Especificar, false, false, null);
        }
        if (investigacaoAgravoEpizootia.getSuspeitaDiagnostica3Especificar() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableInput(suspeitaDiagnostica3Especificar, false, false, null);
        }


        autoCompleteMunicipioOcorrencia.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                codMunicipioOcorrencia.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(cidade.getCodigo());
                }

                estadoMunicipioOcorrencia.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(codMunicipioOcorrencia, estadoMunicipioOcorrencia);
            }
        });

        autoCompleteMunicipioOcorrencia.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                codMunicipioOcorrencia.limpar(target);
                estadoMunicipioOcorrencia.limpar(target);
            }
        });

        ddAmbiente.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                ambienteOutro.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddAmbiente, InvestigacaoAgravoEpizootiaEnum.AmbienteEnum.OUTRO.value()));
                target.add(ambienteOutro);
            }
        });

        ddColetaMaterial.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddColetaMaterial, InvestigacaoAgravoEpizootiaEnum.SimNaoIgnoradoEnum.SIM.value())){
                    dataColeta.setEnabled(true);
                    ddColetaMaterialFigado.setEnabled(true);
                    ddColetaMaterialRim.setEnabled(true);
                    ddColetaMaterialBaco.setEnabled(true);
                    ddColetaMaterialCerebro.setEnabled(true);
                    ddColetaMaterialCoracao.setEnabled(true);
                    ddColetaMaterialFezes.setEnabled(true);
                    ddColetaMaterialSoro.setEnabled(true);
                    ddColetaMaterialSangueTotal.setEnabled(true);
                    coletaMaterialOutro.setEnabled(true);
                }
                else {
                    dataColeta.setEnabled(false);
                    dataColeta.limpar(target);
                    ddColetaMaterialFigado.setEnabled(false);
                    ddColetaMaterialFigado.limpar(target);
                    ddColetaMaterialRim.setEnabled(false);
                    ddColetaMaterialRim.limpar(target);
                    ddColetaMaterialBaco.setEnabled(false);
                    ddColetaMaterialBaco.limpar(target);
                    ddColetaMaterialCerebro.setEnabled(false);
                    ddColetaMaterialCerebro.limpar(target);
                    ddColetaMaterialCoracao.setEnabled(false);
                    ddColetaMaterialCoracao.limpar(target);
                    ddColetaMaterialFezes.setEnabled(false);
                    ddColetaMaterialFezes.limpar(target);
                    ddColetaMaterialSoro.setEnabled(false);
                    ddColetaMaterialSoro.limpar(target);
                    ddColetaMaterialSangueTotal.setEnabled(false);
                    ddColetaMaterialSangueTotal.limpar(target);
                    coletaMaterialOutro.setEnabled(false);
                    coletaMaterialOutro.limpar(target);
                }

                target.add(dataColeta, ddColetaMaterialFigado, ddColetaMaterialRim, ddColetaMaterialBaco, ddColetaMaterialCerebro,
                        ddColetaMaterialCoracao, ddColetaMaterialFezes, ddColetaMaterialSoro, ddColetaMaterialSangueTotal, coletaMaterialOutro);
            }
        });

        ddAnimaisAcometidosOpcao1.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                animaisAcometidosOpcao1Especificar.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddAnimaisAcometidosOpcao1, InvestigacaoAgravoEpizootiaEnum.AnimaisAcometidosEnum.OUTROS.value()));
                target.add(animaisAcometidosOpcao1Especificar);
            }
        });

        ddAnimaisAcometidosOpcao2.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                animaisAcometidosOpcao2Especificar.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddAnimaisAcometidosOpcao2, InvestigacaoAgravoEpizootiaEnum.AnimaisAcometidosEnum.OUTROS.value()));
                target.add(animaisAcometidosOpcao2Especificar);
            }
        });

        ddSuspeitaDiagnostica1.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                suspeitaDiagnostica1Especificar.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddSuspeitaDiagnostica1, InvestigacaoAgravoEpizootiaEnum.SuspeitaDiagnosticaEnum.OUTRO.value()));
                target.add(suspeitaDiagnostica1Especificar);
            }
        });
        ddSuspeitaDiagnostica2.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                suspeitaDiagnostica2Especificar.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddSuspeitaDiagnostica2, InvestigacaoAgravoEpizootiaEnum.SuspeitaDiagnosticaEnum.OUTRO.value()));
                target.add(suspeitaDiagnostica2Especificar);
            }
        });
        ddSuspeitaDiagnostica3.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                suspeitaDiagnostica3Especificar.setEnabled(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddSuspeitaDiagnostica3, InvestigacaoAgravoEpizootiaEnum.SuspeitaDiagnosticaEnum.OUTRO.value()));
                target.add(suspeitaDiagnostica3Especificar);
            }
        });


    }

    private void criarObservacoes(InvestigacaoAgravoEpizootia proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacoes");
        containerObservacoes.setOutputMarkupId(true);
        containerObservacoes.add(new InputArea(path(proxy.getObservacao())));

        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void criarUsuarioDataEncerramento(InvestigacaoAgravoEpizootia proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }
}
