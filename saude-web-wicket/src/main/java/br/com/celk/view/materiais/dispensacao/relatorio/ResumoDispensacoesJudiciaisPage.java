package br.com.celk.view.materiais.dispensacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto.RelatorioResumoDispensacaoJudicialDTOParam;
import br.com.ksisolucoes.report.entrada.dispensacao.interfaces.facade.DispensacaoMedicamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.entradas.estoque.EstoqueEmpresa;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 */
@Private

public class ResumoDispensacoesJudiciaisPage extends RelatorioPage<RelatorioResumoDispensacaoJudicialDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaUnidadesDispensadoras;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaUnidadesDispensadoras = new AutoCompleteConsultaEmpresa("unidadesDispensadoras"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produtos"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(new RequiredPnlChoicePeriod("period"));
        form.add(getDropDownFormaApresentacao());
        
        autoCompleteConsultaUnidadesDispensadoras.setMultiplaSelecao(true);
        autoCompleteConsultaUnidadesDispensadoras.setOperadorValor(true);
        autoCompleteConsultaProduto.setMultiplaSelecao(true);
        autoCompleteConsultaProduto.setOperadorValor(true);
    }
    
    private DropDown getDropDownFormaApresentacao(){
        DropDown dropDown = new DropDown("formaApresentacao");
        
        dropDown.addChoice(Bundle.getStringApplication("rotulo_produto"), BundleManager.getString("produto"));
        dropDown.addChoice(Bundle.getStringApplication("rotulo_unidade_dispensadora"), BundleManager.getString("unidadeDispensadora"));
        
        return dropDown;
    }

    @Override
    public Class<RelatorioResumoDispensacaoJudicialDTOParam> getDTOParamClass() {
        return RelatorioResumoDispensacaoJudicialDTOParam.class;
    }

    @Override
    public void customDTOParam(RelatorioResumoDispensacaoJudicialDTOParam param) {
        param.setOrdenacao(Bundle.getStringApplication("rotulo_valor_total"));
        param.setTipoOrdenacao(BuilderQueryCustom.QuerySorter.DECRESCENTE);
        param.setTipoPreco(EstoqueEmpresa.PROP_ULTIMO_PRECO);
    }

    @Override
    public DataReport getDataReport(RelatorioResumoDispensacaoJudicialDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(DispensacaoMedicamentoReportFacade.class).relatorioResumoDispensacaoJudicial(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoDispensacoesJudiciais");
    }

}
