package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.vigilancia.registroagravo.enums.SimNaoEnum;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.model.Model;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class FichaInvestigacaoAgravoHelper {

    public static Cidade getCidadeByCodigo(Long codigo) {
        Cidade cidadeTemp = LoadManager.getInstance(Cidade.class)
                .addProperty(VOUtils.montarPath(Cidade.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Cidade.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                .addProperty(VOUtils.montarPath(Cidade.PROP_ESTADO, Estado.PROP_DESCRICAO))
                .setId(codigo)
                .start().getVO();

        return cidadeTemp;
    }

    public static Empresa getEmpresaByCodigo(Long codigo) {
        Empresa empresaTemp = LoadManager.getInstance(Empresa.class)
                .addProperty(VOUtils.montarPath(Empresa.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_TELEFONE))
                .addProperty(VOUtils.montarPath(Empresa.PROP_CNES))
                .addProperty(VOUtils.montarPath(Empresa.PROP_RUA))
                .addProperty(VOUtils.montarPath(Empresa.PROP_NUMERO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_COMPLEMENTO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_CIDADE, Cidade.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_CIDADE, Cidade.PROP_DESCRICAO))
                .addProperty(VOUtils.montarPath(Empresa.PROP_CIDADE, Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                .setId(codigo)
                .start().getVO();

        return empresaTemp;
    }

    public static TabelaCbo getTabelaCboByCodUser(Long codigo) {
        UsuarioCadsus usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_TABELA_CBO, TabelaCbo.PROP_CBO))
                .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_TABELA_CBO, TabelaCbo.PROP_DESCRICAO))
                .setId(codigo)
                .start().getVO();

        return usuarioCadsus.getTabelaCbo();
    }

    public enum SimNaoIgnoradoIndeterminadoEnum implements IEnum {
        SIM(1L, "Sim"),
        NAO(2L, "Não"),
        INDETERMINADO(3L, "Indeterminado"),
        IGNORADO(9L, "Ignorado");

        private Long value;
        private String descricao;

        SimNaoIgnoradoIndeterminadoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static SimNaoIgnoradoIndeterminadoEnum[] getSimNaoIndeterminado() {
            SimNaoIgnoradoIndeterminadoEnum array [] = {SIM, NAO, INDETERMINADO};
            return array;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SimNaoIgnoradoIndeterminadoEnum valueOf(Long value) {
            for (SimNaoIgnoradoIndeterminadoEnum v : SimNaoIgnoradoIndeterminadoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public static DropDown createDropDownCasoAutoctone(String pathCasoAutoctone, WebMarkupContainer containerCasoAutoctone, boolean addBlankOption) {
        DropDown ddCasoAutoctone = DropDownUtil.getIEnumDropDown(pathCasoAutoctone, FichaInvestigacaoAgravoHelper.SimNaoIgnoradoIndeterminadoEnum.getSimNaoIndeterminado(), addBlankOption);
        ddCasoAutoctone.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                containerCasoAutoctone.setEnabled(false);
                if (FichaInvestigacaoAgravoHelper.SimNaoIgnoradoIndeterminadoEnum.NAO.value().equals(ddCasoAutoctone.getComponentValue())) {
                    containerCasoAutoctone.setEnabled(true);
                }

                target.add(containerCasoAutoctone);
                target.appendJavaScript(JScript.initMasks());
            }
        });

        return ddCasoAutoctone;
    }

    public static void enableDisableDropDown(DropDown dropdown, boolean enable, boolean required, AjaxRequestTarget limpar) {
        if (!enable) {
            dropdown.setEnabled(enable);
            dropdown.setRequired(enable);
            dropdown.removeRequiredClass();

        } else {
            dropdown.setEnabled(enable);
            if (required) {
                dropdown.setRequired(required);
                dropdown.addRequiredClass();
            }
        }

        if (limpar != null) {
            dropdown.limpar(limpar);
        }
    }

    public static void enableDisableDates(DateChooser dates, boolean enable, boolean required, AjaxRequestTarget limpar) {
        if (!enable) {
            dates.setEnabled(enable);
            dates.setRequired(enable);
            dates.removeRequiredClass();

        } else {
            dates.setEnabled(enable);
            if (required) {
                dates.setRequired(required);
                dates.addRequiredClass();
            }
        }
        if (limpar != null) {
            dates.limpar(limpar);
        }
    }

    public static void enableDisableInput(InputField inputField, boolean enable, boolean required, AjaxRequestTarget target) {
        if (!enable) {
            inputField.setEnabled(enable);
            inputField.setRequired(enable);
            inputField.removeRequiredClass();

        } else {
            inputField.setEnabled(enable);
            if (required) {
                inputField.setRequired(required);
                inputField.addRequiredClass();
            }
        }
        if (target != null) {
            inputField.limpar(target);
            target.add(inputField);
        }
    }

    public static void enableDisableInputs(List<InputField> list, boolean enable, AjaxRequestTarget target) {
        for (InputField input : list) {
            input.setEnabled(enable);

            if (target != null) {
                input.limpar(target);
                target.add(input);
            }
        }
    }


    public static void createRadioSimNao(
            RadioButtonGroup radioGroup, WebMarkupContainer containerTarget
    ) {
        createRadioSimNaoIgnoradoIndeterminado(
                radioGroup, containerTarget, true, false, false, false, false, false
        );
    }

    public static void createRadioSimNaoIgnorado(
            RadioButtonGroup radioGroup, WebMarkupContainer containerTarget
    ) {
        createRadioSimNaoIgnoradoIndeterminado(
                radioGroup, containerTarget, true, false, false, false, true, false
        );
    }

    public static void createRadioSimNaoIndeterminado(
            RadioButtonGroup radioGroup, WebMarkupContainer containerTarget
    ) {
        createRadioSimNaoIgnoradoIndeterminado(
                radioGroup, containerTarget, true, false, true, false, false, false
        );
    }

    public static void createRadioSimNaoIgnoradoIndeterminado(
            RadioButtonGroup radioGroup, WebMarkupContainer containerTarget
    ) {
        createRadioSimNaoIgnoradoIndeterminado(
                radioGroup, containerTarget, true, false, true, false, true, false
        );
    }

    public static void createRadioSimNaoIgnoradoIndeterminado(
            RadioButtonGroup radioGroup,
            WebMarkupContainer containerTarget,
            boolean simHabilita,  boolean naoHabilita,
            boolean containIndeterminado, boolean indeterminado,
            boolean containIgnorado, boolean ignorado
    ) {
        radioGroup.add(new AjaxRadio("sim", new Model((SimNaoEnum.SIM.value()))) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                containerTarget.setEnabled(simHabilita);
                target.add(containerTarget);
            }
        });

        radioGroup.add(new AjaxRadio("nao", new Model((SimNaoEnum.NAO.value()))) {
            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                containerTarget.setEnabled(naoHabilita);
                target.add(containerTarget);
            }
        });

        if (containIndeterminado) {
            radioGroup.add(new AjaxRadio("indeterminado", new Model((SimNaoEnum.INDETERMINADO.value()))) {
                @Override
                public void onAjaxEvent(AjaxRequestTarget target) {
                    containerTarget.setEnabled(indeterminado);
                    target.add(containerTarget);
                }
            });
        }

        if (containIgnorado) {
            radioGroup.add(new AjaxRadio("ignorado", new Model((SimNaoEnum.IGNORADO.value()))) {
                @Override
                public void onAjaxEvent(AjaxRequestTarget target) {
                    containerTarget.setEnabled(ignorado);
                    target.add(containerTarget);
                }
            });
        }
    }

    public static boolean isLongTrue(DropDown dd, Long e) {
        return dd != null && dd.getComponentValue() != null && dd.getComponentValue().equals(e);
    }

    public static boolean anyDropDownsTrue(Long valueComparator, DropDown ...dds) {
        boolean val = false;

        if (dds != null && dds.length > 0) {
            for (DropDown dd : dds) {
                if (isLongTrue(dd, valueComparator)) {
                    val = true;
                }
            }
        }

        return val;
    }

    public static boolean isLongFalse(DropDown dd, Long e) {
        return dd != null && dd.getComponentValue() != null && !dd.getComponentValue().equals(e);
    }

    public static boolean isLongSim(Long l) {
        return SimNaoEnum.SIM.value().equals(l);
    }

    public static boolean isLongNao(Long l) {
        return SimNaoEnum.NAO.value().equals(l);
    }

    /**
     * Verifica se a primeira data vem ANTES da segunda
     * @param data1
     * @param data2
     * @return
     */
    public static boolean isBefore(Date data1, Date data2) {
        return (data1 != null && data2 != null && data1.compareTo(data2) < 0);
    }

    /**
     *  Verifica se a primeira data vem DEPOIS da segunda
     * @param data1
     * @param data2
     * @return
     */
    public static boolean isAfter(Date data1, Date data2) {
        return (data1 != null && data2 != null && (data1.compareTo(data2) > 0));
    }

    /**
     * Verifica se a primeira data é IGUAL a segunda
     * @param data1
     * @param data2
     * @return
     */
    public boolean isEqual(Date data1, Date data2) {
        return (data1 != null && data2 != null && data1.compareTo(data2) == 0);
    }

    public static void validarCheckboxes(String msg, WebMarkupContainer container, CheckBoxLongValue... a) throws ValidacaoException {
        if (container.isEnabled()) {
            List<CheckBoxLongValue> checks = Arrays.asList(a);

            int qtdNull = 0;
            int qtdNotNull = 0;

            for (CheckBoxLongValue c : checks) {
                if (c.getComponentValue() == null || c.getComponentValue() == 0L) {
                    qtdNull++;
                } else {
                    qtdNotNull++;
                }
            }

            if (qtdNull > 0 && qtdNotNull == 0) {
                throw new ValidacaoException(msg);
            }
        }
    }

    public static String validarDatasFuturas(List<DateChooser> listDatas) throws ValidacaoException {
        StringBuilder sb = new StringBuilder();
        for (DateChooser data : listDatas) {
            if (isAfter(data.getComponentValue(), DataUtil.getDataAtual())) {
                sb.append("Campo: " + data.getLabel().getObject() + ", com a data " + DataUtil.getFormatarDiaMesAno(data.getConvertedInput()) + ", não pode ser maior que o dia de hoje! \n");
            }
        }
        return sb.toString();
    }

    public static void setCidadeEstadoOnAutoComplete(AutoCompleteConsultaCidade autocomplete, Cidade cidade) {
        if (cidade != null && cidade.getCodigo() != null) {
            Cidade e = getCidadeByCodigo(cidade.getCodigo());
            autocomplete.setComponentValue(e);
        }
    }
}
