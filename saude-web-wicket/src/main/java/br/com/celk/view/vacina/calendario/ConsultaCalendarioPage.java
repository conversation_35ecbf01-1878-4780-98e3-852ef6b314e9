package br.com.celk.view.vacina.calendario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vacina.VacinaHelper;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.clone.DefinerPropertiesCloning;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vacina.base.BaseCalendario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaCalendarioPage extends ConsultaPage<Calendario, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new UpperField("descricao"));

        setExibeExpandir(true);
        getLinkNovo().setVisible(VacinaHelper.permissaoCrudVacinas());
    }

    @Override
    public List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> getColumns(List<org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(Calendario.class);
        Calendario proxy = on(Calendario.class);

        columns.add(getActionColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), path(proxy.getCodigo())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), path(proxy.getDescricao())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("padrao"), path(proxy.getPadrao()), path(proxy.getPadraoFormatado())));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("atualizacao"), path(proxy.getFlagAtualizacao()), path(proxy.getFlagAtualizacaoFormatado())));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<Calendario>() {

            @Override
            public void customizeColumn(Calendario rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<Calendario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Calendario modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCalendarioPage(modelObject));
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Calendario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Calendario modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(VacinaFacade.class).deletarCalendario(modelObject);
                        getPageableTable().update(target);
                    }
                }).setVisible(VacinaHelper.permissaoCrudVacinas());
                addAction(ActionType.CLONAR, rowObject, new IModelAction<Calendario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Calendario modelObject) throws ValidacaoException, DAOException {
                        Calendario newCalendario = new DefinerPropertiesCloning().define(modelObject);
                        setResponsePage(new CadastroCalendarioPage(newCalendario, modelObject));
                    }
                }).setVisible(VacinaHelper.permissaoCrudVacinas());
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Calendario>() {
                    @Override
                    public void action(AjaxRequestTarget target, Calendario modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCalendarioPage(modelObject, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaCalendario());
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseCalendario.PROP_DESCRICAO), BuilderQueryCustom.QueryParameter.ILIKE, descricao));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroCalendarioPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaEstrategia");
    }

}
