package br.com.celk.view.comunicacao.mensagem.table;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.pageable.IPageableTable;
import br.com.celk.component.table.pageable.IPagingBar;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import java.io.Serializable;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;

import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;

public class MessagingPagingBar<T extends Serializable> extends Panel implements IPagingBar{

    private IPageableTable table;
    private AbstractAjaxLink btnFirst;
    private AbstractAjaxLink btnPrevious;
    private AbstractAjaxLink btnNext;
    private AbstractAjaxLink btnLast;
    private Label lblPage;
    private Label lblNPages;
    private Label lblTotalItens;
    
    public MessagingPagingBar(String id, final IPageableTable table) {
        super(id);

        this.table = table;
        
        WebMarkupContainer column = new WebMarkupContainer("column");

        column.add(new AttributeModifier("colspan", table.getColumns().size()));
        
        column.add(btnFirst = new AbstractAjaxLink("first") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                table.firstPage(target);
            }

            @Override
            public boolean isEnabled() {
                if (table != null) {
                    return table.hasPreviousPage();
                }
                return false;
            }

        });
        
        column.add(btnPrevious = new AbstractAjaxLink("previous") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                table.previousPage(target);
            }

            @Override
            public boolean isEnabled() {
                if (table != null) {
                    return table.hasPreviousPage();
                }
                return false;
            }
        });
        
        column.add(btnNext = new AbstractAjaxLink("next") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                table.nextPage(target);
            }

            @Override
            public boolean isEnabled() {
                if (table != null) {
                    return table.hasNextPage();
                }
                return false;
            }
        });
        
        column.add(btnLast = new AbstractAjaxLink("last") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                table.lastPage(target);
            }

            @Override
            public boolean isEnabled() {
                if (table != null) {
                    return table.hasNextPage();
                }
                return false;
            }
        });

        createPageNumberItem(column);
        
        createTotalItens(column);
        
        add(column);
    }
    
    private void createPageNumberItem(WebMarkupContainer column){
        column.add(lblPage = new Label("page", new Model<Long>() {

            @Override
            public Long getObject() {
                if (table == null) {
                    return 0L;
                }
                if (table.getPageCount() == 1) {
                    return 1L;
                }
                return (table.getCurrentPage() + 1);
            }
        }));
        
        lblPage.setOutputMarkupId(true);

        column.add(lblNPages = new Label("npages", new Model<Long>() {

            @Override
            public Long getObject() {
                if (table == null) {
                    return 0L;
                }
                return table.getPageCount();
            }
        }));
        
        lblNPages.setOutputMarkupId(true);
    }
    
    private void createTotalItens(WebMarkupContainer column){
        column.add(lblTotalItens = new Label("lblTotalItens", new Model<Long>() {

            @Override
            public Long getObject() {
                if (table == null || !table.isPopulated()) {
                    return 0L;
                }
                return table.getItemCount();
            }
        }));
        
        lblTotalItens.setOutputMarkupId(true);
    }
    
    @Override
    public void update(AjaxRequestTarget target){
        target.add(btnFirst);
        target.add(btnPrevious);
        target.add(btnNext);
        target.add(btnLast);
        target.add(lblPage);
        target.add(lblNPages);
        target.add(lblTotalItens);
    }

    @Override
    public DropDown<Integer> getCbxItemsPerPage() {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public Integer getRowsPerPage(){
        return 10;
    }

}
