package br.com.celk.view.unidadesaude.bpa.processo.esuspanels;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaEsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgEsusProcedimento extends Window{
    
    private PnlEsusProcedimento pnlEsusProcedimento;
    
    public DlgEsusProcedimento(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("profissionalNivelMedio");
            }
        });
                
        setInitialWidth(600);
        setInitialHeight(120);
        setResizable(true);
        
        setContent(pnlEsusProcedimento = new PnlEsusProcedimento(getContentId()) {

            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void adicionarFicha(AjaxRequestTarget target, ItemContaEsus itemContaEsus) throws ValidacaoException, DAOException {
                DlgEsusProcedimento.this.adicionarFicha(target, itemContaEsus);
                close(target);
            }
        });
    }
    
    public abstract void adicionarFicha(AjaxRequestTarget target, ItemContaEsus itemContaEsus) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, ItemContaEsus itemContaEsus, ContaPaciente contaPaciente){
        pnlEsusProcedimento.setItemContaEsus(target, itemContaEsus, contaPaciente);
        show(target);
    }    

}
