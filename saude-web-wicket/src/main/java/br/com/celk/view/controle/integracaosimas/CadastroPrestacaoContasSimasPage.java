package br.com.celk.view.controle.integracaosimas;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.integracaosimas.autocomplete.AutoCompleteConsultaIndicadorSimas;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.geral.*;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class CadastroPrestacaoContasSimasPage extends CadastroPage<PrestacaoContasSimas> {

    private RequiredInputField txtDescricao;
    private DropDown ddSituacao;

    private WebMarkupContainer containerUnidade;
    private WebMarkupContainer containerIndicador;

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaIndicadorSimas autoCompleteConsultaIndicadorSimas;
    private Table tblUnidades;
    private Table tblIndicadorSimas;

    private Empresa empresa;
    private IndicadorSimas indicadorSimas;

    private List<PrestacaoContasEloUnidadeSimas> prestacaoContasEloUnidadeSimasList;
    private List<PrestacaoContasEloIndicadorSimas> prestacaoContasEloIndicadorSimasList;


    public CadastroPrestacaoContasSimasPage() {
    }

    public CadastroPrestacaoContasSimasPage(PrestacaoContasSimas object) {
        super(object,false);
    }

    public CadastroPrestacaoContasSimasPage(PrestacaoContasSimas object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<PrestacaoContasSimas> form) {
        PrestacaoContasSimas proxy = on(PrestacaoContasSimas.class);

        form.add(txtDescricao = new RequiredInputField(path(proxy.getDescricao())));
        form.add(ddSituacao = DropDownUtil.getIEnumDropDown(path(proxy.getSituacao()), PrestacaoContasSimas.Situacao.values()));

        ddSituacao.setRequired(true);

        form.add(containerUnidade = new WebMarkupContainer("containerUnidade"));
        containerUnidade.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresa", new PropertyModel(this, "empresa")));
        containerUnidade.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
                    if (containsEmpresa(prestacaoContasEloUnidadeSimasList, empresa)) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_unidade_adicionada"));
                    } else {
                        PrestacaoContasEloUnidadeSimas eloUnidadeSimas = new PrestacaoContasEloUnidadeSimas();
                        eloUnidadeSimas.setEmpresa(empresa);

                        prestacaoContasEloUnidadeSimasList.add(eloUnidadeSimas);
                        tblUnidades.populate();
                        tblUnidades.update(target);

                        autoCompleteConsultaEmpresa.limpar(target);
                    }
                }
            }
        }.setDefaultFormProcessing(false));

        containerUnidade.add(tblUnidades = new Table("tblUnidades", getColumnsUnidades(), getCollectionProviderUnidades()));
        tblUnidades.populate();

        form.add(containerIndicador = new WebMarkupContainer("containerIndicador"));
        containerIndicador.add(autoCompleteConsultaIndicadorSimas = new AutoCompleteConsultaIndicadorSimas("indicadorSimas", new PropertyModel(this, "indicadorSimas")));
        containerIndicador.add(new AbstractAjaxButton("btnAdicionarIndicador") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (autoCompleteConsultaIndicadorSimas.getComponentValue() != null) {
                    if (containsIndicadorSimas(prestacaoContasEloIndicadorSimasList, indicadorSimas)) {
                        throw new ValidacaoException(Bundle.getStringApplication("msg_indicador_adicionada"));
                    } else {
                        PrestacaoContasEloIndicadorSimas eloIndicadorSimas = new PrestacaoContasEloIndicadorSimas();
                        eloIndicadorSimas.setIndicadorSimas(indicadorSimas);

                        prestacaoContasEloIndicadorSimasList.add(eloIndicadorSimas);
                        tblIndicadorSimas.populate();
                        tblIndicadorSimas.update(target);

                        autoCompleteConsultaIndicadorSimas.limpar(target);
                    }
                }
            }
        }.setDefaultFormProcessing(false));

        containerIndicador.add(tblIndicadorSimas = new Table("tblIndicadorSimas", getColumnsIndicadorSimas(), getCollectionProviderIndicadorSimas()));
        tblIndicadorSimas.populate();

        carregarEloUnidades(form.getModelObject());
        carregarEloIndicadores(form.getModelObject());
    }

    private void carregarEloUnidades(PrestacaoContasSimas prestacaoContasSimas) {
       if (prestacaoContasSimas.getCodigo() != null){
           prestacaoContasEloUnidadeSimasList = LoadManager.getInstance(PrestacaoContasEloUnidadeSimas.class)
                   .addProperties(new HQLProperties(PrestacaoContasEloUnidadeSimas.class).getProperties())
                   .addProperties(new HQLProperties(Empresa.class, PrestacaoContasEloUnidadeSimas.PROP_EMPRESA).getProperties())
                   .addParameter(new QueryCustom.QueryCustomParameter(PrestacaoContasEloUnidadeSimas.PROP_PRESTACAO_CONTAS_SIMAS, prestacaoContasSimas))
                   .start().getList();
       }
    }

    public  boolean containsEmpresa(List<PrestacaoContasEloUnidadeSimas> list, Empresa empresa) {
        if (list == null || empresa == null) {
            return false;
        }
        for (PrestacaoContasEloUnidadeSimas item : list) {
            if (item.getEmpresa().equals(empresa)) {
                return true;
            }
        }
        return false;
    }

    public boolean containsIndicadorSimas(List<PrestacaoContasEloIndicadorSimas> list, IndicadorSimas indicadorSimas) {
        if (list == null || indicadorSimas == null) {
            return false;
        }
        for (PrestacaoContasEloIndicadorSimas item : list) {
            if (item.getIndicadorSimas().equals(indicadorSimas)) {
                return true;
            }
        }
        return false;
    }


    private void carregarEloIndicadores(PrestacaoContasSimas prestacaoContasSimas) {
        if (prestacaoContasSimas.getCodigo() != null){
            prestacaoContasEloIndicadorSimasList = LoadManager.getInstance(PrestacaoContasEloIndicadorSimas.class)
                    .addProperties(new HQLProperties(PrestacaoContasEloIndicadorSimas.class).getProperties())
                    .addProperties(new HQLProperties(IndicadorSimas.class, PrestacaoContasEloIndicadorSimas.PROP_INDICADOR_SIMAS).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(PrestacaoContasEloIndicadorSimas.PROP_PRESTACAO_CONTAS_SIMAS, prestacaoContasSimas))
                    .start().getList();
        }
    }

    private ICollectionProvider getCollectionProviderUnidades() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (prestacaoContasEloUnidadeSimasList == null) {
                    prestacaoContasEloUnidadeSimasList = new ArrayList<PrestacaoContasEloUnidadeSimas>();
                }
                return prestacaoContasEloUnidadeSimasList;
            }
        };
    }

    private List<IColumn> getColumnsUnidades() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PrestacaoContasEloUnidadeSimas proxy = on(PrestacaoContasEloUnidadeSimas.class);

        columns.add(getActionColumnUnidades());
        columns.add(createColumn(bundle("unidades"), proxy.getEmpresa().getDescricaoFormatado()));

        return columns;
    }

    private IColumn getActionColumnUnidades() {
        return new MultipleActionCustomColumn<PrestacaoContasEloUnidadeSimas>() {
            @Override
            public void customizeColumn(final PrestacaoContasEloUnidadeSimas rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<PrestacaoContasEloUnidadeSimas>() {
                    @Override
                    public void action(AjaxRequestTarget target, PrestacaoContasEloUnidadeSimas modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < prestacaoContasEloUnidadeSimasList.size(); i++) {
                            PrestacaoContasEloUnidadeSimas item = prestacaoContasEloUnidadeSimasList.get(i);
                            if (item == rowObject) {
                                prestacaoContasEloUnidadeSimasList.remove(i);
                            }
                        }
                        tblUnidades.populate();
                        tblUnidades.update(target);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderIndicadorSimas() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (prestacaoContasEloIndicadorSimasList == null) {
                    prestacaoContasEloIndicadorSimasList = new ArrayList<PrestacaoContasEloIndicadorSimas>();
                }
                return prestacaoContasEloIndicadorSimasList;
            }
        };
    }

    private List<IColumn> getColumnsIndicadorSimas() {
        List<IColumn> columns = new ArrayList<IColumn>();
        PrestacaoContasEloIndicadorSimas proxy = on(PrestacaoContasEloIndicadorSimas.class);

        columns.add(getActionColumnIndicadorSimas());
        columns.add(createColumn(bundle("indicadorSimas"), proxy.getIndicadorSimas().getCodigoDescricaoFormatado()));

        return columns;
    }

    private IColumn getActionColumnIndicadorSimas() {
        return new MultipleActionCustomColumn<PrestacaoContasEloIndicadorSimas>() {
            @Override
            public void customizeColumn(final PrestacaoContasEloIndicadorSimas rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<PrestacaoContasEloIndicadorSimas>() {
                    @Override
                    public void action(AjaxRequestTarget target, PrestacaoContasEloIndicadorSimas modelObject) throws ValidacaoException, DAOException {
                        for (int i = 0; i < prestacaoContasEloIndicadorSimasList.size(); i++) {
                            PrestacaoContasEloIndicadorSimas item = prestacaoContasEloIndicadorSimasList.get(i);
                            if (item == rowObject) {
                                prestacaoContasEloIndicadorSimasList.remove(i);
                            }
                        }
                        tblIndicadorSimas.populate();
                        tblIndicadorSimas.update(target);
                    }
                });
            }
        };
    }

    @Override
    public Class<PrestacaoContasSimas> getReferenceClass() {
        return PrestacaoContasSimas.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaPrestacaoContasSimasPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return Bundle.getStringApplication("cadastroPrestacaoContasSimas");
    }

    @Override
    public Object salvar(PrestacaoContasSimas object) throws ValidacaoException, DAOException {
        if ((prestacaoContasEloUnidadeSimasList == null || prestacaoContasEloUnidadeSimasList.isEmpty()) || (prestacaoContasEloIndicadorSimasList == null || prestacaoContasEloIndicadorSimasList.isEmpty())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_unidade_indicador"));
        }
        return  BOFactoryWicket.getBO(BasicoFacade.class).salvarPrestacaoContasSimas(object, prestacaoContasEloUnidadeSimasList, prestacaoContasEloIndicadorSimasList);
    }
}
