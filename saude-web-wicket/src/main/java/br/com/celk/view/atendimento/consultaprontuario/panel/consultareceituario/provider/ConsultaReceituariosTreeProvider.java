package br.com.celk.view.atendimento.consultaprontuario.panel.consultareceituario.provider;

import br.com.celk.component.treetable.pageable.PageableTreeProvider;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ConsultaReceituariosDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by silvio on 25/06/18.
 */
public class ConsultaReceituariosTreeProvider extends PageableTreeProvider<ConsultaReceituariosDTO, String> {

    private DatePeriod datePeriod;
    private UsuarioCadsus usuarioCadsus;

    public ConsultaReceituariosTreeProvider() {
    }

    public ConsultaReceituariosTreeProvider(DatePeriod datePeriod) {
        this.datePeriod = datePeriod;
        carregar();
    }

    private void carregar() {
        List<ReceituarioItem> itens = null;
        ReceituarioItem proxy = on(ReceituarioItem.class);
        itens = LoadManager.getInstance(ReceituarioItem.class)
                .addProperties(new HQLProperties(ReceituarioItem.class).getProperties())
                .addProperties(new HQLProperties(Receituario.class, path(proxy.getReceituario())).getProperties())
                .addProperty(path(proxy.getReceituario().getAtendimento().getCidPrincipal().getCodigo()))
                .addProperty(path(proxy.getReceituario().getTipoReceita().getDescricao()))
                .addProperty(path(proxy.getProduto().getUnidade().getCodigo()))
                .addProperty(path(proxy.getProduto().getUnidade().getDescricao()))
                .addProperty(path(proxy.getReceituario().getTipoReceita().getTipoReceita()))
                .addProperty(path(proxy.getProduto().getUsoContinuo()))
                .addProperty(path(proxy.getReceituario().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getCodigo()))
                .addProperty(path(proxy.getReceituario().getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao()))
                .addProperty(path(proxy.getReceituario().getProfissional().getNome()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getTipoReceita().getTipoReceita()), BuilderQueryCustom.QueryParameter.DIFERENTE, TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getDataCadastro()), datePeriod))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getUsuarioCadsus()), usuarioCadsus))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getReceituario().getSituacao()), BuilderQueryCustom.QueryParameter.DIFERENTE, ReceituarioItem.Status.CANCELADO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getReceituario().getCodigo()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getNomeProduto())))
                .start().getList();

        String id;
        Receituario receituario;
        ConsultaReceituariosDTO dtoPai;
        ConsultaReceituariosDTO dtoFilho;
        ConsultaReceituariosDTO dtoHeader;
        if (CollectionUtils.isNotNullEmpty(itens)) {
            for (ReceituarioItem item : itens) {
                receituario = item.getReceituario();
                id = receituario.getCodigo().toString();
                dtoPai = new ConsultaReceituariosDTO();

                if (!getMapPaisAdicionados().containsKey(id)) {
                    dtoPai.setReceituario(receituario);
                    dtoPai.setHeader(false);
                    getMapPaisAdicionados().put(id, dtoPai);
                    getRootList().add(dtoPai);
                } else {
                    dtoPai = getMapPaisAdicionados().get(id);
                }

                if (dtoPai.getFilhos() == null || dtoPai.getFilhos().isEmpty()) {
                    dtoHeader = new ConsultaReceituariosDTO();
                    dtoHeader.setHeader(true);
                    dtoHeader.getHeaders().add(bundle("medicamento"));
                    dtoHeader.getHeaders().add(bundle("posologia"));
                    dtoHeader.getHeaders().add(bundle("prescrito"));
                    dtoHeader.getHeaders().add(bundle("situacaoItem"));
                    dtoPai.addFilho(dtoHeader);
                }

                dtoFilho = new ConsultaReceituariosDTO();
                dtoFilho.setDescricao(id);
                dtoFilho.setReceituarioItem(item);
                dtoFilho.setHeader(false);

                dtoPai.addFilho(dtoFilho);
                dtoFilho.setPai(dtoPai);
                getLeafs().add(dtoFilho);
            }
        }
    }

    @Override
    public void recarregarProvider() {
        setRootList(new ArrayList<ConsultaReceituariosDTO>());
        setLeafs(new ArrayList<ConsultaReceituariosDTO>());
        setMapPaisAdicionados(new HashMap<String, ConsultaReceituariosDTO>());
        carregar();
    }

    public void recarregarProvider(DatePeriod datePeriod, UsuarioCadsus usuarioCadsus) {
        setRootList(new ArrayList<ConsultaReceituariosDTO>());
        setLeafs(new ArrayList<ConsultaReceituariosDTO>());
        setMapPaisAdicionados(new HashMap<String, ConsultaReceituariosDTO>());
        this.datePeriod = datePeriod;
        this.usuarioCadsus = usuarioCadsus;
        carregar();
    }

}
