package br.com.celk.view.frota.registromanutencao.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.agenda.agendamento.tfd.cadastroviagem.autocomplete.AutoCompleteConsultaVeiculo;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.programasaude.autocomplete.AutoCompleteConsultaProgramaSaude;
import br.com.celk.view.frota.registromanutencao.autocomplete.AutoCompleteConsultaTipoOperacao;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.frota.interfaces.dto.RelatorioDetalhamentoGastosDTOParam;
import br.com.ksisolucoes.report.frota.interfaces.facade.FrotaReportFacade;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioDetalhamentoGastosPage extends RelatorioPage<RelatorioDetalhamentoGastosDTOParam> {

    private AutoCompleteConsultaVeiculo autoCompleteConsultaVeiculo;
    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("empresa"));
        form.add(new RequiredPnlDatePeriod("periodo"));
        form.add(new AutoCompleteConsultaTipoOperacao("tipoOperacao"));
        form.add(new AutoCompleteConsultaProgramaSaude("programaSaude"));
        form.add(autoCompleteConsultaVeiculo = new AutoCompleteConsultaVeiculo("veiculo"));
        form.add(DropDownUtil.getIEnumDropDown("formaApresentacao", RelatorioDetalhamentoGastosDTOParam.FormaApresentacao.values()));
        form.add(DropDownUtil.getIEnumDropDown("tipoResumo", RelatorioDetalhamentoGastosDTOParam.TipoResumo.values()));
    }

    @Override
    public Class<RelatorioDetalhamentoGastosDTOParam> getDTOParamClass() {
        return RelatorioDetalhamentoGastosDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioDetalhamentoGastosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(FrotaReportFacade.class).relatorioDetalhamentoGastos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhamentoGastos");
    }

}
