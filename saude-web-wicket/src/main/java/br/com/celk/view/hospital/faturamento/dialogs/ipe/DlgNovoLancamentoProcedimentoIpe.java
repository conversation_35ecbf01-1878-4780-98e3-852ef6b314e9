package br.com.celk.view.hospital.faturamento.dialogs.ipe;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgNovoLancamentoProcedimentoIpe extends Window {

    private PnlNovoLancamentoProcedimentoIpe pnlNovoLancamentoIpe;

    public DlgNovoLancamentoProcedimentoIpe(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(350);
        setInitialWidth(600);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("despesas");
            }
        });

        setContent(pnlNovoLancamentoIpe = new PnlNovoLancamentoProcedimentoIpe(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException {
                DlgNovoLancamentoProcedimentoIpe.this.onConfirmar(target, itemContaPaciente);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                DlgNovoLancamentoProcedimentoIpe.this.onFechar(target);
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente, ContaPaciente contaPaciente) {
        show(target);
        //pnlNovoLancamento.limpar(target);  
        pnlNovoLancamentoIpe.setItemContaPaciente(target, itemContaPaciente, contaPaciente);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
