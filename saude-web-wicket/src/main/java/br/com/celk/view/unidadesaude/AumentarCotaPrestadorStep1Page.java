package br.com.celk.view.unidadesaude;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.tipoexame.autocomplete.AutoCompleteConsultaTipoExame;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.ExameUnidadeCompetencia;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static br.com.ksisolucoes.TipoEstabelecimento.*;


/**
 *
 * <AUTHOR>
 */
@Private

public class AumentarCotaPrestadorStep1Page extends BasePage{

    private AutoCompleteConsultaTipoExame autoCompleteConsultaTipoExame;
    private TipoExame tipoExame;
    private Empresa prestador;
    private ArrayList<ExameUnidadeCompetencia> lstExameUnidadeCompetencias;
    private Date competenciaAtual;
    private ExamePrestadorCompetencia examePrestadorCompetencia;

    public AumentarCotaPrestadorStep1Page() {
        try {
            init();
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    public void init() throws DAOException, ValidacaoException{
        Form form = new Form("form",new CompoundPropertyModel(this));
        
        form.add(autoCompleteConsultaTipoExame = new AutoCompleteConsultaTipoExame("tipoExame",true));
        form.add(new AutoCompleteConsultaEmpresa("prestador",true).setTiposEstabelecimento(getTiposEstabelecimentoFiltro()));
        
        form.add(new AbstractAjaxButton("btnAvancar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                carregarExameUnidadeCompetencias();
                avancar(target);
            }
        });
        
        add(form);
    }

    private List<Long> getTiposEstabelecimentoFiltro() {
        return Arrays.asList(
                PRESTADOR_SERVICO.value(),
                UNIDADE.value(),
                UNIDADE_FILANTROPICA.value());
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("aumentarCotaPrestador");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTipoExame.getTxtDescricao().getTextField();
    }
    
    private void avancar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        Page page = new AumentarCotaPrestadorStep2Page(tipoExame, prestador,lstExameUnidadeCompetencias,examePrestadorCompetencia,competenciaAtual);
        setResponsePage(page);
    }

    
    private void carregarExameUnidadeCompetencias() throws DAOException, ValidacaoException {
        int diaInicioCompetencia = 0;
        try {
            diaInicioCompetencia = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).<Long>getParametro("diaInicioCompetencia").intValue();
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage());
        }
        this.competenciaAtual = Data.competenciaData(diaInicioCompetencia, Data.getDataAtual());
        
        examePrestadorCompetencia = LoadManager.getInstance(ExamePrestadorCompetencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorCompetencia.PROP_EMPRESA, Empresa.PROP_CODIGO), prestador.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorCompetencia.PROP_DATA_COMPETENCIA), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, this.competenciaAtual))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorCompetencia.PROP_TIPO_EXAME, TipoExame.PROP_CODIGO), tipoExame.getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(ExamePrestadorCompetencia.PROP_DATA_COMPETENCIA, QueryCustom.QueryCustomSorter.CRESCENTE))
                .setMaxResults(1).start().getVO();
        if (examePrestadorCompetencia == null) {
            throw new ValidacaoException(BundleManager.getString("msgPrestadorNaoRealizaTipoExameCompetencia"));
        }

        
        List<ExameUnidadeCompetencia> list = LoadManager.getInstance(ExameUnidadeCompetencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeCompetencia.PROP_TIPO_EXAME, tipoExame))
                .addParameter(new QueryCustom.QueryCustomParameter(ExameUnidadeCompetencia.PROP_DATA_COMPETENCIA, this.competenciaAtual))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ExameUnidadeCompetencia.PROP_EMPRESA,Empresa.PROP_REFERENCIA)))
                .start().getList();
        lstExameUnidadeCompetencias = new ArrayList<ExameUnidadeCompetencia>(list);
    }
}
