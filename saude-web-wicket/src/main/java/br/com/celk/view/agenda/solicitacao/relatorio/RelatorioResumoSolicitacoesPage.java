package br.com.celk.view.agenda.solicitacao.relatorio;

import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.authorization.annotation.ActionsEnum;
import br.com.celk.system.authorization.annotation.Permission;
import br.com.celk.system.authorization.annotation.PermissionContainer;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.agenda.tipoprocedimentoclassificacao.autocomplete.AutoCompleteConsultaTipoProcedimentoClassificacao;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.dto.RelatorioResumoSolicitacoesDTOParam;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;


/**
 *
 * <AUTHOR>
 */
@Private

public class RelatorioResumoSolicitacoesPage extends RelatorioPage<RelatorioResumoSolicitacoesDTOParam> implements PermissionContainer {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaTipoProcedimento autoCompleteConsultaTipoProcedimento;
    private AutoCompleteConsultaTipoProcedimentoClassificacao autoCompleteConsultaTipoProcedimentoClassificacao;
    
    @Permission(type=Permissions.PROFISSIONAL, action=ActionsEnum.RENDER)
    private WebMarkupContainer componenteProfissional;
    
    @Permission(type=Permissions.PACIENTE, action=ActionsEnum.RENDER)
    private WebMarkupContainer componentePaciente;
    
    @Permission(type=Permissions.TIPO_RESUMO, action=ActionsEnum.RENDER)
    private WebMarkupContainer componenteTipoResumo;
    
    @Permission(type=Permissions.ORDENACAO, action=ActionsEnum.RENDER)
    private WebMarkupContainer componenteOrdenacao;
    
    @Override
    public void init(Form form) {
        componenteProfissional = new WebMarkupContainer("componenteProfissional");
        componenteProfissional.add(new AutoCompleteConsultaProfissional("profissionalSolicitante").setOperadorValor(true).setMultiplaSelecao(true));
        componenteProfissional.add(new Label("lblProfissional", BundleManager.getString("profissionalSolicitante")));
        componentePaciente = new WebMarkupContainer("componentePaciente");
        componentePaciente.add(new AutoCompleteConsultaUsuarioCadsus("paciente").setOperadorValor(true).setMultiplaSelecao(true));
        componentePaciente.add(new Label("lblPaciente", BundleManager.getString("paciente")));
        componenteTipoResumo = new WebMarkupContainer("componenteTipoResumo");
        componenteTipoResumo.add(DropDownUtil.getEnumDropDown("tipoResumo", RelatorioResumoSolicitacoesDTOParam.TipoResumo.values()));
        componenteTipoResumo.add(new Label("lblTipoResumo", BundleManager.getString("tipoResumo")));
        componenteOrdenacao = new WebMarkupContainer("componenteOrdenacao");
        componenteOrdenacao.add(DropDownUtil.getEnumDropDown("ordenacao", RelatorioResumoSolicitacoesDTOParam.Ordenacao.values()));
        componenteOrdenacao.add(new Label("lblOrdenacao", BundleManager.getString("ordenacao")));
        
        form.add(componenteProfissional);
        form.add(componentePaciente);
        form.add(componenteTipoResumo);
        form.add(componenteOrdenacao);
        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("unidadeSolicitante"));
        form.add(autoCompleteConsultaTipoProcedimento = new AutoCompleteConsultaTipoProcedimento("tipoProcedimento"));
        form.add(autoCompleteConsultaTipoProcedimentoClassificacao = new AutoCompleteConsultaTipoProcedimentoClassificacao("tipoProcedimentoClassificacao"));
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getEnumDropDown("formaApresentacao", RelatorioResumoSolicitacoesDTOParam.FormaApresentacao.values()));
        
        autoCompleteConsultaEmpresa.setOperadorValor(true);
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaTipoProcedimento.setOperadorValor(true);
        autoCompleteConsultaTipoProcedimento.setMultiplaSelecao(true);
        autoCompleteConsultaTipoProcedimento.setIncluirInativos(true);
        autoCompleteConsultaTipoProcedimentoClassificacao.setOperadorValor(true);
        autoCompleteConsultaTipoProcedimentoClassificacao.setMultiplaSelecao(true);
    }
    
    @Override
    public void customDTOParam(RelatorioResumoSolicitacoesDTOParam param) {
        if (param.getTipoResumo() == null) {
            param.setTipoResumo(RelatorioResumoSolicitacoesDTOParam.TipoResumo.TIPO_PROCEDIMENTO);
        }
        
        if (param.getOrdenacao() == null) {
            param.setOrdenacao(RelatorioResumoSolicitacoesDTOParam.Ordenacao.DIAS_MEDIA_ESPERA);
        }
        
        if (param.getTipoOrdenacao() == null) {
            param.setTipoOrdenacao(RelatorioResumoSolicitacoesDTOParam.TipoOrdenacao.DECRESCENTE);
        }
    }

    @Override
    public Class<RelatorioResumoSolicitacoesDTOParam> getDTOParamClass() {
        return RelatorioResumoSolicitacoesDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioResumoSolicitacoesDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioResumoSolicitacoes(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoSolicitacoes");
    }

    public WebMarkupContainer getComponenteProfissional() {
        return componenteProfissional;
    }

    public WebMarkupContainer getComponentePaciente() {
        return componentePaciente;
    }

    public WebMarkupContainer getComponenteTipoResumo() {
        return componenteTipoResumo;
    }

    public WebMarkupContainer getComponenteOrdenacao() {
        return componenteOrdenacao;
    }
}
