package br.com.celk.view.agenda.agendamento.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.RequiredPnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.faixaetaria.PnlConsultaFaixaEtaria;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.exame.dto.GraficoDistribuicaoMensalExameAgendamentosDTOParam;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.List;


/**
 *
 * <AUTHOR>
 */
@Private

public class GraficoDistribuicaoMensalExameAgendamentosPage extends RelatorioPage<GraficoDistribuicaoMensalExameAgendamentosDTOParam> {
        
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<EquipeArea> dropDownEquipeArea;
    private PnlConsultaFaixaEtaria pnlConsultaFaixaEtaria;
    private DropDown dropDownFormaApresentacao;
    
    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa("empresa")
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaEmpresa("empresaOrigem")
                .setMultiplaSelecao(true));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento")
                .setIncluirInativos(true)
                .setOperadorValor(true)
                .setMultiplaSelecao(true));
        form.add(pnlConsultaFaixaEtaria = new PnlConsultaFaixaEtaria("faixaEtaria",true));
        this.pnlConsultaFaixaEtaria.setEnabled(false);
        form.add(new RequiredPnlChoicePeriod("periodo"));
        form.add(new InputField<Long>("maximoSerie"));
        form.add(getDropDownEquipeArea());
        form.add(dropDownFormaApresentacao = DropDownUtil.getEnumDropDown("formaApresentacao", GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.values()));
        
        dropDownFormaApresentacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownFormaApresentacao.getComponentValue() != null) {
                    if (dropDownFormaApresentacao.getComponentValue().equals(GraficoDistribuicaoMensalExameAgendamentosDTOParam.FormaApresentacao.FAIXA_ETARIA)) {
                        pnlConsultaFaixaEtaria.setEnabled(true);
                    } else {
                        pnlConsultaFaixaEtaria.limpar(target);
                        pnlConsultaFaixaEtaria.setEnabled(false);
                    }
                    target.add(pnlConsultaFaixaEtaria);
                }
            }
        });
        
        form.add(DropDownUtil.getEnumDropDown("tipoDado", GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoDado.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoAgendamento", GraficoDistribuicaoMensalExameAgendamentosDTOParam.TipoAgendamento.values()));
    }
    
    private DropDown getDropDownEquipeArea(){
        if (dropDownEquipeArea == null) {
            dropDownEquipeArea = new DropDown<EquipeArea>("equipeArea");
                List<EquipeArea> areas = LoadManager.getInstance(EquipeArea.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getCidade()))
                        .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                        .start().getList();

                dropDownEquipeArea.addChoice(null, BundleManager.getString("todas"));
                for (EquipeArea equipeArea : areas) {
                    dropDownEquipeArea.addChoice(equipeArea, equipeArea.getDescricao());
                }
        }
        
        return dropDownEquipeArea;
    }
    
    
    @Override
    public Class getDTOParamClass() {
        return GraficoDistribuicaoMensalExameAgendamentosDTOParam.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaEmpresa.getTxtDescricao().getTextField();
    }

    @Override
    public DataReport getDataReport(GraficoDistribuicaoMensalExameAgendamentosDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).graficoDistribuicaoMensalExameAgendamentos(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("distribuicaoMensalAgendamentos");
    }
}
