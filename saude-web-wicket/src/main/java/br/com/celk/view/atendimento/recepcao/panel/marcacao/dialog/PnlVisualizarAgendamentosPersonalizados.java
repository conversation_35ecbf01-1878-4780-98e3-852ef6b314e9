package br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog;

import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionLinkPanel;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.TimeColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

import java.text.ParseException;
import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlVisualizarAgendamentosPersonalizados extends Panel {

    private Form form;
    private Table<AgendaGradeAtendimentoDTO> tblAgendaGradeAtendimentoDTO;
    private List<AgendaGradeAtendimentoDTO> dtoList;
    private AgendaGradeAtendimentoDTO dto;
    private AjaxButton btnConfirmarAgendamentos;

    private Date data;
    private String profissional;
    private Date horaInicio;
    private Date horaFinal;
    private Date horaFinalAtendimento;
    private Long tempoConsulta;
    private Long horarioFechado;
    private String observacao;
    private AgendaGradeAtendimentoDTO agendaSelecionada;
    private InputField txtHoraInicio;
    private InputField txtTempoConsulta;
    private InputField txtObservacao;
    private InputField txtTotal;
    private DropDown dropDownHorarioFechado;
    private AjaxButton btnAgendarContinuar;

    public PnlVisualizarAgendamentosPersonalizados(String id) {
        super(id);
        init();
    }
    public PnlVisualizarAgendamentosPersonalizados(String id,AgendaGradeAtendimentoDTO agendaSelecionada) {
        super(id);
        this.agendaSelecionada=agendaSelecionada;
        init();
    }

    private void init() {
        form = new Form<AgendaGradeAtendimentoDTO>("form", new CompoundPropertyModel(this));
        setOutputMarkupId(true);

        form.add(new DisabledInputField("data"));
        form.add(new DisabledInputField("profissional"));
        form.add(txtHoraInicio = (InputField) new HoraMinutoField("horaInicio").setEnabled(false));
        form.add(txtTempoConsulta = new InputField<Long>("tempoConsulta", new PropertyModel<Long>(this, "tempoConsulta")));
        form.add(txtObservacao = new InputField("observacao", new PropertyModel<Date>(this, "observacao")));
        form.add(dropDownHorarioFechado = DropDownUtil.getSimNaoLongDropDown("horarioFechado", new PropertyModel<Long>(this, "horarioFechado"), false, false));
        
        form.add(txtTotal = (InputField) new InputField("total", new PropertyModel(this, "dtoList.size")).setEnabled(false));

        tblAgendaGradeAtendimentoDTO = new Table("tblAgendaGradeAtendimentoDTO", getColumns(), getCollectionProvider()){
            @Override
            public void update(AjaxRequestTarget target) {
                super.update(target); //To change body of generated methods, choose Tools | Templates.
                target.add(txtTotal);
            }
        };
        tblAgendaGradeAtendimentoDTO.setScrollY("180px");
        form.add(tblAgendaGradeAtendimentoDTO);
        tblAgendaGradeAtendimentoDTO.populate();


        form.add(btnConfirmarAgendamentos = new AbstractAjaxButton("btnAgendar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarRegrasNegocio(target)) {
                    onConfirmar(target, dto);
                }
            }
        });

            form.add(btnAgendarContinuar = new AbstractAjaxButton("btnAgendarContinuar") {

                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    if (validarRegrasNegocio(target)) {
                        onContinuar(target, dto);
                    }
                }
            });
        btnAgendarContinuar.setVisible(agendaSelecionada.getTipoProcedimento().habilitaAgendamentoGrupo());
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoDTO proxy = on(AgendaGradeAtendimentoDTO.class);

        columns.add(getCustomActionColumn());
        columns.add(new DateColumn<Date>(bundle("data"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData())).setPattern("dd/MM/yyyy"));
        columns.add(createColumn(bundle("semana"), proxy.getDiaSemanaAbv()));
        columns.add(new TimeColumn<Date>(bundle("hora_inicio"), path(proxy.getHoraInicial())).setPattern("HH:mm"));
        columns.add(new TimeColumn<Date>(bundle("hora_termino"), path(proxy.getHoraFinal())).setPattern("HH:mm"));
        columns.add(createColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createColumn(bundle("observacao"), proxy.getObservacaoAtendimento()));

        return columns;
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoDTO>() {
            @Override
            public void customizeColumn(final AgendaGradeAtendimentoDTO rowObject) {
                ActionLinkPanel alp = addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remove(target, rowObject);

                    }
                });
                alp.setTitleBundleKey("remover");
            }
        };
    }

    public void remove(AjaxRequestTarget target, AgendaGradeAtendimentoDTO rowObject) {
        dtoList.remove(rowObject);
        tblAgendaGradeAtendimentoDTO.update(target);
    }

    public ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) throws DAOException, ValidacaoException {
                return dtoList;
            }
        };
    }

    public boolean validarRegrasNegocio(AjaxRequestTarget target) throws DAOException {
        if (validarCadastro(target)) {
            try {
                Date dataFormatada = Data.addMinutos(Data.getDateTime(data, horaInicio), tempoConsulta.intValue());

                if (DataUtil.compareHour(dataFormatada, horaFinal) > 0) {
                    throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_soma_horario_tempo_consulta_nao_podem_ultrapassar_hora_final_definada_agenda"));
                } else if(tempoConsulta != null && tempoConsulta <= 0L){
                    throw new ValidacaoException(BundleManager.getString("msgTempoConsultaNaoPodeSerZero"));
                }
                dto.setHoraFinalAtendimento(dataFormatada);
                dto.setHoraFinal(dataFormatada);
                dto.setHoraInicioAgendaPersonalizada(DataUtil.mergeDataHora(data, horaInicio));

                List<AgendaGradeAtendimentoHorario> conflitoHorarios = BOFactoryWicket.getBO(AgendamentoFacade.class)
                        .consultarConflitoHorariosAgendaPersonalizada(dto.getAgendaGradeAtendimento(), horaInicio, dto.getHoraFinal());
                if (CollectionUtils.isNotNullEmpty(conflitoHorarios)) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_horario_conflita_horario_agendado_favor_verificar_tempo_consulta"));
                }

            } catch (ValidacaoException e) {
                MessageUtil.modalWarn(target, this, e);
                return false;
            } catch (ParseException ex) {
                MessageUtil.modalWarn(target, this, ex);
                return false;
            }

            dto.setTempoConsulta(tempoConsulta);
            dto.setHorarioFechado(horarioFechado);
            dto.setObservacaoAtendimento(observacao);
        } else {
            return false;
        }

        return true;
    }

    public boolean validarCadastro(AjaxRequestTarget target) throws DAOException {
        try {
            if (txtHoraInicio.getComponentValue() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_horario"));
            }
            if (txtTempoConsulta.getComponentValue() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_informe_tempo_consulta"));
            }
            if (tempoConsulta % dto.getAgendaGradeAtendimento().getTempoMedio() != 0L) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_valor_tempo_consulta_deve_ser_multiplo_valor_tempo_medio_agenda_tempo_medio_x",
                        dto.getAgendaGradeAtendimento().getTempoMedio()));
            }
            if (dropDownHorarioFechado.getComponentValue() == null) {
                throw new ValidacaoException(Bundle.getStringApplication("rotulo_msg_selecione_campo_horario_fechado"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException;

    public abstract void onContinuar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        target.add(form);
    }

    public void setDTO(AjaxRequestTarget target, List<AgendaGradeAtendimentoDTO> dtoList, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
        this.dto = dto;
        this.data = dto.getAgendaGradeAtendimento().getAgendaGrade().getData();
        if(dto.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional() != null){
            this.profissional = dto.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getNome();            
        }
        this.horaInicio = dto.getHoraInicial();
        this.tempoConsulta = dto.getAgendaGradeAtendimento().getTempoMedio();
        this.horaFinal = dto.getAgendaSelecionada().getAgendaGradeAtendimento().getAgendaGrade()
                .getHoraFinal();

        this.dtoList = dtoList;
        Collections.sort(dtoList);
    }
}
