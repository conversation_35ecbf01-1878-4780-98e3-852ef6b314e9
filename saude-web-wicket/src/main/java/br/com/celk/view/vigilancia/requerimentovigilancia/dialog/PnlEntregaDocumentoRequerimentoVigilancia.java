package br.com.celk.view.vigilancia.requerimentovigilancia.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaEntregaDocumentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import java.util.Date;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlEntregaDocumentoRequerimentoVigilancia extends Panel{
    
    private Form form;
    private RequerimentoVigilancia rv;
    private String nome;
    private String vinculo;
    private Date dataEntrega;
    private InputField txtNome;
    private InputField txtVinculo;
    private DateChooser dcDataEntrega;
    
    public PnlEntregaDocumentoRequerimentoVigilancia(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);
        
        form.add(txtNome = new InputField("nome", new PropertyModel<String>(this, "nome")));
        form.add(txtVinculo = new InputField("vinculo", new PropertyModel<String>(this, "vinculo")));
        form.add(dcDataEntrega = new DateChooser("dataEntrega", new PropertyModel<Date>(this, "dataEntrega")));
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    RequerimentoVigilanciaEntregaDocumentoDTO dto = new RequerimentoVigilanciaEntregaDocumentoDTO();
                    dto.setRequerimentoVigilancia(rv);
                    dto.setNome(nome);
                    dto.setVinculo(vinculo);
                    dto.setDataEntrega(dataEntrega);
                    onConfirmar(target, dto);
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if(txtNome.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_nome"));
            } else if(dcDataEntrega.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_entrega"));
            } else if(dataEntrega != null && Data.adjustRangeHour(dataEntrega).getDataInicial().after(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())){
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_entrega_deve_ser_menor_igual_data_atual"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaEntregaDocumentoDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setObject(AjaxRequestTarget target, RequerimentoVigilancia rv){
        this.rv = rv;
        txtNome.limpar(target);
        txtVinculo.limpar(target);
        dcDataEntrega.limpar(target);
        this.nome = null;
        this.vinculo = null;
        this.dataEntrega = DataUtil.getDataAtual();
        target.focusComponent(txtNome);
        target.add(dcDataEntrega);
    }
}