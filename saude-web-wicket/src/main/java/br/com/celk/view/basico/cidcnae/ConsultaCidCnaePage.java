package br.com.celk.view.basico.cidcnae;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.CidCerest;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * Created by jonas on 21/03/18.
 */
public class ConsultaCidCnaePage extends ConsultaPage<CidCerest, List<BuilderQueryCustom.QueryParameter>> {

    private Cid codigoCidCerest;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;


    public ConsultaCidCnaePage() {
        super();
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(autoCompleteConsultaCid = new AutoCompleteConsultaCid("codigoCidCerest"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {

        ColumnFactory columnFactory = new ColumnFactory(CidCerest.class);
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("cidcnaes"), VOUtils.montarPath(CidCerest.PROP_CODIGO_CID, Cid.PROP_DESCRICAO_FORMATADO)));
        return columns;
    }

    private CustomColumn<CidCerest> getCustomColumn(){
        return new CustomColumn<CidCerest>() {
            @Override
            public Component getComponent(String componentId, final CidCerest rowObject) {
                return new CrudActionsColumnPanel<CidCerest>(componentId, rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCidCnaePage(rowObject, true));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(BasicoFacade.class).deletarCidCnaeCetest(getObject());
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCidCnaePage(rowObject, false));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter(){

            @Override
            public Class getClassConsulta() {
                return CidCerest.class;
            }

        });
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(CidCerest.PROP_CODIGO_CID), codigoCidCerest));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroCidCnaePage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaCidCnae");
    }

}
