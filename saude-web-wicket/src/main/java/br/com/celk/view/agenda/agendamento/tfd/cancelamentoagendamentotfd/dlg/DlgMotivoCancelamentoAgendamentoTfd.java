package br.com.celk.view.agenda.agendamento.tfd.cancelamentoagendamentotfd.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoCancelamentoAgendamentoTfd extends Window {
    
    private Long codigoAgah;
    private PnlMotivoCancelamentoAgendamentoTfd pnlMotivo;
    public DlgMotivoCancelamentoAgendamentoTfd(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(80);

        setResizable(false);

        setTitle(BundleManager.getString("cancelamentoAgendamento"));

        setContent(pnlMotivo = new PnlMotivoCancelamentoAgendamentoTfd(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, Boolean reabrir) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoCancelamentoAgendamentoTfd.this.onConfirmar(target,motivo,DlgMotivoCancelamentoAgendamentoTfd.this.codigoAgah, reabrir);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String motivo, Long codigoAgah, Boolean reabrir) throws ValidacaoException, DAOException;

    public void setObject(Long codigoAgah) {
        this.codigoAgah = codigoAgah;
    }

    @Override
    public void show(AjaxRequestTarget target) {
        pnlMotivo.limpar(target);
        super.show(target);
    }
}