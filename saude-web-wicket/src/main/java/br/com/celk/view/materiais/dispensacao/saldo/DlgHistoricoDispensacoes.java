package br.com.celk.view.materiais.dispensacao.saldo;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgHistoricoDispensacoes extends Window{

    private PnlHistoricoDispensacoes pnlHistoricoDispensacoes;
    
    public DlgHistoricoDispensacoes(String id) {
        super(id);
        init();
    }

    private void init() {
        setContent(getPnlDispensacoesAberto());
        
        setInitialWidth(900);
        setInitialHeight(520);
        
        setTitle(BundleManager.getString("historicoDispensacoes"));
    }
    
    public PnlHistoricoDispensacoes getPnlDispensacoesAberto() {
        if (this.pnlHistoricoDispensacoes == null) {
            this.pnlHistoricoDispensacoes = new PnlHistoricoDispensacoes(getContentId()) {

                @Override
                public void fechar(AjaxRequestTarget target) {
                    close(target);
                }
            };
        }
        
        return this.pnlHistoricoDispensacoes;
    }
    
    public void setModelObject(UsuarioCadsus usuarioCadsus, Empresa estabelecimentoExecutante) throws ValidacaoException, DAOException {
        setModelObject(usuarioCadsus, null, estabelecimentoExecutante);
    }
    
    public void setModelObject(UsuarioCadsus usuarioCadsus, Produto produto) throws ValidacaoException, DAOException {
        setModelObject(usuarioCadsus, produto, SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa());
    }
    
    public void setModelObject(UsuarioCadsus usuarioCadsus, Produto produto, Empresa estabelecimentoExecutante) throws ValidacaoException, DAOException {
        this.pnlHistoricoDispensacoes.setModelObject(usuarioCadsus, produto, estabelecimentoExecutante);
    }
    
}
