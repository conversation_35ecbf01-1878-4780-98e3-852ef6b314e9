package br.com.celk.view.unidadesaude.conduta;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Conduta;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaCondutaPage extends ConsultaPage<Conduta, List<BuilderQueryCustom.QueryParameter>> {

    private String descricao;
    private Long tipoConduta;

    public ConsultaCondutaPage() {
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("descricao"));
        form.add(DropDownUtil.getIEnumDropDown("tipoConduta",Conduta.TipoConduta.values(), true, "Todos"));
        
        setExibeExpandir(false);        
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        columns.add(getCustomColumn());
        Conduta proxy = on(Conduta.class);
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("classificacao"), proxy.getDescricaoClassificacaoEsus()));
        columns.add(createColumn(bundle("tipo"), proxy.getDescricaoTipoConduta()));
        columns.add(createColumn(bundle("codigoEsus"), proxy.getCodigoEsus()));

        return columns;
    }


    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<Conduta>() {
            @Override
            public void customizeColumn(final Conduta rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<Conduta>() {
                    @Override
                    public void action(AjaxRequestTarget target, Conduta conduta) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCondutaPage(conduta, false));
                    }
                });
 
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Conduta>() {
                    @Override
                    public void action(AjaxRequestTarget target, Conduta conduta) throws ValidacaoException, DAOException {
                        BOFactoryWicket.delete(conduta);
                        getPageableTable().populate(target);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<Conduta>() {
                    @Override
                    public void action(AjaxRequestTarget target, Conduta conduta) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroCondutaPage(conduta, true));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return Conduta.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(Conduta.class).getProperties());
            }

        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(Conduta.PROP_DESCRICAO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(Conduta.PROP_DESCRICAO, BuilderQueryCustom.QueryParameter.ILIKE, descricao));
        parameters.add(new QueryCustom.QueryCustomParameter(Conduta.PROP_TIPO_CONDUTA, tipoConduta));

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroCondutaPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaCondutaAtendimento");
    }
}
