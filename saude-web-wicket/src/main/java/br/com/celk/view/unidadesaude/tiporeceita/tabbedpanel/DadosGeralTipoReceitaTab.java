package br.com.celk.view.unidadesaude.tiporeceita.tabbedpanel;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.unidadesaude.tiporeceita.TipoReceitaDTO;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 * <AUTHOR>
 */
public class DadosGeralTipoReceitaTab extends TabPanel<TipoReceitaDTO> {

    private InputField<String> txtDescricao;
    private DropDown<String> dropDownReceita;
    private LongField txtQtdDiasAntibiotico;
    private LongField txtQtdMaxDiasAntibiotico;
    private DropDown<Long> dropDownFlagPortariaSes2082020;

    public DadosGeralTipoReceitaTab(String id, TipoReceitaDTO object) {
        super(id, object);
        init();
    }

    public void init() {
        add(txtDescricao = new RequiredInputField<String>(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_DESCRICAO)));
        add(getDropDownReceita());
        add(new InputField<Long>(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_QUANTIDADE_MAXIMA_REQUISICAO)));
        add(new RequiredInputField<Long>(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_DIAS_MAXIMO_TRATAMENTO)));
        add(DropDownUtil.getNaoSimDropDown(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_FLAG_IMPRIMIR_RECEITA)));
        WebMarkupContainer containerPortaria = new WebMarkupContainer("containerPortaria");
        containerPortaria.setOutputMarkupPlaceholderTag(true);
        containerPortaria.add(DropDownUtil.getNaoSimLongDropDown(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_FLAG_ATENDE_PORTARIA_SES2082020))
                .setEnabled(false));
        containerPortaria.setVisible(TipoReceita.RECEITA_AZUL.equals(
                object.getTipoReceita() != null &&
                        object.getTipoReceita().getTipoReceita() != null ? object.getTipoReceita().getTipoReceita() : null));
        add(containerPortaria);
        add(DropDownUtil.getNaoSimDropDown(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_CONTROLADA)));
        add(new InputField<String>(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_RECEITA_LISTA)));
        add(DropDownUtil.getSimNaoLongDropDown(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_FLAG_LISTA_RECEITA)));

        add(txtQtdDiasAntibiotico = new LongField(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_DIAS_VALIDADE_ANTIBIOTICO)));
        add(txtQtdMaxDiasAntibiotico = new LongField(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_DIAS_MAXIMO_VALIDADE_ANTIBIOTICO)));

        txtQtdDiasAntibiotico.setVMax(999L);
        txtQtdDiasAntibiotico.setVMin(0L);

        txtQtdMaxDiasAntibiotico.setVMax(999L);
        txtQtdMaxDiasAntibiotico.setVMin(0L);

    }

    public DropDown getDropDownReceita() {
        if (dropDownReceita == null) {
            dropDownReceita = new DropDown<String>(VOUtils.montarPath(TipoReceitaDTO.PROP_TIPO_RECEITA, TipoReceita.PROP_TIPO_RECEITA));
            dropDownReceita.addChoice(TipoReceita.RECEITA_AMARELA, BundleManager.getString("receitaAmarelaA"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_ANTIMICROBIANA, BundleManager.getString("antimicrobiana"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_AZUL, BundleManager.getString("azul"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_BRANCA, BundleManager.getString("branca"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_BASICA, BundleManager.getString("basica"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_MAGISTRAL, BundleManager.getString("masgistral"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO, BundleManager.getString("prescricaoAtedimento"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_PRESCRICAO_OCULOS, BundleManager.getString("prescricaoOculos"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_SOLICITACAO_MATERIAIS, BundleManager.getString("solicitacaoMeteriais"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_RECEITUARIO, BundleManager.getString("receituario"));
            dropDownReceita.addChoice(TipoReceita.RECEITA_BRANCA_C3,BundleManager.getString("rotulo_receita_branca_c3"));
        }
        return dropDownReceita;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return this.txtDescricao;
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("dadosGerais");
    }
}
