package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlNovoLancamentoServico extends Panel {

    private CompoundPropertyModel<ItemContaPaciente> model;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;
    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private DoubleField txtQuantidade;
    private DoubleField txtPrecoUnitario;
    private AbstractAjaxLink lnkInserirEmHoras;
    private DlgHoraMinutoToQuantidade dlgHoraMinutoToQuantidade;

    public PnlNovoLancamentoServico(String id) {
        super(id);
        init();
    }

    private void init() {
        Form<ItemContaPaciente> form = new Form("form", model = new CompoundPropertyModel(new ItemContaPaciente()));
        ItemContaPaciente proxy = on(ItemContaPaciente.class);

        form.add(autoCompleteConsultaProcedimento = new AutoCompleteConsultaProcedimento(path(proxy.getProcedimento())));

        form.add(txtQuantidade = new DoubleField(path(proxy.getQuantidade())));
        txtQuantidade.setVMax(99999999D);
        txtQuantidade.addAjaxUpdateValue();

        form.add(lnkInserirEmHoras = new AbstractAjaxLink("lnkInserirEmHoras") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                viewDlgHoraMinutoToQuantidade(target);
            }
        });

        form.add(txtPrecoUnitario = new DoubleField(path(proxy.getPrecoUnitario())));
        txtPrecoUnitario.setVMax(99999999D);

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                ItemContaPaciente itemContaPaciente = model.getObject();
                itemContaPaciente.setTipo(ItemContaPaciente.Tipo.SERVICO.value());
                itemContaPaciente.setStatus(ItemContaPaciente.Status.ABERTO.value());
                validarAdicionar(itemContaPaciente);
                onConfirmar(target, itemContaPaciente);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnFechar.setDefaultFormProcessing(false);

        setOutputMarkupId(true);

        add(form);
    }

    private void validarAdicionar(ItemContaPaciente itemContaPaciente) throws ValidacaoException, DAOException {
        validarCboProfissionalProcedimento(itemContaPaciente);

        if (itemContaPaciente.getProcedimento() == null) {
            throw new ValidacaoException(bundle("informeProcedimento"));
        }

        if (itemContaPaciente.getQuantidade() == null) {
            throw new ValidacaoException(bundle("informeQuantidade"));
        }
    }

    private void validarCboProfissionalProcedimento(ItemContaPaciente itemContaPaciente) throws ValidacaoException, DAOException {
        if (itemContaPaciente.getProfissional() != null) {
            QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
            param.setEmpresa(itemContaPaciente.getContaPaciente().getAtendimentoInformacao().getAtendimentoPrincipal().getEmpresa());
            param.setProfissional(itemContaPaciente.getProfissional());
            param.setProcedimento(itemContaPaciente.getProcedimento());

            TabelaCbo cbo = BOFactory.getBO(ProfissionalFacade.class).consultaProfissionalCargaHoraria(param);
            if (cbo == null) {
                throw new ValidacaoException(BundleManager.getString("cboProfissionalIncompativelCboProcedimento"));
            }
        }
    }

    private void viewDlgHoraMinutoToQuantidade(AjaxRequestTarget target) {
        if (dlgHoraMinutoToQuantidade == null) {
            WindowUtil.addModal(target, PnlNovoLancamentoServico.this, dlgHoraMinutoToQuantidade = new DlgHoraMinutoToQuantidade(WindowUtil.newModalId(PnlNovoLancamentoServico.this)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Double horaMinutoDecimal) throws DAOException, ValidacaoException {
                    txtQuantidade.limpar(target);
                    txtQuantidade.setComponentValue(horaMinutoDecimal);
                    target.add(txtQuantidade);
                }
            });
        }
        dlgHoraMinutoToQuantidade.show(target, txtQuantidade.getComponentValue());
    }

    public void limpar(AjaxRequestTarget target) {
        model.setObject(new ItemContaPaciente());
        autoCompleteConsultaProcedimento.limpar(target);
        txtQuantidade.limpar(target);
        txtPrecoUnitario.limpar(target);
        autoCompleteConsultaProfissional.limpar(target);
        update(target);
    }

    public void setItemContaPaciente(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) {
        limpar(target);
        model.setObject(itemContaPaciente);
        target.appendJavaScript(JScript.focusComponent(autoCompleteConsultaProcedimento.getTxtDescricao().getTextField()));
        update(target);
    }

    private void update(AjaxRequestTarget target) {
        target.appendJavaScript(JScript.removeAutoCompleteDrop());
        target.add(this);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPaciente itemContaPaciente) throws DAOException, ValidacaoException;

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
