package br.com.celk.view.importacaoexamescovid;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.importacaoexamecovid.dto.ResultadoImportacaoExameCovidDTO;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import ch.lambdaj.Lambda;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR> Santos
 */
@Private

public class ImportacaoExameCovidPage extends BasePage {

    private Form<ImportacaoExameCovidPage> form;
    private FileUploadField fileInput;
    private RepeatingView controls;
    private AbstractAjaxButton btnImportar;
    private List<FileUpload> fileUpload;
    private List<ResultadoImportacaoExameCovidDTO> resultadoImportacaoExameDTOS;

    public ImportacaoExameCovidPage() {
        super();
        initForm();
    }

    public void initForm() {
        form = new Form<>("form", new CompoundPropertyModel<>(this));

        fileInput = new FileUploadField("fileUpload");
        fileInput.setOutputMarkupId(true);
        form.add(fileInput);

        controls = new RepeatingView("controls");
        form.add(controls);

        btnImportar = new AbstractAjaxButton(controls.newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                acaoImportarArquivo(target);
            }
        };
        controls.add(btnImportar);

        btnImportar.add(new AttributeModifier("class", "refresh"));
        btnImportar.add(new AttributeModifier("value", BundleManager.getString("importarArquivo")));

        add(form);

        resultadoImportacaoExameDTOS = new ArrayList<>();
    }

    private void acaoImportarArquivo(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        try {
            enviarArquivo(target);
        } catch (ValidacaoException e) {
            throw new ValidacaoException(e);
        } finally {
            limparResultados(target);
        }
    }

    private void enviarArquivo(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        final List<FileUpload> arquivos = fileInput.getFileUploads();

        if (arquivos == null) {
            throw new ValidacaoException(BundleManager.getString("escolhaUmArquivoASerImportado"));
        }

        for (FileUpload arquivo : arquivos) {
            uploadArquivo(target, arquivo);
        }

    }

    private void uploadArquivo(AjaxRequestTarget target, FileUpload upload) throws ValidacaoException, DAOException {
        try {
            File newFile = criarTempFile(upload);

            resultadoImportacaoExameDTOS.add(BOFactoryWicket.getBO(ExameFacade.class).importarExameCovid(newFile));
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        mostrarResultado(target);
    }

    private File criarTempFile(FileUpload arquivo) throws IOException {
        File newFile = File.createTempFile("IMPORTACAO", "IPE");

        newFile.createNewFile();
        arquivo.writeTo(newFile);
        return newFile;
    }

    private void limparResultados(AjaxRequestTarget target) {
        resultadoImportacaoExameDTOS.clear();
        if (fileUpload != null) {
            fileUpload.clear();
        }
        fileInput.clearInput();

        target.add(fileInput);
    }

    private void mostrarResultado(AjaxRequestTarget target) {
        info(target, criarMensagem());
    }

    private String criarMensagem() {
        if (resultadoImportacaoExameDTOS == null || (!resultadoImportacaoExameDTOS.isEmpty() && resultadoImportacaoExameDTOS.get(0) == null)) {
           return BundleManager.getString("nenhumExameImportado");
        }

        Long totalImportacoes = Lambda.sum(resultadoImportacaoExameDTOS, Lambda.on(ResultadoImportacaoExameCovidDTO.class).getQuantidadeImportacoes());
        Long totalErros = Lambda.sum(resultadoImportacaoExameDTOS, Lambda.on(ResultadoImportacaoExameCovidDTO.class).getQuantidadeImportacoesDuplicadas());

        StringBuilder mensagem = new StringBuilder();
        if (totalImportacoes > 0) {
            mensagem.append(totalImportacoes);
            mensagem.append(totalImportacoes > 1 ? " exames importados" : " exame importado");
        } else {
            mensagem.append("Nenhum exame importado");
        }
        mensagem.append(" com sucesso e ");
        if (totalErros > 0) {
            mensagem.append(totalErros);
            mensagem.append(totalErros > 1 ? " exames não importados" : " exame não importado");
            mensagem.append(" por duplicidade");
        } else {
            mensagem.append("nenhum exame não importado por duplicidade");
        }
        return mensagem.toString();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("importacaoExamesCovid");
    }
}
