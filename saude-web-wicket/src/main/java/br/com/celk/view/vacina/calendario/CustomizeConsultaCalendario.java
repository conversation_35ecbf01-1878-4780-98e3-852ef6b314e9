package br.com.celk.view.vacina.calendario;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.vacina.Calendario;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaCalendario extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return Calendario.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(Calendario.class).getProperties();
    }
    
}
