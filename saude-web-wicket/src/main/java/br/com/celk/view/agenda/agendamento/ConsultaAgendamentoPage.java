package br.com.celk.view.agenda.agendamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeQueryPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgRecomendacoesAgenda;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoClassificacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaAgendamentoPage extends BasePage {

    private PnlDatePeriod pnlDatePeriod;

    private DlgDetalhesAgendamento dialogDetalhesAgendamento;
    private String nomePaciente;
    private InputField inputFieldResponsavel;
    private InputField inputCodigoPaciente;
    private boolean hasPermission;
    private Form<AgendaGradeAtendimentoDTOParam> form;
    private List<Empresa> empresaOrigem;
    private DlgImpressaoObject<RelatorioImprimirComprovanteAgendamentoDTOParam> dlgImpressao;
    private DlgRecomendacoesAgenda dlgRecomendacoesAgenda;
    private List<AgendaGradeAtendimentoHorario> agendamentosList = new ArrayList<>();

    public ConsultaAgendamentoPage(PageParameters parameters) {
        super(parameters);
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(new AgendaGradeAtendimentoDTOParam()));
        hasPermission = isActionPermitted(Permissions.EMPRESA);

        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true));
        form.add(inputFieldResponsavel = new InputField("nomePaciente", new PropertyModel(this, "nomePaciente")));
        form.add(new DateChooser("dataNascimento"));
        form.add(inputCodigoPaciente = new InputField("codigoPaciente"));

        form.add(new AutoCompleteConsultaProfissional("profissional"));
        form.add(new AutoCompleteConsultaEmpresa("empresa"));

        AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresaOrigem", new PropertyModel<Empresa>(this, "empresaOrigem"));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!hasPermission);
        form.add(autoCompleteConsultaEmpresa);
        form.add(new AutoCompleteConsultaUsuario("usuario"));
        form.add(pnlDatePeriod = new PnlDatePeriod("datePeriod"));
        form.add(populateDropDownSituacao(new DropDown<Long>("situacao")));
        form.add(populateDropDownTipoData(new DropDown<AgendaGradeAtendimentoDTOParam.TipoData>("tipoData")));

        PageableTable pageableTable = getPageableTable();
        form.add(new ProcurarButton<AgendaGradeAtendimentoDTOParam>("btnProcurar", pageableTable) {

            @Override
            public AgendaGradeAtendimentoDTOParam getParam() {
                return ConsultaAgendamentoPage.this.getParam();
            }

        });

        form.add(pageableTable);
        form.add(dialogDetalhesAgendamento = new DlgDetalhesAgendamento("dialogDetalhesAgendamento"));
        pnlDatePeriod.setModelObject(new DatePeriod(Data.getDataAtual(), null));

        add(form);

//        addModal(dlgImpressao = new DlgImpressaoObject<RelatorioImprimirComprovanteAgendamentoDTOParam>(newModalId(), BundleManager.getString("msgComprovanteAgendamentoGeradoComSucessoDesejaImprimir")) {
//            @Override
//            public DataReport getDataReport(RelatorioImprimirComprovanteAgendamentoDTOParam reportParam) throws ReportException {
//                    return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(reportParam);
//            }
//        });
    }

    private DropDown<Long> populateDropDownSituacao(DropDown<Long> dropDown) {
        dropDown.addChoice(null, BundleManager.getString("todas"));
        dropDown.addChoice(AgendaGradeAtendimentoHorario.STATUS_AGENDADO, BundleManager.getString("agendado"));
        dropDown.addChoice(AgendaGradeAtendimentoHorario.STATUS_CONCLUIDO, BundleManager.getString("confirmado"));
        dropDown.addChoice(AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU, BundleManager.getString("nao_compareceu"));
        dropDown.addChoice(AgendaGradeAtendimentoHorario.STATUS_CANCELADO, BundleManager.getString("cancelado"));

        return dropDown;
    }

    private DropDown<AgendaGradeAtendimentoDTOParam.TipoData> populateDropDownTipoData(DropDown<AgendaGradeAtendimentoDTOParam.TipoData> dropDown) {

        dropDown.addChoice(AgendaGradeAtendimentoDTOParam.TipoData.DATA_AGENDAMENTO, BundleManager.getString("agendamento"));
        dropDown.addChoice(AgendaGradeAtendimentoDTOParam.TipoData.DATA_CADASTRO, BundleManager.getString("cadastro"));

        return dropDown;
    }

    public PageableTable getPageableTable() {
        PageableTable pageableTable = new PageableTable("table", getColumns(), getDataProvider());
        pageableTable.setScrollX("100%");
        return pageableTable;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoHorarioDTO proxy = on(AgendaGradeAtendimentoHorarioDTO.class);
        AgendaGradeAtendimentoHorario proxyAgah = on(AgendaGradeAtendimentoHorario.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(BundleManager.getString("tipo_procedimento"), proxyAgah.getTipoProcedimento().getDescricao(), proxy.getTipoProcedimentoFormatado()));
        columns.add(createSortableColumn(BundleManager.getString("paciente"), proxyAgah.getPaciente(), proxy.getNomePaciente()));
        columns.add(createSortableColumn(BundleManager.getString("local_agendamento"), proxyAgah.getLocalAgendamento().getDescricao(), proxy.getAgendaGradeAtendimentoHorario().getLocalAgendamento().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("data"), proxyAgah.getDataAgendamento(), proxy.getDataAtendimentoFormatadoDiaHora()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxyAgah.getStatus(), proxy.getAgendaGradeAtendimentoHorario().getSituacao()));
        columns.add(createSortableColumn(BundleManager.getString("unidade_solicitante"), proxyAgah.getEmpresaOrigem().getDescricao(), proxy.getAgendaGradeAtendimentoHorario().getEmpresaOrigem().getDescricao()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoHorarioDTO>() {
            @Override
            public void customizeColumn(AgendaGradeAtendimentoHorarioDTO rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AgendaGradeAtendimentoHorarioDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new DetalhesAgendamentoPage(modelObject.getAgendaGradeAtendimentoHorario().getCodigo(), ConsultaAgendamentoPage.class));
                    }
                }).setTitleBundleKey("detalhar");

                addAction(ActionType.IMPRIMIR, rowObject, new IModelAction<AgendaGradeAtendimentoHorarioDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgRecomendacoesAgenda(target, modelObject);
                    }
                }).setTitleBundleKey("gerarComprovanteAgendamento")
                        .setIcon(Icon.CLIPBOARD_PAST)
                        .setEnabled(!rowObject.getAgendaGradeAtendimentoHorario().getStatus().equals(AgendaGradeAtendimentoHorario.STATUS_CANCELADO));

            }
        };
    }

    private void initDlgRecomendacoesAgenda(AjaxRequestTarget target, AgendaGradeAtendimentoHorarioDTO modelObject) {
        if (dlgRecomendacoesAgenda == null) {
            addModal(target, dlgRecomendacoesAgenda = new DlgRecomendacoesAgenda(newModalId()) {
                @Override
                public DataReport onImprimir() throws ReportException {
                    return imprimirComprovanteAgendamento();
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }

        dlgRecomendacoesAgenda.show(target, agendamentosList(modelObject.getAgendaGradeAtendimentoHorario()), true);
    }

    private IPagerProvider<AgendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParam> getDataProvider() {
        return new CustomizeQueryPagerProvider<AgendaGradeAtendimentoHorarioDTO, AgendaGradeAtendimentoDTOParam>() {
            @Override
            public DataPagingResult executeQueryPager(DataPaging dataPaging) throws DAOException, ValidacaoException {
                return BOFactoryWicket.getBO(AgendamentoFacade.class).consultarAgendamentosPager(dataPaging);
            }

            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam<String>(AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO, true);
            }
        };
    }

    private AgendaGradeAtendimentoDTOParam getParam() {
        AgendaGradeAtendimentoDTOParam param = form.getModel().getObject();

        param.setExibirCancelados(true);
        param.setNomeUsuarioCadsus(nomePaciente);
        //param.getUsuarioCadsus().setCodigo(codigo);
        param.setPermission(hasPermission);

        if (CollectionUtils.isNotNullEmpty(empresaOrigem)) {
            param.setEmpresas(new ArrayList<Long>());
            for (Empresa empresa : empresaOrigem) {
                param.getEmpresas().add(empresa.getCodigo());
            }
        }

        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO, TipoProcedimentoClassificacao.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_TIPO_PROCEDIMENTO_CLASSIFICACAO, TipoProcedimentoClassificacao.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_STATUS));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_SOLICITACAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_NOME_PACIENTE));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO, Empresa.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_EMPRESA_ORIGEM, Empresa.PROP_DESCRICAO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_DATA_CADASTRO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_STATUS));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO));
        param.getConfigureParam().addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorarioDTO.PROP_AGENDA_GRADE_ATENDIMENTO_HORARIO, AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL));

        return param;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consulta_agendamento");
    }

    private DataReport imprimirComprovanteAgendamento() throws ReportException {
        List<AgendaGradeAtendimentoHorario> agahList = agendamentosList;

        if (CollectionUtils.isNotNullEmpty(agahList)) {
            RelatorioImprimirComprovanteAgendamentoDTOParam reportParam = new RelatorioImprimirComprovanteAgendamentoDTOParam();
            reportParam.setAgendaGradeAtendimentoHorarioList(agahList);

            return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(reportParam);
        }
        return null;
    }

    private List<AgendaGradeAtendimentoHorario> agendamentosList(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
        agendamentosList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS)).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_TIPO_PROCEDIMENTO)).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, AgendaGradeAtendimentoHorario.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_CODIGO, agendaGradeAtendimentoHorario.getCodigo()))
                .start().getList();

        return agendamentosList;
    }
}
