package br.com.celk.view.controle.sessoes;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.usuario.autocomplete.AutoCompleteConsultaUsuario;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.acessotemporeal.AcessoTempoRealFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.controle.dto.RelatorioControleAcessoDTO;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.SessaoWeb;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class SessoesPage extends BasePage {

    private Empresa empresa;
    private Table tblUsuarios;
    private DlgImpressaoObject<RelatorioControleAcessoDTO> dlgImpressao;

    private List<SessaoWeb> sessoes;
    private DropDown<Empresa> empresaDropDown;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaUsuario autoCompleteConsultaUsuario;
    private Empresa unidadeLogada;
    private Usuario usuario;

    private DateChooser dchDataEntrada;
    private DateChooser dchDataSaida;
    private Date dataEntrada;
    private Date dataSaida;

    public SessoesPage() {
        init();
    }

    private void init() {
        SessaoWeb on = on(SessaoWeb.class);
        Form form = new Form("form");
        form.setDefaultModel(new CompoundPropertyModel(this));
        autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(on.getUnidadeLogada()));
        autoCompleteConsultaUsuario = new AutoCompleteConsultaUsuario(path(on.getUsuario()));
        form.add(autoCompleteConsultaEmpresa);
        form.add(autoCompleteConsultaUsuario);

        form.add(dchDataEntrada = new RequiredDateChooser(path(on.getDataEntrada())));
        dchDataEntrada.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()))
                .setLabel(new Model<>(bundle("dataEntrada")))
                .setEnabled(true);
        dchDataEntrada.setComponentValue(Data.removeDias(DataUtil.getDataAtual(), 15));
        dchDataEntrada.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dchDataEntrada.getComponentValue() != null) {
                    dchDataSaida.setMinDateAjax(target, dchDataEntrada.getComponentValue());
                    dchDataSaida.setMaxDateAjax(target, Data.addDias(dchDataEntrada.getComponentValue(), 15));
                } else {
                    dchDataSaida.setMinDateAjax(target, null);
                    dchDataSaida.setMaxDateAjax(target, DataUtil.getDataAtual());
                }
                target.add(dchDataSaida);
            }
        });

        form.add(dchDataSaida = new RequiredDateChooser(path(on.getDataSaida())));
        dchDataSaida.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()))
                .setLabel(new Model<>(bundle("dataSaida")))
                .setEnabled(true);
        dchDataSaida.setComponentValue(DataUtil.getDataAtual());
        dchDataSaida.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dchDataSaida.getComponentValue() != null) {
                    dchDataEntrada.setMaxDateAjax(target, dchDataSaida.getComponentValue());
                    dchDataEntrada.setMinDateAjax(target, Data.removeDias(dchDataSaida.getComponentValue(), 15));
                } else {
                    dchDataEntrada.setMaxDateAjax(target, DataUtil.getDataAtual());
                    dchDataEntrada.setMinDateAjax(target, null);
                }
                target.add(dchDataEntrada);
            }
        });


        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                atualizarSessoes(target);
            }
        });

        form.add(tblUsuarios = new Table("tblUsuarios", getColumns(), getCollectionProvider()));

        tblUsuarios.populate();
        add(form);

        atualizarSessoes(null);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        SessaoWeb on = on(SessaoWeb.class);

        columns.add(createColumn(bundle("usuario"), on.getUsuario().getNome()));
        columns.add(createColumn(bundle("unidade"), on.getDescricaoUnidadeUsuarioInternoExterno()));
        columns.add(new DateColumn(bundle("login"), path(on.getDataEntrada())).setPattern("dd/MM/yyyy - HH:mm:ss"));
        columns.add(new DateColumn(bundle("logout"), path(on.getDataSaida())).setPattern("dd/MM/yyyy - HH:mm:ss"));
        columns.add(createColumn(bundle("ip"), on.getHost()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) {
                return sessoes;
            }
        };
    }

    private void atualizarSessoes(AjaxRequestTarget target) {
        sessoes = loadSessoes();
        if (target != null) {
            tblUsuarios.update(target);
        }
    }

    private List<SessaoWeb> loadSessoes() {
        return LoadManager.getInstance(SessaoWeb.class)
                .addProperties(new HQLProperties(SessaoWeb.class).getProperties())
                .addProperty(VOUtils.montarPath(SessaoWeb.PROP_USUARIO, Usuario.PROP_NOME))
                .addParameter(new QueryCustom.QueryCustomParameter(SessaoWeb.PROP_DATA_ENTRADA, BuilderQueryCustom.QueryParameter.MAIOR, dchDataEntrada.getComponentValue()))
                .addParameter(new QueryCustom.QueryCustomParameter(SessaoWeb.PROP_DATA_SAIDA, BuilderQueryCustom.QueryParameter.MENOR, Data.addDias(dchDataSaida.getComponentValue(), 1)))
                .addParameter(new QueryCustom.QueryCustomParameter(SessaoWeb.PROP_UNIDADE_LOGADA, autoCompleteConsultaEmpresa.getComponentValue()))
                .addParameter(new QueryCustom.QueryCustomParameter(SessaoWeb.PROP_USUARIO, autoCompleteConsultaUsuario.getComponentValue()))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(SessaoWeb.PROP_USUARIO, Usuario.PROP_NOME), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .addSorter(new QueryCustom.QueryCustomSorter(SessaoWeb.PROP_UNIDADE_LOGADA, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .addSorter(new QueryCustom.QueryCustomSorter(SessaoWeb.PROP_DATA_ENTRADA, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();
    }


    @Override
    public String getTituloPrograma() {
        return bundle("sessoes");
    }
}
