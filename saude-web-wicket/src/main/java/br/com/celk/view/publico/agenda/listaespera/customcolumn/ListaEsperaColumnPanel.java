package br.com.celk.view.publico.agenda.listaespera.customcolumn;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.link.ReportLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.controle.agendadorprocessos.customcolumn.AgendadorProcessosColumnPanel;
import br.com.celk.view.publico.agenda.listaespera.dialog.DlgDetalhesSolicitacaoListaEspera;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.resumocontatoagendamento.dto.ResumoContatoAgendamentoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.cadsus.interfaces.dto.FichaPacienteDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.dto.RelatorioImpressaoAtendimentoDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AgendadorProcesso;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoInformacao;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

public class ListaEsperaColumnPanel extends Panel {

    private AjaxLink btnConsultar;
    private AgendamentoListaEsperaDTO dto;
    private DlgDetalhesSolicitacaoListaEspera dlgDetalhesSolicitacaoListaEspera;

    public ListaEsperaColumnPanel(String id, AgendamentoListaEsperaDTO dto) {
        super(id);
        this.dto = dto;
        init();
    }

    private void init() {

        add(btnConsultar = new AbstractAjaxLink("btnConsultar") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgDetalhesSolicitacaoListaEspera(target);
                if (dlgDetalhesSolicitacaoListaEspera != null) {
                    dlgDetalhesSolicitacaoListaEspera.show(target, dto);
                }
            }

            @Override
            public boolean isEnabled() {
                return dto.isExibirLupaSolicitacaoAgendamento();
            }
        });

        btnConsultar.add(new AttributeModifier("title", BundleManager.getString("consultar")));
    }

    private void initDlgDetalhesSolicitacaoListaEspera(AjaxRequestTarget target) {
        if (dlgDetalhesSolicitacaoListaEspera == null) {
            WindowUtil.addModal(target, this, dlgDetalhesSolicitacaoListaEspera = new DlgDetalhesSolicitacaoListaEspera(WindowUtil.newModalId(this)) {
            });
        }
    }

    public AjaxLink getBtnConsultar() {
        return btnConsultar;
    }
}
