package br.com.celk.view.basico.profissional;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.ProfissionalCargaHoraria;
import br.com.ksisolucoes.vo.cadsus.ProfissionalHistorico;
import br.com.ksisolucoes.vo.hospital.tiss.EloTissProfissionalConvenio;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroProfissionalDTO implements Serializable{
    
    public static final String PROP_PROFISSIONAL = "profissional";
    public static final String PROP_VINCULOS = "vinculos";
    
    private Profissional profissional;
    private List<ProfissionalCargaHoraria> vinculos = new ArrayList<ProfissionalCargaHoraria>();
    private List<ProfissionalHistorico> historicosVinculos = new ArrayList<ProfissionalHistorico>(); 
    private List<EloTissProfissionalConvenio> elosTiss = new ArrayList<EloTissProfissionalConvenio>(); 

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public List<ProfissionalCargaHoraria> getVinculos() {
        return vinculos;
    }

    public void setVinculos(List<ProfissionalCargaHoraria> vinculos) {
        this.vinculos = vinculos;
    }

    public List<ProfissionalHistorico> getHistoricosVinculos() {
        return historicosVinculos;
    }

    public void setHistoricoVinculos(List<ProfissionalHistorico> historicosVinculos) {
        this.historicosVinculos = historicosVinculos;
    }

    public List<EloTissProfissionalConvenio> getElosTiss() {
        return elosTiss;
    }

    public void setElosTiss(List<EloTissProfissionalConvenio> elosTiss) {
        this.elosTiss = elosTiss;
    }
}
