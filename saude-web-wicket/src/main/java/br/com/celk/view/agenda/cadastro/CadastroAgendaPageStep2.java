package br.com.celk.view.agenda.cadastro;

import br.com.celk.agendamento.CadastroAgendaBehavior;
import br.com.celk.appcidadao.dto.agenda.ScheduleUpdateDTO;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.duracaofield.HoraMinutoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.DayColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.util.validacao.ValidacaoProcesso;
import br.com.celk.view.agenda.cadastro.dialog.DlgAgendaGradeExame;
import br.com.celk.view.agenda.cadastro.dialog.DlgAgendaGradeHorario;
import br.com.celk.view.atendimento.tipoatendimentoagenda.autocomplete.AutoCompleteConsultaTipoAtendimentoAgenda;
import br.com.ksisolucoes.agendamento.dto.*;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.appcidadao.interfaces.facade.AppCidadaoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.dto.MensagemAnexoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.*;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.ExamePrestadorContrato;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroAgendaPageStep2 extends BasePage {

    private Form<AgendaGradeAtendimento> form;
    private boolean isEditando;
    private final Agenda agenda;
    private DateChooserAjax dateChooserData;
    private HoraMinutoField txtHoraInicial;
    private HoraMinutoField txtHoraFinal;
    private AutoCompleteConsultaTipoAtendimentoAgenda autoCompleteConsultaTipoAtendimentoAgenda;
    private InputField txtQuantidadeAtendimento;
    private InputField txtTempoMedio;
    private String descricaoDia;
    private Label lblDescricaoDia;
    private Table tblAgendaGradeAtendimento;
    private List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentoHorariosAnterioresDTOList = new ArrayList<AgendaGradeAtendimentoHorariosDTO>();
    private List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentoHorariosAtuaisDTOList = new ArrayList<AgendaGradeAtendimentoHorariosDTO>();
    private List<AgendaGradeAtendimentoHorariosDTO> dtosPersistir = new ArrayList<AgendaGradeAtendimentoHorariosDTO>();
    private final List<AgendaGradeAtendimento> agendasDiariasAlteradasOcorrencias = new ArrayList<AgendaGradeAtendimento>();
    private final List<AgendaGrade> agendaGradeDeletarList = new ArrayList<AgendaGrade>();
    private final CadastroAgendaBehavior cadastroAgendaBehavior;
    private DlgCopiarAgenda dlgCopiarAgenda;
    private DlgAgendaGradeHorario dlgAgendaGradeHorario;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private DlgConfirmacaoSimNao dlgConfirmacaoFeriadoSimNao;
    private DlgConfirmacaoSimNao dlgConfirmacaoConflitoCopiaSimNao;
    private DlgAlterarVagas dlgAlterarVagas;
    private final boolean tipoAgendaHorario;
    private final boolean tipoAgendaPersonalizada;
    private final MensagemAnexoDTO anexoDTO;
    private final boolean possuiAnexo;
    private DlgAgendaGradeExame dlgAgendaGradeExame;
    private final Long idPrestador;
    private final Long idTipoProcedimento;
    private CheckBoxSimNao checkVisualizarGradesAnteriores;
    private InputField quantidadeDiasField;
    private int quantidadeDias;
    private AbstractAjaxButton btnAtualizarGrade;
    private ExamePrestadorContrato contrato;

    CadastroAgendaPageStep2(Agenda agenda, MensagemAnexoDTO anexoDTO, boolean possuiAnexo, boolean tipoAgendaHorario, boolean tipoAgendaPersonalizada, Long idPrestador, Long idTipoProcedimento) {
        this.agenda = agenda;
        this.anexoDTO = anexoDTO;
        this.possuiAnexo = possuiAnexo;
        cadastroAgendaBehavior = new CadastroAgendaBehavior(agenda);
        this.tipoAgendaHorario = tipoAgendaHorario;
        this.tipoAgendaPersonalizada = tipoAgendaPersonalizada;
        this.idPrestador = idPrestador;
        this.idTipoProcedimento = idTipoProcedimento;
    }

    CadastroAgendaPageStep2(Agenda agenda, MensagemAnexoDTO anexoDTO, boolean possuiAnexo, boolean tipoAgendaHorario, boolean tipoAgendaPersonalizada, boolean isEditando, Long idPrestador, Long idTipoProcedimento) {
        this.isEditando = isEditando;
        this.agenda = agenda;
        this.anexoDTO = anexoDTO;
        this.possuiAnexo = possuiAnexo;
        cadastroAgendaBehavior = new CadastroAgendaBehavior(agenda);
        this.tipoAgendaHorario = tipoAgendaHorario;
        this.tipoAgendaPersonalizada = tipoAgendaPersonalizada;
        this.idPrestador = idPrestador;
        this.idTipoProcedimento = idTipoProcedimento;
    }

    @Override
    protected void postConstruct() {
        form = new Form<AgendaGradeAtendimento>("form", new CompoundPropertyModel<AgendaGradeAtendimento>(new AgendaGradeAtendimento()));
        AgendaGradeAtendimento proxy = on(AgendaGradeAtendimento.class);

        form.add(dateChooserData = new DateChooserAjax(path(proxy.getAgendaGrade().getData())));
        dateChooserData.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                descricaoDia = "";
                if (dateChooserData.getModelObject() != null) {
                    descricaoDia = new SimpleDateFormat("EEEE", Bundle.getLocale()).format(dateChooserData.getModelObject());
                }
                target.add(lblDescricaoDia);
            }
        });

        form.add(lblDescricaoDia = new Label("descricaoDia", new PropertyModel<String>(this, "descricaoDia")));
        lblDescricaoDia.setOutputMarkupId(true);
        form.add(txtHoraInicial = new HoraMinutoField(path(proxy.getAgendaGrade().getHoraInicial())));
        form.add(txtHoraFinal = new HoraMinutoField(path(proxy.getAgendaGrade().getHoraFinal())));
        form.add(txtQuantidadeAtendimento = new InputField<Long>(path(proxy.getQuantidadeAtendimento())));
        form.add(autoCompleteConsultaTipoAtendimentoAgenda = new AutoCompleteConsultaTipoAtendimentoAgenda(path(proxy.getTipoAtendimentoAgenda())));
        form.add(txtTempoMedio = new InputField<Long>(path(proxy.getTempoMedio())));
        containerTabelaGrade(form);

        tblAgendaGradeAtendimento.populate();

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(new VoltarButton("btnVoltar"));

        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                if (CollectionUtils.isEmpty(agendaGradeAtendimentoHorariosAtuaisDTOList)) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_voce_deve_agendar_horarios"));
                }
                salvar(target);
            }
        }));

        add(form);

        if (agenda.getCodigo() != null) {
            carregaListaAtendimentos(0);
        }
        if (!isTipoAgendaPersonalizada()) {
            txtHoraFinal.setEnabled(false);
        } else {
            txtQuantidadeAtendimento.setComponentValue(1L);
            txtQuantidadeAtendimento.setEnabled(false);
        }

        setaTempoMedio();
        adicionaDialog();
        ordenarLista();
    }

    private void adicionaDialog() {
        addModal(dlgAlterarVagas = new DlgAlterarVagas(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws DAOException, ValidacaoException {
                for (int i = 0; i < agendaGradeAtendimentoHorariosAtuaisDTOList.size(); i++) {
                    if (agendaGradeAtendimentoHorariosAtuaisDTOList.get(i) == dto) {
                        agendaGradeAtendimentoHorariosAtuaisDTOList.remove(i);
                        if (dto.getAgendaGradeAtendimento().getCodigo() != null) {
                            agendasDiariasAlteradasOcorrencias.add(dto.getAgendaGradeAtendimento());
                        }
                        break;
                    }
                }
                dtosPersistir.add(dto);
                agendaGradeAtendimentoHorariosAtuaisDTOList.add(dto);
                tblAgendaGradeAtendimento.update(target);
            }
        });
    }

    private void setaTempoMedio() {
        autoCompleteConsultaTipoAtendimentoAgenda.add(new ConsultaListener<TipoAtendimentoAgenda>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoAtendimentoAgenda object) {
                txtTempoMedio.setComponentValue(form.getModel().getObject().getTipoAtendimentoAgenda().getTempoMedio());
                target.add(txtTempoMedio);
            }
        });

        autoCompleteConsultaTipoAtendimentoAgenda.add(new RemoveListener<TipoAtendimentoAgenda>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoAtendimentoAgenda object) {
                txtTempoMedio.setComponentValue(null);
                target.add(txtTempoMedio);
            }
        });
    }

    private void containerTabelaGrade(Form<AgendaGradeAtendimento> form) {
        form.add(checkVisualizarGradesAnteriores = new CheckBoxSimNao("checkVisualizarGradesAnteriores", new Model<>(RepositoryComponentDefault.NAO)) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                quantidadeDiasField.setEnabled(RepositoryComponentDefault.SIM.equals(checkVisualizarGradesAnteriores.getComponentValue()));
                btnAtualizarGrade.setEnabled(RepositoryComponentDefault.SIM.equals(checkVisualizarGradesAnteriores.getComponentValue()));
                target.add(quantidadeDiasField);
                target.add(btnAtualizarGrade);
            }
        });

        form.add(quantidadeDiasField = new InputField<Long>("quantidadeDias", new PropertyModel(this, "quantidadeDias")));
        quantidadeDiasField.add(new Tooltip().setText("msgQuantidadeDiasGradesAnteriores"));

        checkVisualizarGradesAnteriores.setEnabled(agenda.getCodigo() != null);
        quantidadeDiasField.setEnabled(agenda.getCodigo() != null);

        form.add(tblAgendaGradeAtendimento = new CadastroAgendaTableColor("tblAgendaGradeAtendimento", getColumns(), getCollectionProvider()));
        form.add(btnAtualizarGrade = criarBotaoAtualizarGrade(RepositoryComponentDefault.SIM.equals(checkVisualizarGradesAnteriores.getComponentValue())));
        checkVisualizarGradesAnteriores.addAjaxUpdateValue();
        checkVisualizarGradesAnteriores.setOutputMarkupId(true);
        quantidadeDiasField.addAjaxUpdateValue();
        quantidadeDiasField.setOutputMarkupId(true);
        quantidadeDiasField.setEnabled(RepositoryComponentDefault.SIM.equals(checkVisualizarGradesAnteriores.getComponentValue()));
    }

    private List<IColumn> getColumns() {

        ColumnFactory columnFactory = new ColumnFactory(AgendaGradeAtendimentoHorariosDTO.class);
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoHorariosDTO proxy = on(AgendaGradeAtendimentoHorariosDTO.class);
        columns.add(getActionColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("data"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial())));
        columns.add(columnFactory.createColumn(BundleManager.getString("diaSemana"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData()), DayColumn.class));
        columns.add(columnFactory.createColumn(BundleManager.getString("tipo_atendimento"), path(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao())));
        columns.add(columnFactory.createColumn(BundleManager.getString("vagas"), path(proxy.getAgendaGradeAtendimento().getQuantidadeAtendimento())));
        columns.add(columnFactory.createColumn(BundleManager.getString("tempo_medio"), path(proxy.getAgendaGradeAtendimento().getTempoMedio())));
        if(!isTipoAgendaPersonalizada()){
            columns.add(columnFactory.createColumn(BundleManager.getString("possuiExames"), path(proxy.getDescricaoPossuiExames())));
        }
        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoHorariosDTO>() {
            @Override
            public void customizeColumn(AgendaGradeAtendimentoHorariosDTO rowObject) {
                addAction(ActionType.CLONAR, rowObject, new IModelAction<AgendaGradeAtendimentoHorariosDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO modelObject) throws ValidacaoException, DAOException {
                        copiar(target, modelObject.getAgendaGradeAtendimento().getAgendaGrade(), modelObject);
                    }
                }).setTitleBundleKey("copiar");

                addAction(ActionType.EDITAR, rowObject, new IModelAction<AgendaGradeAtendimentoHorariosDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO modelObject) throws ValidacaoException, DAOException {
                        dlgAlterarVagas.show(target, agenda, modelObject, agendaGradeAtendimentoHorariosAtuaisDTOList);
                    }
                }).setTitleBundleKey("alterarVagas")
                    .setEnabled(!isTipoAgendaHorario() && !isTipoAgendaPersonalizada() && dataMaiorOuIgualAtual(rowObject.getAgendaGradeAtendimento().getAgendaGrade().getData()));

                addAction(ActionType.AGENDAR, rowObject, new IModelAction<AgendaGradeAtendimentoHorariosDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO modelObject) throws ValidacaoException, DAOException {
                        informarHorarios(target, modelObject);
                    }
                }).setTitleBundleKey("horarios")
                    .setIcon(Icon.CLOCK)
                    .setEnabled(isTipoAgendaHorario() && dataMaiorOuIgualAtual(rowObject.getAgendaGradeAtendimento().getAgendaGrade().getData()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AgendaGradeAtendimentoHorariosDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO modelObject) throws ValidacaoException, DAOException {
                        cadastroAgendaBehavior.validaExcluir(modelObject.getAgendaGradeAtendimento());
                        removerItem(target, modelObject);
                        tblAgendaGradeAtendimento.update(target);
                    }
                }).setEnabled(dataMaiorOuIgualAtual(rowObject.getAgendaGradeAtendimento().getAgendaGrade().getData()));

                addAction(ActionType.TRANSFERENCIA, rowObject, new IModelAction<AgendaGradeAtendimentoHorariosDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgAgendaGradeExame(target, modelObject);
                    }
                }).setTitleBundleKey("examesProcedimentos")
                    .setIcon(Icon.DOC_LINES)
                    .setVisible(!isTipoAgendaPersonalizada());
            }
        };
    }

    private void initDlgAgendaGradeExame(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto){
        addModal(target, dlgAgendaGradeExame = new DlgAgendaGradeExame(newModalId(), dto.getAgendaGradeAtendimento().getAgendaGrade().getData()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) {
                tblAgendaGradeAtendimento.update(target);
                dtosPersistir.add(dto);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws ValidacaoException, DAOException {
                tblAgendaGradeAtendimento.update(target);
                dtosPersistir.add(dto);
            }
        });
        dto.setIdPrestador(idPrestador);
        dto.setIdTipoProcedimnto(idTipoProcedimento);
        dlgAgendaGradeExame.show(target, dto);
    }

    private void informarHorarios(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) {
        if (dlgAgendaGradeHorario == null) {
            addModal(target, dlgAgendaGradeHorario = new DlgAgendaGradeHorario(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws ValidacaoException, DAOException {

                    dtosPersistir.add(dto);
                    tblAgendaGradeAtendimento.update(target);
                }
            });
        }
        dlgAgendaGradeHorario.show(target, dto, agenda, agendaGradeAtendimentoHorariosAtuaisDTOList);
    }

    private ExamePrestadorContrato getContratoPrestador() {
        if (contrato == null) {
            contrato = LoadManager.getInstance(ExamePrestadorContrato.class)
                    .addProperty(VOUtils.montarPath(ExamePrestadorContrato.PROP_DATA_VALIDADE))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_PRESTADOR, Empresa.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, idPrestador))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExamePrestadorContrato.PROP_EXAME_PRESTADOR, ExamePrestador.PROP_TIPO_EXAME, TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO), BuilderQueryCustom.QueryParameter.IGUAL, idTipoProcedimento))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestadorContrato.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.IGUAL, ExamePrestadorContrato.Situacao.ATIVO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExamePrestadorContrato.PROP_DATA_VALIDADE, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, DataUtil.getDataAtualSemHora()))
                    .addSorter(new QueryCustom.QueryCustomSorter(ExamePrestadorContrato.PROP_DATA_VALIDADE, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .setMaxResults(1)
                    .start().getVO();
        }

        return contrato;
    }

    private boolean isTipoAgendaHorario() {
        return tipoAgendaHorario;
    }

    private boolean isTipoAgendaPersonalizada() {
        return tipoAgendaPersonalizada;
    }

    private void copiar(AjaxRequestTarget target, AgendaGrade agendaGrade, AgendaGradeAtendimentoHorariosDTO modelObject) {
        if (dlgCopiarAgenda == null) {
            dlgCopiarAgenda = new DlgCopiarAgenda(newModalId()) {
                @Override
                public void onOk(AjaxRequestTarget target, CopiaAgendaDTO dto, AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO) throws ValidacaoException, DAOException {
                    if (getContratoPrestador() != null && getContratoPrestador().getDataValidade() != null && dto.getDataTermino().after(getContratoPrestador().getDataValidade())) {
                        throw new ValidacaoException(new ValidacaoProcesso(BundleManager.getString("dataTerminoMaiorDataValidade", Data.formatar(getContratoPrestador().getDataValidade()))));
                    }

                    if (isTipoAgendaHorario()) {
                        copiarAgendaHorario(target, dto);
                    } else {
                        copiarAgendaDiariaPersonalizada(target, dto, agendaGradeAtendimentoHorariosDTO);
                    }
                }
            };
            addModal(target, dlgCopiarAgenda);
        }
        dlgCopiarAgenda.show(target, agendaGrade, modelObject);
    }

    private void copiarAgendaDiariaPersonalizada(AjaxRequestTarget target, final CopiaAgendaDTO dto, final AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO) throws ValidacaoException, DAOException {
        final List<AgendaGradeAtendimento> agendasCopiadas = cadastroAgendaBehavior.copiarAgendaDiariaPersonalizada(agendaGradeAtendimentoHorariosAtuaisDTOList, dto);

        if (CollectionUtils.isNotNullEmpty(agendasCopiadas)) {
            StringBuilder sb = new StringBuilder();
            String mensagemHorarioConflitante;
            for (final AgendaGradeAtendimento item : agendasCopiadas) {
                mensagemHorarioConflitante = cadastroAgendaBehavior.validarConflitosAgendasSalvas(item);
                if (mensagemHorarioConflitante != null) {
                    sb.append(Data.formatar(item.getAgendaGrade().getData()));
                    sb.append(", ");
                }
            }

            if (Coalesce.asString(sb.toString()).length() > 0) {
                addModal(target, dlgConfirmacaoConflitoCopiaSimNao = new DlgConfirmacaoSimNao(newModalId(),
                    Bundle.getStringApplication("msg_conflito_horario_outras_agendas_data_X_adicionar_sim_nao", sb.substring(0, sb.toString().length() - 2))) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        for (final AgendaGradeAtendimento item : agendasCopiadas) {
                            validarFeriadosCopia(target, item, null, dto, agendaGradeAtendimentoHorariosDTO.getAgendaGradeExameList());
                        }
                    }
                });
                dlgConfirmacaoConflitoCopiaSimNao.show(target);
            } else {
                for (final AgendaGradeAtendimento item : agendasCopiadas) {
                    validarFeriadosCopia(target, item, null, dto, agendaGradeAtendimentoHorariosDTO.getAgendaGradeExameList());
                }
            }
        }
    }

    private void copiarAgendaHorario(AjaxRequestTarget target, final CopiaAgendaDTO dto) throws ValidacaoException, DAOException {
        final List<AgendaGradeAtendimentoHorariosDTO> agendasCopiadas = cadastroAgendaBehavior.copiarAgendaHorario(agendaGradeAtendimentoHorariosAtuaisDTOList, dto);

        if (CollectionUtils.isNotNullEmpty(agendasCopiadas)) {
            StringBuilder sb = new StringBuilder();
            String mensagemHorarioConflitante;
            for (final AgendaGradeAtendimentoHorariosDTO item : agendasCopiadas) {
                mensagemHorarioConflitante = cadastroAgendaBehavior.validarConflitosAgendasSalvas(item.getAgendaGradeAtendimento());
                if (mensagemHorarioConflitante != null) {
                    sb.append(Data.formatar(item.getAgendaGradeAtendimento().getAgendaGrade().getData()));
                    sb.append(", ");
                }
            }

            if (Coalesce.asString(sb.toString()).length() > 0) {
                addModal(target, dlgConfirmacaoConflitoCopiaSimNao = new DlgConfirmacaoSimNao(newModalId(),
                    Bundle.getStringApplication("msg_conflito_horario_outras_agendas_data_X_adicionar_sim_nao", sb.substring(0, sb.toString().length() - 2))) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        for (final AgendaGradeAtendimentoHorariosDTO item : agendasCopiadas) {
                            validarFeriadosCopia(target, item.getAgendaGradeAtendimento(), item.getAgendaGradeHorarioList(), dto, item.getAgendaGradeExameList());
                        }
                    }
                });
                dlgConfirmacaoConflitoCopiaSimNao.show(target);
            } else {
                for (final AgendaGradeAtendimentoHorariosDTO item : agendasCopiadas) {
                    validarFeriadosCopia(target, item.getAgendaGradeAtendimento(), item.getAgendaGradeHorarioList(), dto, item.getAgendaGradeExameList());
                }
            }
        }
    }

    // Validação de feriados
    private void validarFeriadosCopia(AjaxRequestTarget target, final AgendaGradeAtendimento agendaGradeAtendimento, final List<AgendaGradeHorario> list, CopiaAgendaDTO dto, final List<AgendaGradeExame> agendaGradeExameList) throws ValidacaoException, DAOException {
        if (agenda != null) {
            String mensagemDataFeriado = getValidarDataFeriado(agendaGradeAtendimento.getAgendaGrade().getData());

            if (mensagemDataFeriado != null) {
                addModal(target, dlgConfirmacaoFeriadoSimNao = new DlgConfirmacaoSimNao(newModalId(), mensagemDataFeriado) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        adicionarCopiaAgenda(target, agendaGradeAtendimento, list, agendaGradeExameList, dto);
                    }
                });
                dlgConfirmacaoFeriadoSimNao.show(target);
            } else {
                adicionarCopiaAgenda(target, agendaGradeAtendimento, list, agendaGradeExameList, dto);
            }
        }
    }

    private void adicionarCopiaAgenda(AjaxRequestTarget target, AgendaGradeAtendimento agendaGradeAtendimento, List<AgendaGradeHorario> list, List<AgendaGradeExame> agendaGradeExameList,
                                      CopiaAgendaDTO copiaAgendaDTO) {
        AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO = new AgendaGradeAtendimentoHorariosDTO();
        agendaGradeAtendimentoHorariosDTO.setCopiaBase(copiaAgendaDTO);
        agendaGradeAtendimentoHorariosDTO.setCopia(true);
        agendaGradeAtendimentoHorariosDTO.setAgendaGradeAtendimento(agendaGradeAtendimento);
        if (CollectionUtils.isNotNullEmpty(agendaGradeExameList)) {
            agendaGradeAtendimentoHorariosDTO.setAgendaGradeExameList(agendaGradeExameList);
        }
        if (CollectionUtils.isNotNullEmpty(list)) {
            agendaGradeAtendimentoHorariosDTO.setAgendaGradeHorarioList(list);
        }
        dtosPersistir.add(agendaGradeAtendimentoHorariosDTO);
        agendaGradeAtendimentoHorariosAtuaisDTOList.add(agendaGradeAtendimentoHorariosDTO);
        ordenarLista();
        tblAgendaGradeAtendimento.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return agendaGradeAtendimentoHorariosAtuaisDTOList;
            }
        };
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        AgendaGradeAtendimento agendaGradeAtendimento = form.getModel().getObject();

        if (agendaGradeAtendimento.getQuantidadeAtendimentoOriginal() == null) {
            agendaGradeAtendimento.setQuantidadeAtendimentoOriginal(agendaGradeAtendimento.getQuantidadeAtendimento());
        }

        try {
            if (form.getModelObject().getAgendaGrade() != null && form.getModelObject().getAgendaGrade().getData() != null) {
                if (Data.getMesDiferenca(DataUtil.getDataAtual(), form.getModelObject().getAgendaGrade().getData()) > 11) {
                    throw new ValidacaoException(Bundle.getStringApplication("msg_periodo_nao_deve_exceder_doze_meses"));
                }

                if (getContratoPrestador() != null && getContratoPrestador().getDataValidade() != null && form.getModelObject().getAgendaGrade().getData().after(getContratoPrestador().getDataValidade())) {
                    throw new ValidacaoException(new ValidacaoProcesso(BundleManager.getString("dataMaiorDataValidade", Data.formatar(getContratoPrestador().getDataValidade()))));
                }
            }
        } catch (ParseException e) {
            Loggable.log.error(e.getMessage());
        }

        if (agendaGradeAtendimento.getTempoMedio() != null && agendaGradeAtendimento.getTempoMedio() <= 0) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_tempo_medio_maior_zero_agenda"));
        }

        validarFeriados(agendaGradeAtendimento, target);
    }

    // Validação de feriados
    private void validarFeriados(final AgendaGradeAtendimento agendaGradeAtendimento, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (agenda != null && agendaGradeAtendimento.getAgendaGrade() != null && agendaGradeAtendimento.getAgendaGrade().getData() != null) {
            String mensagemDataFeriado = getValidarDataFeriado(agendaGradeAtendimento.getAgendaGrade().getData());
            if (mensagemDataFeriado != null) {
                addModal(target, dlgConfirmacaoFeriadoSimNao = new DlgConfirmacaoSimNao(newModalId(), mensagemDataFeriado) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        continuarValidacoes(agendaGradeAtendimento, target);
                    }
                });
                dlgConfirmacaoFeriadoSimNao.show(target);
            } else {
                continuarValidacoes(agendaGradeAtendimento, target);
            }
        } else {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_agenda"));
        }
    }

    private void continuarValidacoes(final AgendaGradeAtendimento agendaGradeAtendimento, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        cadastroAgendaBehavior.validarAdicionar(agendaGradeAtendimento);
        if (!isTipoAgendaPersonalizada()) {
            agendaGradeAtendimento.getAgendaGrade().setHoraFinal(cadastroAgendaBehavior.getHoraFinal(agendaGradeAtendimento));
        }
        cadastroAgendaBehavior.validarConflitosAgendasNaoSalvas(agendaGradeAtendimentoHorariosAtuaisDTOList, agendaGradeAtendimento);
        validarConflitos(agendaGradeAtendimento, target);
    }

    private void ordenarLista() {
        Collections.sort(agendaGradeAtendimentoHorariosAtuaisDTOList, new Comparator<AgendaGradeAtendimentoHorariosDTO>() {
            @Override
            public int compare(AgendaGradeAtendimentoHorariosDTO o1, AgendaGradeAtendimentoHorariosDTO o2) {
                if (o1.getAgendaGradeAtendimento().getAgendaGrade().getData().before(o2.getAgendaGradeAtendimento().getAgendaGrade().getData())) {
                    return -1;
                } else if (o1.getAgendaGradeAtendimento().getAgendaGrade().getData().equals(o2.getAgendaGradeAtendimento().getAgendaGrade().getData())) {
                    if (o1.getAgendaGradeAtendimento().getAgendaGrade().getHoraInicial().before(o2.getAgendaGradeAtendimento().getAgendaGrade().getHoraInicial())) {
                        return -1;
                    } else {
                        return 1;
                    }
                } else {
                    return 1;
                }
            }
        });
    }

    private void limparComponents(AjaxRequestTarget target) {
        form.getModel().setObject(new AgendaGradeAtendimento());
        txtHoraInicial.limpar(target);
        txtHoraFinal.limpar(target);
        dateChooserData.limpar(target);
        if (isTipoAgendaPersonalizada()) {
            form.getModel().getObject().setQuantidadeAtendimento(1L);
        } else {
            txtQuantidadeAtendimento.limpar(target);
        }
        txtTempoMedio.limpar(target);
        autoCompleteConsultaTipoAtendimentoAgenda.limpar(target);
        descricaoDia = "";
        target.add(lblDescricaoDia);
        target.add(txtHoraInicial);
        target.add(txtHoraFinal);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroAgenda");
    }

    private void removerItem(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO horarioParaRemover) {
        filtrarAgendaGradeAtendimentoDuplicado(horarioParaRemover);
        agendaGradeAtendimentoHorariosAtuaisDTOList.remove(horarioParaRemover);
        addAgendaGradeDeletar(horarioParaRemover.getAgendaGradeAtendimento().getAgendaGrade());
        tblAgendaGradeAtendimento.update(target);
    }

    public void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        CadastroAgendaGradeAtendimentoHorarioDTO cadastroAgendaGradeAtendimentoHorario = new CadastroAgendaGradeAtendimentoHorarioDTO();
        cadastroAgendaGradeAtendimentoHorario.setAgenda(agenda);
        cadastroAgendaGradeAtendimentoHorario.setAnexoDTO(anexoDTO);
        cadastroAgendaGradeAtendimentoHorario.setPossuiAnexo(possuiAnexo);
        cadastroAgendaGradeAtendimentoHorario.setAgendasDiariasAlteradasOcorrencias(agendasDiariasAlteradasOcorrencias);
        AgendaGradeAtendimentoHorariosDTO proxy = on(AgendaGradeAtendimentoHorariosDTO.class);
        List<List<AgendaGradeAtendimentoHorariosDTO>> grupos = CollectionUtils.groupList(dtosPersistir, path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData()), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getHoraInicial()));
        List<AgendaGradeDTO> agendaGradeDTOList = new ArrayList<AgendaGradeDTO>();
        AgendaGradeDTO agendaGradeDTO;
        for (List<AgendaGradeAtendimentoHorariosDTO> list : grupos) {
            agendaGradeDTO = new AgendaGradeDTO();
            AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoHorariosDTO = list.get(0);
            AgendaGrade agendaGrade = agendaGradeAtendimentoHorariosDTO.getAgendaGradeAtendimento().getAgendaGrade();
            agendaGradeDTO.setAgendaGrade(agendaGrade);
            agendaGradeDTO.setCopia(agendaGradeAtendimentoHorariosDTO.isCopia());
            agendaGradeDTO.setCopiaBase(agendaGradeAtendimentoHorariosDTO.getCopiaBase());
            if (!isTipoAgendaPersonalizada()) {
                agendaGrade.setHoraFinal(cadastroAgendaBehavior.getHoraFinal(list));
            }
            agendaGradeDTO.setAgendaGradeAtendimentoHorariosDTOList(list);
            agendaGradeDTO.setAgendaGradeExameList(agendaGradeAtendimentoHorariosDTO.getAgendaGradeExameList());
            agendaGradeDTOList.add(agendaGradeDTO);
        }
        cadastroAgendaGradeAtendimentoHorario.setAgendaGradeDTOList(agendaGradeDTOList);
        if (CollectionUtils.isNotNullEmpty(agendaGradeDeletarList)) {
            cadastroAgendaGradeAtendimentoHorario.setAgendaGradeDeletarList(agendaGradeDeletarList);
        }
        if (CollectionUtils.isNotNullEmpty(agendaGradeDTOList) || CollectionUtils.isNotNullEmpty(agendaGradeDeletarList) || isEditando) {
            BOFactoryWicket.getBO(AgendamentoFacade.class).salvarAgenda(cadastroAgendaGradeAtendimentoHorario, isEditando);
        }
        atualizaAgendasCidadao();
        ConsultaAgendaPage page = new ConsultaAgendaPage();
        getSession().getFeedbackMessages().info(page, BundleManager.getString("registro_salvo_sucesso"));
        setResponsePage(page);
    }

    private void atualizaAgendasCidadao(){
        try{
            ScheduleUpdateDTO scheduleUpdateDTO = new ScheduleUpdateDTO();
            scheduleUpdateDTO.setProcedureId(idTipoProcedimento);
            scheduleUpdateDTO.setCityId(SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa().getCidade().getCodigo());
            scheduleUpdateDTO.setCareUnitId(agenda.getEmpresa().getCodigo());
            try{
                BOFactoryWicket.getBO(AppCidadaoFacade.class).updateSchedules(scheduleUpdateDTO);
            }catch(DAOException daoException){
                Loggable.log.debug("Não foi possível realizar a invalidação de cache / atualização das agendas do appcidadão. "+daoException.getMessage());
            }catch(ValidacaoException validacaoException){
                Loggable.log.debug("Não foi possível realizar a invalidação de cache / atualização das agendas do appcidadão. "+validacaoException.getMessage());
            }
        }catch(Exception exception){ //Este processo de invalidação de cache não pode comprometer a funcionalidade de alteração das agendas
            Loggable.log.debug("Não foi possível realizar a invalidação de cache / atualização das agendas do appcidadão. "+exception.getMessage());
        }
    }

    private void carregaListaAtendimentos(int dias) {
        agendaGradeAtendimentoHorariosAtuaisDTOList = cadastroAgendaBehavior.carregaListaAtendimentos(agendaGradeAtendimentoHorariosAtuaisDTOList, Data.addDias(DataUtil.getDataAtual(), -dias), dias == 0);
    }

    // Validação de conflitos de horários já registrados no banco de dados
    private void validarConflitos(final AgendaGradeAtendimento agendaGradeAtendimento, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (agenda != null) {
            String mensagemHorarioConflitante = cadastroAgendaBehavior.validarConflitosAgendasSalvas(agendaGradeAtendimento);

            if (mensagemHorarioConflitante != null) {
                addModal(target, dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(newModalId(), mensagemHorarioConflitante) {
                    @Override
                    public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        adicionarItem(agendaGradeAtendimento, target);
                    }
                });
                dlgConfirmacaoSimNao.show(target);
            } else {
                adicionarItem(agendaGradeAtendimento, target);
            }
        }
    }

    private void adicionarItem(AgendaGradeAtendimento agendaGradeAtendimento, AjaxRequestTarget target) throws ValidacaoException {
        AgendaGradeAtendimentoHorariosDTO dto = cadastroAgendaBehavior.gerarAgendaGradeAtendimento(agendaGradeAtendimento);
        dtosPersistir.add(dto);
        agendaGradeAtendimentoHorariosAtuaisDTOList.add(dto);
        dto.setAgendaGradeHorarioToSaveList(dto.getAgendaGradeHorarioList());
        ordenarLista();
        tblAgendaGradeAtendimento.update(target);
        limparComponents(target);
        target.focusComponent(dateChooserData.getData());
    }

    private AbstractAjaxButton criarBotaoAtualizarGrade(boolean habilitar) {
        AbstractAjaxButton botaoAtualizarGrade = new AbstractAjaxButton("btnAtualizarGrade") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException {
                validarLimiteGradesAnteriores((int) quantidadeDiasField.getComponentValue());
                atualizarGradeAction(target);
                checkVisualizarGradesAnteriores.limpar(target);
                quantidadeDiasField.setEnabled(RepositoryComponentDefault.SIM.equals(checkVisualizarGradesAnteriores.getComponentValue()));
            }
        };
        botaoAtualizarGrade.setEnabled(habilitar);
        return botaoAtualizarGrade;
    }

    private void atualizarGradeAction(AjaxRequestTarget target) {
        int dias = 0;
        if (RepositoryComponentDefault.SIM.equals(checkVisualizarGradesAnteriores.getComponentValue())) {
            dias = (int) quantidadeDiasField.getComponentValue();
        }
        carregaListaAtendimentos(dias);
        ordenarLista();
        tblAgendaGradeAtendimento.update(target);
        target.add(tblAgendaGradeAtendimento);
        btnAtualizarGrade.setEnabled(false);
        target.add(btnAtualizarGrade);
        quantidadeDiasField.setComponentValue(0);
        target.add(quantidadeDiasField);
    }

    private String getValidarDataFeriado(Date data) throws ValidacaoException {
        return cadastroAgendaBehavior.validarDataFeriado(data, agenda.getEmpresa());
    }

    public void addAgendaGradeDeletar(AgendaGrade agendaGrade){
        if(agendaGrade.getCodigo() != null){
            agendaGradeDeletarList.add(agendaGrade);
        }
    }

    private void filtrarAgendaGradeAtendimentoDuplicado(AgendaGradeAtendimentoHorariosDTO agendaGradeAtendimentoDTO){
        List<AgendaGradeAtendimentoHorariosDTO> agendaGradeAtendimentos = new ArrayList<>();
        for(AgendaGradeAtendimentoHorariosDTO agendaGrade : dtosPersistir){
            if(!agendaGrade.equals(agendaGradeAtendimentoDTO)){
                agendaGradeAtendimentos.add(agendaGrade);
            }
        }
        dtosPersistir = agendaGradeAtendimentos;
    }
    public static void validarLimiteGradesAnteriores(int dias) throws ValidacaoException {
        if (dias > 60) {
            throw new ValidacaoException(Bundle.getStringApplication("limiteDiasGradesAnteriores"));
        }
    }

    private boolean dataMaiorOuIgualAtual(Date data){
        Date dataAtual = DataUtil.getDataAtualSemHora();
        return !data.before(dataAtual);
    }
}
