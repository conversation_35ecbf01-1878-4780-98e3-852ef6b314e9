package br.com.celk.view.vigilancia.externo.view.components.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RespostaParecerExternoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RespostaParecerViewDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * <AUTHOR>
 */
public abstract class DlgEscolherParecerResposta extends Window {

    private PnlEscolherParecerResposta pnlEscolherParecerResposta;

    public DlgEscolherParecerResposta(String id, Class clazz, RespostaParecerExternoDTO dto) {
        super(id);
        init(clazz, dto);
    }

    private void init(Class clazz, RespostaParecerExternoDTO dto) {
        setOutputMarkupId(true);

        setInitialWidth(450);
        setInitialHeight(150);
        setTitle(BundleManager.getString("pareceresCadastrados"));

        setResizable(false);

        setContent(pnlEscolherParecerResposta = new PnlEscolherParecerResposta(getContentId(), dto) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                limpar(target);
                close(target);
            }

            @Override
            public void onAction(AjaxRequestTarget target, RespostaParecerViewDTO dto) throws ValidacaoException, DAOException {
                DlgEscolherParecerResposta.this.onAction(target, dto);
            }
        });
        pnlEscolherParecerResposta.setOutputMarkupId(true);
    }
    
    public void show(AjaxRequestTarget target){
        super.show(target);
    }

    public void update(AjaxRequestTarget target) {
        pnlEscolherParecerResposta.update(target);
    }

    public abstract void onAction(AjaxRequestTarget target, RespostaParecerViewDTO dto) throws ValidacaoException, DAOException;

}
