package br.com.celk.view.hospital.faturamento.tiss.dialogs.honorarios;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPacienteTissDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgNovoLancamentoHonorariosTiss extends Window {

    private PnlNovoLancamentoHonorariosTiss pnlNovoLancamento;

    public DlgNovoLancamentoHonorariosTiss(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(465);
        setInitialWidth(800);

        setResizable(true);

        setTitle(new LoadableDetachableModel<String>() {
            @Override
            protected String load() {
                return BundleManager.getString("novoLancamento");
            }
        });

        setContent(pnlNovoLancamento = new PnlNovoLancamentoHonorariosTiss(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, ItemContaPacienteTissDTO dto) throws DAOException, ValidacaoException {
                DlgNovoLancamentoHonorariosTiss.this.onConfirmar(target, dto);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                pnlNovoLancamento.limpar(target);
                close(target);
            }
        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                pnlNovoLancamento.limpar(target);
                return true;
            }
        });
    }

    public void show(AjaxRequestTarget target, ItemContaPacienteTissDTO dto) {
        show(target);
        pnlNovoLancamento.setItemContaPaciente(target, dto);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ItemContaPacienteTissDTO dto) throws DAOException, ValidacaoException;
}
