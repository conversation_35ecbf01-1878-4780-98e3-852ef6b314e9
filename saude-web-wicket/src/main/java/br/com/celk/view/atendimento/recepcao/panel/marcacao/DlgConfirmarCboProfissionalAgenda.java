package br.com.celk.view.atendimento.recepcao.panel.marcacao;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.agendamento.dto.DadosAgendamentoDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgConfirmarCboProfissionalAgenda extends Window {

    private PnlConfirmarCboProfissionalAgenda pnlConfirmarCboProfissionalAgenda;
    private ConsultaUsuarioCadsusDTO consultaUsuarioCadsusDTO;
    private boolean isPermiteEncaixarPaciente;
    private boolean permiteAgendarVagaInterna;

    public DlgConfirmarCboProfissionalAgenda(String id, ConsultaUsuarioCadsusDTO consultaUsuarioCadsusDTO, boolean isPermiteEncaixarPaciente, boolean permiteAgendarVagaInterna) {
        super(id);
        this.consultaUsuarioCadsusDTO = consultaUsuarioCadsusDTO;
        this.isPermiteEncaixarPaciente = isPermiteEncaixarPaciente;
        this.permiteAgendarVagaInterna = permiteAgendarVagaInterna;
        init();
    }


    private void init() {
        setTitle(BundleManager.getString("marcacaoConsulta"));

        setInitialWidth(800);
        setInitialHeight(370);

        setResizable(false);

        setContent(pnlConfirmarCboProfissionalAgenda = new PnlConfirmarCboProfissionalAgenda(getContentId(), consultaUsuarioCadsusDTO, isPermiteEncaixarPaciente, permiteAgendarVagaInterna) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                fechar(target);
            }

            @Override
            public void agendamentoDiario(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO) throws DAOException {
                close(target);
                DlgConfirmarCboProfissionalAgenda.this.agendamentoDiario(target, dto, dadosAgendamentoDTO);
            }

            @Override
            public void agendamentoHorario(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO) throws DAOException {
                close(target);
                DlgConfirmarCboProfissionalAgenda.this.agendamentoHorario(target, dto, dadosAgendamentoDTO);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                fechar(target);
                return true;
            }
        });
    }


    abstract void agendamentoDiario(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO) throws DAOException;
    abstract void agendamentoHorario(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto, DadosAgendamentoDTO dadosAgendamentoDTO) throws DAOException;

    public void onFechar(AjaxRequestTarget target) {
    }

    public void show(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) {
        pnlConfirmarCboProfissionalAgenda.limpar(target);
        show(target);
    }

    private void fechar(AjaxRequestTarget target) {
        DlgConfirmarCboProfissionalAgenda.this.onFechar(target);
        close(target);
    }
}
