package br.com.celk.view.unidadesaude.esus.planejamentovisitadomicilio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.esus.interfaces.facade.EsusFacade;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.esus.interfaces.dto.PlanejamentoVisitaDTO;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.*;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.core.javascript.helper.DateHelper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class CadastroPlanejamentoVisitaDomicilioPage extends BasePage {

    private MultiSelectionTableOld<EnderecoDomicilio> tblEnderecoDomicilio;
    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private EquipeMicroArea equipeMicroAreaInativa;
    private DateChooser dcVisitaInicio;
    private DateChooser dcVisitaFim;
    private InputArea txaObservacao;
    private PlanejamentoVisitaDTO planejamentoVisitaDTO;
    private final boolean isActionPermittedProfissional;
    private CheckBoxLongValue checkBoxPlanejamentoForaMicroarea;
    private WebMarkupContainer containerPlanejamentoForaMicroarea;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    public CadastroPlanejamentoVisitaDomicilioPage(boolean isActionPermittedProfissional) {
        planejamentoVisitaDTO = new PlanejamentoVisitaDTO();
        planejamentoVisitaDTO.setPlanejamentoVisita(new PlanejamentoVisita());
        this.isActionPermittedProfissional = isActionPermittedProfissional;
    }

    public CadastroPlanejamentoVisitaDomicilioPage(PlanejamentoVisita planejamentoVisita, boolean isActionPermittedProfissional) {
        planejamentoVisitaDTO = new PlanejamentoVisitaDTO();
        planejamentoVisitaDTO.setPlanejamentoVisita(planejamentoVisita);
        this.isActionPermittedProfissional = isActionPermittedProfissional;
    }

    @Override
    protected void postConstruct() {
        Form<PlanejamentoVisitaDTO> form = new Form("form", new CompoundPropertyModel(planejamentoVisitaDTO == null ? (planejamentoVisitaDTO = new PlanejamentoVisitaDTO()) : planejamentoVisitaDTO));
        PlanejamentoVisitaDTO proxy = on(PlanejamentoVisitaDTO.class);

        form.add(dropDownArea = getDropDownArea(path(proxy.getPlanejamentoVisita().getEquipeMicroArea().getEquipeArea())));
        form.add(dropDownMicroArea = getDropDownMicroArea(path(proxy.getPlanejamentoVisita().getEquipeMicroArea())));
        form.add(dcVisitaInicio = (DateChooser) new DateChooser(path(proxy.getPlanejamentoVisita().getDataVisitaInicio()))
                .setLabel(new Model<String>(bundle("dataVisitaInicial"))));
        form.add(dcVisitaFim = (DateChooser) new DateChooser(path(proxy.getPlanejamentoVisita().getDataVisitaFim()))
                .setLabel(new Model<String>(bundle("dataVisitaFinal"))));
        form.add(txaObservacao = new InputArea(path(proxy.getPlanejamentoVisita().getObservacao())));

        form.add(containerPlanejamentoForaMicroarea = new WebMarkupContainer("containerPlanejamentoForaMicroarea"));
        containerPlanejamentoForaMicroarea.setVisible(isActionPermittedProfissional);
        containerPlanejamentoForaMicroarea.setOutputMarkupId(true);
        containerPlanejamentoForaMicroarea.setOutputMarkupPlaceholderTag(true);
        containerPlanejamentoForaMicroarea.add(checkBoxPlanejamentoForaMicroarea = new CheckBoxLongValue(path(proxy.getPlanejamentoVisita().getPlanejamentoForaMicroarea())) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                onUpdateForaMicroArea(target);
            }
        });

        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional(path(proxy.getPlanejamentoVisita().getProfissional())));
        autoCompleteConsultaProfissional.setEnabled(false);
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional profissional) {

            }
        });

        dcVisitaInicio.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                dcVisitaFim.setMinDateAjax(art, dcVisitaInicio.getData().getModelObject());
            }
        });

        dcVisitaFim.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget art) {
                dcVisitaInicio.setMaxDateAjax(art, dcVisitaFim.getData().getModelObject());
            }
        });

        form.add(tblEnderecoDomicilio = new MultiSelectionTableOld("tbmEnderecoDomicilio", getColumns(), getCollectionProvider()));
        tblEnderecoDomicilio.setScrollY("400");

        EquipeMicroArea equipeMicroArea = dropDownMicroArea.getComponentValue();
        if (equipeMicroArea != null) {
            tblEnderecoDomicilio.populate();
        }

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar();
            }

        }));

        add(form);

        if (planejamentoVisitaDTO.getPlanejamentoVisita() != null && planejamentoVisitaDTO.getPlanejamentoVisita().getCodigo() != null) {
            tblEnderecoDomicilio.setSelectedObject(carregarEnderecosSelecionados());
        }

        form.getModel().setObject(planejamentoVisitaDTO);
    }

    private void onUpdateForaMicroArea(AjaxRequestTarget target) {
        if (RepositoryComponentDefault.SIM_LONG.equals(checkBoxPlanejamentoForaMicroarea.getComponentValue())) {
            dropDownMicroArea.setEnabled(true);
            if (isActionPermittedProfissional || SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster()) {
                autoCompleteConsultaProfissional.setEnabled(true);
            }
        } else {
            dropDownMicroArea.setEnabled(false);
            autoCompleteConsultaProfissional.setEnabled(false);
            setProfissional(target, dropDownMicroArea.getComponentValue());
        }

        if (target != null) {
            target.add(dropDownMicroArea);
            target.add(autoCompleteConsultaProfissional);
        }
    }

    private void salvar() throws ValidacaoException, DAOException {
        if (planejamentoVisitaDTO.getPlanejamentoVisita().getEquipeMicroArea() == null || planejamentoVisitaDTO.getPlanejamentoVisita().getEquipeMicroArea().getCodigo() == null) {
            throw new ValidacaoException(bundle("selecioneAreaMicroArea"));
        }
        List<EnderecoDomicilio> lstEnderecoDomicilios = tblEnderecoDomicilio.getSelectedObjects();
        if (lstEnderecoDomicilios == null || lstEnderecoDomicilios.isEmpty()) {
            throw new ValidacaoException(bundle("selecionePeloMenosUmEndereco"));
        }
        if (planejamentoVisitaDTO.getPlanejamentoVisita().getDataVisitaInicio() == null || planejamentoVisitaDTO.getPlanejamentoVisita().getDataVisitaFim() == null) {
            throw new ValidacaoException(bundle("selecioneDataVisita"));
        }
        if (planejamentoVisitaDTO.getPlanejamentoVisita().getDataVisitaInicio().after(planejamentoVisitaDTO.getPlanejamentoVisita().getDataVisitaFim())) {
            throw new ValidacaoException(bundle("msgDataInicialVisitaMaiorDataFinalVisita"));
        }

        if (planejamentoVisitaDTO.getPlanejamentoVisita().getProfissional() == null) {
            throw new ValidacaoException(bundle("selecioneProfissional"));
        } else {
            EquipeProfissional equipeProfissional = planejamentoVisitaDTO.getPlanejamentoVisita().getEquipeMicroArea().getEquipeProfissional();

            if (equipeProfissional == null || !equipeProfissional.getProfissional().equals(planejamentoVisitaDTO.getPlanejamentoVisita().getProfissional())) {
                EquipeMicroArea equipeMicroArea = getEquipeMicroArea(planejamentoVisitaDTO.getPlanejamentoVisita().getProfissional());

                if (equipeMicroArea == null) {
                    throw new ValidacaoException(bundle("profissionalNaoVinculadoEquipe"));
                }
                equipeProfissional= equipeMicroArea.getEquipeProfissional();
            }
            planejamentoVisitaDTO.getPlanejamentoVisita().setEmpresa(equipeProfissional.getEquipe().getEmpresa());
        }


        planejamentoVisitaDTO.getPlanejamentoVisita().setStatus(PlanejamentoVisita.Status.PENDENTE.value());
        if (planejamentoVisitaDTO.getPlanejamentoVisita().getDataCadastro() == null) {
            planejamentoVisitaDTO.getPlanejamentoVisita().setDataCadastro(DataUtil.getDataAtual());
        }
        BOFactoryWicket.getBO(EsusFacade.class).salvarPlanejamentoVisita(planejamentoVisitaDTO, lstEnderecoDomicilios);

        Page page = new ConsultaPlanejamentoVisitaDomicilioPage();
        getSession().getFeedbackMessages().info(page, bundle("registro_salvo_sucesso"));
        setResponsePage(page);
    }

    private List<EnderecoDomicilio> carregarEnderecosSelecionados() {
        List<PlanejamentoVisitaDomicilio> lstPlanejamentoVisitaDomicilios = LoadManager.getInstance(PlanejamentoVisitaDomicilio.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PlanejamentoVisitaDomicilio.PROP_PLANEJAMENTO_VISITA, PlanejamentoVisita.PROP_CODIGO), planejamentoVisitaDTO.getPlanejamentoVisita().getCodigo()))
                .start().getList();

        List<EnderecoDomicilio> lstEnderecoDomicilios = new ArrayList<EnderecoDomicilio>();
        for (PlanejamentoVisitaDomicilio planejamentoVisitaDomicilio : lstPlanejamentoVisitaDomicilios) {
            lstEnderecoDomicilios.add(planejamentoVisitaDomicilio.getEnderecoDomicilio());
        }

        return lstEnderecoDomicilios;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();
        EnderecoDomicilio proxy = on(EnderecoDomicilio.class);

        columns.add(createSortableColumn(bundle("familia"), proxy.getNumeroFamilia()));
        columns.add(createSortableColumn(bundle("rua"), proxy.getEnderecoUsuarioCadsus().getLogradouro(), proxy.getEnderecoUsuarioCadsus().getRuaFormatadaSemNumero()));
        columns.add(createSortableColumn(bundle("numero"), proxy.getEnderecoUsuarioCadsus().getNumeroLogradouro()));
        columns.add(createSortableColumn(bundle("complemento"), proxy.getEnderecoUsuarioCadsus().getComplementoLogradouro()));
        columns.add(createSortableColumn(bundle("bairro"), proxy.getEnderecoUsuarioCadsus().getBairro()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return consultarLista(getSort());
            }

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_DESCRICAO), true);
            }

        };
    }

    private List<EnderecoDomicilio> consultarLista(SortParam sort) {
        EquipeMicroArea equipeMicroArea = dropDownMicroArea.getComponentValue();
        if (equipeMicroArea != null) {
            return LoadManager.getInstance(EnderecoDomicilio.class)
                    .addProperties(new HQLProperties(EnderecoDomicilio.class).getProperties())
                    .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_CODIGO), equipeMicroArea.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EnderecoDomicilio.PROP_EXCLUIDO), RepositoryComponentDefault.NAO_EXCLUIDO))
                    .addSorter(new QueryCustom.QueryCustomSorter((String) sort.getProperty(), sort.isAscending() ? BuilderQueryCustom.QuerySorter.CRESCENTE : BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_TIPO_LOGRADOURO, TipoLogradouroCadsus.PROP_DESCRICAO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_LOGRADOURO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(EnderecoDomicilio.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_NUMERO_LOGRADOURO), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();
        } else {
            return new ArrayList<EnderecoDomicilio>();
        }
    }

    private DropDown getDropDownMicroArea(String id) {
        DropDown dropDown = new DropDown(id);
        dropDown.addChoice(null, BundleManager.getString("selecioneArea"));

        dropDown.setEnabled(false);
        EquipeArea equipeArea = dropDownArea.getComponentValue();
        List<EquipeMicroArea> equipeMicroAreas = Collections.EMPTY_LIST;
        if (equipeArea != null) {
            equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                    .addProperty(EquipeMicroArea.PROP_CODIGO)
                    .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                    .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                    .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_CODIGO))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_STATUS, RepositoryComponentDefault.ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                    .start().getList();

            dropDown.removeAllChoices();
            dropDown.addChoice(null, BundleManager.getString("selecione"));

            if (planejamentoVisitaDTO.getPlanejamentoVisita().getEquipeMicroArea() != null && RepositoryComponentDefault.INATIVO.equals(planejamentoVisitaDTO.getPlanejamentoVisita().getEquipeMicroArea().getStatus())) {
                equipeMicroAreaInativa = (EquipeMicroArea) SerializationUtils.clone(planejamentoVisitaDTO.getPlanejamentoVisita().getEquipeMicroArea());
                dropDown.addChoice(equipeMicroAreaInativa, equipeMicroAreaInativa.getDescricaoMicroAreaSituacao());
            }

            for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                dropDown.addChoice(equipeMicroArea, descricao);
            }
        }

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeMicroArea equipeMicroArea = dropDownMicroArea.getComponentValue();
                setProfissional(target, equipeMicroArea);
                if (equipeMicroArea != null) {
                    tblEnderecoDomicilio.populate();
                }
                tblEnderecoDomicilio.update(target);
            }
        });

        return dropDown;
    }

    private DropDown getDropDownArea(String id) {
        DropDown dropDown = new DropDown(id);

        List<EquipeArea> areas = LoadManager.getInstance(EquipeArea.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getCidade()))
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                .start().getList();

        dropDown.addChoice(null, BundleManager.getString("todas"));
        for (EquipeArea equipeArea : areas) {
            dropDown.addChoice(equipeArea, equipeArea.getDescricao());
        }

        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();
        if (profissional != null) {
            EquipeMicroArea equipeMicroArea = getEquipeMicroArea(profissional);
            if (equipeMicroArea != null) {
                if (planejamentoVisitaDTO.getPlanejamentoVisita().getEquipeMicroArea() == null) {
                    planejamentoVisitaDTO.getPlanejamentoVisita().setEquipeMicroArea(equipeMicroArea);
                }
            }

            setProfissional(null, equipeMicroArea);
        }

        if ((isActionPermittedProfissional || SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster()) && planejamentoVisitaDTO.getPlanejamentoVisita().getCodigo() == null) {
            dropDown.setEnabled(true);
        } else {
            dropDown.setEnabled(false);
        }

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                EquipeArea equipeArea = dropDownArea.getComponentValue();
                List<EquipeMicroArea> equipeMicroAreas = Collections.EMPTY_LIST;
                if (equipeArea != null) {
                    equipeMicroAreas = LoadManager.getInstance(EquipeMicroArea.class)
                            .addProperty(EquipeMicroArea.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_MICRO_AREA))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                            .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_CODIGO))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_STATUS, RepositoryComponentDefault.ATIVO))
                            .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_EQUIPE_AREA, equipeArea))
                            .addSorter(new QueryCustom.QueryCustomSorter(EquipeMicroArea.PROP_MICRO_AREA))
                            .start().getList();
                    dropDownMicroArea.setEnabled(true);
                } else {
                    dropDownMicroArea.setEnabled(false);
                }

                dropDownMicroArea.removeAllChoices();
                dropDownMicroArea.limpar(target);
                dropDownMicroArea.addChoice(null, BundleManager.getString("selecione"));

                if (equipeMicroAreaInativa != null && equipeArea.equals(equipeMicroAreaInativa.getEquipeArea())) {
                    dropDownMicroArea.addChoice(equipeMicroAreaInativa, equipeMicroAreaInativa.getDescricaoMicroAreaSituacao());
                }

                for (EquipeMicroArea equipeMicroArea : equipeMicroAreas) {
                    String descricao = equipeMicroArea.getMicroArea().toString() + " - " + (equipeMicroArea.getEquipeProfissional() != null ? equipeMicroArea.getEquipeProfissional().getProfissional().getNome() : BundleManager.getString("semProfissional"));
                    dropDownMicroArea.addChoice(equipeMicroArea, descricao);
                }
                tblEnderecoDomicilio.update(target);

                target.add(dropDownMicroArea);
            }
        });

        return dropDown;
    }

    private EquipeMicroArea getEquipeMicroArea(Profissional profissional) {
        EquipeMicroArea equipeMicroArea = LoadManager.getInstance(EquipeMicroArea.class)
                .addProperties(new HQLProperties(EquipeMicroArea.class).getProperties())
                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                .addProperty(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA, Empresa.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(EquipeMicroArea.PROP_STATUS, RepositoryComponentDefault.ATIVO))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_PROFISSIONAL, Profissional.PROP_CODIGO), profissional.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_STATUS), EquipeProfissional.STATUS_ATIVO))
                .start().getVO();
        return equipeMicroArea;
    }

    private void setProfissional(AjaxRequestTarget target, EquipeMicroArea equipeMicroArea) {
        Profissional profissional = SessaoAplicacaoImp.getInstance().getUsuario().getProfissional();

        if (SessaoAplicacaoImp.getInstance().getUsuario().isNivelAdminOrMaster()) {
            if (target != null) {
                autoCompleteConsultaProfissional.limpar(target);
            }

            if (equipeMicroArea != null && equipeMicroArea.getEquipeProfissional() != null) {
                planejamentoVisitaDTO.getPlanejamentoVisita().setProfissional(equipeMicroArea.getEquipeProfissional().getProfissional());
            }
        } else {
            planejamentoVisitaDTO.getPlanejamentoVisita().setProfissional(profissional);
        }

        if (target != null) {
            target.add(autoCompleteConsultaProfissional);
        }
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (dcVisitaFim.getModelObject() != null) {
            response.render(OnDomReadyHeaderItem.forScript("$( '#" + dcVisitaInicio.getMarkupId() + "' ).datepicker( 'option', 'maxDate', " + DateHelper.getJSDate(dcVisitaFim.getModelObject()) + " );"));
        }
        if (dcVisitaInicio.getModelObject() != null) {
            response.render(OnDomReadyHeaderItem.forScript("$( '#" + dcVisitaFim.getMarkupId() + "' ).datepicker( 'option', 'minDate', " + DateHelper.getJSDate(dcVisitaInicio.getModelObject()) + " );"));
        }
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroPlanejamentoVisita");
    }
}
