package br.com.celk.view.unidadesaude.diagnosticoenfermagem;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.DiagnosticoEnfermagem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroDiagnosticoEnfermagemPage extends CadastroPage<DiagnosticoEnfermagem> {

    private RequiredInputField<String> txtDescricao;

    public CadastroDiagnosticoEnfermagemPage(DiagnosticoEnfermagem object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
        
    }
    
    public CadastroDiagnosticoEnfermagemPage(DiagnosticoEnfermagem object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroDiagnosticoEnfermagemPage(DiagnosticoEnfermagem object) {
        this(object, false);
    }

    public CadastroDiagnosticoEnfermagemPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        DiagnosticoEnfermagem proxy = on(DiagnosticoEnfermagem.class);
        form.add(txtDescricao = new RequiredInputField<String>(path(proxy.getDescricao())));
        form.add(new RequiredInputField<String>(path(proxy.getReferencia())));
        form.add(new InputArea(path(proxy.getDefinicao())));
        form.add(new InputArea(path(proxy.getPlano())));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtDescricao;
    }

    @Override
    public Class<DiagnosticoEnfermagem> getReferenceClass() {
        return DiagnosticoEnfermagem.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaDiagnosticoEnfermagemPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroDiagnosticosEnfermagem");
    }

    @Override
    public void antesSalvar(AjaxRequestTarget target, DiagnosticoEnfermagem object) throws DAOException, ValidacaoException {
        boolean existeDiagnostico = LoadManager.getInstance(DiagnosticoEnfermagem.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DiagnosticoEnfermagem.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(DiagnosticoEnfermagem.PROP_REFERENCIA, object.getReferencia()))
                .addParameter(new QueryCustom.QueryCustomParameter(DiagnosticoEnfermagem.PROP_DESCRICAO, object.getDescricao()))
                .exists();
        if (existeDiagnostico){
            throw new ValidacaoException(bundle("msgDiagnosticoComMesmaReferenciaDescricao"));
        }
    }
}
