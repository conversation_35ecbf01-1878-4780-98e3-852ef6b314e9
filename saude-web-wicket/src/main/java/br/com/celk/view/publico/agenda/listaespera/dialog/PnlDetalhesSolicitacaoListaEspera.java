package br.com.celk.view.publico.agenda.listaespera.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.atendimento.recepcao.panel.marcacao.dialog.DlgRecomendacoesAgenda;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTO;
import br.com.ksisolucoes.agendamento.exame.dto.SolicitacaoAgendamentoExameViewDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamentoOcorrencia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.service.sms.SmsControleIntegracao;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public abstract class PnlDetalhesSolicitacaoListaEspera extends Panel {

    private Form<SolicitacaoAgendamento> form;
    private SolicitacaoAgendamento solicitacaoAgendamento;
    private Table table;
    private Table tableAgendamento;
    private Table tableSolicitacoes;
    private Table tableSms;
    private ICollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento> collectionProvider;
    private ICollectionProvider<SolicitacaoAgendamento, SolicitacaoAgendamento> collectionProviderSolicitacao;
    private ICollectionProvider<SmsControleIntegracao, SolicitacaoAgendamento> collectionProviderSms;
    private ICollectionProvider<AgendaGradeAtendimentoHorario, SolicitacaoAgendamento> collectionProviderAgendamento;
    private WebMarkupContainer containerDescricao;
    boolean possuiDescricao = false;
    private String descricao;
    private DlgRecomendacoesAgenda dlgRecomendacoesAgenda;
    private WebMarkupContainer containerTableAgendamentos;
    private WebMarkupContainer containerTableSms;
    private WebMarkupContainer containerTableOcorrencias;

    private WebMarkupContainer containerExamesSolicitados;
    private WebMarkupContainer containerObservacao;
    private WebMarkupContainer containerObservacaoUrgente;
    private WebMarkupContainer containerRegulacao;
    private Table<SolicitacaoAgendamentoExameViewDTO> tblExamesSolicitados;
    private List<SolicitacaoAgendamentoExame> lstSolicitacaoAgendamentoExame;
    private List<SmsControleIntegracao> smsControleIntegracaoList;
    private List<AgendaGradeAtendimentoHorario> agendaGradeAtendimentoHorarioList;
    private List<SolicitacaoAgendamentoOcorrencia> solicitacaoAgendamentoOcorrenciaList;
    private WebMarkupContainer containerJustificativaPrioridade;

    private WebMarkupContainer containerSolicitarPrioridadeMelhorDiaHora;
    private CheckBoxLongValue checkDom;
    private CheckBoxLongValue checkSeg;
    private CheckBoxLongValue checkTer;
    private CheckBoxLongValue checkQua;
    private CheckBoxLongValue checkQui;
    private CheckBoxLongValue checkSex;
    private CheckBoxLongValue checkSab;
    private DisabledInputField InputMelhorHora;
    private CheckBoxLongValue checkTodos;

    private Long melhorDiaDom;
    private Long melhorDiaSeg;
    private Long melhorDiaTer;
    private Long melhorDiaQua;
    private Long melhorDiaQui;
    private Long melhorDiaSex;
    private Long melhorDiaSab;
    private Long todos;

    private WebMarkupContainer containerSolicitacaoProfissional;
    private String tipoControleRegulacao;

    public PnlDetalhesSolicitacaoListaEspera(String id) {
        super(id);
        solicitacaoAgendamento = new SolicitacaoAgendamento();
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        getForm().add(new DisabledInputField("usuarioCadsus.descricaoSocialFormatado"));
        getForm().add(new DisabledInputField("usuarioCadsus.dataNascimento"));
        getForm().add(new DisabledInputField("usuarioCadsus.sexoFormatado"));
        getForm().add(new DisabledInputField("usuarioCadsus.cnsSemPontos"));

        getForm().add(new DisabledInputField("usuarioCadsus.telefoneFormatado"));
        getForm().add(new DisabledInputField("usuarioCadsus.telefone2Formatado"));
        getForm().add(new DisabledInputField("usuarioCadsus.telefone3"));
        getForm().add(new DisabledInputField("usuarioCadsus.telefone4"));
        getForm().add(new DisabledInputField("usuarioCadsus.celularFormatado"));
        getForm().add(new DisabledInputField("usuarioCadsus.email"));

        getForm().add(new DisabledInputField("usuario.nome"));

        getForm().add(new DisabledInputField("codigo"));
        getForm().add(new DisabledInputField("dataCadastro"));
        getForm().add(new DisabledInputField("dataSolicitacao"));
        getForm().add(new DisabledInputField("descricaoSituacao"));
        getForm().add(new DisabledInputField("empresa.descricaoFormatado"));
        getForm().add(new DisabledInputField("nomeProfissionalOrigem"));
        getForm().add(new DisabledInputField("unidadeExecutante.descricaoFormatado"));
        getForm().add(new DisabledInputField("tipoProcedimento.descricao"));
        getForm().add(new DisabledInputField("solicitarPrioridadeFormatadoData"));
        getForm().add(new DisabledInputField("numeracaoAuxiliar"));
        getForm().add(new DisabledInputField("atendimentoOrigem.codigo"));
        getForm().add(new DisabledInputField("descricaoAcamado"));
        getForm().add(new DisabledInputField("cid.descricaoFormatado"));
        getForm().add(new DisabledInputField("nomeProfissionalDesejado"));

        containerObservacao = new WebMarkupContainer("containerObservacao");
        containerObservacao.add(new DisabledInputArea("observacao"));
        if (!Coalesce.asString(solicitacaoAgendamento.getObservacao()).isEmpty() && !RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
            containerObservacao.setVisible(true);
        } else {
            containerObservacao.setVisible(false);
        }

        getForm().add(containerObservacao);

        containerObservacaoUrgente = new WebMarkupContainer("containerObservacaoUrgente");
        containerObservacaoUrgente.add(new DisabledInputArea("observacaoUrgente"));
        if (SolicitacaoAgendamento.PRIORIDADE_URGENTE.equals(solicitacaoAgendamento.getPrioridade()) && !RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao())) {
            containerObservacaoUrgente.setVisible(true);
        } else {
            containerObservacaoUrgente.setVisible(false);
        }

        getForm().add(containerObservacaoUrgente);


        containerJustificativaPrioridade = new WebMarkupContainer("containerJustificativaPrioridade");
        containerJustificativaPrioridade.add(new DisabledInputArea("observacaoUrgente"));
        if (RepositoryComponentDefault.SIM_LONG.equals(solicitacaoAgendamento.getSolicitarPrioridade())) {
            containerJustificativaPrioridade.setVisible(true);
        } else {
            containerJustificativaPrioridade.setVisible(false);
        }

        getForm().add(containerJustificativaPrioridade);

        containerExamesSolicitados = new WebMarkupContainer("containerExamesSolicitados");
        containerRegulacao = new WebMarkupContainer("containerRegulacao");
        containerExamesSolicitados.add(tblExamesSolicitados = new Table("tblExamesSolicitados", getColumnsExamesSolicitados(), getCollectionProviderExamesSolicitados()));
        tblExamesSolicitados.populate();

        getForm().add(containerExamesSolicitados);

        getForm().add(new DisabledInputField("procedimento.descricaoFormatado"));
        containerRegulacao.add(new DisabledInputField("usuarioAutorizador.descricaoFormatado"));
        containerRegulacao.add(new DisabledInputField("dataAutorizador"));
        containerRegulacao.add(new DisabledInputArea("observacaoAutorizador"));
        containerRegulacao.add(new DisabledInputArea("observacaoDevolucao"));
        getForm().add(containerRegulacao);

        getForm().add(new DisabledInputField("dataAgendamento"));
        getForm().add(new DisabledInputField("dataDesejada"));
        getForm().add(new DisabledInputField("unidadeExecutante.cidade.descricao"));
        getForm().add(new DisabledInputField("unidadeExecutante.telefone"));

        getForm().add(containerTableSms = new WebMarkupContainer("containerTableSms"));
        getForm().add(containerTableAgendamentos = new WebMarkupContainer("containerTableAgendamentos"));
        getForm().add(containerTableOcorrencias = new WebMarkupContainer("containerTableOcorrencias"));
        containerTableSms.setOutputMarkupId(true).setVisible(true);
        containerTableAgendamentos.setOutputMarkupId(true).setVisible(true);
        containerTableOcorrencias.setOutputMarkupId(true).setVisible(true);

        containerTableOcorrencias.add(table = new Table("tableOcorrencias", getColumnsOcorrencias(), getCollectionProviderOcorrencias()));
        table.populate();

        containerTableAgendamentos.add(tableAgendamento = new Table("tableAgendamento", getColumnsAgendamento(), getCollectionProviderAgendamento()));
        tableAgendamento.populate();

        containerTableSms.add(tableSms = new Table("tableSms", getColumnsSms(), getCollectionProviderSms()));
        tableSms.populate();

        getForm().add(containerTableAgendamentos);
        getForm().add(containerTableSms);
        getForm().add(containerTableOcorrencias);


        getForm().add(containerDescricao = new WebMarkupContainer("containerDescricao"));
        containerDescricao.setOutputMarkupId(true);
        containerDescricao.add(new DisabledInputArea("descricao", new PropertyModel<String>(this, "descricao")));

        containerSolicitarPrioridadeMelhorDiaHora = new WebMarkupContainer("containerSolicitarPrioridadeMelhorDiaHora");
        containerSolicitarPrioridadeMelhorDiaHora.setOutputMarkupId(true);
        containerSolicitarPrioridadeMelhorDiaHora.setOutputMarkupPlaceholderTag(true);

        containerSolicitarPrioridadeMelhorDiaHora.add(checkDom = new CheckBoxLongValue  ("melhorDiaDom"));
        containerSolicitarPrioridadeMelhorDiaHora.add(checkSeg = new CheckBoxLongValue  ("melhorDiaSeg"));
        containerSolicitarPrioridadeMelhorDiaHora.add(checkTer = new CheckBoxLongValue  ("melhorDiaTer"));
        containerSolicitarPrioridadeMelhorDiaHora.add(checkQua = new CheckBoxLongValue  ("melhorDiaQua"));
        containerSolicitarPrioridadeMelhorDiaHora.add(checkQui = new CheckBoxLongValue  ("melhorDiaQui"));
        containerSolicitarPrioridadeMelhorDiaHora.add(checkSex = new CheckBoxLongValue  ("melhorDiaSex"));
        containerSolicitarPrioridadeMelhorDiaHora.add(checkSab = new CheckBoxLongValue  ("melhorDiaSab"));
        desabilitarMelhoresDias(false);

        containerSolicitarPrioridadeMelhorDiaHora.add(InputMelhorHora = new DisabledInputField("melhorHora"));

        getForm().add(containerSolicitarPrioridadeMelhorDiaHora);


        getForm().add(containerSolicitacaoProfissional = new WebMarkupContainer("containerSolicitacaoProfissional"));
        containerSolicitacaoProfissional.setOutputMarkupId(true);
        containerSolicitacaoProfissional.add(new DisabledInputField("descricaoFlagEnviarRegulacao"));
        containerSolicitacaoProfissional.add(new DisabledInputArea("descricaoEnviarRegulacao"));

        AbstractAjaxButton btnImprimir;
        getForm().add(btnImprimir = new AbstractAjaxButton("btnImprimirComprovantes") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                initDlgRecomendacoesAgenda(target);
            }
        });

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(getForm());

        configuraContainers();

        if (CollectionUtils.isNotNullEmpty(agendaGradeAtendimentoHorarioList)) {
            btnImprimir.setVisible(true);
        } else {
            btnImprimir.setVisible(false);
        }
    }

    private Form<SolicitacaoAgendamento> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<>(new SolicitacaoAgendamento()));
        }
        return this.form;
    }

    private void desabilitarMelhoresDias(boolean viewOnly) {
        checkDom.setEnabled(viewOnly);
        checkSeg.setEnabled(viewOnly);
        checkTer.setEnabled(viewOnly);
        checkQua.setEnabled(viewOnly);
        checkQui.setEnabled(viewOnly);
        checkSex.setEnabled(viewOnly);
        checkSab.setEnabled(viewOnly);
    }

    private void configuraContainers() {
        if (CollectionUtils.isEmpty(solicitacaoAgendamentoOcorrenciaList)) {
            containerTableOcorrencias.setVisible(false);
        } else {
            containerTableOcorrencias.setVisible(true);
        }
        if (CollectionUtils.isEmpty(agendaGradeAtendimentoHorarioList)) {
            containerTableAgendamentos.setVisible(false);
        } else {
            containerTableAgendamentos.setVisible(true);
        }
        if (CollectionUtils.isEmpty(smsControleIntegracaoList)) {
            containerTableSms.setVisible(false);
        } else {
            containerTableSms.setVisible(true);
        }

        containerSolicitacaoProfissional.setVisible(RepositoryComponentDefault.TIPO_CONTROLE_REGULACAO_SOLICITACAO_PROFISSIONAL.equals(getTipoControleRegulacao()));
    }

    private ICollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento> getCollectionProviderOcorrencias() {
        if (this.collectionProvider == null) {
            this.collectionProvider = new CollectionProvider<SolicitacaoAgendamentoOcorrencia, SolicitacaoAgendamento>() {
                @Override
                public Collection getCollection(SolicitacaoAgendamento sa) throws DAOException, ValidacaoException {
                    return solicitacaoAgendamentoOcorrenciaList;
                }
            };
        }

        return this.collectionProvider;
    }

    private ICollectionProvider<AgendaGradeAtendimentoHorario, SolicitacaoAgendamento> getCollectionProviderAgendamento() {
        if (this.collectionProviderAgendamento == null) {
            this.collectionProviderAgendamento = new CollectionProvider<AgendaGradeAtendimentoHorario, SolicitacaoAgendamento>() {
                @Override
                public Collection getCollection(SolicitacaoAgendamento sa) throws DAOException, ValidacaoException {
                    return agendaGradeAtendimentoHorarioList;
                }
            };
        }

        return this.collectionProviderAgendamento;
    }

    private ICollectionProvider<SmsControleIntegracao, SolicitacaoAgendamento> getCollectionProviderSms() {
        if (this.collectionProviderSms == null) {
            this.collectionProviderSms = new CollectionProvider<SmsControleIntegracao, SolicitacaoAgendamento>() {
                @Override
                public Collection getCollection(SolicitacaoAgendamento sa) throws DAOException, ValidacaoException {
                    return smsControleIntegracaoList;
                }
            };
        }

        return this.collectionProviderSms;
    }

    private List<IColumn> getColumnsExamesSolicitados() {
        List<IColumn> columns = new ArrayList<IColumn>();
        SolicitacaoAgendamentoExame proxy = on(SolicitacaoAgendamentoExame.class);

        columns.add(createColumn(bundle("codigo"), proxy.getExameProcedimento().getProcedimento().getCodigoFormatado()));
        columns.add(createColumn(bundle("procedimento"), proxy.getExameProcedimento().getDescricaoProcedimento()));
        columns.add(createColumn(bundle("complemento"), proxy.getComplemento()));

        return columns;
    }

    private List<IColumn> getColumnsAgendamento() {
        List<IColumn> columns = new ArrayList();

        AgendaGradeAtendimentoHorario proxy = on(AgendaGradeAtendimentoHorario.class);
        columns.add(createColumn(bundle("dataAgendamento"), proxy.getDataHoraAgendamentoFormatado()));
        columns.add(createColumn(bundle("local"), proxy.getLocalAgendamento()));
        columns.add(createColumn(bundle("unidade_solicitante"), proxy.getEmpresaOrigem().getDescricao()));
        columns.add(createColumn(bundle("registro"), proxy.getDataCadastro()));
        columns.add(createColumn(bundle("cmce"), proxy.getCodigoExterno()));


        return columns;
    }

    private List<IColumn> getColumnsSms() {
        List<IColumn> columns = new ArrayList();

        SmsControleIntegracao proxy = on(SmsControleIntegracao.class);
        columns.add(createColumn(bundle("dataEnvio"), proxy.getSmsMensagem().getDataCadastro()));
        columns.add(createColumn(bundle("mensagem"), proxy.getSmsMensagem().getMensagem()));
        columns.add(createColumn(bundle("tipoMensagem"), proxy.getDescricaoTipoMensagem()));
        columns.add(createColumn(bundle("situacao"), proxy.getSmsMensagem().getMensagemOperadora()));
        columns.add(createColumn(bundle("resposta"), proxy.getSmsMensagem().getUltimaResposta()));
        columns.add(createColumn(bundle("dataResposta"), proxy.getSmsMensagem().getDataUltimaResposta()));
        columns.add(createColumn(bundle("situacao"), proxy.getSmsMensagem().getDescricaoStatus()));


        return columns;
    }

    private void initDlgRecomendacoesAgenda(AjaxRequestTarget target) {
        if (dlgRecomendacoesAgenda == null) {
            WindowUtil.addModal(target, this, dlgRecomendacoesAgenda = new DlgRecomendacoesAgenda(WindowUtil.newModalId(this)) {
                @Override
                public DataReport onImprimir() throws ReportException {
                    return imprimirComprovanteAgendamento();
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }

        dlgRecomendacoesAgenda.show(target, agendaGradeAtendimentoHorarioList, true);
    }

    private DataReport imprimirComprovanteAgendamento() throws ReportException {
        if (CollectionUtils.isNotNullEmpty(agendaGradeAtendimentoHorarioList)) {
            RelatorioImprimirComprovanteAgendamentoDTOParam reportParam = new RelatorioImprimirComprovanteAgendamentoDTOParam();
            reportParam.setAgendaGradeAtendimentoHorarioList(agendaGradeAtendimentoHorarioList);

            return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(reportParam);
        }
        return null;
    }

    public List<SmsControleIntegracao> getSmsControleIntegracaoList() {
        smsControleIntegracaoList = LoadManager.getInstance(SmsControleIntegracao.class)
                .addProperties(new HQLProperties(SmsControleIntegracao.class).getProperties())
                .addProperties(new HQLProperties(SmsMensagem.class, VOUtils.montarPath(SmsControleIntegracao.PROP_SMS_MENSAGEM)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SmsControleIntegracao.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(SmsControleIntegracao.PROP_SMS_MENSAGEM, SmsMensagem.PROP_DATA_CADASTRO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
        return smsControleIntegracaoList;
    }

    public List<AgendaGradeAtendimentoHorario> getAgendaGradeAtendimentoHorarioList() {
        agendaGradeAtendimentoHorarioList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, AgendaGradeAtendimentoHorario.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, BuilderQueryCustom.QueryParameter.NOT_IN, java.util.Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_CANCELADO, AgendaGradeAtendimentoHorario.STATUS_REMANEJADO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO_PRINCIPAL, BuilderQueryCustom.QueryParameter.IS_NULL))
                .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeAtendimentoHorario.PROP_DATA_AGENDAMENTO))
                .start().getList();
        return agendaGradeAtendimentoHorarioList;
    }

    private List<SolicitacaoAgendamentoOcorrencia> getSolicitacaoAgendamentoOcorrenciaList() {
        SolicitacaoAgendamentoOcorrencia proxy = on(SolicitacaoAgendamentoOcorrencia.class);
        solicitacaoAgendamentoOcorrenciaList = LoadManager.getInstance(SolicitacaoAgendamentoOcorrencia.class)
                .addProperty(path(proxy.getDataOcorrencia()))
                .addProperty(path(proxy.getUsuario().getNome()))
                .addProperty(path(proxy.getDescricao()))
                .addProperty(path(proxy.getTipoOcorrencia()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getSolicitacaoAgendamento()), solicitacaoAgendamento))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataOcorrencia()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
        return solicitacaoAgendamentoOcorrenciaList;
    }


    private ICollectionProvider getCollectionProviderExamesSolicitados() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                lstSolicitacaoAgendamentoExame = LoadManager.getInstance(SolicitacaoAgendamentoExame.class)
                        .addProperty(SolicitacaoAgendamentoExame.PROP_CODIGO)
                        .addProperty(SolicitacaoAgendamentoExame.PROP_COMPLEMENTO)
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamentoExame.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamentoExame.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO))
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamentoExame.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(SolicitacaoAgendamentoExame.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_REFERENCIA))
                        .addParameter(new QueryCustom.QueryCustomParameter(SolicitacaoAgendamentoExame.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                        .start().getList();
                return lstSolicitacaoAgendamentoExame;
            }
        };
    }

    private List<IColumn> getColumnsOcorrencias() {
        List<IColumn> columns = new ArrayList<IColumn>();

        ColumnFactory columnFactory = new ColumnFactory(SolicitacaoAgendamentoOcorrencia.class);

        columns.add(columnFactory.createColumn(BundleManager.getString("data_hora"), VOUtils.montarPath(SolicitacaoAgendamentoOcorrencia.PROP_DATA_HORA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("usuario"), VOUtils.montarPath(SolicitacaoAgendamentoOcorrencia.PROP_USUARIO, Usuario.PROP_NOME)));
        columns.add(columnFactory.createColumn(BundleManager.getString("tipo"), VOUtils.montarPath(SolicitacaoAgendamentoOcorrencia.PROP_DESCRICAO_TIPO_OCORRENCIA)));
        columns.add(columnFactory.createColumn(BundleManager.getString("ocorrencia"), VOUtils.montarPath(SolicitacaoAgendamentoOcorrencia.PROP_DESCRICAO)));

        return columns;
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void setDTO(AjaxRequestTarget target, AgendamentoListaEsperaDTO dto) {
        carregarSolicitacaoAgendamento(dto.getSolicitacaoAgendamento().getCodigo());
//        txtCelular.limpar(target);
//        txtCns.limpar(target);
//        autoCompleteEstabelecimentoCerest.limpar(target);
//
//        this.codigoUsuarioCadsus = dto.getUsuarioCadsus().getCodigo();
//        this.nomeUsuarioCadsus = dto.getUsuarioCadsus().getNomeSocial();
//        this.celularUsuarioCadsus = dto.getUsuarioCadsus().getCelular();
//        this.cnsUsuarioCadsus = dto.getUsuarioCadsus().getCns();
//        if(isPermiteInformarLocaldeTrabalhoPacienteRecepcao()){
//            this.estabelecimentoCerest = dto.getUsuarioCadsus().getEstabelecimentoCerest();
//        }
//        this.actionType = actionType;
//
//        txtCelular.setComponentValue(this.celularUsuarioCadsus);
////        autoCompleteEstabelecimentoCerest.limpar(target);
//        if(estabelecimentoCerest != null){
//            autoCompleteEstabelecimentoCerest.setComponentValue(this.estabelecimentoCerest);
//        }
//        target.add(txtNome);
//        target.add(txtCelular);
//        target.add(txtCns);
//        target.add(autoCompleteEstabelecimentoCerest);
    }

    private void carregarSolicitacaoAgendamento(Long codigo) {
        if(codigo != null){
            solicitacaoAgendamento = LoadManager.getInstance(SolicitacaoAgendamento.class)
                    .addProperties(new HQLProperties(SolicitacaoAgendamento.class).getProperties())
                    .addProperties(new HQLProperties(UsuarioCadsus.class, SolicitacaoAgendamento.PROP_USUARIO_CADSUS).getProperties())
                    .addProperties(new HQLProperties(Usuario.class, SolicitacaoAgendamento.PROP_USUARIO).getProperties())
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_EMPRESA, Empresa.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_EMPRESA, Empresa.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_UNIDADE_EXECUTANTE, Empresa.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_UNIDADE_EXECUTANTE, Empresa.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_UNIDADE_EXECUTANTE, Empresa.PROP_TELEFONE))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_UNIDADE_EXECUTANTE, Empresa.PROP_CIDADE, Cidade.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_UNIDADE_EXECUTANTE, Empresa.PROP_CIDADE, Cidade.PROP_DESCRICAO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_AUTORIZADOR, Usuario.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(SolicitacaoAgendamento.PROP_USUARIO_AUTORIZADOR, Usuario.PROP_NOME))
                    .setId(codigo).start().getVO();

            getSmsControleIntegracaoList();
            getAgendaGradeAtendimentoHorarioList();
            getSolicitacaoAgendamentoOcorrenciaList();
            consultarRecomendacoes();
        }

        getForm().getModel().setObject(solicitacaoAgendamento != null ? solicitacaoAgendamento : new SolicitacaoAgendamento());
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (CollectionUtils.isNotNullEmpty(lstSolicitacaoAgendamentoExame)) {
            response.render(OnDomReadyHeaderItem.forScript(JScript.showFieldset(containerExamesSolicitados)));
        }
    }

    public void consultarRecomendacoes() {
        descricao = null;
        containerDescricao.setVisible(true);
        possuiDescricao = false;

        List<AgendaGradeAtendimentoHorario> agendamentosList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, AgendaGradeAtendimentoHorario.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE, AgendaGrade.PROP_AGENDA, Agenda.PROP_CODIGO))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_SOLICITACAO_AGENDAMENTO, solicitacaoAgendamento))
                .start().getList();

        if(CollectionUtils.isNotNullEmpty(agendamentosList)){
            if(agendamentosList.get(0).getAgendaGradeAtendimento() != null
                    && agendamentosList.get(0).getAgendaGradeAtendimento().getCodigo() != null){
                carregarAnexoAgenda(agendamentosList.get(0).getAgendaGradeAtendimento().getAgendaGrade().getAgenda());
            }
        }

        if (!possuiDescricao) {
            containerDescricao.setVisible(false);
        }
    }

    private void carregarAnexoAgenda(Agenda agenda) {
        Agenda a = LoadManager.getInstance(Agenda.class)
                .addProperties(new HQLProperties(Agenda.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, Agenda.PROP_GERENCIADOR_ARQUIVO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(Agenda.PROP_CODIGO, agenda.getCodigo()))
                .start().getVO();

        if (a != null && RepositoryComponentDefault.SIM_LONG.equals(a.getFlagVisualizaAgendar())) {
            if (a.getRecomendacoes() != null) {
                descricao = a.getRecomendacoes();
                possuiDescricao = true;
            } else {
                containerDescricao.setVisible(false);
            }
        }
    }

    public String getTipoControleRegulacao() {
        if(tipoControleRegulacao == null){
            try {
                tipoControleRegulacao = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("tipoControleRegulação");
            } catch (DAOException e) {
                 br.com.ksisolucoes.util.log.Loggable.log.error(e);
            }
        }
        return tipoControleRegulacao;
    }
}