package br.com.celk.view.vigilancia.requerimentovigilancia.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaCancelamentoFinalizacaoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaEntregaDocumentoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgEntregaDocumentoRequerimentoVigilancia extends Window{
    
    private PnlEntregaDocumentoRequerimentoVigilancia pnlEntregaDocumentoRequerimentoVigilancia;
    
    public DlgEntregaDocumentoRequerimentoVigilancia(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("entregaDocumento");
            }
        });
                
        setInitialWidth(550);
        setInitialHeight(120);
        setResizable(true);
        
        
        setContent(pnlEntregaDocumentoRequerimentoVigilancia = new PnlEntregaDocumentoRequerimentoVigilancia(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaEntregaDocumentoDTO dto) throws ValidacaoException, DAOException {
                close(target);
                DlgEntregaDocumentoRequerimentoVigilancia.this.onConfirmar(target, dto);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, RequerimentoVigilanciaEntregaDocumentoDTO dto) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, RequerimentoVigilancia rv){
        show(target);
        pnlEntregaDocumentoRequerimentoVigilancia.setObject(target, rv);
    }
}