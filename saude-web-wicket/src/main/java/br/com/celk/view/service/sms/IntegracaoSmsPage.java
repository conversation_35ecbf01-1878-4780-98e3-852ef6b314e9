package br.com.celk.view.service.sms;

import br.com.celk.bo.service.sms.interfaces.facade.SmsFacade;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.service.sms.dlg.DlgMensagemEnviada;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.service.sms.SmsMensagem;
import br.com.ksisolucoes.vo.service.sms.SmsParametro;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Criado em: Nov 18, 2013
 */
public class IntegracaoSmsPage extends BasePage {

    private String paciente;
    private DatePeriod periodo;
    private Long tipoCadastro;
    private Long statusSms;
    private PageableTable pageableTable;
    private DlgMensagemEnviada dlgMensagemEnviada;

    @Override
    protected void postConstruct() {
        Form form = new Form("form");
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new InputField("paciente"));
        form.add(new PnlChoicePeriod("periodo"));
        form.add(DropDownUtil.getIEnumDropDown("tipoCadastro", SmsMensagem.TipoCadastro.values()));
        form.add(DropDownUtil.getIEnumDropDown("statusSms", SmsMensagem.StatusSms.values(), true));

        pageableTable = new PageableTable("table", getColumns(), getPageProvider());
        form.add(pageableTable);

        if (limiteSmsDiario()){
            getFeedbackMessages().warn(this, bundle("limite_diario_sms_atingido"));
        }

        form.add(new ProcurarButton("btnProcurar", pageableTable) {
            @Override
            public Object getParam() {
                return null;
            }
        });
        form.add(new AbstractAjaxButton("btnAtualizarMensagens") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(SmsFacade.class).atualizarMensagensSms();
                BOFactoryWicket.getBO(SmsFacade.class).consultarRespostasSms();
                pageableTable.populate(target);
            }
        });

        add(form);
    }

    private List<IColumn> getColumns() {
        SmsMensagem proxy = on(SmsMensagem.class);
        List<IColumn> columns = new ArrayList<>();

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("codigo"), proxy.getCodigo() , proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNome() , proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("celular"), proxy.getTelefone() , proxy.getTelefone()));
        columns.add(createSortableColumn(bundle("data"), proxy.getDataCadastro() , proxy.getDataCadastro()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(createSortableColumn(bundle("retorno"), proxy.getMensagemOperadora() , proxy.getMensagemOperadora()));
        columns.add(createSortableColumn(bundle("resposta"), proxy.getUltimaResposta() , proxy.getUltimaResposta()));
        columns.add(createSortableColumn(bundle("dataResposta"), proxy.getDataUltimaResposta() , proxy.getDataUltimaResposta()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<SmsMensagem>() {
            @Override
            public void customizeColumn(SmsMensagem rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<SmsMensagem>() {
                    @Override
                    public void action(AjaxRequestTarget target, SmsMensagem modelObject) throws ValidacaoException, DAOException {
                        if (dlgMensagemEnviada == null) {
                            addModal(target, dlgMensagemEnviada = new DlgMensagemEnviada(newModalId()));
                        }
                        dlgMensagemEnviada.setModelObject(modelObject.getMensagem());
                        dlgMensagemEnviada.show(target);
                    }
                });
            }
        };
    }

    private IPagerProvider getPageProvider() {
        return new CustomizeConsultaPagerProvider<SmsMensagem>(new CustomizeConsultaSmsMensagem() {
            @Override
            public void consultaCustomizeParameters(List<BuilderQueryCustom.QueryParameter> parameters) {
                super.consultaCustomizeParameters(parameters);

                if (paciente != null) {
                    parameters.add(new QueryCustom.QueryCustomParameter(
                            new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SmsMensagem.PROP_NOME_PACIENTE), BuilderQueryCustom.QueryParameter.ILIKE, paciente))),
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                            new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SmsMensagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                                    new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                            new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SmsMensagem.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, paciente))))));
                }
                if (SmsMensagem.TipoCadastro.CADASTRO.value().equals(tipoCadastro)) {
                    parameters.add(new QueryCustom.QueryCustomParameter(SmsMensagem.PROP_DATA_CADASTRO, periodo));
                } else if (SmsMensagem.TipoCadastro.RESPOSTA.value().equals(tipoCadastro)) {
                    parameters.add(new QueryCustom.QueryCustomParameter(SmsMensagem.PROP_DATA_ULTIMA_RESPOSTA, periodo));
                }
                parameters.add(new QueryCustom.QueryCustomParameter(SmsMensagem.PROP_STATUS, statusSms));
            }
        }) {
            @Override
            public SortParam<String> getDefaultSort() {
                return new SortParam(SmsMensagem.PROP_CODIGO, false);
            }
        };
    }

    public boolean limiteSmsDiario() {
        try {
            SmsParametro smsParametro = LoadManager.getInstance(SmsParametro.class)
                    .addProperties(new HQLProperties(SmsParametro.class).getProperties())
                    .start().getVO();
            return DataUtil.getDataAtualSemHora().equals(smsParametro.getDataLimiteAtingido())
                    && RepositoryComponentDefault.SIM_LONG.equals(smsParametro.getFlagLimiteDiarioAtingido());
        } catch (NullPointerException e) {
            return false;
        }

    }

    @Override
    public String getTituloPrograma() {
        return WicketMethods.bundle("integracaoSms");
    }
}
