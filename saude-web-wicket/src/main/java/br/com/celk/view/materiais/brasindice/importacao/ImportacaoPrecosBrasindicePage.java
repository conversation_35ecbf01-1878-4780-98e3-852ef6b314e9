package br.com.celk.view.materiais.brasindice.importacao;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.css.TextAlign;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.StringColumn;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.basico.interfaces.facade.MaterialBasicoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.materiais.importacao.brasindice.dto.BrasindiceDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.methods.CoreMethods;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.BrasindiceProcess;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import static ch.lambdaj.Lambda.on;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.upload.FileUpload;
import org.apache.wicket.markup.html.form.upload.FileUploadField;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
public class ImportacaoPrecosBrasindicePage extends BasePage implements IAsyncProcessNotification {

    private AbstractAjaxButton btnImportar;
    private Form form;
    private RepeatingView controls;
    private String arquivo;
    private FileUploadField fileInput;
    private List<FileUpload> fileUpload;
    private DateChooserAjax dateChooserData;
    private Date dataVigencia;
    private Long modeloArquivo;
    private Table table;
    private List<BrasindiceProcess> itens = new ArrayList<BrasindiceProcess>();

    public ImportacaoPrecosBrasindicePage() {
        super();
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(dateChooserData = new DateChooserAjax("dataVigencia"));
        dateChooserData.setRequired(true);
        dateChooserData.setLabel(new Model(BundleManager.getString("dataInicioVigencia")));
        form.add(DropDownUtil.getIEnumDropDown("modeloArquivo", BrasindiceDTO.ModeloArquivo.values()));

        form.add(fileInput = new FileUploadField("fileUpload"));

        form.add(controls = new RepeatingView("controls"));

        controls.add(btnImportar = new AbstractAjaxButton(controls.newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                sendFile(target);
            }
        });

        btnImportar.add(new AttributeModifier("class", "refresh"));
        btnImportar.add(new AttributeModifier("value", BundleManager.getString("importarArquivos")));

        table = new Table("table", getColumns(), getCollectionPorvider());
        form.add(table.populate());
        add(form);
        try {
            BOFactoryWicket.getBO(MaterialBasicoFacade.class).removerBrasindiceProcessAdicionais();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        }
        atualizarItens();
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        BrasindiceProcess proxy = on(BrasindiceProcess.class);
        columns.add(getCustomColumn());
        columns.add(WicketMethods.createColumn(BundleManager.getString("data"), proxy.getAsyncProcess().getDataRegistro()));
        columns.add(new StringColumn(BundleManager.getString("registros"), CoreMethods.path(proxy.getDescricaoRegistros())).setTextAlign(TextAlign.CENTER));
        columns.add(getCustomColumnStatus());

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<BrasindiceProcess>() {
            @Override
            public void customizeColumn(BrasindiceProcess rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<BrasindiceProcess>() {
                    @Override
                    public void action(AjaxRequestTarget target, BrasindiceProcess modelObject) throws ValidacaoException, DAOException {
                        verificaMsgErro(modelObject.getCodigo());
                    }
                }).setEnabled(rowObject.getMensagemValidacao() != null && !rowObject.getMensagemValidacao().isEmpty());
            }
        };
    }

    public CustomColumn getCustomColumnStatus() {
        return new CustomColumn<BrasindiceProcess>() {
            @Override
            public Component getComponent(String componentId, BrasindiceProcess rowObject) {
                return new StatusBrasindiceProcessColumnPanel(componentId, rowObject);
            }
        };
    }

    private ICollectionProvider getCollectionPorvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }

    private void atualizarItens() {
        itens = LoadManager.getInstance(BrasindiceProcess.class)
                .addProperties(new HQLProperties(BrasindiceProcess.class).getProperties())
                .addProperties(new HQLProperties(AsyncProcess.class, BrasindiceProcess.PROP_ASYNC_PROCESS).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(BrasindiceProcess.PROP_ASYNC_PROCESS, AsyncProcess.PROP_DATA_REGISTRO), BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("importacaoTabelaPrecoBrasindice");
    }

    private void sendFile(AjaxRequestTarget target) throws ValidacaoException, DAOException {

        final List<FileUpload> uploads = fileInput.getFileUploads();
        if (uploads != null) {
            for (FileUpload upload : uploads) {
                try {
                    File newFile = File.createTempFile("IMPORTACAO", "IPE");

                    newFile.createNewFile();
                    upload.writeTo(newFile);

                    FileInputStream entrada = new FileInputStream(newFile);
                    InputStreamReader entradaFormatada = new InputStreamReader(entrada);
                    int c = entradaFormatada.read();
                    Long tipoArquivo;
//                    34 == aspas duplas
                    if (c == 34) {
                        tipoArquivo = BrasindiceDTO.TipoArquivo.CSV.getValue();
                    } else {
                        tipoArquivo = BrasindiceDTO.TipoArquivo.FIXED.getValue();
                    }

                    importarTabelaPrecosBrasindice(newFile.getAbsolutePath(), tipoArquivo);

                    atualizarItens();
                    table.update(target);
                } catch (IOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }

        } else {
            throw new ValidacaoException(BundleManager.getString("escolhaUmArquivoASerImportado"));
        }
    }

    private void importarTabelaPrecosBrasindice(String filePath, Long tipoArquivo) throws DAOException, ValidacaoException {
        BrasindiceDTO dto = new BrasindiceDTO();
        dto.setFilePath(filePath);
        dto.setDataVigencia(dataVigencia);
        dto.setTipoArquivo(tipoArquivo);
        dto.setModeloArquivo(modeloArquivo);
        BOFactoryWicket.getBO(MaterialBasicoFacade.class).cadastrarProcessoBrasindice(dto);
    }

    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        atualizarItens();
        table.update(target);
    }

    private void verificaMsgErro(Long codigoBrasindiceProcess) throws ValidacaoException {
        BrasindiceProcess bp = (BrasindiceProcess) LoadManager.getInstance(BrasindiceProcess.class)
                .addProperties(new HQLProperties(BrasindiceProcess.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BrasindiceProcess.PROP_CODIGO), codigoBrasindiceProcess))
                .start().getVO();

        if (bp.getMensagemValidacao() != null) {
            Loggable.log.error(bp.getMensagemValidacao());
            throw new ValidacaoException(bundle("verifiqueModeloArquivoSelecionado"));
        }
    }
}
