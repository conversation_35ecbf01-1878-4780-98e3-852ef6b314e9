package br.com.celk.view.unidadesaude.bpa.processo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dialog.DlgData;
import br.com.celk.component.dialog.DlgMotivoArea;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.duracaofield.MesAnoField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.ControleProducaoPagamento;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Américo
 */

@Private
public class ConsultaControleProducaoPagamentoPage extends ConsultaPage<ControleProducaoPagamento, List<BuilderQueryCustom.QueryParameter>> {

    private boolean ViewOnly;
    private InputField<String> nrDocumento;
    private AutoCompleteConsultaEmpresa prestador;
    private DropDown dropDownSituacao;
    private IPagerProvider pagerProvider;
    private DlgMotivoArea<ControleProducaoPagamento> dlgMotivoCancelamento;
    private DlgData dlgDataCancelamento;
    private MesAnoField dcDataCompetencia;


    private Date dataCompetencia;
    private String numeroDocumento;
    private Long situacao;
    private Empresa empresa;

    public ConsultaControleProducaoPagamentoPage() {
    }

    public ConsultaControleProducaoPagamentoPage(boolean ViewOnly) {
        this.ViewOnly = ViewOnly;
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(prestador = new AutoCompleteConsultaEmpresa("empresa"));
        form.add(dcDataCompetencia = new MesAnoField("dataCompetencia"));
        form.add(nrDocumento = new InputField<String>("numeroDocumento"));
        form.add(DropDownUtil.getIEnumDropDown("situacao", ControleProducaoPagamento.Situacao.values(), true, bundle("todos")));

    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {

        ControleProducaoPagamento proxy = on(ControleProducaoPagamento.class);
        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("prestador"), proxy.getEmpresa().getDescricao(), proxy.getEmpresa().getDescricaoFormatado()));
        columns.add(createSortableColumn(bundle("competencia"), proxy.getDataCompetencia(), proxy.getCompetenciaFormatada()));
        columns.add(createSortableColumn(bundle("numeroDocumento"), proxy.getNumeroDocumento()));
        columns.add(createSortableColumn(bundle("situacao"), proxy.getSituacao(), proxy.getSituacaoFormatado()));
        return columns;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {

            @Override
            public Class getClassConsulta() {
                return ControleProducaoPagamento.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ControleProducaoPagamento.class).getProperties(),
                        new HQLProperties(Empresa.class, VOUtils.montarPath(ControleProducaoPagamento.PROP_EMPRESA)).getProperties()
                );
            }
        }) {

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(ControleProducaoPagamento.PROP_NUMERO_DOCUMENTO, true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleProducaoPagamento.PROP_EMPRESA), empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleProducaoPagamento.PROP_DATA_COMPETENCIA), dataCompetencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleProducaoPagamento.PROP_NUMERO_DOCUMENTO), numeroDocumento));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ControleProducaoPagamento.PROP_SITUACAO), situacao));

        return parameters;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<ControleProducaoPagamento>() {
            @Override
            public void customizeColumn(final ControleProducaoPagamento rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<ControleProducaoPagamento>() {
                    @Override
                    public void action(AjaxRequestTarget target, ControleProducaoPagamento modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroControleProducaoPagamentoPage(modelObject));
                    }
                }).setEnabled(situacaoPendenteControleProducao(rowObject));

                addAction(ActionType.CANCELAR, rowObject, new IModelAction<ControleProducaoPagamento>() {
                    @Override
                    public void action(AjaxRequestTarget target, ControleProducaoPagamento modelObject) throws ValidacaoException, DAOException {
                        showDlgCancelamento(target, modelObject);
                    }
                }).setEnabled(situacaoPendenteControleProducao(rowObject));

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ControleProducaoPagamento>() {
                    @Override
                    public void action(AjaxRequestTarget target, ControleProducaoPagamento modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroControleProducaoPagamentoPage(modelObject, true));
                    }
                });
                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ControleProducaoPagamento>() {
                    @Override
                    public void action(AjaxRequestTarget target, ControleProducaoPagamento modelObject) throws ValidacaoException, DAOException {
//                        confirmarPagamento(modelObject, target);
                        showDlgDataCancelamento(target,modelObject);
                    }
                }).setEnabled(situacaoPendenteControleProducao(rowObject));
            }
        };
    }

    private void showDlgCancelamento(AjaxRequestTarget target, final ControleProducaoPagamento modelObject) {
        if (dlgMotivoCancelamento == null) {
            addModal(target, dlgMotivoCancelamento = new DlgMotivoArea<ControleProducaoPagamento>(newModalId(), bundle("motivoCancelamento")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, ControleProducaoPagamento object) throws ValidacaoException, DAOException {
                    cancelar(modelObject,target,motivo);
            }
            });
        }
        dlgMotivoCancelamento.show(target);
    }


    private void showDlgDataCancelamento(AjaxRequestTarget target, final ControleProducaoPagamento modelObject) {
        if (dlgDataCancelamento == null) {
            addModal(target, dlgDataCancelamento = new DlgData(newModalId(), bundle("msgConfirmeDataPagamento"), "dataPagamento") {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Date date) throws ValidacaoException, DAOException {
                    confirmarPagamento(modelObject,target,date);
                }
            });
        }
        dlgDataCancelamento.show(target);
    }


    private void confirmarPagamento(ControleProducaoPagamento object, AjaxRequestTarget target,Date date) throws ValidacaoException, DAOException {
        object.setSituacao(ControleProducaoPagamento.Situacao.PAGO.value());
        object.setUsuarioPagamento(SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario());
        object.setDataPagamento(date);
        salvar(object, target);
    }

    private void cancelar(ControleProducaoPagamento object, AjaxRequestTarget target, String motivo) throws ValidacaoException, DAOException {
        object.setSituacao(ControleProducaoPagamento.Situacao.CANCELADO.value());
        object.setUsuarioCancelado(SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario());
        object.setDataCancelamento(DataUtil.getDataAtual());
        object.setMotivoCancelamento(motivo);
        salvar(object, target);
    }

    private void salvar(ControleProducaoPagamento controleProducaoPagamento, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        BOFactoryWicket.save(controleProducaoPagamento);
        this.getPageableTable().update(target);
    }

    private boolean situacaoPendenteControleProducao(ControleProducaoPagamento rowObject) {
        if (rowObject != null) {
            if (ControleProducaoPagamento.Situacao.PENDENTE.value().equals(rowObject.getSituacao())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroControleProducaoPagamentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaControleProducaoPagamento");
    }
}