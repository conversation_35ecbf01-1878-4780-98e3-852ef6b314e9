package br.com.celk.view.vigilancia.rotinas.autoIntimacao.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.rotinas.autoIntimacao.autocomplete.AutoCompleteConsultaMotivoRetorno;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.odlabs.wiquery.ui.datepicker.DateOption;

/**
 * Created by izael on 23/10/17.
 */
public abstract class PnlAutoIntimacaoReceber extends Panel {

    private DateChooser dchRecebimento;
    private AutoCompleteConsultaMotivoRetorno autoCompleteConsultaMotivoRetorno;
    private AbstractAjaxLink btnCadastrarMotivo;
    private DlgCadastroMotivoRetorno dlgCadastroMotivoRetorno;
    private Form<AutoIntimacao> form;
    private AutoIntimacao autoIntimacao;

    public PnlAutoIntimacaoReceber(String id, AutoIntimacao autoIntimacao) {
        super(id);
        this.autoIntimacao = autoIntimacao;
        init();
    }


    public void init() {
        setOutputMarkupId(true);
        form = new Form("form", new CompoundPropertyModel(autoIntimacao));

        form.add(dchRecebimento = new DateChooser(AutoIntimacao.PROP_DATA_RECEBIMENTO));
        dchRecebimento.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dchRecebimento.addAjaxUpdateValue();
        DropDown<Long> dropDownAr;
        form.add(dropDownAr = DropDownUtil.getEnviadosDropDown(AutoIntimacao.PROP_ENVIADO, true, false ));
        dropDownAr.addAjaxUpdateValue();

        form.add(autoCompleteConsultaMotivoRetorno = new AutoCompleteConsultaMotivoRetorno(AutoIntimacao.PROP_MOTIVO_RETORNO));
        autoCompleteConsultaMotivoRetorno.addAjaxUpdateValue();
        form.add(btnCadastrarMotivo = new AbstractAjaxLink("btnCadastrarMotivo") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                showDlgCadastrarMotivoRetorno(target);
            }
        });

        form.add(new SubmitButton("btnSalvar",new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onSalvar(target);
            }
        }).setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);

    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    public abstract void onSalvar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    private void showDlgCadastrarMotivoRetorno(AjaxRequestTarget target) {
        if (dlgCadastroMotivoRetorno == null) {
            WindowUtil.addModal(target, this, dlgCadastroMotivoRetorno = new DlgCadastroMotivoRetorno(WindowUtil.newModalId(PnlAutoIntimacaoReceber.this)){
                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    target.add(autoCompleteConsultaMotivoRetorno);
                    autoCompleteConsultaMotivoRetorno.focus(target);
                }

                @Override
                public void onExcluir(AjaxRequestTarget target, MotivoRetorno motivoRetorno) throws ValidacaoException, DAOException {
                    if (form.getModel().getObject().getMotivoRetorno() != null && motivoRetorno.equals(form.getModel().getObject().getMotivoRetorno())) {
                        autoCompleteConsultaMotivoRetorno.limpar(target);
                    }
                }
            });
        }
        dlgCadastroMotivoRetorno.show(target);
    }
}
