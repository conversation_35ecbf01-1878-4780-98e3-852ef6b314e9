package br.com.celk.view.vigilancia.externo.template.configuracoes.nodes;

import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.vigilancia.externo.template.UsuarioExternoDefaultCadastroPanel;
import br.com.celk.view.vigilancia.externo.template.configuracoes.nodes.annotations.ConfUsuarioVigilanciaNode;
import br.com.celk.view.vigilancia.externo.template.panel.UsuarioExternoMinhaContaNodePanel;
import br.com.celk.vigilancia.nodes.NodesConfUsuariosVigilanciaRef;

/**
 * <AUTHOR>
 */
@ConfUsuarioVigilanciaNode(NodesConfUsuariosVigilanciaRef.MINHA_CONTA)
public class UsuarioExternoMinhaContaNode extends ConfUsuariosVigilanciaNodeImp {

    private UsuarioExternoMinhaContaNodePanel usuarioExternoMinhaContaNodePanel;

    @Override
    public UsuarioExternoDefaultCadastroPanel getPanel(String id) {
        return new UsuarioExternoMinhaContaNodePanel(id);
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("minha_conta");
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.MEDICAL_RECORD;
    }

}
