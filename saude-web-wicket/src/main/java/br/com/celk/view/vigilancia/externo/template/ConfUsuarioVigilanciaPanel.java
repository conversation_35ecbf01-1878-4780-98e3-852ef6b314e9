package br.com.celk.view.vigilancia.externo.template;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.Label;

/**
 * <AUTHOR>
 */
public abstract class ConfUsuarioVigilanciaPanel extends DefaultConsultaConfUsuarioVigilanciaPanel {

    public ConfUsuarioVigilanciaPanel(String id, String titulo) {
        super(id);
        add(new Label("titulo", titulo));
    }

    @Override
    public void postConstruct() {
    }

    @Override
    public void changePanelAction(AjaxRequestTarget target) {
    }

}
