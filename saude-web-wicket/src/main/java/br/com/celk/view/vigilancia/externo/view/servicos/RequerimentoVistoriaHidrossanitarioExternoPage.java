package br.com.celk.view.vigilancia.externo.view.servicos;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.vigilanciaendereco.PnlVigilanciaEndereco;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.StringUtil;
import br.com.celk.view.vigilancia.RequerimentoVigilanciaFiscaisPanel;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.dialog.DlgCadastroResponsavelTecnico;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.externo.view.estabelecimento.CadastroEstabelecimentoExternoPage;
import br.com.celk.view.vigilancia.financeiro.BoletoVigilanciaExternoPage;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.requerimentovigilancia.autocomplete.AutoCompleteConsultaRequerimentoProjetoHidrossanitario;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.AutoCompleteConsultaResponsavelTecnico;
import br.com.celk.view.vigilancia.tipoenquadramentoprojeto.autocomplete.AutoCompleteConsultaTipoEnquadramentoProjeto;
import br.com.celk.view.vigilancia.tipoprojetovigilancia.autocomplete.AutoCompleteConsultaTipoProjetoVigilancia;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.EmailValidator;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitario;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.TipoEnquadramentoProjeto;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.enums.RequerimentosProjetosEnums;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.commons.lang.SerializationUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * Created by sulivan on 03/01/19.
 */
@Deprecated
public class RequerimentoVistoriaHidrossanitarioExternoPage extends RequerimentosVigilanciaPage {
	
	private boolean      editMode;
	private SubmitButton btnSalvar;
	private VoltarButton btnVoltar;
	
	private Form<RequerimentoVistoriaHidrossanitarioDTO>           form;
	private TipoSolicitacao                                        tipoSolicitacao;
	private RequerimentoVigilancia                                 requerimentoVigilancia;
	private RequerimentoVistoriaHidrossanitario                    requerimentoVistoriaHidrossanitario;
	private AutoCompleteConsultaTipoProjetoVigilancia              autoCompleteConsultaTipoProjetoVigilancia;
	private AutoCompleteConsultaTipoEnquadramentoProjeto           autoCompleteConsultaTipoEnquadramentoProjeto;
	private List<RequerimentoVigilanciaAnexoDTO>                   requerimentoVigilanciaAnexoDTOList;
	private DlgImpressaoObjectMulti<RequerimentoVigilancia>        dlgConfirmacaoImpressao;
	private PnlRequerimentoVigilanciaAnexo                         pnlRequerimentoVigilanciaAnexo;
	private AutoCompleteConsultaRequerimentoProjetoHidrossanitario autoCompleteConsultaRequerimentoProjetoHidrossanitario;
	
	private DropDown ddTipoPessoa;
	private AutoCompleteConsultaEstabelecimento    autoCompleteConsultaEstabelecimento;
	private AutoCompleteConsultaVigilanciaPessoa   autoCompleteConsultaVigilanciaPessoa;
	private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
	private DlgCadastroVigilanciaPessoa            dlgCadastroVigilanciaPessoa;
	private WebMarkupContainer                     containerDadosGerais;
	private WebMarkupContainer                     containerEstabelecimento;
	private WebMarkupContainer                     containerPessoa;
	private WebMarkupContainer                     containerTipoProjeto;
	private DropDown ddUsoEdificacao;
	private InputField                             txtObservacaoUsoEdificacao;
	private LongField                              txtNumeroLotesParcelamento;
	private DoubleField                            txtArea;
	private DoubleField                            txtAreaComercial;
	private DoubleField                            txtAreaResidencial;
	private DoubleField                            txtAreaTotal;
	private InputField                             txtEmailPessoa;
	private InputField                             txtEmailEstabelecimento;
	private InputField                             txtNumeroProjetoHidrossanitario;
	private InputField                             txtObraNumeroEndereco;
	private InputField                             txtObraQuadra;
	private InputField                             txtObraNumeroLado;
	private InputField                             txtObraLote;
	private InputField                             txtObraComplemento;
	private InputField                             txtObraNumeroLoteamento;
	
	private WebMarkupContainer containerInformacoes;
	private DropDown           ddRegiaoCobertaRedeEsgoto;
	private DropDown           ddRegiaoAbastecidaAgua;
	private DropDown           ddSistemaAprovAguasPluviais;
	private DropDown           ddUnifamiliar;
	private DropDown           ddLincenciavelAmbiental;
	private DisabledInputField descricaoClassificaoRisco;
	private CheckBoxLongValue  cbxTermoAceite;
	
	
	private Class                  classReturn;
	private WebMarkupContainer     containerTipoEnquadramentoProjeto;
	private WebMarkupContainer     containerDadosObra;
	private ConfiguracaoVigilancia configuracaoVigilancia = null;
	
	private Table                                                    tblTipoProjeto;
	private CompoundPropertyModel<TipoProjetoRequerimentoVigilancia> modelTipoProjetoRequerimentoVigilancia;
	
	private PnlVigilanciaEndereco pnlVigilanciaEndereco;
	
	private ResponsavelTecnico responsavelTecnico;
	private InputField<String> txtInscricaoImobiliaria;
	
	private Table                                  tblResponsavelTecnico;
	private Table                                  tblInscricaoImobiliaria;
	private AutoCompleteConsultaResponsavelTecnico autoCompleteConsultaResponsavelTecnico;
	private AbstractAjaxButton                     btnAdicionarResponsavelTecnico;
	private AbstractAjaxButton                     btnAdicionarInscricao;
	private AbstractAjaxLink                       btnCadadastroResponsavel;
	private DlgCadastroResponsavelTecnico          dlgCadastroResponsavelTecnico;
	
	private String             numeroInscricaoImobiliaria;
	private InputField         txtNumeroProcessoProjeto;
	private WebMarkupContainer containerNumPHSAprovado;
	private InputField         txtNumeroPHSAprovado;
	private InputField         txtNumeroProjetoUrbanistico;
	private InputField         txtNumeroLicitacaoAmbiental;
	private InputField         txtNumeroProjetoEsgoto;
	private InputField         txtNumeroProjetoAgua;
	private WebMarkupContainer containerParcelamentoSolo;
	private AbstractAjaxButton btnAdicionarTipoProjeto;
	private AjaxPreviewBlank   ajaxPreviewBlank;
	private Estabelecimento    estabelecimento;
	private Label              lbl;
	private String             msg;
	
	public RequerimentoVistoriaHidrossanitarioExternoPage (
			TipoSolicitacao tipoSolicitacao,
			Class clazz
	) {
		this.tipoSolicitacao = tipoSolicitacao;
		init(true);
		this.classReturn = clazz;
	}
	
	public RequerimentoVistoriaHidrossanitarioExternoPage (
			TipoSolicitacao tipoSolicitacao,
			Estabelecimento estabelecimento,
			Class clazz
	) {
		this.tipoSolicitacao = tipoSolicitacao;
		this.estabelecimento = estabelecimento;
		init(true);
		this.classReturn = clazz;
	}
	
	public RequerimentoVistoriaHidrossanitarioExternoPage (
			RequerimentoVigilancia requerimentoVigilancia,
			boolean editMode,
			Class clazz
	) {
		this.requerimentoVigilancia = requerimentoVigilancia;
		carregarRequerimentoVistoriaHidrossanitario(requerimentoVigilancia);
		if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao()) ||
				RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())) {
			init(editMode);
		} else {
			init(false);
		}
		this.classReturn = clazz;
	}
	
	private void init (boolean editMode) {
		this.editMode = editMode;
		try {
			configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
		} catch (ValidacaoException e) {
			Loggable.vigilancia.error(e.getMessage(), e);
		}
		
		if (this.requerimentoVigilancia != null) {
			info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
		}
		
		RequerimentoVistoriaHidrossanitarioDTO proxy = on(RequerimentoVistoriaHidrossanitarioDTO.class);
		
		form = new Form("form", new CompoundPropertyModel(new RequerimentoVistoriaHidrossanitarioDTO()));
		if (requerimentoVistoriaHidrossanitario != null) {
			form.getModel().getObject().setRequerimentoVistoriaHidrossanitario(requerimentoVistoriaHidrossanitario);
			
			carregarTipoProjetoRequerimentoVigilanciaList(requerimentoVigilancia);
			carregarEloRequerimentoVigilanciaResponsavelTecnico(requerimentoVigilancia);
			carregarInscricoesImob(requerimentoVigilancia);
			
		} else {
			form.getModel()
				.getObject()
				.setRequerimentoVistoriaHidrossanitario(new RequerimentoVistoriaHidrossanitario());
			form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.setRequerimentoVigilancia(new RequerimentoVigilancia());
		}

		criarMensagensAndPdfView();
		criarDadosProprietario(proxy);
		addCamposEndereco(proxy);
		
		criarNumeroProtocolo(proxy);
		
		criarTipoProjeto();
		criarNumeroProjetoAprovado(proxy);
		
		criarEnderecoObra(proxy);
		criarInscricaoImobiliaria();
		criarResponsavelTecnico();
		criarContainerInformacoes(proxy);
		
		criarTipoEnquadramento(proxy);
		
		criarUsoEdificacao(proxy);
		
		criarParcelamentoDoSolo(proxy);
		criarMetragem(proxy);

		criarDadosSolicitante(proxy);

		criarAnexos();

		criarFiscais();
		criarOcorrencias();
		
		criarCheckboxTermoAceite(proxy);
		criarBtnVoltarSalvar();

		add(form);
		
		if (getFormRVH().getCodigo() == null) {
			try {
				if (configuracaoVigilancia == null) {
					throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
				}
			} catch (ValidacaoException ex) {
				warn(ex);
			}
		}
		
		configurarEstabelecimento();
		enableCamposDadosGeral(null, false);
		habilitarUsoEdificacao(null);
		habilitarParcelamentoSolo(null);
		habilitarMetragem(null);
		
		configurarDadosSolicitante();
		
		habilitarNumeroPHSAprovado();
		habilitarParcelamentoSolo(null);
		vigilanciaAltoBaixoRisco(null);
	}

	private void criarMensagensAndPdfView() {
		lbl = new Label("msg", msg);
		lbl.setVisible(false);
		lbl.setOutputMarkupPlaceholderTag(true);
		form.add(lbl);
		form.add(ajaxPreviewBlank = new AjaxPreviewBlank());
	}

	private void criarDadosProprietario(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		form.add(containerDadosGerais = new WebMarkupContainer("containerDadosGerais"));
		containerDadosGerais.setEnabled(
				getFormRVH().getCodigo() == null);
		containerDadosGerais.setOutputMarkupId(true);
		containerDadosGerais.add(getDropDownTipoPessoa(proxy));
		containerDadosGerais.add(getContainerPessoa(proxy));
		containerDadosGerais.add(getContainerEstabelecimento(proxy));
	}

	private void addCamposEndereco (RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerDadosGerais.add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco(
				path(proxy.getRequerimentoVistoriaHidrossanitario()
						.getRequerimentoVigilancia()
						.getVigilanciaEndereco()), true));
		autoCompleteConsultaVigilanciaEndereco.setLabel(new Model(bundle("endereco")));
		autoCompleteConsultaVigilanciaEndereco.setEnabled(false);
	}

	private void criarNumeroProtocolo(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		form.add(new DisabledInputField<String>(path(proxy.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.getProtocoloFormatado())));
	}
	
	private void criarContainerInformacoes (RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerInformacoes = new WebMarkupContainer("containerInformacoes");
		containerInformacoes.setOutputMarkupId(true);
		
		descricaoClassificaoRisco = new DisabledInputField(path(proxy.getDescricaoClassificacaoRisco()));
		descricaoClassificaoRisco.setOutputMarkupPlaceholderTag(true);
		descricaoClassificaoRisco.setOutputMarkupId(true);
		
		txtNumeroProcessoProjeto = new RequiredInputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getNumeroProcessoProjeto()));
		txtNumeroProcessoProjeto.setEnabled(this.editMode);
		txtNumeroProcessoProjeto.setLabel(new Model(bundle("numeroProcessoProjetoArquitetonicoJuntoSMDU")));
		txtNumeroProcessoProjeto.addAjaxUpdateValue();
		txtNumeroProcessoProjeto.setOutputMarkupPlaceholderTag(true);
		
		ddRegiaoCobertaRedeEsgoto = (DropDown) DropDownUtil.getNaoSimLongDropDown(
																   path(proxy.getRequerimentoVistoriaHidrossanitario().getRegiaoCobertaRedeEsgoto()), true, true)
														   .setLabel(new Model(bundle("regiaoCobertaPorRedeEsgoto")))
														   .setEnabled(this.editMode);
		ddRegiaoCobertaRedeEsgoto.setLabel(new Model(bundle("regiaoCobertaPorRedeEsgoto"))).setEnabled(this.editMode);
		ddRegiaoCobertaRedeEsgoto.add(new AjaxFormComponentUpdatingBehavior("onchange") {
			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				habilitarParcelamentoSolo(target);
				vigilanciaAltoBaixoRisco(target);
			}
		});
		
		ddRegiaoAbastecidaAgua = (DropDown) DropDownUtil.getNaoSimLongDropDown(
																path(proxy.getRequerimentoVistoriaHidrossanitario().getRegiaoAbastecidaAgua()), true, true)
														.setLabel(new Model(bundle("regiaoAbastecidaPorAguaPotavel")))
														.setEnabled(this.editMode);
		ddRegiaoAbastecidaAgua.add(new AjaxFormComponentUpdatingBehavior("onchange") {
			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				vigilanciaAltoBaixoRisco(target);
			}
		});
		ddRegiaoAbastecidaAgua.setOutputMarkupPlaceholderTag(true);
		ddRegiaoAbastecidaAgua.addAjaxUpdateValue();
		
		ddSistemaAprovAguasPluviais = (DropDown) DropDownUtil.getNaoSimLongDropDown(
																	 path(proxy.getRequerimentoVistoriaHidrossanitario().getSistemaAguaPluvial()), true, false)
															 .setLabel(new Model(
																	 bundle("sistemaAproveitamentoAguasPluviais")))
															 .setEnabled(this.editMode);
		ddSistemaAprovAguasPluviais.setRequired(false);
		
		ddUnifamiliar = (DropDown) DropDownUtil.getNaoSimLongDropDown(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getEdificacaoExclusivamenteUnifamiliar()), true,
				true).setLabel(new Model(bundle("edificacaoExclusivamenteUnifamiliar"))).setEnabled(editMode);
		ddUnifamiliar.add(new AjaxFormComponentUpdatingBehavior("onchange") {
			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				vigilanciaAltoBaixoRisco(target);
			}
		});
		ddUnifamiliar.setOutputMarkupPlaceholderTag(true);
		ddUnifamiliar.addAjaxUpdateValue();
		
		ddLincenciavelAmbiental = (DropDown) DropDownUtil.getNaoSimLongDropDown(
																 path(proxy.getRequerimentoVistoriaHidrossanitario().getProjetoLicenciavelOrgaoAmbiental()), true, true)
														 .setLabel(
																 new Model(bundle("projetoLicenciavelOrgaoAmbiental")))
														 .setEnabled(editMode);
		ddLincenciavelAmbiental.add(new AjaxFormComponentUpdatingBehavior("onchange") {
			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				vigilanciaAltoBaixoRisco(target);
			}
		});
		ddLincenciavelAmbiental.setOutputMarkupPlaceholderTag(true);
		ddLincenciavelAmbiental.addAjaxUpdateValue();
		
		containerInformacoes.add(descricaoClassificaoRisco, txtNumeroProcessoProjeto, ddRegiaoCobertaRedeEsgoto,
								 ddRegiaoAbastecidaAgua, ddSistemaAprovAguasPluviais, ddUnifamiliar,
								 ddLincenciavelAmbiental);
		containerDadosObra.add(containerInformacoes);
	}

	private void criarTipoEnquadramento(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerTipoEnquadramentoProjeto = new WebMarkupContainer("containerTipoEnquadramentoProjeto");
		containerTipoEnquadramentoProjeto.setOutputMarkupId(true);
		containerDadosObra.add(containerTipoEnquadramentoProjeto);

		containerTipoEnquadramentoProjeto.add(
						autoCompleteConsultaTipoEnquadramentoProjeto = new AutoCompleteConsultaTipoEnquadramentoProjeto(
								path(proxy.getRequerimentoVistoriaHidrossanitario().getTipoEnquadramentoProjeto()), true))
				.setEnabled(this.editMode);
		autoCompleteConsultaTipoEnquadramentoProjeto.setLabel(Model.of(bundle("tipoEnquadramentoProjeto")));

		autoCompleteConsultaTipoEnquadramentoProjeto.add(new ConsultaListener<TipoEnquadramentoProjeto>() {
			@Override
			public void valueObjectLoaded (
					AjaxRequestTarget target,
					TipoEnquadramentoProjeto tipoEnquadramentoProjeto
			) {
				if (tipoEnquadramentoProjeto.getDescricao().equalsIgnoreCase("Substituição de Projeto")) {
					containerNumPHSAprovado.setVisible(true);
					txtNumeroPHSAprovado.limpar(target);
					target.add(containerNumPHSAprovado);
					target.add(txtNumeroPHSAprovado);
				}
			}
		});

		autoCompleteConsultaTipoEnquadramentoProjeto.add(new RemoveListener<TipoEnquadramentoProjeto>() {
			@Override
			public void valueObjectUnLoaded (
					AjaxRequestTarget target,
					TipoEnquadramentoProjeto tipoEnquadramentoProjeto
			) {
				containerNumPHSAprovado.setVisible(false);
				txtNumeroPHSAprovado.limpar(target);
				target.add(containerNumPHSAprovado);
				target.add(txtNumeroPHSAprovado);
			}
		});

		containerNumPHSAprovado = new WebMarkupContainer("containerNumPHSAprovado");
		containerNumPHSAprovado.setOutputMarkupId(true);
		containerNumPHSAprovado.setOutputMarkupPlaceholderTag(true);
		containerNumPHSAprovado.getAjaxRegionMarkupId();
		containerTipoEnquadramentoProjeto.add(containerNumPHSAprovado);
		containerNumPHSAprovado.add(txtNumeroPHSAprovado = new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getNumeroProjetoHSAprovado())));
	}

	private void criarUsoEdificacao(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerDadosObra.add(ddUsoEdificacao = (DropDown) DropDownUtil.getIEnumDropDown(
						path(proxy.getRequerimentoVistoriaHidrossanitario().getUsoEdificacao()),
                        RequerimentosProjetosEnums.UsoEdificacao.values(), true, "", true, false, true)
				.setEnabled(this.editMode));
		containerDadosObra.add(txtObservacaoUsoEdificacao = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getObservacaoUsoEdificacao())).setEnabled(
				this.editMode));
		txtObservacaoUsoEdificacao.setLabel(Model.of(bundle("outros")));
		txtObservacaoUsoEdificacao.setEnabled(this.editMode);

		ddUsoEdificacao.addRequiredClass();

		ddUsoEdificacao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				habilitarUsoEdificacao(target);
				habilitarParcelamentoSolo(target);
				habilitarMetragem(target);
				target.appendJavaScript(JScript.initMasks());
			}
		});
	}

	private void criarParcelamentoDoSolo(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerParcelamentoSolo = new WebMarkupContainer("containerParcelamentoSolo");
		containerParcelamentoSolo.setOutputMarkupId(true);
		containerParcelamentoSolo.setOutputMarkupPlaceholderTag(true);
		containerParcelamentoSolo.getAjaxRegionMarkupId();
		containerParcelamentoSolo.setEnabled(this.editMode);

		containerParcelamentoSolo.add(txtNumeroLotesParcelamento = (LongField) new LongField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getParcelamentoSoloNumeroLotes())).setEnabled(
				this.editMode));
		txtNumeroLotesParcelamento.setVMax(99999L);
		txtNumeroLotesParcelamento.setLabel(Model.of(bundle("nrLotes")));

		containerParcelamentoSolo.add(txtNumeroProjetoUrbanistico = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getNumeroProjetoUrbanistico())).setEnabled(
				this.editMode));
		containerParcelamentoSolo.add(txtNumeroLicitacaoAmbiental = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getNumeroLicitacaoAmbiental())).setEnabled(
				this.editMode));
		containerParcelamentoSolo.add(txtNumeroProjetoEsgoto = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getNumeroProjetoEsgoto())).setEnabled(
				this.editMode));
		containerParcelamentoSolo.add(txtNumeroProjetoAgua = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getNumeroProjetoAgua())).setEnabled(this.editMode));

		txtNumeroProjetoUrbanistico.setLabel(new Model(bundle("numeroProjetoUrbanismo")));
		txtNumeroLicitacaoAmbiental.setLabel(new Model(bundle("numeroLicencaAmbientalLAO")));
		txtNumeroProjetoEsgoto.setLabel(new Model(bundle("numeroProjetoEsgoto")));
		txtNumeroProjetoAgua.setLabel(new Model(bundle("numeroProjetoAgua")));

		containerDadosObra.add(containerParcelamentoSolo);
	}

	private void criarMetragem(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerDadosObra.add(txtAreaComercial = new RequiredDoubleField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getAreaComercial())));
		txtAreaComercial.setMDec(4).addAjaxUpdateValue();
		txtAreaComercial.setVMax(99999.9999);
		txtAreaComercial.setEnabled(this.editMode);
		txtAreaComercial.setLabel(Model.of(bundle("areaComercial")));

		txtAreaComercial.add(new AjaxFormComponentUpdatingBehavior("onchange") {

			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				validarAreaTotalConstrucao(target);
			}
		});

		containerDadosObra.add(txtAreaResidencial = new RequiredDoubleField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getAreaResidencial())));
		txtAreaResidencial.setMDec(4).addAjaxUpdateValue();
		txtAreaResidencial.setVMax(99999.9999);
		txtAreaResidencial.setEnabled(this.editMode);
		txtAreaResidencial.setLabel(Model.of(bundle("areaResidencial")));

		txtAreaResidencial.add(new AjaxFormComponentUpdatingBehavior("onchange") {

			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				validarAreaTotalConstrucao(target);
			}
		});

		containerDadosObra.add(txtAreaTotal = new DoubleField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getAreaTotalConstrucao())));
		txtAreaTotal.setMDec(4).addAjaxUpdateValue();
		txtAreaTotal.setVMax(999999.9999);
		txtAreaTotal.setEnabled(false);
		txtAreaTotal.setLabel(Model.of(bundle("areaTotalConstrucao")));
	}

	private void criarDadosSolicitante(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		form.add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia(),
				this.editMode));
	}

	private void criarAnexos() {
		PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
		dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
		dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
		pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, this.editMode, false,
				false, false,
				"Anexos - Inserir documentos do processo");
		pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
		form.add(pnlRequerimentoVigilanciaAnexo);
	}

	private void criarFiscais() {
		form.add(new RequerimentoVigilanciaFiscaisPanel("fiscaisRequerimento", requerimentoVigilancia));
	}

	private void criarOcorrencias() {
		form.add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.getCodigo(), true).setVisible(!this.editMode));
	}
	
	private void vigilanciaAltoBaixoRisco (AjaxRequestTarget target) {
		
		String styleSemRisco   = "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #F2F2F2";
		String styleRiscoBaixo = "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #2AABD2";
		String styleRiscoAlto  = "vertical-align: bottom; height: 25px; text-align: center; margin-left: 18px; font-weight: 700; background: #E17373";
		
		if (allInfoDropsContainValues()) {
			if (isAltoRisco()) {
				descricaoClassificaoRisco.add(new AttributeModifier("style", styleRiscoAlto));
                getFormRVH().setClassificacaoRisco(RequerimentosProjetosEnums.ClassificacaoRisco.ALTO_RISCO.value());
				descricaoClassificaoRisco.setComponentValue(
                        RequerimentosProjetosEnums.ClassificacaoRisco.getValue(getFormRVH().getClassificacaoRisco()).descricao()
				);

			} else if (isBaixoRisco()) {
				descricaoClassificaoRisco.add(new AttributeModifier("style", styleRiscoBaixo));
                getFormRVH().setClassificacaoRisco(RequerimentosProjetosEnums.ClassificacaoRisco.BAIXO_RISCO.value());
				descricaoClassificaoRisco.setComponentValue(
                        RequerimentosProjetosEnums.ClassificacaoRisco.getValue(getFormRVH().getClassificacaoRisco()).descricao());

				if (target != null) {
					MessageUtil.modalWarn(target, descricaoClassificaoRisco,
										  new ValidacaoException(bundle("msgClassificacaoProjetoBaixoRisco")));
				}

			} else {
				descricaoClassificaoRisco.add(new AttributeModifier("style", styleSemRisco));
				descricaoClassificaoRisco.setComponentValue("");
			}

		} else {
			descricaoClassificaoRisco.add(new AttributeModifier("style", styleSemRisco));
			descricaoClassificaoRisco.setComponentValue("");
		}
		
		if (target != null) {
			target.add(descricaoClassificaoRisco);
		}
	}

	public boolean allInfoDropsContainValues () {
		List<DropDown> dds = Arrays.asList(ddUsoEdificacao,
				ddRegiaoCobertaRedeEsgoto,
				ddRegiaoAbastecidaAgua,
				ddUnifamiliar,
				ddLincenciavelAmbiental);

		for (DropDown dd : dds) {
			if (isDDEmpty(dd)) {
				return false;
			}
		}
		return true;
	}

	public boolean isDDEmpty(DropDown dd) {
		return (dd == null || (dd != null && dd.getComponentValue() == null) || (dd != null && dd.getComponentValue() == ""));
	}

	public boolean isBaixoRisco () {

        if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamentoOrCondominio(getFormRVH().getUsoEdificacao())) {
			return isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua);
		} else {
			return (
					(isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
							(isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
							(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
							(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongSim(ddUnifamiliar)) ||
							(isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar)) ||
							(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongSim(ddLincenciavelAmbiental))
			);
		}

	}

	public boolean isAltoRisco () {

        if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamentoOrCondominio(getFormRVH().getUsoEdificacao())) {
			return (
					(isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua)) ||
							(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua)) ||
							(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua))
			);
		} else {
			return (
					(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongSim(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongNao(ddLincenciavelAmbiental)) ||
							(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongNao(ddLincenciavelAmbiental)) ||
							(isLongNao(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongSim(ddLincenciavelAmbiental)) ||
							(isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongNao(ddLincenciavelAmbiental)) ||
							(isLongSim(ddRegiaoCobertaRedeEsgoto) && isLongNao(ddRegiaoAbastecidaAgua) && isLongNao(ddUnifamiliar) && isLongSim(ddLincenciavelAmbiental))

			);
		}
	}

	private boolean isLongSim (DropDown dd) {
		return (dd != null && dd.getComponentValue() != null &&
				RepositoryComponentDefault.SIM_LONG.equals(dd.getComponentValue()));
	}

	private boolean isLongNao (DropDown dd) {
		return (dd != null && dd.getComponentValue() != null &&
				RepositoryComponentDefault.NAO_LONG.equals(dd.getComponentValue()));
	}


	private void habilitarNumeroPHSAprovado () {
		if (requerimentoVistoriaHidrossanitario == null || Coalesce.asString(
				requerimentoVistoriaHidrossanitario.getNumeroProcessoProjeto()).isEmpty()) {
			containerNumPHSAprovado.setVisible(false);
		} else {
			containerNumPHSAprovado.setVisible(true);
		}
	}
	
	private void criarResponsavelTecnico () {
		autoCompleteConsultaResponsavelTecnico = new AutoCompleteConsultaResponsavelTecnico("responsavelTecnico",
																							new PropertyModel(this,
																											  "responsavelTecnico"));
		autoCompleteConsultaResponsavelTecnico.setEnabled(this.editMode);
		autoCompleteConsultaResponsavelTecnico.setOutputMarkupId(true);
		autoCompleteConsultaResponsavelTecnico.setLabel(new Model(Bundle.getStringApplication("setorResponsavel")));
		
		if (form.getModelObject().getListResponsavelTecnico().size() == 0) {
			autoCompleteConsultaResponsavelTecnico.getTxtDescricao().addRequiredClass();
			autoCompleteConsultaResponsavelTecnico.setRequired(true);
		}
		
		btnCadadastroResponsavel = new AbstractAjaxLink("btnCadadastroResponsavel") {
			@Override
			public void onAction (AjaxRequestTarget target) throws ValidacaoException, DAOException {
				cadastrarResponsavel(target);
			}
		};
		btnCadadastroResponsavel.setEnabled(this.editMode);
		btnCadadastroResponsavel.getAjaxRegionMarkupId();
		btnCadadastroResponsavel.setOutputMarkupPlaceholderTag(true);
		
		btnAdicionarResponsavelTecnico = new AbstractAjaxButton("btnAdicionarResponsavelTecnico") {
			@Override
			public void onAction (
					AjaxRequestTarget target,
					Form form
			) throws ValidacaoException, DAOException {
				adicionarResponsavelTecnico(target);
			}
		};
		btnAdicionarResponsavelTecnico.setEnabled(this.editMode);
		btnAdicionarResponsavelTecnico.getAjaxRegionMarkupId();
		btnAdicionarResponsavelTecnico.setOutputMarkupPlaceholderTag(true);
		btnAdicionarResponsavelTecnico.setDefaultFormProcessing(false);
		
		tblResponsavelTecnico = new Table("tblResponsavelTecnico", getColumnsResponsavelTecnico(),
										  getCollectionProviderResponsavelTecnico());
		tblResponsavelTecnico.setScrollY("180px");
		tblResponsavelTecnico.populate();
		tblResponsavelTecnico.setOutputMarkupPlaceholderTag(true);
		tblResponsavelTecnico.setOutputMarkupId(true);
		tblResponsavelTecnico.getAjaxRegionMarkupId();
		tblResponsavelTecnico.setEnabled(this.editMode);
		
		containerDadosObra.add(autoCompleteConsultaResponsavelTecnico, btnCadadastroResponsavel,
							   btnAdicionarResponsavelTecnico, tblResponsavelTecnico);
	}
	
	private void adicionarResponsavelTecnico (AjaxRequestTarget target) throws ValidacaoException {
		if (responsavelTecnico == null) {
			throw new ValidacaoException(BundleManager.getString("informeResponsavelTecnico"));
		}
		
		if (CollectionUtils.isNotNullEmpty(
				form.getModel().getObject().getListResponsavelTecnico())) {
			for (EloRequerimentoVigilanciaResponsavelTecnico elo : form.getModel()
																	   .getObject()
																	   .getListResponsavelTecnico()) {
				if (elo.getResponsavelTecnico().getCodigo().equals(responsavelTecnico.getCodigo())) {
					throw new ValidacaoException(BundleManager.getString("responsavelTecnicoJaAdicionado"));
				}
			}
		}
		
		EloRequerimentoVigilanciaResponsavelTecnico elo = new EloRequerimentoVigilanciaResponsavelTecnico();
		elo.setResponsavelTecnico(responsavelTecnico);
		form.getModel().getObject().getListResponsavelTecnico().add(elo);
		
		if (form.getModel().getObject().getListResponsavelTecnico().size() > 0) {
			autoCompleteConsultaResponsavelTecnico.setRequired(false);
			autoCompleteConsultaResponsavelTecnico.getTxtDescricao().removeRequiredClass();
			target.add(autoCompleteConsultaResponsavelTecnico);
		}
		
		tblResponsavelTecnico.update(target);
		tblResponsavelTecnico.populate();
		responsavelTecnico = null;
		autoCompleteConsultaResponsavelTecnico.limpar(target);
	}
	
	private void cadastrarResponsavel (AjaxRequestTarget target) {
		if (dlgCadastroResponsavelTecnico == null) {
			addModal(target, dlgCadastroResponsavelTecnico = new DlgCadastroResponsavelTecnico(newModalId()));
			dlgCadastroResponsavelTecnico.add(new ICadastroListener<ResponsavelTecnico>() {
				@Override
				public void onSalvar (
						AjaxRequestTarget target,
						ResponsavelTecnico responsavelTecnico
				) throws ValidacaoException, DAOException {
					autoCompleteConsultaResponsavelTecnico.limpar(target);
					autoCompleteConsultaResponsavelTecnico.setComponentValue(responsavelTecnico);
					target.add(autoCompleteConsultaResponsavelTecnico);
				}
			});
		}
		
		dlgCadastroResponsavelTecnico.limpar(target);
		dlgCadastroResponsavelTecnico.show(target);
	}
	
	private void removerResponsavelTecnico (
			AjaxRequestTarget target,
			EloRequerimentoVigilanciaResponsavelTecnico rowObject
	) throws ValidacaoException, DAOException {
		for (int i = 0; i < form.getModel()
								.getObject()
								.getListResponsavelTecnico()
								.size(); i++) {
			EloRequerimentoVigilanciaResponsavelTecnico elo = form.getModel()
																  .getObject()
																  .getListResponsavelTecnico()
																  .get(i);
			if (elo == rowObject) {
				if (elo.getCodigo() != null) {
					form.getModel().getObject().getListResponsavelTecnicoExcluidos().add(elo);
				}
				form.getModel().getObject().getListResponsavelTecnico().remove(i);
			}
		}
		
		if (form.getModel().getObject().getListResponsavelTecnico().size() == 0) {
			autoCompleteConsultaResponsavelTecnico.setRequired(true);
			autoCompleteConsultaResponsavelTecnico.getTxtDescricao().addRequiredClass();
			target.add(autoCompleteConsultaResponsavelTecnico);
		}
		
		tblResponsavelTecnico.populate();
		tblResponsavelTecnico.update(target);
		
		CrudUtils.removerItem(target, tblResponsavelTecnico,
							  form.getModel().getObject().getListResponsavelTecnico(),
							  rowObject);
	}
	
	private List<IColumn> getColumnsResponsavelTecnico () {
		ColumnFactory                               columnFactory = new ColumnFactory(
				EloRequerimentoVigilanciaResponsavelTecnico.class);
		List<IColumn>                               columns       = new ArrayList<>();
		EloRequerimentoVigilanciaResponsavelTecnico proxy         = on(
				EloRequerimentoVigilanciaResponsavelTecnico.class);
		
		columns.add(getCustomColumnResponsavelTecnico());
		columns.add(columnFactory.createColumn(BundleManager.getString("nome"),
											   path(proxy.getResponsavelTecnico().getNome())));
		columns.add(columnFactory.createColumn(BundleManager.getString("cpf"),
											   path(proxy.getResponsavelTecnico().getCpfFormatado())));
		columns.add(columnFactory.createColumn(BundleManager.getString("registro"),
											   path(proxy.getResponsavelTecnico().getDescricaoRegistro())));
		
		return columns;
	}
	
	private ICollectionProvider getCollectionProviderResponsavelTecnico () {
		return new CollectionProvider() {
			
			@Override
			public Collection getCollection (Object param) throws DAOException, ValidacaoException {
				return form.getModelObject().getListResponsavelTecnico();
			}
		};
	}
	
	private IColumn getCustomColumnResponsavelTecnico () {
		return new MultipleActionCustomColumn<EloRequerimentoVigilanciaResponsavelTecnico>() {
			@Override
			public void customizeColumn (final EloRequerimentoVigilanciaResponsavelTecnico rowObject) {
				addAction(ActionType.REMOVER, new IAction() {
					@Override
					public void action (AjaxRequestTarget target) throws ValidacaoException, DAOException {
						removerResponsavelTecnico(target, rowObject);
					}
				});
			}
		};
	}
	
	private void criarInscricaoImobiliaria () {
		txtInscricaoImobiliaria = new InputField("numeroInscricaoImobiliaria",
												 new PropertyModel(this, "numeroInscricaoImobiliaria"));
		txtInscricaoImobiliaria.setLabel(Model.of(bundle("nInscricaoImobiliaria")));
		txtInscricaoImobiliaria.setEnabled(this.editMode);
		txtInscricaoImobiliaria.addAjaxUpdateValue();
		
		if (form.getModelObject().getListInscricaoImobiliaria().size() == 0) {
			txtInscricaoImobiliaria.setRequired(true);
			txtInscricaoImobiliaria.addRequiredClass();
		}
		
		tblInscricaoImobiliaria = new Table("tblInscricaoImobiliaria", getColumnsInscricaoImobiliaria(),
											getCollectionProviderInscricaoImobiliaria());
		tblInscricaoImobiliaria.setScrollY("180px");
		tblInscricaoImobiliaria.populate();
		tblInscricaoImobiliaria.setOutputMarkupPlaceholderTag(true);
		tblInscricaoImobiliaria.setOutputMarkupId(true);
		tblInscricaoImobiliaria.getAjaxRegionMarkupId();
		tblInscricaoImobiliaria.setEnabled(this.editMode);
		
		btnAdicionarInscricao = new AbstractAjaxButton("btnAdicionarInscricao") {
			@Override
			public void onAction (
					AjaxRequestTarget target,
					Form form
			) throws ValidacaoException, DAOException {
				adicionarInscricaoImobiliaria(target);
			}
		};
		btnAdicionarInscricao.setDefaultFormProcessing(false);
		btnAdicionarInscricao.setEnabled(this.editMode);
		
		containerDadosObra.add(tblInscricaoImobiliaria, btnAdicionarInscricao, txtInscricaoImobiliaria);
	}
	
	private void adicionarInscricaoImobiliaria (AjaxRequestTarget target) throws ValidacaoException {
		if (txtInscricaoImobiliaria.getComponentValue() == null) {
			throw new ValidacaoException(BundleManager.getString("informeNumeroInscricaoImobiliaria"));
		}
		
		if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getListInscricaoImobiliaria())) {
			for (RequerimentoVigilanciaInscricaoImob insc : form.getModel()
																.getObject()
																.getListInscricaoImobiliaria()) {
				if (insc.getNumeroInscricaoImobiliaria().equals(txtInscricaoImobiliaria.getComponentValue())) {
					throw new ValidacaoException(BundleManager.getString("inscricaoImobiliariaJaAdicionada"));
				}
			}
		}
		
		RequerimentoVigilanciaInscricaoImob inscricao = new RequerimentoVigilanciaInscricaoImob();
		inscricao.setNumeroInscricaoImobiliaria(txtInscricaoImobiliaria.getComponentValue());
		form.getModel().getObject().getListInscricaoImobiliaria().add(inscricao);
		
		if (form.getModel().getObject().getListInscricaoImobiliaria().size() > 0) {
			txtInscricaoImobiliaria.setRequired(false);
			txtInscricaoImobiliaria.removeRequiredClass();
			target.add(txtInscricaoImobiliaria);
		}
		
		tblInscricaoImobiliaria.update(target);
		tblInscricaoImobiliaria.populate();
		txtInscricaoImobiliaria.limpar(target);
	}
	
	private List<IColumn> getColumnsInscricaoImobiliaria () {
		ColumnFactory                       columnFactory = new ColumnFactory(
				RequerimentoVigilanciaInscricaoImob.class);
		List<IColumn>                       columns       = new ArrayList<>();
		RequerimentoVigilanciaInscricaoImob proxy         = on(RequerimentoVigilanciaInscricaoImob.class);
		
		columns.add(getCustomColumnInscricaoImobiliaria());
		columns.add(columnFactory.createColumn(BundleManager.getString("inscricaoImobiliaria"),
											   path(proxy.getNumeroInscricaoImobiliaria())));
		
		return columns;
	}
	
	private ICollectionProvider getCollectionProviderInscricaoImobiliaria () {
		return new CollectionProvider() {
			
			@Override
			public Collection getCollection (Object param) throws DAOException, ValidacaoException {
				return form.getModelObject().getListInscricaoImobiliaria();
			}
		};
	}
	
	private IColumn getCustomColumnInscricaoImobiliaria () {
		return new MultipleActionCustomColumn<RequerimentoVigilanciaInscricaoImob>() {
			@Override
			public void customizeColumn (final RequerimentoVigilanciaInscricaoImob rowObject) {
				addAction(ActionType.REMOVER, new IAction() {
					@Override
					public void action (AjaxRequestTarget target) throws ValidacaoException, DAOException {
						removerInscricaoImobiliaria(target, rowObject);
					}
				});
			}
		};
	}
	
	private void removerInscricaoImobiliaria (
			AjaxRequestTarget target,
			RequerimentoVigilanciaInscricaoImob rowObject
	) throws ValidacaoException, DAOException {
		for (int i = 0; i < form.getModel().getObject().getListInscricaoImobiliaria().size(); i++) {
			RequerimentoVigilanciaInscricaoImob insc = form.getModel()
														   .getObject()
														   .getListInscricaoImobiliaria()
														   .get(i);
			if (insc == rowObject) {
				if (insc.getCodigo() != null) {
					form.getModel().getObject().getListInscricaoImobiliariaExcluidos().add(insc);
				}
				form.getModel().getObject().getListInscricaoImobiliaria().remove(i);
			}
		}
		
		if (form.getModel().getObject().getListInscricaoImobiliaria().size() == 0) {
			txtInscricaoImobiliaria.setRequired(true);
			txtInscricaoImobiliaria.addRequiredClass();
			target.add(txtInscricaoImobiliaria);
		}
		
		tblInscricaoImobiliaria.populate();
		tblInscricaoImobiliaria.update(target);
		
		CrudUtils.removerItem(target, tblInscricaoImobiliaria,
							  form.getModel().getObject().getListInscricaoImobiliaria(), rowObject);
	}
	
	private void criarTipoProjeto () {
		TipoProjetoRequerimentoVigilancia proxyTipoProjeto = on(TipoProjetoRequerimentoVigilancia.class);
		containerTipoProjeto = new WebMarkupContainer("containerTipoProjeto",
													  modelTipoProjetoRequerimentoVigilancia = new CompoundPropertyModel<>(
															  new TipoProjetoRequerimentoVigilancia()));
		containerTipoProjeto.setOutputMarkupId(true);
		containerTipoProjeto.setEnabled(this.editMode);
		
		autoCompleteConsultaTipoProjetoVigilancia = new AutoCompleteConsultaTipoProjetoVigilancia(
				path(proxyTipoProjeto.getTipoProjetoVigilancia()));
		autoCompleteConsultaTipoProjetoVigilancia.setOutputMarkupPlaceholderTag(true);
		autoCompleteConsultaTipoProjetoVigilancia.addAjaxUpdateValue();
		autoCompleteConsultaTipoProjetoVigilancia.setLabel(Model.of(bundle("tipoProjeto")));
		autoCompleteConsultaTipoProjetoVigilancia.setTipo(TipoProjetoVigilancia.Tipo.VISTORIA_HABITE_SE.value());
		autoCompleteConsultaTipoProjetoVigilancia.setEnabled(this.editMode);
		
		txtArea = new DoubleField(path(proxyTipoProjeto.getArea()));
		txtArea.setMDec(4).addAjaxUpdateValue();
		txtArea.setVMax(99999.9999);
		txtArea.setEnabled(this.editMode);
		txtArea.setLabel(Model.of(bundle("areaM2")));
		
		if (form.getModelObject().getListTipoProjetos().size() == 0) {
			autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().addRequiredClass();
			autoCompleteConsultaTipoProjetoVigilancia.setRequired(true);
			txtArea.addRequiredClass();
			txtArea.setRequired(true);
		}
		
		btnAdicionarTipoProjeto = new AbstractAjaxButton("btnAdicionarTipoProjeto") {
			@Override
			public void onAction (
					AjaxRequestTarget target,
					Form form
			) throws ValidacaoException, DAOException {
				adicionarTipoProjeto(target);
			}
		};
		btnAdicionarTipoProjeto.setDefaultFormProcessing(false).setEnabled(this.editMode);
		btnAdicionarTipoProjeto.setOutputMarkupPlaceholderTag(true);
		btnAdicionarTipoProjeto.getAjaxRegionMarkupId();
		
		tblTipoProjeto = new Table("tblTipoProjeto", getColumnsTipoProjetoRequerimentoVigilancia(),
								   getCollectionProviderTipoProjetoRequerimentoVigilancia());
		tblTipoProjeto.populate();
		tblTipoProjeto.setScrollY("1800");
		tblTipoProjeto.setEnabled(this.editMode);
		
		containerTipoProjeto.add(autoCompleteConsultaTipoProjetoVigilancia, txtArea, btnAdicionarTipoProjeto,
								 tblTipoProjeto);
		form.add(containerTipoProjeto);
		
		if (getFormRVH().getCodigo() != null) {
			containerTipoProjeto.setEnabled(CollectionUtils.isEmpty(VigilanciaHelper.getVigilanciaFinanceiroList(
					getFormRVH().getRequerimentoVigilancia())));
		}
	}

	private void criarNumeroProjetoAprovado(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		form.add(
				autoCompleteConsultaRequerimentoProjetoHidrossanitario = new AutoCompleteConsultaRequerimentoProjetoHidrossanitario(
						path(proxy.getRequerimentoVistoriaHidrossanitario().getRequerimentoProjetoHidrossanitario())));
		autoCompleteConsultaRequerimentoProjetoHidrossanitario.addAjaxUpdateValue();
		autoCompleteConsultaRequerimentoProjetoHidrossanitario.setSituacao(
				RequerimentoVigilancia.Situacao.DEFERIDO.value());
		autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(editMode);
		autoCompleteConsultaRequerimentoProjetoHidrossanitario.setLabel(
				Model.of(bundle("numeroProjetoHidrossanitarioAprovado")));

		autoCompleteConsultaRequerimentoProjetoHidrossanitario.add(
				new ConsultaListener<RequerimentoProjetoHidrossanitario>() {
					@Override
					public void valueObjectLoaded (
							AjaxRequestTarget target,
							RequerimentoProjetoHidrossanitario object
					) {
						habilitarCampoProjetoHidroManual(target, object, false);
					}
				});
		autoCompleteConsultaRequerimentoProjetoHidrossanitario.add(
				new RemoveListener<RequerimentoProjetoHidrossanitario>() {
					@Override
					public void valueObjectUnLoaded (
							AjaxRequestTarget target,
							RequerimentoProjetoHidrossanitario object
					) {
						habilitarCampoProjetoHidroManual(target, null, false);
					}
				});

		form.add(txtNumeroProjetoHidrossanitario = new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getNumeroProjetoAprovado())));
		txtNumeroProjetoHidrossanitario.setEnabled(editMode);
		txtNumeroProjetoHidrossanitario.setLabel(Model.of(bundle("numeroProjetoHidrossanitarioAprovado")));
	}

	private void criarEnderecoObra(RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerDadosObra = new WebMarkupContainer("containerDadosObra");
		containerDadosObra.setOutputMarkupId(true);
		form.add(containerDadosObra);

		containerDadosObra.add(pnlVigilanciaEndereco = (PnlVigilanciaEndereco) new PnlVigilanciaEndereco(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getVigilanciaEndereco())));
		pnlVigilanciaEndereco.setEnabled(this.editMode);
		pnlVigilanciaEndereco.addRequired();
		pnlVigilanciaEndereco.setRequired(true);
		pnlVigilanciaEndereco.setLabel(new Model(bundle("endereco")));
		pnlVigilanciaEndereco.addAjaxUpdateValue();
		pnlVigilanciaEndereco.setOutputMarkupPlaceholderTag(true);
		containerDadosObra.getAjaxRegionMarkupId();
		containerDadosObra.setOutputMarkupPlaceholderTag(true);

		if (requerimentoVistoriaHidrossanitario != null && requerimentoVistoriaHidrossanitario.getVigilanciaEndereco() != null) {
			pnlVigilanciaEndereco.setModelObject(requerimentoVistoriaHidrossanitario.getVigilanciaEndereco());
		}

		containerDadosObra.add(txtObraNumeroEndereco = (InputField) new RequiredInputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getObraNumeroEndereco())).setLabel(
				new Model(bundle("numeroAbv"))).setEnabled(this.editMode));
		containerDadosObra.add(txtObraQuadra = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getObraQuadra())).setEnabled(this.editMode));
		containerDadosObra.add(txtObraNumeroLado = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getObraNumeroLado())).setEnabled(this.editMode));
		containerDadosObra.add(txtObraLote = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getObraLote())).setEnabled(this.editMode));
		containerDadosObra.add(txtObraComplemento = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getObraComplemento())).setEnabled(this.editMode));
		containerDadosObra.add(txtObraNumeroLoteamento = (InputField) new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getObraNumeroLoteamento())).setEnabled(
				this.editMode));
	}
	
	private void adicionarTipoProjeto (AjaxRequestTarget target) throws ValidacaoException, DAOException {
		TipoProjetoRequerimentoVigilancia tprv = modelTipoProjetoRequerimentoVigilancia.getObject();
		
		if (tprv.getTipoProjetoVigilancia() == null) {
			throw new ValidacaoException(bundle("informeTipoProjeto"));
		}
		
		if (tprv.getArea() == null) {
			throw new ValidacaoException(bundle("informeArea"));
		}
		
		for (TipoProjetoRequerimentoVigilancia item : form.getModel()
														  .getObject()
														  .getListTipoProjetos()) {
			if (item.getTipoProjetoVigilancia().getCodigo().equals(tprv.getTipoProjetoVigilancia().getCodigo())) {
				throw new ValidacaoException(BundleManager.getString("tipoProjetoJaAdicionado"));
			}
		}
		
		if (Coalesce.asDouble(tprv.getArea()) > tprv.getTipoProjetoVigilancia().getMetragemMaxima()) {
			if (Coalesce.asDouble(configuracaoVigilancia.getValorExcedidoAnaliseProjeto()) == 0D) {
				throw new ValidacaoException(
						BundleManager.getString("msgAreaInformadaExcedeMetragemMaximaTipoProjeto"));
			}
		}
		
		form.getModel().getObject().getListTipoProjetos().add(tprv);
		
		if (form.getModel().getObject().getListTipoProjetos().size() > 0) {
			autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().removeRequiredClass();
			autoCompleteConsultaTipoProjetoVigilancia.setRequired(false);
			txtArea.removeRequiredClass();
			txtArea.setRequired(false);
			target.add(autoCompleteConsultaTipoProjetoVigilancia, txtArea);
		}
		
		calcularAreaTotalConstrucao(target);
		
		limparTipoProjeto(target);
		tblTipoProjeto.update(target);
		target.focusComponent(autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().getTextField());
	}
	
	private List<IColumn> getColumnsTipoProjetoRequerimentoVigilancia () {
		List<IColumn>                     columns = new ArrayList<IColumn>();
		TipoProjetoRequerimentoVigilancia proxy   = on(TipoProjetoRequerimentoVigilancia.class);
		
		columns.add(getActionColumnTipoProjetoRequerimentoVigilancia());
		columns.add(createColumn(bundle("tipo"), proxy.getTipoProjetoVigilancia().getDescricao()));
		columns.add(createColumn(bundle("area"), proxy.getArea()));
		
		return columns;
	}
	
	private IColumn getActionColumnTipoProjetoRequerimentoVigilancia () {
		return new MultipleActionCustomColumn<TipoProjetoRequerimentoVigilancia>() {
			@Override
			public void customizeColumn (TipoProjetoRequerimentoVigilancia rowObject) {
				addAction(ActionType.REMOVER, rowObject, new IModelAction<TipoProjetoRequerimentoVigilancia>() {
					@Override
					public void action (
							AjaxRequestTarget target,
							TipoProjetoRequerimentoVigilancia modelObject
					) throws ValidacaoException, DAOException {
						removerTipoProjetoRequerimentoVigilancia(target, modelObject);
					}
				});
			}
		};
	}
	
	private void removerTipoProjetoRequerimentoVigilancia (
			AjaxRequestTarget target,
			TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia
	) {
		limparTipoProjeto(target);
		for (int i = 0; i < form.getModel().getObject().getListTipoProjetos().size(); i++) {
			if (form.getModel()
					.getObject()
					.getListTipoProjetos()
					.get(i) == tipoProjetoRequerimentoVigilancia) {
				if (form.getModel().getObject().getListTipoProjetos().get(i).getCodigo() != null) {
					form.getModel()
						.getObject()
						.getListTiposProjetosExcluidos()
						.add(form.getModel().getObject().getListTipoProjetos().get(i));
				}
				form.getModel().getObject().getListTipoProjetos().remove(i);
				break;
			}
		}
		
		if (form.getModelObject().getListTipoProjetos().size() == 0) {
			autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().addRequiredClass();
			autoCompleteConsultaTipoProjetoVigilancia.setRequired(true);
			txtArea.addRequiredClass();
			txtArea.setRequired(true);
			target.add(autoCompleteConsultaTipoProjetoVigilancia, txtArea);
		}
		
		calcularAreaTotalConstrucao(target);
		tblTipoProjeto.update(target);
	}
	
	private void limparTipoProjeto (AjaxRequestTarget target) {
		modelTipoProjetoRequerimentoVigilancia.setObject(new TipoProjetoRequerimentoVigilancia());
		autoCompleteConsultaTipoProjetoVigilancia.limpar(target);
		txtArea.limpar(target);
	}
	
	private ICollectionProvider getCollectionProviderTipoProjetoRequerimentoVigilancia () {
		return new CollectionProvider() {
			@Override
			public Collection getCollection (Object param) throws DAOException, ValidacaoException {
				return form.getModel().getObject().getListTipoProjetos();
			}
		};
	}
	
	private void habilitarCampoProjetoHidroManual (
			AjaxRequestTarget target,
			RequerimentoProjetoHidrossanitario requerimentoProjetoHidrossanitario,
			boolean limparCampoProjetoHidrossanitario
	) {
		if (target != null) {
			if (limparCampoProjetoHidrossanitario) {
				autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
			}
			
			// Grupo Tipo do Projeto
			autoCompleteConsultaTipoProjetoVigilancia.limpar(target);
			txtArea.limpar(target);
			
			// Grupo Dados da Obra
			txtObraNumeroEndereco.limpar(target);
			txtObraQuadra.limpar(target);
			txtObraNumeroLado.limpar(target);
			txtObraLote.limpar(target);
			txtObraComplemento.limpar(target);
			txtObraNumeroLoteamento.limpar(target);
			
			// Grupo Informações
			txtNumeroProcessoProjeto.limpar(target);
			ddRegiaoCobertaRedeEsgoto.limpar(target);
			ddRegiaoAbastecidaAgua.limpar(target);
			ddSistemaAprovAguasPluviais.limpar(target);
			
			// Grupo Enquadramento
			autoCompleteConsultaTipoEnquadramentoProjeto.limpar(target);
			
			// Grupo Uso da Edificação
			ddUsoEdificacao.limpar(target);
			txtObservacaoUsoEdificacao.limpar(target);
			
			// Grupo Parcelamento do Solo
			txtNumeroLotesParcelamento.limpar(target);
			
			// Grupo Metragem
			txtAreaComercial.limpar(target);
			txtAreaResidencial.limpar(target);
			txtAreaTotal.limpar(target);
			
			pnlVigilanciaEndereco.limpar(target);
		}
		
		// Grupo Fiscais
		//pnlDadosComumRequerimentoVigilancia.getParam().getRequerimentoVigilanciaFiscalList().clear();
		
		if (requerimentoProjetoHidrossanitario != null) {
			// Grupo Tipo do Projeto
			//            autoCompleteConsultaTipoProjetoVigilancia.setComponentValue(requerimentoProjetoHidrossanitario.getTipoProjetoVigilancia());
			//            txtArea.setComponentValue(requerimentoProjetoHidrossanitario.getArea());
			carregarTipoProjetoRequerimentoVigilanciaList(
					requerimentoProjetoHidrossanitario.getRequerimentoVigilancia());
			tblTipoProjeto.populate();
			//txtNumeroPHSAprovado.setRequired(true);
			
			if (CollectionUtils.isNotNullEmpty(
					form.getModel().getObject().getListTipoProjetos())) {
				boolean       contains;
				StringBuilder builder = new StringBuilder();
				for (TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia : form.getModel()
																							   .getObject()
																							   .getListTipoProjetos()) {
					contains = Valor.resolveSomatorio(
											tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getTipo())
									.contains(TipoProjetoVigilancia.Tipo.VISTORIA_HABITE_SE.value());
					if (!contains) {
						builder.append(
								"O Tipo de Projeto '" + tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia()
																						 .getDescricao() + "' do Projeto Hidrossanitário Aprovado não está habilitado para o Habite-se. Aréa: " + tipoProjetoRequerimentoVigilancia.getArea());
						tipoProjetoRequerimentoVigilancia.setTipoProjetoVigilancia(null);
						
						//                        try {
						//                            throw new ValidacaoException("O Tipo de Projeto '" + tipoProjetoRequerimentoVigilancia.getTipoProjetoVigilancia().getDescricao() + "' do Projeto Hidrossanitário Aprovado não está habilitado para o Habite-se");
						//                        } catch (ValidacaoException e) {
						//                            if (target != null) {
						//                                modalWarn(target, e);
						//                                autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
						//                                form.getModel().getObject().getTipoProjetoRequerimentoVigilanciaList().clear();
						//                                tblTipoProjeto.update(target);
						//                            }
						//                            return;
						//                        }
					}
				}
				if (StringUtils.trimToNull(builder.toString()) != null) {
					if (target != null) {
						info(target, builder.toString());
					}
					form.getModel().getObject().getListTipoProjetos().clear();
					if (target != null) {
						tblTipoProjeto.populate(target);
					}
				}
			}
			
			if (CollectionUtils.isNotNullEmpty(
					form.getModel().getObject().getListTipoProjetos())) {
				List<TipoProjetoRequerimentoVigilancia> list = (List<TipoProjetoRequerimentoVigilancia>) SerializationUtils.clone(
						(Serializable) form.getModel().getObject().getListTipoProjetos());
				form.getModel().getObject().getListTipoProjetos().clear();
				for (TipoProjetoRequerimentoVigilancia elo : list) {
					TipoProjetoRequerimentoVigilancia eloClone = VOUtils.cloneObject(elo);
					eloClone.setRequerimentoVigilancia(null);
					form.getModel().getObject().getListTipoProjetos().add(eloClone);
				}
			}
			
			if (target != null) {
				target.add(tblTipoProjeto);
				target.add(autoCompleteConsultaTipoProjetoVigilancia);
				target.add(txtArea);
			}
			
			// Grupo Dados da Obra
			if (requerimentoProjetoHidrossanitario.getVigilanciaEndereco() != null) {
				VigilanciaEndereco endereco = LoadManager.getInstance(VigilanciaEndereco.class)
														 .addProperties(new HQLProperties(
																 VigilanciaEndereco.class).getProperties())
														 .addParameter(new QueryCustom.QueryCustomParameter(
																 VigilanciaEndereco.PROP_CODIGO,
																 requerimentoProjetoHidrossanitario.getVigilanciaEndereco()
																								   .getCodigo()))
														 .start()
														 .getVO();
				pnlVigilanciaEndereco.setModelObject(endereco);
				form.getModelObject().getRequerimentoVistoriaHidrossanitario().setVigilanciaEndereco(endereco);
			}
			
			txtObraNumeroEndereco.setComponentValue(requerimentoProjetoHidrossanitario.getObraNumeroEndereco());
			txtObraQuadra.setComponentValue(requerimentoProjetoHidrossanitario.getObraQuadra());
			txtObraNumeroLado.setComponentValue(requerimentoProjetoHidrossanitario.getObraNumeroLado());
			txtObraLote.setComponentValue(requerimentoProjetoHidrossanitario.getObraLote());
			txtObraComplemento.setComponentValue(requerimentoProjetoHidrossanitario.getObraComplemento());
			txtObraNumeroLoteamento.setComponentValue(requerimentoProjetoHidrossanitario.getObraNumeroLoteamento());
			
			if (target != null) {
				target.add(pnlVigilanciaEndereco);
				target.add(txtObraNumeroEndereco);
				target.add(txtObraQuadra);
				target.add(txtObraNumeroLado);
				target.add(txtObraLote);
				target.add(txtObraComplemento);
				target.add(txtObraNumeroLoteamento);
				
				// Grupo Informações
				txtNumeroProcessoProjeto.limpar(target);
				ddRegiaoCobertaRedeEsgoto.limpar(target);
				ddRegiaoAbastecidaAgua.limpar(target);
				ddSistemaAprovAguasPluviais.limpar(target);
			}
			
			txtNumeroProcessoProjeto.setComponentValue(requerimentoProjetoHidrossanitario.getNumeroProcessoProjeto());
			ddRegiaoCobertaRedeEsgoto.setComponentValue(
					requerimentoProjetoHidrossanitario.getRegiaoCobertaRedeEsgoto());
			ddRegiaoAbastecidaAgua.setComponentValue(requerimentoProjetoHidrossanitario.getRegiaoAbastecidaAgua());
			ddSistemaAprovAguasPluviais.setComponentValue(requerimentoProjetoHidrossanitario.getSistemaAguaPluvial());
			
			if (target != null) {
				target.add(txtNumeroProcessoProjeto);
				target.add(ddRegiaoCobertaRedeEsgoto);
				target.add(ddRegiaoAbastecidaAgua);
				target.add(ddSistemaAprovAguasPluviais);
			}
			
			// Grupo Enquadramento
			autoCompleteConsultaTipoEnquadramentoProjeto.setComponentValue(
					requerimentoProjetoHidrossanitario.getTipoEnquadramentoProjeto());
			if (target != null) {
				target.add(autoCompleteConsultaTipoEnquadramentoProjeto);
			}
			
			// Grupo Uso da Edificação
			ddUsoEdificacao.setComponentValue(requerimentoProjetoHidrossanitario.getUsoEdificacao());
			if (target != null) {
				target.add(ddUsoEdificacao);
			}
			habilitarUsoEdificacao(target);
			txtObservacaoUsoEdificacao.setComponentValue(
					requerimentoProjetoHidrossanitario.getObservacaoUsoEdificacao());
			if (target != null) {
				target.add(txtObservacaoUsoEdificacao);
			}
			
			// Grupo Parcelamento do Solo
			habilitarParcelamentoSolo(target);
			txtNumeroLotesParcelamento.setComponentValue(
					requerimentoProjetoHidrossanitario.getParcelamentoSoloNumeroLotes());
			if (target != null) {
				target.add(txtNumeroLotesParcelamento);
			}
			
			// Grupo Metragem
			habilitarMetragem(target);
			txtAreaComercial.setComponentValue(requerimentoProjetoHidrossanitario.getAreaComercial());
			txtAreaResidencial.setComponentValue(requerimentoProjetoHidrossanitario.getAreaResidencial());
			txtAreaTotal.setComponentValue(requerimentoProjetoHidrossanitario.getAreaTotalConstrucao());
			
			List<EloRequerimentoVigilanciaResponsavelTecnico> responsavelTecnicoList = LoadManager.getInstance(
																										  EloRequerimentoVigilanciaResponsavelTecnico.class)
																								  .addProperties(
																										  new HQLProperties(
																												  EloRequerimentoVigilanciaResponsavelTecnico.class).getProperties())
																								  .addProperties(
																										  new HQLProperties(
																												  ResponsavelTecnico.class,
																												  EloRequerimentoVigilanciaResponsavelTecnico.PROP_RESPONSAVEL_TECNICO).getProperties())
																								  .addParameter(
																										  new QueryCustom.QueryCustomParameter(
																												  EloRequerimentoVigilanciaResponsavelTecnico.PROP_REQUERIMENTO_VIGILANCIA,
																												  requerimentoProjetoHidrossanitario.getRequerimentoVigilancia()))
																								  .start()
																								  .getList();
			if (CollectionUtils.isNotNullEmpty(responsavelTecnicoList)) {
				for (EloRequerimentoVigilanciaResponsavelTecnico elo : responsavelTecnicoList) {
					EloRequerimentoVigilanciaResponsavelTecnico eloClone = VOUtils.cloneObject(elo);
					eloClone.setRequerimentoVigilancia(null);
					form.getModel().getObject().getListResponsavelTecnico().add(eloClone);
				}
			}
			
			
			List<RequerimentoVigilanciaInscricaoImob> inscricaoList = LoadManager.getInstance(
																						 RequerimentoVigilanciaInscricaoImob.class)
																				 .addProperties(new HQLProperties(
																						 RequerimentoVigilanciaInscricaoImob.class).getProperties())
																				 .addParameter(
																						 new QueryCustom.QueryCustomParameter(
																								 RequerimentoVigilanciaInscricaoImob.PROP_REQUERIMENTO_VIGILANCIA,
																								 requerimentoProjetoHidrossanitario.getRequerimentoVigilancia()))
																				 .start()
																				 .getList();
			
			if (CollectionUtils.isNotNullEmpty(inscricaoList)) {
				for (RequerimentoVigilanciaInscricaoImob elo : inscricaoList) {
					RequerimentoVigilanciaInscricaoImob eloClone = VOUtils.cloneObject(elo);
					eloClone.setRequerimentoVigilancia(null);
					form.getModel().getObject().getListInscricaoImobiliaria().add(eloClone);
				}
			}
			
			if (target != null) {
				target.add(tblResponsavelTecnico);
				target.add(tblInscricaoImobiliaria);
			}
		} else {
			form.getModelObject()
				.setListResponsavelTecnico(
						new ArrayList<EloRequerimentoVigilanciaResponsavelTecnico>());
			form.getModelObject()
				.setListInscricaoImobiliaria(new ArrayList<RequerimentoVigilanciaInscricaoImob>());
			form.getModelObject()
				.setListTipoProjetos(new ArrayList<TipoProjetoRequerimentoVigilancia>());
			if (target != null) {
				tblInscricaoImobiliaria.update(target);
				tblResponsavelTecnico.update(target);
				tblTipoProjeto.update(target);
			}
			habilitarUsoEdificacao(target);
			habilitarParcelamentoSolo(target);
			habilitarMetragem(target);
		}
		//pnlDadosComumRequerimentoVigilancia.getTblFiscais().update(target);
	}
	
	private void habilitarUsoEdificacao (AjaxRequestTarget target) {
		if (target != null) {
			txtObservacaoUsoEdificacao.limpar(target);
		}

        if (RequerimentosProjetosEnums.UsoEdificacao.OUTROS.value()
																	.equals(form.getModel()
																				.getObject()
																				.getRequerimentoVistoriaHidrossanitario()
																				.getUsoEdificacao())) {
			txtObservacaoUsoEdificacao.setEnabled(true);
			txtObservacaoUsoEdificacao.setRequired(true);
			txtObservacaoUsoEdificacao.addRequiredClass();
		} else {
			txtObservacaoUsoEdificacao.setEnabled(false);
			txtObservacaoUsoEdificacao.setRequired(false);
			txtObservacaoUsoEdificacao.removeRequiredClass();
		}
		
		if (target != null) {
			target.add(txtObservacaoUsoEdificacao);
		}
	}
	
	private void habilitarParcelamentoSolo (AjaxRequestTarget target) {
		if (target != null) {
			txtNumeroLotesParcelamento.limpar(target);
			txtNumeroProjetoUrbanistico.limpar(target);
			txtNumeroLicitacaoAmbiental.limpar(target);
			txtNumeroProjetoEsgoto.limpar(target);
			txtNumeroProjetoAgua.limpar(target);
		}
		
		if (form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
                .getUsoEdificacao() != null && (RequerimentosProjetosEnums.UsoEdificacao.LOTEAMENTO.value()
																											.equals(form.getModel()
																														.getObject()
																														.getRequerimentoVistoriaHidrossanitario()
                                                                                                                    .getUsoEdificacao()) || RequerimentosProjetosEnums.UsoEdificacao.CONDOMINIO.value()
																																																			.equals(form.getModel()
																																																						.getObject()
																																																						.getRequerimentoVistoriaHidrossanitario()
																																																						.getUsoEdificacao()))) {
			containerParcelamentoSolo.setVisible(true);
			txtNumeroLotesParcelamento.setRequired(true);
			txtNumeroLotesParcelamento.addRequiredClass();
			
			txtNumeroProjetoUrbanistico.setRequired(true);
			txtNumeroProjetoUrbanistico.addRequiredClass();

            if (RequerimentosProjetosEnums.UsoEdificacao.isLoteamento(getFormRVH().getUsoEdificacao())
																					   && RepositoryComponentDefault.SIM_LONG.equals(
					form.getModel()
						.getObject()
						.getRequerimentoVistoriaHidrossanitario()
						.getRegiaoCobertaRedeEsgoto())) {
				txtNumeroProjetoEsgoto.setRequired(true);
				txtNumeroProjetoEsgoto.addRequiredClass();
			} else {
				txtNumeroProjetoEsgoto.setRequired(false);
				txtNumeroProjetoEsgoto.removeRequiredClass();
			}
			
			txtNumeroProjetoAgua.setRequired(true);
			txtNumeroProjetoAgua.addRequiredClass();
			
			if (form.getModel()
					.getObject()
					.getRequerimentoVistoriaHidrossanitario()
					.getNumeroProcessoProjeto() != null) {
				txtNumeroProjetoUrbanistico.setComponentValue(StringUtil.getDigits(form.getModel()
																					   .getObject()
																					   .getRequerimentoVistoriaHidrossanitario()
																					   .getNumeroProcessoProjeto()));
			}
		} else {
			containerParcelamentoSolo.setVisible(false);
			txtNumeroLotesParcelamento.setRequired(false);
			txtNumeroLotesParcelamento.removeRequiredClass();
			
			txtNumeroProjetoUrbanistico.setRequired(false);
			txtNumeroProjetoUrbanistico.removeRequiredClass();
			
			txtNumeroProjetoEsgoto.setRequired(false);
			txtNumeroProjetoEsgoto.removeRequiredClass();
			
			txtNumeroProjetoAgua.setRequired(false);
			txtNumeroProjetoAgua.removeRequiredClass();
		}
		
		if (target != null) {
			target.add(txtNumeroLotesParcelamento);
			target.add(containerParcelamentoSolo);
			target.add(txtNumeroProjetoUrbanistico);
			target.add(txtNumeroLicitacaoAmbiental);
			target.add(txtNumeroProjetoEsgoto);
			target.add(txtNumeroProjetoAgua);
		}
	}

	private void configurarDadosSolicitante() {
		if (form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.getCodigo() != null) {
			if (RequerimentoVigilancia.TipoRequerente.PESSOA.value()
					.equals(form.getModel()
							.getObject()
							.getRequerimentoVistoriaHidrossanitario()
							.getRequerimentoVigilancia()
							.getTipoRequerente())) {
				habilitarCampoProjetoHidroVigPessoa(null, form.getModel()
						.getObject()
						.getRequerimentoVistoriaHidrossanitario()
						.getRequerimentoVigilancia()
						.getVigilanciaPessoa());
			} else {
				habilitarCampoProjetoHidroEstabelecimento(null, form.getModel()
						.getObject()
						.getRequerimentoVistoriaHidrossanitario()
						.getRequerimentoVigilancia()
						.getEstabelecimento());
			}
		}
	}
	
	private void habilitarMetragem (AjaxRequestTarget target) {
		if (target != null) {
			txtAreaComercial.limpar(target);
			txtAreaResidencial.limpar(target);
		}

        if (RequerimentosProjetosEnums.UsoEdificacao.MISTA.value()
																   .equals(form.getModel()
																			   .getObject()
																			   .getRequerimentoVistoriaHidrossanitario()
																			   .getUsoEdificacao())) {
			txtAreaComercial.setEnabled(true);
			txtAreaComercial.setRequired(true);
			txtAreaComercial.addRequiredClass();
			
			txtAreaResidencial.setEnabled(true);
			txtAreaResidencial.setRequired(true);
			txtAreaResidencial.addRequiredClass();
		} else {
			txtAreaComercial.setEnabled(false);
			txtAreaComercial.setRequired(false);
			txtAreaComercial.removeRequiredClass();
			
			txtAreaResidencial.setEnabled(false);
			txtAreaResidencial.setRequired(false);
			txtAreaResidencial.removeRequiredClass();
		}
		
		if (target != null) {
			target.add(txtAreaComercial);
			target.add(txtAreaResidencial);
		}
	}
	
	private void calcularAreaTotalConstrucao (AjaxRequestTarget target) {
		Double totalArea = 0D;
		if (CollectionUtils.isNotNullEmpty(form.getModel().getObject().getListTipoProjetos())) {
			for (TipoProjetoRequerimentoVigilancia tipoProjetoRequerimentoVigilancia : form.getModel()
																						   .getObject()
																						   .getListTipoProjetos()) {
				totalArea = new Dinheiro(totalArea).somar(new Dinheiro(tipoProjetoRequerimentoVigilancia.getArea()))
												   .doubleValue();
			}
		}
		txtAreaTotal.setComponentValue(totalArea);
		target.add(txtAreaTotal);
	}
	
	
	private void validarAreaTotalConstrucao (AjaxRequestTarget target) {
		double areaSomada;
        if (RequerimentosProjetosEnums.UsoEdificacao.isMista(getFormRVH().getUsoEdificacao())
				&& getFormRVH().getAreaComercial() != null && getFormRVH().getAreaResidencial() != null) {
			areaSomada = new Dinheiro(Coalesce.asDouble(
					getFormRVH().getAreaComercial())).somar(
						 Coalesce.asDouble(
								 getFormRVH().getAreaResidencial())).doubleValue();
			if (getFormRVH()
					.getAreaTotalConstrucao()
					.compareTo(areaSomada) != 0) {
				modalWarn(target, new ValidacaoException(
						bundle("msgValorSomadoAreaComercialAreaResidencialDiferenteDoTotalCalculado")));
			}
		}
	}
	
	private DropDown getDropDownTipoPessoa (RequerimentoVistoriaHidrossanitarioDTO proxy) {
		ddTipoPessoa = DropDownUtil.getIEnumDropDown(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getRequerimentoVigilancia().getTipoRequerente()),
				RequerimentoVigilancia.TipoRequerente.values(), false, true);
		ddTipoPessoa.setEnabled(this.editMode);
		ddTipoPessoa.addAjaxUpdateValue();
		ddTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				enableCamposDadosGeral(target, true);
				habilitarCampoProjetoHidroManual(target, null, true);
			}
		});
		return ddTipoPessoa;
	}
	
	private WebMarkupContainer getContainerPessoa (RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerPessoa = new WebMarkupContainer("containerPessoa");
		containerPessoa.setOutputMarkupPlaceholderTag(true);
		
		containerPessoa.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getRequerimentoVigilancia().getVigilanciaPessoa()),
				true));
		autoCompleteConsultaVigilanciaPessoa.setOutputMarkupPlaceholderTag(true);
		autoCompleteConsultaVigilanciaPessoa.setEnabled(this.editMode);
		try {
			autoCompleteConsultaVigilanciaPessoa.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(
					BOFactoryWicket.getBO(CommomFacade.class)
								   .modulo(Modulos.VIGILANCIA_SANITARIA)
								   .getParametro("restricaoPessoaRequerimentoExterno")));
		} catch (DAOException e) {
			br.com.ksisolucoes.util.log.Loggable.log.error(e);
		}
		autoCompleteConsultaVigilanciaPessoa.setLabel(new Model(bundle("pessoa")));
		autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
			@Override
			public void valueObjectLoaded (
					AjaxRequestTarget target,
					VigilanciaPessoa object
			) {
				atualizarEnderecoPessoa(target, object);
				habilitarCampoProjetoHidroVigPessoa(target, object);
				habilitarCampoProjetoHidroManual(target, null, true);
			}
		});
		
		autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
			@Override
			public void valueObjectUnLoaded (
					AjaxRequestTarget target,
					VigilanciaPessoa object
			) {
				atualizarEnderecoPessoa(target, null);
				habilitarCampoProjetoHidroVigPessoa(target, null);
				habilitarCampoProjetoHidroManual(target, null, true);
			}
		});
		
		containerPessoa.add(txtEmailPessoa = new InputField(path(proxy.getRequerimentoVistoriaHidrossanitario()
																	  .getRequerimentoVigilancia()
																	  .getVigilanciaPessoa()
																	  .getEmail())));
		txtEmailPessoa.setEnabled(false);
		txtEmailPessoa.setLabel(new Model(bundle("email")));
		
		containerPessoa.add(new AbstractAjaxLink("btnCadPessoa") {
			@Override
			public void onAction (AjaxRequestTarget target) throws ValidacaoException, DAOException {
				addModal(target, dlgCadastroVigilanciaPessoa = new DlgCadastroVigilanciaPessoa(newModalId(), true) {
					@Override
					public void setVigilanciaPessoa (
							AjaxRequestTarget target,
							VigilanciaPessoa vigilanciaPessoa
					) {
						autoCompleteConsultaVigilanciaPessoa.limpar(target);
						autoCompleteConsultaVigilanciaPessoa.setComponentValue(target, vigilanciaPessoa);
						autoCompleteConsultaVigilanciaEndereco.limpar(target);
						VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class)
														   .setId(vigilanciaPessoa.getVigilanciaEndereco().getCodigo())
														   .start()
														   .getVO();
						form.getModel()
							.getObject()
							.getRequerimentoVistoriaHidrossanitario()
							.getRequerimentoVigilancia()
							.setVigilanciaEndereco(ve);
						target.add(autoCompleteConsultaVigilanciaEndereco);
						
						txtEmailPessoa.limpar(target);
						VigilanciaPessoa vp = LoadManager.getInstance(VigilanciaPessoa.class)
														 .addProperty(VigilanciaPessoa.PROP_EMAIL)
														 .setId(vigilanciaPessoa.getCodigo())
														 .start()
														 .getVO();
						txtEmailPessoa.setComponentValue(vp.getEmail());
						target.add(txtEmailPessoa);
						autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
						target.focusComponent(
								autoCompleteConsultaTipoProjetoVigilancia.getTxtDescricao().getTextField());
					}
				});
				dlgCadastroVigilanciaPessoa.show(target, new VigilanciaPessoa());
			}
		});
		return containerPessoa;
	}
	
	private void habilitarCampoProjetoHidroVigPessoa (
			AjaxRequestTarget target,
			VigilanciaPessoa vigilanciaPessoa
	) {
		if (target != null) {
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
			txtNumeroProjetoHidrossanitario.limpar(target);
		}
		
		if (vigilanciaPessoa != null) {
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(true);
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setVigilanciaPessoa(vigilanciaPessoa);
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEstabelecimento(null);
		} else {
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(false);
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setVigilanciaPessoa(null);
		}
		
		if (target != null) {
			target.add(autoCompleteConsultaRequerimentoProjetoHidrossanitario);
			target.add(txtNumeroProjetoHidrossanitario);
		}
	}
	
	private void atualizarEnderecoPessoa (
			AjaxRequestTarget target,
			VigilanciaPessoa vigilanciaPessoa
	) {
		autoCompleteConsultaVigilanciaEndereco.limpar(target);
		txtEmailPessoa.limpar(target);
		
		if (vigilanciaPessoa != null) {
			VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoPessoa(vigilanciaPessoa);
			form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.setVigilanciaEndereco(ve);
			autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);
			
			VigilanciaPessoa vp = LoadManager.getInstance(VigilanciaPessoa.class)
											 .addProperty(VigilanciaPessoa.PROP_EMAIL)
											 .setId(vigilanciaPessoa.getCodigo())
											 .start()
											 .getVO();
			
			if (vp.getEmail() == null) {
				txtEmailPessoa.setEnabled(true);
				txtEmailPessoa.setRequired(true);
				txtEmailPessoa.addRequiredClass();
			} else {
				txtEmailPessoa.setRequired(false);
				txtEmailPessoa.setEnabled(false);
				txtEmailPessoa.removeRequiredClass();
			}
			txtEmailPessoa.setComponentValue(vp.getEmail());
			target.add(txtEmailPessoa);
		}
	}
	
	private WebMarkupContainer getContainerEstabelecimento (RequerimentoVistoriaHidrossanitarioDTO proxy) {
		containerEstabelecimento = new WebMarkupContainer("containerEstabelecimento");
		containerEstabelecimento.setOutputMarkupPlaceholderTag(true);
		containerEstabelecimento.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(
				path(proxy.getRequerimentoVistoriaHidrossanitario().getRequerimentoVigilancia().getEstabelecimento()),
				true));
		autoCompleteConsultaEstabelecimento.setOutputMarkupPlaceholderTag(true);
		autoCompleteConsultaEstabelecimento.setLabel(new Model(bundle("estabelecimento")));
		autoCompleteConsultaEstabelecimento.setExibirNaoAutorizados(true);
		autoCompleteConsultaEstabelecimento.setExibirProvisorios(true);
		autoCompleteConsultaEstabelecimento.setEnabled(this.editMode);
		try {
			autoCompleteConsultaEstabelecimento.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(
					BOFactoryWicket.getBO(CommomFacade.class)
								   .modulo(Modulos.VIGILANCIA_SANITARIA)
								   .getParametro("restricaoEstabelecimentoRequerimentoExterno")));
		} catch (DAOException e) {
			br.com.ksisolucoes.util.log.Loggable.log.error(e);
		}
		
		autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
			@Override
			public void valueObjectLoaded (
					AjaxRequestTarget target,
					Estabelecimento object
			) {
				atualizarEnderecoEstabelecimento(target, object);
				habilitarCampoProjetoHidroEstabelecimento(target, object);
				habilitarCampoProjetoHidroManual(target, null, true);
				verificarEstabelecimentoIsento(target, object);
			}
		});
		
		autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
			@Override
			public void valueObjectUnLoaded (
					AjaxRequestTarget target,
					Estabelecimento object
			) {
				atualizarEnderecoEstabelecimento(target, null);
				habilitarCampoProjetoHidroEstabelecimento(target, object);
				habilitarCampoProjetoHidroManual(target, null, true);
				autoCompleteConsultaTipoProjetoVigilancia.limpar(target);
				target.add(autoCompleteConsultaTipoProjetoVigilancia);
				lbl.setVisible(false);
				target.add(lbl);
			}
		});
		
		containerEstabelecimento.add(txtEmailEstabelecimento = new InputField(
				path(proxy.getRequerimentoVistoriaHidrossanitario()
						  .getRequerimentoVigilancia()
						  .getEstabelecimento()
						  .getEmail())));
		txtEmailEstabelecimento.setEnabled(false);
		txtEmailEstabelecimento.setLabel(new Model(bundle("email")));
		
		containerEstabelecimento.add(new AbstractAjaxLink("btnCadEstabelecimento") {
			@Override
			public void onAction (AjaxRequestTarget target) throws ValidacaoException, DAOException {
				RequerimentoVistoriaHidrossanitarioExternoPage.this.setResponsePage(
						new CadastroEstabelecimentoExternoPage(tipoSolicitacao, true));
			}
		});
		
		return containerEstabelecimento;
	}
	
	private void habilitarCampoProjetoHidroEstabelecimento (
			AjaxRequestTarget target,
			Estabelecimento estabelecimento
	) {
		if (target != null) {
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.limpar(target);
			txtNumeroProjetoHidrossanitario.limpar(target);
		}
		
		if (estabelecimento != null) {
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(true);
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEstabelecimento(estabelecimento);
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setVigilanciaPessoa(null);
		} else {
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEnabled(false);
			autoCompleteConsultaRequerimentoProjetoHidrossanitario.setEstabelecimento(null);
		}
		
		if (target != null) {
			target.add(autoCompleteConsultaRequerimentoProjetoHidrossanitario);
			target.add(txtNumeroProjetoHidrossanitario);
		}
	}
	
	private void atualizarEnderecoEstabelecimento (
			AjaxRequestTarget target,
			Estabelecimento estabelecimento
	) {
		if (target != null) {
			autoCompleteConsultaVigilanciaEndereco.limpar(target);
			txtEmailEstabelecimento.limpar(target);
		}
		
		if (estabelecimento != null) {
			VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoEstabelecimento(estabelecimento);
			form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.setVigilanciaEndereco(ve);
			if (target != null) {
				autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);
			}
			
			Estabelecimento e = LoadManager.getInstance(Estabelecimento.class)
										   .addProperty(Estabelecimento.PROP_EMAIL)
										   .setId(estabelecimento.getCodigo())
										   .start()
										   .getVO();
			if (e.getEmail() == null) {
				txtEmailEstabelecimento.setEnabled(true);
				txtEmailEstabelecimento.setRequired(true);
				txtEmailEstabelecimento.addRequiredClass();
			} else {
				txtEmailEstabelecimento.setRequired(false);
				txtEmailEstabelecimento.setEnabled(false);
				txtEmailEstabelecimento.removeRequiredClass();
			}
			txtEmailEstabelecimento.setComponentValue(e.getEmail());
			if (target != null) {
				target.add(txtEmailEstabelecimento);
			}
		}
	}

	private void configurarEstabelecimento() {
		if (estabelecimento != null && estabelecimento.getCodigo() != null) {
			form.getModel()
					.getObject()
					.getRequerimentoVistoriaHidrossanitario()
					.getRequerimentoVigilancia()
					.setTipoRequerente(RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value());
			form.getModel()
					.getObject()
					.getRequerimentoVistoriaHidrossanitario()
					.getRequerimentoVigilancia()
					.setEstabelecimento(estabelecimento);

			atualizarEnderecoEstabelecimento(null, estabelecimento);
			habilitarCampoProjetoHidroEstabelecimento(null, estabelecimento);
			habilitarCampoProjetoHidroManual(null, null, true);
		}
	}

	private void enableCamposDadosGeral (
			AjaxRequestTarget target,
			boolean limparCampos
	) {
		containerPessoa.setEnabled(editMode);
		if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value()
																 .equals(form.getModel()
																			 .getObject()
																			 .getRequerimentoVistoriaHidrossanitario()
																			 .getRequerimentoVigilancia()
																			 .getTipoRequerente())) {
			form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.setVigilanciaPessoa(null);
			containerEstabelecimento.setVisible(true);
			containerPessoa.setVisible(false);
		} else {
			form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.setEstabelecimento(null);
			containerEstabelecimento.setVisible(false);
			containerPessoa.setVisible(true);
		}
		
		if (target != null) {
			if (limparCampos) {
				autoCompleteConsultaEstabelecimento.limpar(target);
				autoCompleteConsultaVigilanciaPessoa.limpar(target);
				autoCompleteConsultaVigilanciaEndereco.limpar(target);
			}
			target.add(containerDadosGerais);
			target.appendJavaScript(JScript.initMasks());
		}
	}
	
	private void validarRequerimento (
			AjaxRequestTarget target,
			RequerimentoVistoriaHidrossanitarioDTO dto
	) throws DAOException, ValidacaoException {
		if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value()
																 .equals(dto.getRequerimentoVistoriaHidrossanitario()
																			.getRequerimentoVigilancia()
																			.getTipoRequerente())) {
			if (dto.getRequerimentoVistoriaHidrossanitario().getRequerimentoVigilancia().getEstabelecimento() == null) {
				throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarEstabelecimento"));
			}
			if (txtEmailEstabelecimento.isEnabled()) {
				if (!EmailValidator.validarEmail(form.getModel()
													 .getObject()
													 .getRequerimentoVistoriaHidrossanitario()
													 .getRequerimentoVigilancia()
													 .getEstabelecimento()
													 .getEmail())) {
					throw new ValidacaoException(bundle("email_invalido"));
				}
				Estabelecimento estabelecimentoSave = BOFactoryWicket.save(form.getModel()
																			   .getObject()
																			   .getRequerimentoVistoriaHidrossanitario()
																			   .getRequerimentoVigilancia()
																			   .getEstabelecimento());
				if (estabelecimentoSave != null) {
					form.getModel()
						.getObject()
						.getRequerimentoVistoriaHidrossanitario()
						.getRequerimentoVigilancia()
						.setEstabelecimento(estabelecimentoSave);
				}
			}
		}
		if (RequerimentoVigilancia.TipoRequerente.PESSOA.value()
														.equals(dto.getRequerimentoVistoriaHidrossanitario()
																   .getRequerimentoVigilancia()
																   .getTipoRequerente())) {
			if (dto.getRequerimentoVistoriaHidrossanitario()
				   .getRequerimentoVigilancia()
				   .getVigilanciaPessoa() == null) {
				throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarPessoa"));
			}
			if (txtEmailPessoa.isEnabled()) {
				if (!EmailValidator.validarEmail(form.getModel()
													 .getObject()
													 .getRequerimentoVistoriaHidrossanitario()
													 .getRequerimentoVigilancia()
													 .getVigilanciaPessoa()
													 .getEmail())) {
					throw new ValidacaoException(bundle("email_invalido"));
				}
				VigilanciaPessoa vigilanciaPessoa = BOFactoryWicket.save(form.getModel()
																			 .getObject()
																			 .getRequerimentoVistoriaHidrossanitario()
																			 .getRequerimentoVigilancia()
																			 .getVigilanciaPessoa());
				if (vigilanciaPessoa != null) {
					form.getModel()
						.getObject()
						.getRequerimentoVistoriaHidrossanitario()
						.getRequerimentoVigilancia()
						.setVigilanciaPessoa(vigilanciaPessoa);
				}
			}
		}
		if (!EmailValidator.validarEmail(form.getModel()
											 .getObject()
											 .getRequerimentoVistoriaHidrossanitario()
											 .getRequerimentoVigilancia()
											 .getEmailSolicitante())) {
			throw new ValidacaoException(bundle("email_invalido"));
		}
		if (form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoProjetoHidrossanitario() == null && form.getModel()
																		.getObject()
																		.getRequerimentoVistoriaHidrossanitario()
																		.getNumeroProjetoAprovado() == null) {
			throw new ValidacaoException(bundle("msgInformeCampoNumeroProjetoHidrossanitarioAprovado"));
		}
		if (form.getModel()
				.getObject()
				.getRequerimentoVistoriaHidrossanitario()
				.getRequerimentoVigilancia()
				.getCpfSolicitante() == null && form.getModel()
													.getObject()
													.getRequerimentoVistoriaHidrossanitario()
													.getRequerimentoVigilancia()
													.getRgSolicitante() == null) {
			throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
		}
		
		if (CollectionUtils.isEmpty(form.getModel().getObject().getListResponsavelTecnico())) {
			throw new ValidacaoException(bundle("msgInformeAoMenosUmResponsavelTecnico"));
		}
		
		if (CollectionUtils.isEmpty(form.getModel().getObject().getListInscricaoImobiliaria())) {
			throw new ValidacaoException(bundle("msgInformeAoMenosUmaInscricaoImobiliaria"));
		}
		
		if (CollectionUtils.isEmpty(form.getModel().getObject().getListTipoProjetos())) {
			try {
				throw new ValidacaoException(bundle("msgInformeAoMenosUmTipoProjeto"));
			} catch (ValidacaoException e) {
				modalWarn(target, e);
			}
			return;
		}
		
		if (isBaixoRisco()) {
			throw new ValidacaoException(bundle("msgClassificacaoProjetoBaixoRiscoHabitese"));
		}
	}
	
	private void criarBtnVoltarSalvar () {
		btnVoltar = new VoltarButton("btnVoltar");
		btnSalvar = (SubmitButton) new SubmitButton("btnSalvar", new ISubmitAction() {
			@Override
			public void onSubmit (
					AjaxRequestTarget target,
					Form form
			) throws DAOException, ValidacaoException {
				salvar(target);
			}
		});
		btnSalvar.setEnabled(isEnableBtnSalvar());
		
		form.add(btnVoltar, btnSalvar);
	}
	
	private void salvar (AjaxRequestTarget target) throws DAOException, ValidacaoException {
		RequerimentoVistoriaHidrossanitarioDTO dto = form.getModel().getObject();
		
		validarRequerimento(target, dto);
		
		dto.setListAnexos(
				pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
		dto.setListAnexosExcluidos(
				pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
		dto.setTipoSolicitacao(tipoSolicitacao);
		dto.getRequerimentoVistoriaHidrossanitario()
		   .getRequerimentoVigilancia()
		   .setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());
		dto.getRequerimentoVistoriaHidrossanitario().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
		RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class)
												   .salvarRequerimentoVistoriaHidrossanitario(
														   form.getModel().getObject());
		
		String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
		if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
			mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
		}
		
		gerarBoleto(target, rv, configuracaoVigilancia);
		
		addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(),
																									   mensagemImpressao) {
			@Override
			public List<IReport> getDataReports (RequerimentoVigilancia object) throws ReportException {
				RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
				param.setRequerimentoVigilancia(object);
				
				QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(
						VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
				param.setQRCodeParam(qrCodeParam);
				
				DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class)
																	.impressaoRequerimentoVigilanciaComprovante(param);
				
				List<IReport> lstDataReport = new ArrayList<>();
				lstDataReport.add(comprovanteRequerimento);
				
				if (RepositoryComponentDefault.SIM_LONG.equals(
						configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
					DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class)
																		.impressaoTermoSolicitacaoServico(
																				object.getCodigo());
					lstDataReport.add(termoSolicitacaoServico);
				}
				
				return lstDataReport;
			}
			
			@Override
			public void onFechar (
					AjaxRequestTarget target,
					RequerimentoVigilancia object
			) throws ValidacaoException, DAOException {
				if (VigilanciaHelper.abrirTelaFinanceiro(object)) {
					RequerimentoVigilanciaDTO dto = new RequerimentoVigilanciaDTO();
					dto.setRequerimentoVigilancia(object);
					setResponsePage(new BoletoVigilanciaExternoPage(dto, classReturn));
				} else {
					try {
						Page pageReturn = (Page) classReturn.newInstance();
						getSession().getFeedbackMessages()
									.info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x",
																			  object.getProtocoloFormatado()));
						setResponsePage(pageReturn);
					} catch (InstantiationException | IllegalAccessException e) {
						Loggable.log.error(e.getMessage(), e);
					}
				}
			}
		});
		
		dlgConfirmacaoImpressao.show(target, rv);
	}
	
	private void gerarBoleto (
			AjaxRequestTarget target,
			RequerimentoVigilancia requerimentoVigilancia,
			ConfiguracaoVigilancia configuracaoVigilancia
	) throws ValidacaoException, DAOException {
		String boletoBase64RequerimentoExterno = FinanceiroVigilanciaHelper.getBoletoBase64RequerimentoExterno(
				requerimentoVigilancia, configuracaoVigilancia);
		
		if (boletoBase64RequerimentoExterno != null) {
			ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64RequerimentoExterno);
		}
	}
	
	private void carregarRequerimentoVistoriaHidrossanitario (RequerimentoVigilancia requerimentoVigilancia) {
		if (requerimentoVigilancia != null) {
			tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();
			
			requerimentoVistoriaHidrossanitario = LoadManager.getInstance(RequerimentoVistoriaHidrossanitario.class)
															 .addProperties(new HQLProperties(
																	 RequerimentoVistoriaHidrossanitario.class).getProperties())
															 .addProperties(new HQLProperties(
																	 RequerimentoProjetoHidrossanitario.class,
																	 RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO).getProperties())
															 .addProperties(
																	 new HQLProperties(RequerimentoVigilancia.class,
																					   VOUtils.montarPath(
																							   RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_PROJETO_HIDROSSANITARIO,
																							   RequerimentoProjetoHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA)).getProperties())
															 .addProperties(new HQLProperties(Estabelecimento.class,
																							  VOUtils.montarPath(
																									  RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA,
																									  RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
															 .addProperties(new HQLProperties(VigilanciaPessoa.class,
																							  VOUtils.montarPath(
																									  RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA,
																									  RequerimentoVigilancia.PROP_VIGILANCIA_PESSOA)).getProperties())
															 .addProperties(new HQLProperties(Cidade.class,
																							  VOUtils.montarPath(
																									  RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA,
																									  RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO,
																									  VigilanciaEndereco.PROP_CIDADE)).getProperties())
															 .addProperties(new HQLProperties(Estado.class,
																							  VOUtils.montarPath(
																									  RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA,
																									  RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO,
																									  VigilanciaEndereco.PROP_CIDADE,
																									  Cidade.PROP_ESTADO)).getProperties())
															 .addProperties(
																	 new HQLProperties(RequerimentoVigilancia.class,
																					   RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
															 .addProperties(
																	 new HQLProperties(TipoEnquadramentoProjeto.class,
																					   RequerimentoVistoriaHidrossanitario.PROP_TIPO_ENQUADRAMENTO_PROJETO).getProperties())
															 .addProperties(new HQLProperties(VigilanciaEndereco.class,
																							  VOUtils.montarPath(
																									  RequerimentoVistoriaHidrossanitario.PROP_VIGILANCIA_ENDERECO)).getProperties())
															 .addParameter(new QueryCustom.QueryCustomParameter(
																	 RequerimentoVistoriaHidrossanitario.PROP_REQUERIMENTO_VIGILANCIA,
																	 requerimentoVigilancia))
															 .start()
															 .getVO();
			requerimentoVistoriaHidrossanitario.setRequerimentoVigilancia(requerimentoVigilancia);
			carregarAnexos(requerimentoVigilancia);
		}
	}
	
	private void carregarTipoProjetoRequerimentoVigilanciaList (RequerimentoVigilancia requerimentoVigilancia) {
		List<TipoProjetoRequerimentoVigilancia> list =
				LoadManager.getInstance(TipoProjetoRequerimentoVigilancia.class)
						  .addProperties(new HQLProperties(TipoProjetoRequerimentoVigilancia.class).getProperties())
						  .addProperties(new HQLProperties(TipoProjetoVigilancia.class, TipoProjetoRequerimentoVigilancia.PROP_TIPO_PROJETO_VIGILANCIA).getProperties())
						  .addParameter(new QueryCustom.QueryCustomParameter(TipoProjetoRequerimentoVigilancia.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
						  .start()
						  .getList();
		
		if (CollectionUtils.isNotNullEmpty(list)) {
			form.getModel().getObject().getListTipoProjetos().addAll(list);
		}
	}
	
	private void carregarAnexos (RequerimentoVigilancia rv) {
		List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);
		
		requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
		RequerimentoVigilanciaAnexoDTO anexoDTO;
		for (RequerimentoVigilanciaAnexo rva : list) {
			anexoDTO = new RequerimentoVigilanciaAnexoDTO();
			anexoDTO.setDescricaoAnexo(rva.getDescricao());
			anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
			anexoDTO.setRequerimentoVigilanciaAnexo(rva);
			
			requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
		}
	}
	
	private void carregarEloRequerimentoVigilanciaResponsavelTecnico (RequerimentoVigilancia requerimentoVigilancia) {
		List<EloRequerimentoVigilanciaResponsavelTecnico> elos = LoadManager.getInstance(
																					EloRequerimentoVigilanciaResponsavelTecnico.class)
																			.addProperties(new HQLProperties(
																					EloRequerimentoVigilanciaResponsavelTecnico.class).getProperties())
																			.addProperty(VOUtils.montarPath(
																					EloRequerimentoVigilanciaResponsavelTecnico.PROP_RESPONSAVEL_TECNICO,
																					ResponsavelTecnico.PROP_CPF))
																			.addParameter(
																					new QueryCustom.QueryCustomParameter(
																							EloRequerimentoVigilanciaResponsavelTecnico.PROP_REQUERIMENTO_VIGILANCIA,
																							requerimentoVigilancia))
																			.start()
																			.getList();
		
		if (CollectionUtils.isNotNullEmpty(elos)) {
			form.getModelObject().getListResponsavelTecnico().addAll(elos);
		}
	}
	
	private void carregarInscricoesImob (RequerimentoVigilancia requerimentoVigilancia) {
		List<RequerimentoVigilanciaInscricaoImob> inscricaoImobList = LoadManager.getInstance(
																						 RequerimentoVigilanciaInscricaoImob.class)
																				 .addProperties(new HQLProperties(
																						 RequerimentoVigilanciaInscricaoImob.class).getProperties())
																				 .addParameter(
																						 new QueryCustom.QueryCustomParameter(
																								 RequerimentoVigilanciaInscricaoImob.PROP_REQUERIMENTO_VIGILANCIA,
																								 requerimentoVigilancia))
																				 .start()
																				 .getList();
		
		if (CollectionUtils.isNotNullEmpty(inscricaoImobList)) {
			form.getModelObject().getListInscricaoImobiliaria().addAll(inscricaoImobList);
		}
	}
	
	@Override
	public String getTituloPrograma () {
		return BundleManager.getString("habitese");
	}
	
	@Override
	public void renderHead (IHeaderResponse response) {
		super.renderHead(response);
		if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value()
																 .equals(form.getModel()
																			 .getObject()
																			 .getRequerimentoVistoriaHidrossanitario()
																			 .getRequerimentoVigilancia()
																			 .getTipoRequerente())) {
			response.render(OnDomReadyHeaderItem.forScript(
					JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
		} else {
			response.render(OnDomReadyHeaderItem.forScript(
					JScript.focusComponent(autoCompleteConsultaVigilanciaPessoa.getTxtDescricao().getTextField())));
		}
	}
	
	@Override
	public Permissions getAction () {
		return Permissions.VISTORIA_HABITESE_SANITARIO;
	}
	
	private void verificarEstabelecimentoIsento (
			AjaxRequestTarget target,
			Estabelecimento object
	) {
		if (Estabelecimento.TipoEmpresa.PRIVADA.value().equals(object.getTipoEmpresa())) {
			if (VigilanciaHelper.isIsentoPorLei(object)) {
				lbl.setDefaultModel(Model.of(
						"Esta empresa está configurada como “Isenta por lei” de taxas, portanto será necessário anexar o(s) documento(s) comprobatório(s) desta isenção."));
				lbl.setVisible(true);
				target.add(lbl);
			}
		}
	}

	private void enableDisableBtnSalvar (AjaxRequestTarget target) {
		if (target != null) {
			btnSalvar.setEnabled(isEnableBtnSalvar());
			target.add(btnSalvar);
		}
	}

	private boolean isEnableBtnSalvar () {
		return isEditMode() && isOrientacaoTecnica();
	}

	private boolean hasEntity () {
		return (requerimentoVistoriaHidrossanitario != null &&
				requerimentoVistoriaHidrossanitario.getCodigo() != null &&
				requerimentoVistoriaHidrossanitario.getCodigo() > 0L);
	}

	private boolean isEditMode () {
		return this.editMode;
	}

	private boolean isOrientacaoTecnica () {
		return cbxTermoAceite != null && cbxTermoAceite.getComponentValue() != null &&
				RepositoryComponentDefault.SIM_LONG.equals(cbxTermoAceite.getComponentValue());
	}

	private void criarCheckboxTermoAceite (RequerimentoVistoriaHidrossanitarioDTO proxy) {

		cbxTermoAceite = new CheckBoxLongValue(path(proxy.getRequerimentoVistoriaHidrossanitario().getTermoAceite()), RepositoryComponentDefault.SIM_LONG);

		cbxTermoAceite.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
			@Override
			protected void onUpdate (AjaxRequestTarget target) {
				enableDisableBtnSalvar(target);
			}
		});
		cbxTermoAceite.setEnabled(editMode);
		form.add(cbxTermoAceite);
	}

	private boolean listContainValues(List list) {
		return (list != null && list.size() > 0);
	}

	private RequerimentoVistoriaHidrossanitario getFormRVH() {
		return form.getModel().getObject().getRequerimentoVistoriaHidrossanitario();
	}
}
