package br.com.celk.view.hospital.exportacao.producaoaih;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxDownload;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.asyncprocess.interfaces.IAsyncProcessNotification;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import static br.com.ksisolucoes.system.methods.CoreMethods.*;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.io.FileUtils;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.AihProcesso;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import static ch.lambdaj.Lambda.on;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.repeater.RepeatingView;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

/**
 *
 * <AUTHOR>
 */
public class ExportarProducaoAihPage extends BasePage implements IAsyncProcessNotification {

    private AbstractAjaxButton btnGerarProducao;
    private Form form;
    private RepeatingView controls;
    private Table table;
    private List<AihProcesso> itens = new ArrayList<AihProcesso>();
    private DlgExportarProducao dlgExportarProducao;
    private String data;
    private InputField txtCompetencia;
    private AjaxDownload ajaxDownload;

    public ExportarProducaoAihPage() {
        super();
        init();
    }

    private void init() {
        form = new Form("form", new CompoundPropertyModel(this));

        form.add(txtCompetencia = new InputField("data"));

        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                //Data.isValidaDataMesAno(data);
                validaData(data);
                atualizarItens();
                table.update(target);
            }
        });

        form.add(controls = new RepeatingView("controls"));

        controls.add(btnGerarProducao = new AbstractAjaxButton(controls.newChildId()) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dlgExportarProducao.show(target);
            }
        });

        btnGerarProducao.add(new AttributeModifier("class", "doc-plus"));
        btnGerarProducao.add(new AttributeModifier("value", BundleManager.getString("gerarProducao")));

        table = new Table("table", getColumns(), getCollectionPorvider());
        form.add(table.populate());

        form.add(ajaxDownload = new AjaxDownload());

        add(form);

        try {
            BOFactoryWicket.getBO(HospitalFacade.class).removerIpeProcessAdicionais();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        }

        addModal(dlgExportarProducao = new DlgExportarProducao(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String data, String dataApresentacao) throws DAOException, ValidacaoException {
                geraProducaoAih(target, data, dataApresentacao);
            }
        });

        try {
            BOFactoryWicket.getBO(HospitalFacade.class).removerAihProcessAdicionais();
        } catch (SGKException ex) {
            Loggable.log.warn(ex.getMessage(), ex);
        }

        atualizarItens();
    }

    private void validaData(String data) throws ValidacaoException {
        if (data != null) {
            Integer mes = Integer.parseInt(data.substring(0, 2));
            Integer ano = Integer.parseInt(data.substring(3, 7));
            if (!Data.isValidoMes(mes)) {
                throw new ValidacaoException(BundleManager.getString("mesInvalido"));
            }
            if (!Data.isValidoAno(ano)) {
                throw new ValidacaoException(BundleManager.getString("anoInvalido"));
            }
        }

    }

    private void verificaExportacaoCompetencia(String data) throws ValidacaoException {
        AihProcesso aihp = LoadManager.getInstance(AihProcesso.class)
                .addProperty(AihProcesso.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(AihProcesso.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AihProcesso.STATUS_GERADO, AihProcesso.STATUS_GERANDO_TEXTO)))
                .addParameter(new QueryCustom.QueryCustomParameter(AihProcesso.PROP_COMPETENCIA, QueryCustom.QueryCustomParameter.MAIOR_IGUAL, getDataPeriodo(data).getDataInicial()))
                .addParameter(new QueryCustom.QueryCustomParameter(AihProcesso.PROP_COMPETENCIA, QueryCustom.QueryCustomParameter.MENOR_IGUAL, getDataPeriodo(data).getDataFinal()))
                .start().getVO();

        if (aihp != null) {
            throw new ValidacaoException(BundleManager.getString("msg_exportacao_existente_competencia"));
        }
    }

    private void geraProducaoAih(AjaxRequestTarget target, String data, String dataApresentacao) throws DAOException, ValidacaoException {
        verificaExportacaoCompetencia(data);
        Calendar c = Calendar.getInstance();
        c.setTime(Data.getDataParaPrimeiroDiaMes(Data.parserMounthYear(data)));
        Long mes = Integer.valueOf(c.get(Calendar.MONTH) + 1).longValue();
        Long ano = Integer.valueOf(c.get(Calendar.YEAR)).longValue();

        Calendar c2 = Calendar.getInstance();
        c2.setTime(Data.getDataParaPrimeiroDiaMes(Data.parserMounthYear(dataApresentacao)));

        BOFactoryWicket.getBO(HospitalFacade.class).cadastrarProcessoAih(mes, ano, c2.getTime());

        atualizarItens();
        table.update(target);
    }

    private DatePeriod getDataPeriodo(String data) {
        if (data == null) {
            return null;
        }
        Calendar c = Calendar.getInstance();
        c.setTime(Data.getDataParaPrimeiroDiaMes(Data.parserMounthYear(data)));

        return Data.adjustRangeMonth(c.getTime());
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        AihProcesso proxy = on(AihProcesso.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(BundleManager.getString("dataDaGeracao"), proxy.getDataGeracao()));
        columns.add(createColumn(BundleManager.getString("usuario"), proxy.getUsuarioGeracao()));
        columns.add(new DateColumn(BundleManager.getString("competencia"), path(proxy.getCompetencia())).setPattern("MM/yyyy"));
        columns.add(getCustomColumnStatus());

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AihProcesso>() {
            @Override
            public void customizeColumn(AihProcesso rowObject) {

                addAction(ActionType.BAIXA, rowObject, new IModelAction<AihProcesso>() {
                    @Override
                    public void action(AjaxRequestTarget target, AihProcesso modelObject) throws ValidacaoException, DAOException {
                        downloadArquivo(target, modelObject);
                    }
                }).setEnabled(AihProcesso.STATUS_GERADO == rowObject.getStatus());

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AihProcesso>() {
                    @Override
                    public void action(AjaxRequestTarget target, AihProcesso modelObject) throws ValidacaoException, DAOException {
                        verificaMsgErro(modelObject.getCodigo());
                    }
                }).setEnabled((rowObject.getAsyncProcess().getMensagemErro() != null
                        && !rowObject.getAsyncProcess().getMensagemErro().isEmpty())
                        && rowObject.getStatus() != AihProcesso.STATUS_CANCELADO);

                addAction(ActionType.REMOVER, rowObject, new IModelAction<AihProcesso>() {
                    @Override
                    public void action(AjaxRequestTarget target, AihProcesso modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).cancelarProducaoAih(modelObject);
                        atualizarItens();
                        table.update(target);
                    }
                }).setQuestionDialogBundleKey("cancelar_producao_competencia")
                        .setTitleBundleKey("cancelar")
                        .setEnabled(AihProcesso.STATUS_CANCELADO != rowObject.getStatus());
            }
        };
    }

    public CustomColumn getCustomColumnStatus() {
        return new CustomColumn<AihProcesso>() {
            @Override
            public Component getComponent(String componentId, AihProcesso rowObject) {
                return new StatusAihProcessColumnPanel(componentId, rowObject);
            }
        };
    }

    private ICollectionProvider getCollectionPorvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }

    private void atualizarItens() {
        AihProcesso proxy = on(AihProcesso.class);

        LoadManager l = LoadManager.getInstance(AihProcesso.class)
                .addProperties(new HQLProperties(AihProcesso.class).getProperties())
                .addProperties(new HQLProperties(AsyncProcess.class, AihProcesso.PROP_ASYNC_PROCESS).getProperties())
                .addProperty(path(proxy.getUsuarioGeracao().getCodigo()))
                .addProperty(path(proxy.getUsuarioGeracao().getNome()))
                .addProperty(path(proxy.getUsuarioGeracao().getLogin()))
                .addProperty(path(proxy.getLoteAih().getCodigo()))
                .addProperty(path(proxy.getLoteAih().getStatus()));

        if (data != null) {
            l.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCompetencia()), BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, getDataPeriodo(data).getDataInicial()));
            l.addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCompetencia()), BuilderQueryCustom.QueryParameter.MENOR_IGUAL, getDataPeriodo(data).getDataFinal()));
        }
        l.addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getAsyncProcess().getDataRegistro()), BuilderQueryCustom.QuerySorter.DECRESCENTE));

        itens = l.start().getList();
    }

    @Override
    public void notifyProcess(AjaxRequestTarget target, AsyncProcess event) {
        atualizarItens();
        table.update(target);
    }

    private void verificaMsgErro(Long codigoAihProcess) throws ValidacaoException {
        AihProcesso proxy = on(AihProcesso.class);

        AihProcesso aihP = (AihProcesso) LoadManager.getInstance(AihProcesso.class)
                .addProperty(path(proxy.getCodigo()))
                .addProperty(path(proxy.getAsyncProcess().getCodigo()))
                .addProperty(path(proxy.getAsyncProcess().getMensagemErro()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), codigoAihProcess))
                .start().getVO();

        if (aihP.getAsyncProcess().getMensagemErro() != null) {
            throw new ValidacaoException(aihP.getAsyncProcess().getMensagemErro());
        }

    }

    private void downloadArquivo(AjaxRequestTarget target, AihProcesso aihProcesso) throws DAOException, ValidacaoException {
        AihProcesso aihp = BOFactoryWicket.getBO(HospitalFacade.class).downloadProducaoAih(aihProcesso);
        carregarArquivo(target, aihp.getCaminhoArquivo());

    }

    private void carregarArquivo(AjaxRequestTarget target, String path) throws ValidacaoException, DAOException {
        try {
            File f = File.createTempFile("aihProcesso", ".txt");
            FileUtils.buscarArquivoFtp(path, f.getAbsolutePath());

            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(f));
            ajaxDownload.initiate(target, "aih_exportacao.txt", resourceStream);
        } catch (IOException ex) {
            throw new DAOException(ex.getMessage());
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("exportacaoProducaoAih");
    }
}
