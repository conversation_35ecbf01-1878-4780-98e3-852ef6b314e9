package br.com.celk.view.unidadesaude.bpa.processo.esuspanels;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaEsus;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgEsusIndividual extends Window{
    
    private PnlEsusIndividual pnlEsusIndividual;
    ItemContaEsus itemContaEsus;
    
    public DlgEsusIndividual(String id, ItemContaEsus itemContaEsus, ContaPaciente contaPaciente){
        super(id);
        init(itemContaEsus, contaPaciente);
    }

    private void init(ItemContaEsus itemContaEsus, ContaPaciente contaPaciente) {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("profissionalNivelSuperior");
            }
        });
                
        setInitialWidth(620);
        setInitialHeight(330);
        setResizable(true);
        
        setContent(pnlEsusIndividual = new PnlEsusIndividual(getContentId(), itemContaEsus, contaPaciente) {

            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void adicionarFicha(AjaxRequestTarget target, ItemContaEsus itemContaEsus) throws ValidacaoException, DAOException {
                DlgEsusIndividual.this.adicionarFicha(target, itemContaEsus);
                close(target);
            }
        });
    }
    
    public abstract void adicionarFicha(AjaxRequestTarget target, ItemContaEsus itemContaEsus) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, ItemContaEsus itemContaEsus){
        show(target);
    }    

}
