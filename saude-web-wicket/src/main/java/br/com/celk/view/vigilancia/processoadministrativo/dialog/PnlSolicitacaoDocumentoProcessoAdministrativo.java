package br.com.celk.view.vigilancia.processoadministrativo.dialog;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroProcessoAdministrativoOcorrenciaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlSolicitacaoDocumentoProcessoAdministrativo extends Panel{

    private Form form;
    private CompoundPropertyModel<CadastroProcessoAdministrativoOcorrenciaDTO> model;
    private ProcessoAdministrativo processoAdministrativo;
    private Profissional profissional;
    private InputArea txaDescricao;
    private Table<Profissional> tblProfissional;
    private List<Profissional> profissionalList;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;

    public PnlSolicitacaoDocumentoProcessoAdministrativo(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form<>("form", model = new CompoundPropertyModel(new CadastroProcessoAdministrativoOcorrenciaDTO()));
        final CadastroProcessoAdministrativoOcorrenciaDTO proxy = on(CadastroProcessoAdministrativoOcorrenciaDTO.class);
        setOutputMarkupId(true);
        
        form.add(txaDescricao = new InputArea(path(proxy.getDescricao())));
        
        form.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", new PropertyModel<Profissional>(this, "profissional")));
        autoCompleteConsultaProfissional.setCodigoEmpresa(ApplicationSession.get().getSessaoAplicacao().<Empresa>getEmpresa().getCodigo());
        
        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        form.add(tblProfissional = new Table("tblProfissional", getColumns(), getCollectionProvider()));
        tblProfissional.populate();
        
        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarCadastro(target)){
                    model.getObject().setProcessoAdministrativo(processoAdministrativo);
                    model.getObject().setProfissionalList(profissionalList);

                    onConfirmar(target, model.getObject());
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarCadastro(AjaxRequestTarget target) {
        try {            
            if(txaDescricao.getComponentValue() == null){
                throw new ValidacaoException(bundle("msgInformeDescricao"));
            } else if(CollectionUtils.isEmpty(profissionalList)){
                throw new ValidacaoException(bundle("msgAdicionePeloMenosUmFiscal"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }
    
    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (CollectionUtils.isEmpty(profissionalList)) {
            profissionalList = new ArrayList();
        }

        if (profissional == null) {
            throw new ValidacaoException(bundle("informeProfissional"));
        }

        if (profissionalList.contains(profissional)) {
            throw new ValidacaoException(bundle("fiscalJaAdicionado"));
        }

        profissionalList.add(profissional);
        tblProfissional.update(target);

        autoCompleteConsultaProfissional.limpar(target);
        target.focusComponent(autoCompleteConsultaProfissional.getTxtDescricao().getTextField());
    }
    
    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        Profissional proxy = on(Profissional.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("fiscal"), proxy.getNome()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<Profissional>() {
            @Override
            public void customizeColumn(Profissional rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Profissional>() {
                    @Override
                    public void action(AjaxRequestTarget target, Profissional modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                    }
                });
            }
        };
    }

    private void remover(AjaxRequestTarget target, Profissional modelObject) {
        profissionalList.remove(modelObject);
        tblProfissional.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return profissionalList;
            }
        };
    }
   
    public abstract void onConfirmar(AjaxRequestTarget target, CadastroProcessoAdministrativoOcorrenciaDTO dto) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void setObject(AjaxRequestTarget target, ProcessoAdministrativo processoAdministrativo, List<Profissional> profissionalList){
        this.processoAdministrativo = processoAdministrativo;
        txaDescricao.limpar(target);
        target.focusComponent(txaDescricao);
        if(CollectionUtils.isNotNullEmpty(profissionalList)){
            this.profissionalList = profissionalList;
        } else {
            this.profissionalList = new ArrayList();
        }
    }
}