
package br.com.celk.view.agenda.agendamento;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.unidadesaude.exames.PnlMotivoTelefoneAgendamentos;
import br.com.celk.view.unidadesaude.exames.PnlMotivoTelefoneRemanejamentoAgendamento;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoHorarioDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgMotivoRemanejamentoAgendamento extends Window {

    private AgendaGradeAtendimentoHorarioDTO dto;
    private PnlMotivoTelefoneRemanejamentoAgendamento pnlMotivoTelefoneAgendamentos;

    public DlgMotivoRemanejamentoAgendamento(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(190);

        setResizable(false);

        setTitle(BundleManager.getString("remanejamento"));

        setContent(pnlMotivoTelefoneAgendamentos = new PnlMotivoTelefoneRemanejamentoAgendamento(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, Empresa empresa) throws ValidacaoException, DAOException {
                close(target);
                DlgMotivoRemanejamentoAgendamento.this.onConfirmar(target,motivo,empresa, dto);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target,String motivo, Empresa empresa,AgendaGradeAtendimentoHorarioDTO dto) throws ValidacaoException, DAOException;

    public void setDto(AgendaGradeAtendimentoHorarioDTO dto, AjaxRequestTarget target) {
        this.dto = dto;
        pnlMotivoTelefoneAgendamentos.setDto(target, this.dto);
    }
    
    
}
