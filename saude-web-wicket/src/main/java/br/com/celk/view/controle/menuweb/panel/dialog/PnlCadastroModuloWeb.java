package br.com.celk.view.controle.menuweb.panel.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.controle.web.MenuWeb;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlCadastroModuloWeb extends Panel{
    
    private Form<MenuWeb> form;
    private CompoundPropertyModel<MenuWeb> model;
    
    private InputField txtDescricao;
    
    public PnlCadastroModuloWeb(String id){
        super(id);
        init();
    }

    private void init() {
        form = new Form<MenuWeb>("form", model = new CompoundPropertyModel(new MenuWeb()));
        setOutputMarkupId(true);
        MenuWeb proxy = on(MenuWeb.class);
        
        form.add(txtDescricao = (InputField) new InputField(path(proxy.getDescricao())));
        
        form.add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarSalvar(target)){
                    model.getObject().setLayoutMenu(MenuWeb.LayoutMenu.PADRAO.value());
                    onSalvar(target, model.getObject());
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    private boolean validarSalvar(AjaxRequestTarget target) throws ValidacaoException, DAOException{
        try {
            if (txtDescricao.getComponentValue() == null) {
                throw new ValidacaoException(bundle("msgInformeDescricao"));
            }
            
            boolean existsModulo = LoadManager.getInstance(MenuWeb.class)
                    .addParameter(new QueryCustomParameter(MenuWeb.PROP_DESCRICAO, txtDescricao.getComponentValue()))
                    .addParameter(new QueryCustomParameter(MenuWeb.PROP_MENU_WEB_PAI, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .addParameter(new QueryCustomParameter(MenuWeb.PROP_PROGRAMA_WEB, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .addParameter(new QueryCustomParameter(MenuWeb.PROP_MENU_MODULO, BuilderQueryCustom.QueryParameter.IS_NULL))
                    .exists();
            
            if(existsModulo){
                throw new ValidacaoException(bundle("msgJaExisteModuloComEstaDescricao"));                
            }
        } catch (ValidacaoRuntimeException ex) {
            MessageUtil.modalWarn(target, this, ex);
            return false;
        }
        return true;
    }
   
    public abstract void onSalvar(AjaxRequestTarget target, MenuWeb modulo) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target){
        form.getModel().setObject(new MenuWeb());
        
        txtDescricao.limpar(target);
        target.focusComponent(txtDescricao);
    }
}