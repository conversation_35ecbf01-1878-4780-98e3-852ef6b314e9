package br.com.celk.view.cadsus.usuariocadsus.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.IAjaxIndicatorAware;
import org.apache.wicket.behavior.Behavior;
import org.apache.wicket.extensions.ajax.markup.html.AjaxIndicatorAppender;
import org.apache.wicket.model.IModel;
import org.apache.wicket.request.Response;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaUsuarioCadsus extends AutoCompleteConsulta<UsuarioCadsus> implements IAjaxIndicatorAware {

    private Configuration configuration;
    private AjaxIndicatorAppender indicatorAppender;

    public AutoCompleteConsultaUsuarioCadsus(String id, IModel<UsuarioCadsus> model, boolean required) {
        super(id, model, required);
        custom();
    }

    public AutoCompleteConsultaUsuarioCadsus(String id, IModel<UsuarioCadsus> model) {
        super(id, model);
        custom();
    }

    public AutoCompleteConsultaUsuarioCadsus(String id, boolean required) {
        super(id, required);
        custom();
    }

    public AutoCompleteConsultaUsuarioCadsus(String id) {
        super(id);
        custom();
    }

    private void custom() {
        getPanelConsulta().getPageableTable().setScrollX("1000px");
    }

    @Override
    public int getMinDialogWidth() {
        return 1024;
    }

    @Override
    public int getMinDialogHeight() {
        return 500;
    }

    @Override
    public IConsultaConfigurator<UsuarioCadsus> getConsultaConfigurator() {
        try {
            return getConfiguration().configuratorClass().newInstance();
        } catch (InstantiationException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IllegalAccessException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("pacientes");
    }

    public enum Configuration {

        DEFAULT(ConfiguratorDominioUsuarioCadsus.class),
        ATIVO_PROVISORIO(ConfiguratorUsuarioCadsusAtivoProvisorio.class),
        NAO_EXCLUIDOS(ConfiguratorUsuarioCadsusNaoExcluidos.class),
        EXCLUIDOS(ConfiguratorUsuarioCadsusExcluidos.class),
        ATIVO_PROVISORIO_FEMININO(ConfiguratorUsuarioCadsusAtivoProvisorioFeminino.class),
        RESPONSAVEL_FAMILIAR(ConfiguratorUsuarioCadsusResponsavelFamiliar.class),
        ATIVO(ConfiguratorUsuarioCadsusAtivo.class),;

        private Class<ConsultaConfigurator> configuratorClass;

        private Configuration(Class configuratorClass) {
            this.configuratorClass = configuratorClass;
        }

        public Class<ConsultaConfigurator> configuratorClass() {
            return configuratorClass;
        }
    }

    public final Configuration getConfiguration() {
        if (this.configuration == null) {
            this.configuration = getConfigurationInstance();
        }

        return this.configuration;
    }

    public Configuration getConfigurationInstance() {
        return Configuration.DEFAULT;
    }

    public AutoCompleteConsultaUsuarioCadsus addBusyIndicator() {
        indicatorAppender = new AjaxIndicatorAppender(){

            @Override
            public void afterRender(Component component) {
                final Response r = component.getResponse();

		r.write("<span style=\"display:none;\" class=\"");
		r.write(getSpanClass());
		r.write("\" ");
		r.write("id=\"");
		r.write(getMarkupId());
		r.write("\">");
		r.write("<img src=\"");
		r.write(getIndicatorUrl());
		r.write("\" style=\"height: 12px;\" alt=\"\"/></span>");
            }
            
        };
        add(indicatorAppender);
        return this;
    }

    @Override
    public String getAjaxIndicatorMarkupId() {
        if (indicatorAppender != null) {
            return indicatorAppender.getMarkupId();
        } else {
            return "";
        }
    }

}
