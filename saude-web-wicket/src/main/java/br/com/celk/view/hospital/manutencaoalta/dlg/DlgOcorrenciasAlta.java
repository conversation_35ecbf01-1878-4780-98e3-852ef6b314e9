package br.com.celk.view.hospital.manutencaoalta.dlg;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public class DlgOcorrenciasAlta extends Window {

    private PnlOcorrenciasAlta pnlOcorrenciasAlta;

    public DlgOcorrenciasAlta(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(850);
        setInitialHeight(200);

        setResizable(true);

        setTitle(BundleManager.getString("ocorrenciasAlta"));

        setContent(pnlOcorrenciasAlta = new PnlOcorrenciasAlta(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, Long codigoAtendimentoAlta) {
        pnlOcorrenciasAlta.carregarListaOcorrencias(target, codigoAtendimentoAlta);
        super.show(target);
    }
}
