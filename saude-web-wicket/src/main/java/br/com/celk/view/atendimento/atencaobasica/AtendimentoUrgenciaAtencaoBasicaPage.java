package br.com.celk.view.atendimento.atencaobasica;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.cpffield.CpfField;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.DefaultProntuarioPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.CadastroPacientePage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.cadsus.usuariocadsus.foto.FotoComponente;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.QueryConsultaProfissionalCargaHorariaDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.ProfissionalFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.AtendimentoWebDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.recepcao.interfaces.dto.ConfirmacaoAtendimentoDTO;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.*;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.enderecoestruturado.EnderecoEstruturado;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimentoAtendimento;
import br.com.ksisolucoes.vo.prontuario.grupos.EloGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.grupos.EloTipoAtendimentoGrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;
import static org.hamcrest.Matchers.isIn;

/**
 * Created by sulivan on 17/01/19.
 */
@Private
public class AtendimentoUrgenciaAtencaoBasicaPage extends BasePage {

    private Form<ConfirmacaoAtendimentoDTO> form;
    private ConfirmacaoAtendimentoDTO confirmacaoAtendimentoDTO;
    private AbstractAjaxLink btnCadPaciente;
    private DropDown dropDownTipoProcedimentoAtendimento;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private InputField txtNrFamilia;
    private InputField txtArea;
    private InputField txtMicroarea;
    private InputField txtCelular;
    private InputField txtCns;
    private InputField txtCpf;
    private InputField txtAcs;
    private Long nrFamilia;
    private String area;
    private Long microArea;
    private String acs;
    private Equipe equipe;
    private String cnsUsuarioCadsus;
    private NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento;
    private SubmitButton btnAtender;
    private SubmitButton btnIncluirFila;
    private DlgConfirmacaoSimNao dlgMensagemPacienteCepInvalido;
    private DlgConfirmacaoOk dlgMensagemPacienteRacaInvalida;
    private DlgConfirmacaoSimNao dlgMensagemPacienteSemEnderCadastrado;
    private boolean pacienteSemCep = false;
    private FotoComponente fotoComponente;
    private final AtendimentoWebDTOParam parameters;
    private DropDown<Equipe> cbxEquipeAcompanhamento;
    private Equipe equipeAcompanhamento;
    private Cidade cidadeParametroEnderecoEstruturado;
    private boolean exigeEquipeAcompanhamento;
    private Usuario usuarioLogado;
    private DropDown<TabelaCbo> dropDownCbo;
    private TabelaCbo tabelaCbo;

    public AtendimentoUrgenciaAtencaoBasicaPage(AtendimentoWebDTOParam parameters) {
        this.parameters = parameters;
        init();
    }

    public AtendimentoUrgenciaAtencaoBasicaPage(ConfirmacaoAtendimentoDTO confirmacaoAtendimentoDTO) {
        this.confirmacaoAtendimentoDTO = confirmacaoAtendimentoDTO;
        this.parameters = confirmacaoAtendimentoDTO.getParameters();
        init();
    }

    private void init() {
        try {
            cidadeParametroEnderecoEstruturado = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("MunicipioComEnderecoEstruturado");
            exigeEquipeAcompanhamento = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("ExigeEquipeAcompanhamento"));
        } catch (ValidacaoRuntimeException | DAOException e) {
            warn(e.getMessage());
            Loggable.log.error(e.getMessage(), e);
        }
        ConfirmacaoAtendimentoDTO proxy = on(ConfirmacaoAtendimentoDTO.class);

        getForm().add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getProfissional()), true).setLabel(new Model<String>(bundle("profissional"))));
        autoCompleteConsultaProfissional.setCodigoEmpresa(getForm().getModel().getObject().getEstabelecimentoExecutante().getCodigo());
        autoCompleteConsultaProfissional.setPeriodoEmpresa(true);
        getForm().add(dropDownCbo = new DropDown<>(path(proxy.getTabelaCbo())));
        dropDownCbo.setEnabled(false);
        warn(Bundle.getStringApplication("msgCampoCboParaProfissionaisComMaisDeUmCboNaUnidade"));

        if (getUsuarioLogado() != null && getUsuarioLogado().getProfissional() != null && getUsuarioLogado().getProfissional().getCodigo() != null) {
            autoCompleteConsultaProfissional.setComponentValue(getUsuarioLogado().getProfissional());
        }

        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                equipe = null;

                List<EquipeProfissional> epList = LoadManager.getInstance(EquipeProfissional.class)
                        .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL)).getProperties())
                        .addProperties(new HQLProperties(Equipe.class, EquipeProfissional.PROP_EQUIPE).getProperties())
                        .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL), object))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA), getForm().getModel().getObject().getEstabelecimentoExecutante()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_STATUS), EquipeProfissional.STATUS_ATIVO))
                        .start().getList();

                if (!epList.isEmpty()) {
                    EquipeProfissional ep = epList.get(0);
                    equipe = ep.getEquipe();
                }

                populateDropDownCbo(object);
                btnAtender.setEnabled(true);
                if ((getUsuarioLogado() != null && getUsuarioLogado().getProfissional() == null) || (getUsuarioLogado() != null && getUsuarioLogado().getProfissional() != null && getUsuarioLogado().getProfissional().getCodigo() != null
                        && !getUsuarioLogado().getProfissional().getCodigo().equals(object.getCodigo()))) {
                    btnAtender.setEnabled(false);
                }
                target.add(btnAtender);
                target.add(dropDownCbo);
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                equipe = null;
                btnAtender.setEnabled(true);
                if (getUsuarioLogado() != null && (getUsuarioLogado().getProfissional() == null || getUsuarioLogado().getProfissional().getCodigo() == null)) {
                    btnAtender.setEnabled(false);
                }
                dropDownCbo.removeAllChoices();
                dropDownCbo.addAjaxUpdateValue();
                dropDownCbo.setEnabled(false);
                target.add(dropDownCbo);
                target.add(btnAtender);
            }
        });

        getForm().add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getConsultaUsuarioCadsusDTO().getUsuarioCadsus()), true) {
            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }
        });
        autoCompleteConsultaUsuarioCadsus.addAjaxUpdateValue();
        autoCompleteConsultaUsuarioCadsus.setLabel(new Model<String>(bundle("paciente")));
        autoCompleteConsultaUsuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                carregarInformacoesPaciente(target, object);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        autoCompleteConsultaUsuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                carregarInformacoesPaciente(target, null);
                target.appendJavaScript(JScript.removeAutoCompleteDrop());
            }
        });

        getForm().add(btnCadPaciente = new AbstractAjaxLink("btnCadPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                setResponsePage(new CadastroPacientePage(getForm().getModel().getObject(), true, false));
            }
        });

        getForm().add(txtNrFamilia = new DisabledInputField("nrFamilia", new PropertyModel(this, "nrFamilia")));
        getForm().add(txtArea = new DisabledInputField("area", new PropertyModel(this, "area")));
        getForm().add(txtMicroarea = new DisabledInputField("microArea", new PropertyModel(this, "microArea")));
        getForm().add(txtAcs = new DisabledInputField("acs", new PropertyModel(this, "acs")));
        getForm().add(cbxEquipeAcompanhamento = new DropDown("usuarioCadsus.equipe", new PropertyModel(this, "equipeAcompanhamento")));
        getForm().add(new DisabledInputField(path(proxy.getEstabelecimentoExecutante().getDescricao())));
        getForm().add(txtCelular = (InputField) new TelefoneField(path(proxy.getConsultaUsuarioCadsusDTO().getUsuarioCadsus().getCelular())).setLabel(new Model<String>(bundle("celular"))));
        getForm().add(txtCns = new InputField("cnsUsuarioCadsus", new PropertyModel<String>(this, "cnsUsuarioCadsus")));
        getForm().add(txtCpf = (InputField) new CpfField(path(proxy.getConsultaUsuarioCadsusDTO().getUsuarioCadsus().getCpf())).setLabel(new Model<String>(bundle("cpf"))));
        getForm().add(getDropDownTipoProcedimentoAtendimento(path(proxy.getTipoProcedimentoAtendimento())));
        getForm().add(dropDownCbo);

        getForm().add(fotoComponente = new FotoComponente(new UsuarioCadsus()));
        fotoComponente.setOutputMarkupId(true);
        fotoComponente.setOutputMarkupPlaceholderTag(true);

        dropDownCbo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget ajaxRequestTarget) {
                if (dropDownCbo.getComponentValue() != null) {
                    tabelaCbo = dropDownCbo.getComponentValue();
                }
            }
        });

        getForm().add(new SubmitButton("btnVoltar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                setResponsePage(new ConsultaAtendimentoAtencaoBasicaPage(getForm().getModel().getObject().getParameters()));
            }
        }).setDefaultFormProcessing(false));
        getForm().add(btnAtender = new SubmitButton("btnAtender", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                atender(false);
            }
        }));
        getForm().add(btnIncluirFila = new SubmitButton("btnIncluirFila", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                atender(true);
            }
        }));

        add(getForm());
        getNaturezaProcuraTipoAtendimento();
        if (getForm().getModel().getObject().getConsultaUsuarioCadsusDTO() != null && getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus() != null) {
            carregarInformacoesPaciente(null, getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus());
        }
        if (getUsuarioLogado() != null && (getUsuarioLogado().getProfissional() == null || getUsuarioLogado().getProfissional().getCodigo() == null)) {
            btnAtender.setEnabled(false);
        }
    }

    private void carregarInformacoesPaciente(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        if (target != null) {
            autoCompleteConsultaUsuarioCadsus.limpar(target);
            txtNrFamilia.limpar(target);
            txtArea.limpar(target);
            txtMicroarea.limpar(target);
            txtCelular.limpar(target);
            txtCns.limpar(target);
            txtCpf.limpar(target);
            txtAcs.limpar(target);
            cbxEquipeAcompanhamento.limpar(target);
        }

        if (usuarioCadsus != null) {
            usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperties(new HQLProperties(UsuarioCadsus.class).getProperties())
                    .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class, UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS).getProperties())
                    .setId(usuarioCadsus.getCodigo())
                    .start().getVO();

            if (getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus() == null) {
                getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().setUsuarioCadsus(usuarioCadsus);
            }

            fotoComponente.setUsuarioCadsus(usuarioCadsus);

            try {
                validarCepUsuarioCadSus(target, getForm().getModel().getObject().getConsultaUsuarioCadsusDTO());
                if (!pacienteSemCep) {
                    validarSituacaoPaciente(usuarioCadsus);
                    if (validarEndereco()) {
                        validaCadastroCompleto(getForm().getModel().getObject().getConsultaUsuarioCadsusDTO());
                    } else {
                        if (target != null && getPermissaoCadastrarPaciente()) {
                            mensagemPacienteSemEnderecoCadastrado(target);
                        } else {
                            throw new ValidacaoException(Bundle.getStringApplication("msg_usuario_sem_endereco"));
                        }
                    }
                }
            } catch (ValidacaoException | DAOException ex) {
                if (target != null) {
                    MessageUtil.modalWarn(target, AtendimentoUrgenciaAtencaoBasicaPage.this, ex);
                } else {
                    warn(ex.getMessage());
                }
                return;
            }
            try {
                UsuarioCadsusHelper.validarObrigatoriedadeRaca(usuarioCadsus);
            } catch (ValidacaoException ex) {
                if (target != null) {
                    MessageUtil.modalWarn(target, AtendimentoUrgenciaAtencaoBasicaPage.this, ex);
                    mensagemPacienteRacaInvalido(target);
                } else {
                    warn(ex.getMessage());
                }
                return;
            }

            getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus().setCelular(usuarioCadsus.getCelular());
            cnsUsuarioCadsus = usuarioCadsus.getCns() != null ? StringUtils.trimToNull(StringUtilKsi.getDigits(usuarioCadsus.getCns())) : null;

            if (usuarioCadsus.getEnderecoDomicilio() != null && usuarioCadsus.getEnderecoDomicilio().getCodigo() != null) {
                EnderecoDomicilio enderecoDomicilio = LoadManager.getInstance(EnderecoDomicilio.class)
                        .addProperty(EnderecoDomicilio.PROP_AREA)
                        .addProperty(EnderecoDomicilio.PROP_NUMERO_FAMILIA)
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_MICRO_AREA))
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_PROFISSIONAL, EquipeProfissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_EQUIPE_MICRO_AREA, EquipeMicroArea.PROP_EQUIPE_AREA, EquipeArea.PROP_CODIGO_AREA))
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_CIDADE, Cidade.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(EnderecoDomicilio.PROP_CIDADE, Cidade.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(EnderecoDomicilio.PROP_CODIGO, usuarioCadsus.getEnderecoDomicilio()
                                .getCodigo())).start().getVO();

                if (enderecoDomicilio != null) {
                    area = enderecoDomicilio.getEquipeMicroArea() != null ? enderecoDomicilio.getEquipeMicroArea().getEquipeArea().getDescricao() : null;
                    microArea = enderecoDomicilio.getEquipeMicroArea() != null ? enderecoDomicilio.getEquipeMicroArea().getMicroArea() : null;
                    nrFamilia = enderecoDomicilio.getNumeroFamilia();

                    EquipeMicroArea equipeMicroArea = enderecoDomicilio.getEquipeMicroArea();

                    if (equipeMicroArea != null && equipeMicroArea.getEquipeProfissional() != null && equipeMicroArea.getEquipeProfissional().getCodigo() != null) {

                        List<EquipeProfissional> epList = LoadManager.getInstance(EquipeProfissional.class)
                                .addProperties(new HQLProperties(Profissional.class, VOUtils.montarPath(EquipeProfissional.PROP_PROFISSIONAL)).getProperties())
                                .addProperties(new HQLProperties(Equipe.class, EquipeProfissional.PROP_EQUIPE).getProperties())
                                .addProperties(new HQLProperties(Empresa.class, VOUtils.montarPath(EquipeProfissional.PROP_EQUIPE, Equipe.PROP_EMPRESA)).getProperties())
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_CODIGO), equipeMicroArea.getEquipeProfissional().getCodigo()))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeProfissional.PROP_STATUS), EquipeProfissional.STATUS_ATIVO))
                                .start().getList();

                        if (!epList.isEmpty()) {
                            EquipeProfissional ep = epList.get(0);
                            acs = ep.getProfissional().getNome();
                            getForm().getModel().getObject().setUnidadeOrigem(ep.getEquipe().getEmpresa());
                        }
                    }
                }
            }
            EnderecoUsuarioCadsus enderecoUsuarioCadsus = usuarioCadsus.getEnderecoUsuarioCadsus();
            if (enderecoUsuarioCadsus != null) {
                enderecoUsuarioCadsus = LoadManager.getInstance(EnderecoUsuarioCadsus.class)
                        .addProperties(new HQLProperties(EnderecoUsuarioCadsus.class).getProperties())
                        .addProperties(new HQLProperties(Empresa.class, EnderecoUsuarioCadsus.PROP_EMPRESA).getProperties())
                        .addProperties(new HQLProperties(EnderecoEstruturado.class, EnderecoUsuarioCadsus.PROP_ENDERECO_ESTRUTURADO).getProperties())
                        .setId(enderecoUsuarioCadsus.getCodigo()).start().getVO();
            }
            if (getForm().getModel().getObject().getUnidadeOrigem() == null) {
                if (enderecoUsuarioCadsus != null) {
                    getForm().getModel().getObject().setUnidadeOrigem(enderecoUsuarioCadsus.getEmpresa());
                }
            }
            if (cidadeParametroEnderecoEstruturado != null
                    && enderecoUsuarioCadsus != null && enderecoUsuarioCadsus.getCidade() != null
                    && !cidadeParametroEnderecoEstruturado.getCodigo().equals(enderecoUsuarioCadsus.getCidade().getCodigo())) {
                cbxEquipeAcompanhamento.setVisible(false);
            } else {
                cbxEquipeAcompanhamento.setVisible(true);
                loadEquipes(enderecoUsuarioCadsus != null ? enderecoUsuarioCadsus.getCidade() : null);
                carregarEquipeAcompanhamento(usuarioCadsus, enderecoUsuarioCadsus != null ? enderecoUsuarioCadsus.getEnderecoEstruturado() : null);
                cbxEquipeAcompanhamento.setEnabled(!exigeEquipeAcompanhamento);
            }
        } else {
            fotoComponente.setUsuarioCadsus(new UsuarioCadsus());
        }

        if (target != null) {
            target.add(autoCompleteConsultaUsuarioCadsus);
            target.add(txtNrFamilia);
            target.add(txtArea);
            target.add(txtMicroarea);
            target.add(txtCelular);
            target.add(cbxEquipeAcompanhamento);
            target.appendJavaScript(JScript.initMasks());
            target.add(txtCns);
            target.add(txtCpf);
            target.add(txtAcs);
            target.add(fotoComponente);
        }
    }


    private void mensagemPacienteRacaInvalido(AjaxRequestTarget target) {
        if (dlgMensagemPacienteRacaInvalida == null) {
            String nomePaciente;
            try {
                nomePaciente = getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus().getDescricaoNomeFormatado();
            } catch (DAOException ex) {
                nomePaciente = getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus().getNome();
            }

            addModal(target, dlgMensagemPacienteRacaInvalida = new DlgConfirmacaoOk(newModalId(), Bundle.getStringApplication("msg_valida_cor_raca", nomePaciente)) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    setResponsePage(new CadastroPacientePage(getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus(), getForm().getModel().getObject(), true, false));
                }

            });
        }
        dlgMensagemPacienteRacaInvalida.show(target);
    }

    private void mensagemPacienteSemEnderecoCadastrado(AjaxRequestTarget target) {
        if (dlgMensagemPacienteSemEnderCadastrado == null) {
            addModal(target, dlgMensagemPacienteSemEnderCadastrado = new DlgConfirmacaoSimNao(newModalId(), bundle("pacienteSemEnderecoCadastrado")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    setResponsePage(new CadastroPacientePage(getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus(), getForm().getModel().getObject(), true, false));
                }
            });
        }
        dlgMensagemPacienteSemEnderCadastrado.show(target);
    }

    private boolean validarEndereco() throws DAOException, ValidacaoException {
        UsuarioCadsus usuarioCadsus = getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus();
        EnderecoUsuarioCadsus enderecoUsuarioCadsus = null;

        if (usuarioCadsus.getEnderecoDomicilio() != null) {
            enderecoUsuarioCadsus = usuarioCadsus.getEnderecoDomicilio().getEnderecoUsuarioCadsus();
        }
        UsuarioCadsus usuarioCadsusValidacao = null;
        if (enderecoUsuarioCadsus == null) {
            UsuarioCadsus proxy = on(UsuarioCadsus.class);

            usuarioCadsusValidacao = LoadManager.getInstance(UsuarioCadsus.class)
                    .addProperty(path(proxy.getCodigo()))
                    .addProperty(path(proxy.getEnderecoUsuarioCadsus().getCodigo()))
                    .setId(usuarioCadsus.getCodigo())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEnderecoUsuarioCadsus().getAtivo()), BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.ATIVO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.ATIVO))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEnderecoUsuarioCadsus().getExcluido()), BuilderQueryCustom.QueryParameter.IGUAL, RepositoryComponentDefault.NAO_EXCLUIDO, HQLHelper.NOT_RESOLVE_TYPE, RepositoryComponentDefault.NAO_EXCLUIDO))
                    .start().getVO();
        }

        return usuarioCadsusValidacao != null && usuarioCadsusValidacao.getEnderecoUsuarioCadsus() != null && usuarioCadsusValidacao.getEnderecoUsuarioCadsus().getCodigo() != null;
    }

    private void validaCadastroCompleto(ConsultaUsuarioCadsusDTO dto) throws DAOException, ValidacaoException {
        if (!RepositoryComponentDefault.SIM_LONG.equals(dto.getUsuarioCadsus().getFlagSimplificado()) &&
                (dto.getUsuarioCadsus().getDataCadastro() == null || !Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial().equals(Data.adjustRangeHour(dto.getUsuarioCadsus().getDataCadastro()).getDataInicial()))) {
            try {
                if (!existeCns(dto.getUsuarioCadsus().getCodigo())) {
                    // Valida obrigatoriedade dos documentos e se os documentos estão corretos
                    UsuarioCadsusHelper.validarIdentidadeCertidao(getDocumentosUsuarioCadsus(dto.getUsuarioCadsus()), true, false, false);
                }
            } catch (ValidacaoException ex) {
                throw new ValidacaoException(bundle("pacienteCadastroIncompleto") + " " + ex.getMessage());
            }
        }
    }

    private boolean existeCns(Long codigoPaciente) {
        if (cnsUsuarioCadsus != null) {
            return true;
        }

        Long count = LoadManager.getInstance(UsuarioCadsusCns.class)
                .addGroup(new QueryCustom.QueryCustomGroup(UsuarioCadsusCns.PROP_CODIGO, BuilderQueryCustom.QueryGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_USUARIO_CADSUS, codigoPaciente))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusCns.PROP_EXCLUIDO, RepositoryComponentDefault.NAO_EXCLUIDO))
                .start().getVO();
        return count > 0;
    }

    private List<UsuarioCadsusDocumento> getDocumentosUsuarioCadsus(UsuarioCadsus usuarioCadsus) {

        List<UsuarioCadsusDocumento> listaDeDocumentos = LoadManager.getInstance(UsuarioCadsusDocumento.class)
                .addProperties(new HQLProperties(UsuarioCadsusDocumento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDocumento.PROP_USUARIO_CADSUS), usuarioCadsus))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusDocumento.PROP_SITUACAO_EXCLUIDO), RepositoryComponentDefault.NAO_EXCLUIDO))
                .start().getList();

        return listaDeDocumentos;
    }

    private void validarCepUsuarioCadSus(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) throws ValidacaoException, DAOException {
        pacienteSemCep = false;
        CepBrasil cepBrasil = null;
        UsuarioCadsus usuarioCadsusCep = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(UsuarioCadsus.PROP_ENDERECO_USUARIO_CADSUS, EnderecoUsuarioCadsus.PROP_CEP))
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, dto.getUsuarioCadsus().getCodigo()))
                .start().getVO();

        if (usuarioCadsusCep != null) {
            if (usuarioCadsusCep.getEnderecoUsuarioCadsus() == null || usuarioCadsusCep.getEnderecoUsuarioCadsus().getCep() == null || usuarioCadsusCep.getEnderecoUsuarioCadsus().getCep().isEmpty()) {
                validacaoPacienteSemCep(target);
            } else {
                cepBrasil = LoadManager.getInstance(CepBrasil.class)
                        .addProperties(new HQLProperties(CepBrasil.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(CepBrasil.PROP_CEP, new Long(usuarioCadsusCep.getEnderecoUsuarioCadsus().getCep())))
                        .start().getVO();
            }
            if (cepBrasil == null) {
                validacaoPacienteSemCep(target);
            }
        }
    }

    private void validarSituacaoPaciente(UsuarioCadsus usuarioCadsus) throws ValidacaoException {
        if (UsuarioCadsus.SITUACAO_EXCLUIDO.equals(usuarioCadsus.getSituacao()) || UsuarioCadsus.SITUACAO_INATIVO.equals(usuarioCadsus.getSituacao())) {
            throw new ValidacaoException(BundleManager.getString("msgNaoPossivelMarcarAgendarPacienteSituacaoX", usuarioCadsus.getDescricaoSituacao()));
        }
    }

    private void validacaoPacienteSemCep(AjaxRequestTarget target) throws ValidacaoException {
        pacienteSemCep = true;
        if (target != null && getPermissaoCadastrarPaciente()) {
            mensagemPacienteCepInvalido(target);
        } else {
            throw new ValidacaoException(BundleManager.getString("PacienteCEPInvalidoOuNaoCadastradoFavorCadastrarParaPoderProsseguirComAtendimento"));
        }
    }

    private void mensagemPacienteCepInvalido(AjaxRequestTarget target) {
        if (dlgMensagemPacienteCepInvalido == null) {
            addModal(target, dlgMensagemPacienteCepInvalido = new DlgConfirmacaoSimNao(newModalId(), bundle("pacienteComcepInvalidoOuNaoCadastradoDesejaCadastrar")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    setResponsePage(new CadastroPacientePage(getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus(), getForm().getModel().getObject(), true, false));
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgMensagemPacienteCepInvalido.show(target);
    }

    private DropDown getDropDownTipoProcedimentoAtendimento(String path) {
        dropDownTipoProcedimentoAtendimento = new DropDown(path);

        List<TipoProcedimentoAtendimento> tipoList = LoadManager.getInstance(TipoProcedimentoAtendimento.class)
                .addProperties(new HQLProperties(TipoProcedimentoAtendimento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimentoAtendimento.PROP_FLAG_RESERVA, RepositoryComponentDefault.NAO_LONG))
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimentoAtendimento.PROP_FLAG_EXIBIR_PAGINA, BuilderQueryCustom.QueryParameter.IN, Arrays.asList(TipoProcedimentoAtendimento.ExibirPagina.AMBOS.value(), TipoProcedimentoAtendimento.ExibirPagina.ATENDIMENTO_ATENCAO_BASICA.value())))
                .addSorter(new QueryCustom.QueryCustomSorter(TipoProcedimentoAtendimento.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();

        dropDownTipoProcedimentoAtendimento.addChoice(null, "");
        for (TipoProcedimentoAtendimento tipoProcedimentoAtendimento : tipoList) {
            dropDownTipoProcedimentoAtendimento.addChoice(tipoProcedimentoAtendimento, tipoProcedimentoAtendimento.getDescricao());
        }
        return dropDownTipoProcedimentoAtendimento;
    }

    private Form<ConfirmacaoAtendimentoDTO> getForm() {
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(confirmacaoAtendimentoDTO != null ? confirmacaoAtendimentoDTO : new ConfirmacaoAtendimentoDTO()));
            getForm().getModel().getObject().setEstabelecimentoExecutante(ApplicationSession.get().getSessaoAplicacao().getEmpresa());

            if (parameters != null) {
                getForm().getModel().getObject().setParameters(parameters);
            }
            getFeedbackMessages().clear();
        }
        return form;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaProfissional.getTxtDescricao().getTextField();
    }

    private void atender(boolean inserirFila) throws DAOException, ValidacaoException {
        if (cnsUsuarioCadsus != null) {
            getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().setNumeroCartao(new Long(StringUtils.trimToNull(StringUtilKsi.getDigits(cnsUsuarioCadsus))));
        }
        getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus().setFoto(fotoComponente.getUsuarioCadsus() != null ? fotoComponente.getUsuarioCadsus().getFoto() : null);

        getForm().getModel().getObject().getConsultaUsuarioCadsusDTO().getUsuarioCadsus().setEquipe(equipeAcompanhamento != null ? equipeAcompanhamento : null);
        getForm().getModel().getObject().setNaturezaProcuraTipoAtendimento(getNaturezaProcuraTipoAtendimento());
        getForm().getModel().getObject().setInserirFilaAtendimento(inserirFila);
        getForm().getModel().getObject().setEquipe(equipe);
        getForm().getModel().getObject().setAtendimentoUrgencia(true);
        if (tabelaCbo != null) {
            getForm().getModel().getObject().setTabelaCbo(tabelaCbo);
        }

        Atendimento atendimento = BOFactoryWicket.getBO(AtendimentoFacade.class)
                .registrarAtendimentoUrgencia(getForm().getModel().getObject());

        if (inserirFila) {
            setResponsePage(new ConsultaAtendimentoAtencaoBasicaPage(getForm().getModel().getObject().getParameters()));
        } else {
            setResponsePage(new DefaultProntuarioPage(atendimento, new PageParameters(), true));
        }
    }

    private void populateDropDownCbo(Profissional object) {
        try {
            if (dropDownCbo == null) {
                dropDownCbo = new DropDown<>("cbo", new PropertyModel<>(this, "cbo"));
            }

            QueryConsultaProfissionalCargaHorariaDTOParam param = new QueryConsultaProfissionalCargaHorariaDTOParam();
            param.setEmpresa(getForm().getModel().getObject().getEstabelecimentoExecutante());
            param.setProfissional(object);
            if (getNaturezaProcuraTipoAtendimento().getTipoProcedimento() != null) {
                param.setProcedimento(getNaturezaProcuraTipoAtendimento().getTipoProcedimento().getProcedimento());
            }
            if (autoCompleteConsultaUsuarioCadsus.getComponentValue() != null) {
                UsuarioCadsus usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
                        .addProperty(UsuarioCadsus.PROP_CODIGO)
                        .addProperty(UsuarioCadsus.PROP_DATA_NASCIMENTO)
                        .addProperty(UsuarioCadsus.PROP_NOME)
                        .addProperty(UsuarioCadsus.PROP_APELIDO)
                        .addProperty(UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL)
                        .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsus.PROP_CODIGO, autoCompleteConsultaUsuarioCadsus.getComponentValue()))
                        .setMaxResults(1).start().getVO();
                param.setUsuarioCadsus(usuarioCadsus);
            }

            List<TabelaCbo> cbosProfissional = BOFactory.getBO(ProfissionalFacade.class).consultaCbosProfissional(param);
            List<TabelaCbo> tabelaCbosPermitidos = getTabelasCboPermitidasParaTipoAtendimento();
            List<String> cbosPermitidos = extract(tabelaCbosPermitidos, on(TabelaCbo.class).getCbo());

            List<TabelaCbo> cbos = select(cbosProfissional, having(on(TabelaCbo.class).getCbo(), isIn(cbosPermitidos)));

            if (!cbos.isEmpty() && cbos.size() >= 2) {
                dropDownCbo.setVisible(true);
                for (TabelaCbo cbo : cbos) {
                    dropDownCbo.addChoice(cbo, cbo.getDescricaoFormatado());
                }
            } else {
                dropDownCbo.setVisible(false);
            }
            dropDownCbo.addAjaxUpdateValue();
        } catch (DAOException e) {
            throw new RuntimeException(e);
        } catch (ValidacaoException e) {
            throw new RuntimeException(e);
        }
    }

    private List<TabelaCbo> getTabelasCboPermitidasParaTipoAtendimento() {
        List<TabelaCbo> tabelaCbos = null;
        List<EloTipoAtendimentoGrupoAtendimentoCbo> eloTipoAtendimentoGrupoAtendimentoCbos = getEloTipoAtendimentoGrupoAtendimentoCbos();

        if (!CollectionUtils.isEmpty(eloTipoAtendimentoGrupoAtendimentoCbos)) {
            List<GrupoAtendimentoCbo> grupoAtendimentoCbos = Lambda.extract(eloTipoAtendimentoGrupoAtendimentoCbos, on(EloTipoAtendimentoGrupoAtendimentoCbo.class).getGrupoAtendimentoCbo());
            List<EloGrupoAtendimentoCbo> eloGrupoAtendimentoCbos = getEloGrupoAtendimentoCbos(grupoAtendimentoCbos);

            if (!CollectionUtils.isEmpty(eloGrupoAtendimentoCbos)) {
                tabelaCbos = Lambda.extract(eloGrupoAtendimentoCbos, on(EloGrupoAtendimentoCbo.class).getTabelaCbo());
            }
        }

        return tabelaCbos;
    }

    private List<EloTipoAtendimentoGrupoAtendimentoCbo> getEloTipoAtendimentoGrupoAtendimentoCbos() {
        EloTipoAtendimentoGrupoAtendimentoCbo eloTipoAtendimentoGrupoAtendimentoCboProxy = on(EloTipoAtendimentoGrupoAtendimentoCbo.class);

        return LoadManager.getInstance(EloTipoAtendimentoGrupoAtendimentoCbo.class)
                .addProperty(path(eloTipoAtendimentoGrupoAtendimentoCboProxy.getCodigo()))
                .addProperty(path(eloTipoAtendimentoGrupoAtendimentoCboProxy.getGrupoAtendimentoCbo().getCodigo()))
                .addProperty(path(eloTipoAtendimentoGrupoAtendimentoCboProxy.getTipoAtendimento().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(eloTipoAtendimentoGrupoAtendimentoCboProxy.getTipoAtendimento().getCodigo()), getNaturezaProcuraTipoAtendimento().getTipoAtendimento()))
                .start()
                .getList();
    }

    private List<EloGrupoAtendimentoCbo> getEloGrupoAtendimentoCbos(List<GrupoAtendimentoCbo> grupoAtendimentoCbos) {
        EloGrupoAtendimentoCbo eloGrupoAtendimentoCboProxy = on(EloGrupoAtendimentoCbo.class);

        return LoadManager.getInstance(EloGrupoAtendimentoCbo.class)
                .addProperty(path(eloGrupoAtendimentoCboProxy.getTabelaCbo().getCbo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(eloGrupoAtendimentoCboProxy.getGrupoAtendimentoCbo()), BuilderQueryCustom.QueryParameter.IN, grupoAtendimentoCbos))
                .start()
                .getList();
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("atendimentoUrgencia");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    private NaturezaProcuraTipoAtendimento getNaturezaProcuraTipoAtendimento() {
        if (naturezaProcuraTipoAtendimento == null) {
            try {
                naturezaProcuraTipoAtendimento = BOFactory.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("NaturezaTipoAtendimentoUrgenciaAtencaoBasica");
            } catch (DAOException e) {
                warn(BundleManager.getString(e.getMessage()));
                btnCadPaciente.setEnabled(false);
                btnAtender.setEnabled(false);
                btnIncluirFila.setEnabled(false);
            }
        }
        return naturezaProcuraTipoAtendimento;
    }

    private boolean getPermissaoCadastrarPaciente() {
        return isActionPermitted(SessaoAplicacaoImp.getInstance().getUsuario(), Permissions.CADASTRAR, ConsultaAtendimentoAtencaoBasicaPage.class);
    }

    public void setResourceImage(AjaxRequestTarget target, String... param) {
        fotoComponente.setImageResource(target, param);
    }

    private void carregarEquipeAcompanhamento(UsuarioCadsus usuarioCadsus, EnderecoEstruturado enderecoEstruturado) {
        if (usuarioCadsus != null && usuarioCadsus.getEquipe() != null) {
            equipeAcompanhamento = usuarioCadsus.getEquipe();
        } else if (enderecoEstruturado != null && enderecoEstruturado.getEquipeMicroArea() != null && enderecoEstruturado.getEquipeMicroArea().getEquipeProfissional() != null) {
            equipeAcompanhamento = enderecoEstruturado.getEquipeMicroArea().getEquipeProfissional().getEquipe();
        }
    }

    private void loadEquipes(Cidade cidade) {
        cbxEquipeAcompanhamento.removeAllChoices();
        if (cidade != null) {
            Equipe equipeProxy = on(Equipe.class);

            List<Equipe> equipe = LoadManager.getInstance(Equipe.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProxy.getEquipeArea().getCidade().getCodigo()), cidade.getCodigo()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProxy.getTipoEquipe().getCodigo()), BuilderQueryCustom.QueryParameter.IN, Equipe.TIPOS_SAUDE_DA_FAMILIA))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(equipeProxy.getAtivo()), RepositoryComponentDefault.SIM))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(equipeProxy.getReferencia())))
                    .start().getList();

            cbxEquipeAcompanhamento.addChoice(null, "");
            if (CollectionUtils.isNotNullEmpty(equipe)) {
                for (Equipe equipeChild : equipe) {
                    cbxEquipeAcompanhamento.addChoice(equipeChild, equipeChild.getReferencia());
                }
            }
        }
    }

    private Usuario getUsuarioLogado() {
        if (usuarioLogado == null) {
            usuarioLogado = LoadManager.getInstance(Usuario.class)
                    .addProperties(new HQLProperties(Usuario.class).getProperties())
                    .addProperties(new HQLProperties(Profissional.class, Usuario.PROP_PROFISSIONAL).getProperties())
                    .setId(SessaoAplicacaoImp.getInstance().getUsuario().getCodigo())
                    .start().getVO();
        }
        return usuarioLogado;
    }
}