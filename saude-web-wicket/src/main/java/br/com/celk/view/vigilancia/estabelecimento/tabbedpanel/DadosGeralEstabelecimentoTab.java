package br.com.celk.view.vigilancia.estabelecimento.tabbedpanel;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.SimpleSelectionTable;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.service.web.vigilancia.InspecaoEstabelecimentoService;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.atividadeestabelecimento.PnlAtividadeEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.estabelecimento.dialog.DlgCadastroResponsavelTecnico;
import br.com.celk.view.vigilancia.responsaveltecnico.autocomplete.AutoCompleteConsultaResponsavelTecnico;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.CadastroEstabelecimentoDTO;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaEnum;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice;
import ch.lambdaj.Lambda;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.markup.html.form.RadioGroup;
import org.apache.wicket.markup.html.internal.HtmlHeaderContainer;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.ListIterator;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class DadosGeralEstabelecimentoTab extends TabPanel<CadastroEstabelecimentoDTO> {

    private ConfiguracaoVigilancia configuracaoVigilancia;
    private InspecaoEstabelecimentoService inspecaoEstabelecimentoService;
    private InputField<String> txtCnpjCpf;
    private DateChooser dcInicioFuncionamento;
    private CheckBoxSimNao chkPossuiEstabPrincipal;
    private String possuiEstabPrincipal;
    private DisabledInputField ultimaAlteracao;
    private InputField<String> txtRazaoSocial;
    private InputField<String> txtFantasia;
    private DropDown dropDownTipoEmpresa;
    private WebMarkupContainer containerIsencaoLei;
    private DropDown<Long> dropDownIsentoTaxa;
    private DropDown<Long> dropDownTipoPessoa;
    private DropDown<Long> dropDownIsentoPorLei;
    private DropDown<Long> dropDownPorteEmpresa;
    private InputField<String> porteEmpresaOutros;
    private AutoCompleteConsultaResponsavelTecnico autoCompleteConsultaResponsavel;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimentoPrincipal;
    private PnlAtividadeEstabelecimento pnlAtividadeEstabelecimento;
    private AttributeModifier attributeModifierCnpj = new AttributeModifier("class", "cnpj");
    private AttributeModifier attributeModifierCpf = new AttributeModifier("class", "cpf");
    private InputField txtDescricaoTipoEmpresa;
    private InputField txtDescricaoLeiNum;

    private DlgCadastroResponsavelTecnico dlgCadastroResponsavelTecnico;
    private AbstractAjaxLink btnCadadastroResponsavel;

    private Table<EstabelecimentoResponsavelTecnico> tblItens;
    private ResponsavelTecnico responsavelTecnico;

    private SimpleSelectionTable<EstabelecimentoAtividade> tblAtividades;
    private AtividadeEstabelecimento atividadeEstabelecimento;
    private WebMarkupContainer containerAtividades;
    private WebMarkupContainer containerTipoServico;
    private CompoundPropertyModel<EstabelecimentoAtividade> modelAtividades;
    private CompoundPropertyModel<EstabelecimentoTipoServicoAtividade> modelAtividadesTipoServico;
    private EstabelecimentoAtividade estabelecimentoAtividadeEdicao;
    private TaxaIndice taxaVigente;
    private Table tblTipoServico;

    private CheckBoxLongValue checkBoxFlagImprimirNoAlvara;

    private DoubleField txtQuantidadeTaxaAlvaraInicial;
    private String valorTaxaAlvaraInicial;
    private Label lbValorTaxaAlvaraInicial;

    private DoubleField txtQuantidadeTaxaAlvaraRevalidacao;
    private Label lbValorTaxaAlvaraRevalidacao;
    private String valorTaxaAlvaraRevalidacao;

    private DoubleField txtQuantidadeTaxa;
    private String valorTaxa;
    private String totalTaxa;
    private Label lblValorTaxa;
    private Label lblValorTotalTaxa;

    private String totalTaxaAlvaraInicial;
    private Label lblTotalTaxaAlvaraInicial;

    private String totalTaxaAlvaraRevalidacao;
    private Label lblTotalTaxaAlvaraRevalidacao;
    private Label labelCampoAtividade;
    private Label labelContainerAtividades;
    private Label labelOrgaoPublico;
    private boolean externo;
    private boolean linkAlvaraInicial;
    private DateChooser dcDataValidadeAlvara;
    private DateChooser dcDataValidadePrimeiroAlvara;

    private WebMarkupContainer containerClassificacaoRisco;
    private DisabledInputField inputClassificacaoRisco;
    private String classificacaoRisco;
    private String proximaInspecao;
    private String ultimaInspecao;

    private Estabelecimento estabelecimento;


    public DadosGeralEstabelecimentoTab(String id, CadastroEstabelecimentoDTO object, boolean externo, boolean linkAlvaraInicial) {
        super(id, object);

        this.externo = externo;
        this.linkAlvaraInicial = linkAlvaraInicial;
        responsavelTecnico = object.getResponsavelTecnico();
        this.estabelecimento = object.getEstabelecimento();
        inspecaoEstabelecimentoService = InspecaoEstabelecimentoService.getInstance();
        init();
        afterInit();
    }

    private void init() {
        CadastroEstabelecimentoDTO proxy = on(CadastroEstabelecimentoDTO.class);
        carregarConfigVigilancia();
        verificarAtividadesRT();
        dadosEstabelecimento(proxy);
        atividadeEstabelecimento();
        responsavelTecnico();
        validadeAlvara(proxy);

        if (object.getEstabelecimento().getEstabelecimentoPrincipal() != null) {
            chkPossuiEstabPrincipal.setComponentValue(RepositoryComponentDefault.SIM);
            autoCompleteConsultaEstabelecimentoPrincipal.setEnabled(true);
            txtCnpjCpf.setEnabled(false);
            txtCnpjCpf.setRequired(false);
            txtCnpjCpf.removeRequiredClass();
            dropDownTipoPessoa.setEnabled(false);
        }

        if (dropDownIsentoTaxa != null && object != null && object.getEstabelecimento() != null && object.getEstabelecimento().getFlagMicroEmpresa() != null) {
            dropDownIsentoTaxa.setComponentValue(object.getEstabelecimento().getFlagMicroEmpresa());
        }

        txtRazaoSocial.setLabel(new Model(bundle("razaoSocial")));
        txtCnpjCpf.setLabel(new Model(bundle("cnpjCpf")));
        pnlAtividadeEstabelecimento.setLabel(new Model(bundle("atividade")));
        dcInicioFuncionamento.setLabel(new Model(bundle("inicioFuncionamento")));
        autoCompleteConsultaEstabelecimentoPrincipal.setLabel(new Model(bundle("estabelecimentoPrincipal")));
        txtDescricaoLeiNum.setLabel(new Model("Lei nº"));
    }

    private void afterInit() {
        isentarEstabelecimento(null);
        selecionaAtividadePrincipal();
        calculaTaxaAtividadeEstabelecimento(null);
        calculaTaxaAlvaraInicial(null);
        calculaTaxaAlvaraRevalidacao(null);
        calcularTotalTaxas(null);
        configurarIsencaoLei(null);// aqui muda o isento
        configurarNumLei(null);
        setStatusDropDownIsentoTaxa(this.externo);
        enableTaxa(null);
        carregarRegrasComponentes();
    }

    private void carregarConfigVigilancia() {
        try {
            this.configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        } catch (ValidacaoException e) {
            Loggable.log.warn(e.getMessage(), e);
        }
    }

    private void estabelecimentoPrincipal(CadastroEstabelecimentoDTO proxy) {
        chkPossuiEstabPrincipal = new CheckBoxSimNao("possuiEstabPrincipal", new PropertyModel<String>(this, "possuiEstabPrincipal")) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (chkPossuiEstabPrincipal.getComponentValue() != null && RepositoryComponentDefault.NAO.equals(chkPossuiEstabPrincipal.getComponentValue())) {
                    autoCompleteConsultaEstabelecimentoPrincipal.setEnabled(false);
                    autoCompleteConsultaEstabelecimentoPrincipal.setRequired(false);
                    autoCompleteConsultaEstabelecimentoPrincipal.getTxtDescricao().removeRequiredClass();
                    txtCnpjCpf.setRequired(true);
                    txtCnpjCpf.addRequiredClass();
                    txtCnpjCpf.setEnabled(true);
                    dropDownTipoPessoa.setEnabled(true);

                    target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                    if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                        txtCnpjCpf.remove(attributeModifierCnpj);
                    }
                    if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                        txtCnpjCpf.limpar(target);
                        txtCnpjCpf.add(attributeModifierCpf);
                    }
                } else {
                    autoCompleteConsultaEstabelecimentoPrincipal.setEnabled(true);
                    autoCompleteConsultaEstabelecimentoPrincipal.setRequired(true);
                    autoCompleteConsultaEstabelecimentoPrincipal.getTxtDescricao().addRequiredClass();
                    txtCnpjCpf.setEnabled(false);
                    txtCnpjCpf.setRequired(false);
                    txtCnpjCpf.removeRequiredClass();
                    dropDownTipoPessoa.setEnabled(false);
                }
                autoCompleteConsultaEstabelecimentoPrincipal.limpar(target);
                txtCnpjCpf.limpar(target);
                dropDownTipoPessoa.limpar(target);
                target.add(txtCnpjCpf, autoCompleteConsultaEstabelecimentoPrincipal, dropDownTipoPessoa);
            }
        };

        autoCompleteConsultaEstabelecimentoPrincipal = new AutoCompleteConsultaEstabelecimento(path(proxy.getEstabelecimento().getEstabelecimentoPrincipal()));
        autoCompleteConsultaEstabelecimentoPrincipal.setEnabled(false);
        autoCompleteConsultaEstabelecimentoPrincipal.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                if (txtRazaoSocial.getComponentValue() == null) {
                    txtRazaoSocial.setComponentValue(object.getRazaoSocial());
                    target.add(txtRazaoSocial);
                }
                if (object.getCnpjCpf() != null) {
                    if (Estabelecimento.TipoPessoa.FISICA.value().equals(object.getTipoPessoa())) {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.remove(attributeModifierCnpj);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCpf);
                        }
                    } else {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.remove(attributeModifierCpf);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCnpj);
                        }
                    }

                    dropDownTipoPessoa.setComponentValue(object.getTipoPessoa());
                    txtCnpjCpf.setComponentValue(object.getCnpjCpf());
                    target.add(dropDownTipoPessoa, txtCnpjCpf);
                }
            }
        });

        add(chkPossuiEstabPrincipal, autoCompleteConsultaEstabelecimentoPrincipal);
    }

    private void dadosEstabelecimento(CadastroEstabelecimentoDTO proxy) {
        add(ultimaAlteracao = new DisabledInputField(path(proxy.getEstabelecimento().getUltimaAlteracao())));

        estabelecimentoPrincipal(proxy);

        add(txtRazaoSocial = new RequiredInputField<>(path(proxy.getEstabelecimento().getRazaoSocial())));
        add(txtFantasia = new InputField<>(path(proxy.getEstabelecimento().getFantasia())));
        add(dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getEstabelecimento().getTipoPessoa()), Estabelecimento.TipoPessoa.values(), false, true, false));
        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (getFormComponent().getModelObject() != null) {
                    if (Estabelecimento.TipoPessoa.FISICA.value().equals(getFormComponent().getModelObject())) {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('999.999.999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.remove(attributeModifierCnpj);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCpf);
                        }
                    } else {
                        target.appendJavaScript("$('#" + txtCnpjCpf.getMarkupId() + "').mask('99.999.999/9999-99');");
                        if (txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                            txtCnpjCpf.remove(attributeModifierCpf);
                        }
                        if (!txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                            txtCnpjCpf.limpar(target);
                            txtCnpjCpf.add(attributeModifierCnpj);
                        }
                    }
                    target.add(txtCnpjCpf);
                }
            }
        });

        add(txtCnpjCpf = new InputField<>(path(proxy.getEstabelecimento().getCnpjCpf())));
        this.txtCnpjCpf.add(this.attributeModifierCnpj);
        if (object != null && object.getEstabelecimento() != null && object.getEstabelecimento().getCodigo() != null) {
            if (Estabelecimento.TipoPessoa.JURIDICA.value().equals(object.getEstabelecimento().getTipoPessoa())) {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.remove(attributeModifierCpf);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.add(attributeModifierCnpj);
                }
            } else {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.remove(attributeModifierCnpj);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.add(attributeModifierCpf);
                }
            }
        }
        txtCnpjCpf.setRequired(true);
        txtCnpjCpf.addRequiredClass();

        add(DropDownUtil.getSimNaoLongDropDown(path(proxy.getEstabelecimento().getMatriz())));
        add(dcInicioFuncionamento = new DateChooser(path(proxy.getEstabelecimento().getDataInicioFuncionamento())));

        String visualizarCampoTipoEmpresaCompleto = RepositoryComponentDefault.SIM;
        try {
            visualizarCampoTipoEmpresaCompleto = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("visualizarCampoTipoEmpresaCompleto");
        } catch (DAOException e) {
            Loggable.log.error(e);
        }
        if (RepositoryComponentDefault.SIM.equals(visualizarCampoTipoEmpresaCompleto)) {
            Estabelecimento.TipoEmpresa[] values = Estabelecimento.TipoEmpresa.values();
            add(dropDownTipoEmpresa = DropDownUtil.getIEnumDropDown(path(proxy.getEstabelecimento().getTipoEmpresa()), values, true));
        } else {
            Estabelecimento.TipoEmpresaResumido[] values = Estabelecimento.TipoEmpresaResumido.values();
            add(dropDownTipoEmpresa = DropDownUtil.getIEnumDropDown(path(proxy.getEstabelecimento().getTipoEmpresa()), values, true));
        }

        dropDownTipoEmpresa.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (Estabelecimento.TipoEmpresa.OUTRO.value().equals(object.getEstabelecimento().getTipoEmpresa())) {
                    txtDescricaoTipoEmpresa.setEnabled(true);
                } else {
                    txtDescricaoTipoEmpresa.limpar(target);
                    txtDescricaoTipoEmpresa.setEnabled(false);
                }
                configurarIsencaoLei(target);
                target.add(txtDescricaoTipoEmpresa);
            }
        });

        add(txtDescricaoTipoEmpresa = new InputField(path(proxy.getEstabelecimento().getDescricaoTipoEmpresa())));
        if (Estabelecimento.TipoEmpresa.OUTRO.value().equals(object.getEstabelecimento().getTipoEmpresa())) {
            txtDescricaoTipoEmpresa.setEnabled(true);
        } else {
            txtDescricaoTipoEmpresa.setEnabled(false);
        }

        dropDownPorteEmpresa = DropDownUtil.getIEnumDropDown(path(proxy.getEstabelecimento().getTipoPorte()), Estabelecimento.TipoPorteEstabelecimento.getValuesWithoutMei(), true, true, false);
        dropDownPorteEmpresa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                porteEmpresaOutros.setEnabled(dropDownPorteEmpresa != null && dropDownPorteEmpresa.getComponentValue() != null && Estabelecimento.TipoPorteEstabelecimento.isOutro(dropDownPorteEmpresa.getComponentValue()));
                porteEmpresaOutros.limpar(target);
            }
        });
        porteEmpresaOutros = new InputField<>(path(proxy.getEstabelecimento().getTipoPorteOutros()));
        porteEmpresaOutros.setEnabled(false);
        add(dropDownPorteEmpresa, porteEmpresaOutros);


        containerIsencaoLei = new WebMarkupContainer("containerIsencaoLei");
        containerIsencaoLei.setOutputMarkupPlaceholderTag(true);

        containerIsencaoLei.add(dropDownIsentoPorLei = DropDownUtil.getNaoSimLongDropDown(path(proxy.getEstabelecimento().getFlagIsentoPorLei())));
        dropDownIsentoPorLei.setOutputMarkupPlaceholderTag(true);
        containerIsencaoLei.add(txtDescricaoLeiNum = new InputField(path(proxy.getEstabelecimento().getLeiNumero())));
        containerIsencaoLei.add(labelOrgaoPublico = new Label("labelOrgaoPublico", bundle("lbOrgaoPublico")));
        labelOrgaoPublico.setOutputMarkupId(true);
        add(containerIsencaoLei);
        dropDownIsentoPorLei.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                configurarNumLei(target);
                isentarEstabelecimento(target);
            }
        });
    }

    private void responsavelTecnico() {
        btnCadadastroResponsavel = new AbstractAjaxLink("btnCadadastroResponsavel") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarResponsavel(target);
            }
        };
        autoCompleteConsultaResponsavel = new AutoCompleteConsultaResponsavelTecnico("responsavelTecnico", new PropertyModel<ResponsavelTecnico>(this, "responsavelTecnico"));
        AbstractAjaxButton btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarResponsavelTecnico(target);
            }
        };
        btnAdicionar.setDefaultFormProcessing(false);

        tblItens = new Table("tblItens", getColumnsItens(), getCollectionProviderItens());
        tblItens.populate();

        add(btnCadadastroResponsavel, autoCompleteConsultaResponsavel, btnAdicionar, tblItens);
    }

    private void validadeAlvara(CadastroEstabelecimentoDTO proxy) {
        WebMarkupContainer containerValidadeAlvara = new WebMarkupContainer("containerValidadeAlvara");
        containerValidadeAlvara.setOutputMarkupPlaceholderTag(true);
        // A regra de desabilitar o campo abaixo serve para não deixar o usuário editar um alvará originado via sistema - #14235
        boolean existAlvaraSistema = false;
        if (object.getEstabelecimento() != null && object.getEstabelecimento().getCodigo() != null) {
            existAlvaraSistema = VigilanciaHelper.existAlvaraSistema(object.getEstabelecimento());
        }
        containerValidadeAlvara.add(dcDataValidadeAlvara = (DateChooser) new DateChooser(path(proxy.getEstabelecimento().getValidadeAlvara())).setEnabled(!existAlvaraSistema));
        dcDataValidadeAlvara.setOutputMarkupPlaceholderTag(true);
        dcDataValidadeAlvara.setOutputMarkupId(true);
        dcDataValidadeAlvara.addAjaxUpdateValue();
        dcDataValidadeAlvara.setLabel(new Model(bundle("validadeAlvara")));
        containerValidadeAlvara.add(dcDataValidadePrimeiroAlvara = new DateChooser(path(proxy.getEstabelecimento().getDataValidadePrimeiroAlvara())));
        dcDataValidadePrimeiroAlvara.addAjaxUpdateValue();
        dcDataValidadePrimeiroAlvara.setLabel(new Model(bundle("dataValidadePrimeiroAlvara")));

        this.dcDataValidadePrimeiroAlvara.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (object.getEstabelecimento() == null || object.getEstabelecimento().getCodigo() == null
                        || !VigilanciaHelper.existAlvaraSistema(object.getEstabelecimento())) {
                    dcDataValidadeAlvara.limpar(target);

                    if (dcDataValidadePrimeiroAlvara.getComponentValue() != null) {
                        dcDataValidadeAlvara.setComponentValue(dcDataValidadePrimeiroAlvara.getComponentValue());
                    }

                    target.add(dcDataValidadeAlvara);
                }
            }
        });

        containerValidadeAlvara.setVisible(externo && !linkAlvaraInicial);
        add(containerValidadeAlvara);
    }

    private void atividadeEstabelecimento() {
        EstabelecimentoAtividade proxyAtvd = on(EstabelecimentoAtividade.class);
        containerAtividades = new WebMarkupContainer("containerAtividades", modelAtividades = new CompoundPropertyModel<>(new EstabelecimentoAtividade()));
        containerAtividades.setOutputMarkupId(true);
        labelContainerAtividades = new Label("labelContainerAtividades", isGestaoAtividadeCnae() ? BundleManager.getString("cnae") : BundleManager.getString("atividades"));

        pnlAtividadeEstabelecimento = new PnlAtividadeEstabelecimento(path(proxyAtvd.getAtividadeEstabelecimento()));
        pnlAtividadeEstabelecimento.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dropDownIsentoTaxa.getComponentValue() != null) {
                    enableTaxa(target);
                }
            }
        });
        pnlAtividadeEstabelecimento.addAjaxUpdateValue();
        pnlAtividadeEstabelecimento.add(new ConsultaListener<AtividadeEstabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, AtividadeEstabelecimento object) {
                getTaxaVigente(target, object);
                enableTaxa(target);
            }
        });
        pnlAtividadeEstabelecimento.add(new RemoveListener<AtividadeEstabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, AtividadeEstabelecimento object) {
                taxaVigente = null;
                calculaTaxaAtividadeEstabelecimento(target);
                enableTaxa(target);
            }
        });

        dropDownIsentoTaxa = DropDownUtil.getNaoSimLongDropDown(path(proxyAtvd.getIsentoTaxa()), false);
        dropDownIsentoTaxa.addAjaxUpdateValue();
        dropDownIsentoTaxa.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableTaxa(target);
            }
        });
        addFieldsQuantidadeTaxaAlvara(proxyAtvd);

        checkBoxFlagImprimirNoAlvara = new CheckBoxLongValue(path(proxyAtvd.getImpressaoAlvara()));
        checkBoxFlagImprimirNoAlvara.addAjaxUpdateValue();
        checkBoxFlagImprimirNoAlvara.setOutputMarkupId(true);
        modelAtividades.getObject().setImpressaoAlvara(RepositoryComponentDefault.SIM_LONG);

        Label lblImprimirNoAlvara = new Label("lblImprimirAlvara", new Model<>(bundle("imprimirNoAlvara")));
        lblImprimirNoAlvara.add(new Tooltip().setText("mensagemCampoImprimirNoAlvara"));

        AbstractAjaxButton btnAdicionarAtvd = new AbstractAjaxButton("btnAdicionarAtvd") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarAtividade(target);
            }
        };
        btnAdicionarAtvd.setDefaultFormProcessing(false);

        tblAtividades = new SimpleSelectionTable("tblAtividades", getColumnsItensAtvd(), getCollectionProviderItensAtvd());
        tblAtividades.addSelectionAction(new ISelectionAction<EstabelecimentoAtividade>() {
            @Override
            public void onSelection(AjaxRequestTarget target, EstabelecimentoAtividade estabelecimentoAtividade) {
                for (EstabelecimentoAtividade ea : object.getEstabelecimentoAtividadeList()) {
                    if (ea == estabelecimentoAtividade) {
                        ea.setFlagPrincipal(RepositoryComponentDefault.SIM_LONG);
                    } else {
                        ea.setFlagPrincipal(RepositoryComponentDefault.NAO_LONG);
                    }
                }
            }
        });
        tblAtividades.populate();

        lblValorTotalTaxa = new Label("totalTaxa", new PropertyModel<String>(this, "totalTaxa"));
        lblValorTotalTaxa.setOutputMarkupId(true);

        lblTotalTaxaAlvaraInicial = new Label("totalAlvaraInicial", new PropertyModel<String>(this, "totalTaxaAlvaraInicial"));
        lblTotalTaxaAlvaraInicial.setOutputMarkupId(true);

        lblTotalTaxaAlvaraRevalidacao = new Label("totalAlvaraRevalidacao", new PropertyModel<String>(this, "totalTaxaAlvaraRevalidacao"));
        lblTotalTaxaAlvaraRevalidacao.setOutputMarkupId(true);

        containerAtividades.add(new AbstractAjaxButton("btnLimparSelecao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isNotNullEmpty(object.getEstabelecimentoAtividadeList())) {
                    Lambda.forEach(object.getEstabelecimentoAtividadeList()).setFlagPrincipal(RepositoryComponentDefault.NAO_LONG);
                }
                tblAtividades.clearSelection(target);
                ((RadioGroup) tblAtividades.getBody()).setModelObject(null);
                tblAtividades.update(target);
                tblAtividades.populate(target);
            }
        });

        containerTipoServico = new WebMarkupContainer("containerTipoServico", modelAtividadesTipoServico = new CompoundPropertyModel<>(new EstabelecimentoTipoServicoAtividade()));
        containerTipoServico.setOutputMarkupId(true);

        tblTipoServico = new Table("tblTipoServico", getColumnsTipoServico(), getCollectionProviderTipoServico());
        tblTipoServico.populate();
        tblTipoServico.setScrollY("1800");
        containerTipoServico.add(tblTipoServico);
        containerTipoServico.add(new AbstractAjaxButton("btnRecarregar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                recarregarTiposServicosAtividades(target);
            }
        }.add(new Tooltip().setText("msgRecarregarTiposServicosAtividades")));

        containerAtividades.add(labelContainerAtividades, pnlAtividadeEstabelecimento, checkBoxFlagImprimirNoAlvara,
                lblImprimirNoAlvara, tblAtividades, btnAdicionarAtvd, dropDownIsentoTaxa,
                lblValorTotalTaxa, lblTotalTaxaAlvaraInicial, lblTotalTaxaAlvaraRevalidacao,
                containerTipoServico);
        add(containerAtividades, getContainerClassificacaoRisco());
    }

    private void carregarRegrasComponentes() {
        porteEmpresaOutros.setEnabled(estabelecimento != null && estabelecimento.getTipoPorte() != null && Estabelecimento.TipoPorteEstabelecimento.isOutro(estabelecimento.getTipoPorte()));
        configurarCampoEstabelecimentoPrincipal(configuracaoVigilancia);
    }

    private void configurarCampoEstabelecimentoPrincipal(ConfiguracaoVigilancia configuracaoVigilancia) {
        if (externo) {
            chkPossuiEstabPrincipal.setEnabled(RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagHabilitaCampoPossuiEstabPrincipal()));
        }
    }

    public WebMarkupContainer getContainerClassificacaoRisco() {
        containerClassificacaoRisco = new WebMarkupContainer("containerClassificacaoRisco");
        containerClassificacaoRisco.setOutputMarkupPlaceholderTag(true);

        inputClassificacaoRisco = new DisabledInputField<String>("classificacaoRisco", new PropertyModel<String>(this, "classificacaoRisco"));
        DisabledInputField inputProximaInspecao = new DisabledInputField<String>("proximaInspecao", new PropertyModel<String>(this, "proximaInspecao"));
        DisabledInputField inputUltimaInspecao = new DisabledInputField<String>("ultimaInspecao", new PropertyModel<String>(this, "ultimaInspecao"));

        containerClassificacaoRisco.add(inputClassificacaoRisco);
        containerClassificacaoRisco.add(inputProximaInspecao);
        containerClassificacaoRisco.add(inputUltimaInspecao);

        carregarInformacoesCLassificacaoRiscoInspecao();

        return containerClassificacaoRisco;
    }

    private void configurarNumLei(AjaxRequestTarget target) {
        if (RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagIsentoPorLei())
                && Estabelecimento.TipoEmpresa.PRIVADA.value().equals(object.getEstabelecimento().getTipoEmpresa())) {
            txtDescricaoLeiNum.addRequiredClass();
            txtDescricaoLeiNum.setRequired(true);
            txtDescricaoLeiNum.setEnabled(true);
        } else {
            txtDescricaoLeiNum.setRequired(false);
            txtDescricaoLeiNum.setEnabled(false);
            txtDescricaoLeiNum.removeRequiredClass();
            if (target != null) {
                txtDescricaoLeiNum.limpar(target);
            }
        }
        if (target != null) {
            target.add(txtDescricaoLeiNum);
        }
    }

    private void configurarIsencaoLei(AjaxRequestTarget target) {
        if (Estabelecimento.TipoEmpresa.PUBLICO.value().equals(object.getEstabelecimento().getTipoEmpresa())) {
            if (target != null) {
                object.getEstabelecimento().setFlagIsentoPorLei(RepositoryComponentDefault.SIM_LONG);
            }
            containerIsencaoLei.setVisible(true);
            setStatusDropDownIsentoTaxa(this.externo);
            txtDescricaoLeiNum.setEnabled(false);
            labelOrgaoPublico.setVisible(true);
        } else if (Estabelecimento.TipoEmpresa.PRIVADA.value().equals(object.getEstabelecimento().getTipoEmpresa()) && RepositoryComponentDefault.NAO_LONG.equals(object.getEstabelecimento().getFlagMicroEmpresa())) {
            if (target != null) {
                object.getEstabelecimento().setFlagIsentoPorLei(RepositoryComponentDefault.NAO_LONG);
            }
            setStatusDropDownIsentoTaxa(this.externo);
            containerIsencaoLei.setVisible(true);
            txtDescricaoLeiNum.setEnabled(false);
            labelOrgaoPublico.setVisible(false);
            dropDownIsentoPorLei.add(new Tooltip().setText("msgTooltipIsentoPorLei"));
        } else {
            object.getEstabelecimento().setFlagIsentoPorLei(RepositoryComponentDefault.NAO_LONG);
            containerIsencaoLei.setVisible(false);
            txtDescricaoLeiNum.setEnabled(false);
            setStatusDropDownIsentoTaxa(this.externo);

            if (target != null) {
                txtDescricaoLeiNum.limpar(target);
            }
        }
        if (target != null) {
            target.add(containerIsencaoLei, txtDescricaoLeiNum, labelOrgaoPublico,
                    txtDescricaoLeiNum, dropDownIsentoPorLei, dropDownIsentoTaxa);
        }
        configurarNumLei(target);
    }

    private void setStatusDropDownIsentoTaxa(boolean externo) {
        if (RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagMicroEmpresa())
                || RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagIsentoPorLei())) {
            dropDownIsentoTaxa.setComponentValue(RepositoryComponentDefault.SIM_LONG);
            dropDownIsentoTaxa.setEnabled(false);
        } else {
            dropDownIsentoTaxa.setComponentValue(RepositoryComponentDefault.NAO_LONG);
            dropDownIsentoTaxa.setEnabled(true);
        }
        if (externo) {
            dropDownIsentoTaxa.setEnabled(false);
        }
    }

    private void isentarEstabelecimento(AjaxRequestTarget target) {
        Integer idx = null;
        if (RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagIsentoPorLei())
                || RepositoryComponentDefault.SIM_LONG.equals(object.getEstabelecimento().getFlagMicroEmpresa())) {
            for (idx = 0; idx < object.getEstabelecimentoAtividadeList().size(); idx++) {
                object.getEstabelecimentoAtividadeList().get(idx).setIsentoTaxa(RepositoryComponentDefault.SIM_LONG);
            }
            dropDownIsentoTaxa.setComponentValue(RepositoryComponentDefault.SIM_LONG);
            dropDownIsentoTaxa.setEnabled(false);
            enableTaxa(target);
        } else {
            for (idx = 0; idx < object.getEstabelecimentoAtividadeList().size(); idx++) {
                object.getEstabelecimentoAtividadeList().get(idx).setIsentoTaxa(RepositoryComponentDefault.NAO_LONG);
            }
            dropDownIsentoTaxa.setComponentValue(RepositoryComponentDefault.NAO_LONG);
            dropDownIsentoTaxa.setEnabled(true);
        }
        if (this.externo) {
            dropDownIsentoTaxa.setEnabled(false);
        }
        if (target != null) {
            target.add(dropDownIsentoTaxa, tblAtividades);
        }
        calcularTotalTaxas(target);
        tblAtividades.populate();
    }

    private List<IColumn> getColumnsTipoServico() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EstabelecimentoTipoServicoAtividade proxy = on(EstabelecimentoTipoServicoAtividade.class);

        columns.add(getActionColumnTipoServico());
        columns.add(createColumn(bundle("cnae"), proxy.getAtividadeEstabelecimento().getDescricao()));
        columns.add(createColumn(bundle("tipo"), proxy.getAtividadeEstabelecimentoTipoServico().getTipoServico()));

        return columns;
    }

    private IColumn getActionColumnTipoServico() {
        return new MultipleActionCustomColumn<EstabelecimentoTipoServicoAtividade>() {
            @Override
            public void customizeColumn(EstabelecimentoTipoServicoAtividade rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EstabelecimentoTipoServicoAtividade>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoTipoServicoAtividade modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblTipoServico, object.getEstabelecimentoTipoServicoAtividadeList(), modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderTipoServico() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (object.getEstabelecimentoTipoServicoAtividadeList() == null) {
                    object.setEstabelecimentoTipoServicoAtividadeList(new ArrayList<EstabelecimentoTipoServicoAtividade>());
                }
                return object.getEstabelecimentoTipoServicoAtividadeList();
            }
        };
    }

    private void addFieldsQuantidadeTaxaAlvara(EstabelecimentoAtividade proxyAtvd) {
        txtQuantidadeTaxa = new DoubleField(path(proxyAtvd.getQuantidadeTaxa()));
        txtQuantidadeTaxa.setMDec(4).addAjaxUpdateValue();
        txtQuantidadeTaxa.setEnabled(false);
        txtQuantidadeTaxa.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (txtQuantidadeTaxa != null) {
                    calculaTaxaAtividadeEstabelecimento(target);
                }
            }
        });
        txtQuantidadeTaxa.add(new Tooltip().setText("msgQuantidadeTaxaAtividade"));

        lblValorTaxa = new Label("valorTaxa", new PropertyModel<String>(this, "valorTaxa"));
        lblValorTaxa.setOutputMarkupId(true);


        txtQuantidadeTaxaAlvaraInicial = new DoubleField(path(proxyAtvd.getQuantidadeTaxaAlvaraInicial()));
        txtQuantidadeTaxaAlvaraInicial.setMDec(4).addAjaxUpdateValue();
        txtQuantidadeTaxaAlvaraInicial.setEnabled(false);
        txtQuantidadeTaxaAlvaraInicial.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (txtQuantidadeTaxaAlvaraInicial != null) {
                    calculaTaxaAlvaraInicial(target);
                }
            }
        });
        txtQuantidadeTaxaAlvaraInicial.add(new Tooltip().setText("msgTaxaExtraAlvaraInicial"));
        lbValorTaxaAlvaraInicial = new Label("valorTaxaAlvaraInicial", new PropertyModel<String>(this, "valorTaxaAlvaraInicial"));
        lbValorTaxaAlvaraInicial.setOutputMarkupId(true);


        txtQuantidadeTaxaAlvaraRevalidacao = new DoubleField(path(proxyAtvd.getQuantidadeTaxaAlvaraRevalidacao()));
        txtQuantidadeTaxaAlvaraRevalidacao.setMDec(4).addAjaxUpdateValue();
        txtQuantidadeTaxaAlvaraRevalidacao.setEnabled(false);
        txtQuantidadeTaxaAlvaraRevalidacao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONBLUR) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (txtQuantidadeTaxaAlvaraRevalidacao != null) {
                    calculaTaxaAlvaraRevalidacao(target);
                }
            }
        });
        txtQuantidadeTaxaAlvaraRevalidacao.add(new Tooltip().setText("msgTaxaExtraRevalidacaoAlvara"));


        lbValorTaxaAlvaraRevalidacao = new Label("valorTaxaAlvaraRevalidacao", new PropertyModel<String>(this, "valorTaxaAlvaraRevalidacao"));
        lbValorTaxaAlvaraRevalidacao.setOutputMarkupId(true);

        containerAtividades.add(txtQuantidadeTaxa, lblValorTaxa, txtQuantidadeTaxaAlvaraInicial, lbValorTaxaAlvaraInicial,
                txtQuantidadeTaxaAlvaraRevalidacao, lbValorTaxaAlvaraRevalidacao
        );
    }

    private void selecionaAtividadePrincipal() {
        for (EstabelecimentoAtividade ea : object.getEstabelecimentoAtividadeList()) {
            if (RepositoryComponentDefault.SIM_LONG.equals(ea.getFlagPrincipal())) {
                tblAtividades.setSelectedObject(ea);
            }
        }
    }

    private void verificarAtividadesRT() {
        if (CollectionUtils.isNotNullEmpty(object.getEstabelecimentoAtividadeList())) {
            for (EstabelecimentoAtividade atividade : object.getEstabelecimentoAtividadeList()) {
                if (RepositoryComponentDefault.SIM_LONG.equals(atividade.getAtividadeEstabelecimento().getExigeResponsavelTecnico())) {
                    if (CollectionUtils.isAllEmpty(object.getLstEstabelecimentoResponsavelTecnico())) {
                        warn(bundle("msgExisteAtividadeQueExigeResponsavelTecnico"));
                    }
                }
            }
        }
    }

    private void cadastrarResponsavel(AjaxRequestTarget target) {
        if (dlgCadastroResponsavelTecnico == null) {
            WindowUtil.addModal(target, this, dlgCadastroResponsavelTecnico = new DlgCadastroResponsavelTecnico(WindowUtil.newModalId(this)));
            dlgCadastroResponsavelTecnico.add(new ICadastroListener<ResponsavelTecnico>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, ResponsavelTecnico responsavelTecnico) throws ValidacaoException, DAOException {
                    autoCompleteConsultaResponsavel.limpar(target);
                    autoCompleteConsultaResponsavel.setComponentValue(responsavelTecnico);
                    target.add(autoCompleteConsultaResponsavel);
                }
            });
        }
        dlgCadastroResponsavelTecnico.limpar(target);
        dlgCadastroResponsavelTecnico.show(target);
    }

    private List<IColumn> getColumnsItens() {
        List<IColumn> columns = new ArrayList<IColumn>();

        EstabelecimentoResponsavelTecnico proxy = on(EstabelecimentoResponsavelTecnico.class);

        columns.add(getActionColumnResponsavelTecnico());
        columns.add(createColumn(bundle("nome"), proxy.getResponsavelTecnico().getNome()));

        return columns;
    }

    private List<IColumn> getColumnsItensAtvd() {
        List<IColumn> columns = new ArrayList<IColumn>();

        EstabelecimentoAtividade proxy = on(EstabelecimentoAtividade.class);

        columns.add(getActionColumnAtvd());
        columns.add(createColumn(VigilanciaHelper.isGestaoAtividadeCnae() ? bundle("cnae") : bundle("atividade"), proxy.getAtividadeEstabelecimento().getDescricao()));
        columns.add(createColumn(bundle("isentoTaxa"), proxy.getDescricaoFormatadaIsentoTaxa()));
        columns.add(createColumn(bundle("imprimirNoAlvara"), proxy.getImpressaoAlvaraFormatado()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidadeTaxa()));
        columns.add(createColumn(bundle("valorTaxa"), proxy.getDescricaoCalculoTaxa()));
        columns.add(createColumn(bundle("taxaAlvaraInicial"), proxy.getDescricaoCalculoTaxaAlvaraInicial()));
        columns.add(createColumn(bundle("taxaRevalidacaoAlvara"), proxy.getDescricaoCalculoTaxaAlvaraRevalidacao()));
        return columns;
    }

    private IColumn getActionColumnResponsavelTecnico() {
        return new MultipleActionCustomColumn<EstabelecimentoResponsavelTecnico>() {
            @Override
            public void customizeColumn(EstabelecimentoResponsavelTecnico rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EstabelecimentoResponsavelTecnico>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoResponsavelTecnico modelObject) throws ValidacaoException, DAOException {
                        removerResponsavelTecnico(target, modelObject);
                    }
                });
            }
        };
    }

    private IColumn getActionColumnAtvd() {
        return new MultipleActionCustomColumn<EstabelecimentoAtividade>() {
            @Override
            public void customizeColumn(EstabelecimentoAtividade rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EstabelecimentoAtividade>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoAtividade modelObject) throws ValidacaoException, DAOException {
                        editarAtividade(target, modelObject);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EstabelecimentoAtividade>() {
                    @Override
                    public void action(AjaxRequestTarget target, EstabelecimentoAtividade eAtividade) throws ValidacaoException, DAOException {
                        if (object != null && object.getEstabelecimento() != null && object.getEstabelecimento().getCodigo() != null) {
                            RequerimentoVigilancia rv = VigilanciaHelper.carregarAlvaraInicialDeferidoPorEstabelecimento(object.getEstabelecimento());
                            if (!isAlvaraDeferidoNesseEstabelecimento(rv)) {
                                remocaoAutorizada(target, eAtividade);
                            } else {
                                ConfiguracaoVigilanciaEnum.TipoGestaoAtividade tipoGestaoAtividade =
                                        ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.valeuOf(configuracaoVigilancia.getFlagTipoGestaoAtividade());
                                throw new ValidacaoException(bundle("Alteracao_atividade_economica", rv.getProtocoloFormatado(), tipoGestaoAtividade.descricao()));
                            }
                        } else if (object != null && object.getEstabelecimento() != null && object.getEstabelecimento().getCodigo() == null) {
                            //Para remover atividades quando o estabelecimento não estiver cadastrado
                            remocaoAutorizada(target, eAtividade);
                        }
                    }
                }).setQuestionDialogBundleKey("desejaRealmenteExcluirAtividadeEstabelecimento");
            }
        };
    }

    private boolean isAlvaraDeferidoNesseEstabelecimento(RequerimentoVigilancia requerimentoVigilancia) {
        return requerimentoVigilancia != null && requerimentoVigilancia.getCodigo() != null;
    }

    private void remocaoAutorizada(AjaxRequestTarget target, EstabelecimentoAtividade modelObject) throws DAOException, ValidacaoException {
        CrudUtils.removerItem(target, tblAtividades, object.getEstabelecimentoAtividadeList(), modelObject);
        EstabelecimentoAtividade objSelecionado = tblAtividades.getSelectedObject();
        tblAtividades.update(target);
        tblAtividades.setSelectedObject(objSelecionado);
        removerAtividadeTipoServico(target, modelObject);

        calcularTotalTaxas(target);
        carregarInformacoesCLassificacaoRiscoInspecao();
        target.add(containerClassificacaoRisco);
    }

    private void editarAtividade(AjaxRequestTarget target, EstabelecimentoAtividade modelObject) {
        limparAtividade(target);
        estabelecimentoAtividadeEdicao = modelObject;
        modelAtividades.setObject((EstabelecimentoAtividade) SerializationUtils.clone(modelObject));
        target.add(containerAtividades);
        getTaxaVigente(target, modelObject.getAtividadeEstabelecimento());
        enableTaxa(target);
    }

    private void enableTaxa(AjaxRequestTarget target) {
        txtQuantidadeTaxa.setComponentValue(1D);
        txtQuantidadeTaxaAlvaraRevalidacao.setComponentValue(1D);
        txtQuantidadeTaxaAlvaraInicial.setComponentValue(1D);

        if (dropDownIsentoTaxa.getComponentValue() != null && RepositoryComponentDefault.SIM_LONG.equals(dropDownIsentoTaxa.getComponentValue())) {
            txtQuantidadeTaxa.setEnabled(false);
            txtQuantidadeTaxaAlvaraRevalidacao.setEnabled(false);
            txtQuantidadeTaxaAlvaraInicial.setEnabled(false);

        } else if (RepositoryComponentDefault.NAO_LONG.equals(dropDownIsentoTaxa.getComponentValue())) {
            if (target != null) {
                txtQuantidadeTaxa.limpar(target);
                txtQuantidadeTaxaAlvaraRevalidacao.limpar(target);
                txtQuantidadeTaxaAlvaraInicial.limpar(target);
            }

            txtQuantidadeTaxa.setEnabled(true);
            txtQuantidadeTaxaAlvaraRevalidacao.setEnabled(true);
            txtQuantidadeTaxaAlvaraInicial.setEnabled(true);

        } else {
            txtQuantidadeTaxa.setEnabled(false);
            txtQuantidadeTaxaAlvaraRevalidacao.setEnabled(false);
            txtQuantidadeTaxaAlvaraInicial.setEnabled(false);
        }

        txtQuantidadeTaxa.setComponentValue(1D);
        txtQuantidadeTaxaAlvaraRevalidacao.setComponentValue(1D);
        txtQuantidadeTaxaAlvaraInicial.setComponentValue(1D);

        if (target != null) {
            target.add(txtQuantidadeTaxa, txtQuantidadeTaxaAlvaraRevalidacao, txtQuantidadeTaxaAlvaraInicial, lblValorTaxa, lbValorTaxaAlvaraInicial, lbValorTaxaAlvaraRevalidacao);

            calculaTaxaAtividadeEstabelecimento(target);
            calculaTaxaAlvaraInicial(target);
            calculaTaxaAlvaraRevalidacao(target);
        }
    }

    private String calcularTaxa(Double taxaVigente, Double qtdTaxa, Double qtdTaxaAtividade) {
        Double valorTaxaVigente = Coalesce.asDouble(taxaVigente, 0D);
        Double quantidadeTaxa = Coalesce.asDouble(qtdTaxa, 0D);
        Double quantidadeTaxaAtividade = Coalesce.asDouble(qtdTaxaAtividade, 0D);

        BigDecimal totalTaxa = new BigDecimal(0);
        NumberFormat nf = NumberFormat.getCurrencyInstance();

        totalTaxa = new Dinheiro(quantidadeTaxa, 4).multiplicar(quantidadeTaxaAtividade, 4).multiplicar(valorTaxaVigente, 4).bigDecimalValue();

        String valor = nf.format(totalTaxa);

        return valor;
    }

    private void calculaTaxaAtividadeEstabelecimento(AjaxRequestTarget target) {
        if (taxaVigente != null && txtQuantidadeTaxa != null && atividadeEstabelecimento != null) {
            valorTaxa = calcularTaxa(taxaVigente.getValorIndice(), txtQuantidadeTaxa.getComponentValue(), atividadeEstabelecimento.getQuantidadeTaxa());
        } else {
            valorTaxa = calcularTaxa(0D, 0D, 0D);
        }

        if (target != null) {
            target.add(lblValorTaxa);
        }
    }

    private void calculaTaxaAlvaraInicial(AjaxRequestTarget target) {
        if (taxaVigente != null && txtQuantidadeTaxaAlvaraInicial != null && atividadeEstabelecimento != null) {
            valorTaxaAlvaraInicial = calcularTaxa(taxaVigente.getValorIndice(), txtQuantidadeTaxaAlvaraInicial.getComponentValue(), atividadeEstabelecimento.getQuantidadeTaxaAlvaraInicial());
        } else {
            valorTaxaAlvaraInicial = calcularTaxa(0D, 0D, 0D);
        }

        if (target != null) {
            target.add(lbValorTaxaAlvaraInicial);
        }
    }

    private void calculaTaxaAlvaraRevalidacao(AjaxRequestTarget target) {
        if (taxaVigente != null && txtQuantidadeTaxaAlvaraRevalidacao != null && atividadeEstabelecimento != null) {
            valorTaxaAlvaraRevalidacao = calcularTaxa(taxaVigente.getValorIndice(), txtQuantidadeTaxaAlvaraRevalidacao.getComponentValue(), atividadeEstabelecimento.getQuantidadeTaxaAlvaraRevalidacao());
        } else {
            valorTaxaAlvaraRevalidacao = calcularTaxa(0D, 0D, 0D);
        }

        if (target != null) {
            target.add(lbValorTaxaAlvaraRevalidacao);
        }
    }

    private void getTaxaVigente(AjaxRequestTarget target, AtividadeEstabelecimento atividadeEstabelecimento) {
        this.atividadeEstabelecimento = LoadManager.getInstance(AtividadeEstabelecimento.class)
                .setId(atividadeEstabelecimento.getCodigo())
                .start().getVO();

        try {
            taxaVigente = FinanceiroVigilanciaHelper.getTaxaIndiceVigente(atividadeEstabelecimento.getTaxa());
        } catch (Exception e) {
            Loggable.log.info(e.getMessage());
        }

        calculaTaxaAtividadeEstabelecimento(target);
    }

    private void limparAtividade(AjaxRequestTarget target) {
        modelAtividades.setObject(new EstabelecimentoAtividade());
        estabelecimentoAtividadeEdicao = null;
        dropDownIsentoTaxa.limpar(target);

        txtQuantidadeTaxa.limpar(target);
        txtQuantidadeTaxa.add(new Tooltip().setText("msgQuantidadeTaxaAtividade"));

        txtQuantidadeTaxaAlvaraInicial.limpar(target);
        txtQuantidadeTaxaAlvaraInicial.add(new Tooltip().setText("msgTaxaExtraAlvaraInicial"));

        txtQuantidadeTaxaAlvaraRevalidacao.limpar(target);
        txtQuantidadeTaxaAlvaraRevalidacao.add(new Tooltip().setText("msgTaxaExtraRevalidacaoAlvara"));

        txtQuantidadeTaxa.setComponentValue(1D);
        txtQuantidadeTaxaAlvaraRevalidacao.setComponentValue(1D);
        txtQuantidadeTaxaAlvaraInicial.setComponentValue(1D);

        pnlAtividadeEstabelecimento.limpar(target);
        checkBoxFlagImprimirNoAlvara.limpar(target);
        modelAtividades.getObject().setImpressaoAlvara(RepositoryComponentDefault.SIM_LONG);

        target.add(containerAtividades, pnlAtividadeEstabelecimento, checkBoxFlagImprimirNoAlvara);

        calculaTaxaAtividadeEstabelecimento(target);
        calculaTaxaAlvaraInicial(target);
        calculaTaxaAlvaraRevalidacao(target);
        setStatusDropDownIsentoTaxa(this.externo);
    }

    private void removerResponsavelTecnico(AjaxRequestTarget target, EstabelecimentoResponsavelTecnico modelObject) {
        ListIterator<EstabelecimentoResponsavelTecnico> listIterator = object.getLstEstabelecimentoResponsavelTecnico().listIterator();
        while (listIterator.hasNext()) {
            EstabelecimentoResponsavelTecnico next = listIterator.next();
            if (next == modelObject) {
                listIterator.remove();
                break;
            }
        }
        tblItens.update(target);
    }

    private void adicionarResponsavelTecnico(AjaxRequestTarget target) throws ValidacaoException {
        try {
            validarAdicionarResponsavelTecnico(responsavelTecnico);
        } catch (ValidacaoException e) {
            autoCompleteConsultaResponsavel.limpar(target);
            throw e;
        }
        EstabelecimentoResponsavelTecnico estabelecimentoResponsavelTecnico = new EstabelecimentoResponsavelTecnico();
        estabelecimentoResponsavelTecnico.setResponsavelTecnico(responsavelTecnico);
        object.getLstEstabelecimentoResponsavelTecnico().add(estabelecimentoResponsavelTecnico);
        autoCompleteConsultaResponsavel.limpar(target);
        tblItens.update(target);
        target.appendJavaScript(JScript.focusComponent(autoCompleteConsultaResponsavel.getTxtDescricao().getTextField()));
    }

    private boolean isGestaoAtividadeCnae() {
        return ConfiguracaoVigilanciaEnum.TipoGestaoAtividade.CNAE.value().equals(configuracaoVigilancia.getFlagTipoGestaoAtividade());
    }

    private void adicionarAtividade(AjaxRequestTarget target) throws ValidacaoException {
        EstabelecimentoAtividade atividade = modelAtividades.getObject();
        validarAdicionarAtividade(atividade);

        atividade.setAtividadeEstabelecimento((AtividadeEstabelecimento) pnlAtividadeEstabelecimento.getAutoCompleteConsultaAtividadeEstabelecimento().getModel().getObject());
        if (RepositoryComponentDefault.SIM_LONG.equals(this.object.getEstabelecimento().getFlagMicroEmpresa())
                || RepositoryComponentDefault.SIM_LONG.equals(dropDownIsentoPorLei.getComponentValue())
                || RepositoryComponentDefault.SIM_LONG.equals(dropDownIsentoTaxa.getComponentValue())) {
            atividade.setIsentoTaxa(RepositoryComponentDefault.SIM_LONG);
        } else {
            atividade.setIsentoTaxa(RepositoryComponentDefault.NAO_LONG);
        }

        Integer idx = null;
        for (int i = 0; i < object.getEstabelecimentoAtividadeList().size(); i++) {
            EstabelecimentoAtividade item = object.getEstabelecimentoAtividadeList().get(i);
            if (estabelecimentoAtividadeEdicao != null && estabelecimentoAtividadeEdicao == item) {
                idx = i;
            } else if (item.getAtividadeEstabelecimento().equals(atividade.getAtividadeEstabelecimento())) {
                throw new ValidacaoException(BundleManager.getString("msgAtividadeJaAdicionada"));
            }
        }

        if (estabelecimentoAtividadeEdicao != null && idx != null) {
            object.getEstabelecimentoAtividadeList().remove(idx.intValue());
            object.getEstabelecimentoAtividadeList().add(idx, atividade);
        } else {
            object.getEstabelecimentoAtividadeList().add(0, atividade);
        }

        EstabelecimentoAtividade objSelecionado = tblAtividades.getSelectedObject();
        if (CollectionUtils.isAllEmpty(this.object.getEstabelecimentoAtividadeList()) || this.object.getEstabelecimentoAtividadeList().size() == 1) {
            this.object.getEstabelecimentoAtividadeList().get(0).setFlagPrincipal(RepositoryComponentDefault.SIM_LONG);
            objSelecionado = this.object.getEstabelecimentoAtividadeList().get(0);
        }
        tblAtividades.update(target);
        tblAtividades.setSelectedObject(objSelecionado);

        adicionarAtividadeTipoServico(atividade);
        carregarInformacoesCLassificacaoRiscoInspecao();

        modelAtividades.setObject(new EstabelecimentoAtividade());
        estabelecimentoAtividadeEdicao = null;

        limparAtividade(target);
        calcularTotalTaxas(target);

        target.appendJavaScript(JScript.focusComponent(pnlAtividadeEstabelecimento.getAutoCompleteConsultaAtividadeEstabelecimento().getTxtDescricao().getTextField()));
        target.add(containerAtividades, containerClassificacaoRisco);
    }

    public void validarAdicionarAtividade(EstabelecimentoAtividade atividade) throws ValidacaoException {
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagObrigaInformarIsentoTaxa()) && dropDownIsentoTaxa.getComponentValue() == null) {
            throw new ValidacaoException(BundleManager.getString("msgInformeCampoIsentoTaxa"));
        }
        if (atividade.getAtividadeEstabelecimento() == null && pnlAtividadeEstabelecimento.getAutoCompleteConsultaAtividadeEstabelecimento().getModel().getObject() != null) {
            atividade.setAtividadeEstabelecimento((AtividadeEstabelecimento) pnlAtividadeEstabelecimento.getAutoCompleteConsultaAtividadeEstabelecimento().getModel().getObject());
        }
        if (atividade.getAtividadeEstabelecimento() == null) {
            throw new ValidacaoException(bundle("msgFavorInformarAtividade"));
        }
        if (RepositoryComponentDefault.NAO_LONG.equals(atividade.getIsentoTaxa()) && atividade.getQuantidadeTaxa() == null) {
            throw new ValidacaoException(bundle("msgInformeQuantidadeTaxa"));
        }
    }

    private void adicionarAtividadeTipoServico(EstabelecimentoAtividade atividade) {
        AtividadeEstabelecimento atividadeEstabelecimento = atividade.getAtividadeEstabelecimento();
        if (atividadeEstabelecimento != null) {
            List<AtividadeEstabelecimentoTipoServico> tipoServicos = carregarAtividadeEstabelecimentoTipoServicosList(atividadeEstabelecimento);
            for (AtividadeEstabelecimentoTipoServico tipoServico : tipoServicos) {
                EstabelecimentoTipoServicoAtividade estabelecimentoTipoServicoAtividade = new EstabelecimentoTipoServicoAtividade();
                estabelecimentoTipoServicoAtividade.setAtividadeEstabelecimento(atividadeEstabelecimento);
                estabelecimentoTipoServicoAtividade.setAtividadeEstabelecimentoTipoServico(tipoServico);
                estabelecimentoTipoServicoAtividade.setEstabelecimento(object.getEstabelecimento());

                adicionaTipoServico(estabelecimentoTipoServicoAtividade, tipoServico, atividadeEstabelecimento);
            }
        }
    }

    private void adicionaTipoServico(EstabelecimentoTipoServicoAtividade estabelecimentoTipoServicoAtividade,
                                     AtividadeEstabelecimentoTipoServico tipoServico,
                                     AtividadeEstabelecimento atividadeEstabelecimento) {
        for (EstabelecimentoTipoServicoAtividade item : object.getEstabelecimentoTipoServicoAtividadeList()) {
            if (item.getAtividadeEstabelecimentoTipoServico().getTipoServico().equals(tipoServico.getTipoServico()) &&
                    item.getAtividadeEstabelecimento().getDescricao().equals(atividadeEstabelecimento.getDescricao()))
                return;
        }
        object.getEstabelecimentoTipoServicoAtividadeList().add(estabelecimentoTipoServicoAtividade);
    }

    private void removerAtividadeTipoServico(AjaxRequestTarget target, EstabelecimentoAtividade modelObject) {
        List<EstabelecimentoTipoServicoAtividade> estabelecimentoTipoServicoAtividadeList = object.getEstabelecimentoTipoServicoAtividadeList();
        for (EstabelecimentoTipoServicoAtividade estabelecimentoTipoServicoAtividade : estabelecimentoTipoServicoAtividadeList) {
            if (estabelecimentoTipoServicoAtividade.getAtividadeEstabelecimento().equals(modelObject.getAtividadeEstabelecimento())) {
                removeListaDeServicos(estabelecimentoTipoServicoAtividade);
            }
        }
        target.add(tblTipoServico);
    }

    private void removeListaDeServicos(EstabelecimentoTipoServicoAtividade estabelecimentoTipoServicoAtividade) {
        List<EstabelecimentoTipoServicoAtividade> tiposServicosManter = Lambda.select(object.getEstabelecimentoTipoServicoAtividadeList(),
                Lambda.having(on(EstabelecimentoTipoServicoAtividade.class).getAtividadeEstabelecimento().getCodigo(),
                        Matchers.not(estabelecimentoTipoServicoAtividade.getAtividadeEstabelecimento().getCodigo())));

        object.setEstabelecimentoTipoServicoAtividadeList(tiposServicosManter);
    }

    private List<AtividadeEstabelecimentoTipoServico> carregarAtividadeEstabelecimentoTipoServicosList(AtividadeEstabelecimento atividadeEstabelecimento) {
        if (atividadeEstabelecimento != null) {
            AtividadeEstabelecimentoTipoServico proxy = on(AtividadeEstabelecimentoTipoServico.class);

            List<AtividadeEstabelecimentoTipoServico> list = LoadManager.getInstance(AtividadeEstabelecimentoTipoServico.class)
                    .addProperties(new HQLProperties(AtividadeEstabelecimentoTipoServico.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtividadeEstabelecimento().getCodigo()), atividadeEstabelecimento.getCodigo()))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getTipoServico()), BuilderQueryCustom.QuerySorter.CRESCENTE))
                    .start().getList();
            return list;
        }
        return new ArrayList<>();
    }

    private void recarregarTiposServicosAtividades(AjaxRequestTarget target) {
        List<EstabelecimentoAtividade> estabelecimentoAtividadeList = object.getEstabelecimentoAtividadeList();
        for (EstabelecimentoAtividade estabelecimentoAtividade : estabelecimentoAtividadeList) {
            List<AtividadeEstabelecimentoTipoServico> atividadeEstabelecimentoTipoServicos = carregarAtividadeEstabelecimentoTipoServicosList(estabelecimentoAtividade.getAtividadeEstabelecimento());
            for (AtividadeEstabelecimentoTipoServico tipoServico : atividadeEstabelecimentoTipoServicos) {
                List<EstabelecimentoTipoServicoAtividade> select = Lambda.select(object.getEstabelecimentoTipoServicoAtividadeList(),
                        Lambda.having(on(EstabelecimentoTipoServicoAtividade.class).getAtividadeEstabelecimento().getCodigo(), Matchers.equalTo(estabelecimentoAtividade.getAtividadeEstabelecimento().getCodigo()))
                                .and(Lambda.having(on(EstabelecimentoTipoServicoAtividade.class).getAtividadeEstabelecimentoTipoServico().getCodigo(), Matchers.equalTo(tipoServico.getCodigo()))));

                if (CollectionUtils.isAllEmpty(select)) {
                    EstabelecimentoTipoServicoAtividade estabelecimentoTipoServicoAtividade = new EstabelecimentoTipoServicoAtividade();
                    estabelecimentoTipoServicoAtividade.setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
                    estabelecimentoTipoServicoAtividade.setAtividadeEstabelecimentoTipoServico(tipoServico);
                    estabelecimentoTipoServicoAtividade.setEstabelecimento(object.getEstabelecimento());

                    object.getEstabelecimentoTipoServicoAtividadeList().add(estabelecimentoTipoServicoAtividade);
                }

            }
        }
        target.add(tblTipoServico);
    }

    private void calcularTotalTaxas(AjaxRequestTarget target) {
        List<EstabelecimentoAtividade> estabelecimentoAtividadeList = object.getEstabelecimentoAtividadeList();

        BigDecimal sum = Lambda.sum(estabelecimentoAtividadeList, Lambda.on(EstabelecimentoAtividade.class).getCalculoTaxa(true));
        BigDecimal sumTaxaAlvaraInicial = Lambda.sum(estabelecimentoAtividadeList, Lambda.on(EstabelecimentoAtividade.class).getCalculoTaxaAlvaraInicial(true));
        BigDecimal sumTaxaAlvaraRevalidacao = Lambda.sum(estabelecimentoAtividadeList, Lambda.on(EstabelecimentoAtividade.class).getCalculoTaxaAlvaraRevalidacao(true));

        StringBuilder sbTotal = new StringBuilder();
        NumberFormat nfTotal = NumberFormat.getCurrencyInstance();
        sbTotal.append(nfTotal.format(sum));

        StringBuilder sbInicial = new StringBuilder();
        NumberFormat nfInicial = NumberFormat.getCurrencyInstance();
        sbInicial.append(nfInicial.format(sumTaxaAlvaraInicial));

        StringBuilder sbRev = new StringBuilder();
        NumberFormat nfRev = NumberFormat.getCurrencyInstance();
        sbRev.append(nfRev.format(sumTaxaAlvaraRevalidacao));

        totalTaxa = sbTotal.toString();
        totalTaxaAlvaraInicial = sbInicial.toString();
        totalTaxaAlvaraRevalidacao = sbRev.toString();

        if (target != null) {
            target.add(lblValorTotalTaxa, lblTotalTaxaAlvaraInicial, lblTotalTaxaAlvaraRevalidacao);
        }
    }

    private void validarAdicionarResponsavelTecnico(ResponsavelTecnico responsavelTecnico) throws ValidacaoException {
        if (responsavelTecnico == null) {
            throw new ValidacaoException(bundle("msgFavorInformarResponsavel"));
        }

        ListIterator<EstabelecimentoResponsavelTecnico> listIterator = object.getLstEstabelecimentoResponsavelTecnico().listIterator();
        while (listIterator.hasNext()) {
            EstabelecimentoResponsavelTecnico next = listIterator.next();
            if (responsavelTecnico.getCodigo().equals(next.getResponsavelTecnico().getCodigo())) {
                throw new ValidacaoException(bundle("msgResponsavelJaAdicionado"));
            }
        }
    }

    private ICollectionProvider getCollectionProviderItens() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return object.getLstEstabelecimentoResponsavelTecnico();
            }
        };
    }

    private ICollectionProvider getCollectionProviderItensAtvd() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return object.getEstabelecimentoAtividadeList();
            }
        };
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        if (object.getResponsavelTecnico() != null) {
            return this.autoCompleteConsultaResponsavel.getTxtDescricao().getTextField();
        }
        return this.txtRazaoSocial;
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("dadosGerais");
    }

    @Override
    public void renderHead(HtmlHeaderContainer container) {
        super.renderHead(container);
    }

    public void carregarInformacoesCLassificacaoRiscoInspecao() {
        ClassificacaoGrupoEstabelecimento classificacaoGrupoEstabelecimento = inspecaoEstabelecimentoService.getClassificacaoRiscoAtividadesEstabelecimento(object.getEstabelecimentoAtividadeList());
        RequerimentoVigilanciaInspecao ultimaInspecaoEstabelecimento = inspecaoEstabelecimentoService.getUltimaInspecaoEstabelecimento(object.getEstabelecimento());
        RequerimentoVigilanciaInspecao ultimaInspecaoPresencialEstabelecimento = inspecaoEstabelecimentoService.getUltimaInspecaoPresencialEstabelecimento(object.getEstabelecimento());

        setUltimaInspecao(inspecaoEstabelecimentoService.getUltimaInspecaoEstabelecimentoFormatado(ultimaInspecaoEstabelecimento));
        setProximaInspecao(inspecaoEstabelecimentoService.getProximaInspecaoSugerida(ultimaInspecaoPresencialEstabelecimento, classificacaoGrupoEstabelecimento));
        if (classificacaoGrupoEstabelecimento != null) {
            setClassificacaoRisco(classificacaoGrupoEstabelecimento.getDescricao());
            inputClassificacaoRisco.add(new AttributeModifier("style", "background:#" + classificacaoGrupoEstabelecimento.getCor()));
        }
        object.setClassificacaoGrupoEstabelecimento(classificacaoGrupoEstabelecimento);
    }

    public void setClassificacaoRisco(String classificacaoRisco) {
        this.classificacaoRisco = classificacaoRisco;
    }

    public void setUltimaInspecao(String ultimaInspecao) {
        this.ultimaInspecao = ultimaInspecao;
    }

    public void setProximaInspecao(String proximaInspecao) {
        this.proximaInspecao = proximaInspecao;
    }

    public String getClassificacaoRisco() {
        return classificacaoRisco;
    }

    public String getProximaInspecao() {
        return proximaInspecao;
    }

    public String getUltimaInspecao() {
        return ultimaInspecao;
    }

    public DropDown getDropDownTipoEmpresa() {
        return dropDownTipoEmpresa;
    }

    public void setDropDownTipoEmpresa(DropDown dropDownTipoEmpresa) {
        this.dropDownTipoEmpresa = dropDownTipoEmpresa;
    }


}