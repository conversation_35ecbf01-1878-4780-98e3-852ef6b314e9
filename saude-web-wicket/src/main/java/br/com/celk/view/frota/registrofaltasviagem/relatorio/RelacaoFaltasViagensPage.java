package br.com.celk.view.frota.registrofaltasviagem.relatorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.dateperiod.PnlChoicePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.report.ReportProperties;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.frota.interfaces.dto.RelacaoFaltasViagensParam;
import br.com.ksisolucoes.report.frota.interfaces.facade.FrotaReportFacade;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.odlabs.wiquery.ui.datepicker.DatePicker;

import java.text.ParseException;
import java.util.Date;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

@Private
public class RelacaoFaltasViagensPage extends RelatorioPage<RelacaoFaltasViagensParam> {

    private AutoCompleteConsultaUsuarioCadsus autoCompletePassageiro;
    private DropDown<Integer> dropdownTipoRelatorio;
    private PnlChoicePeriod choicePeriod;

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("resumoViagens");
    }

    @Override
    public void init(Form<RelacaoFaltasViagensParam> form) {
        final RelacaoFaltasViagensParam relacaoFaltasViagensParamProxy = on(RelacaoFaltasViagensParam.class);

        autoCompletePassageiro = new AutoCompleteConsultaUsuarioCadsus(path(relacaoFaltasViagensParamProxy.getPassageiro()));
        form.add(autoCompletePassageiro);
        choicePeriod = new PnlChoicePeriod(path(relacaoFaltasViagensParamProxy.getPeriodo()));
        form.add(choicePeriod);
        dropdownTipoRelatorio = getDropDownTipoRelatorio(relacaoFaltasViagensParamProxy);
        form.add(dropdownTipoRelatorio);
        form.add(DropDownUtil.getTipoRelatorioPdfXlsDropDown(path(relacaoFaltasViagensParamProxy.getTipoArquivo())));
    }

    private DropDown<Integer> getDropDownTipoRelatorio(RelacaoFaltasViagensParam relacaoFaltasViagensParamProxy) {
        DropDown<Integer> dropDown = new DropDown<>(path(relacaoFaltasViagensParamProxy.getTipoRelatorio()));

        dropDown.addChoice(ReportProperties.DETALHADO, BundleManager.getString("detalhado"));
        dropDown.addChoice(ReportProperties.RESUMIDO, BundleManager.getString("resumido"));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                boolean isRelatorioDetalhado = ReportProperties.DETALHADO == dropdownTipoRelatorio.getComponentValue();
                autoCompletePassageiro.setEnabled(isRelatorioDetalhado);

                if (!isRelatorioDetalhado) autoCompletePassageiro.limpar(target);

                target.add(autoCompletePassageiro);
            }
        });

        return dropDown;
    }

    @Override
    public Class<RelacaoFaltasViagensParam> getDTOParamClass() {
        return RelacaoFaltasViagensParam.class;
    }

    @Override
    public Object getDataReport(RelacaoFaltasViagensParam param) throws ReportException, ValidacaoException {
        validatePeriod();
        return BOFactoryWicket.getBO(FrotaReportFacade.class).relacaoFaltasViagens(param);
    }

    private void validatePeriod() throws ValidacaoException {
        Date dataInicial = null;
        Date dataFinal = null;

        if (choicePeriod.getConvertedInput() != null) {
            dataInicial = choicePeriod.getConvertedInput().getDataInicial();
            dataFinal = choicePeriod.getConvertedInput().getDataFinal();
        }

        if (dataInicial == null || dataFinal == null) throw new ValidacaoException(BundleManager.getString("msgPeriodoDeveSerInformado"));

        try {
            if (Data.getMesDiferenca(dataInicial, dataFinal) >= 6) throw new ValidacaoException(BundleManager.getString("msgPeriodoMax6Meses"));
        } catch (ParseException e) {
            Loggable.log.error(e);
        }

    }
}
