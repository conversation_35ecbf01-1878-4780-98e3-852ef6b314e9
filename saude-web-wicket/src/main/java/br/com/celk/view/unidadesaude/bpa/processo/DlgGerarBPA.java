package br.com.celk.view.unidadesaude.bpa.processo;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.LoadableDetachableModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgGerarBPA extends Window{
    
    private PnlGerarBPA pnlGerarBPA;
    
    public DlgGerarBPA(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("gerarBpa");
            }
        });
                
        setInitialWidth(650);
        setInitialHeight(350);
        setResizable(true);
        
        setContent(pnlGerarBPA = new PnlGerarBPA(getContentId()) {

            @Override
            public void onGerarBpa(AjaxRequestTarget target, String competencia, Long tipoFinanciamento, Long tipoBpa, List<Empresa> empresaList) throws ValidacaoException, DAOException {
                close(target);
                DlgGerarBPA.this.onGerarBpa(target, competencia, tipoFinanciamento, tipoBpa, empresaList);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }


        });
    }
    
    public abstract void onGerarBpa(AjaxRequestTarget target, String competencia, Long tipoFinanciamento, Long tipoBpa, List<Empresa> empresaList) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, Receituario receituario){
        show(target);
        pnlGerarBPA.limpar(target);
        
    }    

    @Override
    public FormComponent getComponentRequestFocus() {
        return pnlGerarBPA.getTxtCompetencia();
    }
}
