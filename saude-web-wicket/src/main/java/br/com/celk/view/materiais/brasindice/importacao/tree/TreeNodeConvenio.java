package br.com.celk.view.materiais.brasindice.importacao.tree;

import br.com.celk.component.tree.node.TreeNodeImpl;
import br.com.celk.component.tree.node.usercomponent.ISelectableUserComponent;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;

/**
 *
 * <AUTHOR>
 */
public class TreeNodeConvenio extends TreeNodeImpl<ISelectableUserComponent> {

    private Convenio convenio;

    public TreeNodeConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public Convenio getConvenio() {
        return convenio;
    }

}