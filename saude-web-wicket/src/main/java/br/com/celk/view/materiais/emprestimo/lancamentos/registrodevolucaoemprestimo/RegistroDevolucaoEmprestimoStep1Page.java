package br.com.celk.view.materiais.emprestimo.lancamentos.registrodevolucaoemprestimo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.emprestimo.interfaces.dto.CadastroRegistroDevolucaoEmprestimoDTO;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.emprestimo.cadastro.autocomplete.AutoCompleteConsultaTipoDevolucao;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimo;
import br.com.ksisolucoes.vo.emprestimo.TipoDevolucao;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
@Private
public class RegistroDevolucaoEmprestimoStep1Page extends BasePage {

    private Form<CadastroRegistroDevolucaoEmprestimoDTO> form;
    private CadastroRegistroDevolucaoEmprestimoDTO dto;
    private AutoCompleteConsultaTipoDevolucao autoCompleteConsultaTipoDevolucao;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private TipoDevolucao tipoDevolucao;

    public RegistroDevolucaoEmprestimoStep1Page() {
    }
    
    public RegistroDevolucaoEmprestimoStep1Page(DevolucaoEmprestimo devolucaoEmprestimo) {
        this.dto = new CadastroRegistroDevolucaoEmprestimoDTO();
        this.dto.setDevolucaoEmprestimo(devolucaoEmprestimo);
    }

    @Override
    protected void postConstruct() {
        CadastroRegistroDevolucaoEmprestimoDTO proxy = on(CadastroRegistroDevolucaoEmprestimoDTO.class);
        
        getForm().add(autoCompleteConsultaTipoDevolucao = (AutoCompleteConsultaTipoDevolucao) new AutoCompleteConsultaTipoDevolucao(path(proxy.getDevolucaoEmprestimo().getTipoDevolucao()), true)
                .setLabel(new Model(bundle("tipoDevolucao"))));
        autoCompleteConsultaTipoDevolucao.add(new ConsultaListener<TipoDevolucao>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, TipoDevolucao object) {
                tipoDevolucao = object;
                verificarTipoSignatario(target);
            }
        });
        autoCompleteConsultaTipoDevolucao.add(new RemoveListener<TipoDevolucao>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, TipoDevolucao object) {
                tipoDevolucao = null;
                verificarTipoSignatario(target);
            }
        });
        
        getForm().add(new RequiredDateChooser(path(proxy.getDevolucaoEmprestimo().getDataDevolucao())).setLabel(new Model(bundle("dataDevolucao"))));
        getForm().add(autoCompleteConsultaUsuarioCadsus = (AutoCompleteConsultaUsuarioCadsus) new AutoCompleteConsultaUsuarioCadsus(path(proxy.getDevolucaoEmprestimo().getUsuarioCadsus()), false) {
            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO;
            }
        });
        autoCompleteConsultaUsuarioCadsus.setLabel(new Model(bundle("paciente")));
        getForm().add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa(path(proxy.getDevolucaoEmprestimo().getEmpresaDevolucao()), false)
                .setLabel(new Model(bundle("estabelecimento"))));
        getForm().add(new InputArea(path(proxy.getDevolucaoEmprestimo().getObservacao())).setLabel(new Model(bundle("observacao"))));

        getForm().add(new VoltarButton("btnVoltar"));
        getForm().add(new SubmitButton("btnAvancar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                avancar(target);
            }
        }));

        add(getForm());
        
        if(tipoDevolucao == null && dto != null){
            tipoDevolucao = dto.getDevolucaoEmprestimo().getTipoDevolucao();
        }
        
        autoCompleteConsultaTipoDevolucao.setEnabled(dto == null);
        verificarTipoSignatario(null);
    }
    
    private void verificarTipoSignatario(AjaxRequestTarget target){
        if(target != null){
            autoCompleteConsultaEmpresa.limpar(target);
            autoCompleteConsultaUsuarioCadsus.limpar(target);
        }
        
        autoCompleteConsultaEmpresa.setEnabled(false);
        autoCompleteConsultaEmpresa.setRequired(target, false);
        
        autoCompleteConsultaUsuarioCadsus.setEnabled(false);
        autoCompleteConsultaUsuarioCadsus.setRequired(target, false);
        
        if(tipoDevolucao != null){
            if(TipoDevolucao.TipoSignatario.ESTABELECIMENTO.value().equals(tipoDevolucao.getTipoSignatario())){
                autoCompleteConsultaEmpresa.setEnabled(true);
                autoCompleteConsultaEmpresa.setRequired(target, true);
            } else if(TipoDevolucao.TipoSignatario.PACIENTE.value().equals(tipoDevolucao.getTipoSignatario())){
                autoCompleteConsultaUsuarioCadsus.setEnabled(true);
                autoCompleteConsultaUsuarioCadsus.setRequired(target, true);
            }
        }
        
        if(target != null){
            target.add(autoCompleteConsultaUsuarioCadsus);
            target.add(autoCompleteConsultaEmpresa);
        }
    }
    
    private void avancar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        dto = getForm().getModel().getObject();
                
        if(dto.getDevolucaoEmprestimo().getDataDevolucao().after(DataUtil.getDataAtual())){
            throw new ValidacaoException(bundle("msgDataDevolucaoNaoPodeSerMaiorDataAtual"));            
        }
        
        setResponsePage(new RegistroDevolucaoEmprestimoStep2Page(dto));
    }
    
    private Form<CadastroRegistroDevolucaoEmprestimoDTO> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(dto != null ? dto : new CadastroRegistroDevolucaoEmprestimoDTO()));
        }
        return this.form;
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaTipoDevolucao.getTxtDescricao().getTextField();
    }

    @Override
    public String getTituloPrograma() {
        return bundle("registroDevolucao");
    }
}