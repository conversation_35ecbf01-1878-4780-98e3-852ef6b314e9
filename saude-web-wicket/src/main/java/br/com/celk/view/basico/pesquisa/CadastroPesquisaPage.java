package br.com.celk.view.basico.pesquisa;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.pesquisa.autocomplete.AutoCompleteConsultaPerguntas;
import br.com.celk.view.basico.pesquisa.dlg.DlgResposta;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.bo.basico.pesquisa.dto.CadastroPesquisaDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa;
import br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa;
import br.com.ksisolucoes.vo.basico.pesquisa.PesquisaPergunta;
import ch.lambdaj.Lambda;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroPesquisaPage extends BasePage {

    private boolean viewOnly;
    private CadastroPesquisaDTO dto;

    private PerguntaPesquisa perguntaPesquisa;
    private AutoCompleteConsultaPerguntas autoCompleteConsultaPerguntas;

    private Table tblPerguntas;
    private DlgResposta dlgResposta;

    private Long limite;

    public CadastroPesquisaPage(Pesquisa object, boolean viewOnly) {
        carregarDados(object);
        this.viewOnly = viewOnly;
        init();
    }

    public CadastroPesquisaPage(Pesquisa object) {
        this(object, true);
    }

    public CadastroPesquisaPage() {
        this(null);

    }

    private void init() {
        Form<CadastroPesquisaDTO> form = new Form("form");
        form.setDefaultModel(new CompoundPropertyModel<CadastroPesquisaDTO>(dto));

        CadastroPesquisaDTO proxy = on(CadastroPesquisaDTO.class);
        form.add(new RequiredInputField(path(proxy.getPesquisa().getDescricao())).setLabel(new Model<String>(bundle("objetivo"))).setEnabled(viewOnly));
        form.add(new RequiredDateChooser(path(proxy.getPesquisa().getDataInicio())).setLabel(new Model<String>(bundle("iniciaEm"))).setEnabled(viewOnly));
        form.add(new RequiredDateChooser(path(proxy.getPesquisa().getDataFim())).setLabel(new Model<String>(bundle("terminaEm"))).setEnabled(viewOnly));

        form.add(autoCompleteConsultaPerguntas = new AutoCompleteConsultaPerguntas("pergunta", new PropertyModel(this, "perguntaPesquisa")));
        autoCompleteConsultaPerguntas.setEnabled(viewOnly);

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        }.setEnabled(viewOnly));

        form.add(tblPerguntas = new Table("tblPerguntas", getColumns(), getCollectionProvider()));
        tblPerguntas.populate();
        tblPerguntas.setEnabled(viewOnly);

        form.add(new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar();
            }
        }.setEnabled(viewOnly));

        form.add(new VoltarButton("btnVoltar"));

        add(form);

        try {
            limite = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("limitePesquisa");
        } catch (DAOException ex) {
            Logger.getLogger(CadastroPesquisaPage.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        PerguntaPesquisa proxy = on(PerguntaPesquisa.class);

        columns.add(getCustomActionColumn());
        columns.add(createSortableColumn(bundle("pergunta"), proxy.getDescricao(), proxy.getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return dto.getPerguntas();
            }

        };
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<PerguntaPesquisa>() {
            @Override
            public void customizeColumn(final PerguntaPesquisa rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dto.getPerguntas().remove(rowObject);
                        tblPerguntas.populate();
                        tblPerguntas.update(target);
                    }
                });
                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        viewDlgResposta(target, rowObject);
                    }
                }).setTitleBundleKey("resposta");
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroPesquisa");
    }

    private void carregarDados(Pesquisa obj) {
        if (obj != null) {
            dto = new CadastroPesquisaDTO();
            dto.setPesquisa(new Pesquisa());
            dto.setPerguntas(new ArrayList<PerguntaPesquisa>());

            dto.setPesquisa(obj);

            List<PesquisaPergunta> listaAux = LoadManager.getInstance(PesquisaPergunta.class)
                    .addProperties(new HQLProperties(PesquisaPergunta.class).getProperties())
                    .addProperties(new HQLProperties(PerguntaPesquisa.class, PesquisaPergunta.PROP_PERGUNTA_PESQUISA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(PesquisaPergunta.PROP_PESQUISA, obj))
                    .start().getList();

            List<PerguntaPesquisa> lista = new ArrayList<PerguntaPesquisa>();

            for (PesquisaPergunta pp : listaAux) {
                lista.add(pp.getPerguntaPesquisa());
            }

            dto.setPerguntas(lista);
        } else {
            dto = new CadastroPesquisaDTO();
            dto.setPesquisa(new Pesquisa());
            dto.setPerguntas(new ArrayList<PerguntaPesquisa>());
        }
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (perguntaPesquisa != null) {
            if (dto.getPerguntas().size() >= limite.intValue()) {
                throw new ValidacaoException(bundle("msgLimitePerguntasPorPesquisa"));
            }

            if (Lambda.exists(dto.getPerguntas(), Matchers.equalTo(perguntaPesquisa))) {
                throw new ValidacaoException(bundle("msgPerguntaJaAdicionada"));
            }

            dto.getPerguntas().add(perguntaPesquisa);
            tblPerguntas.populate(target);
            tblPerguntas.update(target);
            autoCompleteConsultaPerguntas.limpar(target);
            autoCompleteConsultaPerguntas.focus(target);
        } else {
            throw new ValidacaoException(bundle("msgObrigatorioSelecionarAoMenosUmaPergunta"));
        }
    }

    private void salvar() throws DAOException, ValidacaoException {
        if (dto.getPesquisa().getStatus() == null) {
            dto.getPesquisa().setStatus(Pesquisa.Status.ATIVA.value());
        }
        if (CollectionUtils.isAllEmpty(dto.getPerguntas())) {
            throw new ValidacaoException(bundle("msgObrigatoriaAdicionarAoMenosUmaPergunta"));
        }
        if (dto.getPesquisa().getDataInicio().after(dto.getPesquisa().getDataFim())) {
            throw new ValidacaoException(bundle("msgDataInicioMaiorDataFim"));
        }
        BOFactoryWicket.getBO(BasicoFacade.class).salvarPesquisa(dto);
        setResponsePage(ConsultaPesquisaPage.class);
    }

    public void viewDlgResposta(AjaxRequestTarget target, PerguntaPesquisa pp) {
        if (dlgResposta == null) {
            addModal(target, dlgResposta = new DlgResposta(newModalId()));
        }
        dlgResposta.setModelObject(pp);
        dlgResposta.show(target);
    }
}
