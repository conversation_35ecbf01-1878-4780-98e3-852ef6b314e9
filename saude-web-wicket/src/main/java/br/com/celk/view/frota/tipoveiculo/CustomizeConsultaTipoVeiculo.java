package br.com.celk.view.frota.tipoveiculo;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.frota.TipoVeiculo;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaTipoVeiculo extends CustomizeConsultaAdapter{

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("descricao"), new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TipoVeiculo.PROP_DESCRICAO), QueryParameter.ILIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("referencia"), VOUtils.montarPath(TipoVeiculo.PROP_REFERENCIA));
        properties.put(BundleManager.getString("descricao"), VOUtils.montarPath(TipoVeiculo.PROP_DESCRICAO));
    }

    @Override
    public Class getClassConsulta() {
        return TipoVeiculo.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(TipoVeiculo.class).getProperties();
    }

}
