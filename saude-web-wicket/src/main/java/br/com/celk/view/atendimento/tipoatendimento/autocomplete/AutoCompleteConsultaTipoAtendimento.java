package br.com.celk.view.atendimento.tipoatendimento.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.tipoatendimento.autocomplete.restricaocontainer.RestricaoContainerTipoAtendimento;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.QueryConsultaTipoAtendimentoDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaTipoAtendimento extends AutoCompleteConsulta<TipoAtendimento> { 

    private NaturezaProcura naturezaProcura;
    private boolean incluirInativos;
    
    public AutoCompleteConsultaTipoAtendimento(String id) {
        super(id);
    }

    public AutoCompleteConsultaTipoAtendimento(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaTipoAtendimento(String id, IModel<TipoAtendimento> model) {
        super(id, model);
    }

    public AutoCompleteConsultaTipoAtendimento(String id, IModel<TipoAtendimento> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(TipoAtendimento.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(TipoAtendimento.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(TipoAtendimento.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerTipoAtendimento(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<TipoAtendimento, QueryConsultaTipoAtendimentoDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaTipoAtendimentoDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(AtendimentoFacade.class).consultaTipoAtendimento(dataPaging);
                    }

                    @Override
                    public QueryConsultaTipoAtendimentoDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaTipoAtendimentoDTOParam param = new QueryConsultaTipoAtendimentoDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaTipoAtendimentoDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                        param.setNaturezaProcura(naturezaProcura);
                        param.setIncluirInativos(incluirInativos);
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(TipoAtendimento.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return TipoAtendimento.class;
            }

        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tiposAtendimentos");
    }

    public AutoCompleteConsultaTipoAtendimento setIncluirInativos(boolean incluirInativos) {
        this.incluirInativos = incluirInativos;
        return this;
    }

    public void setNaturezaProcura(NaturezaProcura naturezaProcura) {
        this.naturezaProcura = naturezaProcura;
    }

}
