package br.com.celk.view.cadsus.familia;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ComponenteFamiliaDTO;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgDoencasComponente extends Window{

    private PnlDoencasComponente pnlDoencasComponente;
    
    public DlgDoencasComponente(String id) {
        super(id);
        init();
    }

    private void init() {
        setContent(pnlDoencasComponente = new PnlDoencasComponente(getContentId()) {

            @Override
            public void fechar(AjaxRequestTarget target) {
                close(target);
                updateTable(target);
            }
        });
        
        
        setTitle(BundleManager.getString("doencas"));
        setResizable(false);
    }
    
    public void setComponente(ComponenteFamiliaDTO componenteFamiliaDTO){
        this.pnlDoencasComponente.setComponente(componenteFamiliaDTO);
    }
    
    public abstract void updateTable(AjaxRequestTarget target);
    
//    public void limpar(AjaxRequestTarget target){
//        this.pnlDoencasComponente.limpar(target);
//    }
    
}
