package br.com.celk.view.agenda.agendamento.tfd.cadastroviagem;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.frota.OcorrenciaPassageiroViagem;
import br.com.ksisolucoes.vo.frota.RoteiroViagem;
import net.sf.jasperreports.engine.JRException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaOcorrenciaViagemPage extends BasePage {

    private Form<RoteiroViagem> form;
    private final RoteiroViagem roteiroViagemSelecionado;

    private Table<OcorrenciaPassageiroViagem> tblOrorrenciaRoteiro;
    private List<OcorrenciaPassageiroViagem> lstOcorrenciaRoteiro;

    private Long tipoOcorrencia;
    private DropDown dropDownTipoOcorrencia;

    public ConsultaOcorrenciaViagemPage(RoteiroViagem roteiroViagem) {
        this.roteiroViagemSelecionado = roteiroViagem;
    }

    @Override
    protected void postConstruct() {
        form = new Form("form", new CompoundPropertyModel(roteiroViagemSelecionado));
        RoteiroViagem proxy = on (RoteiroViagem.class);
        form.add(new DisabledInputField(path(proxy.getCidade().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getDataSaida())));
        form.add(new DisabledInputField(path(proxy.getVeiculo().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getMotorista().getNome())));

        form.add(getDropDownTipoOcorrencia());

        form.add(new AbstractAjaxButton("btnProcurar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException, JRException, IOException {
                consultarOcorrenciaRoteiroViagem();
                tblOrorrenciaRoteiro.populate(target);
            }
        });
        consultarOcorrenciaRoteiroViagem();
        form.add(tblOrorrenciaRoteiro = new Table("tblOcorrencias", getColumns(), getCollectionProvider()));
        tblOrorrenciaRoteiro.populate();

        form.add(new SubmitButton("btnVoltar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {

                setResponsePage(new ConsultaViagemTfdPage());
            }
        }).setDefaultFormProcessing(false));

        add(form);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        OcorrenciaPassageiroViagem proxy = on(OcorrenciaPassageiroViagem.class);

        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getDataCadastro())).setPattern("dd/MM/yyyy HH:mm"));
        columns.add(createColumn(bundle("tipoOcorrencia"), proxy.getDescricaoTipoOcorrencia()));
        columns.add(createColumn(bundle("descricaoOcorrencia"), proxy.getDescricaoOcorrencia()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getNome()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstOcorrenciaRoteiro;
            }
        };
    }

    private DropDown getDropDownTipoOcorrencia() {
        dropDownTipoOcorrencia = new DropDown("tipoOcorrencia", new PropertyModel(this, "tipoOcorrencia"));

        dropDownTipoOcorrencia.addChoice(null, BundleManager.getString("todos"));
        dropDownTipoOcorrencia.addChoice(OcorrenciaPassageiroViagem.TipoOcorrencia.CADASTRO.value(), BundleManager.getString("rotulo_cadastro"));
        dropDownTipoOcorrencia.addChoice(OcorrenciaPassageiroViagem.TipoOcorrencia.INCLUSAO_PACIENTE.value(), BundleManager.getString("rotulo_inclusao_paciente"));
        dropDownTipoOcorrencia.addChoice(OcorrenciaPassageiroViagem.TipoOcorrencia.EXCLUSAO_PACIENTE.value(), BundleManager.getString("rotulo_exclusao_paciente"));
        dropDownTipoOcorrencia.addChoice(OcorrenciaPassageiroViagem.TipoOcorrencia.CANCELAMENTO.value(), BundleManager.getString("cancelamento"));

        return dropDownTipoOcorrencia;
    }

    public void consultarOcorrenciaRoteiroViagem() {

        HQLProperties propsOcorrencia = new HQLProperties(OcorrenciaPassageiroViagem.class);
        HQLProperties propsUsuario = new HQLProperties(Usuario.class, OcorrenciaPassageiroViagem.PROP_USUARIO);

        LoadManager manager = LoadManager.getInstance(OcorrenciaPassageiroViagem.class)
                .addProperties(propsOcorrencia.getProperties())
                .addProperties(propsUsuario.getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(
                        VOUtils.montarPath(OcorrenciaPassageiroViagem.PROP_ROTEIRO_VIAGEM, RoteiroViagem.PROP_CODIGO),
                        roteiroViagemSelecionado.getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(
                        VOUtils.montarPath(OcorrenciaPassageiroViagem.PROP_DATA_CADASTRO),
                        BuilderQueryCustom.QuerySorter.DECRESCENTE));

        if (tipoOcorrencia != null) {
            manager.addParameter(new QueryCustom.QueryCustomParameter(
                    VOUtils.montarPath(OcorrenciaPassageiroViagem.PROP_TIPO_OCORRENCIA),
                    tipoOcorrencia.longValue()));
        }

        lstOcorrenciaRoteiro = manager.start().getList();
    }
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaOcorrenciaViagem");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
    }


}
