package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

/**
 * <AUTHOR>
 */

public enum TipoAnaliseEnum implements IEnum {
    ANALISE(1L, "Análise"),
    ANALISE_COM_PENDENCIA(2L, "Análise / Com Pendência"),
    ANALISE_PARADO(3L, "Análise / Parado"),
    ANALISE_RETORNO(4L, "Análise / Retorno");

    private Long value;
    private String descricao;

    TipoAnaliseEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static TipoAnaliseEnum valueOf(Long value) {
        for (TipoAnaliseEnum v : TipoAnaliseEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
