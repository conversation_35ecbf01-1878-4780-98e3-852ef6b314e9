package br.com.celk.view.agenda.lotesolicitacaoagendamento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.agenda.lotesolicitacaoagendamento.dialog.DlgConfirmarEnvioLoteSolicitacaoAgendamento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.dto.EmpresasUsuarioDTO;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.interfaces.facade.AgendamentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.bo.UnidadeHelper;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;
import br.com.ksisolucoes.vo.basico.Empresa;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaLoteSolicitacaoAgendamentoPage extends ConsultaPage<LoteSolicitacaoAgendamento, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa estabelecimento;
    private Empresa estabelecimentoDestino;
    private Boolean estabelecimentoCentral;
    private Long situacao;
    private DropDown<Long> dropDownSituacao;
    private DlgConfirmarEnvioLoteSolicitacaoAgendamento dlgConfirmacaoEnvioLote;
    private DlgImpressaoObject<LoteSolicitacaoAgendamento> dlgConfirmacaoImpressao;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new AutoCompleteConsultaEmpresa("estabelecimento", new PropertyModel(this, "estabelecimento")).setValidaUsuarioEmpresa(true));
        form.add(new AutoCompleteConsultaEmpresa("estabelecimentoDestino", new PropertyModel(this, "estabelecimentoDestino")));
        form.add(getDropDownStatus());

        if (this.estabelecimentoCentral == null) {
            form.setEnabled(false);
        }

        addModal(dlgConfirmacaoEnvioLote = new DlgConfirmarEnvioLoteSolicitacaoAgendamento(newModalId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, LoteSolicitacaoAgendamento loteSolicitacaoAgendamento) throws DAOException, ValidacaoException {
                BOFactoryWicket.getBO(AgendamentoFacade.class).enviarLoteSolicitacaoAgendamento(loteSolicitacaoAgendamento);
                dlgConfirmacaoImpressao.show(target, loteSolicitacaoAgendamento);
                getPageableTable().update(target);
            }
        });
        addModal(dlgConfirmacaoImpressao = new DlgImpressaoObject<LoteSolicitacaoAgendamento>(newModalId(), bundle("envioLoteConfirmadoSucesso")) {
            @Override
            public DataReport getDataReport(LoteSolicitacaoAgendamento loteSolicitacaoAgendamento) throws ReportException {
                return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioImpressaoLoteSolicitacao(loteSolicitacaoAgendamento);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, LoteSolicitacaoAgendamento loteSolicitacaoAgendamento) throws ValidacaoException, DAOException {
            }
        });

        setExibeExpandir(true);
    }

    private DropDown getDropDownStatus() {
        if (dropDownSituacao == null) {
            dropDownSituacao = new DropDown<Long>("situacao");
            dropDownSituacao.addChoice(null, BundleManager.getString("todas"));

            for (LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento status : LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.values()) {
                    dropDownSituacao.addChoice(status.value(), status.descricao());
            }
        }
        return dropDownSituacao;
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        LoteSolicitacaoAgendamento proxy = on(LoteSolicitacaoAgendamento.class);

        columns.add(getCustomColumn());
        columns.add(createSortableColumn(BundleManager.getString("codigo"), proxy.getCodigo()));
        columns.add(createSortableColumn(BundleManager.getString("estabelecimentoDestino"), proxy.getEmpresaDestino().getDescricao()));
        columns.add(createSortableColumn(BundleManager.getString("data"), proxy.getDataCadastro()));
        columns.add(createSortableColumn(BundleManager.getString("situacao"), proxy.getStatus(), proxy.getDescricaoStatus()));
        columns.add(createSortableColumn(BundleManager.getString("responsavel"), proxy.getResponsavel()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<LoteSolicitacaoAgendamento>() {
            @Override
            public void customizeColumn(LoteSolicitacaoAgendamento rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<LoteSolicitacaoAgendamento> () {
                    @Override
                    public void action(AjaxRequestTarget target, LoteSolicitacaoAgendamento modelObject) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroLoteSolicitacaoAgendamentoStep2Page(modelObject));
                    }
                }).setEnabled(rowObject.getStatus().equals(LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.PENDENTE.value()));

                addAction(ActionType.REMOVER, rowObject, new IModelAction<LoteSolicitacaoAgendamento> () {
                    @Override
                    public void action(AjaxRequestTarget target, LoteSolicitacaoAgendamento modelObject) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(AgendamentoFacade.class).excluirLoteSolicitacaoAgendamento(modelObject);
                        getPageableTable().update(target);
                    }
                }).setEnabled(rowObject.getStatus().equals(LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.PENDENTE.value()));

                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<LoteSolicitacaoAgendamento> () {
                    @Override
                    public void action(AjaxRequestTarget target, LoteSolicitacaoAgendamento modelObject) throws ValidacaoException, DAOException {
                        dlgConfirmacaoEnvioLote.show(target, modelObject);
                    }
                }).setTitleBundleKey("confirmarEnvio")
                        .setIcon(Icon.HAND_PRO)
                        .setEnabled(rowObject.getStatus().equals(LoteSolicitacaoAgendamento.StatusLoteSolicitacaoAgendamento.PENDENTE.value()));

                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<LoteSolicitacaoAgendamento>() {
                    @Override
                    public DataReport action(LoteSolicitacaoAgendamento modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(AgendamentoReportFacade.class).relatorioImpressaoLoteSolicitacao(modelObject);
                    }
                }).setTitleBundleKey("relatorioEnvio");
            }        
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return LoteSolicitacaoAgendamento.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(LoteSolicitacaoAgendamento.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_CODIGO),
                            VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_EMPRESA_DESTINO, Empresa.PROP_DESCRICAO),
                            VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_DATA_CADASTRO),
                            VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_STATUS),
                            VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_RESPONSAVEL),});
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_DATA_CADASTRO), false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (this.estabelecimento == null) {
            try {
                parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_EMPRESA), QueryCustom.QueryCustomParameter.IN, BOFactoryWicket.getBO(UsuarioFacade.class).getListEmpresasUsuario(new EmpresasUsuarioDTO(SessaoAplicacaoImp.getInstance().getUsuario()))));
            } catch (ValidacaoException ex) {
                Loggable.log.error(ex);   
            } catch (DAOException ex) {
                Loggable.log.error(ex);   
            }
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_EMPRESA), this.estabelecimento));
        }

        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_EMPRESA_DESTINO), this.estabelecimentoDestino));

        if (this.situacao != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(LoteSolicitacaoAgendamento.PROP_STATUS), situacao));
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        try {
            estabelecimentoCentral = UnidadeHelper.isCentralAgendamento();
        } catch (ValidacaoRuntimeException ex) {
            warn(ex.getMessage());
        }

        if (estabelecimentoCentral != null && estabelecimentoCentral) {
            return CadastroLoteSolicitacaoAgendamentoStep1Page.class;
        }

        return CadastroLoteSolicitacaoAgendamentoStep2Page.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaLoteEnvioSolicitacoesAgendamento");
    }
}
