package br.com.celk.view.basico.banco;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Banco;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.axis2.databinding.types.soapencoding.String;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroBancoPage extends CadastroPage<Banco>{

    private boolean permissionEmpresa;
    private InputField txtNumeroBanco;

    public CadastroBancoPage(Banco object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroBancoPage(Banco object) {
        this(object, false);
    }

    public CadastroBancoPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        Banco proxy = on(Banco.class);
        
        permissionEmpresa = true;

        form.add(new RequiredInputField<String>(Banco.PROP_DESCRICAO_BANCO));

        form.add(txtNumeroBanco = new RequiredInputField<String>(path(proxy.getNumeroBanco())));

        permissionEmpresa();
    }
    
    private void permissionEmpresa() {
        Usuario usuarioLogado = getUsuarioLogado();
        if (!isActionPermitted(usuarioLogado, Permissions.EMPRESA, ConsultaBancoPage.class)) {
            permissionEmpresa = false;
        }
    }
    
    public Usuario getUsuarioLogado() {
        return ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
    }


    
    @Override
    public Class<Banco> getReferenceClass() {
        return Banco.class;
    }

    @Override
    public FormComponent getComponentRequestFocus() { return txtNumeroBanco; }

    @Override
    public Class getResponsePage() {
        return ConsultaBancoPage.class;
    }
    
    @Override
    public java.lang.String getTituloPrograma() {
        return BundleManager.getString("cadastroBanco");
    }  

    @Override
    public Object salvar(Banco object) throws DAOException, ValidacaoException {
        if (object.getNumeroBanco() != null ) {
            LoadManager load = LoadManager.getInstance(Banco.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Banco.PROP_NUMERO_BANCO, BuilderQueryCustom.QueryParameter.IGUAL, object.getNumeroBanco()));
            Banco banco = load.setMaxResults(1).start().getVO();

            if(banco != null && banco.getCodigo().compareTo(Coalesce.asLong(object.getCodigo())) != 0){
                throw new ValidacaoException(bundle("msgJaExisteUmBancoComEsseNumeroParaEsseUsuario"));
            }
        }

        return super.salvar(object);

    }
}