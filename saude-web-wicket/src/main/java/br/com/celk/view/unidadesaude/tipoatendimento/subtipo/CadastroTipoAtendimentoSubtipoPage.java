package br.com.celk.view.unidadesaude.tipoatendimento.subtipo;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.base.BasePage;
import br.com.celk.unidadesaude.tipoatendimento.dto.EmpresaVisivelDTO;
import br.com.celk.unidadesaude.tipoatendimento.dto.NaturezaProcuraTipoAtendimentoDTO;
import br.com.celk.unidadesaude.tipoatendimento.dto.TipoAtendimentoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

/**
 *
 * <AUTHOR>
 */
public class CadastroTipoAtendimentoSubtipoPage extends BasePage {
    
    private TipoAtendimentoDTO dto;
    
    public CadastroTipoAtendimentoSubtipoPage() {
        this(null);
        
    }
    
    public CadastroTipoAtendimentoSubtipoPage(TipoAtendimentoDTO tipoAtendimentoDTO) {
        this(tipoAtendimentoDTO, false);
    }
    
    public CadastroTipoAtendimentoSubtipoPage(TipoAtendimentoDTO tipoAtendimentoDTO, boolean viewOnly) {
        dto = tipoAtendimentoDTO;
        init(tipoAtendimentoDTO, viewOnly);
    }
    
    private void init(TipoAtendimentoDTO tipoAtendimentoDTO, boolean viewOnly) {
        if (dto == null) {
            dto = new TipoAtendimentoDTO();
        }
        if (dto.getTipoAtendimentoEdicao() != null) {
            dto.setDescricao(dto.getTipoAtendimentoEdicao().getDescricao());
            List<TipoAtendimento> lstTipoAtendimento = LoadManager.getInstance(TipoAtendimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(TipoAtendimento.PROP_TIPO_ATENDIMENTO_AGRUPADOR, dto.getTipoAtendimentoEdicao()))
                    .start().getList();
            dto.setLstTipoAtendimento(lstTipoAtendimento);
            
            List<NaturezaProcuraTipoAtendimento> lstNaturezaProcura = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, dto.getTipoAtendimentoEdicao()))
                    .start().getList();
            
            List<NaturezaProcuraTipoAtendimentoDTO> lstNaturezaDTO = new ArrayList<>();
            
            if (CollectionUtils.isNotNullEmpty(lstNaturezaProcura)) {
                for (NaturezaProcuraTipoAtendimento npta : lstNaturezaProcura) {
                    
                    NaturezaProcuraTipoAtendimentoDTO dto = new NaturezaProcuraTipoAtendimentoDTO();
                    dto.setNaturezaProcura(npta.getNaturezaProcura());
//                    dto.setTipoProcedimento(npta.getTipoProcedimento());
                    List<EmpresaNaturezaProcuraTipoAtendimento> lstEmpresaNaturezaProcuraTipoAtendimentos = LoadManager.getInstance(EmpresaNaturezaProcuraTipoAtendimento.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EmpresaNaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO), npta))
                            .start().getList();
                    
                    List<EmpresaVisivelDTO> lstEmpresaVisivel = new ArrayList<>();
                    for (EmpresaNaturezaProcuraTipoAtendimento enpta : lstEmpresaNaturezaProcuraTipoAtendimentos) {
                        EmpresaVisivelDTO ev = new EmpresaVisivelDTO();
                        ev.setEmpresa(enpta.getEmpresa());
                        ev.setVisivel(enpta.getVisivel().equals(RepositoryComponentDefault.SIM) ? RepositoryComponentDefault.SIM_LONG : RepositoryComponentDefault.NAO_LONG);
                        lstEmpresaVisivel.add(ev);
                    }
                    
                    dto.setLstEmpresaVisivelDTO(lstEmpresaVisivel);
                    lstNaturezaDTO.add(dto);
                }
                dto.setLstNaturezaProcuraTipoAtendimentoDTO(lstNaturezaDTO);
            }
        }
        
        List<ITab> tabs = new ArrayList<ITab>();
        tabs.add(new CadastroTab<TipoAtendimentoDTO>(dto) {
            @Override
            public ITabPanel<TipoAtendimentoDTO> newTabPanel(String panelId, TipoAtendimentoDTO tipoAtendimentoDTO) {
                return new CadastroTipoAtendimentoTab(panelId, tipoAtendimentoDTO);
            }
        });
        tabs.add(new CadastroTab<TipoAtendimentoDTO>(dto) {
            @Override
            public ITabPanel<TipoAtendimentoDTO> newTabPanel(String panelId, TipoAtendimentoDTO tipoAtendimentoDTO) {
                return new CadastroTipoAtendimentoNaturezaTab(panelId, tipoAtendimentoDTO);
            }
        });
        
        add(new CadastroTipoAtendimentoTabbedPanel("wizard", dto, viewOnly, tabs, true));
    }
    
    @Override
    public String getTituloPrograma() {
        return bundle("cadastroTipoAtendimentoSubtipo");
    }
}
