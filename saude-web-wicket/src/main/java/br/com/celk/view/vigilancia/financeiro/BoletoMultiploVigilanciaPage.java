package br.com.celk.view.vigilancia.financeiro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.link.AjaxReportLink;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.component.radio.interfaces.IRadioButtonChangeListener;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.SimpleSelectionTable;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.methods.WicketMethods;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.EmissaoBoletoMultiploDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.VigilanciaFinanceiroBoletoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.financeiro.VigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporte;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.RequerimentoLicencaTransporteHelper;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR>
 */
@Private
public class BoletoMultiploVigilanciaPage extends BasePage {

    private Form<EmissaoBoletoMultiploDTO> form;
    private EmissaoBoletoMultiploDTO dto;
    private RequerimentoVigilancia requerimentoVigilancia;

    private Table tblBoletos;
    private List<EmissaoBoletoMultiploDTO> emissaoBoletoVeiculoDTOList = new ArrayList<EmissaoBoletoMultiploDTO>();

    private Table<VeiculoEstabelecimento> tblVeiculos;
    private List<VeiculoEstabelecimento> veiculoEstabelecimentoList;

    private Table<EstabelecimentoAtividade> tblAtividades;
    private List<EstabelecimentoAtividade> estabelecimentoAtividadeList = new ArrayList<EstabelecimentoAtividade>();

    private WebMarkupContainer containerEstabelecimento;
    private WebMarkupContainer containerProfissional;
    private WebMarkupContainer containerVigilanciaPessoa;
    private WebMarkupContainer containerSolicitante;

    private WebMarkupContainer containerBtn;
    private WebMarkupContainer containerLicTransporte;
    private WebMarkupContainer containerBoletos;
    private WebMarkupContainer containerIsencao;
    private WebMarkupContainer containerIsentoSim;
    private WebMarkupContainer containerVencimento;
    private WebMarkupContainer containerValorBoleto;
    private WebMarkupContainer containerAtividades;

    private RadioButtonGroup radioGroupIsento;
    private RequiredDateChooser dcDataVencimento;
    private InputField<String> txtOutraIsencao;
    private DropDown<Long> dropDownIsento;
    private AbstractAjaxButton btnAdicionarBoleto;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private AjaxReportLink btnImprimir;
    private SubmitButton btnSalvar;
    private SubmitButton btnSalvarImprimir;

    // Campos de bind
    private RequerimentoLicencaTransporte requerimentoLicencaTransporte;
    private ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro;
    private TaxaIndice taxaVigente;
    private List<VigilanciaFinanceiro> vigilanciaFinanceiroList;
    private String titulo;
    private String descricaoTotalTaxa;
    private String outraIsencao;
    private boolean autorizacaoSanitaria;

    private Class clazz;


    public BoletoMultiploVigilanciaPage(RequerimentoVigilancia requerimentoVigilancia, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        setConfiguracaoVigilanciaFinanceiro();
        if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
            titulo = BundleManager.getString("emissaoBoleto");
        } else {
            titulo = BundleManager.getString("emissaoMemorando");
        }
        this.clazz = clazz;
        init();
    }

    private void init() {

        EmissaoBoletoMultiploDTO proxy = on(EmissaoBoletoMultiploDTO.class);

        addContainerLicencaTransporte();

        addContainerBoletos(proxy);

        getForm().add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getProtocoloFormatado())));
        getForm().add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getDescricaoTipoDocumento())));

        { //Estabelecimento
            containerEstabelecimento = new WebMarkupContainer("containerEstabelecimento");
            containerEstabelecimento.setOutputMarkupId(true);
            containerEstabelecimento.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getEstabelecimento().getRazaoSocial())));
            containerEstabelecimento.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getEstabelecimento().getCnpjCpfFormatado())));
            containerEstabelecimento.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getEstabelecimento().getMicroEmpresaFormatado())));
            containerEstabelecimento.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getEnderecoFormatadoComCidade())));

            containerAtividades = new WebMarkupContainer("containerAtividades");
            containerAtividades.setOutputMarkupId(true);

            containerAtividades.add(tblAtividades = new SimpleSelectionTable("tblAtividades", getColumnsItensAtvd(), getCollectionProviderItensAtvd()));
            tblAtividades.populate();
            containerAtividades.setEnabled(false);
            containerEstabelecimento.setEnabled(false);
            containerEstabelecimento.add(containerAtividades);

            getForm().add(containerEstabelecimento);
        }

        {  //Profissional
            containerProfissional = new WebMarkupContainer("containerProfissional");
            containerProfissional.setOutputMarkupId(true);

            containerProfissional.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getVigilanciaProfissional().getNomeProfissional())));
            containerProfissional.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getVigilanciaProfissional().getCpf())));
            containerProfissional.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getEnderecoFormatadoComCidade())));
            containerProfissional.setEnabled(false);
            getForm().add(containerProfissional);
        }

        {   //VigilanciaPessoa
            containerVigilanciaPessoa = new WebMarkupContainer("containerVigilanciaPessoa");
            containerVigilanciaPessoa.setOutputMarkupId(true);

            containerVigilanciaPessoa.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getNome())));
            containerVigilanciaPessoa.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getVigilanciaPessoa().getCpfFormatado())));
            containerVigilanciaPessoa.add(new DisabledInputField<String>(path(proxy.getRequerimentoVigilancia().getVigilanciaEndereco().getEnderecoFormatadoComCidade())));
            containerVigilanciaPessoa.setEnabled(false);
            getForm().add(containerVigilanciaPessoa);
        }

        {        //Solicitante
            containerSolicitante = new WebMarkupContainer("containerSolicitante");
            containerSolicitante.setOutputMarkupId(true);

            containerSolicitante.add(new RequiredInputField<String>(path(proxy.getRequerimentoVigilancia().getNomeSolicitante())));
            containerSolicitante.add(new UpperField(path(proxy.getRequerimentoVigilancia().getCpfSolicitante())));
            containerSolicitante.add(new UpperField(path(proxy.getRequerimentoVigilancia().getRgSolicitante())));
            containerSolicitante.add(new InputField(path(proxy.getRequerimentoVigilancia().getEnderecoSolicitante())));
            containerSolicitante.add(new InputField(path(proxy.getRequerimentoVigilancia().getCelularSolicitante())));
            containerSolicitante.setEnabled(false);
            getForm().add(containerSolicitante);
        }
        {
            containerBtn = new WebMarkupContainer("containerBtn");
            containerBtn.setOutputMarkupId(true);
            containerBtn.add(new AbstractAjaxButton("btnVoltar") {

                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    try {
                        Page returnPage = (Page) clazz.newInstance();
                        setResponsePage(returnPage);
                    } catch (InstantiationException | IllegalAccessException e) {
                        Loggable.log.error(e.getMessage());
                    }
                }
            }.setDefaultFormProcessing(false));
            containerBtn.add(btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {

                @Override
                public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                    salvar(target, false);
                }
            }));
            btnSalvar.setDefaultFormProcessing(false);

            containerBtn.add(btnSalvarImprimir = new SubmitButton("btnSalvarImprimir", new ISubmitAction() {

                @Override
                public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                    salvar(target, true);
                }
            }));
            btnSalvarImprimir.setDefaultFormProcessing(false);


            containerBtn.add(btnImprimir = new AjaxReportLink("btnImprimir") {
                @Override
                public DataReport getDataRepoReport(AjaxRequestTarget target) throws ValidacaoException, DAOException, ReportException {
                    if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                        imprimirBoleto(target);
                    } else if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.MEMORANDO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                        return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoMemorando(requerimentoVigilancia);
                    }
                    return null;
                }

            });
            btnImprimir.setVisible(false);

            getForm().add(containerBtn);
        }

        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        enableContainers();
        initForm();
        add(getForm());
    }

    private void setConfiguracaoVigilanciaFinanceiro() {
        if (configuracaoVigilanciaFinanceiro == null) {
            try {
                configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
            } catch (ValidacaoException e) {
                Loggable.vigilancia.error(e.getMessage(), e);
            }
        }
    }

    private void initForm() {
        if (requerimentoVigilancia.getFlagIsentoMei() != null ||
                VigilanciaHelper.isMEI(requerimentoVigilancia.getEstabelecimento())) {
            getForm().getModel().getObject().setIsento(requerimentoVigilancia.getFlagIsentoMei());
            getForm().getModel().getObject().setIsentoSimNao(RepositoryComponentDefault.SIM_LONG);
            getForm().getModel().getObject().setOutraIsencao(requerimentoVigilancia.getDescricaoIsentoOutro());
            containerBoletos.setEnabled(false);
            containerLicTransporte.setEnabled(false);
            actionIsento(null);
        } else {
            getForm().getModel().getObject().setIsentoSimNao(RepositoryComponentDefault.NAO_LONG);
        }
    }


    private void addContainerLicencaTransporte() {
        //Lic Transporte
        containerLicTransporte = new WebMarkupContainer("containerLicTransporte");
        containerLicTransporte.setOutputMarkupId(true);

        requerimentoLicencaTransporte = RequerimentoLicencaTransporteHelper.carregarLicencaTransporte(requerimentoVigilancia);
        if (requerimentoLicencaTransporte != null) {
            autorizacaoSanitaria = RequerimentoLicencaTransporte.TipoLicenca.AUTORIZACAO_SANITARIA.value().equals(requerimentoLicencaTransporte.getTipoLicenca());
        }

        containerLicTransporte.add(tblVeiculos = new Table("tblVeiculos", getColumnsVeiculos(), getCollectionProviderVeiculo()));

        if (TipoSolicitacao.TipoDocumento.LICENCA_TRANSPORTE.value().equals(requerimentoVigilancia.getTipoDocumento())) {
            containerLicTransporte.setVisible(true);
            veiculoEstabelecimentoList = RequerimentoLicencaTransporteHelper.buscarVeiculosByRequerimentoVigilancia(requerimentoVigilancia);
        } else {
            containerLicTransporte.setVisible(false);
        }
        tblVeiculos.populate();
        getForm().add(containerLicTransporte);
    }

    private void addContainerBoletos(EmissaoBoletoMultiploDTO proxy) {
        containerBoletos = new WebMarkupContainer("containerBoletos");
        containerBoletos.setOutputMarkupPlaceholderTag(true);

        containerIsencao = new WebMarkupContainer("containerIsencao");
        containerIsencao.setOutputMarkupPlaceholderTag(true);
        containerIsencao.add(dropDownIsento = DropDownUtil.getNaoSimLongDropDown(path(proxy.getIsentoSimNao())));
        dropDownIsento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                actionIsento(target);
            }
        });
        containerIsencao.add(new Label(path(proxy.getInformacoesPagador())));

        containerIsentoSim = new WebMarkupContainer("containerIsentoSim");
        containerIsentoSim.setOutputMarkupId(true);
        containerIsentoSim.setOutputMarkupPlaceholderTag(true);
        containerIsentoSim.setVisible(false);

        containerIsentoSim.add(radioGroupIsento = new RadioButtonGroup(path(proxy.getIsento())));
        radioGroupIsento.setRequired(true);
        radioGroupIsento.setOutputMarkupId(true);
        radioGroupIsento.setVisible(false);
        radioGroupIsento.setLabel(new Model(BundleManager.getString("isencao")));

        radioGroupIsento.add(new AjaxRadio("mei", new Model(RequerimentoVigilancia.Isencao.MEI.value())));
        radioGroupIsento.add(new AjaxRadio("outro", new Model(RequerimentoVigilancia.Isencao.OUTRO.value())));

        containerIsentoSim.add(txtOutraIsencao = new InputField<>(path(proxy.getOutraIsencao())));
        txtOutraIsencao.setLabel(new Model(BundleManager.getString("isencao")));
        containerIsencao.add(containerIsentoSim);
        txtOutraIsencao.setOutputMarkupId(true);
        radioGroupIsento.add(new IRadioButtonChangeListener() {
            @Override
            public void valueChanged(AjaxRequestTarget target) {
                if (RequerimentoVigilancia.Isencao.MEI.value().equals(getForm().getModel().getObject().getIsento())) {
                    txtOutraIsencao.setEnabled(false);
                    txtOutraIsencao.setRequired(false);
                    txtOutraIsencao.removeRequiredClass();
                } else {
                    txtOutraIsencao.setEnabled(true);
                    txtOutraIsencao.setRequired(true);
                    txtOutraIsencao.addRequiredClass();
                }
                target.add(txtOutraIsencao);
            }
        });

        containerValorBoleto = new WebMarkupContainer("containerValorBoleto");
        containerValorBoleto.setOutputMarkupId(true);
        containerValorBoleto.setOutputMarkupPlaceholderTag(true);
        containerValorBoleto.add(new Label(path(proxy.getDescricaoTotalTaxa())));

        containerVencimento = new WebMarkupContainer("containerVencimento");
        containerVencimento.setOutputMarkupId(true);
        containerVencimento.setOutputMarkupPlaceholderTag(true);
        containerVencimento.add(dcDataVencimento = new RequiredDateChooser(path(proxy.getDataVencimento())));
        dcDataVencimento.setLabel(new Model<String>(WicketMethods.bundle("dataVencimento")));
        dcDataVencimento.getData().setMinDate(new DateOption(DataUtil.getDataAtual()));

        containerBoletos.add(containerVencimento);
        containerBoletos.add(containerValorBoleto);
        containerBoletos.add(containerIsencao);
        containerBoletos.add(btnAdicionarBoleto = new AbstractAjaxButton("btnAdicionarBoleto") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarBoleto(target, (EmissaoBoletoMultiploDTO) getForm().getModel().getObject());
            }
        });


        containerBoletos.add(tblBoletos = new Table("tblBoletos", getColumnsBoletos(), getCollectionProviderBoletos()));
        tblBoletos.populate();

        getForm().add(containerBoletos);
    }

    private void adicionarBoleto(AjaxRequestTarget target, EmissaoBoletoMultiploDTO dto) throws ValidacaoException {
        if (dto.getVeiculoEstabelecimento() == null) {
            throw new ValidacaoException("Vincule um veículo ao boleto (ação adicionar)");
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(dto.getIsentoSimNao())) {
            dto.setDataVencimento(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataFinal());
            BigDecimal totalTaxa = new BigDecimal(0);
            NumberFormat nf = NumberFormat.getCurrencyInstance();
            dto.setDescricaoTotalTaxa(nf.format(totalTaxa));
        }
        CrudUtils.adicionarItem(target, tblBoletos, emissaoBoletoVeiculoDTOList, dto);
        resetFormBoletos();
        ComponentUtils.limparContainer(containerBoletos, target);
        veiculoEstabelecimentoList.remove(dto.getVeiculoEstabelecimento());
        tblVeiculos.populate(target);
        actionIsento(target);
    }

    private void resetFormBoletos() {
        dto = new EmissaoBoletoMultiploDTO();
        dto.setRequerimentoVigilancia(requerimentoVigilancia);
        dto.setIsentoSimNao(RepositoryComponentDefault.NAO_LONG);
        getForm().getModel().setObject(dto);
    }

    private void actionIsento(AjaxRequestTarget target) {
        if (RepositoryComponentDefault.SIM_LONG.equals(requerimentoVigilancia.getEstabelecimento().getFlagMicroEmpresa())) {
            txtOutraIsencao.setComponentValue(BundleManager.getString("mei"));
            dropDownIsento.setComponentValue(RepositoryComponentDefault.SIM_LONG);
            dropDownIsento.setEnabled(false);
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getIsentoSimNao())) {
            containerIsentoSim.setVisible(true);
            containerVencimento.setVisible(false);
            btnSalvarImprimir.setVisible(false);
            btnImprimir.setVisible(false);
            txtOutraIsencao.setRequired(true);
            txtOutraIsencao.addRequiredClass();
            getForm().getModel().getObject().setDescricaoTotalTaxa(null);
        } else {
            txtOutraIsencao.setRequired(false);
            txtOutraIsencao.removeRequiredClass();
            containerIsentoSim.setVisible(false);
            containerVencimento.setVisible(true);
            btnSalvarImprimir.setVisible(true);
            if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
                btnImprimir.setVisible(true);
            }
        }
        if (target != null) {
            target.add(txtOutraIsencao);
            target.add(getForm());
        }
    }

    private void enableContainers() {
        if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
            containerBoletos.setVisible(true);
        } else {
            containerBoletos.setVisible(false);
        }
        if (requerimentoVigilancia.getEstabelecimento() != null) {
            containerEstabelecimento.setVisible(true);
            if (TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value().equals(requerimentoVigilancia.getTipoDocumento())
                    || TipoSolicitacao.TipoDocumento.ALVARA_REVALIDACAO.value().equals(requerimentoVigilancia.getTipoDocumento())
                    || TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value().equals(requerimentoVigilancia.getTipoDocumento())
                    || TipoSolicitacao.TipoDocumento.REVALIDACAO_LICENCA_SANITARIA.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                radioGroupIsento.setVisible(false);
                if (TipoSolicitacao.TipoDocumento.ALVARA_INICIAL.value().equals(requerimentoVigilancia.getTipoDocumento()) || TipoSolicitacao.TipoDocumento.LICENCA_SANITARIA.value().equals(requerimentoVigilancia.getTipoDocumento())) {
                    radioGroupIsento.setVisible(true);
                }
                carregarEstabelecimentoAtividade(requerimentoVigilancia.getEstabelecimento());
                containerAtividades.setVisible(true);
            } else {
                containerAtividades.setVisible(false);
            }
            containerProfissional.setVisible(false);
            containerVigilanciaPessoa.setVisible(false);
        } else if (requerimentoVigilancia.getVigilanciaPessoa() != null) {
            containerEstabelecimento.setVisible(false);
            containerProfissional.setVisible(false);
            containerVigilanciaPessoa.setVisible(true);
        } else {
            containerEstabelecimento.setVisible(false);
            containerVigilanciaPessoa.setVisible(false);
            containerProfissional.setVisible(true);
        }

        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
            btnImprimir.setVisible(true);
            btnSalvar.setVisible(false);
            btnSalvarImprimir.setVisible(false);
            dcDataVencimento.setEnabled(false);
            containerIsencao.setEnabled(false);
        } else {
            btnSalvar.setVisible(true);
            btnSalvarImprimir.setVisible(true);
            dcDataVencimento.setEnabled(true);
            btnImprimir.setVisible(false);
        }
    }

    private List<IColumn> getColumnsVeiculos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        VeiculoEstabelecimento proxy = on(VeiculoEstabelecimento.class);

        columns.add(getCustomColumnVeiculos());
        columns.add(createColumn(bundle("placa"), proxy.getPlacaFormatada()));
        columns.add(createColumn(bundle("tipoVeiculo"), proxy.getTipoVeiculo()));
        columns.add(createColumn(bundle("renavam"), proxy.getRenavam()));
        columns.add(createColumn(bundle("especificacao"), proxy.getEspecificacao()));
        columns.add(createColumn(bundle("observacao"), proxy.getRestricoes()));
        if (autorizacaoSanitaria) {
            columns.add(createColumn(bundle("valorBoleto"), proxy.getDescricaoCalculoTaxaAutorizacaoSanitaria()));
        } else {
            columns.add(createColumn(bundle("valorBoleto"), proxy.getDescricaoCalculoTaxa()));
        }

        return columns;
    }


    private IColumn getCustomColumnVeiculos() {
        return new MultipleActionCustomColumn<VeiculoEstabelecimento>() {

            @Override
            public void customizeColumn(VeiculoEstabelecimento rowObject) {
                addAction(ActionType.ADICIONAR, rowObject, new IModelAction<VeiculoEstabelecimento>() {

                    @Override
                    public void action(AjaxRequestTarget target, VeiculoEstabelecimento modelObject) throws ValidacaoException, DAOException {
                        adicionarBoletoVeiculo(target, modelObject);
                    }
                }).setTitleBundleKey("adicionar");
            }
        };
    }

    private void adicionarBoletoVeiculo(AjaxRequestTarget target, VeiculoEstabelecimento modelObject) {
        EmissaoBoletoMultiploDTO dto = (EmissaoBoletoMultiploDTO) SerializationUtils.clone(getForm().getModel().getObject());
        dto.setVeiculoEstabelecimento(modelObject);
        if (autorizacaoSanitaria) {
            dto.setDescricaoTotalTaxa(modelObject.getDescricaoCalculoTaxaAutorizacaoSanitaria());
        } else {
            dto.setDescricaoTotalTaxa(modelObject.getDescricaoCalculoTaxa());
        }

        StringBuilder infoVeiculoBuilder = new StringBuilder();
        infoVeiculoBuilder.append("Veículo: ");
        infoVeiculoBuilder.append(modelObject.getPlacaFormatada());
        infoVeiculoBuilder.append(" / ");
        infoVeiculoBuilder.append(modelObject.getTipoVeiculo());
        dto.setInformacoesPagador(infoVeiculoBuilder.toString());
        getForm().getModel().setObject(dto);

        target.add(containerBoletos);
        calcularDataVencto(target);
    }

    private void calcularDataVencto(AjaxRequestTarget target) {
        Long quantidadeDiasVencimentoBoleto = configuracaoVigilanciaFinanceiro.getQuantidadeDiasVencimentoBoleto();

        if (Coalesce.asLong(quantidadeDiasVencimentoBoleto) > 0) {
            Date dtVencto = Data.addDias(DataUtil.getDataAtual(), quantidadeDiasVencimentoBoleto.intValue());

            while (!Data.isDiaUtil(dtVencto)) {
                dtVencto = Data.addDias(dtVencto, 1);
            }

            dcDataVencimento.setComponentValue(dtVencto);
        } else {
            target.focusComponent(dcDataVencimento.getData());
        }
    }

    private List<IColumn> getColumnsBoletos() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EmissaoBoletoMultiploDTO proxy = on(EmissaoBoletoMultiploDTO.class);

        columns.add(createColumn(bundle("dataVencimento"), proxy.getDataVencimento()));
        columns.add(createColumn(bundle("valorBoleto"), proxy.getDescricaoTotalTaxa()));
        columns.add(createColumn(bundle("isento"), proxy.getOutraIsencao()));
        columns.add(createColumn(bundle("informacoesPagador"), proxy.getInformacoesPagador()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderVeiculo() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (veiculoEstabelecimentoList == null) {
                    veiculoEstabelecimentoList = new ArrayList<>();
                }
                return veiculoEstabelecimentoList;
            }
        };
    }

    private ICollectionProvider getCollectionProviderBoletos() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (emissaoBoletoVeiculoDTOList == null) {
                    emissaoBoletoVeiculoDTOList = new ArrayList<>();
                }
                return emissaoBoletoVeiculoDTOList;
            }
        };
    }

    private List<IColumn> getColumnsItensAtvd() {
        List<IColumn> columns = new ArrayList<IColumn>();

        EstabelecimentoAtividade proxy = on(EstabelecimentoAtividade.class);

        columns.add(createColumn(bundle("atividade"), proxy.getAtividadeEstabelecimento().getDescricao()));
        columns.add(createColumn(bundle("isentoTaxa"), proxy.getDescricaoFormatadaIsentoTaxa()));
        columns.add(createColumn(bundle("quantidade"), proxy.getQuantidadeTaxa()));
        columns.add(createColumn(bundle("valorTaxa"), proxy.getDescricaoCalculoTaxa()));

        return columns;
    }

    private Form<EmissaoBoletoMultiploDTO> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(dto == null ? (dto = new EmissaoBoletoMultiploDTO()) : dto));
            dto.setRequerimentoVigilancia(this.requerimentoVigilancia);
        }
        return this.form;
    }

    private ICollectionProvider getCollectionProviderItensAtvd() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (estabelecimentoAtividadeList == null) {
                    estabelecimentoAtividadeList = new ArrayList<>();
                }
                return estabelecimentoAtividadeList;
            }
        };
    }

    private void carregarEstabelecimentoAtividade(Estabelecimento estabelecimento) {
        List<EstabelecimentoAtividade> estabelecimentoAtividades = buscarEstabelecimentoAtividade(estabelecimento);
        estabelecimentoAtividadeList.addAll(estabelecimentoAtividades);
    }

    private List<EstabelecimentoAtividade> buscarEstabelecimentoAtividade(Estabelecimento estabelecimento) {
        return LoadManager.getInstance(EstabelecimentoAtividade.class)
                .addProperties(new HQLProperties(EstabelecimentoAtividade.class).getProperties())
                .addProperties(new HQLProperties(AtividadeEstabelecimento.class, EstabelecimentoAtividade.PROP_ATIVIDADE_ESTABELECIMENTO).getProperties())
                .addProperties(new HQLProperties(Estabelecimento.class, EstabelecimentoAtividade.PROP_ESTABELECIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                .start().getList();
    }

    private void salvar(AjaxRequestTarget target, boolean imprimir) throws DAOException, ValidacaoException {
        if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
            FinanceiroVigilanciaHelper.validateConnectionBoleto();
        }
        if (CollectionUtils.isNotNullEmpty(veiculoEstabelecimentoList)) {
            throw new ValidacaoException("Obrigatório adicionar o financeiro para todos os veículos");
        }
        VigilanciaFinanceiroBoletoDTO vigilanciaFinanceiroBoletoDTO = new VigilanciaFinanceiroBoletoDTO();
        vigilanciaFinanceiroBoletoDTO.setRequerimentoVigilancia(requerimentoVigilancia);
        vigilanciaFinanceiroBoletoDTO.setEmissaoBoletoMultiploDTOList(emissaoBoletoVeiculoDTOList);

        this.vigilanciaFinanceiroList = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarEmissaoBoletoRequerimentoPorVeiculo(vigilanciaFinanceiroBoletoDTO);


        if (imprimir) {
            if (ConfiguracaoVigilanciaFinanceiro.FormaCobranca.BOLETO.value().equals(configuracaoVigilanciaFinanceiro.getFormaCobranca())) {
                imprimirBoleto(target);
            }
            btnSalvarImprimir.setVisible(false);
            btnSalvar.setVisible(false);
            btnImprimir.setVisible(true);
            target.add(containerBtn);
            dcDataVencimento.setEnabled(false);
            target.add(dcDataVencimento);
        } else {
            try {
                Page returnPage = (Page) clazz.newInstance();
                getSession().getFeedbackMessages().info(returnPage, BundleManager.getString("registro_salvo_sucesso"));
                setResponsePage(returnPage);
            } catch (InstantiationException | IllegalAccessException e) {
                Loggable.log.error(e.getMessage());
            }
        }
    }


    private void carregarVigilanciaFinanceiroList() {
        vigilanciaFinanceiroList = LoadManager.getInstance(VigilanciaFinanceiro.class)
                .addProperties(new HQLProperties(VigilanciaFinanceiro.class).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, VOUtils.montarPath(VigilanciaFinanceiro.PROP_ANEXO_BOLETO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaFinanceiro.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .addParameter(new QueryCustom.QueryCustomParameter(VigilanciaFinanceiro.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, VigilanciaFinanceiro.Status.CANCELADO.value()))
                .start().getList();
    }


    private void imprimirBoleto(AjaxRequestTarget target) throws ValidacaoException {
        if (CollectionUtils.isEmpty(vigilanciaFinanceiroList)) {
            carregarVigilanciaFinanceiroList();
        }
        if (CollectionUtils.isNotNullEmpty(vigilanciaFinanceiroList)) {
            String boletoBase64 = FinanceiroVigilanciaHelper.getBoletoBase64(vigilanciaFinanceiroList);
            ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64);
        }
    }

    @Override
    public String getTituloPrograma() {
        return titulo;
    }
}