package br.com.celk.view.hospital.quartosleitos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dialog.DlgMotivoDataDesativacaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.hospital.quartosleitos.customize.CustomizeConsultaQuartoInternacao;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
import br.com.ksisolucoes.bo.hospital.interfaces.facade.HospitalFacade;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto;
import br.com.ksisolucoes.vo.prontuario.hospital.QuartoInternacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaQuartosLeitos extends ConsultaPage<QuartoInternacao, List<BuilderQueryCustom.QueryParameter>> {

    private List<Empresa> unidade;
    private String referencia;
    private Empresa empresa;
    private Long tipoLeito;
    private DlgMotivoDataDesativacaoObject<QuartoInternacao> dlgMotivo;

    @Override
    public void initForm(Form form) {
        boolean permissaoEmpresa = !isActionPermitted(br.com.celk.system.session.ApplicationSession.get().getSession().<Usuario>getUsuario(), Permissions.EMPRESA);

        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new UpperField("referencia"));
        form.add(new AutoCompleteConsultaEmpresa("empresa").setValidaUsuarioEmpresa(permissaoEmpresa));
        form.add(getDropDownTipoLeito("tipoLeito"));
        addModal(dlgMotivo = new DlgMotivoDataDesativacaoObject<QuartoInternacao>(newModalId(), BundleManager.getString("desativarQuartoTemporariamente")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, String motivo, QuartoInternacao object, Date dataDesatualizacao) throws ValidacaoException, DAOException {
                BOFactoryWicket.getBO(HospitalFacade.class).desativarQuartoInternacao(object, motivo, dataDesatualizacao);
                getPageableTable().update(target);
            }
        });

        if (permissaoEmpresa) {
            try {
                carregarUnidades();
            } catch (SGKException ex) {
                error(ex.getMessage());
            }
        }
    }

    public DropDown getDropDownTipoLeito(String id) {
        DropDown dropDown = new DropDown(id);

        dropDown.addChoice(null , BundleManager.getString("todos"));
        dropDown.addChoice(LeitoQuarto.TipoLeito.SUS.value() , BundleManager.getString("sus"));
        dropDown.addChoice(LeitoQuarto.TipoLeito.CONVENIO.value() , BundleManager.getString("convenio"));
        dropDown.addChoice(LeitoQuarto.TipoLeito.PARTICULAR.value() , BundleManager.getString("particular"));

        return dropDown;
    }

    private void carregarUnidades() throws DAOException, ValidacaoException {
        List<Long> empresas = BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(SessaoAplicacaoImp.getInstance().getUsuario());
        if (CollectionUtils.isNotNullEmpty(empresas)) {
            List<Empresa> empresaList = LoadManager.getInstance(Empresa.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Empresa.PROP_CODIGO, BuilderQueryCustom.QueryParameter.IN, empresas))
                    .start().getList();
            unidade = empresaList;
        }
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ColumnFactory columnFactory = new ColumnFactory(QuartoInternacao.class);

        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), VOUtils.montarPath(QuartoInternacao.PROP_REFERENCIA)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(QuartoInternacao.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("estabelecimento"), VOUtils.montarPath(QuartoInternacao.PROP_EMPRESA, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipo"), VOUtils.montarPath(QuartoInternacao.PROP_TIPO_QUARTO), VOUtils.montarPath(QuartoInternacao.PROP_DESCRICAO_TIPO_QUARTO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(QuartoInternacao.PROP_SITUACAO), VOUtils.montarPath(QuartoInternacao.PROP_SITUACAO_DESCRICAO)));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<QuartoInternacao>() {
            @Override
            public void customizeColumn(final QuartoInternacao rowObject) {
                addAction(ActionType.EDITAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroQuartosLeitosStep1(rowObject));
                    }
                });

                addAction(ActionType.REATIVAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).reativarQuartoInternacao(rowObject);
                        getPageableTable().update(target);
                    }
                }).setQuestionDialogBundleKey("desejaRealmenteReativarEsteQuarto")
                        .setTitleBundleKey("reativarQuartoInternacao")
                        .setEnabled(isActionPermitted(Permissions.EDITAR))
                        .setVisible(rowObject.getSituacao().equals(QuartoInternacao.Situacao.DESATIVADO.value()));

                addAction(ActionType.DESATIVAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        dlgMotivo.setObject(rowObject);
                        dlgMotivo.show(target);
                    }
                }).setQuestionDialogBundleKey(null)
                        .setTitleBundleKey("desativarQuartoTemporariamente")
                        .setEnabled(isActionPermitted(Permissions.EDITAR))
                        .setVisible(rowObject.getSituacao().equals(QuartoInternacao.Situacao.ATIVO.value()));

                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).excluirQuartoInternacao(rowObject);
                        getPageableTable().populate(target);
                    }
                }).setQuestionDialogBundleKey("desejaRealmenteExcluirEsteQuartoTodosSeusLeitos")
                        .setEnabled(isActionPermitted(Permissions.EDITAR));

                addAction(ActionType.DESATIVAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).isolarQuartoInternacao(rowObject);
                        getPageableTable().update(target);
                    }
                }).setIcon(Icon.ROUND_MINUS)
                        .setQuestionDialogBundleKey("desejaRealmenteIsolarEsteQuarto")
                        .setTitleBundleKey("isolarQuarto")
                        .setEnabled(isActionPermitted(Permissions.ENCAMINHAR))
                        .setVisible(rowObject.getSituacao().equals(QuartoInternacao.Situacao.ATIVO.value()));
                
                addAction(ActionType.REATIVAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(HospitalFacade.class).reativarQuartoIsoladoInternacao(rowObject);
                        getPageableTable().update(target);
                    }
                }).setIcon(Icon.ROUND_CHECKMARK)
                        .setTitleBundleKey("liberarQuartoIsolado")
                        .setEnabled(isActionPermitted(Permissions.ENCAMINHAR))
                        .setVisible(rowObject.getSituacao().equals(QuartoInternacao.Situacao.ISOLADO.value()));
                
                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroQuartosLeitosDetalhes(rowObject));
                    }
                });
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaQuartoInternacao()) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(QuartoInternacao.PROP_DESCRICAO, true);
            }

            @Override
            public List getInterceptors() {
                return Arrays.asList(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        if (tipoLeito != null) {
                            StringBuilder where = new StringBuilder();
                            where.append("select 1 ");
                            where.append("from LeitoQuarto lq ");
                            where.append("where lq.quartoInternacao = ").append(alias).append(".codigo and lq.tipoLeito = ");
                            where.append(tipoLeito.longValue());
                            where.insert(0, "exists(").append(")");
                            hql.addToWhereWhithAnd(where.toString());
                        }
                    }
                });
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(QuartoInternacao.PROP_REFERENCIA), referencia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(QuartoInternacao.PROP_EMPRESA), BuilderQueryCustom.QueryParameter.IN, unidade));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(QuartoInternacao.PROP_EMPRESA), empresa));
        parameters.add(new QueryCustom.QueryCustomParameter(QuartoInternacao.PROP_SITUACAO, QueryCustom.QueryCustomParameter.DIFERENTE, QuartoInternacao.Situacao.EXCLUIDO.value()));
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroQuartosLeitosStep1.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaQuartos");
    }
}
