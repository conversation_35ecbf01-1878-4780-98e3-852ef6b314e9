package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;

public enum SituacaoMercadoTrabalhoEnum implements IEnum<SituacaoMercadoTrabalhoEnum> {
    REGISTRADO_CARTEIRA_ASSINADA(1L, "Empregado registrado com carteira assinada"),
    NAO_REGISTRADO(2L, "Empregado não registrado"),
    AUTONOMO(3L, "Autônomo/conta própria"),
    SERVIDOR_PUBLICO(4L, "Servidor público estatuário"),
    SERVIDOR_PUBLICO_CELETISTA(5L, "Servidor público celetista"),
    APOSENTADO(6L, "Aposentado"),
    DESEMPREGADO(7L, "Desempregado"),
    TRABALHO_TEMPORARIO(8L, "Trabalho temporário"),
    COOPERATIVADO(9L, "Cooperativado"),
    TRABALHADOR_AVULSO(10L, "Trabalhador avulso"),
    EMPREGADOR(11L, "Empregador"),
    OUTROS(12L, "Outros"),
    IGNORADO(99L, "Ignorado");

    private Long value;
    private String descricao;

    SituacaoMercadoTrabalhoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static SituacaoMercadoTrabalhoEnum valueOf(Long value) {
        for (SituacaoMercadoTrabalhoEnum v : SituacaoMercadoTrabalhoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
