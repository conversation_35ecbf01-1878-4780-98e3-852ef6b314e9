package br.com.celk.view.laboratorio.modeloresultado;

import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.laboratorio.ModeloResultadoLaboratorio;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import static ch.lambdaj.Lambda.on;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaModeloResultadoPage extends ConsultaPage<ModeloResultadoLaboratorio, List<BuilderQueryCustom.QueryParameter>>{

    private String exame;
    
    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField<String>("exame"));
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        ModeloResultadoLaboratorio proxy = on(ModeloResultadoLaboratorio.class);
        
        columns.add(getCustomColumn());
        columns.add(createSortableColumn(bundle("exame"), proxy.getExameProcedimento().getDescricaoFormatado()));
        return columns;
    }
    
    private CustomColumn<ModeloResultadoLaboratorio> getCustomColumn() {
        return new CustomColumn<ModeloResultadoLaboratorio>() {

            @Override
            public Component getComponent(String componentId, final ModeloResultadoLaboratorio rowObject) {
                return new CrudActionsColumnPanel(componentId) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroModeloResultadoPage(rowObject));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        BOFactoryWicket.getBO(CadastroFacade.class).delete(rowObject);
                        getPageableTable().populate(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroModeloResultadoPage(rowObject, true));
                    }
                };
            }
        };
    }
    
    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return ModeloResultadoLaboratorio.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(ModeloResultadoLaboratorio.class).getProperties(),
                        new String[]{
                    VOUtils.montarPath(ModeloResultadoLaboratorio.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO)
                });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ModeloResultadoLaboratorio.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ModeloResultadoLaboratorio.PROP_EXAME_PROCEDIMENTO,ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO), BuilderQueryCustom.QueryParameter.ILIKE, exame));
        
        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return CadastroModeloResultadoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaModeloResultado");
    }
    
}
