package br.com.celk.view.unidadesaude.esus.cadastro;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliarProcedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroVisitaDomiciliarProcedimentoPage extends CadastroPage<VisitaDomiciliarProcedimento> {

    private AutoCompleteConsultaProcedimento autoCompleteConsultaProcedimento;
    private AutoCompleteConsultaTabelaCbo autoCompleteConsultaTabelaCbo;

    public CadastroVisitaDomiciliarProcedimentoPage() {
    }

    public CadastroVisitaDomiciliarProcedimentoPage(VisitaDomiciliarProcedimento object) {
        super(object);
    }

    public CadastroVisitaDomiciliarProcedimentoPage(VisitaDomiciliarProcedimento object, boolean viewOnly) {
        super(object, viewOnly);
    }

    @Override
    public void init(Form<VisitaDomiciliarProcedimento> form) {
        VisitaDomiciliarProcedimento proxy = on(VisitaDomiciliarProcedimento.class);

        form.add(autoCompleteConsultaProcedimento = new AutoCompleteConsultaProcedimento(path(proxy.getProcedimento()), true));
        form.add(autoCompleteConsultaTabelaCbo = new AutoCompleteConsultaTabelaCbo(path(proxy.getCbo()), true));
        autoCompleteConsultaTabelaCbo.setFiltrarAtivos(true);
        if (form.getModelObject().getProcedimento() == null) {
            autoCompleteConsultaTabelaCbo.setEnabled(false);
        }
        autoCompleteConsultaProcedimento.add(new ConsultaListener<Procedimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Procedimento object) {
                autoCompleteConsultaTabelaCbo.setEnabled(true);
                autoCompleteConsultaTabelaCbo.setProcedimento(object);
                autoCompleteConsultaTabelaCbo.limpar(target);
            }
        });
        autoCompleteConsultaProcedimento.add(new RemoveListener<Procedimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Procedimento object) {
                autoCompleteConsultaTabelaCbo.limpar(target);
                autoCompleteConsultaTabelaCbo.setEnabled(false);
            }
        });
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroProcedimentoVisitaDomiciliar");
    }

    @Override
    public Object salvar(VisitaDomiciliarProcedimento object) throws DAOException, ValidacaoException {
        LoadManager load = LoadManager.getInstance(VisitaDomiciliarProcedimento.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(VisitaDomiciliarProcedimento.PROP_CBO, TabelaCbo.PROP_CBO), object.getCbo().getCbo()));
        if (object.getCodigo() != null) {
            load.addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliarProcedimento.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, object.getCodigo()));
        }
        VisitaDomiciliarProcedimento vdp = load.start().getVO();
        if (vdp != null) {
            throw new ValidacaoException(bundle("CboJaCadastrado"));
        }
        return BOFactoryWicket.save(object);
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaProcedimento;
    }

    @Override
    public Class<VisitaDomiciliarProcedimento> getReferenceClass() {
        return VisitaDomiciliarProcedimento.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaVisitaDomiciliarProcedimentoPage.class;
    }
}
