package br.com.celk.view.frota.motorista;

import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom.QueryParameter;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.frota.Motorista;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaMotorista extends CustomizeConsultaAdapter {

    @Override
    public void consultaCustomizeFilterProperties(Map<String, QueryParameter> filterProperties) {
        filterProperties.put(BundleManager.getString("nome"), new QueryCustom.QueryCustomParameter(Motorista.PROP_NOME, QueryParameter.ILIKE));
    }

    @Override
    public void consultaCustomizeViewProperties(Map<String, String> properties) {
        properties.put(BundleManager.getString("referencia"), Motorista.PROP_REFERENCIA);
        properties.put(BundleManager.getString("nome"), Motorista.PROP_NOME);
    }

    @Override
    public Class getClassConsulta() {
        return Motorista.class;
    }

    @Override
    public String[] getProperties() {
        return VOUtils.mergeProperties(
                new HQLProperties(Motorista.class).getProperties(),
                new String[]{VOUtils.montarPath(Motorista.PROP_PROFISSIONAL, Profissional.PROP_NOME)});
    }

}
