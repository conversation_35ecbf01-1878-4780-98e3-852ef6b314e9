package br.com.celk.view.unidadesaude.anexo;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dialog.DlgMotivoObject;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.unidadesaude.tipoanexo.autocomplete.AutoCompleteConsultaTipoAnexo;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.AnexoPaciente;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAnexo;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaAnexoPage extends ConsultaPage<AnexoPaciente, List<BuilderQueryCustom.QueryParameter>> {

    private String paciente;
    private TipoAnexo tipoAnexo;
    private String keyword;
    private DatePeriod periodo;
    private DlgMotivoObject dlgMotivoCancelamento;

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));
        form.add(new InputField("paciente"));
        form.add(new AutoCompleteConsultaTipoAnexo("tipoAnexo"));
        form.add(new InputField("keyword"));
        form.add(new PnlDatePeriod("periodo"));

        setExibeExpandir(true);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AnexoPaciente proxy = on(AnexoPaciente.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createColumn(bundle("tipoAnexo"), proxy.getTipoAnexo().getDescricao()));
        columns.add(createColumn(bundle("palavraChave"), proxy.getKeyword()));
        columns.add(createColumn(bundle("dataDocumento"), proxy.getDataDocumento()));

        return columns;
    }

    private CustomColumn<AnexoPaciente> getCustomColumn() {
        return new CustomColumn<AnexoPaciente>() {
            @Override
            public Component getComponent(String componentId, final AnexoPaciente rowObject) {
                return new CrudActionsColumnPanel(componentId) {
                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        validarAnexoMesmoDia(rowObject.getDataCadastro());
                        setResponsePage(new CadastroAnexoPage(rowObject.getCodigo(), false));
                    }

                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        validarAnexoMesmoDia(rowObject.getDataCadastro());
                        viewDlgMotivoCancelamento(target, rowObject);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) {
                        setResponsePage(new CadastroAnexoPage(rowObject.getCodigo(), true));
                    }
                };
            }
        };
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return AnexoPaciente.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(
                        new HQLProperties(AnexoPaciente.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO),
                                VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                                VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
                                VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(AnexoPaciente.PROP_DATA_DOCUMENTO, false);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();
        Empresa empresaLogado = SessaoAplicacaoImp.getInstance().getEmpresa();

        if (paciente != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, paciente))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPaciente.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, paciente))))));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(AnexoPaciente.PROP_TIPO_ANEXO, tipoAnexo));
        parameters.add(new QueryCustom.QueryCustomParameter(AnexoPaciente.PROP_KEYWORD, BuilderQueryCustom.QueryParameter.ILIKE, keyword));
        parameters.add(new QueryCustom.QueryCustomParameter(AnexoPaciente.PROP_DATA_DOCUMENTO, periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(AnexoPaciente.PROP_STATUS, AnexoPaciente.Status.NORMAL.value()));

        parameters.add(new QueryCustom.QueryCustomParameter(
                new BuilderQueryCustom.QueryGroupAnd(
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPaciente.PROP_EMPRESA), BuilderQueryCustom.QueryParameter.IS_NULL))),
                        new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AnexoPaciente.PROP_EMPRESA), empresaLogado))))));

        return parameters;
    }

    private void validarAnexoMesmoDia(Date dataCadastro) throws ValidacaoException {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(DataUtil.getDataAtual());
        calendar.add(Calendar.DAY_OF_MONTH, -1);

        if (dataCadastro.before(calendar.getTime())) {
            throw new ValidacaoException(bundle("msgSomentePermitidoEditarExcluirAnexoIncluidoMesmoDia"));
        }
    }

    private void viewDlgMotivoCancelamento(AjaxRequestTarget target, AnexoPaciente anexoPaciente) {
        if (dlgMotivoCancelamento == null) {
            addModal(target, dlgMotivoCancelamento = new DlgMotivoObject<AnexoPaciente>(newModalId(), bundle("motivo")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, String motivo, AnexoPaciente anexoPaciente) throws ValidacaoException, DAOException {
                    anexoPaciente.setMotivoCancelamento(motivo);
                    anexoPaciente.setStatus(AnexoPaciente.Status.CANCELADO.value());
                    BOFactoryWicket.save(anexoPaciente);
                    getPageableTable().populate(target);
                }
            });
        }
        dlgMotivoCancelamento.setObject(anexoPaciente);
        dlgMotivoCancelamento.show(target);
    }

    @Override
    public Class getCadastroPage() {
        return CadastroAnexoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("consultaAnexos");
    }

}
