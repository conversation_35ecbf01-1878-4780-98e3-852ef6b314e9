package br.com.celk.view.vigilancia.registroagravo;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.radio.RadioButtonGroup;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaPais;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.vigilancia.registroagravo.panel.FichaInvestigacaoAgravoBasePage;
import br.com.celk.vigilancia.dto.FichaInvestigacaoAgravoRubeolaSarampoDTO;
import br.com.celk.vigilancia.dto.Ficha********************************DTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.*;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.request.resource.CssResourceReference;
import org.odlabs.wiquery.ui.datepicker.DateOption;

import java.util.Date;

import static br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoHelper.createRadioSimNaoIgnorado;
import static br.com.celk.view.vigilancia.registroagravo.FichaInvestigacaoAgravoHelper.isLongSim;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public class Ficha********************************Page extends FichaInvestigacaoAgravoBasePage {
    private final String CSSFILE = "Ficha********************************Page.css";

    private ******************************** ********************************;

    private WebMarkupContainer containerInvestigacao;
    private DateChooser dataInvestigacao;
    private DisabledInputField ocupacaoCbo;

    private WebMarkupContainer containerAntecedentesEpidemiologicosMae;
    private DropDown ddNumConsultasPreNatal;
    private DropDown ddAntecedentesVacinais;

    private DateChooser data1Dose;
    private DateChooser data2Dose;
    private DateChooser data3Dose;
    private DateChooser dataUltimoReforco;

    private InputField txtIdadeMae;
    private DropDown ddNumGestacao;
    private DropDown ddEscolaridadeMae;


    private WebMarkupContainer containerAntecedentesEpidemiologicosRn;
    private DropDown ddLocalParto;
    private InputField txtOutroLocalParto;

    private DropDown ddPartoAtendido;
    private InputField txtOutroPartoAtendido;

    private DropDown ddSugouAposNascimento;


    private WebMarkupContainer containerDadosClinicos;
    private DropDown ddSinaisSintomasDificuldadeMamar;
    private DropDown ddSinaisSintomasChoroExcessivo;
    private DropDown ddSinaisSintomasProcessoInflamatorio;
    private DropDown ddSinaisSintomasCrisesContraturas;
    private DropDown ddSinaisSintomasTrismo;
    private DropDown ddSinaisSintomasContraturaLabial;
    private DropDown ddSinaisSintomasOpistotono;
    private DropDown ddSinaisSintomasRigidezNuca;
    private DropDown ddSinaisSintomasRigidezAbdominal;
    private DropDown ddSinaisSintomasRigidezMembros;
    private InputField txtOutroSinaisSintomas;
    private DateChooser dataTrismo;


    private WebMarkupContainer containerAtendimento;
    private DropDown ddOrigemCaso;
    private DropDown ddLocalResidencia;
    private InputField txtOutroLocalResidencia;

    private RadioButtonGroup radioGroupHospitalizacao;
    private WebMarkupContainer containerHospitalizacao;
    private DateChooser dataInternacao;
    private DisabledInputField estadoHospitalizacao;
    private DisabledInputField municipioHospitalizacao;
    private DisabledInputField codMunicipioHospital;
    private AutoCompleteConsultaEmpresa autoCompleteUnidadeHospitalizacao;
    private DisabledInputField unidadeHospitalizacaoCnes;
    private DisabledInputField telefoneHospital;


    private WebMarkupContainer containerMedidasControle;
    private DropDown ddMedidasControleEsquemaVacinalMae;
    private DropDown ddMedidasControleBuscaAtiva;
    private DropDown ddMedidasControleAnaliseCv;
    private DropDown ddMedidasControleCadastroParteiras;
    private DropDown ddMedidasControleOrientacaoParturientes;
    private DropDown ddMedidasControleDivulgacaoProblema;
    private InputField txtOutrasMedidasControle;


    private RadioButtonGroup radioGroupCasoAutoctone;
    private WebMarkupContainer containerLocalInfeccao;
    private DropDown ddLocalProvavelInfeccao;
    private InputField txtLocalProvavelInfeccaoOutro;
    private AutoCompleteConsultaEmpresa autoCompleteUnidadeLocalInfeccao;
    private DisabledInputField unidadeLocalInfeccaoCnes;
    private AutoCompleteConsultaCidade autoCompleteCidadeLocalInfeccao;
    private DisabledInputField estadoLocalInfeccao;
    private AutoCompleteConsultaPais autoCompletePaisLocalInfeccao;
    private DisabledInputField codMunicipioLocalInfeccao;
    private InputField distritoLocalInfeccao;
    private InputField bairroLocalInfeccao;


    private WebMarkupContainer containerConclusao;
    private DropDown ddClassificacaoFinal;
    private DropDown ddEvolucaoCaso;
    private DateChooser dataObito;


    private WebMarkupContainer containerObservacoes;

    private WebMarkupContainer containerEncerramento;
    private DisabledInputField usuarioEncerramento;
    private DisabledInputField dataEncerramento;


    public Ficha********************************Page(Long idRegistroAgravo, boolean modoLeitura) {
        super(idRegistroAgravo, modoLeitura);
    }

    @Override
    public void carregarFicha() {
        ******************************** = ********************************.buscaPorRegistroAgravo(getAgravo());
    }

    @Override
    public void inicializarForm() {
        if (******************************** == null) {
            ******************************** = new ********************************();
            ********************************.setFlagInformacoesComplementares(RepositoryComponentDefault.SIM);
            ********************************.setRegistroAgravo(getAgravo());
        }

        TabelaCbo tabelaCbo = FichaInvestigacaoAgravoHelper.getTabelaCboByCodUser(********************************.getRegistroAgravo().getUsuarioCadsus().getCodigo());
        if (tabelaCbo != null) {
            ********************************.getRegistroAgravo().getUsuarioCadsus().setTabelaCbo(tabelaCbo);
            ********************************.setOcupacaoCbo(tabelaCbo);
        }

        if (********************************.getCidadeLocalInfeccao() != null) {
            Cidade cidadeExposicao = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(********************************.getCidadeLocalInfeccao().getCodigo());
            ********************************.setCidadeLocalInfeccao(cidadeExposicao);
        }

        setForm(new Form("form", new CompoundPropertyModel(********************************)));
    }

    @Override
    public Object getFichaDTO() {
        Ficha********************************DTO fichaDTO = new Ficha********************************DTO();
        fichaDTO.setRegistroAgravo(getAgravo());
        fichaDTO.set********************************(********************************);
        return fichaDTO;
    }

    @Override
    public String carregarInformacoesComplementares() {
        return ********************************.getFlagInformacoesComplementares() == null
                ? "S" : ********************************.getFlagInformacoesComplementares();
    }

    @Override
    public void inicializarFicha() {
        ******************************** proxy = on(********************************.class);
        criarInvestigacao(proxy);
        criarAntecedentesEpidemiologicosMae(proxy);
        criarAntecedentesEpidemiologicosRN(proxy);
        criarDadosClinicos(proxy);
        criarAtendimento(proxy);
        criarMedidasControle(proxy);
        criarHospitalizacao(proxy);
        criarLocalInfeccao(proxy);
        criaConclusao(proxy);
        criarObservacoes(proxy);
        criarUsuarioDataEncerramento(proxy);
    }

    @Override
    public void inicializarRegrasComponentes(AjaxRequestTarget target) {
        carregarInvestigacao();
        carregarAntecedentesEpidemiologicosMae();
        carregarAntecedentesEpidemiologicosRN();
        carregarDadosClinicos();
        carregarAtendimento();
        carregarHospitalizacao();
        carregarLocalInfeccao();
        carregarConclusao();
    }

    @Override
    public void validarFicha() throws ValidacaoException {
        if(data1Dose.getComponentValue() != null && data2Dose.getComponentValue() != null ){
            validarRegra(
                    isBefore(data1Dose.getData().getConvertedInput(), ********************************.getData2Dose()),
                    dataInternacao,
                    "msg_validacao_data_2dose");
        }
        if(data2Dose.getComponentValue() != null && data3Dose.getComponentValue() != null ){
            validarRegra(
                    isBefore(data2Dose.getData().getConvertedInput(), ********************************.getData3Dose()),
                    dataInternacao,
                    "msg_validacao_data_3dose");
        }
        if(data3Dose.getComponentValue() != null && dataUltimoReforco.getComponentValue() != null ){
            validarRegra(
                    isBefore(data3Dose.getData().getConvertedInput(), ********************************.getDataUltimoReforco()),
                    dataInternacao,
                    "msg_validacao_data_ultimo_reforco");
        }

    }

    /**
     * Verifica se a primeira data vem ANTES da segunda
     *
     * @param data1
     * @param data2
     * @return
     */
    public boolean isBefore(Date data1, Date data2) {
        return (data1.compareTo(data2) < 0);
    }

    @Override
    public void salvarFicha(Object fichaDTO) throws ValidacaoException, DAOException {
        Ficha********************************DTO dto = (Ficha********************************DTO) fichaDTO;

        validarFicha();

        BOFactoryWicket.getBO(VigilanciaFacade.class).salvarFicha********************************(dto);
        Page page = new ConsultaRegistroAgravoPage();
        setResponsePage(page);
    }

    @Override
    public Object getEncerrarFichaDTO() {
        Ficha********************************DTO fichaDTO = (Ficha********************************DTO) getFichaDTO();

        if (********************************.getUsuarioEncerramento() == null) {
            Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
            ********************************.setUsuarioEncerramento(usuarioLogado);
            ********************************.setDataEncerramento(DataUtil.getDataAtual());
        }

        fichaDTO.setEncerrarFicha(true);
        return fichaDTO;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(
                CssHeaderItem.forReference(new CssResourceReference(Ficha********************************Page.class, CSSFILE))
        );
    }

    private void criarInvestigacao(******************************** proxy) {
        containerInvestigacao = new WebMarkupContainer("containerInvestigacao");
        containerInvestigacao.setOutputMarkupId(true);

        dataInvestigacao = new DateChooser(path(proxy.getDataInvestigacao()));
        dataInvestigacao.setRequired(true).setEnabled(!isModoLeitura());
        dataInvestigacao.addRequiredClass();
        ocupacaoCbo = new DisabledInputField(path(proxy.getOcupacaoCbo().getDescricao()));

        containerInvestigacao.add(dataInvestigacao, ocupacaoCbo);
        getContainerInformacoesComplementares().add(containerInvestigacao);
    }
    private void carregarInvestigacao() {
        FichaInvestigacaoAgravoHelper.enableDisableDates(dataInvestigacao, !isModoLeitura(), true, null);
        dataInvestigacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
    }


    private void criarAntecedentesEpidemiologicosMae(******************************** proxy) {
        containerAntecedentesEpidemiologicosMae = new WebMarkupContainer("containerAntecedentesEpidemiologicosMae");
        containerAntecedentesEpidemiologicosMae.setOutputMarkupId(true);

        ddNumConsultasPreNatal = DropDownUtil.getIEnumDropDown(path(proxy.getNumConsultasPreNatal()), ********************************Enum.NumeroEnum.values(), true);
        ddAntecedentesVacinais = DropDownUtil.getIEnumDropDown(path(proxy.getAntecedentesVacinais()), ********************************Enum.AntecedentesVacinaisEnum.values(), true);

        data1Dose = new DateChooser(path(proxy.getData1Dose()));
        data1Dose.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        data2Dose = new DateChooser(path(proxy.getData2Dose()));
        data2Dose.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        data3Dose = new DateChooser(path(proxy.getData3Dose()));
        data3Dose.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));
        dataUltimoReforco = new DateChooser(path(proxy.getDataUltimoReforco()));
        dataUltimoReforco.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        txtIdadeMae = new InputField(path(proxy.getIdadeMae()));
        ddNumGestacao = DropDownUtil.getIEnumDropDown(path(proxy.getNumGestacao()), ********************************Enum.NumeroGestacoesEnum.values(), true);
        ddEscolaridadeMae = DropDownUtil.getIEnumDropDown(path(proxy.getEscolaridadeMae()), UsuarioCadsusEsus.NivelEscolaridade.values(), true);


        containerAntecedentesEpidemiologicosMae.add(ddNumConsultasPreNatal, ddAntecedentesVacinais, data1Dose, data2Dose, data3Dose, dataUltimoReforco,
                txtIdadeMae, ddNumGestacao, ddEscolaridadeMae
        );
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicosMae);
    }

    private void carregarAntecedentesEpidemiologicosMae(){
        if (********************************.getData1Dose() == null){
            FichaInvestigacaoAgravoHelper.enableDisableDates(data1Dose, false, false, null);
        }
        if (********************************.getData2Dose() == null){
            FichaInvestigacaoAgravoHelper.enableDisableDates(data2Dose, false, false, null);
        }
        if (********************************.getData3Dose() == null){
            FichaInvestigacaoAgravoHelper.enableDisableDates(data3Dose, false, false, null);
        }
        if (********************************.getDataUltimoReforco() == null){
            FichaInvestigacaoAgravoHelper.enableDisableDates(dataUltimoReforco, false, false, null);
        }


        ddAntecedentesVacinais.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddAntecedentesVacinais, ********************************Enum.AntecedentesVacinaisEnum.VACINADA.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableDates(data1Dose, true, true, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDates(data2Dose, true, true, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDates(data3Dose, true, true, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDates(dataUltimoReforco, true, false, target);
                }
                else {
                    FichaInvestigacaoAgravoHelper.enableDisableDates(data1Dose, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDates(data2Dose, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDates(data3Dose, false, false, target);
                    FichaInvestigacaoAgravoHelper.enableDisableDates(dataUltimoReforco, false, false, target);
                }
                target.add(data1Dose, data2Dose, data3Dose, dataUltimoReforco);
            }
        });
    }

    private void criarAntecedentesEpidemiologicosRN(******************************** proxy) {
        containerAntecedentesEpidemiologicosRn = new WebMarkupContainer("containerAntecedentesEpidemiologicosRn");
        containerAntecedentesEpidemiologicosRn.setOutputMarkupId(true);

        ddLocalParto = DropDownUtil.getIEnumDropDown(path(proxy.getLocalParto()), ********************************Enum.LocalEnum.values(), true);
        txtOutroLocalParto = new InputField(path(proxy.getOutroLocalParto()));

        ddPartoAtendido = DropDownUtil.getIEnumDropDown(path(proxy.getPartoAtendido()), ********************************Enum.PartoAtendidoEnum.values(), true);
        txtOutroPartoAtendido = new InputField(path(proxy.getPartoAtendidoOutro()));

        ddSugouAposNascimento = DropDownUtil.getIEnumDropDown(path(proxy.getSugouAposNascimento()), ********************************Enum.SimNaoIgnoradoEnum.values(), true);

        containerAntecedentesEpidemiologicosRn.add(ddLocalParto, txtOutroLocalParto, ddPartoAtendido, txtOutroPartoAtendido, ddSugouAposNascimento);
        getContainerInformacoesComplementares().add(containerAntecedentesEpidemiologicosRn);
    }

    private void carregarAntecedentesEpidemiologicosRN(){
        if (********************************.getOutroLocalParto() == null){
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutroLocalParto, false, false, null);
        }
        if (********************************.getPartoAtendidoOutro() == null){
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutroPartoAtendido, false, false, null);
        }

        ddLocalParto.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddLocalParto, ********************************Enum.LocalEnum.OUTRO.value())) {
                    txtOutroLocalParto.setEnabled(true);
                    txtOutroLocalParto.limpar(target);
                }
                else {
                    txtOutroLocalParto.setEnabled(false);
                    txtOutroLocalParto.limpar(target);
                }
                target.add(txtOutroLocalParto);
            }
        });

        ddPartoAtendido.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddPartoAtendido, ********************************Enum.PartoAtendidoEnum.OUTRO.value())) {
                    txtOutroPartoAtendido.setEnabled(true);
                    txtOutroLocalParto.limpar(target);
                }
                else {
                    txtOutroPartoAtendido.setEnabled(false);
                    txtOutroLocalParto.limpar(target);
                }

                target.add(txtOutroPartoAtendido);
            }
        });
    }

    private void criarDadosClinicos(******************************** proxy) {
        containerDadosClinicos = new WebMarkupContainer("containerDadosClinicos");
        containerDadosClinicos.setOutputMarkupId(true);

        ddSinaisSintomasDificuldadeMamar = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasDificuldadeMamar()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasChoroExcessivo = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasChoroExcessivo()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasProcessoInflamatorio = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasProcessoInflamatorio()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasCrisesContraturas = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasCrisesContraturas()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasTrismo = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasTrismo()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasContraturaLabial = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasContraturaLabial()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasOpistotono = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasOpistotono()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasRigidezNuca = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasRigidezNuca()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasRigidezAbdominal = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasRigidezAbdominal()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        ddSinaisSintomasRigidezMembros = DropDownUtil.getIEnumDropDown(path(proxy.getSinaisSintomasRigidezMembros()), ********************************Enum.SimNaoIgnoradoEnum.values(), true, true);
        txtOutroSinaisSintomas = new InputField(path(proxy.getSinaisSintomasOutros()));

        dataTrismo = new DateChooser(path(proxy.getDataTrismo()));
        dataTrismo.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        containerDadosClinicos.add(ddSinaisSintomasDificuldadeMamar, ddSinaisSintomasChoroExcessivo, ddSinaisSintomasProcessoInflamatorio, ddSinaisSintomasCrisesContraturas,
                ddSinaisSintomasTrismo, ddSinaisSintomasContraturaLabial, ddSinaisSintomasOpistotono, ddSinaisSintomasRigidezNuca, ddSinaisSintomasRigidezAbdominal,
                ddSinaisSintomasRigidezMembros, txtOutroSinaisSintomas, dataTrismo
        );
        getContainerInformacoesComplementares().add(containerDadosClinicos);
    }

    private void carregarDadosClinicos(){
        if (********************************.getSinaisSintomasDificuldadeMamar() == null){
            ddSinaisSintomasDificuldadeMamar.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasChoroExcessivo() == null){
            ddSinaisSintomasChoroExcessivo.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasProcessoInflamatorio() == null){
            ddSinaisSintomasProcessoInflamatorio.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasCrisesContraturas() == null){
            ddSinaisSintomasCrisesContraturas.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasTrismo() == null){
            ddSinaisSintomasTrismo.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasContraturaLabial() == null){
            ddSinaisSintomasContraturaLabial.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasOpistotono() == null){
            ddSinaisSintomasOpistotono.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasRigidezNuca() == null){
            ddSinaisSintomasRigidezNuca.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasRigidezAbdominal() == null){
            ddSinaisSintomasRigidezAbdominal.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }
        if (********************************.getSinaisSintomasRigidezMembros() == null){
            ddSinaisSintomasRigidezMembros.setComponentValue(********************************Enum.SimNaoIgnoradoEnum.NAO.value());

        }

        if (********************************.getDataTrismo() == null){
            FichaInvestigacaoAgravoHelper.enableDisableDates(dataTrismo, false, false, null);
        }
        ddSinaisSintomasTrismo.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddSinaisSintomasTrismo, ********************************Enum.SimNaoIgnoradoEnum.SIM.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableDates(dataTrismo, true, true, target);
                }
                else {
                    FichaInvestigacaoAgravoHelper.enableDisableDates(dataTrismo, false, false, target);
                }

                target.add(dataTrismo);
            }
        });
    }

    private void criarAtendimento(******************************** proxy) {
        containerAtendimento = new WebMarkupContainer("containerAtendimento");
        containerAtendimento.setOutputMarkupId(true);

        ddOrigemCaso = DropDownUtil.getIEnumDropDown(path(proxy.getOrigemCaso()), ********************************Enum.OrigemCasoEnum.values(), true);

        ddLocalResidencia = DropDownUtil.getIEnumDropDown(path(proxy.getLocalResidencia()), ********************************Enum.LocalResidenciaEnum.values(), true);
        txtOutroLocalResidencia = new InputField(path(proxy.getOutroLocalResidencia()));

        containerAtendimento.add(ddOrigemCaso, ddLocalResidencia, txtOutroLocalResidencia);
        getContainerInformacoesComplementares().add(containerAtendimento);
    }

    private void carregarAtendimento(){
        if (********************************.getOutroLocalResidencia() == null){
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutroLocalResidencia, false, false, null);
        }
        ddLocalResidencia.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddLocalResidencia, ********************************Enum.LocalResidenciaEnum.OUTRO.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutroLocalResidencia, true, true, target);
                }
                else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtOutroLocalResidencia, false, false, target);
                }

                target.add(txtOutroLocalResidencia);
            }
        });
    }

    private void criarHospitalizacao(******************************** proxy) {
        containerHospitalizacao = new WebMarkupContainer("containerHospitalizacao");
        containerHospitalizacao.setOutputMarkupId(true);
        containerHospitalizacao.setEnabled(false);

        radioGroupHospitalizacao = new RadioButtonGroup(path(proxy.getHospitalizacao()));
        radioGroupHospitalizacao.setRequired(true);
        createRadioSimNaoIgnorado(radioGroupHospitalizacao, containerHospitalizacao);

        //Unidade Hospital
        dataInternacao = new DateChooser(path(proxy.getDataInternacao()));
        autoCompleteUnidadeHospitalizacao = new AutoCompleteConsultaEmpresa(path(proxy.getHospital()));
        unidadeHospitalizacaoCnes = new DisabledInputField(path(proxy.getHospital().getCnes()));
        telefoneHospital = new DisabledInputField(path(proxy.getHospital().getTelefoneFormatado()));
        municipioHospitalizacao = new DisabledInputField(path(proxy.getHospital().getCidade().getDescricao()));
        codMunicipioHospital = new DisabledInputField(path(proxy.getHospital().getCidade().getCodigo()));
        estadoHospitalizacao = new DisabledInputField(path(proxy.getHospital().getCidade().getEstado().getSigla()));

        containerHospitalizacao.add(
                dataInternacao,
                autoCompleteUnidadeHospitalizacao, unidadeHospitalizacaoCnes, telefoneHospital,
                municipioHospitalizacao, codMunicipioHospital, estadoHospitalizacao);

        getContainerInformacoesComplementares().add(radioGroupHospitalizacao, containerHospitalizacao);
    }

    private void carregarHospitalizacao() {
        containerHospitalizacao.setEnabled(isLongSim(********************************.getHospitalizacao()));

        dataInternacao.getData().setMinDate(new DateOption(********************************.getRegistroAgravo().getDataPrimeirosSintomas()));
        dataInternacao.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        autoCompleteUnidadeHospitalizacao.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                unidadeHospitalizacaoCnes.setComponentValue(empresa.getCnes());
                telefoneHospital.setComponentValue(empresa.getTelefoneFormatado());
                municipioHospitalizacao.setComponentValue(empresa.getCidade().getDescricao());
                codMunicipioHospital.setComponentValue(empresa.getCidade().getCodigo().toString());

                Cidade cidadeTemp = empresa.getCidade();

                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = LoadManager.getInstance(Cidade.class)
                            .addProperty(VOUtils.montarPath(Cidade.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(Cidade.PROP_DESCRICAO))
                            .addProperty(VOUtils.montarPath(Cidade.PROP_ESTADO, Estado.PROP_SIGLA))
                            .setId(empresa.getCidade().getCodigo())
                            .start().getVO();
                }

                estadoHospitalizacao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(unidadeHospitalizacaoCnes, telefoneHospital, municipioHospitalizacao, codMunicipioHospital, estadoHospitalizacao);
            }
        });
        autoCompleteUnidadeHospitalizacao.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                unidadeHospitalizacaoCnes.limpar(target);
                telefoneHospital.limpar(target);
                municipioHospitalizacao.limpar(target);
                codMunicipioHospital.limpar(target);
                estadoHospitalizacao.limpar(target);
            }
        });
    }

    private void criarMedidasControle(******************************** proxy) {
        containerMedidasControle = new WebMarkupContainer("containerMedidasControle");
        containerMedidasControle.setOutputMarkupId(true);

        ddMedidasControleEsquemaVacinalMae = DropDownUtil.getIEnumDropDown(path(proxy.getMedidasControleEsquemaVacinalMae()), ********************************Enum.SimNaoIgnoradoEnum.values(), true);
        ddMedidasControleBuscaAtiva = DropDownUtil.getIEnumDropDown(path(proxy.getMedidasControleBuscaAtiva()), ********************************Enum.SimNaoIgnoradoEnum.values(), true);
        ddMedidasControleAnaliseCv = DropDownUtil.getIEnumDropDown(path(proxy.getMedidasControleAnaliseCv()), ********************************Enum.SimNaoIgnoradoEnum.values(), true);
        ddMedidasControleCadastroParteiras = DropDownUtil.getIEnumDropDown(path(proxy.getMedidasControleCadastroParteiras()), ********************************Enum.SimNaoIgnoradoEnum.values(), true);
        ddMedidasControleOrientacaoParturientes = DropDownUtil.getIEnumDropDown(path(proxy.getMedidasControleOrientacaoParturientes()), ********************************Enum.SimNaoIgnoradoEnum.values(), true);
        ddMedidasControleDivulgacaoProblema = DropDownUtil.getIEnumDropDown(path(proxy.getMedidasControleDivulgacaoProblema()), ********************************Enum.SimNaoIgnoradoEnum.values(), true);
        txtOutrasMedidasControle = new InputField(path(proxy.getMedidasControleOutros()));

        containerMedidasControle.add(ddMedidasControleEsquemaVacinalMae, ddMedidasControleBuscaAtiva, ddMedidasControleAnaliseCv, ddMedidasControleCadastroParteiras,
                ddMedidasControleOrientacaoParturientes, ddMedidasControleDivulgacaoProblema, txtOutrasMedidasControle);
        getContainerInformacoesComplementares().add(containerMedidasControle);
    }

    private void criarLocalInfeccao(******************************** proxy) {
        containerLocalInfeccao = new WebMarkupContainer("containerLocalInfeccao");
        containerLocalInfeccao.setOutputMarkupId(true);

        radioGroupCasoAutoctone = new RadioButtonGroup(path(proxy.getCasoAutoctone()));
        FichaInvestigacaoAgravoHelper.createRadioSimNaoIgnoradoIndeterminado(radioGroupCasoAutoctone, containerLocalInfeccao, false, true, true, false, false, false);

        ddLocalProvavelInfeccao = DropDownUtil.getIEnumDropDown(path(proxy.getLocalProvavelInfeccao()), ********************************Enum.LocalOcorrenciaEnum.values(), true);
        txtLocalProvavelInfeccaoOutro = new InputField(path(proxy.getLocalProvavelInfeccaoOutro()));

        autoCompleteUnidadeLocalInfeccao = new AutoCompleteConsultaEmpresa(path(proxy.getLocalInfeccaoUnidade()));
        unidadeLocalInfeccaoCnes = new DisabledInputField(path(proxy.getLocalInfeccaoUnidade().getCnes()));

        autoCompleteCidadeLocalInfeccao = new AutoCompleteConsultaCidade(path(proxy.getCidadeLocalInfeccao()));
        codMunicipioLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getCodigo()));
        estadoLocalInfeccao = new DisabledInputField(path(proxy.getCidadeLocalInfeccao().getEstado().getSigla()));

        autoCompletePaisLocalInfeccao = new AutoCompleteConsultaPais(path(proxy.getPaisLocalInfeccao()));
        distritoLocalInfeccao = new InputField(path(proxy.getDistritoLocalInfeccao()));
        bairroLocalInfeccao = new InputField(path(proxy.getBairroLocalInfeccao()));

        containerLocalInfeccao.add(
                radioGroupCasoAutoctone, ddLocalProvavelInfeccao, txtLocalProvavelInfeccaoOutro,
                autoCompleteUnidadeLocalInfeccao, unidadeLocalInfeccaoCnes,
                autoCompleteCidadeLocalInfeccao, codMunicipioLocalInfeccao, estadoLocalInfeccao,
                autoCompletePaisLocalInfeccao, distritoLocalInfeccao, bairroLocalInfeccao
        );

        getContainerInformacoesComplementares().add(radioGroupCasoAutoctone, containerLocalInfeccao);
    }

    private void carregarLocalInfeccao() {

        if (********************************.getLocalProvavelInfeccaoOutro() == null){
            FichaInvestigacaoAgravoHelper.enableDisableInput(txtLocalProvavelInfeccaoOutro, false, false, null);
        }

        boolean notAutoctone = ********************************.getCasoAutoctone() != null && ********************************.getCasoAutoctone() == 2L;
        containerLocalInfeccao.setEnabled(notAutoctone);

        ddLocalProvavelInfeccao.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {

                if(!isModoLeitura() && FichaInvestigacaoAgravoHelper.isLongTrue(ddLocalProvavelInfeccao, ********************************Enum.LocalOcorrenciaEnum.OUTRO.value())) {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtLocalProvavelInfeccaoOutro, true, true, target);
                }
                else {
                    FichaInvestigacaoAgravoHelper.enableDisableInput(txtLocalProvavelInfeccaoOutro, false, false, target);
                }

                target.add(txtLocalProvavelInfeccaoOutro);
            }
        });

        autoCompleteUnidadeLocalInfeccao.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa empresa) {
                unidadeLocalInfeccaoCnes.setComponentValue(empresa.getCnes());

                target.add(unidadeLocalInfeccaoCnes);
            }
        });
        autoCompleteUnidadeLocalInfeccao.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                unidadeLocalInfeccaoCnes.limpar(target);
            }
        });

        autoCompletePaisLocalInfeccao.add(new ConsultaListener<Pais>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Pais pais) {
                if (!pais.getDescricao().equalsIgnoreCase("Brasil")) {
                    estadoLocalInfeccao.limpar(target);
                    autoCompleteCidadeLocalInfeccao.limpar(target);
                    codMunicipioLocalInfeccao.limpar(target);
                }
            }
        });
        autoCompletePaisLocalInfeccao.add(new RemoveListener<Pais>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Pais pais) {
                autoCompleteCidadeLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                codMunicipioLocalInfeccao.limpar(target);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new ConsultaListener<Cidade>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Cidade cidade) {
                codMunicipioLocalInfeccao.setComponentValue(cidade.getCodigo());

                Cidade cidadeTemp = cidade;
                if (cidadeTemp.getEstado() == null) {
                    cidadeTemp = FichaInvestigacaoAgravoHelper.getCidadeByCodigo(cidade.getCodigo());
                }

                estadoLocalInfeccao.setComponentValue(cidadeTemp.getEstado().getSigla());
                target.add(codMunicipioLocalInfeccao, estadoLocalInfeccao);
            }
        });

        autoCompleteCidadeLocalInfeccao.add(new RemoveListener<Cidade>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Cidade object) {
                codMunicipioLocalInfeccao.limpar(target);
                estadoLocalInfeccao.limpar(target);
                autoCompletePaisLocalInfeccao.limpar(target);
                distritoLocalInfeccao.limpar(target);
                bairroLocalInfeccao.limpar(target);
            }
        });
    }

    private void criaConclusao(******************************** proxy) {
        containerConclusao = new WebMarkupContainer("containerConclusao");
        containerConclusao.setOutputMarkupId(true);

        ddClassificacaoFinal = DropDownUtil.getIEnumDropDown(path(proxy.getClassificacaoFinal()), ********************************Enum.ClassificacaoFinalEnum.values(), true, true);
        ddEvolucaoCaso = DropDownUtil.getIEnumDropDown(path(proxy.getEvolucaoCaso()), ********************************Enum.EvolucaoCasoEnum.values(), true, true);
        dataObito = new DateChooser(path(proxy.getDataObito()));
        dataObito.getData().setMaxDate(new DateOption(DataUtil.getDataAtual()));

        containerConclusao.add(ddClassificacaoFinal, ddEvolucaoCaso, dataObito);

        getContainerInformacoesComplementares().add(containerConclusao);
    }

    private void carregarConclusao(){
        if (********************************.getDataObito() == null) {
            FichaInvestigacaoAgravoHelper.enableDisableDates(dataObito, false, false, null);
        }

        ddEvolucaoCaso.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                dataObito.setEnabled(!isModoLeitura() && (FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, ********************************Enum.EvolucaoCasoEnum.OBITO_TETANO_NEONATAL.value()))
                        ||(FichaInvestigacaoAgravoHelper.isLongTrue(ddEvolucaoCaso, ********************************Enum.EvolucaoCasoEnum.OBITO_OUTRAS_CAUSAS.value())) );
                dataObito.limpar(target);

                target.add(dataObito);
            }
        });
    }

    private void criarObservacoes(******************************** proxy) {
        containerObservacoes = new WebMarkupContainer("containerObservacoes");
        containerObservacoes.setOutputMarkupId(true);
        containerObservacoes.add(new InputArea(path(proxy.getObservacao())));

        getContainerInformacoesComplementares().add(containerObservacoes);
    }

    private void criarUsuarioDataEncerramento(******************************** proxy) {
        containerEncerramento = new WebMarkupContainer("containerEncerramento");
        containerEncerramento.setOutputMarkupId(true);

        usuarioEncerramento = new DisabledInputField(path(proxy.getUsuarioEncerramento()));
        dataEncerramento = new DisabledInputField(path(proxy.getDataEncerramento()));

        containerEncerramento.add(usuarioEncerramento, dataEncerramento);
        getContainerInformacoesComplementares().add(containerEncerramento);
    }
}
