package br.com.celk.view.basico.regionalsaude.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.ConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.basico.regionalsaude.autocomplete.restricaocontainer.RestricaoContainerRegionalSaude;
import br.com.ksisolucoes.bo.basico.dto.QueryConsultaRegionalSaudeDTOParam;
import br.com.ksisolucoes.bo.basico.interfaces.facade.BasicoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.RegionalSaude;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.model.IModel;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaRegionalSaude extends AutoCompleteConsulta<RegionalSaude> { 

    public AutoCompleteConsultaRegionalSaude(String id) {
        super(id);
    }

    public AutoCompleteConsultaRegionalSaude(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaRegionalSaude(String id, IModel<RegionalSaude> model) {
        super(id, model);
    }

    public AutoCompleteConsultaRegionalSaude(String id, IModel<RegionalSaude> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfigurator() {

            @Override
            public void getColumns(List<IColumn> columns) {
                ColumnFactory columnFactory = new ColumnFactory(RegionalSaude.class);
                
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), VOUtils.montarPath(RegionalSaude.PROP_CODIGO)));
                columns.add(columnFactory.createSortableColumn(BundleManager.getString("descricao"), VOUtils.montarPath(RegionalSaude.PROP_DESCRICAO)));
            }

            @Override
            public IRestricaoContainer getRestricaoContainerInstance(String id) {
                return new RestricaoContainerRegionalSaude(id);
            }

            @Override
            public IPagerProvider getDataProviderInstance() {
                return new QueryPagerProvider<RegionalSaude, QueryConsultaRegionalSaudeDTOParam>() {

                    @Override
                    public DataPagingResult executeQueryPager(DataPaging<QueryConsultaRegionalSaudeDTOParam> dataPaging) throws DAOException, ValidacaoException {
                        return BOFactoryWicket.getBO(BasicoFacade.class).consultarRegionalSaude(dataPaging);
                    }

                    @Override
                    public QueryConsultaRegionalSaudeDTOParam getSearchParam(String searchCriteria) {
                        QueryConsultaRegionalSaudeDTOParam param = new QueryConsultaRegionalSaudeDTOParam();
                        param.setKeyword(searchCriteria);
                        return param;
                    }
                    
                    @Override
                    public void customizeParam(QueryConsultaRegionalSaudeDTOParam param) {
                        param.setPropSort(getSort().getProperty());
                        param.setAscending(getSort().isAscending());
                    }
                    
                    @Override
                    public SortParam getDefaultSort() {
                        return new SortParam(VOUtils.montarPath(RegionalSaude.PROP_DESCRICAO), true);
                    }
                };
            }

            @Override
            public Class getReferenceClass() {
                return RegionalSaude.class;
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("regionalSaude");
    }

}
