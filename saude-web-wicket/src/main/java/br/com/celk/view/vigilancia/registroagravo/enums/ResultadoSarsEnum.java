package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

/**
 * <AUTHOR>
 */

public enum ResultadoSarsEnum implements IEnum {
    POSITIVO(1L, "Positivo"),
    NEGATIVO(2L, "Negativo"),
    INCONCLUSIVO(3L, "Inconclusivo"),
    NAO_REALIZADO(4L, "Não Realizado"),
    AGUARDANDO_RESULTADO(5L, "Aguardando Resultado"),
    IGNORADO(9L, "Ignorado");

    private Long value;
    private String descricao;

    ResultadoSarsEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static ResultadoSarsEnum valueOf(Long value) {
        for (ResultadoSarsEnum v : ResultadoSarsEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
