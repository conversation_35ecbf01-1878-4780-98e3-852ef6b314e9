package br.com.celk.view.agenda.cadastro;

import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.agendamento.CadastroAgendaBehavior;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.DayColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Icon;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.cadastro.dialog.DlgAgendaGradeExame;
import br.com.celk.view.agenda.cadastro.dialog.DlgAgendaGradeHorario;
import br.com.ksisolucoes.agendamento.dto.AgendaGradeAtendimentoHorariosDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaHorariosAgendaPage extends BasePage {

    private CompoundPropertyModel<Agenda> model;
    private Table tblAgendaGradeAtendimento;
    private DlgAgendaGradeHorario dlgAgendaGradeHorario;
    private DlgAgendaGradeExame dlgAgendaGradeExame;
    private final CadastroAgendaBehavior cadastroAgendaBehavior;
    private List<AgendaGradeAtendimentoHorariosDTO> agendaGradeList = new ArrayList<AgendaGradeAtendimentoHorariosDTO>();
    private boolean tipoAgendaHorario;

    public ConsultaHorariosAgendaPage(Long codigo) {
        carregarAgenda(codigo);
        carregarTipoAgendaHorario();
        cadastroAgendaBehavior = new CadastroAgendaBehavior(model.getObject());
        init();
    }

    private void init() {
        Form form = new Form("form", model);

        Agenda proxy = on(Agenda.class);

        form.add(new DisabledInputField(path(proxy.getTipoProcedimento().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getEmpresa().getDescricao())));
        form.add(new DisabledInputField(path(proxy.getProfissional().getNome())));

        add(form);

        add(new VoltarButton("btnVoltar"));

        agendaGradeList = cadastroAgendaBehavior.carregaListaAtendimentos(agendaGradeList, DataUtil.getDataAtual(), true);
        add(tblAgendaGradeAtendimento = new CadastroAgendaTableColor("tblAgendaGradeAtendimento", getColumns(), getCollectionProvider()));
        tblAgendaGradeAtendimento.populate();
        tblAgendaGradeAtendimento.setOutputMarkupId(true);
    }

    private List<IColumn> getColumns() {
        ColumnFactory columnFactory = new ColumnFactory(AgendaGradeAtendimentoHorariosDTO.class);
        List<IColumn> columns = new ArrayList<IColumn>();
        AgendaGradeAtendimentoHorariosDTO proxy = on(AgendaGradeAtendimentoHorariosDTO.class);
        columns.add(getActionColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("data"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial())));
        columns.add(columnFactory.createColumn(BundleManager.getString("horaFinal"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoHoraFinal())));
        columns.add(columnFactory.createColumn(BundleManager.getString("diaSemana"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData()), DayColumn.class));
        columns.add(columnFactory.createColumn(BundleManager.getString("tipo_atendimento"), path(proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao())));
        columns.add(columnFactory.createColumn(BundleManager.getString("vagas"), path(proxy.getAgendaGradeAtendimento().getQuantidadeAtendimento())));
        columns.add(columnFactory.createColumn(BundleManager.getString("agendadas"), path(proxy.getTotalVagasAgendadas())));
        columns.add(columnFactory.createColumn(BundleManager.getString("tempo_medio"), path(proxy.getAgendaGradeAtendimento().getTempoMedio())));
        columns.add(columnFactory.createColumn(BundleManager.getString("possuiExames"), path(proxy.getDescricaoPossuiExames())));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoHorariosDTO>() {
            @Override
            public void customizeColumn(AgendaGradeAtendimentoHorariosDTO rowObject) {
                addAction(ActionType.AGENDAR, rowObject, new IModelAction<AgendaGradeAtendimentoHorariosDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO modelObject) throws ValidacaoException, DAOException {
                        informarHorarios(target, modelObject);
                    }
                }).setTitleBundleKey("horarios").setIcon(Icon.CLOCK).setEnabled(isTipoAgendaHorario());

                addAction(ActionType.TRANSFERENCIA, rowObject, new IModelAction<AgendaGradeAtendimentoHorariosDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO modelObject) throws ValidacaoException, DAOException {
                        initDlgAgendaGradeExame(target, modelObject);
                }
                }).setTitleBundleKey("examesProcedimentos").setIcon(Icon.DOC_LINES);
            }
        };
    }

    private void informarHorarios(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) {
        if (dlgAgendaGradeHorario == null) {
            addModal(target, dlgAgendaGradeHorario = new DlgAgendaGradeHorario(newModalId(), true) {
                @Override
                public void onSalvar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws ValidacaoException, DAOException {
                    tblAgendaGradeAtendimento.update(target);
                }
            });
        }
        dlgAgendaGradeHorario.show(target, dto, model.getObject(), agendaGradeList);
    }

    private void initDlgAgendaGradeExame(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto){
        if(dlgAgendaGradeExame == null){
            addModal(target, dlgAgendaGradeExame = new DlgAgendaGradeExame(newModalId(), true) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) {
                    tblAgendaGradeAtendimento.update(target);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, AgendaGradeAtendimentoHorariosDTO dto) throws ValidacaoException, DAOException {
                    tblAgendaGradeAtendimento.update(target);
                }
            });
        }
        dlgAgendaGradeExame.show(target, dto);
    }

    private void carregarAgenda(Long codigo) {
        Agenda proxy = on(Agenda.class);

        Agenda agenda = LoadManager.getInstance(Agenda.class)
                .addProperties(new HQLProperties(Agenda.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), codigo))
                .start().getVO();

        model = new CompoundPropertyModel(agenda);
    }

    private boolean isTipoAgendaHorario() {
        return tipoAgendaHorario;
    }

    private void carregarTipoAgendaHorario() {
        try {
            tipoAgendaHorario = TipoProcedimento.TipoAgenda.HORARIO.value().equals(AgendamentoHelper.getTipoAgenda(model.getObject().getTipoProcedimento(), model.getObject().getEmpresa()));
        }
        catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage());
        }
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return agendaGradeList;
            }
        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaHorariosAgenda");
    }
}
