package br.com.celk.view.agenda.cadastro.provider;

import br.com.celk.component.treetable.pageable.PageableTreeProvider;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimento;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import java.util.HashMap;
import java.util.logging.Level;
import java.util.logging.Logger;
import ch.lambdaj.Lambda;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.hamcrest.Matchers;

/**
 *
 * <AUTHOR>
 */
public class AgendaGradeAtendimentoHorarioTreeProvider extends PageableTreeProvider<AgendaProviderDTO, String> {

    private Long codigoAgenda;

    public AgendaGradeAtendimentoHorarioTreeProvider() {
    }
    
    public AgendaGradeAtendimentoHorarioTreeProvider(Long codigoAgenda) {
        this.codigoAgenda = codigoAgenda;
        carregar();
    }

    private void carregar() {
        try {
            List<AgendaGradeHorario> agendaGradeHorarioList = BOFactoryWicket.getBO(AgendamentoFacade.class).carregarHorarios(codigoAgenda);
            Long vagasAgendadas;
            Long vagasDisponiveis;
            
            for (AgendaGradeHorario agendaGradeHorario : agendaGradeHorarioList) {
                String id = agendaGradeHorario.getAgendaGradeAtendimento().getCodigo().toString();
                AgendaProviderDTO dtoPai = new AgendaProviderDTO();
                if (!getMapPaisAdicionados().containsKey(id)) {
                    dtoPai.setDescricao(id);
                    dtoPai.setAgendaGradeAtendimento(agendaGradeHorario.getAgendaGradeAtendimento());
                    
                    if(agendaGradeHorario.getCodigo() != null){
                        
                    vagasAgendadas = new Long(Lambda.select(agendaGradeHorarioList, 
                            Lambda.having(Lambda.on(AgendaGradeHorario.class).getStatus(), Matchers.equalTo(AgendaGradeHorario.Status.AGENDADO.value()))
                                    .and(Lambda.having(Lambda.on(AgendaGradeHorario.class).getAgendaGradeAtendimento().getCodigo(), 
                                            Matchers.equalTo(agendaGradeHorario.getAgendaGradeAtendimento().getCodigo())))).size());
                    
                    vagasDisponiveis = new Long(Lambda.select(agendaGradeHorarioList, 
                            Lambda.having(Lambda.on(AgendaGradeHorario.class).getStatus(), Matchers.equalTo(AgendaGradeHorario.Status.PENDENTE.value()))
                                    .and(Lambda.having(Lambda.on(AgendaGradeHorario.class).getAgendaGradeAtendimento().getCodigo(), 
                                            Matchers.equalTo(agendaGradeHorario.getAgendaGradeAtendimento().getCodigo())))).size());
                    } else {
                        vagasAgendadas = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                                .addGroup(new QueryCustom.QueryCustomGroup(AgendaGradeAtendimentoHorario.PROP_QUANTIDADE_VAGAS_OCUPADAS, BuilderQueryCustom.QueryGroup.SUM))
                                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, QueryCustom.QueryCustomParameter.IN, Arrays.asList(AgendaGradeAtendimentoHorario.STATUS_AGENDADO, AgendaGradeAtendimentoHorario.STATUS_CONCLUIDO, AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU)))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, AgendaGradeAtendimento.PROP_AGENDA_GRADE), agendaGradeHorario.getAgendaGradeAtendimento().getAgendaGrade()))
                                .start().getVO();
                        
                        vagasDisponiveis = new Dinheiro(agendaGradeHorario.getAgendaGradeAtendimento().getQuantidadeAtendimento()).subtrair(Coalesce.asLong(vagasAgendadas).doubleValue()).longValue();
                    }
                    
                    dtoPai.setVagasAgendadas(Coalesce.asLong(vagasAgendadas));
                    dtoPai.setVagasDisponiveis(Coalesce.asLong(vagasDisponiveis));
                    dtoPai.setHeader(false);

                    getMapPaisAdicionados().put(agendaGradeHorario.getAgendaGradeAtendimento().getCodigo().toString(), dtoPai);
                    getRootList().add(dtoPai);
                } else {
                    dtoPai = getMapPaisAdicionados().get(id);
                }

                /**
                 * neste caso, existe root sem filhos
                 */
                if (agendaGradeHorario.getCodigo() != null) {
                    /**
                     * se não tem filhos, coloca um Header
                     */
                    if (dtoPai.getFilhos() == null || dtoPai.getFilhos().isEmpty()) {
                        AgendaProviderDTO dtoHeader = new AgendaProviderDTO();
                        dtoHeader.setHeader(true);
                        dtoHeader.getHeaders().add(BundleManager.getString("hora"));
                        dtoHeader.getHeaders().add(BundleManager.getString("situacao"));
                        dtoPai.addFilho(dtoHeader);
                    }

                    AgendaProviderDTO dtoFilho = new AgendaProviderDTO();
                    dtoFilho.setDescricao(id);
                    dtoFilho.setAgendaGradeHorario(agendaGradeHorario);
                    dtoFilho.setHeader(false);

                    dtoPai.addFilho(dtoFilho);
                    dtoFilho.setPai(dtoPai);
                    getLeafs().add(dtoFilho);
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Logger.getLogger(AgendaGradeAtendimentoHorarioTreeProvider.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    @Override
    public void recarregarProvider() {
        setRootList(new ArrayList<AgendaProviderDTO>());
        setLeafs(new ArrayList<AgendaProviderDTO>());
        setMapPaisAdicionados(new HashMap<String, AgendaProviderDTO>());
//        carregar();
    }

    public void recarregarProvider(Long codigoAgenda) {
        setRootList(new ArrayList<AgendaProviderDTO>());
        setLeafs(new ArrayList<AgendaProviderDTO>());
        setMapPaisAdicionados(new HashMap<String, AgendaProviderDTO>());
        this.codigoAgenda = codigoAgenda;
        carregar();
    }
}
