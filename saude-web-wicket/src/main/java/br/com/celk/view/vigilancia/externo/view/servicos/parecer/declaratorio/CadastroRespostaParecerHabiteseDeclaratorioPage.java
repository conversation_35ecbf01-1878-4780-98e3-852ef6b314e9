package br.com.celk.view.vigilancia.externo.view.servicos.parecer.declaratorio;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.RequiredInputArea;
import br.com.celk.component.tinymce.EditorBehavior;
import br.com.celk.component.tinymce.SimpleEditorSettings;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.celk.view.vigilancia.externo.template.base.BasePageVigilancia;
import br.com.celk.view.vigilancia.externo.view.consulta.ConsultaRequerimentoVigilanciaExternoPage;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlConsultaRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.ImpressaoParecerVistoriaHidrossanitariaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.vo.vigilancia.TipoSolicitacao;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.EmptyPanel;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.util.resource.FileResourceStream;
import org.apache.wicket.util.resource.IResourceStream;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;


/**
 * <AUTHOR>
 */
@Private
public class CadastroRespostaParecerHabiteseDeclaratorioPage extends BasePageVigilancia {
    private RequerimentoVigilancia requerimentoVigilancia;
    private Class returnClass;
    private Form<RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta> form;
    private CompoundPropertyModel<RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta> model = new CompoundPropertyModel<>(new RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta());
    private PageParameters parameters;
    private RequiredInputArea txaResposta;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private boolean enableForm = true;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private AbstractAjaxButton btnSalvar;
    private AbstractAjaxButton btnSalvarEnviar;

    public CadastroRespostaParecerHabiteseDeclaratorioPage(RequerimentoVigilancia requerimentoVigilancia, RequerimentoVistoriaHidrossanitarioDeclaratorioParecer parecer, PageParameters parameters, Class returnClass) {
        super(parameters);
        this.parameters = parameters;
        this.returnClass = returnClass;
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarResposta(parecer);
        init();
    }

    private void init() {
        RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta proxy = on(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.class);
        getForm().add(new MultiLineLabel(path(proxy.getRequerimentoVistoriaHidrossanitarioDeclaratorioParecer().getDescricaoParecer())).setEscapeModelStrings(false));

        getForm().add(txaResposta = new RequiredInputArea(path(proxy.getDescricaoResposta())));
        txaResposta.add(new EditorBehavior(new SimpleEditorSettings(910, 500, true)));
        txaResposta.setEnabled(enableForm);
        txaResposta.setLabel(new Model(bundle("resposta")));

        addCamposAnexos();

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                retornarPaginaConsulta();
            }
        }.setDefaultFormProcessing(false));

        getForm().add(btnSalvar = new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvar(target, false, false);
            }
        });

        getForm().add(btnSalvarEnviar = new AbstractAjaxButton("btnSalvarEnviar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                salvar(target, true, true);
            }
        });

        addPanelAnexosParecer();

        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(getForm());
    }

    private Form getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", model);
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("projetoHidrossanitario") + " - " + BundleManager.getString("parecerTecnico") + " - " + BundleManager.getString("resposta");
    }

    private void salvar(AjaxRequestTarget target, boolean enviar, boolean imprimir) throws ValidacaoException, DAOException {
        if (validarCampoRespostaPreenchido(model.getObject())) {
            throw new ValidacaoException(bundle("msgInformeResposta"));
        }
        if (enviar) {
            model.getObject().setSituacao(RequerimentoVistoriaHidrossanitarioParecerResposta.Situacao.ENVIADO.value());
            model.getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorioParecer().setDataRetorno(DataUtil.getDataAtual());
        } else {
            model.getObject().setSituacao(RequerimentoVistoriaHidrossanitarioParecerResposta.Situacao.CADASTRADO.value());
        }
        RequerimentoVigilancia requerimentoVigilancia = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoVistoriaHidrossanitarioParecerResposta(model.getObject(), pnlRequerimentoVigilanciaAnexo.getPnlRequerimentoVigilanciaAnexoDTO());

        if (imprimir) {
            imprimirParecer(target, requerimentoVigilancia);
            pnlRequerimentoVigilanciaAnexo.setEnabled(false);
            txaResposta.setEnabled(false);
            btnSalvarEnviar.setVisible(false);
            btnSalvar.setVisible(false);
            target.add(btnSalvarEnviar);
            target.add(btnSalvar);
        } else {
            retornarPaginaConsulta();
        }
    }

    private boolean validarCampoRespostaPreenchido(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta resposta) {
        return StringUtils.trimToNull(StringUtilKsi.removeHtmlString(resposta.getDescricaoResposta())) == null;
    }

    private void retornarPaginaConsulta() {
        setResponsePage(returnClass);
    }

    private void imprimirParecer(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia) {
        try {
            ImpressaoParecerVistoriaHidrossanitariaDTOParam param = new ImpressaoParecerVistoriaHidrossanitariaDTOParam();
            param.setRequerimentoVigilancia(requerimentoVigilancia);
            param.setUrlQRcode(VigilanciaHelper.getURLQRCodePageRequerimento());
            param.setChaveQrcode(requerimentoVigilancia.getChaveQRcode());
            DataReport dataReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoParecerVistoriaHabiteseDeclaratorio(param);

            File file = File.createTempFile("parecer", ".pdf");
            JasperExportManager.exportReportToPdfFile(dataReport.getJasperPrint(), file.getAbsolutePath());

            IResourceStream resourceStream = new FileResourceStream(new org.apache.wicket.util.file.File(file));
            ajaxPreviewBlank.initiate(target, "Parecer.pdf", resourceStream);
        } catch (ReportException | IOException | JRException ex) {
            Logger.getLogger(ConsultaRequerimentoVigilanciaExternoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
    }

    private void addCamposAnexos() {
        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(carregarAnexos(model.getObject()));
            requerimentoVigilancia.getTipoSolicitacao().setTipoDocumento(TipoSolicitacao.TipoDocumento.VISTORIA_HABITESE_SANITARIO.value());
            dtoPnlAnexo.setTipoSolicitacao(requerimentoVigilancia.getTipoSolicitacao());
            getForm().add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enableForm));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }
    }

    private List<RequerimentoVigilanciaAnexoDTO> carregarAnexos(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta resposta) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(resposta);

        List<RequerimentoVigilanciaAnexoDTO> listRequerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            listRequerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        return listRequerimentoVigilanciaAnexoDTOList;
    }

    private void carregarResposta(RequerimentoVistoriaHidrossanitarioDeclaratorioParecer parecer) {
        RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta resposta = LoadManager.getInstance(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.class)
                .addProperties(new HQLProperties(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.class).getProperties())
                .addProperties(new HQLProperties(RequerimentoVistoriaHidrossanitarioDeclaratorioParecer.class, RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO_PARECER).getProperties())
                .addProperties(new HQLProperties(RequerimentoVistoriaHidrossanitarioDeclaratorio.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO_PARECER, RequerimentoVistoriaHidrossanitarioDeclaratorioParecer.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO)).getProperties())
                .addProperties(new HQLProperties(RequerimentoVigilancia.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO_PARECER, RequerimentoVistoriaHidrossanitarioDeclaratorioParecer.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO, RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA)).getProperties())
                .addProperties(new HQLProperties(TipoSolicitacao.class, VOUtils.montarPath(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO_PARECER, RequerimentoVistoriaHidrossanitarioDeclaratorioParecer.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO, RequerimentoVistoriaHidrossanitarioDeclaratorio.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_TIPO_SOLICITACAO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.PROP_REQUERIMENTO_VISTORIA_HIDROSSANITARIO_DECLARATORIO_PARECER, parecer))
                .start().getVO();

        if (resposta != null && resposta.getCodigo() != null) {
            if (RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta.Situacao.CADASTRADO.value().equals(resposta.getSituacao())) {
                model.setObject(resposta);
            } else {
                getForm().setEnabled(false);
            }
        } else {
            model.setObject(new RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta());
            model.getObject().setRequerimentoVistoriaHidrossanitarioDeclaratorioParecer(parecer);
        }
    }

    private void addPanelAnexosParecer() {
        Panel panel;
        PnlRequerimentoVigilanciaAnexoDTO dtoAnexos = getDtoAnexos();
        if (CollectionUtils.isNotNullEmpty(dtoAnexos.getRequerimentoVigilanciaAnexoDTOList())) {
            panel = new PnlConsultaRequerimentoVigilanciaAnexo("requerimentoVigilanciaAnexoParecer", dtoAnexos);
        } else {
            panel = new EmptyPanel("requerimentoVigilanciaAnexoParecer");
            panel.setVisible(false);
        }
        getForm().add(panel);
    }

    private PnlRequerimentoVigilanciaAnexoDTO getDtoAnexos() {
        List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        List<RequerimentoVigilanciaAnexo> listObjects = VigilanciaHelper.carregarAnexosVigilancia(model.getObject().getRequerimentoVistoriaHidrossanitarioDeclaratorioParecer());
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : listObjects) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);
            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
        dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
        return dtoPnlAnexo;
    }
}
