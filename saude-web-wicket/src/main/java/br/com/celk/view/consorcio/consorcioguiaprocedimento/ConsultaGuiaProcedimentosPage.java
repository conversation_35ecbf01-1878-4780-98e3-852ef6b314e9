package br.com.celk.view.consorcio.consorcioguiaprocedimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.columnpanel.GuiaProcedimentoColumnPanel;
import br.com.celk.view.consorcio.consorcioguiaprocedimento.customize.CustomizeConsultaConsorcioGuiaProcedimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.controle.web.UsuarioGrupo;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaGuiaProcedimentosPage extends BasePage {

    private static final String INTEGRACAO_AMBULATORIAL_CONSORCIO = "Integraçao Consórcio-Ambulatorial";
    private static final String PARAMETRO_CONSORCIADO_GUIA = "Consorciado da Consulta da Guia de Procedimentos";

    private final String SECRETARIA_IMPRESSAO = "Secretaria Impressão";
    private static final String PARAMETRO_SECRETARIA_IMPRESSAO_GUIA = "Secretaria Impressão de Guia";
    private boolean isSecretariaImpressao = false;
    private boolean isImpressaoGuia = false;

    private PageableTable tblGuias;
    private WebMarkupContainer containerReferencia;
    
    private String nomePaciente;
//    private UsuarioCadsus paciente;
    private Empresa prestador;
    private Empresa consorciado;
    private Long nGuia;
    private String nReferencia;
    private DatePeriod periodo;
    private Long situacao;
    private String cnsPaciente;
    private boolean isConsorcioPadrao;
    boolean informarPacienteSistema;
    private boolean isIntegracao = false;

    private boolean isUsuarioConsorciado = false;
    private boolean isUsuarioConsorciadoGuia = false;
    
    public ConsultaGuiaProcedimentosPage() {
        try {
            informarPacienteSistema = RepositoryComponentDefault.SIM_LONG.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("informarPacienteSistema"));
            isIntegracao = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro(INTEGRACAO_AMBULATORIAL_CONSORCIO));
            Empresa consorcioPadrao = BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("consorcioPadrao");
            Empresa empresaLogada = SessaoAplicacaoImp.getInstance().getEmpresa();
        
            if(consorcioPadrao != null && empresaLogada != null && consorcioPadrao.getCodigo().equals(empresaLogada.getCodigo())){
                isConsorcioPadrao = true;
            } else {
                isConsorcioPadrao = false;
            }
        } catch (DAOException ex) {
            Logger.getLogger(ConsultaGuiaProcedimentosPage.class.getName()).log(Level.SEVERE, null, ex);
        }
        init();
    }

    private void permiteAcoesUsuario() {
        List<UsuarioGrupo> usuarioGrupos = LoadManager.getInstance(UsuarioGrupo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(path(on(UsuarioGrupo.class).getUsuario().getCodigo()), ApplicationSession.get().getSessaoAplicacao().getUsuario().getCodigo()))
                .start().getList();
        for(UsuarioGrupo usuarioGrupo: usuarioGrupos) {
            if(usuarioGrupo.getGrupo().getNome().equals(SECRETARIA_IMPRESSAO)){
                isImpressaoGuia = true;
            } else {
                isImpressaoGuia = false;
            }

        }

    }
    
    private void init(){
        permiteAcoesUsuario();
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(new AutoCompleteConsultaEmpresa("prestador").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        form.add(new AutoCompleteConsultaEmpresa("consorciado").setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));
        form.add(new UpperField("nomePaciente"));
        form.add(new InputField("nGuia"));
        form.add(containerReferencia = new WebMarkupContainer("containerReferencia"));
        containerReferencia.add(new InputField("nReferencia"));
        containerReferencia.setVisible(isIntegracao);
        form.add(new PnlDatePeriod("periodo"));
        form.add(getDropDownSituacao());
        form.add(new InputField("cnsPaciente"));
     
        form.add(tblGuias = new PageableTable("tblGuias", getColumns(), getPagerProvider()));
        tblGuias.setScrollX("1400px");
        
        form.add(new ProcurarButton("btnProcurar", tblGuias) {

            @Override
            public Object getParam() {
                return getParameters();
            }
        });
        
        add(form);
    }
    
    private boolean isConsorcioPadrao(){
        return isConsorcioPadrao;
    }

    private boolean isImpressaoGuia(){
        return isImpressaoGuia;
    }
    
    private DropDown getDropDownSituacao(){
        DropDown cbxSituacao = new DropDown("situacao");
        
        cbxSituacao.addChoice(null, BundleManager.getString("todas"));
        DropDownUtil.setIEnumChoices(cbxSituacao, ConsorcioGuiaProcedimento.StatusGuiaProcedimento.values(), false);
        
        return cbxSituacao;
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        ColumnFactory columnFactory = new ColumnFactory(ConsorcioGuiaProcedimento.class);
        ConsorcioGuiaProcedimento proxy = on(ConsorcioGuiaProcedimento.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nGuia"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CODIGO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("paciente"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_NOME_PACIENTE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cadastro"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("tipoConta"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_TIPO_CONTA, TipoConta.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("prestador"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("consorciado"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO, Empresa.PROP_DESCRICAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DESCRICAO_STATUS)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("cns"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CNS_PACIENTE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("referencia"), VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_REFERENCIA)));

        return columns;
    }
    
    private CustomColumn getCustomColumn(){
        return new CustomColumn<ConsorcioGuiaProcedimento>() {

            @Override
            public Component getComponent(String componentId, ConsorcioGuiaProcedimento rowObject) {
                return new GuiaProcedimentoColumnPanel(componentId, rowObject, isConsorcioPadrao(), isImpressaoGuia()) {

                    @Override
                    public void updateTable(AjaxRequestTarget target) {
                        tblGuias.update(target);
                    }
                };
            }
        };
    }
    
    private IPagerProvider getPagerProvider(){
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaConsorcioGuiaProcedimento()){

            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO), false);
            }
            
        };
    }
    
    private List<BuilderQueryCustom.QueryParameter> getParameters(){
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<BuilderQueryCustom.QueryParameter>();

        if (nomePaciente != null) {
            if (informarPacienteSistema) {
                parameters.add(new QueryCustom.QueryCustomParameter(
                        new BuilderQueryCustom.QueryGroupAnd(
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_NOME_PACIENTE), BuilderQueryCustom.QueryParameter.ILIKE, nomePaciente)))
                        )));

            } else {

                parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_NOME_PACIENTE), BuilderQueryCustom.QueryParameter.ILIKE,nomePaciente));
            }
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CONSORCIO_PRESTADOR, ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), prestador));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_SUB_CONTA, SubConta.PROP_CONTA, Conta.PROP_CONSORCIADO), consorciado));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CODIGO), nGuia));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_DATA_CADASTRO), periodo));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_STATUS), situacao));
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_CNS_PACIENTE), BuilderQueryCustom.QueryParameter.ILIKE,cnsPaciente));

        if(isIntegracao) {
            parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioGuiaProcedimento.PROP_REFERENCIA), nReferencia));
        }
        
        return parameters;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaGuiaProcedimentos");
    }

}
