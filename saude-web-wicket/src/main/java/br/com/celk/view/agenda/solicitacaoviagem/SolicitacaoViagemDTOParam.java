package br.com.celk.view.agenda.solicitacaoviagem;

import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.frota.viagem.TipoTransporteViagem;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class SolicitacaoViagemDTOParam implements Serializable{
    
    private String paciente;
    private String local;
    private Long situacao;
    private Cidade destino;
    private TipoTransporteViagem tipoTransporteViagem;
    private Date dataViagem;

    public SolicitacaoViagemDTOParam() {
    }

    public SolicitacaoViagemDTOParam(Long situacao) {
        this.situacao = situacao;
    }
    
    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public String getLocal() {
        return local;
    }

    public void setLocal(String local) {
        this.local = local;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public Cidade getDestino() {
        return destino;
    }

    public void setDestino(Cidade destino) {
        this.destino = destino;
    }

    public TipoTransporteViagem getTipoTransporteViagem() {
        return tipoTransporteViagem;
    }

    public void setTipoTransporteViagem(TipoTransporteViagem tipoTransporteViagem) {
        this.tipoTransporteViagem = tipoTransporteViagem;
    }

    public Date getDataViagem() {
        return dataViagem;
    }

    public void setDataViagem(Date dataViagem) {
        this.dataViagem = dataViagem;
    }

    @Override
    public String toString() {
        return "SolicitacaoViagemDTOParam{" +
                "paciente='" + paciente + '\'' +
                ", local='" + local + '\'' +
                ", situacao=" + situacao +
                ", destino=" + destino +
                ", tipoTransporteViagem=" + tipoTransporteViagem +
                ", dataViagem=" + dataViagem +
                '}';
    }
}
