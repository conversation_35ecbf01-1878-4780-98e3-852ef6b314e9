package br.com.celk.view.unidadesaude.exames.processos.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.unidadesaude.exames.SubstituirExamesAutorizadosDTO.RequisicaoDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgSubstituirExame extends Window {

    private PnlSubstituirExame pnlSubstituirExame;

    public DlgSubstituirExame(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialWidth(640);
        setInitialHeight(60);
        setResizable(false);

        setTitle(BundleManager.getString("substituicaoExame"));

        setContent(pnlSubstituirExame = new PnlSubstituirExame(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, RequisicaoDTO requisicao) throws DAOException, ValidacaoException {
                DlgSubstituirExame.this.onConfirmar(target, requisicao);
                close(target);
            }

            @Override
            public void onCancelar(AjaxRequestTarget target) {
                close(target);
            }
        });
    }

    public void show(AjaxRequestTarget target, RequisicaoDTO requisicao) {
        show(target);
        pnlSubstituirExame.setExameRequisicao(target, requisicao);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, RequisicaoDTO requisicao) throws DAOException, ValidacaoException;
}
