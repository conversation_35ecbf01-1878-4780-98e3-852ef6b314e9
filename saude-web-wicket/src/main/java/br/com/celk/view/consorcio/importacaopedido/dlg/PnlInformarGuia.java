package br.com.celk.view.consorcio.importacaopedido.dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlInformarGuia extends Panel {

    private AbstractAjaxButton btnFechar;
    private InputField txtGuiaSaida;

    private Long guiaSaida;

    public PnlInformarGuia(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(txtGuiaSaida = new RequiredInputField("guiaSaida"));

        form.add(new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, guiaSaida);
                limpar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
                onFechar(target);
            }
        });

        add(form);

        btnFechar.setDefaultFormProcessing(false);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onConfirmar(AjaxRequestTarget target, Long guiaSaida) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        this.txtGuiaSaida.limpar(target);
        target.appendJavaScript(JScript.initMasks());
        target.focusComponent(txtGuiaSaida);
    }
}
