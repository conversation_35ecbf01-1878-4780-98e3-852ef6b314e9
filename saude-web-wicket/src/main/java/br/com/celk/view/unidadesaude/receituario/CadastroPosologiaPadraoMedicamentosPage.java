package br.com.celk.view.unidadesaude.receituario;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.ProdutoPosologia;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroPosologiaPadraoMedicamentosPage extends BasePage{
    
    private ProdutoPosologia produtoPosologia;
    private CompoundPropertyModel<ProdutoPosologia> model;
    private InputField txtQuantidade;
    private DropDown cbxFrequencia;
    private InputField txtIntervalo;
    private InputField txtDiasTratamento;
    private Label lblSufixoDiasTratamento;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DropDown<Long> dropDownFrequencia;
    
    public CadastroPosologiaPadraoMedicamentosPage() {
        init();
    }
    
    public CadastroPosologiaPadraoMedicamentosPage(ProdutoPosologia produtoPosologia) {
        this.produtoPosologia = produtoPosologia;
        init();
    }
    
    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel<ProdutoPosologia>(produtoPosologia == null ? produtoPosologia = new ProdutoPosologia() : produtoPosologia));
        ProdutoPosologia proxy = on(ProdutoPosologia.class);        
        
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(ProdutoPosologia.PROP_PRODUTO,true).setMedicamento(RepositoryComponentDefault.SIM));
        autoCompleteConsultaProduto.setIncluirInativos(false);
        form.add(txtQuantidade = new RequiredInputField(path(proxy.getQtdade())));
        form.add(txtIntervalo = new InputField(path(proxy.getIntervalo())));
        form.add(txtDiasTratamento = new RequiredInputField(path(proxy.getPeriodoTratamento())));
        form.add(lblSufixoDiasTratamento = new Label("lblSufixoDiasTratamento", new Model()));
        form.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getTratamentoContinuo())));
        lblSufixoDiasTratamento.setOutputMarkupId(true);
        form.add(dropDownFrequencia = getCbxFrequencia(path(proxy.getFrequencia())));
        
        this.dropDownFrequencia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                resolveFrequencia(target);
            }
        });
        
        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(produtoPosologia, target);
            }
        }));

        form.add(new SubmitButton("btnSalvarContinuar", new ISubmitAction() {

            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvarContinuar(produtoPosologia,target);
            }
        }));
        
        add(form);
        resolveIntervalo();
        lblSufixoDiasTratamento.setDefaultModelObject(Bundle.getStringApplication("rotulo_dia_s"));
    }

    private DropDown getCbxFrequencia(String id){
        if (this.cbxFrequencia == null) {
            this.cbxFrequencia = new DropDown(id);
            
            cbxFrequencia.addChoice(ProdutoPosologia.Frequencia.FREQUENCIA_DIA.value(), bundle("dia"));
            cbxFrequencia.addChoice(ProdutoPosologia.Frequencia.FREQUENCIA_HORA.value(), bundle("hora"));
            cbxFrequencia.addChoice(ProdutoPosologia.Frequencia.FREQUENCIA_SEMANA.value(), bundle("semana"));
            cbxFrequencia.addChoice(ProdutoPosologia.Frequencia.FREQUENCIA_MES.value(), bundle("mes"));
        }
        
        return this.cbxFrequencia;
    }
    
    public void resolveIntervalo() {
        if (!ProdutoPosologia.Frequencia.FREQUENCIA_HORA.value().equals(cbxFrequencia.getComponentValue())) {
            txtIntervalo.setEnabled(false);
        }
    }
    
    public void resolveFrequencia(AjaxRequestTarget target) {
        txtIntervalo.setEnabled(ProdutoPosologia.Frequencia.FREQUENCIA_HORA.value().equals(cbxFrequencia.getComponentValue()));
        if (!ProdutoPosologia.Frequencia.FREQUENCIA_HORA.value().equals(cbxFrequencia.getComponentValue())) {
            txtIntervalo.limpar(target);
        } else {
            target.add(txtIntervalo);
        }
        
        if (ProdutoPosologia.Frequencia.FREQUENCIA_HORA.value().equals(cbxFrequencia.getComponentValue())) {
            lblSufixoDiasTratamento.setDefaultModelObject(Bundle.getStringApplication("rotulo_dia_s"));
        } else if (ProdutoPosologia.Frequencia.FREQUENCIA_DIA.value().equals(cbxFrequencia.getComponentValue())) {
            lblSufixoDiasTratamento.setDefaultModelObject(Bundle.getStringApplication("rotulo_dia_s"));
        } else if (ProdutoPosologia.Frequencia.FREQUENCIA_SEMANA.value().equals(cbxFrequencia.getComponentValue())) {
            lblSufixoDiasTratamento.setDefaultModelObject(Bundle.getStringApplication("rotulo_semana_s"));
        } else if (ProdutoPosologia.Frequencia.FREQUENCIA_MES.value().equals(cbxFrequencia.getComponentValue())) {
            lblSufixoDiasTratamento.setDefaultModelObject(Bundle.getStringApplication("rotulo_mes_es"));
        }
        target.add(lblSufixoDiasTratamento);
    }
    
    public void salvar(ProdutoPosologia object, AjaxRequestTarget target) throws DAOException, ValidacaoException {
        validarMedicamento(target);
        
        BOFactoryWicket.save(object);
        Page page = new ConsultaPosologiaPadraoMedicamentosPage();
        setResponsePage(page);
        getSession().getFeedbackMessages().info(page, BundleManager.getString("medicamentoPosologiaSalvoComSucesso"));
    }
    
    public void salvarContinuar(ProdutoPosologia object, AjaxRequestTarget target) throws DAOException, ValidacaoException {
        validarMedicamento(target);
        
        BOFactoryWicket.save(object);
        object.setVersion(null);
        object.setCodigo(null);
        object.setQtdade(null);
        object.setFrequencia(null);
        object.setIntervalo(null);
        object.setPeriodoTratamento(null);
        txtQuantidade.limpar(target);
        cbxFrequencia.limpar(target);
        txtIntervalo.limpar(target);
        txtDiasTratamento.limpar(target);
        autoCompleteConsultaProduto.limpar(target);
        getSession().getFeedbackMessages().info(this, BundleManager.getString("medicamentoPosologiaSalvoComSucesso"));
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroMedicamentoPosologia");
    }
    
    private void validarMedicamento(AjaxRequestTarget target) throws ValidacaoException, DAOException{
        List<ProdutoPosologia> _produtoPosologia = LoadManager.getInstance(ProdutoPosologia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoPosologia.PROP_PRODUTO), this.model.getObject().getProduto()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProdutoPosologia.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, this.model.getObject().getCodigo()))
                .start().getList();
        
        if (CollectionUtils.isNotNullEmpty(_produtoPosologia)) {
            throw new ValidacaoException(BundleManager.getString("jaExisteMedicamentoPosologia", this.model.getObject().getProduto().getDescricao()));
        }
        
        Produto produto =  LoadManager.getInstance(Produto.class)
            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Produto.PROP_CODIGO), produtoPosologia.getProduto().getCodigo()))
            .start().getVO();

        if (produto != null && DispensacaoMedicamentoItem.DISPENSACAO_SEM_CONTROLE.equals(produto.getFlagDispensacaoEspecial())) {
            throw new ValidacaoException(BundleManager.getString("msgConfigMedicamentoNaoPermitidoPosologiaPadrao"));
        }
    }
}
