package br.com.celk.view.publico.agenda.listaespera;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTabbedPanel;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ListaPublicaTabbedPanel extends CadastroTabbedPanel {

    public ListaPublicaTabbedPanel(String id, List<ITab> tabs) {
        super(id, null, false, tabs, false, false);
    }

    @Override
    public Class getReferenceClass() {
        return null;
    }

    @Override
    public Class getResponsePage() {
        return null;
    }

}
