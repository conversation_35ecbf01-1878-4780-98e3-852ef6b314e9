package br.com.celk.view.unidadesaude.esus.relatorios;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.esus.interfaces.facade.EsusReportFacade;
import br.com.celk.component.dateperiod.RequiredPnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.unidadesaude.esus.relatorios.dto.RelacaoProducaoDTOParam;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class RelatorioRelacaoProducaoPage extends RelatorioPage<RelacaoProducaoDTOParam> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private DropDown<String> dropDownFormaApresentacao;
    private static int PERIODO_MAX = 1;
    private RequiredPnlDatePeriod periodo;

    @Override
    public void init(Form<RelacaoProducaoDTOParam> form) {
        RelacaoProducaoDTOParam proxy = on(RelacaoProducaoDTOParam.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento())));
        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));

        form.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        form.add(getDropDownFormaApresentacao());
        form.add(new AutoCompleteConsultaTabelaCbo(path(proxy.getTabelaCbo())));
        form.add(periodo = new RequiredPnlDatePeriod(path(proxy.getPeriodo())));
    }

    @Override
    public Class<RelacaoProducaoDTOParam> getDTOParamClass() {
        return RelacaoProducaoDTOParam.class;
    }

    @Override
    public void antesGerarRelatorio(AjaxRequestTarget target) throws ValidacaoException, ReportException {
        if (DataUtil.getAnosDiferenca(
                periodo.getModelObject().getDataInicial(),
                periodo.getModelObject().getDataFinal()) >= PERIODO_MAX && BundleManager.getString("detalhado").equals(dropDownFormaApresentacao.getModelObject())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_periodo_nao_deve_exceder_doze_meses"));
        }
    }

    @Override
    public DataReport getDataReport(RelacaoProducaoDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EsusReportFacade.class).relacaoProducao(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relacaoProducao");
    }

    private DropDown<String> getDropDownFormaApresentacao(){
        dropDownFormaApresentacao = new DropDown<String>("formaApresentacao");
        dropDownFormaApresentacao.addChoice(BundleManager.getString("consolidado"), BundleManager.getString("consolidado"));
        dropDownFormaApresentacao.addChoice(BundleManager.getString("detalhado"), BundleManager.getString("detalhado"));
        return dropDownFormaApresentacao;
    }
}
