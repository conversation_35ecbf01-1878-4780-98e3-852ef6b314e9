package br.com.celk.view.materiais.tipodocumento.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.materiais.tipodocumento.customize.CustomizeConsultaTipoDocumento;
import br.com.ksisolucoes.vo.entradas.estoque.TipoDocumento;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaTipoDocumento extends PnlConsulta<TipoDocumento> {

    private boolean apenasInterno = false;
    private boolean apenasNFEntrada = false;
    
    public PnlConsultaTipoDocumento(String id, IModel<TipoDocumento> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaTipoDocumento(String id, IModel<TipoDocumento> model) {
        super(id, model);
    }

    public PnlConsultaTipoDocumento(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaTipoDocumento(String id) {
        super(id);
    }
    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator<CustomizeConsultaTipoDocumento>() {

            @Override
            public CustomizeConsultaTipoDocumento getCustomizeConsultaInstance() {
                return new CustomizeConsultaTipoDocumento();
            }

            @Override
            public void customQuery(CustomizeConsultaTipoDocumento customizeConsultaQuery) {
                customizeConsultaQuery.setApenasInterno(apenasInterno);
                customizeConsultaQuery.setApenasNotaFiscalEntrada(apenasNFEntrada);
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tiposDocumento");
    }

    public PnlConsultaTipoDocumento setApenasInterno(boolean apenasInterno) {
        this.apenasInterno = apenasInterno;
        return this;
    }
    
    public PnlConsultaTipoDocumento setApenasNotaFiscalEntrada(boolean apenasNFEntrada) {
        this.apenasNFEntrada = apenasNFEntrada;
        return this;
    }

}
