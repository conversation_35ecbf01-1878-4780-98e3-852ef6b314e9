package br.com.celk.view.basico.unidade.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxSimNao;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.validator.CnsFieldValidator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.autocomplete.AutoCompleteConsultaProcedimentoServicoCadastro;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.ksisolucoes.bo.basico.interfaces.dto.EmpresaConjuntoDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomSorter;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaServicoClassificacao;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoCadastro;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacaoPk;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ServicosEspecializadosEmpresaTab extends TabPanel<EmpresaConjuntoDTO> {

    private AutoCompleteConsultaProcedimentoServicoCadastro autoCompleteConsultaProcedimentoServicoCadastro;
    private DropDown dropDownClassificacao;
    private DropDown dropDownTipo;
    private CheckBoxSimNao chkAmbulatorialSus;
    private CheckBoxSimNao chkAmbulatorialNaoSus;
    private CheckBoxSimNao chkHospitalarSus;
    private CheckBoxSimNao chkHospitalarNaoSus;
    private AutoCompleteConsultaEmpresa autoCompleteEmpresaTerceiro;
    private InputField fieldNomeResponsavel;
    private InputField fieldCnsResponsavel;
    private AutoCompleteConsultaTabelaCbo autoCompleteCboResponsavel;

    private Empresa empresaTerceiro;
    private String nomeResponsavel;
    private String cnsResponsavel;
    private TabelaCbo cboResponsavel;
    private ProcedimentoServicoCadastro procedimentoServicoCadastro;
    private ProcedimentoServicoClassificacao procedimentoServicoClassificacao;
    private String codigoCaracterizacao;
    private String ambulatorialSus;
    private String ambulatorialNaoSus;
    private String hospitalarSus;
    private String hospitalarNaoSus;
    private int indexEdicao = -1;

    private Table<EmpresaServicoClassificacao> tblServicos;

    public ServicosEspecializadosEmpresaTab(String id, EmpresaConjuntoDTO object) {
        super(id, object);
        init();
    }

    private void init() {
        List<EmpresaServicoClassificacao> lst = LoadManager.getInstance(EmpresaServicoClassificacao.class)
                .addProperties(new HQLProperties(EmpresaServicoClassificacao.class).getProperties())
                .addProperties(new HQLProperties(ProcedimentoServicoClassificacao.class, VOUtils.montarPath(EmpresaServicoClassificacao.PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO)).getProperties())
                .addProperties(new HQLProperties(ProcedimentoServicoCadastro.class, VOUtils.montarPath(EmpresaServicoClassificacao.PROP_PROCEDIMENTO_SERVICO_CLASSIFICACAO, ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO)).getProperties())
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(EmpresaServicoClassificacao.PROP_EMPRESA, Empresa.PROP_CODIGO), object.getEmpresa()))
                .start().getList();
        object.setServicosClassificacoes(lst);

        EmpresaConjuntoDTO proxy = on(EmpresaConjuntoDTO.class);
        add(new InputField<String>(path(proxy.getEmpresa().getDescricao()))
                .setEnabled(false));
        add(autoCompleteConsultaProcedimentoServicoCadastro = new AutoCompleteConsultaProcedimentoServicoCadastro("procedimentoServicoCadastro", new PropertyModel<ProcedimentoServicoCadastro>(this, "procedimentoServicoCadastro")));
        autoCompleteConsultaProcedimentoServicoCadastro.add(new ConsultaListener<ProcedimentoServicoCadastro>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, ProcedimentoServicoCadastro object) {
                carregarClassificacoes(target, object);
            }
        });
        autoCompleteConsultaProcedimentoServicoCadastro.add(new RemoveListener<ProcedimentoServicoCadastro>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, ProcedimentoServicoCadastro object) {
                dropDownClassificacao.removeAllChoices();
                dropDownClassificacao.setEnabled(false);
                target.add(dropDownClassificacao);
            }
        });
        add(dropDownClassificacao = getDropDownClassificacao("procedimentoServicoClassificacao"));
        dropDownClassificacao.addAjaxUpdateValue();
        add(dropDownTipo = getDropDownTipo("codigoCaracterizacao"));
//        add(dropDownTerceiro = getDropDownTerceiro("servicoTerceiroBrasil"));
        add(autoCompleteEmpresaTerceiro = new AutoCompleteConsultaEmpresa("empresaTerceiro", new PropertyModel(this, "empresaTerceiro")));
        autoCompleteEmpresaTerceiro.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO));
        autoCompleteEmpresaTerceiro.setValidarTipoEstabelecimento(true);
        
        add(fieldNomeResponsavel = new InputField("nomeResponsavel", new PropertyModel(this, "nomeResponsavel")));
        add(fieldCnsResponsavel = new InputField("cnsResponsavel", new PropertyModel(this, "cnsResponsavel")));
        fieldCnsResponsavel.add(CnsFieldValidator.getInstance());
        add(autoCompleteCboResponsavel = new AutoCompleteConsultaTabelaCbo("cboResponsavel", new PropertyModel(this, "cboResponsavel")));
        autoCompleteCboResponsavel.setFiltrarAtivos(true);

        add(chkAmbulatorialSus = new CheckBoxSimNao("ambulatorialSus", new PropertyModel<String>(this, "ambulatorialSus")));
        add(chkAmbulatorialNaoSus = new CheckBoxSimNao("ambulatorialNaoSus", new PropertyModel<String>(this, "ambulatorialNaoSus")));
        add(chkHospitalarSus = new CheckBoxSimNao("hospitalarSus", new PropertyModel<String>(this, "hospitalarSus")));
        add(chkHospitalarNaoSus = new CheckBoxSimNao("hospitalarNaoSus", new PropertyModel<String>(this, "hospitalarNaoSus")));

        add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });
        
        AbstractAjaxButton btnLimpar = new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limpar(target);
            }
        };
        btnLimpar.setDefaultFormProcessing(false);
        add(btnLimpar);

        add(tblServicos = new Table("tblServicos", getColumns(), getCollectionProvider()));
        tblServicos.populate();
        
        setOutputMarkupId(true);
        
        autoCompleteEmpresaTerceiro.setEnabled(false);
        fieldNomeResponsavel.setEnabled(false);
        fieldCnsResponsavel.setEnabled(false);
        autoCompleteCboResponsavel.setEnabled(false);
    }

    private void carregarClassificacoes(AjaxRequestTarget target, ProcedimentoServicoCadastro object) {
        dropDownClassificacao.setEnabled(true);
        dropDownClassificacao.removeAllChoices();
        dropDownClassificacao.addChoice(null, "");
        List<ProcedimentoServicoClassificacao> list = LoadManager.getInstance(ProcedimentoServicoClassificacao.class)
                .addProperties(new HQLProperties(ProcedimentoServicoClassificacao.class).getProperties())
                .addProperty(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO, ProcedimentoServicoCadastro.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO, ProcedimentoServicoCadastro.PROP_DESCRICAO))
                .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO), procedimentoServicoCadastro))
                .addSorter(new QueryCustomSorter(ProcedimentoServicoClassificacao.PROP_DESCRICAO))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(list)) {
            for (ProcedimentoServicoClassificacao psc : list) {
                dropDownClassificacao.addChoice(psc, psc.getDescricao());
            }
        }
        target.add(dropDownClassificacao);
    }
    
    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (procedimentoServicoCadastro == null) {
            throw new ValidacaoException(BundleManager.getString("informeServico"));
        }
        if (procedimentoServicoClassificacao == null) {
            throw new ValidacaoException(BundleManager.getString("favorInformeClassificacao"));
        }
        if (!codigoCaracterizacao.equals(EmpresaServicoClassificacao.CARACTERIZACAO_PROPRIO)) {
            if (empresaTerceiro == null) {
                throw new ValidacaoException(BundleManager.getString("informeTerceiro"));
            }
            if(Coalesce.asString(nomeResponsavel).isEmpty()){
                throw new ValidacaoException(BundleManager.getString("informeNomeResponsavel"));
            }
        }

        EmpresaServicoClassificacao empresaServicoClassificacao;
        if(indexEdicao >= 0){
            empresaServicoClassificacao = object.getServicosClassificacoes().get(indexEdicao);
        }else{
            empresaServicoClassificacao = new EmpresaServicoClassificacao();
        }

        empresaServicoClassificacao.setProcedimentoServicoClassificacao(procedimentoServicoClassificacao);
        empresaServicoClassificacao.setCodigoCaracterizacao(codigoCaracterizacao);
        empresaServicoClassificacao.setAmbulatorialSus(ambulatorialSus);
        empresaServicoClassificacao.setAmbulatorial(ambulatorialNaoSus);
        empresaServicoClassificacao.setHospitalarSus(hospitalarSus);
        empresaServicoClassificacao.setHospitalar(hospitalarNaoSus);
        empresaServicoClassificacao.setEmpresa(object.getEmpresa());
        empresaServicoClassificacao.setEmpresaTerceiro(empresaTerceiro);
        empresaServicoClassificacao.setCnsResponsavel(Coalesce.asString(cnsResponsavel).replaceAll("[^0-9]", ""));
        empresaServicoClassificacao.setNomeResponsavel(nomeResponsavel);
        empresaServicoClassificacao.setCboResponsavel(cboResponsavel);

        if(indexEdicao < 0){
            object.getServicosClassificacoes().add(empresaServicoClassificacao);
        }
        limpar(target);
        tblServicos.update(target);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<IColumn>();
        EmpresaServicoClassificacao proxy = on(EmpresaServicoClassificacao.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("servico"), proxy.getProcedimentoServicoClassificacao().getId().getProcedimentoServicoCadastro().getDescricao()));
        columns.add(createColumn(bundle("classificacao"), proxy.getProcedimentoServicoClassificacao().getDescricao()));
        columns.add(createColumn(bundle("tipo"), proxy.getDescricaoCaracterizacao()));
        columns.add(createColumn(bundle("terceiro"), proxy.getEmpresaTerceiro().getDescricao()));
        columns.add(createColumn(bundle("responsavel"), proxy.getNomeResponsavel()));
        columns.add(createColumn(bundle("cbo"), proxy.getCboResponsavel().getDescricaoFormatado()));
//        columns.add(createColumn(bundle("ambulatorialSus"), proxy.getAmbulatorialSus()));
//        columns.add(createColumn(bundle("ambulatorialNaoSus"), proxy.getAmbulatorial()));
//        columns.add(createColumn(bundle("hospitalarSus"), proxy.getHospitalarSus()));
//        columns.add(createColumn(bundle("hospitalarNaoSus"), proxy.getHospitalar()));
        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<EmpresaServicoClassificacao>() {
            @Override
            public void customizeColumn(EmpresaServicoClassificacao rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EmpresaServicoClassificacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, EmpresaServicoClassificacao modelObject) throws ValidacaoException, DAOException {
                        editar(target, modelObject);
                    }
                });
                addAction(ActionType.REMOVER, rowObject, new IModelAction<EmpresaServicoClassificacao>() {
                    @Override
                    public void action(AjaxRequestTarget target, EmpresaServicoClassificacao modelObject) throws ValidacaoException, DAOException {
                        remover(target, modelObject);
                        limpar(target);
                    }
                });
            }
        };
    }

    private void editar(AjaxRequestTarget target, EmpresaServicoClassificacao modelObject) {
        for (int i = 0; i < object.getServicosClassificacoes().size(); i++) {
            EmpresaServicoClassificacao esc = object.getServicosClassificacoes().get(i);
            if (esc.getProcedimentoServicoClassificacao() == modelObject.getProcedimentoServicoClassificacao()) {
                indexEdicao = i;
            }
        }
        
        if(indexEdicao >= 0){
            autoCompleteConsultaProcedimentoServicoCadastro.setComponentValue(target, modelObject.getProcedimentoServicoClassificacao().getId().getProcedimentoServicoCadastro());
            carregarClassificacoes(target, procedimentoServicoCadastro);
            dropDownClassificacao.setComponentValue(modelObject.getProcedimentoServicoClassificacao());
            codigoCaracterizacao = modelObject.getCodigoCaracterizacao();
            
            autoCompleteEmpresaTerceiro.limpar(target);
            empresaTerceiro = modelObject.getEmpresaTerceiro();
            
            autoCompleteCboResponsavel.limpar(target);
            cboResponsavel = modelObject.getCboResponsavel();
            
            nomeResponsavel = modelObject.getNomeResponsavel();
            cnsResponsavel = modelObject.getCnsResponsavel();
            hospitalarNaoSus = modelObject.getHospitalar();
            hospitalarSus = modelObject.getHospitalarSus();
            ambulatorialSus = modelObject.getAmbulatorialSus();
            ambulatorialNaoSus = modelObject.getAmbulatorial();
            
            target.add(this);
            ajustarCamposTerceiro(target);
        }
    }
    
    private void remover(AjaxRequestTarget target, EmpresaServicoClassificacao modelObject) {
        for (int i = 0; i < object.getServicosClassificacoes().size(); i++) {
            EmpresaServicoClassificacao esc = object.getServicosClassificacoes().get(i);
            if (esc == modelObject) {
                object.getServicosClassificacoes().remove(i);
                break;
            }
        }
        tblServicos.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return ServicosEspecializadosEmpresaTab.this.object.getServicosClassificacoes();
            }
        };
    }

    private DropDown getDropDownClassificacao(String id) {
        DropDown dropDown = new DropDown(id, new PropertyModel(this, id));
        dropDown.addChoice(null, "");

        if (procedimentoServicoCadastro != null) {
            List<ProcedimentoServicoClassificacao> list = LoadManager.getInstance(ProcedimentoServicoClassificacao.class)
                    .addProperties(new HQLProperties(ProcedimentoServicoClassificacao.class).getProperties())
                    .addProperty(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO, ProcedimentoServicoCadastro.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO, ProcedimentoServicoCadastro.PROP_DESCRICAO))
                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ProcedimentoServicoClassificacao.PROP_ID, ProcedimentoServicoClassificacaoPk.PROP_PROCEDIMENTO_SERVICO_CADASTRO), procedimentoServicoCadastro))
                    .addSorter(new QueryCustomSorter(ProcedimentoServicoClassificacao.PROP_DESCRICAO))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                for (ProcedimentoServicoClassificacao psc : list) {
                    dropDown.addChoice(psc, psc.getDescricao());
                }
            }
        } else {
            dropDown.setEnabled(false);
        }

        return dropDown;
    }

    private DropDown getDropDownTipo(String id) {
        DropDown dropDown = new DropDown(id, new PropertyModel(this, id));

        dropDown.addChoice(EmpresaServicoClassificacao.CARACTERIZACAO_PROPRIO, Bundle.getStringApplication("rotulo_proprio"));
        dropDown.addChoice(EmpresaServicoClassificacao.CARACTERIZACAO_TERCEIRIZADO, Bundle.getStringApplication("rotulo_terceirizado"));
        dropDown.addChoice(EmpresaServicoClassificacao.CARACTERIZACAO_AMBOS, Bundle.getStringApplication("rotulo_ambos"));

        dropDown.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                ajustarCamposTerceiro(target);
            }
        });

        return dropDown;
    }

    private void ajustarCamposTerceiro(AjaxRequestTarget target) {
        if(EmpresaServicoClassificacao.CARACTERIZACAO_PROPRIO.equals(codigoCaracterizacao)){
            autoCompleteEmpresaTerceiro.limpar(target);
            autoCompleteCboResponsavel.limpar(target);
            nomeResponsavel = null;
            cnsResponsavel = null;
            
            autoCompleteEmpresaTerceiro.setEnabled(false);
            fieldNomeResponsavel.setEnabled(false);
            fieldCnsResponsavel.setEnabled(false);
            autoCompleteCboResponsavel.setEnabled(false);
        }else{
            autoCompleteEmpresaTerceiro.setEnabled(true);
            fieldNomeResponsavel.setEnabled(true);
            fieldCnsResponsavel.setEnabled(true);
            autoCompleteCboResponsavel.setEnabled(true);
        }
        target.add(autoCompleteEmpresaTerceiro);
        target.add(autoCompleteCboResponsavel);
        target.add(fieldCnsResponsavel);
        target.add(fieldNomeResponsavel);
        target.appendJavaScript(JScript.initMasks());
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("servicosEspecializados");
    }

    private void limpar(AjaxRequestTarget target) {
        indexEdicao = -1;
        autoCompleteConsultaProcedimentoServicoCadastro.limpar(target);
        dropDownClassificacao.limpar(target);
        dropDownClassificacao.setEnabled(false);
        dropDownClassificacao.removeAllChoices(target);
        autoCompleteEmpresaTerceiro.limpar(target);
        fieldNomeResponsavel.limpar(target);
        fieldCnsResponsavel.limpar(target);
        autoCompleteCboResponsavel.limpar(target);
        autoCompleteEmpresaTerceiro.setEnabled(false);
        fieldNomeResponsavel.setEnabled(false);
        fieldCnsResponsavel.setEnabled(false);
        autoCompleteCboResponsavel.setEnabled(false);
        dropDownTipo.limpar(target);
        chkAmbulatorialSus.limpar(target);
        chkAmbulatorialNaoSus.limpar(target);
        chkHospitalarSus.limpar(target);
        chkHospitalarNaoSus.limpar(target);
    }

}
