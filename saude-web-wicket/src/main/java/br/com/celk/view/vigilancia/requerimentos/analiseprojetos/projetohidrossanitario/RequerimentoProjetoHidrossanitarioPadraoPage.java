package br.com.celk.view.vigilancia.requerimentos.analiseprojetos.projetohidrossanitario;

import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.vigilancia.requerimentos.analiseprojetos.base.RequerimentoHidrossanitarioBasePage;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoProjetoHidrossanitarioDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitario;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.view.vigilancia.requerimentos.analiseprojetos.base.RequerimentosHelper.isLongSim;


public class RequerimentoProjetoHidrossanitarioPadraoPage extends RequerimentoHidrossanitarioBasePage {

    private RequerimentoProjetoHidrossanitario requerimentoHidrossanitario;
    private RequerimentoProjetoHidrossanitarioDTO requerimentoDTO;


    public RequerimentoProjetoHidrossanitarioPadraoPage(
            TipoSolicitacao tipoSolicitacao,
            Class clazz
    ) {
        super(
                true,
                tipoSolicitacao,
                clazz
        );
    }

    public RequerimentoProjetoHidrossanitarioPadraoPage(
            RequerimentoVigilancia requerimentoVigilancia,
            boolean modoEdicao,
            Class pageReturn
    ) {
        super(
                requerimentoVigilancia,
                modoEdicao,
                pageReturn
        );
    }

    @Override
    public void setTituloAba(String titulo) {
        super.setTituloAba(bundle("analiseProjetoHidrossanitarioPadrao"));
    }

    @Override
    public String getTituloPrograma() {
        return bundle("analiseProjetoHidrossanitarioPadrao");
    }

    @Override
    public void criarEntidadeRequerimento() {
        requerimentoHidrossanitario = RequerimentoProjetoHidrossanitario.carregarRequerimentoProjetoHidrossanitario(getRequerimentoVigilancia());
        requerimentoHidrossanitario.setRequerimentoVigilancia(getRequerimentoVigilancia());
    }

    @Override
    public void carregarLists() {
        if (isEdicao() || isConsulta()) {
            carregarAnexos();
            carregarPranchasHidrossanitario();
            carregarListTipoProjeto(getRequerimentoVigilancia(), false);
            carregarListResponsavelTecnico(getRequerimentoVigilancia(), false);
            carregarListInscricoesImobiliarias(getRequerimentoVigilancia(), false);
        }
    }

    @Override
    public void criarForm() {
        setForm(new Form("form", new CompoundPropertyModel(requerimentoHidrossanitario)));
    }

    @Override
    public void criarComponentesRequerimento() {
        criarNumeroProtocolo(hasEntity());
        criarDadosProprietario(hasEntity());
        criarTipoProjeto(hasEntity(), TipoProjetoVigilancia.Tipo.PHS);
        criarDadosProjeto();
        criarInscricaoImobiliaria();
        criarResponsavelTecnico();
        criarUsoEdificacao();
        criarInformacoes(hasEntity());
        criarTipoEnquadramentoProjeto();
        criarParcelamentoDoSolo();
        criarMetragem();
        criarPanelDadosComuns(bundle("fiscais"));
        criarPanelDadosSolicitante();
        criarPanelAnexos();
        criarPanelFiscais();
        criarPanelAnexoPranchas(GerenciadorArquivo.OrigemArquivo.PRANCHA_PROJ_HIDROSSANITARIO, bundle("anexarPrancha"));
        criarPanelOcorrencias();
        criarCheckboxOrientacaoTecnica(
                bundle("documentacaoOrientacaoTecnica10")
        );
    }

    @Override
    public void carregarRegrasComponentes() {
        adicionarSetorAnaliseProjetos();
        enableCamposDadosProprietario(null, false);
        habilitarUsoEdificacao(null);
        habilitarMetragem(null);
        habilitarNumeroPHSAprovado();
        habilitarParcelamentoSolo(null);
        vigilanciaAltoBaixoRisco(null);
        configurarEstabelecimento();
        tipoProjetoRequired(null);
        getPnlResponsavelTecnico().responsavelTecnicoRequired(null);
        getPnlInscricaoImobiliaria().inscricaoImobiliariaRequired(null);
    }

    @Override
    public void validarRequerimento(Object requerimentoDTO, AjaxRequestTarget target) throws ValidacaoException {
        RequerimentoProjetoHidrossanitarioDTO dto = (RequerimentoProjetoHidrossanitarioDTO) requerimentoDTO;

        if (isBaixoRisco() && !isLongSim(getDdReclassificadoBaixoRisco())) {
            throw new ValidacaoException(bundle("msgClassificacaoProjetoBaixoRiscoHabitese"));
        }

        if (CollectionUtils.isEmpty(dto.getListResponsavelTecnico())) {
            throw new ValidacaoException(bundle("msgInformeAoMenosUmResponsavelTecnico"));
        }

        if (CollectionUtils.isEmpty(dto.getListInscricaoImobiliaria())) {
            throw new ValidacaoException(bundle("msgInformeAoMenosUmaInscricaoImobiliaria"));
        }

        if (CollectionUtils.isEmpty(dto.getListAnexosPrancha()) || dto.getListAnexosPrancha().size() < 2) {
            throw new ValidacaoException(bundle("msgAnexarMinimoDuasPranchas"));
        }

        if (CollectionUtils.isEmpty(dto.getListTipoProjetos())) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_pelo_menos_um_tipo_projeto"));
        }

        if (Coalesce.asDouble(dto.getRequerimentoProjetoHidrossanitario().getAreaTotalConstrucao()) <= 0D) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_area_total_construcao"));
        }

        validarAreaTotalConstrucao(target);
    }

    @Override
    public RequerimentoVigilancia salvarRequerimento(Object requerimentoDTO, AjaxRequestTarget target) throws ValidacaoException, DAOException {
        RequerimentoProjetoHidrossanitarioDTO dto = (RequerimentoProjetoHidrossanitarioDTO) requerimentoDTO;

        Estabelecimento estabelecimento = salvarEstabelecimento(
                dto.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getTipoRequerente(),
                dto.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getEstabelecimento()
        );
        if (estabelecimento != null) {
            dto.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);
        }

        VigilanciaPessoa vigilanciaPessoa = salvarPessoaVigilancia(
                dto.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getTipoRequerente(),
                dto.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().getVigilanciaPessoa()
        );
        if (vigilanciaPessoa != null) {
            dto.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setVigilanciaPessoa(vigilanciaPessoa);
        }

        RequerimentoVigilancia rvSaved = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoProjetoHidrossanitario(dto);

        return rvSaved;
    }

    @Override
    public Object getRequerimentoDTO() {
        if (requerimentoDTO == null) {
            requerimentoDTO = new RequerimentoProjetoHidrossanitarioDTO();
            requerimentoDTO.setRequerimentoProjetoHidrossanitario(requerimentoHidrossanitario);
            requerimentoDTO.getRequerimentoProjetoHidrossanitario().setRequerimentoVigilancia(getBaseDTO().getRequerimentoVigilancia());
            requerimentoDTO.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setTipoSolicitacao(getTipoSolicitacao());

            requerimentoDTO.setListTipoProjetos(getBaseDTO().getListTipoProjetos());
            requerimentoDTO.setListTiposProjetosExcluidos(getBaseDTO().getListTiposProjetosExcluidos());
            requerimentoDTO.setListInscricaoImobiliaria(getBaseDTO().getListInscricaoImobiliaria());
            requerimentoDTO.setListInscricaoImobiliariaExcluidos(getBaseDTO().getListInscricaoImobiliariaExcluidos());
            requerimentoDTO.setListResponsavelTecnico(getBaseDTO().getListResponsavelTecnico());
            requerimentoDTO.setListResponsavelTecnicoExcluidos(getBaseDTO().getListResponsavelTecnicoExcluidos());
            requerimentoDTO.setListAnexos(getBaseDTO().getListAnexos());
            requerimentoDTO.setListAnexosExcluidos(getBaseDTO().getListAnexosExcluidos());
            requerimentoDTO.setListAnexosPrancha(getBaseDTO().getListAnexosPrancha());
            requerimentoDTO.setListAnexosPranchaExcluidos(getBaseDTO().getListAnexosPranchaExcluidos());

            if (getPnlDadosComum() != null && getPnlDadosComum().getParam() != null) {
                requerimentoDTO.getRequerimentoProjetoHidrossanitario().getRequerimentoVigilancia().setObservacaoRequerimento(
                        (String) getPnlDadosComum().getObservacaoRequerimento().getComponentValue()
                );

                requerimentoDTO.setListSetorVigilancia(getPnlDadosComum().getParam().getEloRequerimentoVigilanciaSetorVigilanciaList());
                requerimentoDTO.setListSetorVigilanciaExcluidos(getPnlDadosComum().getParam().getEloRequerimentoVigilanciaSetorVigilanciaExcluirList());
                requerimentoDTO.setListFiscais(getPnlDadosComum().getParam().getRequerimentoVigilanciaFiscalList());
                requerimentoDTO.setListFiscaisExcluidos(getPnlDadosComum().getParam().getRequerimentoVigilanciaFiscalListExcluir());
            }
        }

        return requerimentoDTO;
    }


    private boolean hasEntity() {
        return (requerimentoHidrossanitario != null &&
                requerimentoHidrossanitario.getCodigo() != null &&
                requerimentoHidrossanitario.getCodigo() > 0L);
    }

    /**
     * GETTERS AND SETTERS
     */

    public RequerimentoProjetoHidrossanitario getRequerimentoHidrossanitario() {
        return requerimentoHidrossanitario;
    }

    public void setRequerimentoHidrossanitario(RequerimentoProjetoHidrossanitario requerimentoHidrossanitario) {
        this.requerimentoHidrossanitario = requerimentoHidrossanitario;
    }
}
