package br.com.celk.view.atendimento.programas.auxilioBrasil;

import br.com.celk.component.action.AjaxActionConstantDefault;
import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.manutencaoAuxilioBrasil.dto.ConsultaManutencaoAuxilioBrasilDTO;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.DataUtil;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.avaliacao.AuxilioBrasil;
import br.com.ksisolucoes.vo.prontuario.avaliacao.MotivosDescumprimentoAuxilioBrasil;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;

import java.util.Date;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR> Prado
 */
public class CadastroManutencaoAuxilioBrasilPage extends CadastroPage<ConsultaManutencaoAuxilioBrasilDTO> {


    private WebMarkupContainer containerDadosPaciente;

    private DropDown<Long> dropDownSituacaoAcompanhamento;
    private DropDown<Long> dropDownMotivoNaoAcompanhamento;
    private FormComponent<Date> dataAtendimento;

    private WebMarkupContainer containerGestante;
    private DropDown<Long> dropDownGestante;
    private DateChooserAjax dataUltimoPreNatal;
    private DateChooserAjax dataUltimaMestruacao;
    private DropDown<Long> dropDownMotivoDescumprimentoPreNatal;

    private WebMarkupContainer containerVacina;
    private DropDown<Long> dropDownVacina;
    private DropDown<Long> dropDownMotivoNaoVacinacao;

    private WebMarkupContainer containerVigilanciaAlimentar;
    private DoubleField pesoField;
    private DoubleField alturaField;
    private DropDown<Long> dropDownMotivoDadoNutricionalNaoColetado;

    public CadastroManutencaoAuxilioBrasilPage(ConsultaManutencaoAuxilioBrasilDTO auxilioBrasilDTO, boolean viewOnly, boolean editar) {
        this(auxilioBrasilDTO, viewOnly);

        if (editar) {
            containerDadosPaciente.setEnabled(false);
        }
    }

    public CadastroManutencaoAuxilioBrasilPage(ConsultaManutencaoAuxilioBrasilDTO auxilioBrasilDTO, boolean viewOnly) {
        super(auxilioBrasilDTO, viewOnly);
    }

    @Override
    public void init(Form form) {

        ConsultaManutencaoAuxilioBrasilDTO proxy = on(ConsultaManutencaoAuxilioBrasilDTO.class);

        //Dados do Paciente
        form.add(containerDadosPaciente = new WebMarkupContainer("containerDadosPaciente"));
        containerDadosPaciente.setOutputMarkupId(true);
        containerDadosPaciente.add(new UpperField(path(proxy.getUsuarioCadsus().getNome())));
        containerDadosPaciente.add(new UpperField(path(proxy.getUsuarioCadsus().getNomeMae())));
        containerDadosPaciente.add(new UpperField(path(proxy.getUsuarioCadsus().getIdade())));
        containerDadosPaciente.add(new UpperField(path(proxy.getEndereco())));
        containerDadosPaciente.add(new UpperField(path(proxy.getArea())));
        containerDadosPaciente.add(new UpperField(path(proxy.getMicroArea())));
        containerDadosPaciente.add(new UpperField(path(proxy.getUsuarioCadsus().getCpf())));
        containerDadosPaciente.add(new UpperField(path(proxy.getCns())));
        containerDadosPaciente.add(new UpperField(path(proxy.getUsuarioCadsus().getNis())));

        //Situação do acompanhamento
        form.add(dropDownSituacaoAcompanhamento = getDropDownSituacaoAcompanhamento(path(proxy.getSituacaoAcompanhamento())));
        form.add(dropDownMotivoNaoAcompanhamento = getDropDownMotivoNaoAcompanhamento(path(proxy.getMotivoNaoAcompanhamento())));
        form.add(dataAtendimento = new DateChooserAjax(path(proxy.getDataAtendimento())).setRequired(true));

        dropDownSituacaoAcompanhamento.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarHabilitacaoCampoMotivoNaoAcompanhamento();
                validarObrigatoriedadeCampoPreNatal();
                validarHabilitacaoCampoVacina();
                validarHabilitacaoCampoPeso();
                validarHabilitacaoCampoAltura();
                validarHabilitacaoCampoMotivoDadoNutricionalNaoColetado();

                target.add(dropDownMotivoNaoAcompanhamento);
                target.add(containerGestante);
                target.add(dataAtendimento);
                target.add(containerVacina);
                target.add(pesoField);
                target.add(alturaField);
                target.add(dropDownMotivoDadoNutricionalNaoColetado);
                target.add(containerVigilanciaAlimentar);
                target.add(dropDownSituacaoAcompanhamento);
            }
        });

        validarHabilitacaoCampoMotivoNaoAcompanhamento();

        //GESTANTE
        form.add(containerGestante = new WebMarkupContainer("containerGestante"));
        containerGestante.setOutputMarkupId(true);
        containerGestante.add(dropDownGestante = getDropDownGestante(path(proxy.getGestante())));
        containerGestante.add(dataUltimoPreNatal = new DateChooserAjax(path(proxy.getDataPreNatal())));
        containerGestante.add(dataUltimaMestruacao = new DateChooserAjax(path(proxy.getDum())));
        containerGestante.add(dropDownMotivoDescumprimentoPreNatal = getDropDownMotivoDescumprimentoPreNatal(path(proxy.getMotivoDescumprimentoPreNatal())));

        dropDownGestante.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarObrigatoriedadeCampoPreNatal();
                validarObrigatoriedadeCampoDUM();

                target.add(dropDownGestante);
                target.add(containerGestante);
            }
        });

        validarObrigatoriedadeCampoPreNatal();
        validarObrigatoriedadeCampoDUM();
        visibilidadeContainerGestante();

        //VACINACAO
        form.add(containerVacina = new WebMarkupContainer("containerVacina"));
        containerVacina.setOutputMarkupId(true);
        containerVacina.add(dropDownVacina = getDropDownVacina(path(proxy.getVacinaEmDia())));
        containerVacina.add(dropDownMotivoNaoVacinacao = getDropDownMotivoNaoVacinacao(path(proxy.getMotivoNaoVacinacao())));

        dropDownVacina.add(new AjaxFormComponentUpdatingBehavior(AjaxActionConstantDefault.ONCHANGE) {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                validarHabilitacaoCampoMotivoNaoVacinacao();
                target.add(dropDownMotivoNaoVacinacao);
                target.add(dropDownVacina);
                target.add(containerVacina);
            }
        });

        validarObrigatoriedadeCampoVacina();
        validarHabilitacaoCampoVacina();
        validarHabilitacaoCampoMotivoNaoVacinacao();

        //VIGILANCIA ALIMENTAR
        form.add(containerVigilanciaAlimentar = new WebMarkupContainer("containerVigilanciaAlimentar"));
        containerVigilanciaAlimentar.setOutputMarkupId(true);
        containerVigilanciaAlimentar.add(pesoField = new DoubleField(path(proxy.getPeso())).setMDec(3));
        containerVigilanciaAlimentar.add(alturaField = new DoubleField(path(proxy.getAltura())).setMDec(1));
        containerVigilanciaAlimentar.add(dropDownMotivoDadoNutricionalNaoColetado = getDropDownMotivoDadoNutricionalNaoColetado(path(proxy.getMotivoDeDadoNutricionalNaoColetado())));

        validarObrigatoriedadeCampoPeso();
        validarHabilitacaoCampoPeso();
        validarObrigatoriedadeCampoAltura();
        validarHabilitacaoCampoAltura();
        validarHabilitacaoCampoMotivoDadoNutricionalNaoColetado();
    }

    private DropDown<Long> getDropDownSituacaoAcompanhamento(String id) {
        if (dropDownSituacaoAcompanhamento == null) {
            dropDownSituacaoAcompanhamento = new DropDown<>(id);
            dropDownSituacaoAcompanhamento.addChoice(AuxilioBrasil.Situacao.NAO_ACOMPANHADO.value(), AuxilioBrasil.Situacao.NAO_ACOMPANHADO.descricao());
            dropDownSituacaoAcompanhamento.addChoice(AuxilioBrasil.Situacao.ACOMPANHADO.value(), AuxilioBrasil.Situacao.ACOMPANHADO.descricao());
        }
        return dropDownSituacaoAcompanhamento;
    }

    private DropDown<Long> getDropDownMotivoNaoAcompanhamento(String id) {
        if (dropDownMotivoNaoAcompanhamento == null) {
            dropDownMotivoNaoAcompanhamento = new DropDown<>(id);
            dropDownMotivoNaoAcompanhamento.addChoice(null, "");
            dropDownMotivoNaoAcompanhamento.addChoice(AuxilioBrasil.SituacaoNaoAcompanhamento.AUSENTE.value(), AuxilioBrasil.SituacaoNaoAcompanhamento.AUSENTE.descricao());
            dropDownMotivoNaoAcompanhamento.addChoice(AuxilioBrasil.SituacaoNaoAcompanhamento.MUDOUSE_ENDERECO.value(), AuxilioBrasil.SituacaoNaoAcompanhamento.MUDOUSE_ENDERECO.descricao());
            dropDownMotivoNaoAcompanhamento.addChoice(AuxilioBrasil.SituacaoNaoAcompanhamento.MUDOUSE_MUNICIPIO.value(), AuxilioBrasil.SituacaoNaoAcompanhamento.MUDOUSE_MUNICIPIO.descricao());
            dropDownMotivoNaoAcompanhamento.addChoice(AuxilioBrasil.SituacaoNaoAcompanhamento.FALECIMENTO.value(), AuxilioBrasil.SituacaoNaoAcompanhamento.FALECIMENTO.descricao());
            dropDownMotivoNaoAcompanhamento.addChoice(AuxilioBrasil.SituacaoNaoAcompanhamento.ENDERECO_INCORRENTO.value(), AuxilioBrasil.SituacaoNaoAcompanhamento.ENDERECO_INCORRENTO.descricao());
        }
        return dropDownMotivoNaoAcompanhamento;
    }

    private void validarHabilitacaoCampoMotivoNaoAcompanhamento() {
        dropDownMotivoNaoAcompanhamento.setEnabled(!isAcompanhando());
        dropDownMotivoNaoAcompanhamento.setRequired(!isAcompanhando());

        if(isAcompanhando())
            dropDownMotivoNaoAcompanhamento.setComponentValue(null);
    }

    private DropDown<Long> getDropDownGestante(String id) {
        if (dropDownGestante == null)
            dropDownGestante = DropDownUtil.getNaoSimLongDropDown(id, true);
        return dropDownGestante;
    }

    private DropDown<Long> getDropDownMotivoDescumprimentoPreNatal(String id) {
        if (dropDownMotivoDescumprimentoPreNatal == null){
            dropDownMotivoDescumprimentoPreNatal = new DropDown<>(id);
            popularDropDownMotivoDescumprimentoPreNatal(dropDownMotivoDescumprimentoPreNatal);
        }
        return dropDownMotivoDescumprimentoPreNatal;
    }

    private void popularDropDownMotivoDescumprimentoPreNatal(DropDown<Long> dropDownMotivoDescumprimentoPreNatal) {
        dropDownMotivoDescumprimentoPreNatal.addChoice(null, "");
        for (MotivosDescumprimentoAuxilioBrasil motivo : listarMotivosDescumprimentosPreNatal())
            dropDownMotivoDescumprimentoPreNatal.addChoice(motivo.getCodigo(), motivo.getDescricao());
    }

    private List<MotivosDescumprimentoAuxilioBrasil> listarMotivosDescumprimentosPreNatal(){
        return LoadManager.getInstance(MotivosDescumprimentoAuxilioBrasil.class)
                .addParameter(new QueryCustom.QueryCustomParameter(MotivosDescumprimentoAuxilioBrasil.PROP_MOTIVO_DE_NAO_REALIZACAO_DO_PRE_NATAL, RepositoryComponentDefault.SIM_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(MotivosDescumprimentoAuxilioBrasil.PROP_DESCRICAO))
                .start().getList();
    }

    private void visibilidadeContainerGestante(){
        containerGestante.setVisible(!isUsuarioMasculino());
    }

    private boolean isUsuarioMasculino(){
        return RepositoryComponentDefault.SEXO_MASCULINO.equals(this.getForm().getModelObject().getUsuarioCadsus().getSexo());
    }

    private void validarObrigatoriedadeCampoPreNatal(){
        dataUltimoPreNatal.setRequiredField(verificaCondicaoObrigatoriedadePreNatal());
    }

    private boolean verificaCondicaoObrigatoriedadePreNatal(){
        return isGestante() && isAcompanhando();
    }

    private void validarObrigatoriedadeCampoDUM(){
        dataUltimaMestruacao.setRequiredField(isGestante());
    }

    private boolean isGestante(){
        return RepositoryComponentDefault.SIM_LONG.equals(dropDownGestante.getComponentValue());
    }

    private DropDown<Long> getDropDownVacina(String id) {
        if (dropDownVacina == null)
            dropDownVacina = DropDownUtil.getNaoSimLongDropDown(id, true);
        return dropDownVacina;
    }

    private DropDown<Long> getDropDownMotivoNaoVacinacao(String id) {
        if (dropDownMotivoNaoVacinacao == null){
            dropDownMotivoNaoVacinacao = new DropDown<>(id);
            popularDropDownMotivoNaoVacinacao(dropDownMotivoNaoVacinacao);
        }
        return dropDownMotivoNaoVacinacao;
    }

    private void popularDropDownMotivoNaoVacinacao(DropDown<Long> dropDownMotivoNaoVacinacao) {
        dropDownMotivoNaoVacinacao.addChoice(null, "");
        for (MotivosDescumprimentoAuxilioBrasil motivo : listarMotivosNaoVacinacao())
            dropDownMotivoNaoVacinacao.addChoice(motivo.getCodigo(), motivo.getDescricao());
    }

    private List<MotivosDescumprimentoAuxilioBrasil> listarMotivosNaoVacinacao(){
        return LoadManager.getInstance(MotivosDescumprimentoAuxilioBrasil.class)
                .addParameter(new QueryCustom.QueryCustomParameter(MotivosDescumprimentoAuxilioBrasil.PROP_MOTIVO_DE_NAO_VACINACAO, RepositoryComponentDefault.SIM_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(MotivosDescumprimentoAuxilioBrasil.PROP_DESCRICAO))
                .start().getList();
    }

    private void validarHabilitacaoCampoVacina() {
        containerVacina.setEnabled(isAcompanhando());
    }

    private void validarObrigatoriedadeCampoVacina() {
        dropDownVacina.setRequired(isCrianca());
    }

    private void validarHabilitacaoCampoMotivoNaoVacinacao() {
        boolean condicao = isVacinaEmDia();
        dropDownMotivoNaoVacinacao.setEnabled(!condicao);
        dropDownMotivoNaoVacinacao.setRequired(!condicao);

        if(condicao)
            dropDownMotivoNaoVacinacao.setComponentValue(null);

    }

    private boolean isVacinaEmDia(){
        return !dropDownVacina.getValue().equals("0");
    }

    private DropDown<Long> getDropDownMotivoDadoNutricionalNaoColetado(String id) {
        if (dropDownMotivoDadoNutricionalNaoColetado == null){
            dropDownMotivoDadoNutricionalNaoColetado = new DropDown<>(id);
            popularDropDownMotivoDadoNutricionalNaoColetado(dropDownMotivoDadoNutricionalNaoColetado);
        }
        return dropDownMotivoDadoNutricionalNaoColetado;
    }

    private void popularDropDownMotivoDadoNutricionalNaoColetado(DropDown<Long> dropDownMotivoDadoNutricionalNaoColetado) {
        dropDownMotivoDadoNutricionalNaoColetado.addChoice(null, "");
        for (MotivosDescumprimentoAuxilioBrasil motivo : listarMotivosDadoNutricionalNaoColetado())
            dropDownMotivoDadoNutricionalNaoColetado.addChoice(motivo.getCodigo(), motivo.getDescricao());
    }

    private List<MotivosDescumprimentoAuxilioBrasil> listarMotivosDadoNutricionalNaoColetado(){
        return LoadManager.getInstance(MotivosDescumprimentoAuxilioBrasil.class)
                .addParameter(new QueryCustom.QueryCustomParameter(MotivosDescumprimentoAuxilioBrasil.PROP_MOTIVO_DE_DADO_NUTRICIONAL_NAO_COLETADO, RepositoryComponentDefault.SIM_LONG))
                .addSorter(new QueryCustom.QueryCustomSorter(MotivosDescumprimentoAuxilioBrasil.PROP_DESCRICAO))
                .start().getList();
    }

    private void validarObrigatoriedadeCampoPeso() {
        pesoField.setRequired(isCrianca());
    }

    private void validarHabilitacaoCampoPeso() {
        pesoField.setEnabled(isAcompanhando());
    }

    private void validarObrigatoriedadeCampoAltura() {
        alturaField.setRequired(isCrianca());
    }

    private void validarHabilitacaoCampoAltura() {
        alturaField.setEnabled(isAcompanhando());
    }

    private void validarHabilitacaoCampoMotivoDadoNutricionalNaoColetado() {
        boolean condicao = verificaCondicaoHabilitarCampoMotivoDadoNutricionalNaoColetado();
        dropDownMotivoDadoNutricionalNaoColetado.setEnabled(condicao);
        dropDownMotivoDadoNutricionalNaoColetado.setRequired(condicao);
    }

    private boolean verificaCondicaoHabilitarCampoMotivoDadoNutricionalNaoColetado(){
        return isAcompanhando() && isCrianca() && (pesoVazio() && alturaVazio());
    }

    private boolean isAcompanhando(){
        return AuxilioBrasil.Situacao.ACOMPANHADO.value().equals(dropDownSituacaoAcompanhamento.getComponentValue());
    }

    //VOU MODIFICAR ESSA CONDIÇÂO CRIANDO UM ENUM OU UM ESTADO DENTRO DA CLASSE QWARE
    private boolean isCrianca() {
        return this.getForm().getModelObject().getTipoIntegrante().equals(3L);
    }

    private boolean pesoVazio(){
        return pesoField.getValue().isEmpty();
    }

    private boolean alturaVazio(){
        return alturaField.getValue().isEmpty();
    }

    @Override
    public ConsultaManutencaoAuxilioBrasilDTO salvar(ConsultaManutencaoAuxilioBrasilDTO object) throws DAOException, ValidacaoException {

        ValidarDados validarDados = new ValidarDados(object);
        validarDados.validar();

        AuxilioBrasil auxilioBrasil = new DTOtoAuxilioBrasil(object).montarAuxilioBrasil();
        BOFactory.save(auxilioBrasil);
        return object;
    }

    @Override
    public Class<ConsultaManutencaoAuxilioBrasilDTO> getReferenceClass() {
        return ConsultaManutencaoAuxilioBrasilDTO.class;
    }

    @Override
    public Class<ConsultaManutencaoAuxilioBrasilPage> getResponsePage() {
        return ConsultaManutencaoAuxilioBrasilPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastro_manutencao_auxilio_brasil");
    }
}

class ValidarDados {

    private final ConsultaManutencaoAuxilioBrasilDTO dto;
    private static final Long JULHO = 7L;
    private static final Long QUERENTA_DUAS_SEMANAS = 294L;
    private static final Double PESO_MINIMO = 0.9D;
    private static final Double PESO_MAXIMO = 235.8D;
    private static final Double ALTURA_MINIMA = 22D;
    private static final Double ALTURA_MAXIMA = 235.8D;

    ValidarDados(ConsultaManutencaoAuxilioBrasilDTO dto) {
        this.dto = dto;
    }

    public void validar() throws ValidacaoException {
        validarDataAtendimento(dto.getDataAtendimento());
        if(dto.getDum() != null)
            validarDUM(dto.getDum());
        validarPeso(dto.getPeso());
        validarAltura(dto.getAltura());
    }

    private void validarDataAtendimento(Date dataAtendimento) throws ValidacaoException {
        dataAtendimentoSuperiorDataAtual(dataAtendimento);
        dataDentroSemestreAno(dataAtendimento);
    }

    private void dataAtendimentoSuperiorDataAtual(Date data) throws ValidacaoException {
        if(DataUtil.getDataAtual().before(data))
            throw new ValidacaoException(BundleManager.getString("msgDataAtendimentoMaiorAtual"));
    }

    private void dataDentroSemestreAno(Date dataAtendimento) throws ValidacaoException {
        if(!anoCorrespondente(dataAtendimento) || ( !dentroPrimeiroSemestre(dataAtendimento) && !dentroSegundoSemestre(dataAtendimento)))
            throw new ValidacaoException(BundleManager.getString("msgDataDeveEstarDentroPeriodoSemestre", dto.getSemestre(), dto.getAno()));
    }

    private boolean dentroPrimeiroSemestre(Date dataAtendimento){
        return isPrimeiroSemestre() && isMesMenorJulho(dataAtendimento);
    }

    private boolean dentroSegundoSemestre(Date dataAtendimento){
        return !isPrimeiroSemestre() && !isMesMenorJulho(dataAtendimento);
    }

    private boolean isPrimeiroSemestre(){
        return RepositoryComponentDefault.Semestre.PRIMEIRO_SEMESTRE.value().equals(dto.getSemestre());
    }

    private boolean isMesMenorJulho(Date dataAtendimento){
        return DataUtil.getMes(dataAtendimento) < JULHO;
    }

    private boolean anoCorrespondente(Date dataAtendimento){
        return Util.isNotNull(dataAtendimento) && dto.getAno().equals(String.valueOf(DataUtil.getAno(dataAtendimento)));
    }

    private void validarDUM(Date dum) throws ValidacaoException {
        dataDUMSuperiorDataAtual(dum);
        validarDiferencaEntreDataAtendimentoEDUM(dum, dto.getDataAtendimento());
    }

    private void dataDUMSuperiorDataAtual(Date dum) throws ValidacaoException {
        if(DataUtil.getDataAtual().before(dum))
            throw new ValidacaoException(BundleManager.getString("msgDumNaoMaiorDataAtual"));
    }

    private void validarDiferencaEntreDataAtendimentoEDUM(Date dum, Date dataAtendimento) throws ValidacaoException {
        if(DataUtil.getDiasDiferenca(dum, dataAtendimento) > QUERENTA_DUAS_SEMANAS)
            throw new ValidacaoException(BundleManager.getString("msgDumNaoPodeConter42SemanasDeDiferenca"));
    }

    private void validarPeso(Double peso) throws ValidacaoException {
        if (Util.isNotNull(peso) && peso < PESO_MINIMO)
            throw new ValidacaoException(bundle("msgPesoInformadoDeveSerMaiorIgualX", String.valueOf(PESO_MINIMO)));
        if (Util.isNotNull(peso) && peso > PESO_MAXIMO)
            throw new ValidacaoException(bundle("msgPesoInformadoDeveSerMenorIgualX", String.valueOf(PESO_MAXIMO)));
    }

    private void validarAltura(Double altura) throws ValidacaoException {
        if (Util.isNotNull(altura) && altura < ALTURA_MINIMA)
            throw new ValidacaoException(bundle("msgAlturaInformadaDeveSerMaiorIgualX", String.valueOf(ALTURA_MINIMA)));
        if (Util.isNotNull(altura) && altura > ALTURA_MAXIMA)
            throw new ValidacaoException(bundle("msgAlturaInformadoDeveSerMenorIgualX", String.valueOf(ALTURA_MAXIMA)));
    }
}

class DTOtoAuxilioBrasil {

    private final ConsultaManutencaoAuxilioBrasilDTO dto;

    DTOtoAuxilioBrasil(ConsultaManutencaoAuxilioBrasilDTO dto) {
        this.dto = dto;
    }

    public AuxilioBrasil montarAuxilioBrasil(){
        AuxilioBrasil auxilioBrasil = getAuxilioBrasilByCodigo(dto.getCodigoAuxilioBrasil());

        auxilioBrasil.setUsuarioCadsus(dto.getUsuarioCadsus());
        auxilioBrasil.setDataAtendimento(dto.getDataAtendimento());
        auxilioBrasil.setTipoIntegrante(dto.getTipoIntegrante());
        auxilioBrasil.setVacinaEmDia(dto.getVacinaEmDia());
        auxilioBrasil.setGestante(dto.getGestante());
        auxilioBrasil.setDataPreNatal(dto.getDataPreNatal());
        auxilioBrasil.setDum(dto.getDum());
        auxilioBrasil.setSituacao(dto.getSituacaoAcompanhamento());
        auxilioBrasil.setMotivoDeDadoNutricionalNaoColetado(dto.getMotivoDeDadoNutricionalNaoColetado());
        auxilioBrasil.setMotivoDeNaoRealizacaoDoPreNatal(dto.getMotivoDescumprimentoPreNatal());
        auxilioBrasil.setMotivoDeNaoVacinacao(dto.getMotivoNaoVacinacao());
        auxilioBrasil.setMotivoNaoAcompanhamento(dto.getMotivoNaoAcompanhamento());
        auxilioBrasil.setPeso(dto.getPeso());
        auxilioBrasil.setAltura(dto.getAltura());

        return auxilioBrasil;
    }

    public AuxilioBrasil getAuxilioBrasilByCodigo(Long codigo){
        return LoadManager.getInstance(AuxilioBrasil.class)
                .addParameter(new QueryCustom.QueryCustomParameter(AuxilioBrasil.PROP_CODIGO, codigo))
                .start().getVO();
    }


}

