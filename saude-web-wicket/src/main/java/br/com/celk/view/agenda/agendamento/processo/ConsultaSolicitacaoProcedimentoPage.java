//package br.com.celk.view.agenda.agendamento.processo;
//
//import br.com.celk.component.action.IModelAction;
//import br.com.celk.component.action.IReportAction;
//import br.com.celk.component.action.link.ActionType;
//import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
//import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
//import br.com.celk.component.dateperiod.PnlDatePeriod;
//import br.com.celk.component.dialog.DlgMotivoObject;
//import br.com.celk.component.table.column.MultipleActionCustomColumn;
//import br.com.celk.system.authorization.Permissions;
//import br.com.celk.system.factory.BOFactoryWicket;
//import br.com.celk.template.consulta.ConsultaPage;
//import br.com.celk.view.agenda.agendamento.CadastroAutorizacaoAgendamentoPage;
//import br.com.celk.view.atendimento.prontuario.panel.exame.controller.ExameViewUtil;
//import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
//import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
//import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
//import br.com.celk.view.unidadesaude.exames.manutencaocotacbo.ConsultaCotaCboPage;
//import br.com.ksisolucoes.agendamento.exame.dto.CadastroSolicitacaoProcedimentoDTO;
//import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
//import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
//import br.com.ksisolucoes.bo.command.QueryCustom;
//import br.com.ksisolucoes.bo.controle.interfaces.facade.UsuarioFacade;
//import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
//import br.com.ksisolucoes.dao.HQLProperties;
//import br.com.ksisolucoes.dao.exception.DAOException;
//import br.com.ksisolucoes.operadorvalor.OperadorValor;
//import br.com.ksisolucoes.report.DataReport;
//import br.com.ksisolucoes.report.exception.ReportException;
//import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
//import br.com.ksisolucoes.system.controle.SGKException;
//import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
//import br.com.ksisolucoes.util.DatePeriod;
//import br.com.ksisolucoes.util.VOUtils;
//import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
//import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
//import br.com.ksisolucoes.vo.basico.Empresa;
//import br.com.ksisolucoes.vo.cadsus.Profissional;
//import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
//import br.com.ksisolucoes.vo.controle.Usuario;
//import br.com.ksisolucoes.vo.prontuario.basico.*;
//import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
//import org.apache.wicket.ajax.AjaxRequestTarget;
//import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
//import org.apache.wicket.markup.html.form.Form;
//import org.apache.wicket.model.CompoundPropertyModel;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.logging.Level;
//import java.util.logging.Logger;
//
//import static br.com.celk.system.methods.WicketMethods.bundle;
//import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
//import static ch.lambdaj.Lambda.on;
//
///**
// *
// * <AUTHOR>
// */
//public class ConsultaSolicitacaoProcedimentoPage extends ConsultaPage<ExameRequisicao, List<BuilderQueryCustom.QueryParameter>> {
//
//    private OperadorValor<List<Empresa>> empresaSolicitante;
//    private UsuarioCadsus paciente;
//    private ExameProcedimento procedimento;
//    private DatePeriod periodo;
//
//    private DlgMotivoObject<ExameRequisicao> dlgMotivo;
//
//    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
//
//    @Override
//    public void initForm(Form form) {
//        form.setDefaultModel(new CompoundPropertyModel(this));
//
//        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("empresaSolicitante"));
//        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA));
//        autoCompleteConsultaEmpresa.setOperadorValor(true);
//        autoCompleteConsultaEmpresa.setMultiplaSelecao(true);
//
//        form.add(new AutoCompleteConsultaUsuarioCadsus("paciente"));
//        form.add(new AutoCompleteConsultaExameProcedimento("procedimento").setIncluirInativos(true));
//        form.add(new PnlDatePeriod("periodo"));
//    }
//
//    @Override
//    public List<IColumn> getColumns(List<IColumn> columns) {
//        ExameRequisicao proxy = on(ExameRequisicao.class);
//
//        columns.add(getCustomActionColumn());
//        columns.add(createSortableColumn(bundle("dataSolicitacao"), proxy.getSolicitacaoAgendamento().getDataSolicitacao()));
//        columns.add(createSortableColumn(bundle("paciente"), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNome(), proxy.getSolicitacaoAgendamento().getUsuarioCadsus().getNomeSocial()));
//        columns.add(createSortableColumn(bundle("estabelecimentoSolicitante"), proxy.getSolicitacaoAgendamento().getEmpresa().getDescricao()));
//        columns.add(createSortableColumn(bundle("profissionalSolicitante"), proxy.getSolicitacaoAgendamento().getNomeProfissionalOrigem()));
//        columns.add(createSortableColumn(bundle("prestador"), proxy.getSolicitacaoAgendamento().getExamePrestadorCompetencia().getEmpresa().getDescricao()));
//        columns.add(createSortableColumn(bundle("procedimento"), proxy.getExameProcedimento().getDescricaoProcedimento()));
//        return columns;
//    }
//
//    @Override
//    public IPagerProvider getPagerProviderInstance() {
//        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
//            @Override
//            public Class getClassConsulta() {
//                return ExameRequisicao.class;
//            }
//
//            @Override
//            public String[] getProperties() {
//                return VOUtils.mergeProperties(
//                        new HQLProperties(ExameRequisicao.class).getProperties(), new HQLProperties(ExameRequisicao.class).getProperties(),
//                        new String[]{
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_SOLICITACAO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_CODIGO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_DESCRICAO_PROCEDIMENTO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_CODIGO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_DESCRICAO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, ExameProcedimento.PROP_PROCEDIMENTO, Procedimento.PROP_REFERENCIA),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_EXAME_PRESTADOR_COMPETENCIA, ExamePrestadorCompetencia.PROP_EMPRESA, Empresa.PROP_CODIGO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_EXAME_PRESTADOR_COMPETENCIA, ExamePrestadorCompetencia.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME, TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_FLAG_REQUER_AUTORIZACAO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME, TipoExame.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_REGULADO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_CODIGO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_STATUS),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_EMPRESA, Empresa.PROP_CODIGO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_NOME_PROFISSIONAL_ORIGEM),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_PROFISSIONAL, Profissional.PROP_NOME),
//                            VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO)
//                        });
//            }
//
//            @Override
//            public void consultaCustomizeSorters(List<BuilderQueryCustom.QuerySorter> sorters) {
//                sorters.add(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_SOLICITACAO), "desc"));
//            }
//        });
//    }
//
//    @Override
//    public List<BuilderQueryCustom.QueryParameter> getParameters() {
//        List<BuilderQueryCustom.QueryParameter> parametros = new ArrayList<>();
//        parametros.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_USUARIO_CADSUS), paciente));
//        parametros.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME_PROCEDIMENTO), procedimento));
//        parametros.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_DATA_SOLICITACAO), periodo));
//        parametros.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO), QueryCustom.QueryCustomParameter.IS_NOT_NULL));
//        parametros.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_STATUS), QueryCustom.QueryCustomParameter.DIFERENTE, ExameRequisicao.Status.CANCELADO.value()));
//        if (empresaSolicitante != null) {
//            parametros.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_EMPRESA), QueryCustom.QueryCustomParameter.IN, empresaSolicitante.getValue()));
//        } else if (!isActionPermitted(Permissions.EMPRESA)) {
//            try {
//                Usuario usuario = SessaoAplicacaoImp.getInstance().<br.com.ksisolucoes.vo.controle.Usuario>getUsuario();
//                usuario.setEmpresasUsuario(BOFactoryWicket.getBO(UsuarioFacade.class).getEmpresasUsuario(usuario));
//                parametros.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_SOLICITACAO_AGENDAMENTO, SolicitacaoAgendamento.PROP_EMPRESA, Empresa.PROP_CODIGO), QueryCustom.QueryCustomParameter.IN, usuario.getEmpresasUsuario()));
//            } catch (SGKException ex) {
//                Logger.getLogger(ConsultaCotaCboPage.class.getName()).log(Level.SEVERE, null, ex);
//            }
//        }
//
//        return parametros;
//    }
//
//    private IColumn getCustomActionColumn() {
//        return new MultipleActionCustomColumn<ExameRequisicao>() {
//            @Override
//            public void customizeColumn(final ExameRequisicao rowObject) {
//                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<ExameRequisicao>() {
//
//                    @Override
//                    public void action(AjaxRequestTarget target, ExameRequisicao modelObject) throws ValidacaoException, DAOException {
//                        CadastroSolicitacaoProcedimentoDTO object = new CadastroSolicitacaoProcedimentoDTO();
//                        object.setData(modelObject.getSolicitacaoAgendamento().getDataSolicitacao());
//                        object.setEstabelecimentoSolicitante(modelObject.getSolicitacaoAgendamento().getEmpresa());
//                        object.setPaciente(modelObject.getSolicitacaoAgendamento().getUsuarioCadsus());
//                        object.setProcedimento(modelObject.getExameProcedimento());
//                        object.setProfissionalSolicitante(modelObject.getSolicitacaoAgendamento().getProfissional());
////                        setResponsePage(new CadastroSolicitacaoProcedimentoPage(object, true));
//                    }
//                }).setTitleBundleKey("consultar");
//
//                addAction(ActionType.REMOVER, rowObject, new IModelAction<ExameRequisicao>() {
//                    @Override
//                    public void action(AjaxRequestTarget target, ExameRequisicao modelObject) throws ValidacaoException, DAOException {
//                        if (dlgMotivo == null) {
//                            addModal(target, dlgMotivo = new DlgMotivoObject<ExameRequisicao>(newModalId(), bundle("motivo")) {
//                                @Override
//                                public void onConfirmar(AjaxRequestTarget target, String motivo, ExameRequisicao object) throws ValidacaoException, DAOException {
////                                    BOFactoryWicket.getBO(ExameFacade.class).cancelarSolicitacaoProcedimento(object.getSolicitacaoAgendamento(), object.getExame(), motivo, false);
//                                    getPageableTable().populate();
//                                    getPageableTable().update(target);
//                                }
//                            });
//                        }
//                        dlgMotivo.setObject(rowObject);
//                        dlgMotivo.show(target);
//                    }
//                });
//
//                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<ExameRequisicao>() {
//                    @Override
//                    public DataReport action(ExameRequisicao modelObject) throws ReportException {
//                        return ExameViewUtil.imprimirExame(modelObject.getExame());
//                    }
//                });
//
//                addAction(ActionType.CONFIRMAR, rowObject, new IModelAction<ExameRequisicao>() {
//                    @Override
//                    public void action(AjaxRequestTarget target, ExameRequisicao modelObject) throws ValidacaoException, DAOException {
//                        ExameCadastroAprovacaoDTO dto = new ExameCadastroAprovacaoDTO();
//                        dto.setDataSolicitacao(modelObject.getSolicitacaoAgendamento().getDataSolicitacao());
//                        dto.setNomePaciente(modelObject.getSolicitacaoAgendamento().getUsuarioCadsus().getNomeSocial());
//                        dto.setNomeProfissional(modelObject.getSolicitacaoAgendamento().getNomeProfissionalOrigem());
//                        dto.setSolicitacaoProcedimento(true);
////                        setResponsePage(new CadastroAutorizacaoAgendamentoPage(dto, modelObject.getSolicitacaoAgendamento()));
//                    }
//                }).setTitleBundleKey("autorizar")
//                        .setEnabled(rowObject != null && rowObject.getExame() != null && rowObject.getExame().getTipoExame() != null && rowObject.getExame().getTipoExame().getTipoProcedimento() != null
//                                && RepositoryComponentDefault.SIM_LONG.equals(rowObject.getExame().getTipoExame().getTipoProcedimento().getFlagRequerAutorizacao())
//                                && RepositoryComponentDefault.NAO.equals(rowObject.getExame().getTipoExame().getTipoProcedimento().getRegulado())
//                                && !Exame.STATUS_AUTORIZADO.equals(rowObject.getExame().getStatus()));
//            }
//        };
//    }
//
////    @Override
////    public Class getCadastroPage() {
////        return CadastroSolicitacaoProcedimentoPage.class;
////    }
//
//    @Override
//    public String getTituloPrograma() {
//        return bundle("consultaSolicitacaoProcedimento");
//    }
//}
