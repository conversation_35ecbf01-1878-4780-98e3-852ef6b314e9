package br.com.celk.view.consorcio.consorcioprestador;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestador;
import java.util.Arrays;

import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;


/**
 *
 * <AUTHOR>
 */
@Private

public class NovoConsorcioPrestadorPage extends BasePage{

    private AutoCompleteConsultaEmpresa autoCompleteConsultaPrestador;
    
    private Empresa consorcio;
    private Empresa prestador;
    
    public NovoConsorcioPrestadorPage() {
        init();
    }

    private void init(){
        Form form = new Form("form", new CompoundPropertyModel(this));
        
        form.add(new AutoCompleteConsultaEmpresa("consorcio", true).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIO)).setValidarTipoEstabelecimento(true));
        form.add(autoCompleteConsultaPrestador = new AutoCompleteConsultaEmpresa("prestador", true).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO)).setValidarTipoEstabelecimento(true));
        
        form.add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                setResponsePage(ConsultaConsorcioPrestadorPage.class);
            }
        }.setDefaultFormProcessing(false));

        form.add(new AbstractAjaxButton("btnAvancar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                avancar();
            }
        });
        
        add(form);
        
        try {
            consorcio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("consorcioPadrao");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }
    
    private void avancar() throws DAOException, ValidacaoException{
        ConsorcioPrestador consorcioPrestador = LoadManager.getInstance(ConsorcioPrestador.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioPrestador.PROP_EMPRESA_CONSORCIO), consorcio))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ConsorcioPrestador.PROP_EMPRESA_PRESTADOR), prestador))
                .start().getVO();
        if (consorcioPrestador == null) {
            consorcioPrestador = new ConsorcioPrestador();
            consorcioPrestador.setEmpresaConsorcio(consorcio);
            consorcioPrestador.setEmpresaPrestador(prestador);
        }
        setResponsePage(new CadastroConsorcioPrestadorPage(consorcioPrestador) {
            @Override
            public Page getReturnPage() {
                return NovoConsorcioPrestadorPage.this;
            }
        });
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroProcedimentosPrestador");
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaPrestador.getTxtDescricao().getTextField();
    }

}
