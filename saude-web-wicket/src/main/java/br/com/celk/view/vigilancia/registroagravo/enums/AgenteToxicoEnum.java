package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum AgenteToxicoEnum implements IEnum<AgenteToxicoEnum> {
    MEDICAMENTO(1L, "Medicamento"),
    AGROTOXICO_AGRICOLA(2L, "Agrotóxico/uso agrícola"),
    AGROTOXICO_DOMESTICO(3L, "Agrotóxico/uso doméstico"),
    AGROTOXICO_SAUDE_PUBLICA(4L, "Agrotóxico/uso saúde pública"),
    RATICIDA(5L, "Raticida"),
    PRODUTO_VETERINARIO(6L, "Produto veterinário"),
    PRODUTO_DOMICILIAR(7L, "Produto de uso Domiciliar"),
    COSMETICOS(8L, "Cosmético/higiene pessoal"),
    PRODUTO_QUIMICO(9L, "Produto químico de uso industrial"),
    METAL(10L, "Metal"),
    DROGAS_ABUSO(11L, "Drogas de abuso"),
    PLANTA_TOXICA(12L, "Planta tóxica"),
    ALIMENTO(13L, "Alimento e bebida"),
    OUTRO(14L, "Outro"),
    IGNORADO(99L, "Ignorado");

    private Long value;
    private String descricao;

    AgenteToxicoEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() { return value;
    }

    @Override
    public String descricao() { return descricao;
    }

    public static AgenteToxicoEnum valueOf(Long value) {
        for (AgenteToxicoEnum v : AgenteToxicoEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }

}
