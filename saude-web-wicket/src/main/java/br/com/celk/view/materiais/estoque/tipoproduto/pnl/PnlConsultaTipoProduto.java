package br.com.celk.view.materiais.estoque.tipoproduto.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.materiais.estoque.tipoproduto.customize.CustomizeConsultaTipoProduto;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.entradas.estoque.TipoProduto;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaTipoProduto extends PnlConsulta<TipoProduto> {

    public PnlConsultaTipoProduto(String id, IModel<TipoProduto> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaTipoProduto(String id, IModel<TipoProduto> model) {
        super(id, model);
    }

    public PnlConsultaTipoProduto(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaTipoProduto(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaTipoProduto();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("tipoProduto");
    }

}
