package br.com.celk.view.vigilancia.requerimentos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.vigilancia.eventos.PnlDadosCadastroEventoVigilancia;
import br.com.celk.view.vigilancia.financeiro.BoletoVigilanciaPage;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
@Private
public class RequerimentoCadastroEventoPage extends BasePage {

    private boolean externo;
    private RequerimentoVigilancia requerimentoVigilancia;
    private Class classReturn;
    private TipoSolicitacao tipoSolicitacao;
    private Form<CadastroEventosVigilanciaDTO> form;
    private EventosVigilancia eventosVigilancia;
    private DlgImpressaoObjectMulti<RequerimentoVigilancia> dlgConfirmacaoImpressao;
    private AjaxPreviewBlank ajaxPreviewBlank;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;


    public RequerimentoCadastroEventoPage(TipoSolicitacao tipoSolicitacao, Class clazz, boolean externo) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.externo = externo;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoCadastroEventoPage(TipoSolicitacao tipoSolicitacao, Class clazz, boolean externo, EventosVigilancia eventosVigilancia) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.externo = externo;
        this.eventosVigilancia = eventosVigilancia;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoCadastroEventoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz, boolean permissaoEditar) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarCadastroEvento(requerimentoVigilancia);
        if (permissaoEditar && viewOnly) {
            init(true);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(final boolean enabled) {
        if (eventosVigilancia == null) {
            eventosVigilancia = new EventosVigilancia();
            eventosVigilancia.setRequerimentoVigilancia(new RequerimentoVigilancia());
            add(new PnlDadosCadastroEventoVigilancia("panelCadastro", classReturn != null ? classReturn : RequerimentosPage.class, tipoSolicitacao) {
                @Override
                public Panel getPanelAnexos() {
                    PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
                    dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
                    dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(carregarAnexos(requerimentoVigilancia));
                    pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo("panelAnexos", dtoPnlAnexo, enabled);
                    pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
                    return pnlRequerimentoVigilanciaAnexo;
                }

                @Override
                public void salvar(AjaxRequestTarget target, CadastroEventosVigilanciaDTO dto) throws DAOException, ValidacaoException {
                    RequerimentoCadastroEventoPage.this.salvar(target, dto);
                }

                @Override
                public boolean requerimentoExterno() {
                    return false;
                }
            });
        } else {
            add(new PnlDadosCadastroEventoVigilancia("panelCadastro", eventosVigilancia, enabled, classReturn != null ? classReturn : RequerimentosPage.class, tipoSolicitacao) {
                @Override
                public Panel getPanelAnexos() {
                    PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
                    dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
                    dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(carregarAnexos(requerimentoVigilancia));
                    pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo("panelAnexos", dtoPnlAnexo, enabled);
                    pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
                    return pnlRequerimentoVigilanciaAnexo;
                }

                @Override
                public void salvar(AjaxRequestTarget target, CadastroEventosVigilanciaDTO dto) throws DAOException, ValidacaoException {
                    RequerimentoCadastroEventoPage.this.salvar(target, dto);
                }

                @Override
                public boolean requerimentoExterno() {
                    return false;
                }
            });
        }

        add(ajaxPreviewBlank = new AjaxPreviewBlank());

    }

    private void carregarCadastroEvento(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            eventosVigilancia = LoadManager.getInstance(EventosVigilancia.class)
                    .addProperties(new HQLProperties(EventosVigilancia.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, EventosVigilancia.PROP_ESTABELECIMENTO).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, EventosVigilancia.PROP_VIGILANCIA_ENDERECO).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, EventosVigilancia.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(EventosVigilancia.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            eventosVigilancia.setRequerimentoVigilancia(requerimentoVigilancia);
        }
    }

    private List<RequerimentoVigilanciaAnexoDTO> carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        List<RequerimentoVigilanciaAnexoDTO> listRequerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            listRequerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        return listRequerimentoVigilanciaAnexoDTOList;
    }

    private void salvar(AjaxRequestTarget target, CadastroEventosVigilanciaDTO dto) throws DAOException, ValidacaoException {

        dto.setTipoSolicitacao(tipoSolicitacao);
        if (requerimentoVigilancia == null) {
            dto.getEventosVigilancia().setRequerimentoVigilancia(new RequerimentoVigilancia());
        }
        dto.getEventosVigilancia().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        if (externo) {
            dto.getEventosVigilancia().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());
        }
        dto.setTipoSolicitacao(tipoSolicitacao);
        dto.getEventosVigilancia().setDataInicial(dto.getPeriodo().getDataInicial());
        dto.getEventosVigilancia().setDataFinal(dto.getPeriodo().getDataFinal());

        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoCadastroEvento(dto);

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        final ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }

        if (externo) {
            gerarBoleto(target, rv, configuracaoVigilancia);
        }

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoVigilancia>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoVigilancia object) throws ReportException {
                RelatorioRequerimentoVigilanciaComprovanteDTOParam param = new RelatorioRequerimentoVigilanciaComprovanteDTOParam();
                param.setRequerimentoVigilancia(object);

                QRCodeGenerateDTOParam qrCodeParam = new QRCodeGenerateDTOParam(VigilanciaHelper.getURLQRCodePageRequerimento(), object.getChaveQRcode());
                param.setQRCodeParam(qrCodeParam);

                DataReport comprovanteRequerimento = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoVigilanciaComprovante(param);

                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(comprovanteRequerimento);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVigilancia object) throws ValidacaoException, DAOException {
                if (VigilanciaHelper.abrirTelaFinanceiro(object)) {
                    RequerimentoVigilanciaDTO dto = new RequerimentoVigilanciaDTO();
                    dto.setRequerimentoVigilancia(object);
                    setResponsePage(new BoletoVigilanciaPage(dto, classReturn));
                } else {
                    try {
                        Page pageReturn = (Page) classReturn.newInstance();
                        getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getProtocoloFormatado()));
                        setResponsePage(pageReturn);
                    } catch (InstantiationException | IllegalAccessException e) {
                        Loggable.log.error(e.getMessage(), e);
                    }
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, rv);
    }

    private void gerarBoleto(AjaxRequestTarget target, RequerimentoVigilancia requerimentoVigilancia, ConfiguracaoVigilancia configuracaoVigilancia) throws ValidacaoException, DAOException {
        String boletoBase64RequerimentoExterno = FinanceiroVigilanciaHelper.getBoletoBase64RequerimentoExterno(requerimentoVigilancia, configuracaoVigilancia);

        if (boletoBase64RequerimentoExterno != null) {
            ajaxPreviewBlank.initiatePdfBase64(target, boletoBase64RequerimentoExterno);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroEventos");
    }
}
