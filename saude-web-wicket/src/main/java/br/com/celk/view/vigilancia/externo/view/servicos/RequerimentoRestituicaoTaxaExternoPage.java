package br.com.celk.view.vigilancia.externo.view.servicos;

import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.dialog.DlgImpressaoObjectMulti;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.telefonefield.TelefoneField;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.cadastro.interfaces.ICadastroListener;
import br.com.celk.view.vigilancia.endereco.DlgCadastroVigilanciaEndereco;
import br.com.celk.view.vigilancia.endereco.autocomplete.AutoCompleteConsultaVigilanciaEndereco;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.externo.template.base.RequerimentosVigilanciaPage;
import br.com.celk.view.vigilancia.externo.view.estabelecimento.CadastroEstabelecimentoExternoPage;
import br.com.celk.view.vigilancia.faturamento.autocomplete.AutoCompleteConsultaAtividadesVigilancia;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaOcorrenciaPanel;
import br.com.celk.view.vigilancia.requerimentos.RequerimentoVigilanciaSolicitantePanel;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.PnlRequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RelatorioRequerimentoSolicitacaoJuridicaDTOParam;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoRestituicaoTaxaDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.RequerimentoVigilanciaAnexoDTO;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.IReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoRestituicaoTaxa;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by maicon on 27/12/18.
 */
public class RequerimentoRestituicaoTaxaExternoPage extends RequerimentosVigilanciaPage {

    private Form<RequerimentoRestituicaoTaxaDTO> form;
    private DlgImpressaoObjectMulti<RequerimentoRestituicaoTaxaDTO> dlgConfirmacaoImpressao;

    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoRestituicaoTaxa requerimentoRestituicaoTaxa;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private InputField<String> txtCnpjCpf;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtDescricaoAtividadeEstabelecimento;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private AttributeModifier attributeModifierCnpj = new AttributeModifier("class", "cnpj");
    private AttributeModifier attributeModifierCpf = new AttributeModifier("class", "cpf");
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;
    private UpperField txtCpfSolicitante;
    private UpperField txtRgSolicitante;

    private WebMarkupContainer containerDadosComplementares;
    private WebMarkupContainer containerDadosComplementaresPF;
    private WebMarkupContainer containerRequerentePessoa;
    private WebMarkupContainer containerRequerenteEstabelecimento;
    private LongField txtNumeroEndereco;
    private MultiLineLabel messageLabel;
    private InputField txtTelefone;
    private InputField txtNumeroProcesso;
    private InputArea txtMotivoRestituicao;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private DlgCadastroVigilanciaPessoa dlgCadastroVigilanciaPessoa;
    private AutoCompleteConsultaVigilanciaEndereco autoCompleteConsultaVigilanciaEndereco;
    private DlgCadastroVigilanciaEndereco dlgCadastroVigilanciaEndereco;

    private DropDown dropDownTipoRequerente;
    private Class classReturn;
    private boolean enabled;
    private Long codigoRestituicaoTaxa;
    private WebMarkupContainer containerNumeroEnderecoPessoa;

    private AutoCompleteConsultaAtividadesVigilancia autoCompleteConsultaAtividadesVigilancia;
    private AbstractAjaxLink btnCadastroPessoa;
    private Estabelecimento estabelecimento;

    public RequerimentoRestituicaoTaxaExternoPage(TipoSolicitacao tipoSolicitacao, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoRestituicaoTaxaExternoPage(TipoSolicitacao tipoSolicitacao, Estabelecimento estabelecimento, Class clazz) {
        this.tipoSolicitacao = tipoSolicitacao;
        this.estabelecimento = estabelecimento;
        init(true);
        this.classReturn = clazz;
    }

    public RequerimentoRestituicaoTaxaExternoPage(RequerimentoVigilancia requerimentoVigilancia, boolean viewOnly, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimento(requerimentoVigilancia);
        if (RequerimentoVigilancia.Situacao.PENDENTE.value().equals(requerimentoVigilancia.getSituacao())
                || RequerimentoVigilancia.Situacao.ANALISE.value().equals(requerimentoVigilancia.getSituacao())) {
            init(viewOnly);
        } else {
            init(false);
        }
        this.classReturn = clazz;
    }

    private void init(boolean viewOnly) {
        this.enabled = viewOnly;
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }
        info(bundle("msgInformativaProtocoloSolicitacaoJuridica"));

        RequerimentoRestituicaoTaxaDTO proxy = on(RequerimentoRestituicaoTaxaDTO.class);

        form = new Form("form", new CompoundPropertyModel(new RequerimentoRestituicaoTaxaDTO()));
        if (requerimentoRestituicaoTaxa != null) {
            form.getModel().getObject().setRequerimentoRestituicaoTaxa(requerimentoRestituicaoTaxa);
        } else {
            form.getModel().getObject().setRequerimentoRestituicaoTaxa(new RequerimentoRestituicaoTaxa());
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().setRequerimentoVigilancia(new RequerimentoVigilancia());
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setTipoRequerente(RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value());
        }
        codigoRestituicaoTaxa = form.getModel().getObject().getRequerimentoRestituicaoTaxa().getCodigo();

        form.add(autoCompleteConsultaAtividadesVigilancia = (AutoCompleteConsultaAtividadesVigilancia) new AutoCompleteConsultaAtividadesVigilancia(path(proxy.getRequerimentoRestituicaoTaxa().getAtividadesVigilancia()), true));
        autoCompleteConsultaAtividadesVigilancia.setApenasSolicitacoesJuridicas(true);
        autoCompleteConsultaAtividadesVigilancia.setLabel(Model.of(BundleManager.getString("atividadesVigilancia")));
        autoCompleteConsultaAtividadesVigilancia.setEnabled(enabled);

        form.add(dropDownTipoRequerente = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getTipoRequerente()), RequerimentoVigilancia.TipoRequerente.values()));
        dropDownTipoRequerente.addAjaxUpdateValue();
        dropDownTipoRequerente.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                configurarTipoRequerente(target, true);
            }
        });
        dropDownTipoRequerente.setEnabled(enabled);
        // estabelecimento
        form.add(getContainerRequerenteEstabelecimento(proxy));
        // pessoa
        form.add(getContainerDenunciadoPessoa(proxy));
        // endereço
        addCamposEndereco(proxy);

        form.add(txtNumeroProcesso = new InputField(path(proxy.getRequerimentoRestituicaoTaxa().getNumeroProcesso())));
        txtNumeroProcesso.setEnabled(enabled);
        form.add(txtMotivoRestituicao = new InputArea(path(proxy.getRequerimentoRestituicaoTaxa().getMotivoRestituicao())));
        txtMotivoRestituicao.addRequiredClass();
        txtMotivoRestituicao.setEnabled(enabled);

        form.add(new RequerimentoVigilanciaSolicitantePanel("solicitantePanel", form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia(), enabled));

        {//Inicio Anexos
            PnlRequerimentoVigilanciaAnexoDTO dtoPnlAnexo = new PnlRequerimentoVigilanciaAnexoDTO();
            dtoPnlAnexo.setRequerimentoVigilanciaAnexoDTOList(requerimentoVigilanciaAnexoDTOList);
            dtoPnlAnexo.setTipoSolicitacao(tipoSolicitacao);
            form.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(dtoPnlAnexo, enabled));
            pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        }

        form.add(new VoltarButton("btnVoltar"));
        form.add(new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }).setEnabled(enabled));

        form.add(new RequerimentoVigilanciaOcorrenciaPanel("ocorrencias", form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getCodigo(), true).setVisible(!enabled));

        if(estabelecimento != null && estabelecimento.getCodigo() != null){
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setTipoRequerente(RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value());
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setEstabelecimento(estabelecimento);

            atualizarEnderecoEstabelecimento(null, estabelecimento);
        }
        configurarTipoRequerente(null, false);
        add(form);

        if (codigoRestituicaoTaxa != null) {
            this.txtCnpjCpf.add(this.attributeModifierCpf);
            if (RequerimentoVigilancia.TipoPessoa.JURIDICA.value().equals(form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getTipoPessoa())) {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.remove(attributeModifierCpf);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.add(attributeModifierCnpj);
                }
            } else {
                if (this.txtCnpjCpf.getBehaviors().contains(attributeModifierCnpj)) {
                    this.txtCnpjCpf.remove(attributeModifierCnpj);
                }
                if (!this.txtCnpjCpf.getBehaviors().contains(attributeModifierCpf)) {
                    this.txtCnpjCpf.add(attributeModifierCpf);
                }
            }
            if (form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento() != null) {
                atualizarDadosEstabelecimento(null, form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento());
            }
        }
    }

    private void configurarTipoRequerente(AjaxRequestTarget target, boolean limparCampos) {
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(form.getModelObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getTipoRequerente())) {
            containerRequerenteEstabelecimento.setVisible(true);
            containerRequerentePessoa.setVisible(false);
        } else {
            containerRequerenteEstabelecimento.setVisible(false);
            containerRequerentePessoa.setVisible(true);
        }

        if (RequerimentoVigilancia.TipoRequerente.PESSOA.value().equals(form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getTipoRequerente())) {
            containerNumeroEnderecoPessoa.setVisible(true);
        } else {
            containerNumeroEnderecoPessoa.setVisible(false);
        }

        if (target != null) {
            if (limparCampos) {
                atualizarDadosEstabelecimento(target, null);
            }
            target.add(containerRequerenteEstabelecimento);
            target.add(containerRequerentePessoa);
            target.add(containerNumeroEnderecoPessoa);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private WebMarkupContainer getContainerRequerenteEstabelecimento(RequerimentoRestituicaoTaxaDTO proxy) {
        containerRequerenteEstabelecimento = new WebMarkupContainer("containerRequerenteEstabelecimento");
        containerRequerenteEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerRequerenteEstabelecimento.add(autoCompleteConsultaEstabelecimento = (AutoCompleteConsultaEstabelecimento) new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento()),true).setEnabled(enabled));
        autoCompleteConsultaEstabelecimento.setLabel((new Model(bundle("estabelecimento"))));
        autoCompleteConsultaEstabelecimento.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaEstabelecimento.setExibirNaoAutorizados(true);
        autoCompleteConsultaEstabelecimento.setExibirProvisorios(true);
        try {
            autoCompleteConsultaEstabelecimento.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoEstabelecimentoRequerimentoExterno")));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }

        containerRequerenteEstabelecimento.add(new AbstractAjaxLink("btnCadEstabelecimento") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                RequerimentoRestituicaoTaxaExternoPage.this.setResponsePage(new CadastroEstabelecimentoExternoPage(tipoSolicitacao, true));
            }
        });

        containerRequerenteEstabelecimento.add(txtFantasia = new DisabledInputField<String>(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento().getFantasia())));
        containerDadosComplementares = new WebMarkupContainer("containerDadosComplementares");
        containerDadosComplementares.setVisible(codigoRestituicaoTaxa != null && form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento() != null);
        containerDadosComplementares.setOutputMarkupId(true);
        containerDadosComplementares.setOutputMarkupPlaceholderTag(true);
        containerDadosComplementares.add(txtFantasia = new DisabledInputField<String>(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento().getFantasia())));
        containerDadosComplementares.add(txtDescricaoAtividadeEstabelecimento = new DisabledInputField<String>(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento().getAtividadeEstabelecimento().getDescricao())));
        containerDadosComplementares.add(txtTelefone = (InputField) new TelefoneField(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getTelefone())).setEnabled(enabled));
        containerDadosComplementares.add(txtCnpjCpf = new InputField<>(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getCnpjCpf())));
        containerDadosComplementares.setEnabled(enabled && codigoRestituicaoTaxa == null);
        containerRequerenteEstabelecimento.add(containerDadosComplementares);
        txtCnpjCpf.setEnabled(codigoRestituicaoTaxa != null && form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento() == null);

        autoCompleteConsultaEstabelecimento.add(new ConsultaListener<Estabelecimento>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarEnderecoEstabelecimento(target, object);
            }
        });

        autoCompleteConsultaEstabelecimento.add(new RemoveListener<Estabelecimento>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Estabelecimento object) {
                atualizarEnderecoEstabelecimento(target, null);
            }
        });

        return containerRequerenteEstabelecimento;
    }

    private WebMarkupContainer getContainerDenunciadoPessoa(RequerimentoRestituicaoTaxaDTO proxy) {
        containerRequerentePessoa = new WebMarkupContainer("containerRequerentePessoa");
        containerRequerentePessoa.setOutputMarkupId(true);
        containerRequerentePessoa.setOutputMarkupPlaceholderTag(true);

        containerRequerentePessoa.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getVigilanciaPessoa()), true));
        autoCompleteConsultaVigilanciaPessoa.setLabel((new Model(bundle("pessoa"))));
        containerDadosComplementaresPF = new WebMarkupContainer("containerDadosComplementaresPF");
        containerDadosComplementaresPF.setVisible(codigoRestituicaoTaxa != null && form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento() != null);
        containerDadosComplementaresPF.setOutputMarkupId(true);
        containerDadosComplementaresPF.setOutputMarkupPlaceholderTag(true);
        containerDadosComplementaresPF.add(new TelefoneField(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getTelefone())).setEnabled(enabled));
        containerDadosComplementaresPF.add(new InputField<>(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getCnpjCpf())).setEnabled(false));
        containerRequerentePessoa.add(containerDadosComplementaresPF);
        autoCompleteConsultaVigilanciaPessoa.setOutputMarkupPlaceholderTag(true);
        try {
            autoCompleteConsultaVigilanciaPessoa.setFiltrarUsuarioLogado(RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.VIGILANCIA_SANITARIA).getParametro("restricaoPessoaRequerimentoExterno")));
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarEnderecoPessoa(target, object);
            }
        });

        autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarEnderecoPessoa(target, null);
            }
        });

        containerRequerentePessoa.add(btnCadastroPessoa = new AbstractAjaxLink("btnCadPessoa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                addModal(target, dlgCadastroVigilanciaPessoa = new DlgCadastroVigilanciaPessoa(newModalId(), true) {
                    @Override
                    public void setVigilanciaPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
                        autoCompleteConsultaVigilanciaPessoa.limpar(target);
                        autoCompleteConsultaVigilanciaPessoa.setComponentValue(target, vigilanciaPessoa);
                        autoCompleteConsultaVigilanciaEndereco.limpar(target);
                        VigilanciaEndereco ve = LoadManager.getInstance(VigilanciaEndereco.class).setId(vigilanciaPessoa.getVigilanciaEndereco().getCodigo()).start().getVO();
                        form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
                        target.add(txtNumeroEndereco);
                        target.add(autoCompleteConsultaVigilanciaEndereco);
                    }
                });
                dlgCadastroVigilanciaPessoa.show(target, new VigilanciaPessoa());
            }
        });
        btnCadastroPessoa.getAjaxRegionMarkupId();
        btnCadastroPessoa.setOutputMarkupPlaceholderTag(true);
        btnCadastroPessoa.setEnabled(enabled);
        return containerRequerentePessoa;
    }

    private void addCamposEndereco(RequerimentoRestituicaoTaxaDTO proxy) {
        containerNumeroEnderecoPessoa = new WebMarkupContainer("containerNumeroEnderecoPessoa");

        form.add(new AbstractAjaxLink("btnCadastrarVigilanciaEndereco") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                cadastrarVigilanciaEndereco(target);
            }
        });

        form.add(autoCompleteConsultaVigilanciaEndereco = new AutoCompleteConsultaVigilanciaEndereco(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getVigilanciaEndereco()), true));
        autoCompleteConsultaVigilanciaEndereco.setLabel(new Model(bundle("endereco")));
        autoCompleteConsultaVigilanciaEndereco.setEnabled(enabled);

        containerNumeroEnderecoPessoa.add(txtNumeroEndereco = new LongField(path(proxy.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getVigilanciaPessoa().getNumeroLogradouro())));
        containerNumeroEnderecoPessoa.setOutputMarkupPlaceholderTag(true);
        txtNumeroEndereco.addAjaxUpdateValue();
        txtNumeroEndereco.setConvertZeroToNull(true);
        txtNumeroEndereco.setVMin(0L);
        txtNumeroEndereco.setASep("");
        form.add(containerNumeroEnderecoPessoa);
    }

    private void atualizarEnderecoEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if(target != null){
            autoCompleteConsultaVigilanciaEndereco.limpar(target);
        }

        if (estabelecimento != null) {
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoEstabelecimento(estabelecimento);
            form.getModelObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setTelefone(estabelecimento.getTelefone());
            if(target != null) {
                autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);
            } else {
                autoCompleteConsultaVigilanciaEndereco.setComponentValue(ve);
            }
        }
        atualizarDadosEstabelecimento(target, estabelecimento);
    }


    private void atualizarEnderecoPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        autoCompleteConsultaVigilanciaEndereco.limpar(target);
        txtNumeroEndereco.limpar(target);

        if (vigilanciaPessoa != null) {
            VigilanciaPessoa vo = LoadManager.getInstance(VigilanciaPessoa.class)
                    .addProperty(VigilanciaPessoa.PROP_NUMERO_LOGRADOURO)
                    .addProperty(VigilanciaPessoa.PROP_CELULAR)
                    .addProperty(VigilanciaPessoa.PROP_CPF)
                    .setId(vigilanciaPessoa.getCodigo())
                    .start().getVO();
            VigilanciaEndereco ve = VigilanciaHelper.carregarVigilanciaEnderecoPessoa(vigilanciaPessoa);

            form.getModelObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setVigilanciaEndereco(ve);
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setTelefone(vo.getCelular());
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setCnpjCpf(vo.getCpfFormatado());

            txtNumeroEndereco.setComponentValue(vo.getNumeroLogradouro());
            autoCompleteConsultaVigilanciaEndereco.setComponentValue(target, ve);
            containerDadosComplementaresPF.setVisible(true);
        } else {
            containerDadosComplementaresPF.setVisible(false);
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setVigilanciaEndereco(null);
        }
        target.add(containerDadosComplementaresPF);
        target.add(txtNumeroEndereco);
        target.appendJavaScript(JScript.initMasks());
    }

    private void cadastrarVigilanciaEndereco(AjaxRequestTarget target) {
        if (dlgCadastroVigilanciaEndereco == null) {
            WindowUtil.addModal(target, form, dlgCadastroVigilanciaEndereco = new DlgCadastroVigilanciaEndereco(newModalId()));
            dlgCadastroVigilanciaEndereco.add(new ICadastroListener<VigilanciaEndereco>() {
                @Override
                public void onSalvar(AjaxRequestTarget target, VigilanciaEndereco vigilanciaEndereco) throws ValidacaoException, DAOException {
                    autoCompleteConsultaVigilanciaEndereco.limpar(target);
                    autoCompleteConsultaVigilanciaEndereco.setComponentValue(vigilanciaEndereco);
                    target.add(autoCompleteConsultaVigilanciaEndereco);
                }
            });
        }
        dlgCadastroVigilanciaEndereco.show(target);
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        RequerimentoRestituicaoTaxaDTO dto = form.getModel().getObject();
        if (dto.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento() == null && dto.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getVigilanciaPessoa() == null) {
            throw new ValidacaoException(BundleManager.getString("msgObrigatorioInformarEstabOuNome"));
        }
        if (dto.getRequerimentoRestituicaoTaxa().getMotivoRestituicao() == null) {
            throw new ValidacaoException(BundleManager.getString("msgObrigadoInformarMotivo"));
        }
        if (form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getCpfSolicitante() == null
                && form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getRgSolicitante() == null) {
            throw new ValidacaoException(bundle("msgInformeCpfEOURgSolicitante"));
        }
        dto.setRequerimentoVigilanciaAnexoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoDTOList());
        dto.setRequerimentoVigilanciaAnexoExcluidoDTOList(pnlRequerimentoVigilanciaAnexo.getRequerimentoVigilanciaAnexoExcluidoDTOList());
        dto.setTipoSolicitacao(tipoSolicitacao);
        dto.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setTipoSolicitacao(tipoSolicitacao);
        form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setOrigem(RequerimentoVigilancia.Origem.EXTERNO.value());
        RequerimentoVigilancia rv = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoRestituicaoTaxa(form.getModel().getObject());

        String mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocolo");
        final ConfiguracaoVigilancia configuracaoVigilancia = VigilanciaHelper.getConfiguracaoVigilancia();
        if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
            mensagemImpressao = bundle("msgImprimirComprovanteRequerimentoProtocoloTermoSolicitacaoServico");
        }
        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObjectMulti<RequerimentoRestituicaoTaxaDTO>(newModalId(), mensagemImpressao) {
            @Override
            public List<IReport> getDataReports(RequerimentoRestituicaoTaxaDTO object) throws ReportException {
                RelatorioRequerimentoSolicitacaoJuridicaDTOParam param = new RelatorioRequerimentoSolicitacaoJuridicaDTOParam();
                param.setRequerimentoVigilancia(object.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia());

                DataReport requerimentoSolicitacaoJuridicaReport = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoRequerimentoSolicitacaoJuridica(param);

                List<IReport> lstDataReport = new ArrayList();
                lstDataReport.add(requerimentoSolicitacaoJuridicaReport);

                if (RepositoryComponentDefault.SIM_LONG.equals(configuracaoVigilancia.getFlagEmitirTermoSolicitacaoServico())) {
                    DataReport termoSolicitacaoServico = BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoTermoSolicitacaoServico(object.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getCodigo());
                    lstDataReport.add(termoSolicitacaoServico);
                }

                return lstDataReport;
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoRestituicaoTaxaDTO object) throws ValidacaoException, DAOException {
                try {
                    Page pageReturn = (Page) classReturn.newInstance();
                    getSession().getFeedbackMessages().info(pageReturn, BundleManager.getString("registro_salvo_sucesso_protocolo_x", object.getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getProtocoloFormatado()));
                    setResponsePage(pageReturn);
                } catch (InstantiationException | IllegalAccessException e) {
                    Loggable.log.error(e.getMessage(), e);
                }
            }
        });

        dto.getRequerimentoRestituicaoTaxa().setRequerimentoVigilancia(rv);
        dlgConfirmacaoImpressao.show(target, dto);
    }

    private void atualizarDadosEstabelecimento(AjaxRequestTarget target, Estabelecimento estabelecimento) {
        if (estabelecimento != null) {
            if (form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf() == null || form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpf().isEmpty()) {
                Estabelecimento estabPrincipal = LoadManager.getInstance(Estabelecimento.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(Estabelecimento.PROP_CODIGO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento.getEstabelecimentoPrincipal().getCodigo()))
                        .start().getVO();

                form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setCnpjCpf(estabPrincipal.getCnpjCpfFormatado());
                form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setTipoPessoa(estabPrincipal.getTipoPessoa());
            } else {
                form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setCnpjCpf(estabelecimento.getCnpjCpfFormatado());
                form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setTipoPessoa(estabelecimento.getTipoPessoa());
            }
            if (target != null) {
                autoCompleteConsultaVigilanciaEndereco.limpar(target);
            }

            EstabelecimentoAtividade estabelecimentoAtividade = LoadManager.getInstance(EstabelecimentoAtividade.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_ESTABELECIMENTO, QueryCustom.QueryCustomParameter.IGUAL, estabelecimento))
                    .addParameter(new QueryCustom.QueryCustomParameter(EstabelecimentoAtividade.PROP_FLAG_PRINCIPAL, QueryCustom.QueryCustomParameter.IGUAL, RepositoryComponentDefault.SIM_LONG))
                    .start().getVO();
            if (estabelecimentoAtividade != null && estabelecimentoAtividade.getAtividadeEstabelecimento() != null) {
                form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().getEstabelecimento().setAtividadeEstabelecimento(estabelecimentoAtividade.getAtividadeEstabelecimento());
            }
            containerDadosComplementares.setVisible(true);
        } else {
            containerDadosComplementares.setVisible(false);
            form.getModel().getObject().getRequerimentoRestituicaoTaxa().getRequerimentoVigilancia().setVigilanciaEndereco(null);
            if (target != null) {
                autoCompleteConsultaVigilanciaEndereco.limpar(target);
            }
        }

        if (target != null) {
            target.add(containerDadosComplementares);
            target.add(txtCnpjCpf);
            target.add(txtFantasia);
            target.add(txtDescricaoAtividadeEstabelecimento);
            target.add(txtTelefone);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void carregarRequerimento(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoRestituicaoTaxa = LoadManager.getInstance(RequerimentoRestituicaoTaxa.class)
                    .addProperties(new HQLProperties(RequerimentoRestituicaoTaxa.class).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoRestituicaoTaxa.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoRestituicaoTaxa.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoRestituicaoTaxa.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoRestituicaoTaxa.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoRestituicaoTaxa.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoRestituicaoTaxa.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();
            carregarAnexos(requerimentoVigilancia);
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv) {
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : list) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("solicitacaoJuridica");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
    }

    @Override
    public Permissions getAction() {
        return Permissions.RESTITUICAO_TAXA;
    }
}
