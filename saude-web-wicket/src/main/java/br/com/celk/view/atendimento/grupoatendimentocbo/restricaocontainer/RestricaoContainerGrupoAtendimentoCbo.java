package br.com.celk.view.atendimento.grupoatendimentocbo.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.QueryConsultaGrupoAtendimentoCboDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerGrupoAtendimentoCbo extends Panel implements IRestricaoContainer<QueryConsultaGrupoAtendimentoCboDTOParam> {

    private InputField<String> txtCodigo;
    private InputField<String> txtDescricao;
    
    private QueryConsultaGrupoAtendimentoCboDTOParam param = new QueryConsultaGrupoAtendimentoCboDTOParam();
    
    public RestricaoContainerGrupoAtendimentoCbo(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtCodigo = new InputField<String>("codigo"));
        root.add(txtDescricao = new InputField<String>("descricao"));
        
        add(root);
    }

    @Override
    public QueryConsultaGrupoAtendimentoCboDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtCodigo;
    }

}
