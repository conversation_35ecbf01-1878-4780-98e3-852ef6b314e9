package br.com.celk.view.agenda.agendamento.customcolumn;

import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AgendamentoLoteProcesso;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.markup.html.AjaxLink;
import org.apache.wicket.markup.html.panel.Panel;

/**
 * Created by sulivan on 11/07/19.
 */
public abstract class AgendamentoListaEsperaLoteColumnPanel extends Panel {

    private AjaxLink btnVisualiarErros;
    private AjaxLink btnVisualiarSolicitacoesAgendadas;
    private AjaxLink btnCancelar;

    private DlgConfirmacao dlgConfirmacao;
    private DlgConfirmacaoOk dlgConfirmacaoErro;
    private DlgConfirmacaoOk dlgConfirmacaoSolicitacoesAgendadas;

    private AgendamentoLoteProcesso agendamentoLoteProcesso;

    public AgendamentoListaEsperaLoteColumnPanel(String id, AgendamentoLoteProcesso sincronizacaoHorusProcesso) {
        super(id);
        this.agendamentoLoteProcesso = sincronizacaoHorusProcesso;
        init();
    }

    private void init() {

        add(btnCancelar = new AbstractAjaxLink("btnCancelar") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacao(target);
                if (dlgConfirmacao != null) {
                    dlgConfirmacao.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return AgendamentoLoteProcesso.Status.ERRO.value().equals(agendamentoLoteProcesso.getStatus())
                        || AgendamentoLoteProcesso.Status.SEM_REGISTRO.value().equals(agendamentoLoteProcesso.getStatus());
            }
        });

        btnCancelar.add(new AttributeModifier("title", BundleManager.getString("cancelar")));

        add(btnVisualiarErros = new AbstractAjaxLink("btnVisualiarErros") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacaoErro(target, StringUtils.trimToNull(StringUtilKsi.removeHtmlString(agendamentoLoteProcesso.getMensagemErro())));
                if (dlgConfirmacaoErro != null) {
                    dlgConfirmacaoErro.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return !AgendamentoLoteProcesso.Status.CANCELADO.value().equals(agendamentoLoteProcesso.getStatus()) &&
                        (agendamentoLoteProcesso.getMensagemErro() != null && !agendamentoLoteProcesso.getMensagemErro().isEmpty());
            }
        });

        btnVisualiarErros.add(new AttributeModifier("title", BundleManager.getString("visualizarInconsistencias")));

        add(btnVisualiarSolicitacoesAgendadas = new AbstractAjaxLink("btnVisualiarSolicitacoesAgendadas") {

            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                initDlgConfirmacaoSolicitacoesAgendadas(target, StringUtils.trimToNull(StringUtilKsi.removeHtmlString(agendamentoLoteProcesso.getSolicitacoesAgendadas())));
                if (dlgConfirmacaoSolicitacoesAgendadas != null) {
                    dlgConfirmacaoSolicitacoesAgendadas.show(target);
                }
            }

            @Override
            public boolean isEnabled() {
                return !AgendamentoLoteProcesso.Status.CANCELADO.value().equals(agendamentoLoteProcesso.getStatus()) &&
                        (agendamentoLoteProcesso.getSolicitacoesAgendadas() != null && !agendamentoLoteProcesso.getSolicitacoesAgendadas().isEmpty());
            }
        });

        btnVisualiarSolicitacoesAgendadas.add(new AttributeModifier("title", BundleManager.getString("visualizarSolicitacoesAgendadas")));
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacao == null) {
            WindowUtil.addModal(target, this, dlgConfirmacao = new DlgConfirmacao(WindowUtil.newModalId(this), BundleManager.getString("desejaRealmenteCancelar")) {

                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    cancelarAgendamentoLoteProcesso(target);
                }
            });
        }
    }

    private void initDlgConfirmacaoErro(AjaxRequestTarget target, String mensagemErro) {
        WindowUtil.addModal(target, this, dlgConfirmacaoErro = new DlgConfirmacaoOk(WindowUtil.newModalId(this), mensagemErro, 500, 1024) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    private void initDlgConfirmacaoSolicitacoesAgendadas(AjaxRequestTarget target, String solicitacoesAgendadas) {
        WindowUtil.addModal(target, this, dlgConfirmacaoSolicitacoesAgendadas = new DlgConfirmacaoOk(WindowUtil.newModalId(this), solicitacoesAgendadas, "img-info", 500, 1024) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    private void cancelarAgendamentoLoteProcesso(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (!agendamentoLoteProcesso.getStatus().equals(AgendamentoLoteProcesso.Status.CANCELADO.value())) {
            agendamentoLoteProcesso.setStatus(AgendamentoLoteProcesso.Status.CANCELADO.value());
            agendamentoLoteProcesso = BOFactoryWicket.getBO(CadastroFacade.class).save(agendamentoLoteProcesso);
        }
        updateTable(target);
    }

    public abstract void updateTable(AjaxRequestTarget target);

}