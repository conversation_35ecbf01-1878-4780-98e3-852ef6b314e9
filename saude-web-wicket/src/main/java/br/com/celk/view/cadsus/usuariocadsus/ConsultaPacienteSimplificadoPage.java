package br.com.celk.view.cadsus.usuariocadsus;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.ISortableColumn;
import br.com.celk.component.table.column.panel.CrudActionsColumnPanel;
import br.com.celk.component.table.pageable.SelectionPageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.vigilancia.requerimentovigilancia.PendenciasFiscalPage;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.QueryConsultaUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pais;
import br.com.ksisolucoes.vo.cadsus.*;
import br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.util.SingleSortState;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.link.BookmarkablePageLink;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;
import org.apache.wicket.request.resource.CssResourceReference;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class ConsultaPacienteSimplificadoPage extends BasePage {

    private QueryPagerProvider<UsuarioCadsus, QueryConsultaUsuarioCadsusDTOParam> dataProvider;
    private SelectionPageableTable<ConsultaUsuarioCadsusDTO> pageableTable;
    private final QueryConsultaUsuarioCadsusDTOParam param = new QueryConsultaUsuarioCadsusDTOParam();
    private Long situacao;
    boolean parametroReferencia;
    
    public ConsultaPacienteSimplificadoPage() {
        init();
    }

    public ConsultaPacienteSimplificadoPage(PageParameters parameters) {
        super(parameters);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(param));

        form.add(new InputField("nome"));
        form.add(new InputField("numeroCartao"));
        form.add(new InputField("nomeMae"));
        form.add(new InputField("rg"));
        form.add(getDropDownSituacao());

        try {
            parametroReferencia = RepositoryComponentDefault.SIM.equals(BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("referencia"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        pageableTable = new SelectionPageableTable<ConsultaUsuarioCadsusDTO>("table", getColumns(), getDataProvider(), 10);

        form.add(new ProcurarButton<QueryConsultaUsuarioCadsusDTOParam>("btnProcurar", pageableTable) {

            @Override
            public QueryConsultaUsuarioCadsusDTOParam getParam() {
                return customizeParam();
            }
        });

        form.add(pageableTable);

        add(form);

        add(new BookmarkablePageLink("linkNovo", CadastroPacienteSimplificadoPage.class));
    }
    
    private DropDown getDropDownSituacao(){
        DropDown dropDown = new DropDown("situacao", new PropertyModel(this, "situacao"));
        
        dropDown.addChoice(null, "");
        dropDown.addChoice(UsuarioCadsus.SITUACAO_ATIVO, BundleManager.getString("ativo"));
        dropDown.addChoice(UsuarioCadsus.SITUACAO_INATIVO, BundleManager.getString("inativo"));
        dropDown.addChoice(UsuarioCadsus.SITUACAO_EXCLUIDO, BundleManager.getString("excluido"));
        dropDown.addChoice(UsuarioCadsus.SITUACAO_PROVISORIO, BundleManager.getString("provisorio"));
        dropDown.addChoice(UsuarioCadsus.SITUACAO_AUTO_REFERIDO, BundleManager.getString("auto_referido"));

        return dropDown;
    }
    
    private QueryConsultaUsuarioCadsusDTOParam customizeParam(){
        
        if (param.getCpf()!=null) {
            String cpf = param.getCpf().replace(".", "").replace("-", "").replace("_", "");
            if (!cpf.trim().equals("")) {
                this.param.setCpf(cpf);
            }
        }

        if (param.getNumeroCartao()!=null) {
            String numeroCartao = param.getNumeroCartao().replace(".", "").replace("-", "").replace("_", "");
            if (!numeroCartao.trim().equals("")) {
                this.param.setNumeroCartao(numeroCartao);
            }
        }
        
        param.setSituacao(null);
        
        if (situacao!=null) {
            param.setSituacao(Arrays.asList(situacao));
        }
        
        param.setExibirExcluidos(true);
        param.setExibirInativos(true);
        param.setSomenteTabelaBase(true);
        
        return param;
    }

    private QueryPagerProvider<UsuarioCadsus, QueryConsultaUsuarioCadsusDTOParam> getDataProvider() {
        if (this.dataProvider == null) {
            this.dataProvider = new QueryPagerProvider<UsuarioCadsus, QueryConsultaUsuarioCadsusDTOParam>() {

                @Override
                public DataPagingResult executeQueryPager(DataPaging<QueryConsultaUsuarioCadsusDTOParam> dataPaging) throws DAOException, ValidacaoException {
                    return BOFactoryWicket.getBO(UsuarioCadsusFacade.class).getDominioUsuarioCadsusQueryPager(dataPaging);
                }

                @Override
                public UsuarioCadsus getCustomizeVO(Serializable object) {
                    return ((ConsultaUsuarioCadsusDTO) object).getUsuarioCadsus();
                }

                @Override
                public SortParam getDefaultSort() {
                    return new SortParam(UsuarioCadsus.PROP_NOME, true);
                }

                @Override
                public void customizeParam(QueryConsultaUsuarioCadsusDTOParam param) {
                    SingleSortState<String> sortState = (SingleSortState) getDataProvider().getSortState();
        
                    param.setCampoOrdenacao(sortState.getSort().getProperty());
                    param.setTipoOrdenacao(sortState.getSort().isAscending()?"asc":"desc");
                }
            };
        }

        return this.dataProvider;
    }

    private List<ISortableColumn<ConsultaUsuarioCadsusDTO>> getColumns() {
        List<ISortableColumn<ConsultaUsuarioCadsusDTO>> columns = new ArrayList<ISortableColumn<ConsultaUsuarioCadsusDTO>>();
        ConsultaUsuarioCadsusDTO proxy = on(ConsultaUsuarioCadsusDTO.class);

        ColumnFactory columnFactory = new ColumnFactory(ConsultaUsuarioCadsusDTO.class);
        
        columns.add(getCustomColumn());
        if(parametroReferencia){
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("referencia"), UsuarioCadsus.PROP_REFERENCIA, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_REFERENCIA)));
        }else{
            columns.add(columnFactory.createSortableColumn(BundleManager.getString("codigo"), UsuarioCadsus.PROP_CODIGO, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO)));
        }
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome"), UsuarioCadsus.PROP_NOME, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_SOCIAL)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("rg"), UsuarioCadsus.PROP_RG, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_RG)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("nome_mae"), UsuarioCadsus.PROP_NOME_MAE, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME_MAE)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("situacao"), UsuarioCadsus.PROP_SITUACAO, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DESCRICAO_SITUACAO)));
        columns.add(columnFactory.createSortableColumn(BundleManager.getString("unificado"), UsuarioCadsus.PROP_FLAG_UNIFICADO, VOUtils.montarPath(ConsultaUsuarioCadsusDTO.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_DESCRICAO_FLAG_UNIFICADO)));

        return columns;
    }

    private CustomColumn<ConsultaUsuarioCadsusDTO> getCustomColumn() {
        return new CustomColumn<ConsultaUsuarioCadsusDTO>() {

            @Override
            public Component getComponent(String componentId, ConsultaUsuarioCadsusDTO rowObject) {
                return new CrudActionsColumnPanel<ConsultaUsuarioCadsusDTO>(componentId, rowObject) {

                    @Override
                    public void onEditar(AjaxRequestTarget target) throws ValidacaoException, DAOException {

                        setResponsePage(new CadastroPacienteSimplificadoPage(carregarUsuarioCadsus(getObject().getUsuarioCadsus().getCodigo())));
                    }
                    
                    @Override
                    public void onExcluir(AjaxRequestTarget target) throws ValidacaoException, DAOException  {
                        BOFactoryWicket.getBO(UsuarioCadsusFacade.class).excluirUsuarioCadsus(getObject().getUsuarioCadsus());
                        pageableTable.update(target);
                    }

                    @Override
                    public void onConsultar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        setResponsePage(new CadastroPacienteSimplificadoPage(carregarUsuarioCadsus(getObject().getUsuarioCadsus().getCodigo()), true));
                    }

                    @Override
                    public boolean isExcluirEnabled() {
                        return RepositoryComponentDefault.SIM_LONG.equals(getObject().getUsuarioCadsus().getFlagSimplificado()) && (UsuarioCadsus.SITUACAO_PROVISORIO.equals(getObject().getUsuarioCadsus().getSituacao()) || UsuarioCadsus.SITUACAO_AUTO_REFERIDO.equals(getObject().getUsuarioCadsus().getSituacao()));
                    }

                    @Override
                    public boolean isEditarEnabled() {
                        return RepositoryComponentDefault.SIM_LONG.equals(getObject().getUsuarioCadsus().getFlagSimplificado()) && (UsuarioCadsus.SITUACAO_PROVISORIO.equals(getObject().getUsuarioCadsus().getSituacao()) || UsuarioCadsus.SITUACAO_AUTO_REFERIDO.equals(getObject().getUsuarioCadsus().getSituacao()));
                    }
                };
            }

        };
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("consultaPacienteSimplificado");
    }

    private UsuarioCadsus carregarUsuarioCadsus(Long codigoUsuario) throws DAOException, ValidacaoException {
        UsuarioCadsus proxyUc = on(UsuarioCadsus.class);
        UsuarioCadsus usuarioCadsus = LoadManager.getInstance(UsuarioCadsus.class)
                .addProperties(new HQLProperties(UsuarioCadsus.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, path(proxyUc.getUsuarioCadsusUnificado())).getProperties())
                .addProperties(new HQLProperties(GerenciadorArquivo.class, path(proxyUc.getFoto())).getProperties())
                .addProperties(new HQLProperties(LocalPermanencia.class, path(proxyUc.getLocalPermanencia())).getProperties())
                .addProperties(new HQLProperties(Cidade.class, path(proxyUc.getCidadeNascimento())).getProperties())
                .addProperties(new HQLProperties(Pais.class, path(proxyUc.getPaisNascimento())).getProperties())
                .addProperties(new HQLProperties(Raca.class, path(proxyUc.getRaca())).getProperties())
                .addProperties(new HQLProperties(EtniaIndigena.class, path(proxyUc.getEtniaIndigena())).getProperties())
                .addProperties(new HQLProperties(EstadoCivil.class, path(proxyUc.getEstadoCivil())).getProperties())
                .addProperties(new HQLProperties(TabelaCbo.class, path(proxyUc.getTabelaCbo())).getProperties())
                .addProperties(new HQLProperties(Cidade.class, path(proxyUc.getMunicipioResidencia())).getProperties())
                .addProperties(new HQLProperties(EnderecoDomicilio.class, path(proxyUc.getEnderecoDomicilio())).getProperties())
                .addProperties(new HQLProperties(Escolaridade.class, path(proxyUc.getEscolaridade())).getProperties())
                .addProperties(new HQLProperties(Empresa.class, path(proxyUc.getEmpresaResponsavel())).getProperties())
                .addProperties(new HQLProperties(Usuario.class, path(proxyUc.getUsuario())).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, path(proxyUc.getResponsavelFamiliar())).getProperties())
                .setId(codigoUsuario)
                .start().getVO();

        return usuarioCadsus;
    }

}
