package br.com.celk.view.basico.empresamantenedora;

import br.com.celk.component.cepField.CepWsField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.basico.dto.CepWSDTO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMantenedora;
import br.com.ksisolucoes.vo.prontuario.hospital.TipoPrestadorIpe;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class CadastroEmpresaMantenedoraPage extends CadastroPage<EmpresaMantenedora> {

    private InputField txtReferencia;
    private DropDown<Long> dropDownTipoUnidade;
    private InputField inputFieldCodigo;
    private boolean editar;
    private DropDown<TipoPrestadorIpe> dropDownTipoPrestadorIpe;
    private CepWsField cepWsField;
    private InputField txtLogradouro;
    private InputField txtNumero;
    private InputField txtBairro;

    public CadastroEmpresaMantenedoraPage(EmpresaMantenedora object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
        if (editar) {
            inputFieldCodigo.setEnabled(false);
        }
    }

    public CadastroEmpresaMantenedoraPage(EmpresaMantenedora object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroEmpresaMantenedoraPage() {
        super();
    }

    @Override
    public void init(Form form) {
        EmpresaMantenedora proxy = on(EmpresaMantenedora.class);

        form.add(inputFieldCodigo = (InputField) new InputField<String>(path(proxy.getCodigo())).setLabel(new Model<String>(bundle("cnpj"))));
        inputFieldCodigo.setRequired(true);
//        form.add(new InputField<String>(path(proxy.getCep())));

        form.add(cepWsField = new CepWsField(path(proxy.getCep()), new PropertyModel(form.getModel(), path(proxy.getCep()))) {
            @Override
            public void load(AjaxRequestTarget target, CepWSDTO cepWSDTO) {
                txtLogradouro.setComponentValue(cepWSDTO.getLogradouroFormatado());
                txtBairro.setComponentValue(cepWSDTO.getBairro());

                target.add(txtLogradouro);
                target.add(txtBairro);


                if (StringUtils.isEmpty(cepWSDTO.getLogradouroFormatado())) {
                    target.focusComponent(txtLogradouro);
                } else if (StringUtils.isEmpty(cepWSDTO.getBairro())) {
                    target.focusComponent(txtBairro);
                } else {
                    target.focusComponent(txtNumero);
                }

            }

            @Override
            public void unload(AjaxRequestTarget target) {
                txtNumero.limpar(target);
                txtLogradouro.limpar(target);
                txtBairro.limpar(target);
                target.focusComponent(cepWsField.getField());
            }
        });


        form.add(new InputField<String>(path(proxy.getNomeMantenedora())).setRequired(true).setLabel(new Model<String>(bundle("nome"))));
        form.add(new InputField<String>(path(proxy.getCnes())));

        form.add(txtLogradouro = new InputField<String>(path(proxy.getLogradouro())));
        form.add(txtNumero = new InputField<String>(path(proxy.getNumero())));
        form.add(txtBairro = new InputField<String>(path(proxy.getBairro())));
        form.add(new InputField<String>(path(proxy.getTelefone())));
        form.add(new AutoCompleteConsultaProfissional(path(proxy.getDiretorClinico())));
        form.add(new InputField<String>(path(proxy.getOrgaoEmissor())));
    }

    public DropDown<Long> getTipoUnidade() {
        if (dropDownTipoUnidade == null) {
            dropDownTipoUnidade = new DropDown<Long>(Empresa.PROP_TIPO_UNIDADE);
            dropDownTipoUnidade.addChoice(Empresa.TIPO_ESTABELECIMENTO_EXTERNO, BundleManager.getString("estabelecimentoExterno"));
            dropDownTipoUnidade.addChoice(Empresa.TIPO_ESTABELECIMENTO_SECRETARIA_SAUDE, BundleManager.getString("secretariaSaude"));
        }
        return dropDownTipoUnidade;
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return txtReferencia;
    }

    @Override
    public Class<EmpresaMantenedora> getReferenceClass() {
        return EmpresaMantenedora.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaEmpresaMantenedoraPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroEmpresaMantenedora");
    }
}
