package br.com.celk.view.controle.importacaoFpo.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.agenda.agendamento.dialog.angendamentolote.PnlAgendamentoLote;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoLoteProcessoDTOParam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 * Created by sulivan on 11/07/19.
 */
public abstract class DlgImportarFpo extends Window {

    private PnlImportarFpo pnlImportarFpo;

    public DlgImportarFpo(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){

            @Override
            protected String load(){
                return BundleManager.getString("importarFpo");
            }
        });

        setInitialWidth(500);
        setInitialHeight(200);
        setResizable(false);

        setContent(pnlImportarFpo = new PnlImportarFpo(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target){
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
                DlgImportarFpo.this.onConfirmar(target);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void show(AjaxRequestTarget target){
        super.show(target);
    }
}
