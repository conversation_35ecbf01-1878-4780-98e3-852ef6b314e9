package br.com.celk.view.atendimento.recepcao.panel.exames.Dlg;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.factory.BOFactoryWicket;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ConsultaUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.LoadInterceptorEmpresaNaturezaTipo;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.resource.IResource;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmarMarcacaoExame extends Panel {

    private String paciente;
    private ConsultaUsuarioCadsusDTO dto;
    private TipoAtendimento tipoAtendimento;
    private NaturezaProcura naturezaProcura;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private Empresa estabelecimentoExecutante;
    private DropDown dropDownTipoAtendimento;

    private Image imagem;

    public PnlConfirmarMarcacaoExame(String id) {
        super(id);
        init();
    }

    private void init() {
        try {
            Form form = new Form("form");
            naturezaProcura = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("NatProcuraExame");

            form.add(new InputField("paciente", new PropertyModel(this, "paciente"))
                    .setEnabled(false));

            form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa("estabelecimentoExecutante", new PropertyModel(this, "estabelecimentoExecutante")));
            autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);

            autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {

                @Override
                public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                    carregarDropDownTipoAtendimento(target, object);
                }
            });

            autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {

                @Override
                public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                    dropDownTipoAtendimento.removeAllChoices(target);
                    dropDownTipoAtendimento.limpar(target);
                    dropDownTipoAtendimento.setEnabled(false);
                    target.add(dropDownTipoAtendimento);
                }
            });

            form.add(getDropDownTipoAtendimento());

            form.add(new AbstractAjaxButton("btnOk") {

                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    if (autoCompleteConsultaEmpresa.getComponentValue() == null) {
                        throw new ValidacaoException(bundle("informeEstabelecimentoExecutante"));
                    } else {
                        onOk(target);
                    }
                }
            });

            form.add(new AbstractAjaxButton("btnFechar") {

                @Override
                public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                    onFechar(target);
                }
            }.setDefaultFormProcessing(false));

            form.add(imagem = new Image("imgAvatar", ""));
            imagem.setOutputMarkupId(true);

            add(form);
        } catch (ValidacaoRuntimeException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private DropDown getDropDownTipoAtendimento() {
        if (dropDownTipoAtendimento == null) {
            dropDownTipoAtendimento = new DropDown("tipoAtendimento", new PropertyModel(this, "tipoAtendimento"));
        }

        return dropDownTipoAtendimento;
    }

    private void carregarDropDownTipoAtendimento(AjaxRequestTarget target, Empresa empresa) {
        if (target != null) {
            dropDownTipoAtendimento.removeAllChoices(target);
            dropDownTipoAtendimento.limpar(target);
        } else {
            dropDownTipoAtendimento.removeAllChoices();
        }
        dropDownTipoAtendimento.setEnabled(true);

        dropDownTipoAtendimento.addChoice(null, "");

        List<NaturezaProcuraTipoAtendimento> nptaList = LoadManager.getInstance(NaturezaProcuraTipoAtendimento.class)
                .addProperties(new HQLProperties(TipoAtendimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO).getProperties())
                .addProperties(new HQLProperties(TipoProcedimento.class, NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(NaturezaProcuraTipoAtendimento.PROP_NATUREZA_PROCURA, naturezaProcura))
                .addInterceptor(new LoadInterceptorEmpresaNaturezaTipo(empresa))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(NaturezaProcuraTipoAtendimento.PROP_TIPO_ATENDIMENTO, TipoAtendimento.PROP_DESCRICAO)))
                .start().getList();
        for (NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento : nptaList) {
            dropDownTipoAtendimento.addChoice(naturezaProcuraTipoAtendimento.getTipoAtendimento(), naturezaProcuraTipoAtendimento.getTipoAtendimento().getDescricao());
        }

        if (target != null) {
            target.add(dropDownTipoAtendimento);
        }
    }

    public abstract void onOk(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public void limpar(AjaxRequestTarget target) {
        paciente = null;
        dto = null;
        setTipoAtendimento(null);
    }

    public String getPaciente() {
        return paciente;
    }

    public ConsultaUsuarioCadsusDTO getDto() {
        return dto;
    }

    public void setDto(AjaxRequestTarget target, ConsultaUsuarioCadsusDTO dto) {
        this.dto = dto;

        target.focusComponent(autoCompleteConsultaEmpresa.getTxtDescricao().getTextField());
        if (estabelecimentoExecutante == null) {
            estabelecimentoExecutante = ApplicationSession.get().getSession().getEmpresa();
        }
        if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
            carregarDropDownTipoAtendimento(target, (Empresa) autoCompleteConsultaEmpresa.getComponentValue());
        } else {
            dropDownTipoAtendimento.setEnabled(false);
            target.add(dropDownTipoAtendimento);
        }
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public void setResourceImage(AjaxRequestTarget target, IResource resource) {
        imagem.setImageResource(resource);
        target.add(imagem);
    }

    public Empresa getEstabelecimentoExecutante() {
        return estabelecimentoExecutante;
    }

    public void setEstabelecimentoExecutante(Empresa estabelecimentoExecutante) {
        this.estabelecimentoExecutante = estabelecimentoExecutante;
    }
}
