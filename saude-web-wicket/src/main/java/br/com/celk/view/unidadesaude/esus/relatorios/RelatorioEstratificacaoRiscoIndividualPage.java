package br.com.celk.view.unidadesaude.esus.relatorios;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.bo.esus.interfaces.facade.EsusReportFacade;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.unidadesaude.esus.relatorios.RelatorioEstratificacaoRiscoIndividualDTOParam;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;

import java.util.Collections;
import java.util.List;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR> Santos
 */
@Private
public class RelatorioEstratificacaoRiscoIndividualPage extends RelatorioPage<RelatorioEstratificacaoRiscoIndividualDTOParam> {

    private DropDown<EquipeArea> dropDownArea;
    private DropDown<EquipeMicroArea> dropDownMicroArea;
    private DropDown<RelatorioEstratificacaoRiscoIndividualDTOParam.FormaApresentacao> dropDownFormaApresentacao;
    private DropDown<RelatorioEstratificacaoRiscoIndividualDTOParam.TipoRelatorio> dropDownTipoRelatorio;
    private WebMarkupContainer containerDetalhado;
    private DateChooser dataFim;
    private DateChooser dataInicio;
    private DropDown dropDownResultadoEstratificacao;
    private Form form;

    @Override
    public void init(Form<RelatorioEstratificacaoRiscoIndividualDTOParam> form) {
        this.form = form;
        RelatorioEstratificacaoRiscoIndividualDTOParam proxy = on(RelatorioEstratificacaoRiscoIndividualDTOParam.class);

        form.add(dropDownArea = new DropDown(path(proxy.getArea())));
        dropDownArea.addAjaxUpdateValue();
        dropDownArea.setOutputMarkupId(true);
        createDropDownArea();

        form.add(dropDownMicroArea = new DropDown(path(proxy.getMicroArea())));
        populaMicroArea(null);

        form.add(dropDownFormaApresentacao = DropDownUtil.getEnumDropDown(path(proxy.getFormaApresentacao()), RelatorioEstratificacaoRiscoIndividualDTOParam.FormaApresentacao.values()));
        form.add(dropDownTipoRelatorio = DropDownUtil.getEnumDropDown(path(proxy.getTipoRelatorio()), RelatorioEstratificacaoRiscoIndividualDTOParam.TipoRelatorio.values()));
        form.add(dropDownResultadoEstratificacao = DropDownUtil.getEnumDropDown(path(proxy.getResultadoEstratificacao()), RelatorioEstratificacaoRiscoIndividualDTOParam.ResultadoEstratificacao.values()));
        form.add(dataInicio = new DateChooser(path(proxy.getDataInicio())));
        form.add(dataFim = new DateChooser(path(proxy.getDataFim())));


        adicionaCheckbox(form, proxy);

        add(form);
    }

    private void adicionaCheckbox(Form<RelatorioEstratificacaoRiscoIndividualDTOParam> form, RelatorioEstratificacaoRiscoIndividualDTOParam proxy) {
        form.add(new CheckBoxLongValue(path(proxy.isFlagHipertensaoArterial())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagDiabetes())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagSaudeBucal())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagSaudeMental())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagSaudeIdosoIVCF20())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagSaudeIdosoVES13())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagGestantes())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagCriancas())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagRastreioAgrotoxicosACS())));
        form.add(new CheckBoxLongValue(path(proxy.isFlagPopulacaoCroncamExpostAgrotoxicos())));
    }

    private void createDropDownArea() {
        List<EquipeArea> areas = LoadManager.getInstance(EquipeArea.class)
                .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                .start().getList();

        dropDownArea.addChoice(null, BundleManager.getString("todos"));
        for (EquipeArea equipeArea : areas) {
            dropDownArea.addChoice(equipeArea, equipeArea.getDescricao());
        }

        dropDownArea.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                populaMicroArea(target);
            }
        });
    }

    private void populaMicroArea(AjaxRequestTarget target) {
        EquipeArea equipeArea = dropDownArea.getComponentValue();
        List<EquipeMicroArea> microAreaList = Collections.EMPTY_LIST;
        EquipeMicroArea proxy = on(EquipeMicroArea.class);
        dropDownMicroArea.removeAllChoices();
        if (target != null) {
            dropDownMicroArea.limpar(target);
        }
        dropDownMicroArea.addChoice(null, BundleManager.getString("todos"));
        if (equipeArea != null) {
            microAreaList = LoadManager.getInstance(EquipeMicroArea.class)
                    .addProperty(path(proxy.getCodigo()))
                    .addProperty(path(proxy.getMicroArea()))
                    .addProperty(path(proxy.getEquipeProfissional().getProfissional().getCodigo()))
                    .addProperty(path(proxy.getEquipeProfissional().getProfissional().getNome()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getEquipeArea()), equipeArea))
                    .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getMicroArea())))
                    .start().getList();

            if (!microAreaList.isEmpty()) {
                for (EquipeMicroArea equipeMicroArea : microAreaList) {
                    dropDownMicroArea.addChoice(equipeMicroArea, equipeMicroArea.getMicroArea().toString());
                }
            }
        }
        if (target != null) {
            target.add(dropDownMicroArea);
        }
    }

    @Override
    public Class<RelatorioEstratificacaoRiscoIndividualDTOParam> getDTOParamClass() {
        return RelatorioEstratificacaoRiscoIndividualDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioEstratificacaoRiscoIndividualDTOParam param) throws ReportException, ValidacaoException {
        validaCampos();
        return BOFactoryWicket.getBO(EsusReportFacade.class).relatorioEstratificacaoRiscoIndividual(param);
    }

    private void validaCampos() throws ValidacaoException {
        validaCamposData();
        validaCamposLinhasGuia();
    }

    private void validaCamposLinhasGuia() throws ValidacaoException {
        RelatorioEstratificacaoRiscoIndividualDTOParam param = (RelatorioEstratificacaoRiscoIndividualDTOParam) form.getModel().getObject();
        if (!param.isFlagHipertensaoArterial() && !param.isFlagCriancas() && !param.isFlagDiabetes() && !param.isFlagPopulacaoCroncamExpostAgrotoxicos()
            && !param.isFlagRastreioAgrotoxicosACS() && !param.isFlagSaudeBucal() && !param.isFlagSaudeBucal() && !param.isFlagSaudeIdosoIVCF20()
            && !param.isFlagSaudeIdosoVES13() && !param.isFlagSaudeMental() && !param.isFlagGestantes()) {
            throw new ValidacaoException(BundleManager.getString("msgNenhumCampoLinhaGuiasSelecionado"));
        }
    }

    private void validaCamposData() throws ValidacaoException {
        if (dataFim.getComponentValue() != null) {
            if (dataInicio.getComponentValue() != null) {
                if (dataFim.getComponentValue().before(dataInicio.getComponentValue())) {
                    throw new ValidacaoException(BundleManager.getString("msgDataInicialPosteriorDataFinal"));
                }
            }
        }
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("relatorioEstratificacaoRiscoIndividual");
    }
}
