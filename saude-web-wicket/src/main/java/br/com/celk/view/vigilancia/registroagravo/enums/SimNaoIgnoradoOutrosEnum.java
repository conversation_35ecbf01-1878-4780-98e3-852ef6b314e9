package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

/**
 * <AUTHOR>
 */

public enum SimNaoIgnoradoOutrosEnum implements IEnum {
    SIM(1L, "Sim"),
    NAO(2L, "Não"),
    OUTROS(3L, "Outros"),
    IGNORADO(9L, "Ignorado");

    private Long value;
    private String descricao;

    SimNaoIgnoradoOutrosEnum(Long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }

    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static SimNaoIgnoradoOutrosEnum valueOf(Long value) {
        for (SimNaoIgnoradoOutrosEnum v : SimNaoIgnoradoOutrosEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
