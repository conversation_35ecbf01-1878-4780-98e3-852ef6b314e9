package br.com.celk.view.consorcio.pedidotransferencialicitacao;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.column.ColumnFactory;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.table.pageable.PageableTable;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.consorcio.interfaces.facade.ConsorcioFacade;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.*;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;
import ch.lambdaj.Lambda;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.attributes.AjaxCallListener;
import org.apache.wicket.ajax.attributes.AjaxRequestAttributes;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.hamcrest.Matchers;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static br.com.ksisolucoes.util.CollectionUtils.isNotNullEmpty;
import static ch.lambdaj.Lambda.having;
import static ch.lambdaj.Lambda.on;
import static org.hamcrest.Matchers.equalTo;

/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroPedidoTransferenciaLicitacaoPage extends BasePage {

    private DropDown<FundoConsorcio> fundoConsorcioDropDown;
    private Form<PedidoTransferenciaLicitacao> form;
    private PageableTable<PedidoTransferenciaLicitacaoItem> tblItens;
    private CompoundPropertyModel<PedidoTransferenciaLicitacaoItem> modelItem;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private DoubleField txtQuantidade;
    private InputField txtTotalItens;
    private InputField txtTotalPrecoItens;
    private WebMarkupContainer containerItem;
    private WebMarkupContainer containerSaldo;
    private DropDown<Licitacao> dropDownLicitacoes;
    private Long totalItens;
    private Double totalPrecoItens;
    private PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao;
    private String utilizarControleFinanceiroPedidoTransferenciaConsorcio;
    private InputField txtSaldoAtual;
    private InputField txtSaldoPendente;
    private InputField txtSaldoDisponivel;
    private InputField txtUnidade;
    private Label labelSaldoAtual;
    private Label labelSaldoPendente;
    private Label labelSaldo;
    private Label labelTipoConta;
    private SubConta subContaMedicamento;
    private Conta contaPadrao;
    private Double saldoAtual;
    private Double saldoPendente;
    private Double saldoDisponivel;
    private InputField txtTipoConta;
    private String tipoConta;
    private String observacao;
    private String numeroAutorizacao;
    private TipoConta tipoContaMedicamento;
    private InputField txtDescricaoSituacaoControleFinanceiro;
    private String descricaoSituacaoControleFinanceiro;
    private DlgConfirmacaoOk dlgConfirmacaoOk;
    private AttributeModifier redTextModifier = new AttributeModifier("style", "color: red;");
    private AttributeModifier greenTextModifier = new AttributeModifier("style", "color: green;");
    private Boolean controlaSaldoPorAno;
    private WebMarkupContainer containerFundo;
    private boolean existeFundo;
    private InputField txtNumAutorizacao;
    private InputArea txaObservacao;
//    private List<PedidoTransferenciaLicitacaoItem> itens = new ArrayList<PedidoTransferenciaLicitacaoItem>();
    
    public CadastroPedidoTransferenciaLicitacaoPage() {
        init();
    }
    
    public CadastroPedidoTransferenciaLicitacaoPage(PedidoTransferenciaLicitacao pedidoTransferenciaLicitacao) {
        this.pedidoTransferenciaLicitacao = pedidoTransferenciaLicitacao;
//        initEdicao();
        init();
        dropDownLicitacoes.setEnabled(false);
    }
    
    private void init() {
        verificarExistenciaFundo();
        if(RepositoryComponentDefault.SIM.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())){
            info(bundle("msgInformacaoSaldoDisponivelConsorciadoPedidoLicitacao"));
        }

        getForm().add(getDropDownLicitacoes());
        getForm().add(new DisabledInputField(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_EMPRESA_ALMOXARIFADO, Empresa.PROP_DESCRICAO)));
        getForm().add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO)).setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO)).setValidarTipoEstabelecimento(true));

        // SALDO PARA SUBCONTA DE MEDICAMENTO
        containerSaldo = new WebMarkupContainer("containerSaldo");
        containerFundo = new WebMarkupContainer("containerFundo");
        containerFundo.add(getDropDownFundoConsorcio());
        form.add(containerFundo);
        ajustarFundo();
        containerSaldo.setOutputMarkupId(true);

        containerSaldo.add(labelSaldoAtual = new Label("labelSaldoAtual", new Model(bundle("saldoAtual"))));
        containerSaldo.add(labelSaldoPendente = new Label("labelSaldoPendente", new Model(bundle("saldoPendente"))));
        containerSaldo.add(labelSaldo = new Label("labelSaldo", new Model(bundle("saldoDisponivel"))));
        containerSaldo.add(txtSaldoAtual = new DisabledDoubleField("saldoAtual", new PropertyModel<Double>(this, "saldoAtual")).setNegativeValue(true));
        containerSaldo.add(txtSaldoPendente = new DisabledDoubleField("saldoPendente", new PropertyModel<Double>(this, "saldoPendente")).setNegativeValue(true));
        containerSaldo.add(txtSaldoDisponivel = new DisabledDoubleField("saldoDisponivel", new PropertyModel<Double>(this, "saldoDisponivel")).setNegativeValue(true));
        containerSaldo.add(labelTipoConta = new Label("labelTipoConta", new Model(bundle("tipoConta"))));
        containerSaldo.add(txtTipoConta = new DisabledInputField("tipoConta", new PropertyModel<String>(this, "tipoConta")));
        containerSaldo.add(txtNumAutorizacao = new InputField("numeroAutorizacao"));

        containerSaldo.add(new AbstractAjaxButton("btnAtualizarSaldo") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                calcularSaldoDisponivel();
                target.add(txtSaldoAtual);
                target.add(txtSaldoPendente);
                target.add(txtSaldoDisponivel);
            }

            @Override
            protected void updateAjaxAttributes(AjaxRequestAttributes attributes) {
                super.updateAjaxAttributes(attributes);
                attributes.getAjaxCallListeners().add(new AjaxCallListener() {

                    @Override
                    public CharSequence getBeforeHandler(Component component) {
                        return " $('#" + txtSaldoDisponivel.getMarkupId() + "').addClass('loading');";
                    }

                    @Override
                    public CharSequence getSuccessHandler(Component component) {
                        return " $('#" + txtSaldoDisponivel.getMarkupId() + "').removeClass('loading');";
                    }
                });
            }
        }.setDefaultFormProcessing(false));
        containerSaldo.add(txtDescricaoSituacaoControleFinanceiro = new DisabledInputField("descricaoSituacaoControleFinanceiro", new PropertyModel<Double>(this, "descricaoSituacaoControleFinanceiro")));
        txtDescricaoSituacaoControleFinanceiro.addAjaxUpdateValue();
        atualizarDescricaoSituacaoControleFinanceiro(null);

        getForm().add(containerSaldo);
        
        containerItem = new WebMarkupContainer("containerItem", modelItem = new CompoundPropertyModel(new PedidoTransferenciaLicitacaoItem()));
        containerItem.setOutputMarkupId(true);
        
        containerItem.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO)) {
            
            @Override
            public String[] getPropertiesLoad() {
                return VOUtils.mergeProperties(new HQLProperties(Produto.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(Produto.PROP_UNIDADE, Unidade.PROP_CODIGO),
                            VOUtils.montarPath(Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),}
                );
            }
            
        });
        autoCompleteConsultaProduto.setIncluirInativos(false);
        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                carregarUnidade(target,object);
                if(RepositoryComponentDefault.SIM.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())){
                    if (!configurarSubConta(target, object)) {
                        getForm().setEnabled(false);
                        target.add(getForm());
                    } else {
                        calcularSaldoDisponivel();
                        target.add(txtSaldoAtual);
                        target.add(txtSaldoPendente);
                        target.add(txtSaldoDisponivel);
                    }

                } else {
                    containerSaldo.setVisible(false);
                }
            }
        });

        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                txtUnidade.setComponentValue(null);
                target.add(txtUnidade);
                if(RepositoryComponentDefault.SIM.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())){
                    if (!configurarSubConta(target, null)) {
                        getForm().setEnabled(false);
                        target.add(getForm());
                    } else {
                        calcularSaldoDisponivel();
                        target.add(txtSaldoAtual);
                        target.add(txtSaldoPendente);
                        target.add(txtSaldoDisponivel);
                    }

                } else {
                    containerSaldo.setVisible(false);
                }
            }
        });

        containerItem.add(txtUnidade = new InputField(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO,Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        txtUnidade.setEnabled(false);
        containerItem.add(txtQuantidade = new DoubleField(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)).setMDec(0));
        containerItem.add(new AbstractAjaxButton("btnAdicionar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarItem(target);
            }
        });
        
        containerItem.add(tblItens = new PageableTable("tblItens", getColumns(), getPagerProviderInstance()));
        tblItens.getDataProvider().setParameters(getParameters());
        tblItens.populate();
        containerItem.add(txtTotalItens = new DisabledInputField("totalItens", new PropertyModel<>(this, "totalItens")));
        
        containerItem.add(txtTotalPrecoItens = new DisabledInputField("totalPrecoItens", new PropertyModel<>(this, "totalPrecoItens")));

        getForm().add(txaObservacao = new InputArea("observacao"));

        getForm().add(containerItem);

        add(getForm());
        add(new VoltarButton("btnVoltar"));

        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {
            
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                pedidoTransferenciaLicitacao.setEmpresaConsorciado(object);
                autoCompleteConsultaEmpresa.setEnabled(object == null);
                habilitarContainerItem();
                dropDownLicitacoes.setEnabled(object == null || pedidoTransferenciaLicitacao.getLicitacao() == null);
                target.add(autoCompleteConsultaEmpresa);
                target.add(containerItem);
                target.add(dropDownLicitacoes);
                if (pedidoTransferenciaLicitacao.getLicitacao() != null) {
                    autoCompleteConsultaProduto.focus(target);
                } else {
                    target.focusComponent(dropDownLicitacoes);
                }

                if(RepositoryComponentDefault.SIM.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())){
                    if (pedidoTransferenciaLicitacao.getLicitacao() == null) {
                        warn(target, BundleManager.getString("msgDeveSerSelecionadoUmaLicitacao"));
                        return;
                    }
                    if (!configurarSubConta(target, null)) {
                        getForm().setEnabled(false);
                        target.add(getForm());
                    } else {
                        calcularSaldoDisponivel();
                        target.add(txtSaldoAtual);
                        target.add(txtSaldoPendente);
                        target.add(txtSaldoDisponivel);
                    }
                } else {
                    containerSaldo.setVisible(false);
                }
            }
        });
        
        Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();
        Empresa empresaLogada = ApplicationSession.get().getSessaoAplicacao().getEmpresa();
        
        if (pedidoTransferenciaLicitacao.getCodigo() != null || (!usuarioLogado.isNivelAdminOrMaster() && empresaLogada.getTipoUnidade().equals(Empresa.TIPO_ESTABELECIMENTO_CONSORCIADO))) {
            autoCompleteConsultaEmpresa.setEnabled(false);
        }
        
        try {
            boolean valido = true;
            
            Empresa empresaAlmoxarifado = (Empresa) BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("almoxarifadoPadrao");
            
            autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO)
                    .setEmpresas(Arrays.asList(empresaAlmoxarifado));
            
            pedidoTransferenciaLicitacao.setEmpresaAlmoxarifado(empresaAlmoxarifado);
            
            if (pedidoTransferenciaLicitacao.getEmpresaAlmoxarifado() == null) {
                warn(BundleManager.getString("parametroXNaoEstaDefinido", "almoxarifadoPadrao"));
                valido = false;
            }
            
            if (!valido) {
                getForm().setEnabled(false);
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        habilitarContainerItem();
        getAtualizarTotais();

        if(RepositoryComponentDefault.SIM.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())){
            if (!configurarSubConta(null, null)) {
                getForm().setEnabled(false);
            } else {
                calcularSaldoDisponivel();
            }
        } else {
            containerSaldo.setVisible(false);
        }
    }

    private void verificarExistenciaFundo() {
        existeFundo = CollectionUtils.isNotNullEmpty(LoadManager.getInstance(FundoConsorcio.class).addProperty(FundoConsorcio.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(FundoConsorcio.PROP_SITUACAO, FundoConsorcio.Situacao.ATIVO.value())).start().getList());

    }

    private void ajustarFundo() {
        containerFundo.setVisible(existeFundo);
        fundoConsorcioDropDown.setEnabled(existeFundo);
        if (existeFundo) {
            fundoConsorcioDropDown.addRequiredClass();
        }
    }

    private void carregarUnidade(AjaxRequestTarget target,Produto object) {
       List<Produto> produtos = LoadManager.getInstance(Produto.class).addParameter(new QueryCustom.QueryCustomParameter(Produto.PROP_CODIGO,object.getCodigo()))
               .addProperties(new HQLProperties(Unidade.class, VOUtils.montarPath(Produto.PROP_UNIDADE)).getProperties()).start().getList();
       txtUnidade.setComponentValue(produtos.get(0).getUnidade().getDescricaoComSigla());
       target.add(txtUnidade);
    }

    private void validarLicitacaoComVinculoUsuario(AjaxRequestTarget target){
        if (!Empresa.TIPO_ESTABELECIMENTO_CONSORCIO.equals(SessaoAplicacaoImp.getInstance().getEmpresa().getTipoUnidade())) {
            List<LicitacaoUsuarios> licitacaoUsuariosList = LoadManager.getInstance(LicitacaoUsuarios.class)
                    .addProperty(LicitacaoUsuarios.PROP_CODIGO)
                    .addProperty(VOUtils.montarPath(LicitacaoUsuarios.PROP_USUARIO, Usuario.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(LicitacaoUsuarios.PROP_USUARIO, Usuario.PROP_NOME))
                    .addParameter(new QueryCustom.QueryCustomParameter(LicitacaoUsuarios.PROP_LICITACAO, dropDownLicitacoes.getComponentValue()))
                    .start().getList();

            Usuario usuario = ApplicationSession.get().getSession().getUsuario();
            if (isNotNullEmpty(licitacaoUsuariosList) && !Lambda.exists(licitacaoUsuariosList, Lambda.having(Lambda.on(LicitacaoUsuarios.class).getUsuario(), Matchers.equalTo(usuario)))) {
                MessageUtil.warn(target, dropDownLicitacoes, BundleManager.getString("msgUsuarioNaoPossuiAcessoLicitacao"));
                autoCompleteConsultaEmpresa.limpar(target);
                dropDownLicitacoes.limpar(target);
                target.add(dropDownLicitacoes);
            }
        }
    }

    private Form<PedidoTransferenciaLicitacao> getForm() {
        if (pedidoTransferenciaLicitacao == null) {
            pedidoTransferenciaLicitacao = new PedidoTransferenciaLicitacao();
            descricaoSituacaoControleFinanceiro = PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.PENDENTE.descricao();
        } else {
            descricaoSituacaoControleFinanceiro = pedidoTransferenciaLicitacao.getDescricaoSituacaoControleFinanceiro();
        }
        if (form == null) {
            form = new Form("form", new CompoundPropertyModel(pedidoTransferenciaLicitacao));
        }
        return form;
    }

    private boolean configurarSubConta(AjaxRequestTarget target, Produto produto){
        boolean valido = true;
        tipoConta = null;
//        try {
//            if(pedidoTransferenciaLicitacao == null || pedidoTransferenciaLicitacao.getCodigo() == null){
//                tipoContaMedicamento = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("tipoContaMedicamento");
//            } else {
//                tipoContaMedicamento = pedidoTransferenciaLicitacao.getSubContaMedicamento().getTipoConta();
//            }


            if (pedidoTransferenciaLicitacao != null && pedidoTransferenciaLicitacao.getLicitacao() != null){
                if(pedidoTransferenciaLicitacao.getCodigo() != null){
                    PedidoTransferenciaLicitacao ptl = LoadManager.getInstance(PedidoTransferenciaLicitacao.class)
                            .addProperty(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_LICITACAO, Licitacao.PROP_TIPO_CONTA, TipoConta.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_LICITACAO, Licitacao.PROP_TIPO_CONTA, TipoConta.PROP_DESCRICAO))
                            .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacao.PROP_CODIGO, pedidoTransferenciaLicitacao.getCodigo()))
                            .start().getVO();

                    if(ptl != null){
                        tipoContaMedicamento = ptl.getLicitacao().getTipoConta();
                    }
                } else {
                    tipoContaMedicamento = pedidoTransferenciaLicitacao.getLicitacao().getTipoConta();
                }
            }

            if (pedidoTransferenciaLicitacao.getLicitacao() != null && tipoContaMedicamento == null) {
                if(target != null){
                    warn(target, BundleManager.getString("msgDeveSerConfiguradoTipoContaLicitacao"));
                } else {
                    warn(BundleManager.getString("msgDeveSerConfiguradoTipoContaLicitacao"));
                }
                valido = false;
            }

            if(pedidoTransferenciaLicitacao != null && pedidoTransferenciaLicitacao.getEmpresaConsorciado() != null){

                if(pedidoTransferenciaLicitacao.getCodigo() != null){
                    PedidoTransferenciaLicitacao ptl = LoadManager.getInstance(PedidoTransferenciaLicitacao.class)
                            .addProperty(PedidoTransferenciaLicitacao.PROP_CODIGO)
                            .addProperty(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_CONTA_PADRAO, Conta.PROP_CODIGO))
                            .addProperty(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_CONTA_PADRAO, Conta.PROP_DESCRICAO))
                            .setId(pedidoTransferenciaLicitacao.getCodigo())
                            .start().getVO();

                    if(ptl != null && ptl.getCodigo() != null){
                        contaPadrao = ptl.getEmpresaConsorciado().getContaPadrao();
                    }
                } else {
                        contaPadrao = pedidoTransferenciaLicitacao.getEmpresaConsorciado().getContaPadrao();
                }

                if (contaPadrao == null) {
                    if(target != null) {
                        warn(target, BundleManager.getString("consorciadoNaoPossuiContaPadraoDefinida"));
                    } else {
                        warn(BundleManager.getString("consorciadoNaoPossuiContaPadraoDefinida"));
                    }
                    valido = false;
                } else {
                    contaPadrao = LoadManager.getInstance(Conta.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(Conta.PROP_CODIGO, contaPadrao.getCodigo()))
                            .start().getVO();

                    if (Conta.StatusConta.INATIVO.value().equals(contaPadrao.getStatus())) {
                        if(target != null) {
                            warn(target, BundleManager.getString("contaPadraoDefinidaParaConsorciadoEstaInativa"));
                        } else {
                            warn(BundleManager.getString("contaPadraoDefinidaParaConsorciadoEstaInativa"));
                        }
                        valido = false;
                    }
                }

                if (valido) {
                    if(produto != null){
//                        verificarConfiguracaoContaPedidoTransferenciaLicitacao(target, produto);
                    }

                    tipoConta = tipoContaMedicamento.getDescricao();
                    if(target != null) {
                        target.add(txtTipoConta);
                    }

                    subContaMedicamento = LoadManager.getInstance(SubConta.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubConta.PROP_TIPO_CONTA), tipoContaMedicamento))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubConta.PROP_CONTA), contaPadrao))
                            .start().getVO();

                    if (subContaMedicamento == null) {
                        if(target != null) {
                            warn(target, BundleManager.getString("naoExisteSubContaComTipoContaNaConta", tipoContaMedicamento.getDescricao(), contaPadrao.getDescricao()));
                        } else {
                            warn(BundleManager.getString("naoExisteSubContaComTipoContaNaConta", tipoContaMedicamento.getDescricao(), contaPadrao.getDescricao()));
                        }
                        valido = false;
                    }
                }
            }
//        } catch (DAOException ex) {
//            valido = false;
//            Loggable.log.error(ex.getMessage(), ex);
//        }
//        catch (ValidacaoException ex) {
//            valido = false;
//            Loggable.log.error(ex.getMessage(), ex);
//        }
        return valido;
    }

    private void habilitarContainerItem() {
        containerItem.setEnabled(autoCompleteConsultaEmpresa.getModelObject() != null && dropDownLicitacoes.getComponentValue() != null);
    }
    
    private DropDown getDropDownLicitacoes() {
        if (dropDownLicitacoes == null) {
            dropDownLicitacoes = new DropDown<>(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_LICITACAO));
            List<Licitacao> licitacoes = LoadManager.getInstance(Licitacao.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(Licitacao.PROP_STATUS, QueryCustom.QueryCustomParameter.NOT_IN, Arrays.asList(Licitacao.StatusLicitacao.CANCELADA.value(), Licitacao.StatusLicitacao.FECHADA.value())))
                    .addSorter(new QueryCustom.QueryCustomSorter(Licitacao.PROP_CODIGO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                    .start().getList();
            
            dropDownLicitacoes.addChoice(null, BundleManager.getString("selecione"));
            
            for (Licitacao licitacao : licitacoes) {
                dropDownLicitacoes.addChoice(licitacao, BundleManager.getString("licitacaoXpregaoY", licitacao.getCodigo(), licitacao.getNumeroPregao() == null ? "S/N" : licitacao.getNumeroPregao()));
            }
            
            dropDownLicitacoes.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    habilitarContainerItem();
                    dropDownLicitacoes.setEnabled(autoCompleteConsultaEmpresa.getModelObject() == null);
                    target.add(containerItem);
                    target.add(dropDownLicitacoes);
                    if (autoCompleteConsultaEmpresa.getModelObject() == null) {
                        autoCompleteConsultaEmpresa.focus(target);
                    } else {
                        autoCompleteConsultaProduto.focus(target);
                    }

                    clearNotifications(target);
                    validarLicitacaoComVinculoUsuario(target);

                    if(autoCompleteConsultaEmpresa != null && autoCompleteConsultaEmpresa.getComponentValue() != null) {
                        if (RepositoryComponentDefault.SIM.equals(getUtilizarControleFinanceiroPedidoTransferenciaConsorcio())) {
                            if (!configurarSubConta(target, null)) {
                                getForm().setEnabled(false);
                                target.add(getForm());
                            } else {
                                calcularSaldoDisponivel();
                                target.add(txtSaldoAtual);
                                target.add(txtSaldoPendente);
                                target.add(txtSaldoDisponivel);
                            }

                        } else {
                            containerSaldo.setVisible(false);
                        }
                    }
                }
            });
        }
        return dropDownLicitacoes;
    }

    private DropDown getDropDownFundoConsorcio() {
        if (fundoConsorcioDropDown == null) {
            List<FundoConsorcio> fundoConsorcios = new ArrayList<>();
            fundoConsorcioDropDown = new DropDown<FundoConsorcio>("fundoConsorcio");
            if (existeFundo) {
                fundoConsorcios = LoadManager.getInstance(FundoConsorcio.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(FundoConsorcio.PROP_SITUACAO, FundoConsorcio.Situacao.ATIVO.value()))
                        .addSorter(new QueryCustom.QueryCustomSorter(FundoConsorcio.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE_NULLS_LAST))
                        .start().getList();
            }
            fundoConsorcioDropDown.addChoice(null, "");
            if (!existeFundo || CollectionUtils.isNotNullEmpty(fundoConsorcios)) {
                for (FundoConsorcio fundoConsorcio : fundoConsorcios) {
                    fundoConsorcioDropDown.addChoice(fundoConsorcio, fundoConsorcio.getDescricao());
                }
                fundoConsorcioDropDown.addAjaxUpdateValue();
            }
        }

        return fundoConsorcioDropDown;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        
        ColumnFactory columnFactory = new ColumnFactory(PedidoTransferenciaLicitacaoItem.class);
        
        columns.add(getCustomColumn());
        columns.add(columnFactory.createColumn(BundleManager.getString("produto"), VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO_FORMATADO)));
        columns.add(columnFactory.createColumn(BundleManager.getString("un"), VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE)));
        columns.add(columnFactory.createColumn(BundleManager.getString("quantidade"), VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)));
        columns.add(new DoubleColumn(BundleManager.getString("vlrUnitario"), VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)).setCasasDecimais(4));
        columns.add(columnFactory.createColumn(BundleManager.getString("total"), VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_TOTAL_ITEM)));
        
        return columns;
    }
    
    private CustomColumn getCustomColumn() {
        return new CustomColumn<PedidoTransferenciaLicitacaoItem>() {
            
            @Override
            public Component getComponent(String componentId, final PedidoTransferenciaLicitacaoItem rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerItem(target, rowObject);
                    }
                    
                };
            }
        };
    }

    private void getAtualizarTotais() {
        if (pedidoTransferenciaLicitacao.getCodigo() != null) {
            List<PedidoTransferenciaLicitacaoItem> itemList = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE_ENVIADA)
                    .addProperty(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, pedidoTransferenciaLicitacao))
                    .start().getList();
            
            if(isNotNullEmpty(itemList)){
                totalItens = new Long(itemList.size());
                totalPrecoItens = Lambda.sum(itemList, on(PedidoTransferenciaLicitacaoItem.class).getTotalItem());
            } else {
                totalItens = 0L;
                totalPrecoItens = 0D;
            }
        } else {
            totalItens = 0L;
            totalPrecoItens = 0D;
        }
    }
    
    private void adicionarItem(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (existeFundo) {
            if (getForm().getModelObject().getFundoConsorcio() != null) {
                pedidoTransferenciaLicitacao.setSubContaMedicamento(subContaMedicamento);

                this.pedidoTransferenciaLicitacao = BOFactoryWicket.getBO(ConsorcioFacade.class)
                        .cadastrarPedidoTransferenciaLicitacaoItem(pedidoTransferenciaLicitacao.getLicitacao().getCodigo(), pedidoTransferenciaLicitacao, modelItem.getObject());

                limparItem(target);
                tblItens.getDataProvider().setParameters(getParameters());
                tblItens.update(target);
                autoCompleteConsultaProduto.focus(target);

                atualizarDescricaoSituacaoControleFinanceiro(target);

                getAtualizarTotais();
                target.add(txtTotalItens);
                target.add(txtTotalPrecoItens);

                calcularSaldoDisponivel();
                target.add(txtSaldoAtual);
                target.add(txtSaldoPendente);
                target.add(txtSaldoDisponivel);
            } else {
                error(target, Bundle.getStringApplication("msg_campo_X_obrigatorio", "Fundo"));
            }
        } else {
            pedidoTransferenciaLicitacao.setSubContaMedicamento(subContaMedicamento);
            if (pedidoTransferenciaLicitacao.getLicitacao() == null) {
                List<PedidoTransferenciaLicitacao> pedidoTransferenciaLicitacaoList = LoadManager.getInstance(PedidoTransferenciaLicitacao.class)
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacao.PROP_CODIGO, pedidoTransferenciaLicitacao.getCodigo()))
                        .addProperties(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_LICITACAO))
                        .addProperties(VOUtils.montarPath(PedidoTransferenciaLicitacao.PROP_LICITACAO, Licitacao.PROP_EMPRESA))
                        .addProperties(PedidoTransferenciaLicitacao.PROP_CODIGO)
                        .start().getList();
                if (CollectionUtils.isNotNullEmpty(pedidoTransferenciaLicitacaoList)) {
                    pedidoTransferenciaLicitacao.setLicitacao(pedidoTransferenciaLicitacaoList.get(0).getLicitacao());
                }
            }

            this.pedidoTransferenciaLicitacao = BOFactoryWicket.getBO(ConsorcioFacade.class)
                    .cadastrarPedidoTransferenciaLicitacaoItem(pedidoTransferenciaLicitacao.getLicitacao().getCodigo(), pedidoTransferenciaLicitacao, modelItem.getObject());

            limparItem(target);
            tblItens.getDataProvider().setParameters(getParameters());
            tblItens.update(target);
            autoCompleteConsultaProduto.focus(target);

            atualizarDescricaoSituacaoControleFinanceiro(target);

            getAtualizarTotais();
            target.add(txtTotalItens);
            target.add(txtTotalPrecoItens);

            calcularSaldoDisponivel();
            target.add(txtSaldoAtual);
            target.add(txtSaldoPendente);
            target.add(txtSaldoDisponivel);
        }
    }

    private void atualizarDescricaoSituacaoControleFinanceiro(AjaxRequestTarget target){
        descricaoSituacaoControleFinanceiro = pedidoTransferenciaLicitacao.getDescricaoSituacaoControleFinanceiro();

        if(PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.PENDENTE.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro())
                || PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.NAO_APROVADO.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro())){
            if (txtDescricaoSituacaoControleFinanceiro.getBehaviors().contains(greenTextModifier)) {
                txtDescricaoSituacaoControleFinanceiro.remove(greenTextModifier);
            }
            txtDescricaoSituacaoControleFinanceiro.add(redTextModifier);
        } else if(PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.APROVADO.value().equals(pedidoTransferenciaLicitacao.getSituacaoControleFinanceiro())){
            if (txtDescricaoSituacaoControleFinanceiro.getBehaviors().contains(redTextModifier)) {
                txtDescricaoSituacaoControleFinanceiro.remove(redTextModifier);
            }
            txtDescricaoSituacaoControleFinanceiro.add(greenTextModifier);
        }
        if(target != null ){
            target.add(txtDescricaoSituacaoControleFinanceiro);
        }
    }
    
    private void removerItem(AjaxRequestTarget target, PedidoTransferenciaLicitacaoItem rowObject) throws DAOException, ValidacaoException {
        if(totalItens == 1L){
            throw new ValidacaoException(bundle("msg_pedido_deve_possuir_pelo_menos_item_cadastrado_favor_cadastrar_novo_item_para_remover_cancele_pedido_tela_consulta"));
        }

        pedidoTransferenciaLicitacao = BOFactoryWicket.getBO(ConsorcioFacade.class).removerPedidoTransferenciaLicitacaoItem(rowObject);

//        for (int i = 0; i < itens.size(); i++) {
//            if (itens.get(i) == rowObject) {
//                itens.remove(i);
//            }
//        }
        
        tblItens.getDataProvider().setParameters(getParameters());
        tblItens.update(target);
        atualizarDescricaoSituacaoControleFinanceiro(target);
        
        getAtualizarTotais();
        target.add(txtTotalItens);
        target.add(txtTotalPrecoItens);

        calcularSaldoDisponivel();
        target.add(txtSaldoAtual);
        target.add(txtSaldoPendente);
        target.add(txtSaldoDisponivel);
    }
    
    private void limparItem(AjaxRequestTarget target) {
        modelItem.setObject(new PedidoTransferenciaLicitacaoItem());
        autoCompleteConsultaProduto.limpar(target);
        txtQuantidade.limpar(target);
    }
    
//    private void salvar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//        if (itens.isEmpty()) {
//            throw new ValidacaoException(BundleManager.getString("informePeloMenosUmItem"));
//        }
//        this.pedidoTransferenciaLicitacao = BOFactoryWicket.getBO(ConsorcioFacade.class).cadastrarPedidoTransferenciaLicitacao(pedidoTransferenciaLicitacao.getLicitacao().getCodigo(), pedidoTransferenciaLicitacao, itens);
//        Page page = new ConsultaPedidoTransferenciaLicitacaoPage();
//        setResponsePage(page);
//        getSession().getFeedbackMessages().info(page, BundleManager.getString("registroSalvoSucessoCodigoX", pedidoTransferenciaLicitacao.getCodigo()));
//    }
    
//    private void initEdicao() {
//        if (pedidoTransferenciaLicitacao.getCodigo() != null) {
//            itens = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
//                    .addProperties(new HQLProperties(PedidoTransferenciaLicitacaoItem.class).getProperties())
//                    .addProperty(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO))
//                    .addProperty(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE))
//                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
//                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, pedidoTransferenciaLicitacao))
//                    .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO, Produto.PROP_DESCRICAO)))
//                    .start().getList();
//        }
//    }
//    
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            
            @Override
            public Class getClassConsulta() {
                return PedidoTransferenciaLicitacaoItem.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(PedidoTransferenciaLicitacaoItem.class).getProperties(),
                        new String[]{
                            VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_CODIGO),
                            VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PRODUTO, Produto.PROP_UNIDADE, Unidade.PROP_UNIDADE),
                            VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_SUB_CONTA_MEDICAMENTO, SubConta.PROP_CODIGO),
                                VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_CODIGO),
                            VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_CONTA_PADRAO, Conta.PROP_CODIGO),
                            VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO, Empresa.PROP_CONTA_PADRAO, Conta.PROP_DESCRICAO),
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(PedidoTransferenciaLicitacaoItem.PROP_CODIGO, false);
            }
        };
    }
    
    private List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();
        
        parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()));
        if(pedidoTransferenciaLicitacao.getCodigo() != null){
            parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, pedidoTransferenciaLicitacao));            
        } else {
            parameters.add(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, BuilderQueryCustom.QueryParameter.IS_NULL));            
        }

        return parameters;
    }
    
    public void validarItemCancelamentoLicitacao(Licitacao licitacao, Produto produto) throws ValidacaoException {
        Long result = null;
        try {
            result = BOFactoryWicket.getBO(ConsorcioFacade.class).consultaProdutoLicitacaoPorStatus(licitacao, produto, LicitacaoItem.StatusLicitacaoItem.CANCELADO.value());
        } catch (ValidacaoException | DAOException ex) {
            Logger.getLogger(CadastroPedidoTransferenciaLicitacaoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
        if (result >= 1) {
            throw new ValidacaoException(BundleManager.getString("msgItemCanceladoLicitacao"));
        }
    }
    
    public void validarProdutoLicitaco(Licitacao licitacao, Produto produto) throws ValidacaoException {
        Long result = null;
        try {
            result = BOFactoryWicket.getBO(ConsorcioFacade.class).consultaProdutoLicitacao(licitacao, produto);
        } catch (ValidacaoException | DAOException ex) {
            Logger.getLogger(CadastroPedidoTransferenciaLicitacaoPage.class.getName()).log(Level.SEVERE, null, ex);
        }
        if (result == 0) {
            throw new ValidacaoException(BundleManager.getString("msgProdutoSemLicitacao"));
        }
    }
    
    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroPedidoTransferencia");
    }
    
    @Override
    public FormComponent getComponentRequestFocus() {
        return dropDownLicitacoes;
    }

    private void calcularSaldoDisponivel() {
        if (controlaSaldoPorAno()) {
            calcularSaldoDisponivelPorSubContaAno();
        } else {
            calcularSaldoDisponivelPorSubConta();
        }
    }

    private boolean controlaSaldoPorAno() {
        if (controlaSaldoPorAno == null) {
            controlaSaldoPorAno = false;
            try {
                controlaSaldoPorAno = RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("controlaSaldoPorAno"));
            } catch (DAOException e) {
                Loggable.log.error(e.getMessage());
            }
        }
        return controlaSaldoPorAno;
    }

    private void calcularSaldoDisponivelPorSubContaAno() {
        if (tipoContaMedicamento != null && contaPadrao != null) {
            SubContaAno subContaAno = carregarSubContaAno(tipoContaMedicamento, contaPadrao);
            if (subContaAno == null) {
                subContaMedicamento = carregarSubConta(tipoContaMedicamento, contaPadrao);
                saldoDisponivel = 0D;
            } else {
                subContaMedicamento = subContaAno.getSubConta();
                saldoDisponivel = new Dinheiro(subContaAno.getSaldoAtual()).subtrair(subContaAno.getValorReservado()).doubleValue();
            }

            if(pedidoTransferenciaLicitacao != null && pedidoTransferenciaLicitacao.getEmpresaConsorciado() != null && pedidoTransferenciaLicitacao.getEmpresaConsorciado().getCodigo() != null){
                List<PedidoTransferenciaLicitacaoItem> itemList = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO), pedidoTransferenciaLicitacao.getEmpresaConsorciado()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_SITUACAO_CONTROLE_FINANCEIRO), PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.PENDENTE.value()))
                        .start().getList();

                if(CollectionUtils.isNotNullEmpty(itemList)){
                    Double totalItens = Lambda.sum(itemList, on(PedidoTransferenciaLicitacaoItem.class).getTotalItem());
                    saldoDisponivel = new Dinheiro(saldoDisponivel).subtrair(Coalesce.asDouble(totalItens)).doubleValue();
                }
            }
        }
    }

    private void calcularSaldoDisponivelPorSubConta() {
        if (tipoContaMedicamento != null && contaPadrao != null) {
            subContaMedicamento = carregarSubConta(tipoContaMedicamento, contaPadrao);
            saldoPendente = 0D;
            saldoAtual = new Dinheiro(subContaMedicamento.getSaldoAtual()).subtrair(subContaMedicamento.getValorReservado()).doubleValue();

            if(pedidoTransferenciaLicitacao != null && pedidoTransferenciaLicitacao.getEmpresaConsorciado() != null && pedidoTransferenciaLicitacao.getEmpresaConsorciado().getCodigo() != null){
                List<PedidoTransferenciaLicitacaoItem> itemList = LoadManager.getInstance(PedidoTransferenciaLicitacaoItem.class)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_QUANTIDADE)
                        .addProperty(PedidoTransferenciaLicitacaoItem.PROP_PRECO_UNITARIO)
                        .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferenciaLicitacaoItem.PROP_STATUS, PedidoTransferenciaLicitacaoItem.StatusPedidoTransferenciaLicitacaoItem.ABERTO.value()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_EMPRESA_CONSORCIADO), pedidoTransferenciaLicitacao.getEmpresaConsorciado()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_SUB_CONTA_MEDICAMENTO), subContaMedicamento))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaLicitacaoItem.PROP_PEDIDO_TRANSFERENCIA_LICITACAO, PedidoTransferenciaLicitacao.PROP_SITUACAO_CONTROLE_FINANCEIRO), PedidoTransferenciaLicitacao.SituacaoControleFinanceiro.PENDENTE.value()))
                        .start().getList();

                if(CollectionUtils.isNotNullEmpty(itemList)){
                    saldoPendente = Lambda.sum(itemList, on(PedidoTransferenciaLicitacaoItem.class).getTotalItem());
                }
            }
            saldoDisponivel = new Dinheiro(saldoAtual).subtrair(Coalesce.asDouble(saldoPendente)).doubleValue();
        }
    }

    private SubConta carregarSubConta(TipoConta tipoConta, Conta contaPadrao) {
        return LoadManager.getInstance(SubConta.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubConta.PROP_TIPO_CONTA), tipoConta))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubConta.PROP_CONTA), contaPadrao))
                .start().getVO();
    }

    private SubContaAno carregarSubContaAno(TipoConta tipoConta, Conta contaPadrao) {
        Long ano = Coalesce.asLong(pedidoTransferenciaLicitacao.getAnoCadastro(), (long) DataUtil.getAno());
        return LoadManager.getInstance(SubContaAno.class)
                .addProperties(new HQLProperties(SubContaAno.class).getProperties())
                .addProperties(new HQLProperties(SubConta.class, SubContaAno.PROP_SUB_CONTA).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(SubContaAno.PROP_ANO, ano))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubContaAno.PROP_SUB_CONTA, SubConta.PROP_TIPO_CONTA), tipoConta))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubContaAno.PROP_SUB_CONTA, SubConta.PROP_CONTA), contaPadrao))
                .setMaxResults(1).start().getVO();
    }

    public String getUtilizarControleFinanceiroPedidoTransferenciaConsorcio() {
        if (utilizarControleFinanceiroPedidoTransferenciaConsorcio == null) {
            try {
                utilizarControleFinanceiroPedidoTransferenciaConsorcio = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.CONSORCIO).getParametro("utilizarControleFinanceiroPedidoTransferenciaConsorcio");
            } catch (DAOException ex) {
                Logger.getLogger(CadastroPedidoTransferenciaLicitacaoPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        return utilizarControleFinanceiroPedidoTransferenciaConsorcio;
    }

    private void verificarConfiguracaoContaPedidoTransferenciaLicitacao(AjaxRequestTarget target, Produto produto) throws ValidacaoException {
        ConfiguracaoContaPedidoTransferenciaLicitacao proxy = on(ConfiguracaoContaPedidoTransferenciaLicitacao.class);

        List<ConfiguracaoContaPedidoTransferenciaLicitacao> configuracaoList = LoadManager.getInstance(ConfiguracaoContaPedidoTransferenciaLicitacao.class)
                .addProperty(path(proxy.getGrupo().getCodigo()))
                .addProperty(path(proxy.getSubGrupo().getId().getCodigo()))
                .addProperty(path(proxy.getSubGrupo().getId().getCodigoGrupoProduto()))
                .addProperty(path(proxy.getProduto().getCodigo()))
                .addProperty(path(proxy.getTipoConta().getCodigo()))
                .addProperty(path(proxy.getTipoConta().getDescricao()))
                .addParameter(new QueryCustom.QueryCustomParameter(
                        new BuilderQueryCustom.QueryGroupAnd(
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getProduto()), produto))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getGrupo().getCodigo()), produto.getSubGrupo().getId().getCodigoGrupoProduto()))),
                                new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                        new QueryCustom.QueryCustomParameter(path(proxy.getSubGrupo()), produto.getSubGrupo())))
                        )
                )).start().getList();

        ConfiguracaoContaPedidoTransferenciaLicitacao configuracaoContaPedidoTransferenciaLicitacao;
        if(isNotNullEmpty(configuracaoList)){
            // Busca a configuração por produto
            configuracaoContaPedidoTransferenciaLicitacao = Lambda.selectFirst(configuracaoList, having(on(ConfiguracaoContaPedidoTransferenciaLicitacao.class).getProduto().getCodigo(), equalTo(produto.getCodigo())));

            if(configuracaoContaPedidoTransferenciaLicitacao == null){
                // Busca a configuração por subgrupo
                configuracaoContaPedidoTransferenciaLicitacao = Lambda.selectFirst(configuracaoList, having(on(ConfiguracaoContaPedidoTransferenciaLicitacao.class).getSubGrupo(), equalTo(produto.getSubGrupo())));

                if(configuracaoContaPedidoTransferenciaLicitacao == null){
                    // Busca a configuração por grupo
                    configuracaoContaPedidoTransferenciaLicitacao = Lambda.selectFirst(configuracaoList, having(on(ConfiguracaoContaPedidoTransferenciaLicitacao.class).getGrupo().getCodigo(), equalTo(produto.getSubGrupo().getId().getCodigoGrupoProduto())));
                }
            }

            if(configuracaoContaPedidoTransferenciaLicitacao != null){
                if(pedidoTransferenciaLicitacao.getCodigo() != null){
                    if(!configuracaoContaPedidoTransferenciaLicitacao.getTipoConta().getCodigo().equals(tipoContaMedicamento.getCodigo())){
                        initDlgConfirmacao(target);
                        dlgConfirmacaoOk.setMessage(target, bundle("msgNaoPossívelAdicionarProdutosComTipoContaDiferenteTipoContaUtilizadaPedidoXTipoContaConfiguradaProdutoX",
                                tipoContaMedicamento.getDescricao(), produto.getDescricao(), configuracaoContaPedidoTransferenciaLicitacao.getTipoConta().getDescricao()));
                        dlgConfirmacaoOk.show(target);
                    }
                } else {
                    // Se a subconta da configuração existir conta do consorciado, será utilizada essa. Caso contrário continuar utilizando a do parâmetro GEM tipoContaMedicamento
                    if(LoadManager.getInstance(SubConta.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubConta.PROP_TIPO_CONTA), configuracaoContaPedidoTransferenciaLicitacao.getTipoConta()))
                            .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubConta.PROP_CONTA), contaPadrao))
                            .exists()) {
                        tipoContaMedicamento = configuracaoContaPedidoTransferenciaLicitacao.getTipoConta();
                    }
                }
            }
        }
    }

    private void initDlgConfirmacao(AjaxRequestTarget target) {
        if (dlgConfirmacaoOk == null) {
            addModal(target, dlgConfirmacaoOk = new DlgConfirmacaoOk(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    autoCompleteConsultaProduto.limpar(target);
                }
            });
        }
    }
    
}
