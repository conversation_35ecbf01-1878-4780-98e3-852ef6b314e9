package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum BenznidazolOutroEnum implements IEnum<SimNaoEnum> {
    BENZNIDAZOL(1L, "Benznidazol"),
    OUTRO(2L, "Outro");

    private Long value;
    private String descricao;

    BenznidazolOutroEnum(long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }


    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static BenznidazolOutroEnum valueOf(Long value) {
        for (BenznidazolOutroEnum v : BenznidazolOutroEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
