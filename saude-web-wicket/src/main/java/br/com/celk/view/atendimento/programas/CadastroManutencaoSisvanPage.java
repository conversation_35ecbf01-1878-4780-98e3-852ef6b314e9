package br.com.celk.view.atendimento.programas;

import br.com.celk.component.datechooser.DateChooserAjax;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.doublefield.RequiredDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.atendimento.programas.autocompletealimentacao.AutoCompleteConsultaAlimentacao;
import br.com.celk.view.atendimento.programas.autocompleteintercorrencia.AutoCompleteConsultaIntercorrencia;
import br.com.celk.view.atendimento.programas.autocompletesisvandoenca.AutoCompleteConsultaSisvanDoenca;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.avaliacao.interfaces.dto.RegistrarEstadoNutricionalDTOParam;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusHelper;
import br.com.ksisolucoes.vo.prontuario.avaliacao.Sisvan;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroManutencaoSisvanPage extends CadastroPage<Sisvan> {

    private AutoCompleteConsultaUsuarioCadsus usuarioCadsus;
    private AutoCompleteConsultaAlimentacao alimentacao;
    private Long peso;
    private DropDown gestante;
    private DateChooserAjax dum;
    private DateChooserAjax dataPreNatal;
    private Long idadePaciente = null;
    private DropDown vacinaEmDia;
    private InputField pesoNascer;
    private RequiredDoubleField pesoPreGestacional;
    private AutoCompleteConsultaSisvanDoenca autoCompleteConsultaSisvanDoenca;
    private AutoCompleteConsultaIntercorrencia autoCompleteConsultaIntercorrencia;
    private DoubleField inputFieldAltura;
    private RequiredDoubleField txtPeso;


    public CadastroManutencaoSisvanPage(Sisvan object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
        if (editar) {
            usuarioCadsus.setEnabled(false);
            verificaCampos(object.getUsuarioCadsus(), null);
        }
    }

    public CadastroManutencaoSisvanPage(Sisvan object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroManutencaoSisvanPage() {
        super();
    }

    @Override
    public void init(Form form) {
        Sisvan proxy = on(Sisvan.class);

        form.add(usuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(proxy.PROP_USUARIO_CADSUS, true) {
            @Override
            public AutoCompleteConsultaUsuarioCadsus.Configuration getConfigurationInstance() {
                return AutoCompleteConsultaUsuarioCadsus.Configuration.ATIVO_PROVISORIO;
            }
        });

        form.add(new DateChooserAjax(path(proxy.getDataAcompanhamento())).setRequired(true));
        form.add(txtPeso = (RequiredDoubleField) new RequiredDoubleField(path(proxy.getPeso())).setRequired(true));
        txtPeso.setMDec(3);
        form.add(inputFieldAltura = new DoubleField(path(proxy.getAltura())).setMDec(1));
        inputFieldAltura.setRequired(true);
        inputFieldAltura.setLabel(new Model(BundleManager.getString("altura")));
        form.add(pesoNascer = new InputField(path(proxy.getPesoNascer())));
        form.add(alimentacao = new AutoCompleteConsultaAlimentacao(path(proxy.getSisvanAlimentacao())));
        form.add(pesoPreGestacional = new RequiredDoubleField(path(proxy.getPesoPreGestacional())));
        pesoPreGestacional.setMDec(3);
        form.add(dum = new DateChooserAjax(path(proxy.getDum())));
        form.add(autoCompleteConsultaSisvanDoenca = new AutoCompleteConsultaSisvanDoenca(path(proxy.getSisvanDoenca()), true));
        autoCompleteConsultaSisvanDoenca.setLabel(new Model(BundleManager.getString("doenca")));
        form.add(autoCompleteConsultaIntercorrencia = new AutoCompleteConsultaIntercorrencia(path(proxy.getSisvanIntercorrencia()), true));
        autoCompleteConsultaIntercorrencia.setLabel(new Model(BundleManager.getString("intercorrencia")));

        alimentacao.setEnabled(false);
        pesoPreGestacional.setEnabled(false);
        dum.setEnabled(false);

        add(form);

        usuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                getForm().getModel().getObject().setUsuarioCadsus(null);
                getForm().getModel().getObject().setEnderecoUsuarioCadsus(null);

                alimentacao.limpar(target);
                alimentacao.setEnabled(false);
                alimentacao.setRequired(false);
                pesoNascer.limpar(target);
                pesoPreGestacional.limpar(target);
                pesoPreGestacional.setEnabled(false);
                pesoPreGestacional.setRequired(false);
                dum.limpar(target);
                dum.setEnabled(false);
                dum.setRequired(false);

                target.add(pesoNascer);
                target.add(pesoPreGestacional);
                target.add(dum);
                target.add(alimentacao);
            }
        });

        usuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                try {
                    getForm().getModel().getObject().setEnderecoUsuarioCadsus(UsuarioCadsusHelper.getEnderecoUsuarioCadsus(object, false));
                    setarCamposPreNatal(object, target);
                    verificaCampos(object, target);
                } catch (ValidacaoException ex) {
                    usuarioCadsus.limpar(target);
                    MessageUtil.modalWarn(target, usuarioCadsus, ex);
                }
            }
        });

    }

    @Override
    public Class<Sisvan> getReferenceClass() {
        return Sisvan.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaManutencaoSisvanPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastroManutencaoSisvan");
    }

    private void verificaCampos(UsuarioCadsus usuarioCadsus, AjaxRequestTarget target) {

        if (usuarioCadsus != null) {
            try {

                UsuarioCadsusDado usuarioDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                        .addProperties(new HQLProperties(UsuarioCadsusDado.class).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, usuarioCadsus.getCodigo()))
                        .start().getVO();

                if (usuarioDado != null) {

                    if (usuarioDado.getPesoNascer() != null) {
                        pesoNascer.setComponentValue(usuarioDado.getPesoNascer());
                    }
                    if (usuarioDado.getPesoPreGestacional() != null) {
                        pesoPreGestacional.setComponentValue(usuarioDado.getPesoPreGestacional());
                    }
                    if (usuarioDado.getDum() != null) {
                        dum.setComponentValue(usuarioDado.getDum());
                    }
                    if (usuarioDado.getGestante() != null) {

                        if (usuarioDado.getGestante().equals(RepositoryComponentDefault.SIM_LONG)) {
                            pesoPreGestacional.setEnabled(true);
                            pesoPreGestacional.setRequired(true);
                            dum.setEnabled(true);
                            dum.setRequired(true);
                        } else {
                            pesoPreGestacional.setEnabled(false);
                            pesoPreGestacional.setRequired(false);
                            dum.setEnabled(false);
                            dum.setRequired(false);
                        }
                    }
                }

                if (usuarioCadsus.getIdadeEmMeses() < (Integer) BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("idadeParaValidarAlimentacao")) {
                    alimentacao.setEnabled(true);
                    alimentacao.setRequired(true);
                    alimentacao.setLabel(new Model(BundleManager.getString("alimentacao")));
                } else {
                    alimentacao.setEnabled(false);
                    alimentacao.setRequired(false);
                }
                if (target != null) {
                    target.add(alimentacao);
                    target.add(pesoNascer);
                    target.add(pesoPreGestacional);
                    target.add(dum);
                }

            } catch (DAOException ex) {
                Logger.getLogger(CadastroManutencaoBolsaFamiliaPage.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
    }

    private PreNatal getPreNatalPaciente(UsuarioCadsus usuarioCadsus) {
        return LoadManager.getInstance(PreNatal.class)
                .addParameter(new QueryCustom.QueryCustomParameter(PreNatal.PROP_USUARIO_CADSUS, usuarioCadsus))
                .setMaxResults(1).start().getVO();
    }

    private void setarCamposPreNatal(UsuarioCadsus usuarioCadsus, AjaxRequestTarget target) {
        PreNatal preNatalPaciente = getPreNatalPaciente(usuarioCadsus);
        if (preNatalPaciente != null) {
            getForm().getModel().getObject().setPesoPreGestacional(preNatalPaciente.getPesoAnterior());
            getForm().getModel().getObject().setAltura(preNatalPaciente.getEstatura());
        }
        target.add(pesoPreGestacional);
        target.add(inputFieldAltura);
    }

    @Override
    public Sisvan salvar(Sisvan object) throws DAOException, ValidacaoException {
        Sisvan sisvan = BOFactoryWicket.save(object);
        registrarEstadoNutricional(sisvan);
        return sisvan;
    }

    private void registrarEstadoNutricional(Sisvan sisvan) throws ValidacaoException, DAOException {
        RegistrarEstadoNutricionalDTOParam param = new RegistrarEstadoNutricionalDTOParam();
        param.setSisvan(sisvan);
        BOFactory.getBO(AtendimentoFacade.class).registrarEstadoNutricional(param);
    }
}
