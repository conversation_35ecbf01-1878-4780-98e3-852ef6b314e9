package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.hospital.tiss.DadosAutorizacaoTiss;
import br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlDadosGuiaSolicitacao extends Panel {

    private CompoundPropertyModel<DadosAutorizacaoTiss> model;
    private DateChooser dchDataSolicitacao;
    private InputArea txaIndicacaoClinica;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnCancelar;

    public PnlDadosGuiaSolicitacao(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", model = new CompoundPropertyModel(new DadosAutorizacaoTiss()));

        DadosAutorizacaoTiss proxy = on(DadosAutorizacaoTiss.class);

        form.add(dchDataSolicitacao = new DateChooser(path(proxy.getDataSolicitacao())));
        form.add(txaIndicacaoClinica = new InputArea(path(proxy.getIndicacaoClinica())));

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                confirmar(target);
            }
        });

        form.add(btnCancelar = new AbstractAjaxButton("btnCancelar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });
        btnCancelar.setDefaultFormProcessing(false);

        setOutputMarkupId(true);

        add(form);
    }

    private void confirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        BOFactoryWicket.save(model.getObject());
        onFechar(target);
    }

    public void setContaPaciente(AjaxRequestTarget target, ContaPaciente contaPaciente) {
        DadosAutorizacaoTiss dadosAutorizacaoTiss = LoadManager.getInstance(DadosAutorizacaoTiss.class)
                .addParameter(new QueryCustom.QueryCustomParameter(DadosAutorizacaoTiss.PROP_CONTA_PACIENTE, contaPaciente))
                .start().getVO();

        if (dadosAutorizacaoTiss == null) {
            dadosAutorizacaoTiss = new DadosAutorizacaoTiss();
            dadosAutorizacaoTiss.setContaPaciente(contaPaciente);
        }

        limpar(target);
        model.setObject(dadosAutorizacaoTiss);
        update(target);
    }

    private void limpar(AjaxRequestTarget target) {
        dchDataSolicitacao.limpar(target);
        txaIndicacaoClinica.limpar(target);
    }

    private void update(AjaxRequestTarget target) {
        target.add(this);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException;
}
