package br.com.celk.view.vacina.rnds;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateTimeColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.template.base.BasePage;
import br.com.celk.view.cadsus.usuariocadsus.CadastroPacientePage;
import br.com.celk.view.controle.util.PermissoesWebUtil;
import br.com.ksisolucoes.bo.vacina.interfaces.facade.VacinaFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.rnds.RndsUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vacina.rnds.RndsIntegracaoVacina;
import br.com.ksisolucoes.vo.vacina.rnds.RndsVacinaOcorrencias;
import com.amazonaws.util.Base64;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.MarkupStream;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

@Private
public class DetalhesIntegracaoVacinasRndsPage extends BasePage {

    private Form<RndsIntegracaoVacina> form;
    private final RndsIntegracaoVacina rndsIntegracaoVacina;
    private WebMarkupContainer containerDadosRnds;
    private WebMarkupContainer containerDiagnosticoLegado;
    private MultiLineLabel labelDiagnostico;
    private DisabledInputField inputFieldCns;
    private List<RndsVacinaOcorrencias> ocorrenciasList;
    private AjaxPreviewBlank ajaxPreviewBlank;

    public DetalhesIntegracaoVacinasRndsPage(RndsIntegracaoVacina rndsIntegracaoVacina) {
        this.rndsIntegracaoVacina = rndsIntegracaoVacina;
        init();
    }

    private void init() {
        RndsIntegracaoVacina proxy = on(RndsIntegracaoVacina.class);

        addContainerDadosEdicao(proxy, getForm());
        addContainerDadosPaciente(proxy, getForm());
        addContainerDadosVacina(proxy, getForm());
        addContainerDadosRnds(proxy, getForm());
        addBtns();

        getForm().add(ajaxPreviewBlank = new AjaxPreviewBlank());
        add(getForm());
    }

    private Form<RndsIntegracaoVacina> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel<>(new RndsIntegracaoVacina()));
            this.form.getModel().setObject(carregarDadosIntegracao(rndsIntegracaoVacina));
            carregarOcorrencias(rndsIntegracaoVacina);
        }
        return this.form;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("detalhesIntegracaoRNDS");
    }

    private void addContainerDadosRnds(RndsIntegracaoVacina proxy, Form form) {
        containerDadosRnds = new WebMarkupContainer("containerDadosRnds");
        containerDadosRnds.setOutputMarkupPlaceholderTag(true);
        DisabledInputField inputFieldUuidRnds = new DisabledInputField(path(proxy.getUuidRnds()));
        DisabledInputField inputFieldTipoRegistro = new DisabledInputField(path(proxy.getRiaFormatado()));

        Table tblOcorrencias = new Table("tblOcorrencias", getColumnsOcorrencias(), getCollectionProviderOcorrencias());
        tblOcorrencias.populate();

        containerDadosRnds.add(tblOcorrencias);
        containerDadosRnds.add(inputFieldUuidRnds);
        containerDadosRnds.add(inputFieldTipoRegistro);
        containerDadosRnds.add(getContainerDiagnosticoLegado(proxy));
        form.add(containerDadosRnds);

        editarEstiloLabelDiagnostico(labelDiagnostico);
    }

    private void addBtns() {
        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) {
                setResponsePage(new ConsultaIntegracaoVacinasRndsPage());
            }
        }.setDefaultFormProcessing(false));

        AbstractAjaxButton btnReenviar = new AbstractAjaxButton("btnReEnviar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form formulario) throws ValidacaoException, DAOException {
                reenviarParaRnds(target, form.getModel().getObject());
            }
        };
        btnReenviar.setEnabled(enabledReenviar(form.getModel().getObject()));
        getForm().add(btnReenviar);

        getForm().add(new AbstractAjaxButton("btnAtualizarRegistro") {
            @Override
            public void onAction(AjaxRequestTarget target, Form formulario) {
                atualizarRegistro(target, form.getModel().getObject());
            }
        });
    }

    private WebMarkupContainer getContainerDiagnosticoLegado(RndsIntegracaoVacina proxy) {
        containerDiagnosticoLegado = new WebMarkupContainer("containerDiagnosticoLegado");
        containerDiagnosticoLegado.setOutputMarkupPlaceholderTag(true);
        labelDiagnostico = new MultiLineLabel(path(proxy.getDiagnostico())) {
            @Override
            public void onComponentTagBody(MarkupStream markupStream, ComponentTag openTag) {
                String defaultModelObjectAsString = getDefaultModelObjectAsString();
                replaceComponentTagBody(markupStream, openTag, defaultModelObjectAsString);
            }
        };
        labelDiagnostico.setEscapeModelStrings(false);
        labelDiagnostico.setOutputMarkupPlaceholderTag(true);
        containerDiagnosticoLegado.add(labelDiagnostico);

        containerDiagnosticoLegado.setVisible(getForm().getModelObject().getDiagnostico() != null);

        return containerDiagnosticoLegado;
    }

    private List<IColumn> getColumnsOcorrencias() {
        List<IColumn> columns = new ArrayList();

        RndsVacinaOcorrencias proxy = on(RndsVacinaOcorrencias.class);

        columns.add(getCustomColumn());
        columns.add(new DateTimeColumn(bundle("data"), path(proxy.getDataOcorrencia())).setPattern("dd/MM/yyyy HH:mm:ss"));
        columns.add(createColumn(bundle("ocorrencia"), proxy.getDescricao()));
        columns.add(createColumn(bundle("usuario"), proxy.getUsuario().getDescricaoCodigoNome()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderOcorrencias() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return ocorrenciasList;
            }
        };
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<RndsVacinaOcorrencias>() {
            @Override
            public void customizeColumn(RndsVacinaOcorrencias rowObject) {
                addAction(ActionType.VIEW_JSON, rowObject, new IModelAction<RndsVacinaOcorrencias>() {
                    @Override
                    public void action(AjaxRequestTarget target, RndsVacinaOcorrencias rndsVacinaOcorrencias) {
                        ajaxPreviewBlank.initiateJsonBase64(target, new String(Base64.encode(String.valueOf(rndsVacinaOcorrencias.getDocumentoEnviado()).getBytes())));
                    }
                }).setVisible(rowObject.getDocumentoEnviado() != null);

                addAction(ActionType.VIEW_JSON_RETORNO, rowObject, new IModelAction<RndsVacinaOcorrencias>() {
                    @Override
                    public void action(AjaxRequestTarget target, RndsVacinaOcorrencias rndsVacinaOcorrencias) {
                        ajaxPreviewBlank.initiateJsonBase64(target, new String(Base64.encode(String.valueOf(rndsVacinaOcorrencias.getJsonRetorno()).getBytes())));
                    }
                }).setVisible(rowObject.getJsonRetorno() != null);
            }
        };
    }

    private void editarEstiloLabelDiagnostico(MultiLineLabel labelDiagnostico) {
        if (!RndsIntegracaoVacina.Situacao.FALHA.value().equals(getForm().getModel().getObject().getSituacao())) {
            labelDiagnostico.add(new AttributeModifier("style", "text-decoration: line-through;"));
        }
    }

    private void addContainerDadosVacina(RndsIntegracaoVacina proxy, Form form) {
        WebMarkupContainer containerDadosVacina = new WebMarkupContainer("containerDadosVacina");
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getTipoVacina().getTipoEsusDescricaoComCodigo())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getDescricaoDosesCodigo())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getDescricaoCalendarioOuEstrategia())));

        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getProfissionalAplicacao().getNomeCnsFormatado())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getLote())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getDataAplicacao())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getHoraAplicacaoInicio())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getHoraAplicacaoFim())));

        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getDescricaoLocalAtendimento())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getProdutoVacina().getProduto().getFabricante().getNomeCodigoPni())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getEmpresa().getDescricaoFormatadaCnesDescricao())));

        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getDescricaoHistoricoFormatado())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getLocalAplicacao().getDescricaoFormatado())));
        containerDadosVacina.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getViaAdministracao().getDescricaoFormatado())));
        form.add(containerDadosVacina);
    }

    private void addContainerDadosPaciente(RndsIntegracaoVacina proxy, Form form) {
        WebMarkupContainer containerDadosPaciente = new WebMarkupContainer("containerDadosPaciente");
        AbstractAjaxLink btnEditarPaciente = new AbstractAjaxLink("btnEditarPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target) {
                CadastroPacientePage responsePage = new CadastroPacientePage(getForm().getModelObject().getVacinaAplicacao().getUsuarioCadsus(), true) {
                    @Override
                    public void retornaPagina(AjaxRequestTarget target) {
                        setResponsePage(new DetalhesIntegracaoVacinasRndsPage(rndsIntegracaoVacina));
                        carregarDadosIntegracao(rndsIntegracaoVacina);
                    }
                };
                setResponsePage(responsePage);
            }
        };

        btnEditarPaciente.setVisible(verificaPermissaoEditarPaciente());
        btnEditarPaciente.setOutputMarkupPlaceholderTag(true);

        containerDadosPaciente.add(btnEditarPaciente);
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getUsuarioCadsus().getDescricaoSocialFormatado())));
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getUsuarioCadsus().getDescricaoIdadeAnoMesDia())));
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getCodigoDescricaoGestante())));
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getCodigoDescricaoComunicanteHanseniase())));
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getCodigoDescricaoPuerpera())));
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getCodigoDescricaoViajante())));
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getGrupoAtendimentoRndsDescricao())));

        inputFieldCns = new DisabledInputField(path(proxy.getVacinaAplicacao().getUsuarioCadsus().getCns()));
        inputFieldCns.setOutputMarkupPlaceholderTag(true);
        containerDadosPaciente.add(inputFieldCns);
        containerDadosPaciente.add(new DisabledInputField(path(proxy.getVacinaAplicacao().getUsuarioCadsus().getCpfFormatado())));

        form.add(containerDadosPaciente);
    }

    private void atualizarRegistro(AjaxRequestTarget target, RndsIntegracaoVacina rndsIntegracaoVacina) {
        carregarOcorrencias(rndsIntegracaoVacina);
        atualizarCampos(target);
    }

    private void reenviarParaRnds(AjaxRequestTarget target, RndsIntegracaoVacina rndsIntegracaoVacina) throws ValidacaoException, DAOException {
        if (RndsUtil.utilizaNovaIntegracaoRnds()) {
            BOFactory.getBO(VacinaFacade.class).reintegrarVacinaRnds(rndsIntegracaoVacina);
        } else {
            BOFactory.getBO(VacinaFacade.class).reintegrarVacinaRndsOld(rndsIntegracaoVacina.getVacinaAplicacao(), rndsIntegracaoVacina);
        }
        form.getModel().setObject(carregarDadosIntegracao(rndsIntegracaoVacina));
        carregarOcorrencias(rndsIntegracaoVacina);
        editarEstiloLabelDiagnostico(labelDiagnostico);
        atualizarCampos(target);
    }

    private void atualizarCampos(AjaxRequestTarget target) {
        target.add(containerDadosRnds);
        target.add(inputFieldCns);
        target.add(labelDiagnostico);
    }

    private boolean verificaPermissaoEditarPaciente() {
        if (SessaoAplicacaoImp.getInstance() == null) return false;
        return new PermissoesWebUtil().isPagePermitted(SessaoAplicacaoImp.getInstance().getUsuario(), CadastroPacientePage.class.getName());
    }

    private void addContainerDadosEdicao(RndsIntegracaoVacina proxy, Form form) {
        WebMarkupContainer containerDadosEdicao = new WebMarkupContainer("containerDadosEdicao");
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getUsuario().getNome())));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getDataUsuario())));
        containerDadosEdicao.add(new DisabledInputField(path(proxy.getSituacaoFormatado())));
        form.add(containerDadosEdicao);
    }

    public static RndsIntegracaoVacina carregarDadosIntegracao(RndsIntegracaoVacina rndsIntegracaoVacina) {
        try {
            return BOFactory.getBO(VacinaFacade.class).buscarDadosRndsIntegracaoRnds(rndsIntegracaoVacina);
        } catch (Exception e) {
            Loggable.log.error(e.getMessage(), e.getCause());
        }
        return null;
    }

    private void carregarOcorrencias(RndsIntegracaoVacina rndsIntegracaoVacina) {
        try {
            ocorrenciasList = BOFactory.getBO(VacinaFacade.class).buscarRndsVacinaOcorrencias(rndsIntegracaoVacina);
        } catch (Exception e) {
            Loggable.log.error(e.getMessage(), e.getCause());
        }
    }

    private boolean isCancelado(RndsIntegracaoVacina riv) {
        return RndsIntegracaoVacina.Situacao.CANCELADO.value().equals(riv.getSituacao());
    }

    private boolean isEnvioDuplicado(RndsIntegracaoVacina riv) {
        return RndsIntegracaoVacina.Situacao.ENVIO_DUPLICADO.value().equals(riv.getSituacao());
    }

    private boolean isEnviado(RndsIntegracaoVacina riv) {
        return RndsIntegracaoVacina.Situacao.ENVIADO.value().equals(riv.getSituacao());
    }

    private boolean enabledReenviar(RndsIntegracaoVacina riv) {
        return !isCancelado(riv) && !isEnvioDuplicado(riv) && !isEnviado(riv);
    }


}
