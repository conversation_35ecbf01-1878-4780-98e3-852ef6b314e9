package br.com.celk.view.atendimento.prontuario.cancelamentoatendimento;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.pager.CustomizeConsultaPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.consulta.ConsultaPage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.agenda.tipoprocedimento.autocomplete.AutoCompleteConsultaTipoProcedimento;
import br.com.celk.view.atendimento.prontuario.cancelamentoatendimento.dialog.DlgMotivoCancelamentoAtendimento;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoRuntimeException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusExclusao;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.extensions.markup.html.repeater.util.SortParam;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaCancelamentoAtendimentoPage extends ConsultaPage<Atendimento, List<BuilderQueryCustom.QueryParameter>> {

    private Empresa estabelecimento;
    private Profissional profissional;
    private String nome;
    private TipoProcedimento tipoProcedimento;
    private DlgMotivoCancelamentoAtendimento dlgMotivoCancelamentoAtendimento;

    public ConsultaCancelamentoAtendimentoPage() {
        setProcurarAoAbrir(false);
    }

    @Override
    public void initForm(Form form) {
        form.setDefaultModel(new CompoundPropertyModel(this));

        form.add(new AutoCompleteConsultaEmpresa("estabelecimento").setValidaUsuarioEmpresa(!isActionPermitted(Permissions.EMPRESA)));
        form.add(new AutoCompleteConsultaTipoProcedimento("tipoProcedimento").setIncluirInativos(true));
        form.add(new InputField<String>("nome"));
        form.add(new AutoCompleteConsultaProfissional("profissional"));

        getLinkNovo().setVisible(false);
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        Atendimento proxy = on(Atendimento.class);

        columns.add(getActionColumn());
        columns.add(createSortableColumn(bundle("numeroAtendimento"), proxy.getCodigo()));
        columns.add(createSortableColumn(bundle("dataAtendimento"), proxy.getDataHoraAtendimento()));
        columns.add(createSortableColumn(bundle("tipoProcedimento"), proxy.getNaturezaProcuraTipoAtendimento().getTipoProcedimento().getDescricao()));
        columns.add(createSortableColumn(bundle("paciente"), proxy.getUsuarioCadsus().getNome(), proxy.getUsuarioCadsus().getNomeSocial()));
        columns.add(createSortableColumn(bundle("estabelecimento"), proxy.getEmpresa().getDescricao()));
        columns.add(createSortableColumn(bundle("profissional"), proxy.getProfissional().getNome()));
        return columns;
    }

    @Override
    public void getAntesProcurar() throws ValidacaoException {
        if (this.estabelecimento == null && this.tipoProcedimento == null && this.profissional == null && ("".equals(Coalesce.asString(this.nome)) || Coalesce.asString(this.nome).trim().length() < 3)) {
            throw new ValidacaoException(bundle("msgInformePeloMenosUmFiltroEstabelecimentoProcedimentoPacienteProfissional"));
        }
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<Atendimento>() {
            @Override
            public void customizeColumn(Atendimento rowObject) {
                addAction(ActionType.CANCELAR, rowObject, new IModelAction<Atendimento>() {
                    @Override
                    public void action(AjaxRequestTarget target, Atendimento modelObject) throws ValidacaoException, DAOException {
                        initDlgMotivoCancelamentoAtendimento(target, modelObject);
                    }
                }).setQuestionDialogBundleKey(null);
            }
        };
    }

    private void initDlgMotivoCancelamentoAtendimento(AjaxRequestTarget target, Atendimento atendimento) {
        if (dlgMotivoCancelamentoAtendimento == null) {
            addModal(target, dlgMotivoCancelamentoAtendimento = new DlgMotivoCancelamentoAtendimento(newModalId()) {
                @Override
                public void onConfirmar(AjaxRequestTarget target, Atendimento atendimento, String motivoCancelamento) throws ValidacaoException, DAOException {
                    cancelarAtendimentoFinalizado(target, atendimento, motivoCancelamento);
                }
            });
        }
        dlgMotivoCancelamentoAtendimento.show(target, atendimento);
    }

    private void cancelarAtendimentoFinalizado(AjaxRequestTarget target, Atendimento atendimento, String motivoCancelamento) throws DAOException, ValidacaoException {
        BOFactoryWicket.getBO(AtendimentoFacade.class).cancelarAtendimentoFinalizado(atendimento, motivoCancelamento);
        getPageableTable().update(target);
        info(target, bundle("msgAtendimentoCanceladoSucesso"));
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new CustomizeConsultaPagerProvider(new CustomizeConsultaAdapter() {
            @Override
            public Class getClassConsulta() {
                return Atendimento.class;
            }

            @Override
            public String[] getProperties() {
                return VOUtils.mergeProperties(new HQLProperties(Atendimento.class).getProperties(),
                        new String[]{
                                VOUtils.montarPath(Atendimento.PROP_CODIGO),
                                VOUtils.montarPath(Atendimento.PROP_DATA_ATENDIMENTO),
                                VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO),
                                VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME),
                                VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO),
                                VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL),
                                VOUtils.montarPath(Atendimento.PROP_ATENDIMENTO_PRINCIPAL, Atendimento.PROP_CODIGO),
                                VOUtils.montarPath(Atendimento.PROP_EMPRESA, Empresa.PROP_CODIGO),
                                VOUtils.montarPath(Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO),
                                VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO),
                                VOUtils.montarPath(Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME),
                                VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_CODIGO),
                                VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO, TipoProcedimento.PROP_DESCRICAO),
                        });
            }
        }) {
            @Override
            public SortParam getDefaultSort() {
                return new SortParam(VOUtils.montarPath(UsuarioCadsusExclusao.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), true);
            }
        };
    }

    @Override
    public List<BuilderQueryCustom.QueryParameter> getParameters() {
        List<BuilderQueryCustom.QueryParameter> parameters = new ArrayList<>();

        parameters.add(new QueryCustom.QueryCustomParameter(Atendimento.PROP_EMPRESA, estabelecimento));
        parameters.add(new QueryCustom.QueryCustomParameter(Atendimento.PROP_PROFISSIONAL, profissional));
        if (nome != null) {
            parameters.add(new QueryCustom.QueryCustomParameter(
                    new BuilderQueryCustom.QueryGroupAnd(
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QueryParameter.ILIKE, nome))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupOr(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL), RepositoryComponentDefault.SIM_LONG))),
                            new QueryCustom.QueryCustomParameter(new BuilderQueryCustom.QueryGroupAnd(
                                    new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO), BuilderQueryCustom.QueryParameter.ILIKE, nome))))));
        }
        parameters.add(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(Atendimento.PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO, NaturezaProcuraTipoAtendimento.PROP_TIPO_PROCEDIMENTO), tipoProcedimento));
        parameters.add(new QueryCustom.QueryCustomParameter(Atendimento.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, Atendimento.STATUS_CANCELADO));
        parameters.add(new QueryCustom.QueryCustomParameter(Atendimento.PROP_DATA_ATENDIMENTO, BuilderQueryCustom.QueryParameter.IS_NOT_NULL));

        try {
            Long tempoLimiteCancelamentoAtendimento = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("TempoLimiteCancelamentoAtendimento");

            parameters.add(new QueryCustom.QueryCustomParameter(Atendimento.PROP_DATA_FECHAMENTO, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL,
                    Data.removeMinutos(DataUtil.getDataAtual(), new Dinheiro(Coalesce.asLong(tempoLimiteCancelamentoAtendimento)).multiplicar(60L).intValue())));
        } catch (ValidacaoRuntimeException ex) {
            warn(ex.getMessage());
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return parameters;
    }

    @Override
    public Class getCadastroPage() {
        return ConsultaCancelamentoAtendimentoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cancelamentoAtendimento");
    }
}