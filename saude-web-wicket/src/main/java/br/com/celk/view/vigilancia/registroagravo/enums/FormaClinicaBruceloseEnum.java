package br.com.celk.view.vigilancia.registroagravo.enums;

import br.com.ksisolucoes.enums.IEnum;

public enum FormaClinicaBruceloseEnum implements IEnum<SimNaoEnum> {
    AGUDA(0L, "Aguda"),
    SUB_AGUDA(1L, "Sub aguda"),
    CRONICA(2L, "Crônica"),;

    private Long value;
    private String descricao;

    FormaClinicaBruceloseEnum(long value, String descricao) {
        this.value = value;
        this.descricao = descricao;
    }


    @Override
    public Long value() {
        return value;
    }

    @Override
    public String descricao() {
        return descricao;
    }

    public static FormaClinicaBruceloseEnum valueOf(Long value) {
        for (FormaClinicaBruceloseEnum v : FormaClinicaBruceloseEnum.values()) {
            if (v.value().equals(value)) {
                return v;
            }
        }
        return null;
    }
}
