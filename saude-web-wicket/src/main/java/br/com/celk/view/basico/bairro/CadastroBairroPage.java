package br.com.celk.view.basico.bairro;

import br.com.celk.component.inputfield.upper.RequiredUpperField;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.view.basico.cidade.autocomplete.AutoCompleteConsultaCidade;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.ksisolucoes.bo.basico.interfaces.facade.CidadeFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Bairro;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;


/**
 *
 * <AUTHOR>
 */
@Private

public class CadastroBairroPage extends CadastroPage<Bairro> {

    private AutoCompleteConsultaCidade autoCompleteConsultaCidade;

    public CadastroBairroPage(Bairro object, boolean viewOnly, boolean editar) {
        this(object, viewOnly);
    }

    public CadastroBairroPage(Bairro object, boolean viewOnly) {
        super(object, viewOnly);
    }

    public CadastroBairroPage(Bairro object) {
        this(object, false);
    }

    public CadastroBairroPage() {
        this(null);
    }

    @Override
    public void init(Form form) {
        form.add(autoCompleteConsultaCidade = new AutoCompleteConsultaCidade(VOUtils.montarPath(Bairro.PROP_CIDADE), true));
        form.add(new AutoCompleteConsultaEmpresa(VOUtils.montarPath(Bairro.PROP_EMPRESA)));
        form.add(new RequiredUpperField(VOUtils.montarPath(Bairro.PROP_DESCRICAO)));
    }

    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaCidade.getTxtDescricao();
    }

    @Override
    public Class<Bairro> getReferenceClass() {
        return Bairro.class;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaBairroPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("cadastroBairro");
    }

    @Override
    public Object salvar(Bairro object) throws DAOException, ValidacaoException {
        return BOFactoryWicket.save(object);
    }

    @Override
    public String getMsgSalvo(Object returnObject) {
        return BundleManager.getString("registro_salvo_sucesso");
    }
}
