package br.com.celk.view.hospital.loteAih.customcolumn;

import br.com.celk.component.link.ReportLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.vo.prontuario.hospital.Aih;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public abstract class ConsultaAihsColumnPanel extends Panel{

    private ReportLink btnImprimir;
    private Aih aih;
    
    public ConsultaAihsColumnPanel(String id, Aih aih) {
        super(id);
        this.aih = aih;
        init();
    }

    private void init() {
        add(btnImprimir = new ReportLink("btnImprimir") {

            @Override
            public boolean isEnabled() {
                return Aih.PROP_STATUS != null ||
                       !Aih.Status.CANCELADA.value().equals(aih.getStatus());
            }

            @Override
            public DataReport getDataReport() throws ReportException {
                return onImprimir();
            }

        });
        btnImprimir.add(new AttributeModifier("title", BundleManager.getString("imprimirLaudo")));
    }
    
    public abstract DataReport onImprimir() throws ReportException;

}
