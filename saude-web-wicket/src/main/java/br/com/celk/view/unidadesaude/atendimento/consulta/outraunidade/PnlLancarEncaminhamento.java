package br.com.celk.view.unidadesaude.atendimento.consulta.outraunidade;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.ksisolucoes.bo.interfaces.CadastroFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade;
import br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidadeEncaminhamento;
import com.sun.tools.internal.ws.wsdl.parser.Constants;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.CheckBox;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.markup.html.form.TextField;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;

import java.util.Date;

import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

public abstract class PnlLancarEncaminhamento extends Panel {
    private static final long serialVersionUID = 6990930566174228372L;
    private boolean isFromDetalhes;

    private CheckBox encerrarAcompanhamento;
    private Form<PacienteAtendidoOutraUnidadeEncaminhamento> formLancarEncaminhamento;
    private WebMarkupContainer containerJustificativa;
    private TextArea<String> justificativa;

    private PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade;

    public PnlLancarEncaminhamento(String id, PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade, boolean isFromDetalhes) {
        super(id);
        this.pacienteAtendidoOutraUnidade = pacienteAtendidoOutraUnidade;
        this.isFromDetalhes = isFromDetalhes;
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        setOutputMarkupPlaceholderTag(true);

        addCampos();
        addBotoes();

        add(formLancarEncaminhamento);
    }

    private void addBotoes() {
        formLancarEncaminhamento.add(getBtnFechar());
        formLancarEncaminhamento.add(getBtnSalvar());
    }

    private void addCampos() {
        PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento = on(PacienteAtendidoOutraUnidadeEncaminhamento.class);
        PacienteAtendidoOutraUnidade paciente = on(PacienteAtendidoOutraUnidade.class);

        formLancarEncaminhamento = new Form<>("formLancarEncaminhamento", new CompoundPropertyModel(new PacienteAtendidoOutraUnidadeEncaminhamento()));
        formLancarEncaminhamento.add(new TextField<>("paciente", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getNomePaciente())));
        formLancarEncaminhamento.add(new TextField<>("dtAtendimento", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getDataHoraAtendimento())));
        formLancarEncaminhamento.add(new TextField<>("tipoAtendimento", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getNaturezaProcuraTipoAtendimento().getTipoAtendimento().getDescricao())));
        formLancarEncaminhamento.add(new TextField<>("unidade", new Model<>(pacienteAtendidoOutraUnidade.getAtendimento().getEmpresa().getDescricao())));
        formLancarEncaminhamento.add(new TextArea<>(path(encaminhamento.getDescricao())));

        containerJustificativa = new WebMarkupContainer("containerJustificativa");
        containerJustificativa.setOutputMarkupId(true);
        containerJustificativa.setEnabled(false);
        containerJustificativa.add(justificativa = new TextArea<>(path(paciente.getJustificativa()), new Model<>("")));
        formLancarEncaminhamento.add(containerJustificativa);

        formLancarEncaminhamento.add(encerrarAcompanhamento = new CheckBox("encerrarAcompanhamento", new Model<>(false)));
        encerrarAcompanhamento.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                containerJustificativa.setEnabled(Boolean.parseBoolean(encerrarAcompanhamento.getValue()));
                target.add(containerJustificativa);

            }
        });
    }

    private AjaxButton getBtnFechar() {
        return new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                onClose(target);
            }
        };
    }

    private AjaxButton getBtnSalvar() {
        return new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento = (PacienteAtendidoOutraUnidadeEncaminhamento) form.getModelObject();
                validar(encaminhamento);
                salvar(encaminhamento);
                redirecionar(target);
            }
        };
    }

    private void validar(PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento) throws ValidacaoException {
        if (Constants.FALSE.equals(encerrarAcompanhamento.getValue())) {

            // Validação de preenchimento de descrição do encaminhamento
            if (encaminhamento.getDescricao() == null || encaminhamento.getDescricao().trim().isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_descricao_encaminhamento"));
            }
        } else {

            // Validação de preenchimento da justificativa do encerramento
            if (justificativa.getValue() == null || justificativa.getValue().trim().isEmpty()) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_justificativa_encerramento"));
            }
        }
    }

    private void salvar(PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento) throws DAOException, ValidacaoException {
        if (encaminhamento.getDescricao() != null && !encaminhamento.getDescricao().trim().isEmpty()) {
            montarEncaminhamento(encaminhamento);
            BOFactoryWicket.getBO(CadastroFacade.class).save(encaminhamento);
        }

        if (Constants.TRUE.equals(encerrarAcompanhamento.getValue())) {
            montarEncerramentoPacienteOutraUnidade();
            BOFactoryWicket.getBO(CadastroFacade.class).save(pacienteAtendidoOutraUnidade);
        }
    }

    private void redirecionar(AjaxRequestTarget target) {
        if (isFromDetalhes) {
            if (PacienteAtendidoOutraUnidade.SituacaoPacienteAtendidoOutraUnidade.ENCERRADO.value().equals(pacienteAtendidoOutraUnidade.getSituacao())) {
                setResponsePage(new ConsultaAtendimentoOutraUnidadePage());
            } else {
                setResponsePage(new DetalheAtendimentoOutraUnidadePage(pacienteAtendidoOutraUnidade));
            }
        } else {
           onClose(target);
        }
    }

    private void montarEncerramentoPacienteOutraUnidade() {
        pacienteAtendidoOutraUnidade.setJustificativa(justificativa.getValue());
        pacienteAtendidoOutraUnidade.setDataEncerramento(new Date());
        pacienteAtendidoOutraUnidade.setUsuarioEncerramento(SessaoAplicacaoImp.getInstance().getUsuario());
        pacienteAtendidoOutraUnidade.setSituacao(PacienteAtendidoOutraUnidade.SituacaoPacienteAtendidoOutraUnidade.ENCERRADO.value());
    }

    private void montarEncaminhamento(PacienteAtendidoOutraUnidadeEncaminhamento encaminhamento) {
        encaminhamento.setPacienteAtendidoOutraUnidade(pacienteAtendidoOutraUnidade);
        encaminhamento.setDataCadastro(new Date());
        encaminhamento.setUsuarioCadastro(SessaoAplicacaoImp.getInstance().getUsuario());
    }

    public abstract void onClose(AjaxRequestTarget target);
}
