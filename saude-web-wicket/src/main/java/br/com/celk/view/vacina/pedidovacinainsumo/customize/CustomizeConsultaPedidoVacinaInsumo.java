package br.com.celk.view.vacina.pedidovacinainsumo.customize;

import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.consulta.CustomizeConsultaAdapter;
import br.com.ksisolucoes.vo.vacina.PedidoVacinaInsumo;

/**
 *
 * <AUTHOR>
 */
public class CustomizeConsultaPedidoVacinaInsumo extends CustomizeConsultaAdapter{

    @Override
    public Class getClassConsulta() {
        return PedidoVacinaInsumo.class;
    }

    @Override
    public String[] getProperties() {
        return new HQLProperties(PedidoVacinaInsumo.class).getProperties();
    }

}
