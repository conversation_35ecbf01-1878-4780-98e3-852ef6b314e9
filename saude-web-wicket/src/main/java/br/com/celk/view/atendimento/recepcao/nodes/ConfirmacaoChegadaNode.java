package br.com.celk.view.atendimento.recepcao.nodes;

import br.com.celk.atendimento.recepcao.NodesRecepcaoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.recepcao.nodes.annotations.RecepcaoNode;
import br.com.celk.view.atendimento.recepcao.nodes.base.RecepcaoNodeImp;
import br.com.celk.view.atendimento.recepcao.panel.confirmacaochegada.ConfirmacaoChegadaAihPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;

/**
 * <AUTHOR>
 */
@RecepcaoNode(NodesRecepcaoRef.REG_LEITO_CONFIRMACAO_CHEGADA)
public class ConfirmacaoChegadaNode extends RecepcaoNodeImp{

    @Override
    public RecepcaoCadastroPanel getPanel(String id) {
        return new ConfirmacaoChegadaAihPanel(id);
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("confirmacaoChegada");
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.CALENDAR_EDIT;
    }
}
