package br.com.celk.view.vigilancia.requerimentos.inspecaosanitaria;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria.FornecedorComponentesCriticosDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

public class FornecedoresComponentesCriticosPanel extends Panel {

    private FornecedorComponentesCriticosDTO dto;
    private Form form;
    private WebMarkupContainer containerFornecedorComponentesCriticos;
    private CompoundPropertyModel<FornecedorComponentesCriticosDTO> modelFornecedorComponentesCriticosDTO;
    private Table tblFornecedoresComponentesCriticos;
    private List<FornecedorComponentesCriticosDTO> fornecedorComponentesCriticosDTOList;
    private List<FornecedorComponentesCriticosDTO> fornecedorComponentesCriticosDTOListExcluir = new ArrayList<>();
    private FornecedorComponentesCriticosDTO fornecedorComponentesCriticosDTOEdicao;
    private InputField empresaIF;
    private InputField enderecoIF;
    private InputField etapaFabricacaoProcessoIF;

    public FornecedoresComponentesCriticosPanel(String id, RequerimentoVigilancia requerimentoVigilancia, boolean enabled) {
        super(id);
        this.dto = new FornecedorComponentesCriticosDTO();

        init(enabled);
    }

    private void init(boolean enabled){
        FornecedorComponentesCriticosDTO proxy = on(FornecedorComponentesCriticosDTO.class);
        form = new Form("form", modelFornecedorComponentesCriticosDTO = new CompoundPropertyModel(dto));

        containerFornecedorComponentesCriticos = new WebMarkupContainer("containerFornecedorComponentesCriticos");
        containerFornecedorComponentesCriticos.setOutputMarkupId(true);

        containerFornecedorComponentesCriticos.add(empresaIF = new InputField("empresa"));
        containerFornecedorComponentesCriticos.add(enderecoIF = new InputField("endereco"));
        containerFornecedorComponentesCriticos.add(etapaFabricacaoProcessoIF = new InputField("etapaFabricacaoProcesso"));

        containerFornecedorComponentesCriticos.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        containerFornecedorComponentesCriticos.add(tblFornecedoresComponentesCriticos = new Table("tblFornecedoresComponentesCriticos", getColumns(), getCollectionProvider()));
        tblFornecedoresComponentesCriticos.populate();

        form.add(containerFornecedorComponentesCriticos);
        form.add(new AjaxPreviewBlank());

        form.setEnabled(enabled);
        add(form);

    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        FornecedorComponentesCriticosDTO fornecedorComponentesCriticosDTO = modelFornecedorComponentesCriticosDTO.getObject();
        fornecedorComponentesCriticosDTOList.add(fornecedorComponentesCriticosDTO);
        target.add(form);
        modelFornecedorComponentesCriticosDTO.setObject(new FornecedorComponentesCriticosDTO());

    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        FornecedorComponentesCriticosDTO proxy = on(FornecedorComponentesCriticosDTO.class);

        columns.add(getActionColumn());
        columns.add(createColumn(bundle("empresa"), proxy.getEmpresa()));
        columns.add(createColumn(bundle("endereco"), proxy.getEndereco()));
        columns.add(createColumn(bundle("etapaFabricacaoProcesso"), proxy.getEtapaFabricacaoProcesso()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<FornecedorComponentesCriticosDTO>() {
            @Override
            public void customizeColumn(final FornecedorComponentesCriticosDTO rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<FornecedorComponentesCriticosDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, FornecedorComponentesCriticosDTO modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblFornecedoresComponentesCriticos, fornecedorComponentesCriticosDTOList, modelObject);
                        if(modelObject.getRequerimentoInspecaoSanitariaFornecedor() != null){
                            fornecedorComponentesCriticosDTOListExcluir.add(modelObject);
                        }
                    }
                });
                addAction(ActionType.EDITAR, rowObject, new IModelAction<FornecedorComponentesCriticosDTO>() {

                    @Override
                    public void action(AjaxRequestTarget target, FornecedorComponentesCriticosDTO modelObject) throws ValidacaoException, DAOException {
                        editar(target, modelObject);
                    }
                });
            }
        };
    }

    private void editar(AjaxRequestTarget target, FornecedorComponentesCriticosDTO modelObject) {
        limparForm(target);
        fornecedorComponentesCriticosDTOEdicao = (FornecedorComponentesCriticosDTO) SerializationUtils.clone(modelObject);
        modelFornecedorComponentesCriticosDTO.setObject(fornecedorComponentesCriticosDTOEdicao);
        fornecedorComponentesCriticosDTOList.remove(modelObject);
        target.add(empresaIF);
        target.add(enderecoIF);
        target.add(etapaFabricacaoProcessoIF);
    }

    private void limparForm(AjaxRequestTarget target){
        empresaIF.limpar(target);
        enderecoIF.limpar(target);
        etapaFabricacaoProcessoIF.limpar(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (fornecedorComponentesCriticosDTOList == null) {
                    fornecedorComponentesCriticosDTOList = new ArrayList<>();
                }
                return fornecedorComponentesCriticosDTOList;
            }
        };
    }

    public List<FornecedorComponentesCriticosDTO> getFornecedorComponentesCriticosDTOList() {
        return fornecedorComponentesCriticosDTOList;
    }

    public void setFornecedorComponentesCriticosDTOList(List<FornecedorComponentesCriticosDTO> fornecedorComponentesCriticosDTOList) {
        this.fornecedorComponentesCriticosDTOList = fornecedorComponentesCriticosDTOList;
    }

    public List<FornecedorComponentesCriticosDTO> getFornecedorComponentesCriticosDTOListExcluir() {
        return fornecedorComponentesCriticosDTOListExcluir;
    }
}

