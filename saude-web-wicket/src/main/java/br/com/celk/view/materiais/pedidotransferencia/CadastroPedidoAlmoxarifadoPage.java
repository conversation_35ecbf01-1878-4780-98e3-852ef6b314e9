package br.com.celk.view.materiais.pedidotransferencia;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IAction;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacao;
import br.com.celk.component.dialog.DlgConfirmacaoOk;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgVisualizarObservacao;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.selection.deprecated.MultiSelectionTableOld;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.cadastro.CadastroPage;
import br.com.celk.util.Coalesce;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.materiais.pedidotransferenciaitem.DlgDetalhesItemAlmoxarifado;
import br.com.celk.view.materiais.pedidotransferenciaitem.DlgDetalhesItemAlmoxarifadoKit;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto.QueryConsultaDominioProdutoDTOParam;
import br.com.ksisolucoes.bo.entradas.estoque.interfaces.facade.PedidoTransferenciaFacade;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.DTOPedidoTransferencia;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.DTOPedidoTransferenciaItem;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.OrigemProcessoPedido;
import br.com.ksisolucoes.bo.materiais.pedidotransferencia.PedidoHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaMaterial;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.*;
import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;
import org.apache.commons.collections.CollectionUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.FormComponent;
import org.apache.wicket.model.PropertyModel;
import org.apache.wicket.request.mapper.parameter.PageParameters;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class CadastroPedidoAlmoxarifadoPage extends CadastroPage<PedidoTransferencia> {

    private AutoCompleteConsultaEmpresa autoCompleteConsultaAlmoxarifado;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
//    private InputField txtPossuiEstoque;
    private InputField txtObservacao;
    private LongField txtQuantidade;
    private WebMarkupContainer containerKit;
    private WebMarkupContainer containerKitPedido;
    private WebMarkupContainer containerItem;
    private WebMarkupContainer containerDadosEstoqueItem;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;

    private Table<DTOPedidoTransferenciaItem> table;
    private List<DTOPedidoTransferenciaItem> itens = new ArrayList();
    private List<KitPedidoPaciente> kitPedidoList = new ArrayList();
    private List<UsuarioCadsusKit> kitPedidoPacienteList = new ArrayList();
    private Produto produto;
    private Long quantidade;
    private String observacao;
    private DlgConfirmacao dialogConfirmacaoAtualizarProduto;
    private InputField txtTotalItens;
    private Integer totalItens = 0;
    private DlgConfirmacaoOk dlgConfirmacao;
    private DlgVisualizarObservacao dlgVisualizarObservacao;
    private DlgDetalhesItemAlmoxarifado dlgDetalhesItemAlmoxarifado;
    private DlgDetalhesItemAlmoxarifadoKit dlgDetalhesItemAlmoxarifadoKit;
    private String validarEstoquePedidoAlmoxarifado;
    private MultiSelectionTableOld<KitPedidoPaciente> tblKitPedido;
    private MultiSelectionTableOld<UsuarioCadsusKit> tblKitPedidoPaciente;

    private List<KitPedidoPaciente> kitsUtilizadas = new ArrayList<KitPedidoPaciente>();

    private Empresa almoxarifado;
    private UsuarioCadsus usuarioCadsus;

    private boolean validarSaldoLimite;
    private Double saldoUnidade;
    private Double consumoUltimos90Dias;
    private Double consumoUltimos6Meses;
    private Double consumoUltimos30Dias;
    private Double quantidadeUltimoPedido;
    private Double saldoLimite;
    private DlgConfirmacaoSimNao dlgConfirmacaoAlmoxarifado;

    private boolean naoPermiteEnviarPedido;

    public CadastroPedidoAlmoxarifadoPage(PageParameters parameters) {
        super(parameters);
        carregaKitPaciente();
        habilitaCampoAlmoxarifado(null);
    }

    private void habilitaCampoAlmoxarifado(AjaxRequestTarget target) {
        if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
            autoCompleteConsultaAlmoxarifado.setEnabled(true);
        } else {
            autoCompleteConsultaAlmoxarifado.setEnabled(false);
        }
        if (target != null) {
            target.add(autoCompleteConsultaAlmoxarifado);
            autoCompleteConsultaAlmoxarifado.limpar(target);
        }
    }

    public CadastroPedidoAlmoxarifadoPage(PedidoTransferencia object) {
        super(object);
        try {
            almoxarifado = object.getEmpresaOrigem();
            carregarDados(true);
            carregaKitPaciente();
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    @Override
    public Class<PedidoTransferencia> getReferenceClass() {
        return PedidoTransferencia.class;
    }

    @Override
    public void init(Form form) {
        
        Usuario usuario = ApplicationSession.get().getSessaoAplicacao().<Usuario>getUsuario();
        if (!usuario.isNivelAdminOrMaster()) {
            naoPermiteEnviarPedido = isActionPermitted(Permissions.NÃO_PERMITE_ENVIAR_PEDIDO, CadastroPedidoAlmoxarifadoPage.class);
        } else {
            naoPermiteEnviarPedido = false;
        }

        if (naoPermiteEnviarPedido) {
            info(bundle("msgItensAdicionadosPedidoFicaraoSalvosParaSairPedidoSemEnviarCliqueBotaoVoltar"));
        } else {
            info(bundle("msgItensAdicionadosPedidoFicaraoSalvosParaSairPedidoSemEnviarCliqueBotaoVoltarParaEnviarPedidoAlmoxarifadoCliqueBotaoEnviar"));
        }

        try {
            if (RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("limitePedidosBranet"))) {
                if(! isActionPermitted(Permissions.APROVAR_PEDIDO_TRANSF)){
                    validarSaldoLimite = true;
                }
            }
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        try {
            if (RepositoryComponentDefault.SIM.equals(BOFactory.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("habilitarIntegracaoBranet"))) {

                info(bundle("msgIntegracaoBranetAtiva"));
            }
        } catch (DAOException e) {
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        try {
            validarEstoquePedidoAlmoxarifado = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.MATERIAIS).getParametro("ValidarEstoquePedidoAlmoxarifado");
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        removeDirtyForm();

        final PedidoTransferencia proxy = on(PedidoTransferencia.class);

        form.add(autoCompleteConsultaEmpresa = new AutoCompleteConsultaEmpresa(path(proxy.getEmpresaDestino())));
        autoCompleteConsultaEmpresa.setValidaUsuarioEmpresa(true);
        form.add(autoCompleteConsultaAlmoxarifado = new AutoCompleteConsultaEmpresa("almoxarifado", new PropertyModel<Empresa>(this, "almoxarifado"), true));
        form.add(getDropDownTipo(path(proxy.getTipo())));

        form.add(containerItem = new WebMarkupContainer("containerItem"));
        containerItem.setOutputMarkupId(true);
        containerItem.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto", new PropertyModel<Produto>(this, "produto")));
        autoCompleteConsultaProduto.setEmpresas(Arrays.asList(br.com.celk.system.session.ApplicationSession.get().getSession().<Empresa>getEmpresa()));
        autoCompleteConsultaProduto.setExibir(QueryConsultaDominioProdutoDTOParam.Exibir.SOMENTE_ATIVO);
        autoCompleteConsultaProduto.setIncluirInativos(false);
        containerItem.add(txtQuantidade = new LongField("quantidade", new PropertyModel<Long>(this, "quantidade")));
        containerItem.add(txtObservacao = new InputField("observacao", new PropertyModel<String>(this, "observacao")));

        form.add(containerDadosEstoqueItem = new WebMarkupContainer("containerDadosEstoqueItem"));
        containerDadosEstoqueItem.setOutputMarkupId(true);

        containerDadosEstoqueItem.add(new DisabledInputField("saldoUnidade", new PropertyModel<Produto>(this, "saldoUnidade")));
        containerDadosEstoqueItem.add(new DisabledInputField("consumoUltimos90Dias", new PropertyModel<Produto>(this, "consumoUltimos90Dias")));
        containerDadosEstoqueItem.add(new DisabledInputField("consumoUltimos30Dias", new PropertyModel<Produto>(this, "consumoUltimos30Dias")));
        containerDadosEstoqueItem.add(new DisabledInputField("quantidadeUltimoPedido", new PropertyModel<Produto>(this, "quantidadeUltimoPedido")));


        form.add(containerKit = new WebMarkupContainer("containerKit"));
        containerKit.setOutputMarkupId(true);
        containerKit.add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus("usuarioCadsus", new PropertyModel<UsuarioCadsus>(this, "usuarioCadsus"), false));

        form.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarAdicionar(target);
            }
        });

        form.add(new AbstractAjaxButton("btnAdicionarKit") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarKit(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(tblKitPedido = new MultiSelectionTableOld("tblKitPedido", getColumnsKitPedido(), getCollectionProviderKitPedido()));
        tblKitPedido.populate();
        tblKitPedido.setScrollY("208px");

        form.add(new AbstractAjaxButton("btnAdicionarKitPaciente") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                validarAdicionarKit(target);
            }
        }.setDefaultFormProcessing(false));

        form.add(tblKitPedidoPaciente = new MultiSelectionTableOld("tblKitPedidoPaciente", getColumnsKitPedidoPaciente(), getCollectionProviderKitPedidoPaciente()));
        tblKitPedidoPaciente.populate();
        tblKitPedidoPaciente.setScrollY("208px");

        form.add(new AbstractAjaxButton("btnLimpar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                limparItem(target);
            }
        });

        form.add(table = new Table("table", getColumns(), getCollectionProvider()));

        form.add(txtTotalItens = new DisabledInputField("totalItens", new PropertyModel(this, "totalItens"), Long.class));

        addModal(dlgConfirmacao = new DlgConfirmacaoOk(newModalId(), bundle("produtoSelecionadoNaoPossuiEstoqueNoAlmoxarifadoSelelcionado")) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaProduto.focus(target);
            }
        });

        autoCompleteConsultaUsuarioCadsus.add(new ConsultaListener<UsuarioCadsus>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                eventoConsultaUsuario(target, object);
            }
        });

        autoCompleteConsultaUsuarioCadsus.add(new RemoveListener<UsuarioCadsus>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, UsuarioCadsus object) {
                kitPedidoPacienteList = new ArrayList();
                target.add(tblKitPedidoPaciente);
                tblKitPedidoPaciente.populate();
                tblKitPedido.clearSelection(target);
            }
        });

        autoCompleteConsultaAlmoxarifado.add(new ConsultaListener<Empresa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                autoCompleteConsultaProduto.limpar(target);
//                txtPossuiEstoque.limpar(target);
                autoCompleteConsultaProduto.setEnabled(object != null);
                autoCompleteConsultaProduto.setEmpresas(Arrays.asList(object));
                autoCompleteConsultaProduto.focus(target);
            }
        });

        autoCompleteConsultaAlmoxarifado.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                autoCompleteConsultaProduto.limpar(target);
                autoCompleteConsultaProduto.setEnabled(false);
                autoCompleteConsultaAlmoxarifado.focus(target);
                target.add(autoCompleteConsultaProduto);
            }
        });

        if (autoCompleteConsultaEmpresa.getComponentValue() == null) {
            autoCompleteConsultaAlmoxarifado.setEnabled(false);
        }

        autoCompleteConsultaEmpresa.add(new ConsultaListener<Empresa>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Empresa object) {
                eventoConsultaEmpresa(target, object);
                habilitaCampoAlmoxarifado(target);
            }

        });

        autoCompleteConsultaEmpresa.add(new RemoveListener<Empresa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Empresa object) {
                habilitaCampoAlmoxarifado(target);
            }
        });

        autoCompleteConsultaProduto.add(new ConsultaListener<Produto>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Produto object) {
                try {

                    carregarDadosEstoqueItem(target, object, getForm().getModel().getObject().getEmpresaDestino());

                    if (RepositoryComponentDefault.SIM.equals(validarEstoquePedidoAlmoxarifado)) {
                        EstoqueEmpresa estoqueEmpresa = carregarEstoqueEmpresa(object, almoxarifado);
                        if (estoqueEmpresa.getEstoqueDisponivel().compareTo(0D) < 1) {
                            dlgConfirmacao.show(target);
                        }
                    }
                } catch (SGKException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        });

        autoCompleteConsultaProduto.add(new RemoveListener<Produto>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Produto object) {
                limparCamposDadosEstoqueItem(target);
            }
        });

        form.add(dialogConfirmacaoAtualizarProduto = new DlgConfirmacao("dialogConfirmacaoAtualizarProduto", bundle("produto_ja_adicionado_atualizar_quantidade")) {

            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                adicionar(target, validarProdutoAdicionado());
            }

            @Override
            public String getConfirmarLabel() {
                return bundle("sim");
            }

            @Override
            public String getFecharLabel() {
                return bundle("nao");
            }

        });

        if (!isEdicao()) {
            Empresa empresa = (Empresa) ApplicationSession.get().getSessaoAplicacao().getEmpresa();
            getForm().getModelObject().setEmpresaDestino(empresa);
            try {
                ajustarConsultaAlmoxarifado(empresa, null);
            } catch (DAOException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            } catch (ValidacaoException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        this.getBtnSalvar().add(new AttributeModifier("value", bundle("enviar")));

        if (naoPermiteEnviarPedido) {
            this.getBtnSalvar().setVisible(false);
        } else {
            this.getBtnSalvar().setVisible(true);
        }

//        addModal(dlgConfirmacaoAlmoxarifado = new DlgConfirmacao(newModalId()) {
//
//            @Override
//            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
//                salvar(target);
//            }
//        });

    }

    private void limparCamposDadosEstoqueItem(AjaxRequestTarget target) {
        saldoUnidade = null;
        consumoUltimos30Dias = null;
        consumoUltimos90Dias = null;
        quantidadeUltimoPedido = null;

        if(target != null) {
            target.add(containerDadosEstoqueItem);
        }
    }

    private void carregarDadosEstoqueItem(AjaxRequestTarget target, Produto produto, Empresa empresa) throws ValidacaoException, DAOException {

        saldoUnidade = EstoqueEmpresaHelper.getSaldoEmpresa(empresa, produto);

        consumoUltimos30Dias = EstoqueEmpresaHelper.getConsumoTrintaDias(empresa, produto);

        consumoUltimos90Dias = EstoqueEmpresaHelper.getConsumoNoventaDias(empresa, produto);

        consumoUltimos6Meses = EstoqueEmpresaHelper.getConsumoSeisMeses(empresa, produto);

        saldoLimite = PedidoLimiteHelper.getLimiteProduto(empresa,produto);

        // Campo Qtd Ultimo Pedido
        List<PedidoTransferenciaItem> pedidoTransferenciaItemList = LoadManager.getInstance(PedidoTransferenciaItem.class)
                .addProperty(VOUtils.montarPath(PedidoTransferenciaItem.PROP_QUANTIDADE))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN,
                                Arrays.asList( PedidoTransferenciaItem.StatusPedidoTransferenciaItem.PROCESSADO.value()
                                    , PedidoTransferenciaItem.StatusPedidoTransferenciaItem.RECEBIDO.value()
                                    , PedidoTransferenciaItem.StatusPedidoTransferenciaItem.SEPARANDO.value()))
                )
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_EMPRESA_DESTINO), empresa ))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PRODUTO), produto))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(PedidoTransferenciaItem.PROP_PEDIDO_TRANSFERENCIA, PedidoTransferencia.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, getForm().getModel().getObject().getCodigo()))
                .addSorter(new QueryCustom.QueryCustomSorter(PedidoTransferenciaItem.PROP_DATA_CADASTRO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                .start().getList();

        if(br.com.celk.util.CollectionUtils.isNotNullEmpty(pedidoTransferenciaItemList)) {
            quantidadeUltimoPedido = pedidoTransferenciaItemList.get(0).getQuantidade();
        }

        if(target != null) {
            target.add(containerDadosEstoqueItem);
        }
    }

    private DropDown<Long> getDropDownTipo(String id) {
        DropDown<Long> dropDown = new DropDown<>(id);
        dropDown.addChoice(PedidoTransferencia.Tipo.MENSAL.value(), PedidoTransferencia.Tipo.MENSAL.descricao());
        dropDown.addChoice(PedidoTransferencia.Tipo.COMPLEMENTAR.value(), PedidoTransferencia.Tipo.COMPLEMENTAR.descricao());
        return dropDown;
    }


    @Override
    public FormComponent getComponentRequestFocus() {
        return autoCompleteConsultaAlmoxarifado.getTxtDescricao().getTextField();
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        DTOPedidoTransferenciaItem proxy = on(DTOPedidoTransferenciaItem.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("produto"), proxy.getPedidoTransferenciaItem().getProduto().getDescricaoFormatado()));
        columns.add(createColumn(bundle("un"), proxy.getPedidoTransferenciaItem().getProduto().getUnidade().getUnidade()));
        columns.add(createColumn(bundle("quantidade"), proxy.getPedidoTransferenciaItem().getQuantidade()));
        columns.add(createColumn(bundle("fisico"), proxy.getEstoqueEmpresaDestino().getEstoqueFisico()));
        columns.add(createColumn(bundle("minimo"), proxy.getEstoqueEmpresaDestino().getEstoqueMinimo()));
        columns.add(createColumn(bundle("paciente"), proxy.getPedidoTransferenciaItem().getUsuarioCadsusKit().getUsuarioCadsus().getNomeSocial()));
        columns.add(createColumn(bundle("qtdUltimoPedido"), proxy.getPedidoTransferenciaItem().getQuantidadeUltimoPedido()));
        columns.add(createColumn(bundle("consumoTrintaDiasAbv"), proxy.getPedidoTransferenciaItem().getConsumoTrintaDias()));
        columns.add(createColumn(bundle("consumoNoventaDiasAbv"), proxy.getPedidoTransferenciaItem().getConsumoNoventaDias()));
        columns.add(createColumn(bundle("saldoAtual"), proxy.getPedidoTransferenciaItem().getSaldoEmpresa()));

        return columns;
    }

    private List<IColumn> getColumnsKitPedido() {
        List<IColumn> columns = new ArrayList();

        KitPedidoPaciente proxy = on(KitPedidoPaciente.class);

        columns.add(getCustomColumnKitPedido());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));

        return columns;
    }

    private List<IColumn> getColumnsKitPedidoPaciente() {
        List<IColumn> columns = new ArrayList();

        UsuarioCadsusKit proxy = on(UsuarioCadsusKit.class);

        columns.add(getCustomColumnKitPedidoPaciente());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("paciente"), proxy.getUsuarioCadsus().getDescricaoSocialFormatado()));

        return columns;
    }

    private IColumn getCustomColumnKitPedido() {
        return new MultipleActionCustomColumn<KitPedidoPaciente>() {
            @Override
            public void customizeColumn(KitPedidoPaciente rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<KitPedidoPaciente>() {
                    @Override
                    public void action(AjaxRequestTarget target, KitPedidoPaciente modelObject) throws ValidacaoException, DAOException {
                        initViewDlgKitPedido(target, modelObject);
                    }
                });
            };
        };
    }

    private IColumn getCustomColumnKitPedidoPaciente() {
        return new MultipleActionCustomColumn<UsuarioCadsusKit>() {
            @Override
            public void customizeColumn(UsuarioCadsusKit rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<UsuarioCadsusKit>() {
                    @Override
                    public void action(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
                        initViewDlgUsuarioKit(target, modelObject, true);
                    }
                });

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<UsuarioCadsusKit>() {
                    @Override
                    public void action(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
                        initViewDlgUsuarioKit(target, modelObject, false);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<UsuarioCadsusKit>() {
                    @Override
                    public void action(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
                        removerUsuarioCadsusKit(target, modelObject);
                    }
                });
            };
        };
    }

    private void removerUsuarioCadsusKit(AjaxRequestTarget target, UsuarioCadsusKit modelObject) throws ValidacaoException, DAOException {
        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).deletarUsuarioCadsusKit(modelObject);
        kitPedidoPacienteList.remove(modelObject);
        tblKitPedidoPaciente.update(target);
        tblKitPedidoPaciente.populate();
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<DTOPedidoTransferenciaItem>() {
            @Override
            public void customizeColumn(final DTOPedidoTransferenciaItem rowObject) {
                addAction(ActionType.REMOVER, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        remover(target, rowObject);
                    }
                });

                addAction(ActionType.CONSULTAR, new IAction() {
                    @Override
                    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        initViewDlgVisualizarObservacao(target, rowObject.getPedidoTransferenciaItem().getObservacao());
                    }
                });
            };
        };
    }

    private void initViewDlgVisualizarObservacao(AjaxRequestTarget target, String observacao) {
        if (dlgVisualizarObservacao == null) {
            addModal(target, dlgVisualizarObservacao = new DlgVisualizarObservacao(newModalId()));
        }
        dlgVisualizarObservacao.show(target, observacao);
    }

    private void initViewDlgKitPedido(AjaxRequestTarget target, KitPedidoPaciente kit) {
        if (dlgDetalhesItemAlmoxarifado == null) {
            addModal(target, dlgDetalhesItemAlmoxarifado = new DlgDetalhesItemAlmoxarifado(newModalId()));
        }
        dlgDetalhesItemAlmoxarifado.show(target, kit);
    }

    private void initViewDlgUsuarioKit(AjaxRequestTarget target, UsuarioCadsusKit kit, boolean edicao) {
        if (dlgDetalhesItemAlmoxarifadoKit == null) {
            addModal(target, dlgDetalhesItemAlmoxarifadoKit = new DlgDetalhesItemAlmoxarifadoKit(newModalId()) {
                @Override
                public void onSalvar(AjaxRequestTarget target, List<UsuarioCadsusKitItem> usuarioCadsusKitItemList) throws ValidacaoException, DAOException {
                    BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cadastrarUsuarioCadsusKitItens(usuarioCadsusKitItemList);
                    CadastroPedidoAlmoxarifadoPage.this.info(target, Bundle.getStringApplication("msg_registro_salvo"));
                }
            });
        }
        dlgDetalhesItemAlmoxarifadoKit.show(target, kit, edicao);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return itens;
            }
        };
    }

    private CollectionProvider getCollectionProviderKitPedido() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return kitPedidoList;
            }
        };
    }


    private CollectionProvider getCollectionProviderKitPedidoPaciente() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return kitPedidoPacienteList;
            }
        };
    }

    private void eventoConsultaUsuario(AjaxRequestTarget target, UsuarioCadsus object) {
        List<UsuarioCadsusKit> itensKit = LoadManager.getInstance(UsuarioCadsusKit.class)
                .addProperties(new HQLProperties(UsuarioCadsusKit.class).getProperties())
                .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusKit.PROP_USUARIO_CADSUS), object))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(UsuarioCadsusKit.PROP_DESCRICAO), QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(itensKit)) {
            kitPedidoPacienteList = new ArrayList();
            kitPedidoPacienteList.addAll(itensKit);
            target.add(tblKitPedidoPaciente);
            tblKitPedidoPaciente.populate();
        }
    }

    private void eventoConsultaEmpresa(AjaxRequestTarget target, Empresa object) {
//        autoCompleteConsultaProduto.setEmpresa(object);
        containerItem.setEnabled(object != null);

        autoCompleteConsultaProduto.limpar(target);
//        txtPossuiEstoque.limpar(target);
        txtQuantidade.limpar(target);

        target.add(containerItem);

        if (object != null) {
            target.focusComponent(autoCompleteConsultaProduto.getTxtDescricao().getTextField());

        }
        try {
            ajustarConsultaAlmoxarifado(object, target);
        } catch (SGKException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
    }

    private void ajustarConsultaAlmoxarifado(Empresa empresa, AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (empresa != null) {
            EmpresaMaterial proxy = on(EmpresaMaterial.class);
            EmpresaMaterial empresaMaterial = LoadManager.getInstance(EmpresaMaterial.class)
                    .addProperty(path(proxy.getPedidoOdonto()))
                    .addProperty(path(proxy.getPedidoMedicamento()))
                    .addProperty(path(proxy.getPedidoMaterial()))
                    .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getCodigo()), empresa.getCodigo()))
                    .start().getVO();

            autoCompleteConsultaAlmoxarifado.setEnabled(false);
            autoCompleteConsultaProduto.setEnabled(false);
            ArrayList<Long> tiposEstabelecimento = new ArrayList();
            if (RepositoryComponentDefault.SIM.equals(empresaMaterial.getPedidoMaterial())) {
                tiposEstabelecimento.add(Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL);
                autoCompleteConsultaAlmoxarifado.setEnabled(true);
            }
            if (RepositoryComponentDefault.SIM.equals(empresaMaterial.getPedidoMedicamento())) {
                tiposEstabelecimento.add(Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO);
                autoCompleteConsultaAlmoxarifado.setEnabled(true);
            }
            if (RepositoryComponentDefault.SIM.equals(empresaMaterial.getPedidoOdonto())) {
                tiposEstabelecimento.add(Empresa.TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO);
                autoCompleteConsultaAlmoxarifado.setEnabled(true);
            }

            autoCompleteConsultaAlmoxarifado.setTiposEstabelecimento(tiposEstabelecimento);

            if (autoCompleteConsultaEmpresa.getComponentValue() != null) {
                autoCompleteConsultaAlmoxarifado.setValidarAlmoxarifadosPermitidos(true);
                autoCompleteConsultaAlmoxarifado.setEmpresaPedidoAlmoxarifado((Empresa) autoCompleteConsultaEmpresa.getComponentValue());
            }

        } else {
            autoCompleteConsultaAlmoxarifado.setEnabled(false);
            autoCompleteConsultaProduto.setEnabled(false);
        }
        if (target != null) {
            autoCompleteConsultaAlmoxarifado.limpar(target);
            autoCompleteConsultaProduto.limpar(target);
//            txtPossuiEstoque.limpar(target);
        }
    }

    private void adicionarKit(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if(usuarioCadsus == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_paciente_kit"));
        }
        List<KitPedidoPaciente> selectedObjects = tblKitPedido.getSelectedObjects();
        if(CollectionUtils.isEmpty(selectedObjects)){
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_pelo_menos_um_registro"));
        }else {
            kitPedidoPacienteList.addAll(BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cadastrarUsuarioCadsusKit(selectedObjects, usuarioCadsus));
            kitsUtilizadas = (List<KitPedidoPaciente>) CollectionUtils.subtract(kitsUtilizadas, kitPedidoPacienteList);
            tblKitPedido.clearSelection(target);
            tblKitPedidoPaciente.update(target);
            tblKitPedidoPaciente.populate();
        }
    }
    private void validarAdicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        validarSelecaoEmpresa();

        if (produto == null) {
            throw new ValidacaoException(bundle("informe_produto"));
        }

        if (quantidade == null) {
            throw new ValidacaoException(bundle("informe_quantidade"));
        }

        PedidoHelper.validarCadastroConfiguracaoPedido(getForm().getModelObject().getEmpresaDestino(), produto);

        boolean exists = LoadManager.getInstance(EstoqueEmpresa.class)
                .addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), produto))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), getForm().getModelObject().getEmpresaDestino()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                .exists();

        if (!exists) {
            throw new ValidacaoException(bundle("msgProdutoInativoUnidadeX", getForm().getModelObject().getEmpresaDestino().getDescricao()));
        }

//        if (!SituacaoEstoque.POSSUI_ESTOQUE.equals(produto.getSituacaoEstoque())) {
//            throw new ValidacaoException(BundleManager.getString("naoPermitidoAdicionarProdutosSemEstoque"));
//        }
        DTOPedidoTransferenciaItem dtoAdicionado = validarProdutoAdicionado();

        if (dtoAdicionado == null) {

            adicionar(target, dtoAdicionado);
        } else {
            dialogConfirmacaoAtualizarProduto.show(target);
        }
    }

    private void validarAdicionarKit(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        validarSelecaoEmpresa();

        if(almoxarifado == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_informe_almoxarifado"));
        }
        if(usuarioCadsus == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_adicione_paciente_kit"));
        }

        List<UsuarioCadsusKit> selectedObjects = tblKitPedidoPaciente.getSelectedObjects();
        if(CollectionUtils.isEmpty(selectedObjects)){
            throw new ValidacaoException(Bundle.getStringApplication("msg_selecione_pelo_menos_um_registro"));
        }else {
            for (UsuarioCadsusKit selectedObject : selectedObjects) {
                List<UsuarioCadsusKitItem> itensKit = LoadManager.getInstance(UsuarioCadsusKitItem.class)
                        .addProperties(new HQLProperties(UsuarioCadsusKitItem.class).getProperties())
                        .addProperties(new HQLProperties(UsuarioCadsusKit.class, VOUtils.montarPath(UsuarioCadsusKitItem.PROP_USUARIO_CADSUS_KIT)).getProperties())
                        .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(UsuarioCadsusKitItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_USUARIO_CADSUS)).getProperties())
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusKitItem.PROP_USUARIO_CADSUS_KIT), selectedObject))
                        .start().getList();
                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(itensKit)) {
                    for (UsuarioCadsusKitItem usuarioCadsusKitItem : itensKit) {
                        if (usuarioCadsusKitItem.getQuantidade() == null) {
                            throw new ValidacaoException(bundle("informeQuantidadeKitXprodutoX", selectedObject.getDescricao(), usuarioCadsusKitItem.getProduto().getDescricao()));
                        }

                        boolean exists = LoadManager.getInstance(EstoqueEmpresa.class)
                                .addProperties(EstoqueEmpresa.PROP_FLAG_ATIVO)
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_PRODUTO), usuarioCadsusKitItem.getProduto()))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_ID, EstoqueEmpresaPK.PROP_EMPRESA), getForm().getModelObject().getEmpresaDestino()))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EstoqueEmpresa.PROP_FLAG_ATIVO), RepositoryComponentDefault.SIM))
                                .exists();

                        if (!exists) {
                            throw new ValidacaoException(bundle("msgProdutoXKitXInativoUnidadeX", usuarioCadsusKitItem.getProduto().getDescricao(), selectedObject.getDescricao(), getForm().getModelObject().getEmpresaDestino().getDescricao()));
                        }

                        PedidoHelper.validarCadastroConfiguracaoPedido(getForm().getModelObject().getEmpresaDestino(), usuarioCadsusKitItem.getProduto());
                        adicionarKit(target, validarProdutoAdicionadoKit(usuarioCadsusKitItem.getProduto()), usuarioCadsusKitItem);
                    }
                    salvarProdutosParaEnviar();
                    itens.removeAll(itens);
                    carregarDados(false);
                } else {
                    throw new ValidacaoException(bundle("msg_kit_sem_itens", selectedObject.getDescricao()));
                }
            }
            CadastroPedidoAlmoxarifadoPage.this.info(target, Bundle.getStringApplication("msg_itens_inseridos_com_sucesso"));
        }
        tblKitPedidoPaciente.clearSelection(target);
    }

    private void adicionarKit(AjaxRequestTarget target, DTOPedidoTransferenciaItem dtoPedidoTransferenciaItem, UsuarioCadsusKitItem usuarioCadsusKitItem) throws ValidacaoException, DAOException {
        if (dtoPedidoTransferenciaItem == null) {
            dtoPedidoTransferenciaItem = new DTOPedidoTransferenciaItem();

            PedidoTransferenciaItem pedidoTransferenciaItem = new PedidoTransferenciaItem();

            carregarDadosEstoqueItem(null, usuarioCadsusKitItem.getProduto(), getForm().getModel().getObject().getEmpresaDestino());

            pedidoTransferenciaItem.setProduto(usuarioCadsusKitItem.getProduto());
            pedidoTransferenciaItem.setQuantidade(usuarioCadsusKitItem.getQuantidade());
            pedidoTransferenciaItem.setSaldoEmpresa(saldoUnidade);
            pedidoTransferenciaItem.setConsumoTrintaDias(consumoUltimos30Dias);
            pedidoTransferenciaItem.setConsumoNoventaDias(consumoUltimos90Dias);
            pedidoTransferenciaItem.setConsumoSeisMeses(consumoUltimos6Meses);
            pedidoTransferenciaItem.setQuantidadeUltimoPedido(quantidadeUltimoPedido);

            limparCamposDadosEstoqueItem(null);

            if(usuarioCadsus != null && usuarioCadsusKitItem.getUsuarioCadsusKit() != null) {
                usuarioCadsusKitItem.getUsuarioCadsusKit().setUsuarioCadsus(usuarioCadsus);
            }
            pedidoTransferenciaItem.setUsuarioCadsusKit(usuarioCadsusKitItem.getUsuarioCadsusKit());
            dtoPedidoTransferenciaItem.setPedidoTransferenciaItem(pedidoTransferenciaItem);
            dtoPedidoTransferenciaItem.setEstoqueEmpresaDestino(carregarEstoqueEmpresa(usuarioCadsusKitItem.getProduto(), getForm().getModelObject().getEmpresaDestino()));
            itens.add(dtoPedidoTransferenciaItem);
        } else {
            dtoPedidoTransferenciaItem.getPedidoTransferenciaItem().setQuantidade(dtoPedidoTransferenciaItem.getPedidoTransferenciaItem().getQuantidade() + usuarioCadsusKitItem.getQuantidade());

            carregarDadosEstoqueItem(target, usuarioCadsusKitItem.getProduto(), getForm().getModel().getObject().getEmpresaDestino());
            dtoPedidoTransferenciaItem.getPedidoTransferenciaItem().setSaldoEmpresa(saldoUnidade);
            dtoPedidoTransferenciaItem.getPedidoTransferenciaItem().setConsumoTrintaDias(consumoUltimos30Dias);
            dtoPedidoTransferenciaItem.getPedidoTransferenciaItem().setConsumoNoventaDias(consumoUltimos90Dias);
            dtoPedidoTransferenciaItem.getPedidoTransferenciaItem().setConsumoSeisMeses(consumoUltimos6Meses);
            dtoPedidoTransferenciaItem.getPedidoTransferenciaItem().setQuantidadeUltimoPedido(quantidadeUltimoPedido);
            limparCamposDadosEstoqueItem(target);
        }

        limparItem(target);

        autoCompleteConsultaAlmoxarifado.setEnabled(false);
        autoCompleteConsultaEmpresa.setEnabled(false);

        totalItens = itens.size();

        table.populate(target);
        target.add(autoCompleteConsultaAlmoxarifado);
        target.add(autoCompleteConsultaEmpresa);
        target.add(txtTotalItens);
    }

    private void adicionar(AjaxRequestTarget target, DTOPedidoTransferenciaItem dto) throws DAOException, ValidacaoException {
        if (dto == null) {
            dto = new DTOPedidoTransferenciaItem();

            PedidoTransferenciaItem pedidoTransferenciaItem = new PedidoTransferenciaItem();

            pedidoTransferenciaItem.setProduto(produto);
            pedidoTransferenciaItem.setQuantidade(Coalesce.asLong(quantidade).doubleValue());
            pedidoTransferenciaItem.setObservacao(observacao);
            pedidoTransferenciaItem.setSaldoEmpresa(saldoUnidade);
            pedidoTransferenciaItem.setConsumoTrintaDias(consumoUltimos30Dias);
            pedidoTransferenciaItem.setConsumoNoventaDias(consumoUltimos90Dias);
            pedidoTransferenciaItem.setConsumoSeisMeses(consumoUltimos6Meses);
            pedidoTransferenciaItem.setQuantidadeUltimoPedido(quantidadeUltimoPedido);

            dto.setPedidoTransferenciaItem(pedidoTransferenciaItem);
            dto.setEstoqueEmpresaDestino(carregarEstoqueEmpresa(produto, getForm().getModelObject().getEmpresaDestino()));
            if(validarSaldoLimite && Coalesce.asDouble(dto.getPedidoTransferenciaItem().getQuantidade()) > Coalesce.asDouble(dto.getSaldoLimite())){
                if(Coalesce.asString(dto.getPedidoTransferenciaItem().getObservacao()).isEmpty()){
                    throw new ValidacaoException(bundle("msg_obrigatorio_informar_observacao_saldo_limite_pedido_transf"));
                }
            }
            itens.add(dto);
        } else {
            if(validarSaldoLimite && Coalesce.asLong(quantidade).doubleValue() > Coalesce.asDouble(dto.getSaldoLimite())){
                if(Coalesce.asString(observacao).isEmpty()){
                    throw new ValidacaoException(bundle("msg_obrigatorio_informar_observacao_saldo_limite_pedido_transf"));
                }
            }
            dto.getPedidoTransferenciaItem().setQuantidade(Coalesce.asLong(quantidade).doubleValue());

            dto.getPedidoTransferenciaItem().setSaldoEmpresa(saldoUnidade);
            dto.getPedidoTransferenciaItem().setConsumoTrintaDias(consumoUltimos30Dias);
            dto.getPedidoTransferenciaItem().setConsumoNoventaDias(consumoUltimos90Dias);
            dto.getPedidoTransferenciaItem().setConsumoSeisMeses(consumoUltimos6Meses);
            dto.getPedidoTransferenciaItem().setObservacao(observacao);
            dto.getPedidoTransferenciaItem().setQuantidadeUltimoPedido(quantidadeUltimoPedido);
        }
        if(validarSaldoLimite && Coalesce.asDouble(dto.getPedidoTransferenciaItem().getQuantidade()) > Coalesce.asDouble(dto.getSaldoLimite())){
            dto.getPedidoTransferenciaItem().setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.PENDENTE_APROVACAO.value());
        }else {
            dto.getPedidoTransferenciaItem().setStatus(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.ABERTO.value());
        }
        
        salvarProdutosParaEnviar();

        limparItem(target);

        autoCompleteConsultaAlmoxarifado.setEnabled(false);
        autoCompleteConsultaEmpresa.setEnabled(false);

        totalItens = itens.size();

        table.populate(target);
        target.add(autoCompleteConsultaAlmoxarifado);
        target.add(autoCompleteConsultaEmpresa);
        target.add(txtTotalItens);
        itens.removeAll(itens);
        carregarDados(false);
        info(target, bundle("registroSalvoSucesso"));
    }

    private void salvarProdutosParaEnviar() throws ValidacaoException, DAOException {
        if (itens.isEmpty()) {
            throw new ValidacaoException(bundle("informe_os_itens"));
        }

        PedidoTransferencia pedidoAtualizado;
        if (getForm().getModelObject().getCodigo() != null) {
            pedidoAtualizado = LoadManager.getInstance(PedidoTransferencia.class)
                    .addProperties(new HQLProperties(PedidoTransferencia.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_CODIGO, getForm().getModelObject().getCodigo()))
                    .start().getVO();

        } else {
            pedidoAtualizado = new PedidoTransferencia();
            Empresa empresa = (Empresa) ApplicationSession.get().getSessaoAplicacao().getEmpresa();
            getForm().getModelObject().setEmpresaDestino(empresa);
            pedidoAtualizado.setEmpresaDestino(empresa);
        }

        DTOPedidoTransferencia dTOPedidoTransferencia = new DTOPedidoTransferencia();

        pedidoAtualizado.setEmpresaOrigem(almoxarifado);
        pedidoAtualizado.setTipo(getForm().getModelObject().getTipo());


        pedidoAtualizado.setStatusSeparacao(PedidoTransferencia.STATUS_SEPARACAO_AGUARDANDO_ENVIO);

        dTOPedidoTransferencia.setPedidoTransferencia(pedidoAtualizado);
        dTOPedidoTransferencia.setItens(itens);
        
        boolean pedidoPendenteAprovacao = false;
        if(validarSaldoLimite){
            for (DTOPedidoTransferenciaItem dto : itens) {
                if(PedidoTransferenciaItem.StatusPedidoTransferenciaItem.PENDENTE_APROVACAO.value().equals(dto.getPedidoTransferenciaItem().getStatus())){
                    pedidoPendenteAprovacao = true;
                    break;
                }
            }
        }
        if(pedidoPendenteAprovacao){
            dTOPedidoTransferencia.getPedidoTransferencia().setStatus(PedidoTransferencia.STATUS_PENDENTE_APROVACAO);
        }else{
            dTOPedidoTransferencia.getPedidoTransferencia().setStatus(PedidoTransferencia.STATUS_ABERTO);
        }

        pedidoAtualizado = BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cadastrarPedidoTransferencia(dTOPedidoTransferencia, OrigemProcessoPedido.LANCAMENTO_WEB, false);

        getForm().setModelObject(pedidoAtualizado);
    }

    private void validarSelecaoEmpresa() throws ValidacaoException {
        if (getForm().getModelObject().getEmpresaDestino() == null) {
            throw new ValidacaoException(bundle("informe_unidade"));
        }
    }

    private void remover(AjaxRequestTarget target, DTOPedidoTransferenciaItem item) throws ValidacaoException, DAOException {
        for (int i = 0; i < itens.size(); i++) {
            if (itens.get(i) == item) {
                BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).cancelarPedidoTransferenciaItem(itens.get(i).getPedidoTransferenciaItem().getCodigo(), OrigemProcessoPedido.LANCAMENTO_WEB, null);
                itens.remove(i);
                break;
            }
        }

        totalItens = itens.size();

        table.update(target);
        target.add(txtTotalItens);
    }

    private void limparItem(AjaxRequestTarget target) {
        autoCompleteConsultaProduto.limpar(target);
//        txtPossuiEstoque.limpar(target);
        txtQuantidade.limpar(target);
        txtObservacao.limpar(target);
        autoCompleteConsultaProduto.focus(target);

        limparCamposDadosEstoqueItem(target);
    }

    @Override
    public Object salvar(PedidoTransferencia pedidoTransferencia) throws DAOException, ValidacaoException {
        if (itens.isEmpty()) {
            throw new ValidacaoException(bundle("informe_os_itens"));
        }
        if (getForm().getModelObject() == null || getForm().getModelObject().getCodigo() == null) {
            throw new ValidacaoException(bundle("msgNaoFoiOossivelEnviarPedido"));
        }

        PedidoTransferencia pedidoAtualizado = LoadManager.getInstance(PedidoTransferencia.class)
                .addProperties(new HQLProperties(PedidoTransferencia.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, PedidoTransferencia.PROP_EMPRESA_DESTINO).getProperties())
                .addProperties(new HQLProperties(Empresa.class, PedidoTransferencia.PROP_EMPRESA_ORIGEM).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(PedidoTransferencia.PROP_CODIGO, getForm().getModelObject().getCodigo()))
                .start().getVO();

        pedidoAtualizado.setStatusSeparacao(PedidoTransferencia.STATUS_SEPARACAO_NAO);
        BOFactoryWicket.getBO(PedidoTransferenciaFacade.class).salvarPedidoTransferencia(pedidoAtualizado, PedidoTransferencia.STATUS_ABERTO, true);

        return pedidoAtualizado;
    }

    @Override
    public Class getResponsePage() {
        return ConsultaPedidoAlmoxarifadoPage.class;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("cadastro_pedido_almoxarifado");
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    private void carregarDados(boolean exibeTotal) throws DAOException, ValidacaoException {
        autoCompleteConsultaEmpresa.setEnabled(false);
        autoCompleteConsultaAlmoxarifado.setEnabled(false);
        autoCompleteConsultaProduto.setEnabled(true);
        autoCompleteConsultaProduto.setEmpresas(Arrays.asList(almoxarifado));

        PedidoTransferenciaItem proxy = on(PedidoTransferenciaItem.class);
        List<PedidoTransferenciaItem> itensCarregados = LoadManager.getInstance(PedidoTransferenciaItem.class)
                .addProperties(new HQLProperties(PedidoTransferenciaItem.class).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsusKit.class, VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT)).getProperties())
                .addProperties(new HQLProperties(UsuarioCadsus.class, VOUtils.montarPath(PedidoTransferenciaItem.PROP_USUARIO_CADSUS_KIT, UsuarioCadsusKit.PROP_USUARIO_CADSUS)).getProperties())
                .addProperty(path(proxy.getProduto().getCodigo()))
                .addProperty(path(proxy.getProduto().getReferencia()))
                .addProperty(path(proxy.getProduto().getDescricao()))
                .addProperty(path(proxy.getProduto().getUnidade().getCodigo()))
                .addProperty(path(proxy.getProduto().getUnidade().getUnidade()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getPedidoTransferencia()), getForm().getModelObject()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getStatus()), BuilderQueryCustom.QueryParameter.DIFERENTE, PedidoTransferenciaItem.StatusPedidoTransferenciaItem.CANCELADO.value()))
                .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getItem())))
                .start().getList();

        for (PedidoTransferenciaItem pedidoTransferenciaItem : itensCarregados) {
            EstoqueEmpresa estoqueEmpresa = carregarEstoqueEmpresa(pedidoTransferenciaItem.getProduto(), getForm().getModelObject().getEmpresaDestino());
            pedidoTransferenciaItem.getProduto().setEstoqueEmpresa(estoqueEmpresa);
            DTOPedidoTransferenciaItem dTOPedidoTransferenciaItem = new DTOPedidoTransferenciaItem();
            dTOPedidoTransferenciaItem.setPedidoTransferenciaItem(pedidoTransferenciaItem);
            dTOPedidoTransferenciaItem.setUsuarioCadsusKit(pedidoTransferenciaItem.getUsuarioCadsusKit());
            dTOPedidoTransferenciaItem.setEstoqueEmpresaDestino(estoqueEmpresa);
            itens.add(dTOPedidoTransferenciaItem);
            if (exibeTotal) {
                totalItens++;
            }
        }
        table.populate();
    }

    private void carregaKitPaciente() {
        List<KitPedidoPaciente> itensKit = LoadManager.getInstance(KitPedidoPaciente.class)
                .addProperties(new HQLProperties(KitPedidoPaciente.class).getProperties())
                .addSorter(new QueryCustom.QueryCustomSorter(KitPedidoPaciente.PROP_DESCRICAO, QueryCustom.QueryCustomSorter.CRESCENTE))
                .start().getList();
        kitPedidoList = new ArrayList<KitPedidoPaciente>();
        for (KitPedidoPaciente kitPedidoPaciente : itensKit) {
            kitPedidoList.add(kitPedidoPaciente);
        }
    }

    private EstoqueEmpresa carregarEstoqueEmpresa(Produto produto, Empresa empresaDestino) throws DAOException, ValidacaoException {
        EstoqueEmpresa proxy = on(EstoqueEmpresa.class);
        return LoadManager.getInstance(EstoqueEmpresa.class)
                .addProperty(path(proxy.getEstoqueFisico()))
                .addProperty(path(proxy.getEstoqueMinimo()))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getProduto()), produto))
                .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getId().getEmpresa()), empresaDestino))
                .start().getVO();
    }

    private DTOPedidoTransferenciaItem validarProdutoAdicionado() throws ValidacaoException {
        for (DTOPedidoTransferenciaItem dTOPedidoTransferenciaItem : itens) {
            if (dTOPedidoTransferenciaItem.getPedidoTransferenciaItem().getProduto().equals(produto) && dTOPedidoTransferenciaItem.getPedidoTransferenciaItem().getUsuarioCadsusKit() == null) {
                return dTOPedidoTransferenciaItem;
            }
        }
        return null;
    }

    private DTOPedidoTransferenciaItem validarProdutoAdicionadoKit(Produto produto) throws ValidacaoException {
        for (DTOPedidoTransferenciaItem dTOPedidoTransferenciaItem : itens) {
            if(dTOPedidoTransferenciaItem.getPedidoTransferenciaItem() == null || dTOPedidoTransferenciaItem.getPedidoTransferenciaItem().getUsuarioCadsusKit() == null) {
                return null;
            }
            if (dTOPedidoTransferenciaItem.getPedidoTransferenciaItem().getProduto().equals(produto) && dTOPedidoTransferenciaItem.getPedidoTransferenciaItem().getUsuarioCadsusKit().getUsuarioCadsus().equals(usuarioCadsus)) {
                return dTOPedidoTransferenciaItem;
            }
        }
        return null;
    }
}
