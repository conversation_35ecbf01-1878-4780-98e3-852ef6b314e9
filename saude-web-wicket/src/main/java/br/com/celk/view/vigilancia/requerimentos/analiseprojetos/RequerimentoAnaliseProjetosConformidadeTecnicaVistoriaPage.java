package br.com.celk.view.vigilancia.requerimentos.analiseprojetos;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.button.VoltarButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dirtyforms.button.SubmitButton;
import br.com.celk.component.dirtyforms.button.action.ISubmitAction;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.table.column.TextColumn;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.component.utils.ComponentUtils;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.vigilancia.estabelecimento.autocomplete.AutoCompleteConsultaEstabelecimento;
import br.com.celk.view.vigilancia.faturamento.autocomplete.AutoCompleteConsultaAtividadesVigilancia;
import br.com.celk.view.vigilancia.pessoa.DlgCadastroVigilanciaPessoa;
import br.com.celk.view.vigilancia.pessoa.autocomplete.AutoCompleteConsultaVigilanciaPessoa;
import br.com.celk.view.vigilancia.requerimentos.RequerimentosPage;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlConsultaRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.requerimentos.panel.PnlRequerimentoVigilanciaAnexo;
import br.com.celk.view.vigilancia.tipoprojetovigilancia.autocomplete.AutoCompleteConsultaTipoProjetoVigilancia;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.vigilancia.interfaces.FinanceiroVigilanciaHelper;
import br.com.ksisolucoes.bo.vigilancia.interfaces.dto.*;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaFacade;
import br.com.ksisolucoes.bo.vigilancia.interfaces.facade.VigilanciaReportFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.faturamento.lancamento.LancamentoAtividadesVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.taxa.Taxa;
import br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice;
import ch.lambdaj.Lambda;
import org.apache.wicket.Component;
import org.apache.wicket.Page;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.ComponentTag;
import org.apache.wicket.markup.MarkupStream;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Private
public class RequerimentoAnaliseProjetosConformidadeTecnicaVistoriaPage extends BasePage {

    private Form<RequerimentoVistoriaPBAConformidadeTecnicaDTO> form;
    private TipoSolicitacao tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura;
    private AutoCompleteConsultaEstabelecimento autoCompleteConsultaEstabelecimento;
    private AutoCompleteConsultaTipoProjetoVigilancia autoCompleteConsultaTipoProjetoVigilancia;
    private DisabledInputField<String> txtCnpjCpfFormatado;
    private DisabledInputField<String> txtFantasia;
    private DisabledInputField<String> txtDescricaoEndereco;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList;
    private DlgImpressaoObject<RequerimentoVistoriaPBAConformidadeTecnicaDTO> dlgConfirmacaoImpressao;
    private WebMarkupContainer containerTipoProjeto;
    private Label lbValorTotal;
    private String valorTotal;
    private DoubleField txtArea;
    private ConfiguracaoVigilancia configuracaoVigilancia = null;
    private Class clazz;

    private WebMarkupContainer containerDados;
    private WebMarkupContainer containerPessoa;
    private WebMarkupContainer containerEstabelecimento;
    private DropDown dropDownTipoPessoa;
    private AutoCompleteConsultaVigilanciaPessoa autoCompleteConsultaVigilanciaPessoa;
    private DlgCadastroVigilanciaPessoa dlgCadastroVigilanciaPessoa;
    private DisabledInputField<String> txtNomeFantasia;
    private DisabledInputField<String> txtCpfFormatado;
    private DisabledInputField<String> txtEmail;
    private DropDown<Long> dropDownEstaConforme;

    private AtividadesVigilancia atividade;
    private AutoCompleteConsultaAtividadesVigilancia autoCompleteConsultaAtividadesVigilancia;
    private Table<AtividadesVigilancia> tblAtividade;
    private List<AtividadesVigilancia> atividadeList = new ArrayList();

    private Profissional fiscal;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private Table<Profissional> tblFiscal;
    private List<Profissional> fiscalList = new ArrayList();
    private PnlRequerimentoVigilanciaAnexoDTO pnlRequerimentoVigilanciaAnexoDTO;
    private PnlRequerimentoVigilanciaAnexo pnlRequerimentoVigilanciaAnexo;

    private SubmitButton btnSalvar;

    private WebMarkupContainer containerNovo;

    private WebMarkupContainer containerEdicao;
    private CompoundPropertyModel<RequerimentoVistoriaPBAConformidadeTecnicaDTO> modelEdicao;

    private Table table;
    private List<RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO> requerimentoVistoriaPBAConformidadeTecnicaList;

    private AbstractAjaxButton btnCancelarEdicao;

    private DlgConfirmacaoSimNao dlgConfirmacaoCancelamentoEdicao;

    private PnlConsultaRequerimentoVigilanciaAnexo pnlConsultaRequerimentoVigilanciaAnexo;
    private PnlRequerimentoVigilanciaAnexoDTO pnlConsultaRequerimentoVigilanciaAnexoDTO;
    private PnlConsultaRequerimentoVigilanciaAnexo pnlConsultaRequerimentoVigilanciaAnexoConfirmacao;
    private PnlRequerimentoVigilanciaAnexoDTO pnlConsultaRequerimentoVigilanciaAnexoConfirmacaoDTO;
    private WebMarkupContainer containerResposta;
    private DlgConfirmacaoSimNao dlgConfirmarAnexoProcesso;
    private Component txtResposta;

    private RequiredDateChooser txtDataInspecao;
    private InputArea txtDescricaoObjetivo;
    private InputArea txtDescricaoConstatacoes;
    private InputArea txtDescricaoRessalvas;
    private InputArea txtDescricaoConclusao;
    private InputArea txtDescricaoCaracterizacao;
    private AbstractAjaxButton btnSalvarEdicao;

    public RequerimentoAnaliseProjetosConformidadeTecnicaVistoriaPage(RequerimentoVigilancia requerimentoVigilancia, Class clazz) {
        this.requerimentoVigilancia = requerimentoVigilancia;
        carregarRequerimentoVistoriaProjetoBasicoArquitetura(requerimentoVigilancia);
        this.clazz = clazz;
        init();
    }

    private void init() {
        if (this.requerimentoVigilancia != null) {
            info(VigilanciaHelper.mensagemSituacaoDataAlteracaoRequerimento(requerimentoVigilancia));
        }

        RequerimentoVistoriaPBAConformidadeTecnicaDTO proxy = on(RequerimentoVistoriaPBAConformidadeTecnicaDTO.class);

        form = new Form("form", new CompoundPropertyModel(new RequerimentoVistoriaPBAConformidadeTecnicaDTO()));
        if (requerimentoVistoriaProjetoBasicoArquitetura != null) {
            form.getModel().getObject().setRequerimentoVistoriaProjetoBasicoArquitetura(requerimentoVistoriaProjetoBasicoArquitetura);
        } else {
            form.getModel().getObject().setRequerimentoVistoriaProjetoBasicoArquitetura(new RequerimentoVistoriaProjetoBasicoArquitetura());
            form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().setRequerimentoVigilancia(new RequerimentoVigilancia());
        }

        form.add(containerDados = new WebMarkupContainer("containerDados"));
        containerDados.setOutputMarkupPlaceholderTag(true);
        containerDados.getAjaxRegionMarkupId();
        containerDados.add(getDropDownTipoPessoa(proxy));

        containerDados.add(getContainerPessoa(proxy));

        containerDados.add(getContainerEstabelecimento(proxy));

        containerDados.add(new DisabledInputField<String>(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getProtocoloFormatado())));

        containerTipoProjeto = new WebMarkupContainer("containerTipoProjeto");
        containerTipoProjeto.setOutputMarkupId(true);
        containerTipoProjeto.setEnabled(false);
        containerTipoProjeto.add(autoCompleteConsultaTipoProjetoVigilancia = new AutoCompleteConsultaTipoProjetoVigilancia(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getTipoProjetoVigilancia()), true));
        autoCompleteConsultaTipoProjetoVigilancia.setLabel(Model.of(bundle("tipoProjeto")));

        containerTipoProjeto.add(txtArea = new DoubleField(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getArea())));
        txtArea.setMDec(4).addAjaxUpdateValue();
        txtArea.setEnabled(false);

        if(form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getCodigo() != null){
            montaSequencial(null);
        }
        containerTipoProjeto.add(lbValorTotal = new Label("valorTotal", new PropertyModel<String>(this, "valorTotal")));
        lbValorTotal.setOutputMarkupId(true);

        form.add(containerTipoProjeto);

        addContainerEdicao();
        addContainerNovo();

        form.add(new VoltarButton("btnVoltar"));
        form.add(btnSalvar = new SubmitButton("btnSalvar", new ISubmitAction() {
            @Override
            public void onSubmit(AjaxRequestTarget target, Form form) throws DAOException, ValidacaoException {
                salvar(target);
            }
        }));

        add(form);

        if (requerimentoVigilancia != null && requerimentoVigilancia.getCodigo() != null) {
            carregarFiscaisRequerimento(requerimentoVigilancia);
        }

        enableCamposRequerente(null, false);
    }

    private void addContainerNovo() {
        form.add(containerNovo = new WebMarkupContainer("containerNovo"));
        containerNovo.setOutputMarkupPlaceholderTag(true);

        RequerimentoVistoriaPBAConformidadeTecnicaDTO proxy = on(RequerimentoVistoriaPBAConformidadeTecnicaDTO.class);

        containerNovo.add(dropDownEstaConforme = new DropDown(path(proxy.getEstaConforme())));
        dropDownEstaConforme.addChoice(null, "");
        dropDownEstaConforme.addChoice(RequerimentoVistoriaPBAConformidadeTecnica.TipoConforme.NAO.value(), bundle("nao"));
        dropDownEstaConforme.addChoice(RequerimentoVistoriaPBAConformidadeTecnica.TipoConforme.SIM.value(), bundle("sim"));
        dropDownEstaConforme.addChoice(RequerimentoVistoriaPBAConformidadeTecnica.TipoConforme.PENDENTE.value(), bundle("pendente"));
        dropDownEstaConforme.add(new Tooltip().setText("pendenteEnviaConstatacoesUsuarioExternoRetorno"));

        containerNovo.add(new RequiredDateChooser(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDataInspecao())).setLabel(new Model(bundle("dataInspecao"))));
        containerNovo.add(new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoObjetivo())).setLabel(new Model(bundle("doObjetoNormativasTecnicas"))));
        containerNovo.add(new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoConstatacoes())).setLabel(new Model(bundle("constatacoes"))));
        containerNovo.add(new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoRessalvas())).setLabel(new Model(bundle("ressalvas"))));
        containerNovo.add(new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoConclusao())).setLabel(new Model(bundle("conclusao"))));
        containerNovo.add(new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoCaracterizacao())).setLabel(new Model(bundle("caracterizacaoEdificacaoUnidadeInspecionadaCorrespondenciaAprovado"))));
        containerNovo.add(new DateChooser(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDataSaida())).setLabel(new Model(bundle("dataSaida"))));
        containerNovo.add(new DateChooser(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDataRetorno())).setLabel(new Model(bundle("dataRetorno"))));


        containerNovo.add(autoCompleteConsultaAtividadesVigilancia = new AutoCompleteConsultaAtividadesVigilancia("atividade", new PropertyModel(this, "atividade")));
        containerNovo.add(tblAtividade = new Table("tblAtividade", getColumns(), getCollectionProvider()));
        tblAtividade.populate();
        containerNovo.add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        }.setDefaultFormProcessing(false));

        containerNovo.add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("fiscal",  new PropertyModel(this, "fiscal")));
        containerNovo.add(tblFiscal = new Table("tblFiscal", getColumnsFiscal(), getCollectionProviderFiscal()));
        tblFiscal.populate();
        containerNovo.add(new AbstractAjaxButton("btnAdicionarFiscal") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarFiscal(target);
            }
        }.setDefaultFormProcessing(false));

        containerNovo.add(pnlRequerimentoVigilanciaAnexo = new PnlRequerimentoVigilanciaAnexo(pnlRequerimentoVigilanciaAnexoDTO = new PnlRequerimentoVigilanciaAnexoDTO(), true, false, true, false, "Anexos Requerimento Vigilância"));
        pnlRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
    }

    private void addContainerEdicao() {
        form.add(table = new Table("table", getColumnsEdicao(), getCollectionProviderEdicao()));
        table.populate();
        table.setScrollY("180px");

        form.add(containerEdicao = new WebMarkupContainer("containerEdicao", modelEdicao = new CompoundPropertyModel<>(new RequerimentoVistoriaPBAConformidadeTecnicaDTO())));
        containerEdicao.setOutputMarkupPlaceholderTag(true);
        containerEdicao.setVisible(false);

        RequerimentoVistoriaPBAConformidadeTecnicaDTO proxy = on(RequerimentoVistoriaPBAConformidadeTecnicaDTO.class);
        containerEdicao.add(txtDataInspecao = new RequiredDateChooser(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDataInspecao())));
        txtDataInspecao.setLabel(new Model(bundle("dataInspecao")));
        containerEdicao.add(txtDescricaoObjetivo = new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoObjetivo())));
        txtDescricaoObjetivo.setLabel(new Model(bundle("doObjetoNormativasTecnicas")));
        containerEdicao.add(txtDescricaoConstatacoes = new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoConstatacoes())));
        txtDescricaoConstatacoes.setLabel(new Model(bundle("constatacoes")));
        containerEdicao.add(txtDescricaoRessalvas = new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoRessalvas())));
        txtDescricaoRessalvas.setLabel(new Model(bundle("ressalvas")));
        containerEdicao.add(txtDescricaoConclusao = new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoConclusao())));
        txtDescricaoConclusao.setLabel(new Model(bundle("conclusao")));
        containerEdicao.add(txtDescricaoCaracterizacao = new InputArea(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoCaracterizacao())));
        txtDescricaoCaracterizacao.setLabel(new Model(bundle("caracterizacaoEdificacaoUnidadeInspecionadaCorrespondenciaAprovado")));

        containerResposta = new WebMarkupContainer("containerResposta");
        containerResposta.setOutputMarkupPlaceholderTag(true);
        pnlConsultaRequerimentoVigilanciaAnexoDTO = new PnlRequerimentoVigilanciaAnexoDTO();
        containerResposta.add(pnlConsultaRequerimentoVigilanciaAnexo = new PnlConsultaRequerimentoVigilanciaAnexo("requerimentoVigilanciaAnexo", pnlConsultaRequerimentoVigilanciaAnexoDTO));
        pnlConsultaRequerimentoVigilanciaAnexo.setOutputMarkupId(true);
        pnlConsultaRequerimentoVigilanciaAnexoConfirmacaoDTO = new PnlRequerimentoVigilanciaAnexoDTO();
        pnlConsultaRequerimentoVigilanciaAnexoConfirmacaoDTO.setConfirmacao(true);
        containerResposta.add(pnlConsultaRequerimentoVigilanciaAnexoConfirmacao = new PnlConsultaRequerimentoVigilanciaAnexo("pnlConsultaRequerimentoVigilanciaAnexoConfirmacao", pnlConsultaRequerimentoVigilanciaAnexoConfirmacaoDTO) {
            @Override
            public IColumn getActionInicialColumn() {
                return new MultipleActionCustomColumn<RequerimentoVigilanciaAnexoDTO>() {
                    @Override
                    public void customizeColumn(RequerimentoVigilanciaAnexoDTO rowObject) {
                        addAction(ActionType.CURTIR, rowObject, new IModelAction<RequerimentoVigilanciaAnexoDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, RequerimentoVigilanciaAnexoDTO modelObject) throws ValidacaoException, DAOException {
                                initDlgConfirmacaoAdicionarAnexoProcesso(target, modelObject, bundle("msgDesejaAdicionarAnexoProcesso"), false);
                            }
                        }).setTitleBundleKey("anexarAoProcesso").setEnabled(rowObject.getRequerimentoVigilanciaAnexo().getRequerimentoVigilancia() == null);

                        addAction(ActionType.DESCURTIR, rowObject, new IModelAction<RequerimentoVigilanciaAnexoDTO>() {
                            @Override
                            public void action(AjaxRequestTarget target, RequerimentoVigilanciaAnexoDTO modelObject) throws ValidacaoException, DAOException {
                                initDlgConfirmacaoAdicionarAnexoProcesso(target, modelObject, bundle("msgDesejaRemoverAnexoProcesso"), true);
                            }
                        }).setTitleBundleKey("removerAnexoProcesso").setEnabled(rowObject.getRequerimentoVigilanciaAnexo().getRequerimentoVigilancia() != null);
                    }
                };
            }
        });
        pnlConsultaRequerimentoVigilanciaAnexoConfirmacao.setOutputMarkupId(true);

        containerEdicao.add(containerResposta);
        containerResposta.add(txtResposta = new MultiLineLabel(path(proxy.getRequerimentoVistoriaPBAConformidadeTecnicaResposta().getDescricaoResposta())) {
            @Override
            public void onComponentTagBody(MarkupStream markupStream, ComponentTag openTag) {
                String defaultModelObjectAsString = getDefaultModelObjectAsString();
                replaceComponentTagBody(markupStream, openTag, defaultModelObjectAsString);
            }
        }.setEscapeModelStrings(false));
        txtResposta.setOutputMarkupPlaceholderTag(true);

        containerEdicao.add(btnSalvarEdicao = new AbstractAjaxButton("btnSalvarEdicao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                onActionSalvarEdicao(target);
            }
        });

        containerEdicao.add(btnCancelarEdicao = new AbstractAjaxButton("btnCancelarEdicao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                showDlgConfirmacaoCancelamentoEdicao(target);
            }
        });
        btnCancelarEdicao.setDefaultFormProcessing(false);
    }

    private void showDlgConfirmacaoCancelamentoEdicao(AjaxRequestTarget target) {
        if (dlgConfirmacaoCancelamentoEdicao == null) {
            addModal(target, dlgConfirmacaoCancelamentoEdicao = new DlgConfirmacaoSimNao(newModalId(), bundle("msgDesejaCancelarEdicaoConformidade")) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    onActionCancelarEdicao(target);
                }
            });
        }
        dlgConfirmacaoCancelamentoEdicao.show(target);
    }

    private void onActionSalvarEdicao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        RequerimentoVistoriaPBAConformidadeTecnicaDTO dto = modelEdicao.getObject();

        BOFactoryWicket.save(dto.getRequerimentoVistoriaPBAConformidadeTecnica());

        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObject<RequerimentoVistoriaPBAConformidadeTecnicaDTO>(newModalId(), bundle("msgImprimirRelatorioConformidadeTecnica")) {

            @Override
            public DataReport getDataReport(RequerimentoVistoriaPBAConformidadeTecnicaDTO object) throws ReportException {
                RelatorioConformidadeTecnicaDTOParam param = new RelatorioConformidadeTecnicaDTOParam();
                param.setCodigoParecer(object.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getCodigo());
                param.setCodigoConformidadeTecnica(object.getRequerimentoVistoriaPBAConformidadeTecnica().getCodigo());

                param.setUrlQRcode(VigilanciaHelper.getURLQRCodePageRequerimento());
                param.setChaveQrcode(object.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getChaveQRcode());

                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoConformidadeTecnicaVistoria(param);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVistoriaPBAConformidadeTecnicaDTO object) throws ValidacaoException, DAOException {
                try {
                    Page page = (Page) clazz.newInstance();
                    setResponsePage(page);
                } catch (InstantiationException e) {
                    Loggable.log.error(e.getMessage());
                } catch (IllegalAccessException e) {
                    Loggable.log.error(e.getMessage());
                }
            }

        });

        dto.setRequerimentoVistoriaProjetoBasicoArquitetura(requerimentoVistoriaProjetoBasicoArquitetura);
        dlgConfirmacaoImpressao.show(target, dto);
    }

    private void onActionCancelarEdicao(AjaxRequestTarget target) {
        containerEdicao.setVisible(false);
        btnSalvar.setVisible(true);
        containerNovo.setVisible(true);

        target.add(form);
    }

    private List<IColumn> getColumnsEdicao() {
        List<IColumn> columns = new ArrayList<>();

        RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO proxy = on(RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO.class);

        columns.add(getCustomColumnsEdicao());
        columns.add(new DateColumn(bundle("dataInspecao"), path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDataInspecao())));
        columns.add(new TextColumn(BundleManager.getString("objetivo"), path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoObjetivo())).setMaxPrecision(50));
        columns.add(new TextColumn(BundleManager.getString("constatacoes"), path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoConstatacoes())).setMaxPrecision(50));
        columns.add(new TextColumn(BundleManager.getString("ressalvas"), path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoRessalvas())).setMaxPrecision(50));
        columns.add(new TextColumn(BundleManager.getString("conclusao"), path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoConclusao())).setMaxPrecision(50));
        columns.add(new TextColumn(BundleManager.getString("caracterizacao"), path(proxy.getRequerimentoVistoriaPBAConformidadeTecnica().getDescricaoCaracterizacao())).setMaxPrecision(50));
        columns.add(new TextColumn(BundleManager.getString("resposta"), path(proxy.getRequerimentoVistoriaPBAConformidadeTecnicaResposta().getDescricaoRespostaSemHtml())).setMaxPrecision(50));

        return columns;
    }

    private IColumn getCustomColumnsEdicao() {
        return new MultipleActionCustomColumn<RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO>() {
            @Override
            public void customizeColumn(RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO modelObject) throws ValidacaoException, DAOException {
                        onActionEditar(target, modelObject, true);
                    }
                }).setVisible(rowObject.getRequerimentoVistoriaPBAConformidadeTecnicaResposta() == null);

                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO modelObject) throws ValidacaoException, DAOException {
                        onActionEditar(target, modelObject, false);
                    }
                }).setVisible(rowObject.getRequerimentoVistoriaPBAConformidadeTecnicaResposta() != null);
            }
        };
    }

    private void onActionEditar(AjaxRequestTarget target, RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO dtoResposta, boolean editar) {
        RequerimentoVistoriaPBAConformidadeTecnicaDTO dto = new RequerimentoVistoriaPBAConformidadeTecnicaDTO();
        modelEdicao.setObject(dto);

        ComponentUtils.limparContainer(containerEdicao, target);

        dto.setRequerimentoVistoriaPBAConformidadeTecnica(dtoResposta.getRequerimentoVistoriaPBAConformidadeTecnica());

        btnSalvar.setVisible(false);
        containerNovo.setVisible(false);

        containerEdicao.setVisible(true);

        if (editar) {
            btnSalvarEdicao.setEnabled(true);
            target.add(btnSalvarEdicao);
            txtResposta.setVisible(false);
            target.add(txtResposta);
            modelEdicao.getObject().setRequerimentoVistoriaPBAConformidadeTecnicaResposta(null);
            containerResposta.setVisible(false);
            target.add(containerResposta);
            habilitarCamposParecer(true, target);
        } else {
            btnSalvarEdicao.setEnabled(false);
            target.add(btnSalvarEdicao);
            habilitarCamposParecer(false, target);
            pnlConsultaRequerimentoVigilanciaAnexo.setRequerimentoVigilanciaAnexoDTOList(getDtoAnexos(dtoResposta.getRequerimentoVistoriaPBAConformidadeTecnica()));
            pnlConsultaRequerimentoVigilanciaAnexoConfirmacao.setRequerimentoVigilanciaAnexoDTOList(getDtoAnexosConfirmacao(dtoResposta.getRequerimentoVistoriaPBAConformidadeTecnicaResposta()));
            target.add(pnlConsultaRequerimentoVigilanciaAnexoConfirmacao);
            target.appendJavaScript(JScript.showFieldset(containerEdicao));

            modelEdicao.getObject().setRequerimentoVistoriaPBAConformidadeTecnicaResposta(dtoResposta.getRequerimentoVistoriaPBAConformidadeTecnicaResposta());
            txtResposta.setVisible(true);
            target.add(txtResposta);

            containerResposta.setVisible(true);
            target.add(containerResposta);

            target.add(pnlConsultaRequerimentoVigilanciaAnexo);
        }

        target.add(form);
    }

    private void habilitarCamposParecer(boolean habilitar, AjaxRequestTarget target) {
        txtDataInspecao.setEnabled(habilitar);
        txtDescricaoObjetivo.setEnabled(habilitar);
        txtDescricaoConstatacoes.setEnabled(habilitar);
        txtDescricaoRessalvas.setEnabled(habilitar);
        txtDescricaoConclusao.setEnabled(habilitar);
        txtDescricaoCaracterizacao.setEnabled(habilitar);

        target.add(txtDataInspecao);
        target.add(txtDescricaoObjetivo);
        target.add(txtDescricaoConstatacoes);
        target.add(txtDescricaoRessalvas);
        target.add(txtDescricaoConclusao);
        target.add(txtDescricaoCaracterizacao);
    }

    private List<RequerimentoVigilanciaAnexoDTO> getDtoAnexos(RequerimentoVistoriaPBAConformidadeTecnica requerimentoVistoriaPBAConformidadeTecnica) {
        List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        List<RequerimentoVigilanciaAnexo> listObjects = VigilanciaHelper.carregarAnexosVigilancia(requerimentoVistoriaPBAConformidadeTecnica);
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : listObjects) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);
            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        return requerimentoVigilanciaAnexoDTOList;
    }

    private List<RequerimentoVigilanciaAnexoDTO> getDtoAnexosConfirmacao(RequerimentoVistoriaPBAConformidadeTecnicaResposta resposta) {
        List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        List<RequerimentoVigilanciaAnexo> listObjects = VigilanciaHelper.carregarAnexosVigilancia(resposta);
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for (RequerimentoVigilanciaAnexo rva : listObjects) {
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);
            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
        return requerimentoVigilanciaAnexoDTOList;
    }

    private ICollectionProvider getCollectionProviderEdicao() {
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (requerimentoVistoriaPBAConformidadeTecnicaList == null) {
                    requerimentoVistoriaPBAConformidadeTecnicaList = new ArrayList<>();
                }
                return requerimentoVistoriaPBAConformidadeTecnicaList;
            }
        };
    }

    private WebMarkupContainer getContainerEstabelecimento(RequerimentoVistoriaPBAConformidadeTecnicaDTO proxy) {
        containerEstabelecimento = new WebMarkupContainer("containerEstabelecimento");
        containerEstabelecimento.setOutputMarkupPlaceholderTag(true);
        containerEstabelecimento.getAjaxRegionMarkupId();
        containerEstabelecimento.add(autoCompleteConsultaEstabelecimento = new AutoCompleteConsultaEstabelecimento(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getEstabelecimento()), true));
        autoCompleteConsultaEstabelecimento.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaEstabelecimento.setLabel(Model.of(bundle("estabelecimento")));
        autoCompleteConsultaEstabelecimento.setEnabled(form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getCodigo() == null);
        containerEstabelecimento.add(txtFantasia = new DisabledInputField<>(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getEstabelecimento().getFantasia())));
        containerEstabelecimento.add(txtCnpjCpfFormatado = new DisabledInputField<>(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getEstabelecimento().getCnpjCpfFormatado())));
        containerEstabelecimento.add(txtDescricaoEndereco = new DisabledInputField<>(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaEndereco().getEnderecoFormatadoComCidade())));

        return containerEstabelecimento;
    }

    private WebMarkupContainer getContainerPessoa(RequerimentoVistoriaPBAConformidadeTecnicaDTO proxy) {
        containerPessoa = new WebMarkupContainer("containerPessoa");
        containerPessoa.add(autoCompleteConsultaVigilanciaPessoa = new AutoCompleteConsultaVigilanciaPessoa(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaPessoa())));
        containerPessoa.setOutputMarkupPlaceholderTag(true);
        containerPessoa.getAjaxRegionMarkupId();
        autoCompleteConsultaVigilanciaPessoa.setEnabled(form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getCodigo() == null);
        containerPessoa.add(txtNomeFantasia = new DisabledInputField<>(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaPessoa().getNomeFantasia())));
        containerPessoa.add(txtCpfFormatado = new DisabledInputField<>(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaPessoa().getCpfFormatado())));
        containerPessoa.add(txtEmail = new DisabledInputField<>(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getVigilanciaPessoa().getEmail())));

        autoCompleteConsultaVigilanciaPessoa.setOutputMarkupPlaceholderTag(true);
        autoCompleteConsultaVigilanciaPessoa.add(new ConsultaListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarPessoa(target, object);
            }
        });

        autoCompleteConsultaVigilanciaPessoa.add(new RemoveListener<VigilanciaPessoa>() {
            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, VigilanciaPessoa object) {
                atualizarPessoa(target, null);
            }
        });

        containerPessoa.add(new AbstractAjaxLink("btnCadPessoa") {
            @Override
            public void onAction(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                addModal(target, dlgCadastroVigilanciaPessoa = new DlgCadastroVigilanciaPessoa(newModalId()) {
                    @Override
                    public void setVigilanciaPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
                        autoCompleteConsultaVigilanciaPessoa.limpar(target);
                        autoCompleteConsultaVigilanciaPessoa.setComponentValue(target, vigilanciaPessoa);
//                        target.add(txtNumeroEndereco);
                    }
                });
                dlgCadastroVigilanciaPessoa.show(target, new VigilanciaPessoa());
            }
        });

        return containerPessoa;
    }

    private void atualizarPessoa(AjaxRequestTarget target, VigilanciaPessoa vigilanciaPessoa) {
        if (vigilanciaPessoa != null) {
            form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setVigilanciaPessoa(vigilanciaPessoa);
        } else {
            form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setVigilanciaPessoa(null);
        }
        target.add(txtCpfFormatado);
        target.add(txtNomeFantasia);
        target.add(txtEmail);
    }

    private DropDown getDropDownTipoPessoa(RequerimentoVistoriaPBAConformidadeTecnicaDTO proxy) {
        dropDownTipoPessoa = DropDownUtil.getIEnumDropDown(path(proxy.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getTipoRequerente()), RequerimentoVigilancia.TipoRequerente.values(), false, true);
        dropDownTipoPessoa.addAjaxUpdateValue();
        dropDownTipoPessoa.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                enableCamposRequerente(target, true);
            }
        });
        dropDownTipoPessoa.setEnabled(false);
        return dropDownTipoPessoa;
    }

    private void enableCamposRequerente(AjaxRequestTarget target, boolean limparCampos) {
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getTipoRequerente())) {
            form.getModelObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setVigilanciaPessoa(null);
            containerEstabelecimento.setVisible(true);
            containerPessoa.setVisible(false);
        } else {
            form.getModelObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setEstabelecimento(null);
            form.getModelObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().setVigilanciaEndereco(null);
            containerEstabelecimento.setVisible(false);
            containerPessoa.setVisible(true);
        }

        if (target != null) {
            if (limparCampos) {
                autoCompleteConsultaEstabelecimento.limpar(target);
                autoCompleteConsultaVigilanciaPessoa.limpar(target);
            }
            target.add(containerDados);
            target.appendJavaScript(JScript.initMasks());
        }
    }

    private void montaSequencial(AjaxRequestTarget target) {
        try {
            carregarConfiguracaoVigilancia();
            if (configuracaoVigilancia == null) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_nao_existe_configuracao"));
            }
            StringBuilder sb = new StringBuilder();
            BigDecimal totalTaxaUfm = new BigDecimal(0);
            BigDecimal totalTaxa = new BigDecimal(0);
            TipoProjetoVigilancia tipoProjetoVigilancia = form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getTipoProjetoVigilancia();
            TaxaIndice taxaIndice = null;
            if (tipoProjetoVigilancia != null) {
                taxaIndice = carregarTaxaVigente(tipoProjetoVigilancia.getTaxa());
            } else {
                return;
            }
            totalTaxaUfm = new Dinheiro(Coalesce.asDouble(tipoProjetoVigilancia.getValor()), 4).bigDecimalValue();

            Double area = form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getArea();
            if (Coalesce.asDouble(area) > tipoProjetoVigilancia.getMetragemMaxima()) {
                if (Coalesce.asDouble(configuracaoVigilancia.getValorExcedidoAnaliseProjeto()) == 0D) {
                    throw new ValidacaoException(BundleManager.getString("msgAreaInformadaExcedeMetragemMaximaTipoProjeto"));
                } else {
                    BigDecimal areaExcedida = new Dinheiro(Coalesce.asDouble(area), 4)
                            .subtrair(tipoProjetoVigilancia.getMetragemMaxima(), 4).bigDecimalValue();
                    BigDecimal valorTotalExcedido = new Dinheiro(Coalesce.asBigDecimal(areaExcedida).doubleValue(), 4)
                            .multiplicar(configuracaoVigilancia.getValorExcedidoAnaliseProjeto(), 4).bigDecimalValue();

                    totalTaxaUfm = new Dinheiro(totalTaxaUfm).somar(valorTotalExcedido.doubleValue(), 4).bigDecimalValue();
                }
            }

            if(TipoProjetoVigilancia.TipoCobranca.POR_M2.value().equals(tipoProjetoVigilancia.getTipoCobranca())) {
                totalTaxaUfm = new Dinheiro(Coalesce.asBigDecimal(totalTaxaUfm).doubleValue(), 4)
                        .multiplicar(Coalesce.asDouble(area), 4)
                        .bigDecimalValue();
            }

            totalTaxa = new Dinheiro(Coalesce.asBigDecimal(totalTaxaUfm).doubleValue(), 4)
                    .multiplicar(taxaIndice == null ? 0 : taxaIndice.getValorIndice())
                    .bigDecimalValue();


            NumberFormat nf = NumberFormat.getCurrencyInstance();
            sb.append(totalTaxaUfm.toString());
            sb.append(" ");
            sb.append(tipoProjetoVigilancia.getTaxa().getDescricao());
            sb.append(" - ");
            sb.append(nf.format(totalTaxa));
            valorTotal = sb.toString();
            if (target != null) {
                target.add(lbValorTotal);
            }
        } catch (DAOException | ValidacaoException ex) {
            if (target != null) {
                modalWarn(target, ex);
            }
        }
    }

    private TaxaIndice carregarTaxaVigente(Taxa taxa) {
        try {
            return FinanceiroVigilanciaHelper.getTaxaIndiceVigente(taxa);
        } catch (Exception e) {
            Loggable.log.info(e.getMessage());
        }

        return null;
    }

    private void salvar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (br.com.celk.util.CollectionUtils.isEmpty(fiscalList)) {
            throw new ValidacaoException(bundle("msgInformeAoMenosUmFiscal"));
        }

        RequerimentoVistoriaPBAConformidadeTecnicaDTO dto = form.getModel().getObject();
        dto.getProfissionalList().addAll(fiscalList);
        dto.getAtividadesVigilanciaList().addAll(atividadeList);
        dto.setPnlRequerimentoVigilanciaAnexoDTO(pnlRequerimentoVigilanciaAnexoDTO);
        RequerimentoVistoriaPBAConformidadeTecnica requerimentoVistoriaPBAConformidadeTecnica = BOFactoryWicket.getBO(VigilanciaFacade.class).salvarRequerimentoVistoriaProjetoBasicoArquiteturaConformidadeTecnica(dto);
        dto.setRequerimentoVistoriaPBAConformidadeTecnica(requerimentoVistoriaPBAConformidadeTecnica);
        addModal(target, dlgConfirmacaoImpressao = new DlgImpressaoObject<RequerimentoVistoriaPBAConformidadeTecnicaDTO>(newModalId(), bundle("msgImprimirRelatorioConformidadeTecnica")) {
            @Override
            public DataReport getDataReport(RequerimentoVistoriaPBAConformidadeTecnicaDTO object) throws ReportException {
                RelatorioConformidadeTecnicaDTOParam param = new RelatorioConformidadeTecnicaDTOParam();
                param.setCodigoParecer(object.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getCodigo());
                param.setCodigoConformidadeTecnica(object.getRequerimentoVistoriaPBAConformidadeTecnica().getCodigo());

                param.setUrlQRcode(VigilanciaHelper.getURLQRCodePageRequerimento());
                param.setChaveQrcode(object.getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getChaveQRcode());

                return BOFactoryWicket.getBO(VigilanciaReportFacade.class).impressaoConformidadeTecnicaVistoria(param);
            }

            @Override
            public void onFechar(AjaxRequestTarget target, RequerimentoVistoriaPBAConformidadeTecnicaDTO object) throws ValidacaoException, DAOException {
                try {
                    Page page = (Page) clazz.newInstance();
                    setResponsePage(page);
                } catch (InstantiationException e) {
                    Loggable.log.error(e.getMessage());
                } catch (IllegalAccessException e) {
                    Loggable.log.error(e.getMessage());
                }
            }
        });

        dlgConfirmacaoImpressao.show(target, dto);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        AtividadesVigilancia proxy = on(AtividadesVigilancia.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("atividade"), proxy.getDescricao()));

        return columns;
    }

    private List<IColumn> getColumnsFiscal() {
        List<IColumn> columns = new ArrayList<>();
        Profissional proxy = on(Profissional.class);

        columns.add(getCustomColumnFiscal());
        columns.add(createColumn(bundle("fiscal"), proxy.getDescricaoFormatado()));

        return columns;
    }

    private IColumn getCustomColumn() {
        return new MultipleActionCustomColumn<AtividadesVigilancia>() {

            @Override
            public void customizeColumn(AtividadesVigilancia rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<AtividadesVigilancia>() {
                    @Override
                    public void action(AjaxRequestTarget target, AtividadesVigilancia modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblAtividade, atividadeList, modelObject);
                    }
                }).setVisible(isActionPermitted(Permissions.REMOVER_FISCAIS, RequerimentosPage.class)
                        || rowObject.getCodigo() == null);
            }
        };
    }


    private IColumn getCustomColumnFiscal() {
        return new MultipleActionCustomColumn<Profissional>() {

            @Override
            public void customizeColumn(Profissional rowObject) {
                addAction(ActionType.REMOVER, rowObject, new IModelAction<Profissional>() {
                    @Override
                    public void action(AjaxRequestTarget target, Profissional modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblFiscal, fiscalList, modelObject);
                    }
                });
            }
        };
    }

    private ICollectionProvider getCollectionProviderFiscal() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return fiscalList;
            }
        };
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return atividadeList;
            }
        };
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException {
        if (atividade == null) {
            throw new ValidacaoException(bundle("informeAtividade"));
        }

        CrudUtils.adicionarItem(target, tblAtividade, atividadeList, atividade);
        autoCompleteConsultaAtividadesVigilancia.limpar(target);
        target.focusComponent(autoCompleteConsultaAtividadesVigilancia.getTxtDescricao().getTextField());
    }

    private void adicionarFiscal(AjaxRequestTarget target) throws ValidacaoException {
        if (fiscal == null) {
            throw new ValidacaoException(bundle("msgInformeFiscal"));
        }

        CrudUtils.adicionarItem(target, tblFiscal, fiscalList, fiscal);
        autoCompleteConsultaProfissional.limpar(target);
        target.focusComponent(autoCompleteConsultaProfissional.getTxtDescricao().getTextField());
    }


    private void carregarFiscaisRequerimento(RequerimentoVigilancia requerimentoVigilancia) {
        if(requerimentoVigilancia == null || requerimentoVigilancia.getCodigo() == null){
            return;
        }
        List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList = LoadManager.getInstance(RequerimentoVigilanciaFiscal.class)
                .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVigilanciaFiscal.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(requerimentoVigilanciaFiscalList)) {
            fiscalList = Lambda.extract(requerimentoVigilanciaFiscalList, Lambda.on(RequerimentoVigilanciaFiscal.class).getProfissional());
        }
    }

    private void carregarRequerimentoVistoriaProjetoBasicoArquitetura(RequerimentoVigilancia requerimentoVigilancia) {
        if (requerimentoVigilancia != null) {
            tipoSolicitacao = requerimentoVigilancia.getTipoSolicitacao();

            requerimentoVistoriaProjetoBasicoArquitetura = LoadManager.getInstance(RequerimentoVistoriaProjetoBasicoArquitetura.class)
                    .addProperties(new HQLProperties(RequerimentoVistoriaProjetoBasicoArquitetura.class).getProperties())
                    .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                    .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                    .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(TipoProjetoVigilancia.class, RequerimentoVistoriaProjetoBasicoArquitetura.PROP_TIPO_PROJETO_VIGILANCIA).getProperties())
                    .addProperties(new HQLProperties(Taxa.class, VOUtils.montarPath(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_TIPO_PROJETO_VIGILANCIA, TipoProjetoVigilancia.PROP_TAXA)).getProperties())
                    .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_VIGILANCIA_ENDERECO)).getProperties())
                    .addProperties(new HQLProperties(RequerimentoAnaliseProjeto.class, VOUtils.montarPath(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_ANALISE_PROJETO)).getProperties())
                    .addProperties(new HQLProperties(RequerimentoVigilancia.class, VOUtils.montarPath(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_ANALISE_PROJETO, RequerimentoAnaliseProjeto.PROP_REQUERIMENTO_VIGILANCIA)).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVistoriaProjetoBasicoArquitetura.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                    .start().getVO();

            requerimentoVistoriaProjetoBasicoArquitetura.setRequerimentoVigilancia(requerimentoVigilancia);

            carregarAnexos(requerimentoVigilancia);

            List<RequerimentoVistoriaPBAConformidadeTecnica> conformidades = LoadManager.getInstance(RequerimentoVistoriaPBAConformidadeTecnica.class)
                    .addProperties(new HQLProperties(RequerimentoVistoriaPBAConformidadeTecnica.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoVistoriaPBAConformidadeTecnica.PROP_REQUERIMENTO_VISTORIA_PROJETO_BASICO_ARQUITETURA), requerimentoVistoriaProjetoBasicoArquitetura))
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(RequerimentoVistoriaPBAConformidadeTecnica.PROP_SITUACAO_CONFORMIDADE), RequerimentoVigilancia.SituacaoAnaliseProjetos.COM_PENDENCIA.value()))
                    .addSorter(new QueryCustom.QueryCustomSorter(RequerimentoVistoriaPBAConformidadeTecnica.PROP_DATA_INSPECAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                    .start().getList();
            if (CollectionUtils.isNotNullEmpty(conformidades)) {
                requerimentoVistoriaPBAConformidadeTecnicaList = new ArrayList<>();
                for (RequerimentoVistoriaPBAConformidadeTecnica conformidade : conformidades) {
                    RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO dto = new RequerimentoVistoriaPBAConformidadeTecnicaRespostaDTO();
                    dto.setRequerimentoVistoriaPBAConformidadeTecnica(conformidade);
                    RequerimentoVistoriaPBAConformidadeTecnicaResposta resposta = LoadManager.getInstance(RequerimentoVistoriaPBAConformidadeTecnicaResposta.class)
                            .addProperties(new HQLProperties(RequerimentoVistoriaPBAConformidadeTecnicaResposta.class).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoVistoriaPBAConformidadeTecnicaResposta.PROP_REQUERIMENTO_VISTORIA_P_B_A_CONFORMIDADE_TECNICA, conformidade))
                            .start().getVO();
                    dto.setRequerimentoVistoriaPBAConformidadeTecnicaResposta(resposta);
                    requerimentoVistoriaPBAConformidadeTecnicaList.add(dto);
                }
            }
        }
    }

    private void carregarAnexos(RequerimentoVigilancia rv){
        List<RequerimentoVigilanciaAnexo> list = VigilanciaHelper.carregarAnexosVigilancia(rv);

        requerimentoVigilanciaAnexoDTOList = new ArrayList<>();
        RequerimentoVigilanciaAnexoDTO anexoDTO;
        for(RequerimentoVigilanciaAnexo rva : list){
            anexoDTO = new RequerimentoVigilanciaAnexoDTO();
            anexoDTO.setDescricaoAnexo(rva.getDescricao());
            anexoDTO.setNomeArquivoOriginal(rva.getGerenciadorArquivo().getNomeArquivo());
            anexoDTO.setRequerimentoVigilanciaAnexo(rva);

            requerimentoVigilanciaAnexoDTOList.add(anexoDTO);
        }
    }

    private void initDlgConfirmacaoAdicionarAnexoProcesso(AjaxRequestTarget target, final RequerimentoVigilanciaAnexoDTO modelObject, String mensagem, final boolean removeu) {
        addModal(target, dlgConfirmarAnexoProcesso = new DlgConfirmacaoSimNao<LancamentoAtividadesVigilancia>(newModalId(), mensagem) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                RequerimentoVigilanciaAnexo requerimentoVigilanciaAnexo = modelObject.getRequerimentoVigilanciaAnexo();
                if (removeu) {
                    requerimentoVigilanciaAnexo.setRequerimentoVigilancia(null);
                } else {
                    requerimentoVigilanciaAnexo.setRequerimentoVigilancia(requerimentoVigilancia);
                }
                requerimentoVigilanciaAnexo = BOFactoryWicket.save(requerimentoVigilanciaAnexo);
                modelObject.setRequerimentoVigilanciaAnexo(requerimentoVigilanciaAnexo);
                pnlConsultaRequerimentoVigilanciaAnexoConfirmacao.getTblRequerimentoVigilanciaAnexo().populate(target);
            }
        });
        dlgConfirmarAnexoProcesso.show(target);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("analiseProjetosConformidadeTecnica");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        if (RequerimentoVigilancia.TipoRequerente.ESTABELECIMENTO.value().equals(form.getModel().getObject().getRequerimentoVistoriaProjetoBasicoArquitetura().getRequerimentoVigilancia().getTipoRequerente())) {
            response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaEstabelecimento.getTxtDescricao().getTextField())));
        } else {
            response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(autoCompleteConsultaVigilanciaPessoa.getTxtDescricao().getTextField())));
        }
    }

    private void carregarConfiguracaoVigilancia() throws DAOException, ValidacaoException {
        configuracaoVigilancia = BOFactory.getBO(VigilanciaFacade.class).carregarConfiguracaoVigilancia();
    }
}
