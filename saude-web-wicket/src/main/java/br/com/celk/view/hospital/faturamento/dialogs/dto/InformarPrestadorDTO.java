package br.com.celk.view.hospital.faturamento.dialogs.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaServicoClassificacao;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class InformarPrestadorDTO implements Serializable {

    private Profissional profissional;
    private TabelaCbo cbo;
    private Empresa empresa;
    private EmpresaServicoClassificacao empresaServicoClassificacao;
    private Long atribuirValorPara;

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TabelaCbo getCbo() {
        return cbo;
    }

    public void setCbo(TabelaCbo cbo) {
        this.cbo = cbo;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public EmpresaServicoClassificacao getEmpresaServicoClassificacao() {
        return empresaServicoClassificacao;
    }

    public void setEmpresaServicoClassificacao(EmpresaServicoClassificacao empresaServicoClassificacao) {
        this.empresaServicoClassificacao = empresaServicoClassificacao;
    }

    public Long getAtribuirValorPara() {
        return atribuirValorPara;
    }

    public void setAtribuirValorPara(Long atribuirValorPara) {
        this.atribuirValorPara = atribuirValorPara;
    }

}
