package br.com.celk.view.atendimento.recepcao.panel.examesautorizacao;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.datechooser.RequiredDateChooser;
import br.com.celk.component.doublefield.DisabledDoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.panel.RemoverActionColumnPanel;
import br.com.celk.component.table.selection.MultiSelectionTable;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.exame.view.ExameNaoSusViewPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.DefaultRecepcaoPanel;
import br.com.celk.view.atendimento.recepcao.panel.template.RecepcaoCadastroPanel;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.cadsus.usuariocadsus.autocomplete.AutoCompleteConsultaUsuarioCadsus;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.ksisolucoes.agendamento.exame.CotasExamesHelper;
import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom.QueryCustomParameter;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.controle.SGKException;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetenciaPK;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.ajax.markup.html.form.AjaxButton;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.logging.Level;
import java.util.logging.Logger;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class CadastroRequisicaoExamePanel extends RecepcaoCadastroPanel {

    private final DefaultRecepcaoPanel defaultRecepcaoPanel;
    private Form<ExameCadastroAprovacaoDTO> form;
    private Form<ExameProcedimentoDTO> formExameProcedimento;
    private InputField txtNumeroRequisicao;
    private LongField txtQuantidade;
    private AutoCompleteConsultaEmpresa autoCompleteConsultaEmpresa;
    private AutoCompleteConsultaExameProcedimento autoCompleteConsultaExameProcedimento;
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private AutoCompleteConsultaUsuarioCadsus autoCompleteConsultaUsuarioCadsus;
    private final ArrayList<ExameProcedimentoDTO> lstExameProcedimentoDto;
    private final AttributeModifier redTextModifier = new AttributeModifier("style", "color: red;");
    private DateChooser dchDataSolicitacao;
    private AbstractAjaxButton btnAdicionar;
    private Table<ExameProcedimentoDTO> tableExame;
    private DropDown<Date> dropDownDataCompetencia;
    private Date competenciaAtual;
    private Double cotaUtilizada;
    private Double cotaUnidade;
    private Double saldo;
    private Double total = 0D;
    private ExameProcedimento exameAdd;
    private Long quantidadeAdd;
    private DisabledDoubleField txtTotal;
    private DisabledDoubleField txtSaldo;
    private DisabledDoubleField txtCotaUtilizada;
    private DisabledDoubleField txtCotaUnidade;
    private Long codigoExameAutorizacao;
    private final boolean enableField;
    private RequisicaoPadrao requisicaoPadrao;
    private MultiSelectionTable<RequisicaoPadraoExame> tblExamesPadrao;
    private List<RequisicaoPadraoExame> requisicoesPadraoExames = new ArrayList<>();
    private WebMarkupContainer containerProcedimentoIndividual;

    private AbstractAjaxButton btnSalvarAvancar;
    private AbstractAjaxButton btnSalvar;

    private WebMarkupContainer containerDescricaoDadoClinico;

    public CadastroRequisicaoExamePanel(String id, DefaultRecepcaoPanel defaultRecepcaoPanel) {
        super(id, bundle("solicitacaoExames"));
        this.defaultRecepcaoPanel = defaultRecepcaoPanel;
        lstExameProcedimentoDto = new ArrayList<>();
        this.enableField = true;
    }

    public CadastroRequisicaoExamePanel(String id, DefaultRecepcaoPanel defaultRecepcaoPanel, Long codigoExameAutorizacao) {
        super(id, bundle("solicitacaoExames"));
        this.defaultRecepcaoPanel = defaultRecepcaoPanel;
        this.codigoExameAutorizacao = codigoExameAutorizacao;
        lstExameProcedimentoDto = new ArrayList<>();
        this.enableField = false;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        ExameCadastroAprovacaoDTO proxy = on(ExameCadastroAprovacaoDTO.class);

        getForm().add(txtNumeroRequisicao = (InputField) new InputField(path(proxy.getCodigoExameCadastrado())).setEnabled(enableField));
        this.txtNumeroRequisicao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                try {
                    carregarExame(target);
                } catch (DAOException | ValidacaoException ex) {
                    Logger.getLogger(CadastroRequisicaoExamePanel.class.getName()).log(Level.SEVERE, null, ex);
                }
            }
        });

        getForm().add(autoCompleteConsultaUsuarioCadsus = new AutoCompleteConsultaUsuarioCadsus(path(proxy.getUsuarioCadsus()), true) {
            @Override
            public Configuration getConfigurationInstance() {
                return Configuration.ATIVO;
            }
        });
        autoCompleteConsultaUsuarioCadsus.setLabel(new Model<>(BundleManager.getString("paciente"))).setEnabled(enableField);
        getForm().add(autoCompleteConsultaEmpresa = (AutoCompleteConsultaEmpresa) new AutoCompleteConsultaEmpresa(path(proxy.getEstabelecimento()), true).setEnabled(enableField));
        autoCompleteConsultaEmpresa.setLabel(new Model<>(bundle("estabelecimentoSolicitante")));
        autoCompleteConsultaEmpresa.setTiposEstabelecimento(Arrays.asList(Empresa.TIPO_ESTABELECIMENTO_UNIDADE, Empresa.TIPO_ESTABELECIMENTO_SECRETARIA_SAUDE));
        getForm().add(autoCompleteConsultaProfissional = (AutoCompleteConsultaProfissional) new AutoCompleteConsultaProfissional(path(proxy.getProfissional()), true).setEnabled(enableField));
        autoCompleteConsultaProfissional.setLabel(new Model<>(bundle("profissional")));
        getForm().add(dchDataSolicitacao = (DateChooser) new RequiredDateChooser(path(proxy.getDataSolicitacao())).setLabel(new Model(bundle("dataSolicitacao"))).setEnabled(enableField));
        getForm().add(getDropDownDataCompetencia(path(proxy.getCompetencia())).setEnabled(enableField));

        getForm().add(getDropDownRequisicoesPadroes());
        getForm().add(tblExamesPadrao = new MultiSelectionTable("tblExamesPadrao", getColumnsRequisicoesPadrao(), getCollectionProviderRequisicoesPadrao()));
        tblExamesPadrao.setScrollY("140px");
        tblExamesPadrao.populate();

        form.add(new AbstractAjaxButton("btnAdicionarPadrao") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionarRequisicaoPadrao(target);
            }
        });

        getFormExameProcedimento().add(autoCompleteConsultaExameProcedimento = (AutoCompleteConsultaExameProcedimento) new AutoCompleteConsultaExameProcedimento("exameAdd").setEnabled(enableField));
        getFormExameProcedimento().add(txtQuantidade = (LongField) new LongField("quantidadeAdd").setVMin(1L).setInitialValue(1L).setEnabled(enableField));

        getFormExameProcedimento().add(btnAdicionar = new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        }).setEnabled(enableField);

        getForm().add(tableExame = new Table("tableExame", getColumns(), getCollectionProvider()));
        tableExame.populate();
        tableExame.setEnabled(enableField);

        WebMarkupContainer containerCota = new WebMarkupContainer("containerCota", new CompoundPropertyModel(this));
        containerCota.add(txtCotaUnidade = new DisabledDoubleField("cotaUnidade"));
        containerCota.add(txtCotaUtilizada = new DisabledDoubleField("cotaUtilizada"));
        containerCota.add(txtSaldo = new DisabledDoubleField("saldo"));

        getForm().add(containerCota);

        String demonstraValorContaSaldo = null;
        try {
            demonstraValorContaSaldo = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).getParametro("demonstraValorContaSaldo");
        } catch (DAOException ex) {
            Logger.getLogger(CadastroRequisicaoExamePanel.class.getName()).log(Level.SEVERE, null, ex);
        }

        containerCota.setVisible(RepositoryComponentDefault.SIM.equals(demonstraValorContaSaldo));
        WebMarkupContainer containerTotal = new WebMarkupContainer("containerTotal", new CompoundPropertyModel(this));
        containerTotal.add(txtTotal = new DisabledDoubleField("total"));
        getForm().add(containerTotal);

        AjaxButton btnVoltar;
        getForm().add(btnVoltar = new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getRecepcaoController().changePanel(target, defaultRecepcaoPanel);
            }
        });
        btnVoltar.setDefaultFormProcessing(false);

        btnSalvar = getBtnSalvarOuSalvarAvancar("btnSalvar");
        btnSalvarAvancar = getBtnSalvarOuSalvarAvancar("btnSalvarAvancar");

        getForm().add(btnSalvar);
        getForm().add(btnSalvarAvancar);

        configuraVisibilidadeBotoes();

        getForm().add(getFormExameProcedimento());
        if (codigoExameAutorizacao != null) {
            try {
                carregarExame(null);
            } catch (DAOException | ValidacaoException ex) {
                Logger.getLogger(CadastroRequisicaoExamePanel.class.getName()).log(Level.SEVERE, null, ex);
            }
        }
        form.add(containerDescricaoDadoClinico = new WebMarkupContainer("containerDescricaoDadoClinico"));
        containerDescricaoDadoClinico.setOutputMarkupId(true);
        containerDescricaoDadoClinico.add(new InputArea(path(proxy.getDescricaoDadoClinico())));

        add(getForm());
    }

    public void configuraVisibilidadeBotoes() {
        if (getRecepcaoController().isActionPermitted(Permissions.SALVAR)) {
            btnSalvarAvancar.setVisible(enableField);
            btnSalvar.setVisible(false);
        } else {
            btnSalvarAvancar.setVisible(false);
            btnSalvar.setVisible(enableField);
        }
    }

    private AbstractAjaxButton getBtnSalvarOuSalvarAvancar(String id) {
        return new AbstractAjaxButton(id) {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarAvancar(target);
            }
        };
    }

    private void adicionarRequisicaoPadrao(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        if (tblExamesPadrao.getSelectedObjects() == null || tblExamesPadrao.getSelectedObjects().isEmpty()) {
            throw new ValidacaoException(bundle("selecionePeloMenosUmExamePadrao"));
        }
        for (RequisicaoPadraoExame rpe : tblExamesPadrao.getSelectedObjects()) {
            adicionar(target, rpe.getExameProcedimento(), false);
        }
    }

    private List<IColumn> getColumnsRequisicoesPadrao() {
        List<IColumn> columns = new ArrayList<IColumn>();

        RequisicaoPadraoExame proxy = on(RequisicaoPadraoExame.class);

        columns.add(createColumn(bundle("descricao"), proxy.getExameProcedimento().getDescricaoProcedimento()));
        columns.add(createColumn(bundle("tipoExame"), proxy.getExameProcedimento().getTipoExame().getDescricao()));

        return columns;
    }

    private ICollectionProvider getCollectionProviderRequisicoesPadrao() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return requisicoesPadraoExames;
            }
        };
    }

    private DropDown getDropDownRequisicoesPadroes() {
        DropDown dropDownRequisicoesPadrao = new DropDown("requisicoesPadroes", new PropertyModel(this, "requisicaoPadrao"));
        dropDownRequisicoesPadrao.addChoice(null, bundle("selecione"));

        List<RequisicaoPadrao> requisicaoPadraoList = LoadManager.getInstance(RequisicaoPadrao.class)
                .addParameter(new QueryCustom.QueryCustomParameter(RequisicaoPadrao.PROP_STATUS, RequisicaoPadrao.STATUS_ATIVO))
                .addSorter(new QueryCustom.QueryCustomSorter(RequisicaoPadrao.PROP_DESCRICAO))
                .start().getList();
        for (RequisicaoPadrao rp : requisicaoPadraoList) {
            dropDownRequisicoesPadrao.addChoice(rp, rp.getDescricao());
        }

        dropDownRequisicoesPadrao.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                procurarRequisicoesPadraoExames(requisicaoPadrao);
                tblExamesPadrao.updateAndClearSelection(target);
            }
        });
        return dropDownRequisicoesPadrao;
    }

    private void procurarRequisicoesPadraoExames(RequisicaoPadrao requisicaoPadrao) {
        if (requisicaoPadrao != null) {
            try {
                requisicoesPadraoExames = BOFactoryWicket.getBO(AtendimentoFacade.class).requisicoesPadraoExames(requisicaoPadrao, getForm().getModel().getObject().getProfissional());
            } catch (DAOException | ValidacaoException ex) {
                Logger.getLogger(ExameNaoSusViewPanel.class.getName()).log(Level.SEVERE, null, ex);
            }
        } else {
            requisicoesPadraoExames = new ArrayList<>();
        }
    }

    private DropDown<Date> getDropDownDataCompetencia(String id) {
        if (dropDownDataCompetencia == null) {
            dropDownDataCompetencia = new DropDown<>(id);
            try {
                int diaInicioCompetencia = BOFactoryWicket.getBO(CommomFacade.class).modulo(Modulos.UNIDADE_SAUDE).<Long>getParametro("diaInicioCompetencia").intValue();
                competenciaAtual = Data.competenciaData(diaInicioCompetencia, DataUtil.getDataAtual());
                Date proxCompetencia = Data.addMeses(competenciaAtual, 1);

                Long count = LoadManager.getInstance(ExamePrestadorCompetencia.class)
                        .addGroup(new QueryCustom.QueryCustomGroup(ExamePrestadorCompetencia.PROP_CODIGO, QueryCustom.QueryCustomGroup.COUNT))
                        .addParameter(new QueryCustomParameter(ExamePrestadorCompetencia.PROP_DATA_COMPETENCIA, BuilderQueryCustom.QueryParameter.MAIOR_IGUAL, proxCompetencia))
                        .start().getVO();

                dropDownDataCompetencia.addChoice(competenciaAtual, new SimpleDateFormat("MM/yyyy").format(competenciaAtual));
                if (count > 0) {
                    dropDownDataCompetencia.addChoice(proxCompetencia, new SimpleDateFormat("MM/yyyy").format(proxCompetencia));
                }

                dropDownDataCompetencia.add(new AjaxFormComponentUpdatingBehavior("onchange") {
                    @Override
                    protected void onUpdate(AjaxRequestTarget target) {
                        if (competenciaAtual.equals(getForm().getModel().getObject().getCompetencia())) {
                            if (dropDownDataCompetencia.getBehaviors().contains(redTextModifier)) {
                                dropDownDataCompetencia.remove(redTextModifier);
                            }
                        } else {
                            if (!dropDownDataCompetencia.getBehaviors().contains(redTextModifier)) {
                                dropDownDataCompetencia.add(redTextModifier);
                            }
                        }
                        target.add(dropDownDataCompetencia);

                    }
                });
            } catch (SGKException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return dropDownDataCompetencia;
    }

    private Long getQuantidadeTotalExames() {
        Long quantidadeTotal = 0L;

        for (ExameProcedimentoDTO dto : lstExameProcedimentoDto) {
            quantidadeTotal += dto.getQuantidade();
        }

        return quantidadeTotal;
    }

    private void validacoesAdicionar(ExameProcedimento ep, boolean isProcIndividual) throws DAOException, ValidacaoException {
        if (getForm().getModel().getObject().getEstabelecimento() == null) {
            throw new ValidacaoException(BundleManager.getString("msgInformeEstabelecimentoSolicitante"));
        }
        if (getForm().getModel().getObject().getUsuarioCadsus() == null) {
            throw new ValidacaoException(BundleManager.getString("msgInformePaciente"));
        }
        if (ep == null) {
            throw new ValidacaoException(BundleManager.getString("msgInformeProcedimento"));
        }
        if (CollectionUtils.isNotNullEmpty(lstExameProcedimentoDto)) {
            if (ep.getTipoExame() == null || !ep.getTipoExame().equals(lstExameProcedimentoDto.get(0).getExameProcedimento().getTipoExame())) {
                throw new ValidacaoException(BundleManager.getString("msgSomenteExamesMesmoTipoExame"));
            }
            if (ep.getProcedimento() == null) {
                throw new ValidacaoException(BundleManager.getString("msgExameSemProcedimentoDefinido"));
            }
        }
        ProcedimentoCompetencia procedimentoCompetencia = validarPrecedimentoSexoPaciente(ep);
        if (procedimentoCompetencia != null) {
            if (RepositoryComponentDefault.SEXO_MASCULINO.equals(procedimentoCompetencia.getTipoSexo()) && RepositoryComponentDefault.SEXO_FEMININO.equals(getForm().getModel().getObject().getUsuarioCadsus().getSexo())) {
                throw new ValidacaoException(BundleManager.getString("msgProcedimentoXDestinadoHomens", ep.getDescricaoFormatado()));
            } else if (RepositoryComponentDefault.SEXO_FEMININO.equals(procedimentoCompetencia.getTipoSexo()) && RepositoryComponentDefault.SEXO_MASCULINO.equals(getForm().getModel().getObject().getUsuarioCadsus().getSexo())) {
                throw new ValidacaoException(BundleManager.getString("msgProcedimentoXDestinadoMulheres", ep.getDescricaoFormatado()));
            }
        } else {
            throw new ValidacaoException(BundleManager.getString("msgProcedimentoNaoEncontrado", ep.getDescricaoFormatado(), getForm().getModel().getObject().getCompetencia()));
        }
        if (isProcIndividual) {
            verificarQtdExameRequirido();
        }

        if (!procedimentoCompetencia.getValorIdadeMaxima().equals(UsuarioCadsus.PROP_MAX_IDADE)
                && !procedimentoCompetencia.getValorIdadeMinima().equals(UsuarioCadsus.PROP_MAX_IDADE) && getForm().getModel().getObject().getUsuarioCadsus() != null
                && getForm().getModel().getObject().getUsuarioCadsus().getIdadeEmMeses() != null) {
            if (getForm().getModel().getObject().getUsuarioCadsus().getIdadeEmMeses() > procedimentoCompetencia.getValorIdadeMaxima()
                    || getForm().getModel().getObject().getUsuarioCadsus().getIdadeEmMeses() < procedimentoCompetencia.getValorIdadeMinima()) {
                throw new ValidacaoException(BundleManager.getString("msgExameProcedimentoForaFaixaEtaria", ep.getProcedimento().getDescricaoFormatado(),
                        procedimentoCompetencia.getValorIdadeMinima(), procedimentoCompetencia.getValorIdadeMaxima()));
            }
        }
    }

    private void verificarQtdExameRequirido() throws ValidacaoException {
        Long qtdMaxReq = getQtdTpExame();
        if (qtdMaxReq != null) {
            if (quantidadeAdd.compareTo(qtdMaxReq) > 0) {
                throw new ValidacaoException(BundleManager.getString("msgQtdMaiorQueQtdMaximaExame", qtdMaxReq));
            }
        }
    }

    private Long getQtdTpExame() {
        TipoExame tpExame = LoadManager.getInstance(TipoExame.class)
                .addProperty(TipoExame.PROP_QUANTIDADE_EXAME_REQUISICAO)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoExame.PROP_CODIGO, exameAdd.getTipoExame().getCodigo()))
                .start()
                .getVO();

        return tpExame.getQuantidadeExameRequisicao();
    }

    private ProcedimentoCompetencia validarPrecedimentoSexoPaciente(ExameProcedimento ep) {
        ProcedimentoCompetencia procedimentoCompetencia = LoadManager.getInstance(ProcedimentoCompetencia.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_DATA_COMPETENCIA), CargaBasicoPadrao.getInstance().getParametroPadrao().getDataCompetenciaProcedimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ProcedimentoCompetencia.PROP_ID, ProcedimentoCompetenciaPK.PROP_PROCEDIMENTO), ep.getProcedimento()))
                .start().getVO();
        return procedimentoCompetencia;
    }

    private void adicionar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        adicionar(target, exameAdd, true);
    }

    private void adicionar(AjaxRequestTarget target, ExameProcedimento ep, boolean isProcIndividual) throws DAOException, ValidacaoException {
        validacoesAdicionar(ep, isProcIndividual);
        Long quantidadeOld = 0L;

        ExameProcedimentoDTO exameProcedimentoDTO = null;
        for (ExameProcedimentoDTO dto_ : lstExameProcedimentoDto) {
            if (dto_.getExameProcedimento().equals(ep)) {
                if (Empresa.TIPO_ESTABELECIMENTO_SECRETARIA_SAUDE.equals(getForm().getModel().getObject().getEstabelecimento().getTipoUnidade())) {
                    exameProcedimentoDTO = dto_;
                    quantidadeOld = exameProcedimentoDTO.getQuantidade();
//                    dlgConfirmacaoAdicionar.setQuantidadeOld(quantidadeOld);
//                    dlgConfirmacaoAdicionar.setExameProcedimentoDTO(exameProcedimentoDTO);
//                    dlgConfirmacaoAdicionar.show(target);
                    return;
                } else {
                    throw new ValidacaoException(BundleManager.getString("msgExameJaAdicionadoX", ep.getDescricaoProcedimento()));
                }
            }
        }
        concluiAdicionar(exameProcedimentoDTO, Coalesce.asLong(quantidadeAdd, 1L), quantidadeOld, target, ep);
    }

    public void concluiAdicionar(ExameProcedimentoDTO exameProcedimentoDTO, Long quantidade, Long quantidadeOld, AjaxRequestTarget target, ExameProcedimento ep) throws ValidacaoException, DAOException {
//        Double valorProcedimento = ProcedimentoHelper.getValorExameProcedimento(exameAdd, null);
//        exameAdd.setValorProcedimento(valorProcedimento);

        if (exameProcedimentoDTO == null) {
            exameProcedimentoDTO = new ExameProcedimentoDTO();
            exameProcedimentoDTO.setExameProcedimento(ep);
            exameProcedimentoDTO.setQuantidade(quantidade);
        }
        if (quantidade < 1L) {
            throw new ValidacaoException(BundleManager.getString("msgQuantidadeInvalida"));
        }

        calcularCotas(target, exameProcedimentoDTO, quantidadeOld);
    }

    private void calcularCotas(AjaxRequestTarget target, ExameProcedimentoDTO exameProcedimentoDTO, Long quantidadeOld) throws DAOException, ValidacaoException {
        ExameCadastroAprovacaoDTO param = new ExameCadastroAprovacaoDTO();
        param.setCodigoUnidade(getForm().getModel().getObject().getEstabelecimento().getCodigo());

        param.setExameProcedimentoDTO(exameProcedimentoDTO);

        param.getExameProcedimentoDTOs().addAll(this.lstExameProcedimentoDto);
        final Double totalExames = total - (quantidadeOld * exameProcedimentoDTO.getExameProcedimento().getValorProcedimento());
        param.setTotalExamesAtual(totalExames);
        param.setCompetencia(getForm().getModel().getObject().getCompetencia());

        ExameCadastroAprovacaoDTO exameCadastroAprovacaoDTO = BOFactoryWicket.getBO(ExameFacade.class).adicionarExameAprovacao(param);
        ExameProcedimentoDTO _dto = exameCadastroAprovacaoDTO.getExameProcedimentoDTO();

        cotaUnidade = new Dinheiro(Coalesce.asDouble(exameCadastroAprovacaoDTO.getCotaUnidade())).doubleValue();
        cotaUtilizada = new Dinheiro(Coalesce.asDouble(exameCadastroAprovacaoDTO.getCotaUtilizada())).doubleValue();

        saldo = new Dinheiro(cotaUnidade).subtrair(cotaUtilizada).doubleValue();
        if (quantidadeOld == 0L) {
            lstExameProcedimentoDto.add(_dto);
        }

        atualizarTotalExame();
        if (target != null) {
            target.add(txtCotaUnidade);
            target.add(txtCotaUtilizada);
            target.add(txtSaldo);
            target.add(txtTotal);
            tableExame.update(target);
            limpar(target);
            target.focusComponent(autoCompleteConsultaExameProcedimento.getTxtDescricao().getTextField());
        }
    }

    private void limpar(AjaxRequestTarget target) {
        this.quantidadeAdd = 1L;
        autoCompleteConsultaExameProcedimento.limpar(target);
        target.add(txtQuantidade);
    }

    private void atualizarTotalExame() {
        if (CollectionUtils.isNotNullEmpty(lstExameProcedimentoDto)) {
            Dinheiro totalExame = new Dinheiro(0D);

            if (CotasExamesHelper.isTipoTetoFisico(lstExameProcedimentoDto.get(0).getExameProcedimento().getTipoExame())) {
                for (ExameProcedimentoDTO exameProcedimentoDTO : lstExameProcedimentoDto) {
                    totalExame = totalExame.somar(Double.valueOf(exameProcedimentoDTO.getQuantidade()));
                }
            } else {
                for (ExameProcedimentoDTO exameProcedimentoDTO : lstExameProcedimentoDto) {
                    totalExame = totalExame.somar(exameProcedimentoDTO.getValorTotal());
                }
            }

            total = totalExame.doubleValue();
        } else {
            total = 0D;
        }
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        ExameProcedimentoDTO on = on(ExameProcedimentoDTO.class);

        columns.add(getCustomColumn());
        columns.add(createColumn(bundle("procedimento"), on.getExameProcedimento().getDescricaoFormatado()));
        columns.add(createColumn(bundle("tipoExame"), on.getExameProcedimento().getTipoExame().getDescricao()));
        columns.add(createColumn(bundle("quantidade"), on.getQuantidade()));
        columns.add(createColumn(bundle("valor"), on.getValor()));

        return columns;
    }

    private CustomColumn getCustomColumn() {
        return new CustomColumn<ExameProcedimentoDTO>() {
            @Override
            public Component getComponent(String componentId, final ExameProcedimentoDTO rowObject) {
                return new RemoverActionColumnPanel(componentId) {
                    @Override
                    public void onRemover(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                        removerItem(target, rowObject);
                    }
                };
            }
        };
    }

    private void removerItem(AjaxRequestTarget target, ExameProcedimentoDTO rowObject) throws ValidacaoException, DAOException {
        ExameCadastroAprovacaoDTO param = new ExameCadastroAprovacaoDTO();
        param.setCodigoUnidade(getForm().getModel().getObject().getEstabelecimento().getCodigo());

        param.setTotalExamesAtual(total - rowObject.getValorTotal());

        ExameProcedimentoDTO exameProcedimentoDTO = new ExameProcedimentoDTO();
        exameProcedimentoDTO.setExameProcedimento(rowObject.getExameProcedimento());
        exameProcedimentoDTO.setQuantidade(rowObject.getQuantidade());
        param.setExameProcedimentoDTO(exameProcedimentoDTO);

        lstExameProcedimentoDto.remove(rowObject);

        param.getExameProcedimentoDTOs().addAll(lstExameProcedimentoDto);
        param.getExameProcedimentoDTOs().remove(rowObject);
        param.setCompetencia(getForm().getModel().getObject().getCompetencia());
        ExameCadastroAprovacaoDTO exameCadastroAprovacaoDTO = BOFactoryWicket.getBO(ExameFacade.class).contarCotasPrestadorasExameAprovacao(param);

        cotaUnidade = new Dinheiro(Coalesce.asDouble(exameCadastroAprovacaoDTO.getCotaUnidade())).doubleValue();
        cotaUtilizada = new Dinheiro(Coalesce.asDouble(exameCadastroAprovacaoDTO.getCotaUtilizada())).doubleValue();
        saldo = new Dinheiro(cotaUnidade).subtrair(cotaUtilizada).doubleValue();

//        this.lstExamePrestadorCompetencia = new ArrayList<ExamePrestadorCompetencia>(exameCadastroAprovacaoDTO.getPrestadores());
        atualizarTotalExame();

        target.add(txtCotaUnidade);
        target.add(txtCotaUtilizada);
        target.add(txtSaldo);
        target.add(txtTotal);
        tableExame.update(target);
//        tablePrestadores.update(target);
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return lstExameProcedimentoDto;
            }
        };
    }

    private Form<ExameCadastroAprovacaoDTO> getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", new CompoundPropertyModel<>(new ExameCadastroAprovacaoDTO()));
            if (form.getModel().getObject().getDataSolicitacao() == null) {
                form.getModel().getObject().setDataSolicitacao(DataUtil.getDataAtual());
            }
        }
        return this.form;
    }

    private Form getFormExameProcedimento() {
        if (this.formExameProcedimento == null) {
            this.formExameProcedimento = new Form("formExameProcedimento", new CompoundPropertyModel<>(this));
        }
        return this.formExameProcedimento;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response); //To change body of generated methods, choose Tools | Templates.
        response.render(OnDomReadyHeaderItem.forScript(JScript.focusComponent(txtNumeroRequisicao)));
        response.render(OnLoadHeaderItem.forScript(JScript.initExpandLinks()));
        response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(getFormExameProcedimento())));
    }

    private void carregarExame(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (target != null) {
            autoCompleteConsultaEmpresa.limpar(target);
            autoCompleteConsultaUsuarioCadsus.limpar(target);
            autoCompleteConsultaProfissional.limpar(target);
            dchDataSolicitacao.limpar(target);
            autoCompleteConsultaExameProcedimento.limpar(target);
            txtQuantidade.limpar(target);
        }
        lstExameProcedimentoDto.clear();
        Exame exame = null;

        LoadManager lm = LoadManager.getInstance(Exame.class)
                .addProperties(new HQLProperties(Exame.class).getProperties())
                .addProperty(VOUtils.montarPath(Exame.PROP_ATENDIMENTO, Atendimento.PROP_NOME_PACIENTE))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(Exame.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addProperty(VOUtils.montarPath(Exame.PROP_ATENDIMENTO, Atendimento.PROP_STATUS))
                .addProperty(VOUtils.montarPath(Exame.PROP_ATENDIMENTO, Atendimento.PROP_EMPRESA, Empresa.PROP_DESCRICAO));

        if (enableField) {
            lm.addParameter(new QueryCustomParameter(Exame.PROP_STATUS, Exame.STATUS_SOLICITADO));
        }

        if (codigoExameAutorizacao != null) {
            if (target != null) {
                txtNumeroRequisicao.limpar(target);
            }
            ExameAutorizacao ea = LoadManager.getInstance(ExameAutorizacao.class)
                    .addProperties(new HQLProperties(ExameAutorizacao.class).getProperties())
                    .addProperties(new HQLProperties(Exame.class, ExameAutorizacao.PROP_EXAME).getProperties())
                    .addProperties(new HQLProperties(Atendimento.class, VOUtils.montarPath(ExameAutorizacao.PROP_EXAME, Exame.PROP_ATENDIMENTO)).getProperties())
                    .addProperties(new HQLProperties(ExamePrestadorCompetencia.class, ExameAutorizacao.PROP_EXAME_PRESTADOR_COMPETENCIA).getProperties())
                    .addParameter(new QueryCustomParameter(ExameAutorizacao.PROP_CODIGO, codigoExameAutorizacao))
//                    .addParameter(new QueryCustomParameter(VOUtils.montarPath(ExameAutorizacao.PROP_EXAME, Exame.PROP_ATENDIMENTO, Atendimento.PROP_STATUS), Atendimento.STATUS_FINALIZADO))
                    .start().getVO();

            if (ea != null) {
                exame = lm.addParameter(new QueryCustomParameter(Exame.PROP_CODIGO, ea.getExame().getCodigo())).start().getVO();
            }
        } else if (txtNumeroRequisicao != null && txtNumeroRequisicao.getComponentValue() != null) {
            exame = lm.addParameter(new QueryCustomParameter(Exame.PROP_CODIGO, txtNumeroRequisicao.getComponentValue())).start().getVO();
        }

        if (exame != null) {
            if (exame.getAtendimento() != null && !Atendimento.STATUS_FINALIZADO.equals(exame.getAtendimento().getStatus())) {
                MessageUtil.error(target, txtNumeroRequisicao, bundle("msgNaoAutorizarAtendimentoPacienteXUnidadeXPendente", exame.getAtendimento().getNomePaciente(), exame.getAtendimento().getEmpresa().getDescricao()));
                return;
            }
            ExameCadastroAprovacaoDTO dto = getForm().getModel().getObject();
            dto.setCodigoExameCadastrado(exame.getCodigo());
            dto.setEstabelecimento(exame.getEmpresaSolicitante());
            dto.setUsuarioCadsus(exame.getUsuarioCadsus());
            dto.setProfissional(exame.getProfissional());
            dto.setDataSolicitacao(exame.getDataSolicitacao());
            dto.setDescricaoDadoClinico(exame.getDescricaoDadoClinico());

            autoCompleteConsultaEmpresa.setEnabled(false);
            autoCompleteConsultaUsuarioCadsus.setEnabled(false);
            autoCompleteConsultaProfissional.setEnabled(false);
            dchDataSolicitacao.setEnabled(false);

            List<ExameRequisicao> list = LoadManager.getInstance(ExameRequisicao.class)
                    .addProperties(new HQLProperties(ExameRequisicao.class).getProperties())
                    .addProperties(new HQLProperties(ExameProcedimento.class, ExameRequisicao.PROP_EXAME_PROCEDIMENTO).getProperties())
                    .addProperties(new HQLProperties(TipoExame.class, VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_TIPO_EXAME)).getProperties())
                    .addParameter(new QueryCustomParameter(ExameRequisicao.PROP_EXAME, exame))
                    .addParameter(new QueryCustom.QueryCustomParameter(ExameRequisicao.PROP_STATUS, QueryCustom.QueryCustomParameter.DIFERENTE, ExameRequisicao.Status.CANCELADO.value()))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(list)) {
                lstExameProcedimentoDto.clear();

                ExameProcedimentoDTO procedimentoDTO;
                for (ExameRequisicao er : list) {
                    procedimentoDTO = new ExameProcedimentoDTO();
                    procedimentoDTO.setExameRequisicao(er);
                    procedimentoDTO.setExameProcedimento(er.getExameProcedimento());
                    procedimentoDTO.setQuantidade(er.getQuantidade());
                    procedimentoDTO.setValor(er.getValorProcedimento());

                    lstExameProcedimentoDto.add(procedimentoDTO);

                    calcularCotas(target, procedimentoDTO, er.getQuantidade());
                }
            }
            tableExame.setEnabled(false);
        } else {
            autoCompleteConsultaEmpresa.setEnabled(enableField);
            autoCompleteConsultaUsuarioCadsus.setEnabled(enableField);
            autoCompleteConsultaProfissional.setEnabled(enableField);
            dchDataSolicitacao.setEnabled(enableField);
            autoCompleteConsultaExameProcedimento.setEnabled(enableField);
            txtQuantidade.setEnabled(enableField);
            if (target != null) {
                txtNumeroRequisicao.limpar(target);
            }
        }

        if (target != null) {
            target.add(txtNumeroRequisicao);
            target.add(autoCompleteConsultaEmpresa);
            target.add(autoCompleteConsultaUsuarioCadsus);
            target.add(autoCompleteConsultaProfissional);
            target.add(dchDataSolicitacao);
            target.add(autoCompleteConsultaExameProcedimento);
            target.add(txtQuantidade);
            target.add(tableExame);
            target.add(containerDescricaoDadoClinico);
        }
    }

    private void salvarAvancar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        try {
            ExameCadastroAprovacaoDTO dto = (ExameCadastroAprovacaoDTO) SerializationUtils.clone(getForm().getModel().getObject());

            dto.setCodigoUnidade(getForm().getModel().getObject().getEstabelecimento().getCodigo());
            dto.setUsuarioCadsus(getForm().getModel().getObject().getUsuarioCadsus());
            dto.setProfissional(getForm().getModel().getObject().getProfissional());
            dto.setDataSolicitacao(getForm().getModel().getObject().getDataSolicitacao());
            dto.setNomePaciente(getForm().getModel().getObject().getNomePaciente());
            dto.setNomeProfissional(getForm().getModel().getObject().getNomeProfissional());
            dto.setCodigoCbo(getForm().getModel().getObject().getCodigoCbo());
            dto.getExameProcedimentoDTOs().addAll(lstExameProcedimentoDto);
            dto.setDescricaoDadoClinico(getForm().getModel().getObject().getDescricaoDadoClinico());

            dto.setTotalExamesAtual(total);

            dto = BOFactoryWicket.getBO(ExameFacade.class).salvarExameAutorizacao(dto);

            if (dto.getCodigoExameCadastrado() != null) {
                getSession().getFeedbackMessages().info(getPage(), BundleManager.getString("msgExameGeradoComNumero", dto.getCodigoExameCadastrado()));
                target.add(getPage());
                if (getRecepcaoController().isActionPermitted(Permissions.SALVAR)) {
                    DefaultRecepcaoPanel panel = new CadastroAutorizacaoExamePanel(getRecepcaoController().panelId(), this, dto.getCodigoExameAutorizacao());
                    getRecepcaoController().changePanel(target, panel);
                } else {
                    getRecepcaoController().changePanel(target, defaultRecepcaoPanel);
                }
            }
        } catch (ValidacaoException ex) {
            ExameCadastroAprovacaoDTO dto = (ExameCadastroAprovacaoDTO) ex.getValues().get("COTAS");
            if (dto != null) {
                cotaUnidade = dto.getCotaUnidade();
                cotaUtilizada = dto.getCotaUtilizada();
                saldo = new Dinheiro(cotaUnidade).subtrair(cotaUtilizada).doubleValue();
                target.add(txtCotaUnidade);
                target.add(txtCotaUtilizada);
                target.add(txtSaldo);
            }
            throw ex;
        }
    }
}
