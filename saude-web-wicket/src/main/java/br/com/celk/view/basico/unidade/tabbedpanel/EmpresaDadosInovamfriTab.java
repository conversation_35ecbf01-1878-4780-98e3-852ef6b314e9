package br.com.celk.view.basico.unidade.tabbedpanel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.utils.CrudUtils;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.basico.dto.CadastroEmpresaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EmpresaEspecialidades;
import org.apache.commons.lang.SerializationUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class EmpresaDadosInovamfriTab extends TabPanel<CadastroEmpresaDTO> {

    private Table<EmpresaEspecialidades> tblEmpresaEspecialidades;
    private EmpresaEspecialidades empresaEspecialidadesEdicao;
    private EmpresaEspecialidades empresaEspecialidades;
    private List<EmpresaEspecialidades> empresaEspecialidadesList;
    private Form<EmpresaEspecialidades> form;
    private InputField txtDescricao;
    private InputField txtHorarioAtendimento;

    public EmpresaDadosInovamfriTab(String id, CadastroEmpresaDTO object) {
        super(id, object);
        empresaEspecialidadesList = object.getEmpresaEspecialidadesList();
        init();
    }

    private void init() {
        add(DropDownUtil.getNaoSimLongDropDown(VOUtils.montarPath(CadastroEmpresaDTO.PROP_EMPRESA, Empresa.PROP_FLAG_INTEGRAR)));
        add(DropDownUtil.getNaoSimLongDropDown(VOUtils.montarPath(CadastroEmpresaDTO.PROP_EMPRESA, Empresa.PROP_FLAG_EXIBIR_ESTOQUE)));

        getForm().add(txtDescricao = new InputField<String>(EmpresaEspecialidades.PROP_DESCRICAO));
        getForm().add(txtHorarioAtendimento = new InputField<String>(EmpresaEspecialidades.PROP_HORARIO_ATENDIMENTO));
        getForm().add(DropDownUtil.getSimNaoLongDropDown(EmpresaEspecialidades.PROP_ATIVO));
        txtDescricao.addRequiredClass();
        txtHorarioAtendimento.addRequiredClass();
        getForm().add(new AbstractAjaxButton("btnAdicionar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                adicionar(target);
            }
        });

        getForm().add(tblEmpresaEspecialidades = new Table("tblEmpresaEspecialidades", getColumns(), getCollectionProvider()));
        tblEmpresaEspecialidades.populate();

        add(getForm());
    }

    private Form<EmpresaEspecialidades> getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(empresaEspecialidades == null ? (empresaEspecialidades = new EmpresaEspecialidades()) : empresaEspecialidades));
        }
        return this.form;
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList();

        EmpresaEspecialidades proxy = on(EmpresaEspecialidades.class);
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("descricao"), proxy.getDescricao()));
        columns.add(createColumn(bundle("horarioAtendimento"), proxy.getHorarioAtendimento()));

        return columns;
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<EmpresaEspecialidades>() {

            @Override
            public void customizeColumn(final EmpresaEspecialidades rowObject) {
                addAction(ActionType.EDITAR, rowObject, new IModelAction<EmpresaEspecialidades>() {
                    @Override
                    public void action(AjaxRequestTarget target, EmpresaEspecialidades modelObject) throws ValidacaoException, DAOException {
                        editar(target, modelObject);
                    }
                });

                addAction(ActionType.REMOVER, rowObject, new IModelAction<EmpresaEspecialidades>() {
                    @Override
                    public void action(AjaxRequestTarget target, EmpresaEspecialidades modelObject) throws ValidacaoException, DAOException {
                        CrudUtils.removerItem(target, tblEmpresaEspecialidades, empresaEspecialidadesList, modelObject);
                    }
                });
            }
        };
    }


    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return empresaEspecialidadesList;
            }
        };
    }

    private void editar(AjaxRequestTarget target, EmpresaEspecialidades modelObject) {
        limpar(target);
        empresaEspecialidadesEdicao = modelObject;
        form.setModelObject((EmpresaEspecialidades) SerializationUtils.clone(modelObject));
//        target.add(containerIndices);
    }

    private void limpar(AjaxRequestTarget target) {
        form.setModelObject(new EmpresaEspecialidades());
        empresaEspecialidadesEdicao = null;
        txtDescricao.limpar(target);
        txtHorarioAtendimento.limpar(target);

        target.add(form);
        target.add(txtDescricao);
        target.add(txtHorarioAtendimento);
    }

    private void adicionar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        EmpresaEspecialidades empEspecialidade = form.getModel().getObject();

        validarAdd(empEspecialidade);

        Integer idx = null;
        for (int i = 0; i < empresaEspecialidadesList.size(); i++) {
            EmpresaEspecialidades item = empresaEspecialidadesList.get(i);
            if (empresaEspecialidadesEdicao != null && empresaEspecialidadesEdicao == item) {
                idx = i;
            } else if (empEspecialidade.getDescricao().equals(item.getDescricao())) {
                throw new ValidacaoException(BundleManager.getString("jaExisteIndiceMesmaDataInicio"));
            }
        }

        if (empresaEspecialidadesEdicao != null && idx != null) {
            empresaEspecialidadesList.remove(idx.intValue());
            empresaEspecialidadesList.add(idx, empEspecialidade);
        } else {
            empresaEspecialidadesList.add(0, empEspecialidade);
        }

        tblEmpresaEspecialidades.populate();
        tblEmpresaEspecialidades.update(target);

        form.setModelObject(new EmpresaEspecialidades());
        limpar(target);
//        target.add(containerIndices);
//        target.focusComponent(dataInicio);
        empresaEspecialidadesEdicao = null;
    }

    private void validarAdd(EmpresaEspecialidades empEspecialidade) throws ValidacaoException {
        if (empEspecialidade != null && "".equals(empEspecialidade.getDescricao()) || empEspecialidade.getDescricao().isEmpty()) {
            throw new ValidacaoException(BundleManager.getString("msgInformeEspecialidade"));
        }
        if (empEspecialidade != null && empEspecialidade.getHorarioAtendimento() != null && ("".equals(empEspecialidade.getHorarioAtendimento()) || empEspecialidade.getHorarioAtendimento().isEmpty())) {
            throw new ValidacaoException(BundleManager.getString("msgInformeHorarioAtendimento"));
        }
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("inovamfri");
    }
}
