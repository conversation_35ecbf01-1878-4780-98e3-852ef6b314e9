package br.com.celk.view.materiais.emprestimo.lancamentos.registroemprestimo.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.emprestimo.LancamentoEmprestimo;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.model.LoadableDetachableModel;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgCancelarLancamentoEmprestimo extends Window{
    
    private PnlCancelarLancamentoEmprestimo pnlCancelarLancamentoEmprestimo;
    
    public DlgCancelarLancamentoEmprestimo(String id){
        super(id);
        init();
    }

    private void init() {
        setTitle(new LoadableDetachableModel<String>(){
           
            @Override
            protected String load(){
                return BundleManager.getString("cancelamentoEmprestimo");
            }
        });
                
        setInitialWidth(500);
        setInitialHeight(180);
        setResizable(true);
        
        setContent(pnlCancelarLancamentoEmprestimo = new PnlCancelarLancamentoEmprestimo(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, LancamentoEmprestimo lancamentoEmprestimo, String motivo) throws ValidacaoException, DAOException {
                close(target);
                DlgCancelarLancamentoEmprestimo.this.onConfirmar(target, lancamentoEmprestimo, motivo);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }
    
    public abstract void onConfirmar(AjaxRequestTarget target, LancamentoEmprestimo lancamentoEmprestimo, String motivo) throws ValidacaoException, DAOException;
    
    public void show(AjaxRequestTarget target, LancamentoEmprestimo lancamentoEmprestimo){
        show(target);
        pnlCancelarLancamentoEmprestimo.setObject(target, lancamentoEmprestimo);
    }    
}