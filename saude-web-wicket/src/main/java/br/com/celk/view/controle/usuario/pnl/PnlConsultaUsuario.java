package br.com.celk.view.controle.usuario.pnl;

import br.com.celk.component.consulta.PnlConsulta;
import br.com.celk.component.consulta.configurator.CustomizeConsultaConfigurator;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.controle.usuario.customize.CustomizeConsultaUsuario;
import br.com.ksisolucoes.system.consulta.ICustomizeConsultaQuery;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class PnlConsultaUsuario extends PnlConsulta<Usuario> {

    public PnlConsultaUsuario(String id, IModel<Usuario> model, boolean required) {
        super(id, model, required);
    }

    public PnlConsultaUsuario(String id, IModel<Usuario> model) {
        super(id, model);
    }

    public PnlConsultaUsuario(String id, boolean required) {
        super(id, required);
    }

    public PnlConsultaUsuario(String id) {
        super(id);
    }

    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new CustomizeConsultaConfigurator() {

            @Override
            public ICustomizeConsultaQuery getCustomizeConsultaInstance() {
                return new CustomizeConsultaUsuario();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("usuarios");
    }

}
