package br.com.celk.view.hospital.faturamento.dialogs;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import static br.com.celk.system.methods.WicketMethods.bundle;
import br.com.celk.view.hospital.faturamento.dialogs.dto.InformarPrestadorDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarPrestador extends Window {

    private PnlInformarPrestador pnlInformarPrestador;

    public DlgInformarPrestador(String id) {
        super(id);
        init();
    }

    public void init() {
        setInitialHeight(240);
        setInitialWidth(600);

        setResizable(true);

        setTitle(BundleManager.getString("informarPrestador"));

        setContent(pnlInformarPrestador = new PnlInformarPrestador(getContentId()) {

            @Override
            public void onConfirmar(AjaxRequestTarget target, InformarPrestadorDTO dto) throws DAOException, ValidacaoException {
                if (ItemContaPaciente.AtribuirValorPara.TERCEIRO.value().equals(dto.getAtribuirValorPara())) {
                    if (dto.getEmpresa() == null) {
                        throw new ValidacaoException(bundle("informePrestador"));
                    }
                }
                onFechar(target);
                DlgInformarPrestador.this.onConfirmar(target, dto);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                pnlInformarPrestador.limpar(target);
                close(target);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target, InformarPrestadorDTO dto) throws DAOException, ValidacaoException;
}
