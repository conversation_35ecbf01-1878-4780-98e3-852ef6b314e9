package br.com.celk.view.materiais.estoque.relatorio;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.annotation.authorization.Private;
import br.com.celk.component.inputfield.RequiredInputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.template.report.RelatorioPage;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.dto.RelatorioPrevisaoEstoqueDTOParam;
import br.com.ksisolucoes.report.entrada.estoque.interfaces.facade.EstoqueReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupoPK;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.Model;

/**
 *
 * <AUTHOR>
 */
@Private
public class RelatorioPrevisaoEstoquePage extends RelatorioPage<RelatorioPrevisaoEstoqueDTOParam> {


    private DropDown<GrupoProduto> dropDownGrupoProduto;
    private DropDown<SubGrupo> dropDownSubGrupo;
    private DropDown ordenacao;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;

    @Override
    public void init(Form form) {
        form.add(new AutoCompleteConsultaEmpresa("estabelecimento"));
        form.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto("produto"));
        autoCompleteConsultaProduto.setIncluirInativos(true);
        form.add(getDropDownGrupo());
        form.add(getDropDownSubGrupo());
        form.add(DropDownUtil.getIEnumDropDown("tipoPrevisao", RelatorioPrevisaoEstoqueDTOParam.TipoPrevisao.values()));
        form.add(new RequiredInputField("quantidadeDiasMeses").setLabel(new Model(BundleManager.getString("quantidade"))));
        form.add(DropDownUtil.getNaoSimLongDropDown("agruparEstabelecimento"));
        form.add(DropDownUtil.getIEnumDropDown("ordenacao", RelatorioPrevisaoEstoqueDTOParam.Ordenacao.values()));
        form.add(DropDownUtil.getEnumDropDown("tipoOrdenacao", RelatorioPrevisaoEstoqueDTOParam.TipoOrdenacao.values()));
    }

    @Override
    public Class getDTOParamClass() {
        return RelatorioPrevisaoEstoqueDTOParam.class;
    }

    @Override
    public DataReport getDataReport(RelatorioPrevisaoEstoqueDTOParam param) throws ReportException {
        return BOFactoryWicket.getBO(EstoqueReportFacade.class).relatorioPrevisaoEstoque(param);
    }

    @Override
    public String getTituloPrograma() {
        return BundleManager.getString("previsaoDuracaoEstoque");
    }

    private DropDown<SubGrupo> getDropDownSubGrupo() {
        if (this.dropDownSubGrupo == null) {
            this.dropDownSubGrupo = new DropDown<SubGrupo>("subGrupo");
            dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));

        }

        return this.dropDownSubGrupo;
    }

    private DropDown<GrupoProduto> getDropDownGrupo() {
        if (this.dropDownGrupoProduto == null) {
            this.dropDownGrupoProduto = new DropDown<GrupoProduto>("grupoProduto");
            this.dropDownGrupoProduto.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    dropDownSubGrupo.removeAllChoices();
                    dropDownSubGrupo.addChoice(null, BundleManager.getString("selecioneGrupo"));
                    GrupoProduto grupoProduto = dropDownGrupoProduto.getComponentValue();
                    if (grupoProduto != null) {
                        List<SubGrupo> subGrupos = LoadManager.getInstance(SubGrupo.class)
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO))
                                .addProperty(VOUtils.montarPath(SubGrupo.PROP_DESCRICAO))
                                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(SubGrupo.PROP_ID, SubGrupoPK.PROP_CODIGO_GRUPO_PRODUTO), grupoProduto.getCodigo()))
                                .addSorter(new QueryCustom.QueryCustomSorter(SubGrupo.PROP_DESCRICAO))
                                .start().getList();

                        if (CollectionUtils.isNotNullEmpty(subGrupos)) {
                            dropDownSubGrupo.removeAllChoices();
                            dropDownSubGrupo.addChoice(null, BundleManager.getString("todos"));
                            for (SubGrupo subGrupo : subGrupos) {
                                dropDownSubGrupo.addChoice(subGrupo, subGrupo.getDescricao());
                            }
                        }
                        param.setSubGrupo(null);
                    }
                    target.add(dropDownSubGrupo);
                }
            });

            List<GrupoProduto> grupos = LoadManager.getInstance(GrupoProduto.class)
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_CODIGO))
                    .addProperty(VOUtils.montarPath(GrupoProduto.PROP_DESCRICAO))
                    .addSorter(new QueryCustom.QueryCustomSorter(GrupoProduto.PROP_DESCRICAO))
                    .start().getList();

            dropDownGrupoProduto.addChoice(null, BundleManager.getString("todos"));

            if (CollectionUtils.isNotNullEmpty(grupos)) {
                for (GrupoProduto grupoProduto : grupos) {
                    dropDownGrupoProduto.addChoice(grupoProduto, grupoProduto.getDescricao());
                }
            }
        }
        return this.dropDownGrupoProduto;
    }
}
