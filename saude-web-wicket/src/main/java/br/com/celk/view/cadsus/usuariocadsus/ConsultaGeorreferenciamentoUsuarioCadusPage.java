package br.com.celk.view.cadsus.usuariocadsus;

import br.com.celk.annotation.authorization.Private;
import br.com.celk.cadsus.relatorio.*;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dateperiod.PnlDatePeriod;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.longfield.LongField;
import br.com.celk.system.authorization.Permissions;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.session.ApplicationSession;
import br.com.celk.template.base.BasePage;
import br.com.celk.util.CollectionUtils;
import br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.celk.view.googlemaps.GoogleMapsConf;
import br.com.celk.view.materiais.produto.autocomplete.AutoCompleteConsultaProduto;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.celk.view.prontuario.procedimento.autocomplete.AutoCompleteConsultaProcedimento;
import br.com.celk.view.prontuario.procedimento.tabelacbo.autocomplete.AutoCompleteConsultaTabelaCbo;
import br.com.celk.view.unidadesaude.exames.autocomplete.AutoCompleteConsultaExameProcedimento;
import br.com.celk.view.vacina.vacinacalendario.autocomplete.AutoCompleteConsultaTipoVacina;
import br.com.celk.vigilancia.dto.LocalizacaoRegistroAgravoDTOParam;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.enderecocoordenadas.LatitudeLongitudeEndereco;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus;
import br.com.ksisolucoes.vo.cadsus.Raca;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import br.com.ksisolucoes.vo.vacina.Calendario;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssReferenceHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;
import org.jetbrains.annotations.NotNull;
import org.wicketstuff.gmap.GMap;
import org.wicketstuff.gmap.api.GIcon;
import org.wicketstuff.gmap.api.GLatLng;
import org.wicketstuff.gmap.api.GMarker;
import org.wicketstuff.gmap.api.GMarkerOptions;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
@Private
public class ConsultaGeorreferenciamentoUsuarioCadusPage extends BasePage {

    private GeorrefereniamentoUsuarioCadsusDTOParam param;
    private final GMap map;
    private Form<LocalizacaoRegistroAgravoDTOParam> form;
    private WebMarkupContainer containerRegistroAgravo;
    private WebMarkupContainer containerAtendimento;
    private WebMarkupContainer containerUsuarioCadsus;
    private WebMarkupContainer containerDomicilio;
    private WebMarkupContainer containerVacina;
    private WebMarkupContainer containerDispensacoes;

    private DropDown<GeorrefereniamentoUsuarioCadsusDTOParam.EscolaridadeGeo> dropDownEscolaridade;
    private DropDown<String> tipoIdade;
    private LongField idadeInicial;
    private LongField idadeFinal;
    private AutoCompleteConsultaProduto autoCompleteConsultaProduto;
    private AutoCompleteConsultaProduto autoCompleteConsultaProdutoPrescrito;
    private DropDown dropDownTipoReceita;


    public ConsultaGeorreferenciamentoUsuarioCadusPage() {
        this.map = new GMap("map", GoogleMapsConf.MAP_API_KEY, "https");
        init();
    }

    private void init() {
        GeorrefereniamentoUsuarioCadsusDTOParam proxy = on(GeorrefereniamentoUsuarioCadsusDTOParam.class);

        containerRegistroAgravo = new WebMarkupContainer("containerRegistroAgravo");
        containerRegistroAgravo.add(new AutoCompleteConsultaCid(path(proxy.getCid())));
        containerRegistroAgravo.add(new PnlDatePeriod(path(proxy.getPeriodo())));
        containerRegistroAgravo.add(getDropDownStatus(path(proxy.getStatus())));
        getForm().add(containerRegistroAgravo);

        containerAtendimento = new WebMarkupContainer("containerAtendimento");
        containerAtendimento.add(new AutoCompleteConsultaEmpresa(path(proxy.getEmpresa())));
        containerAtendimento.add(new AutoCompleteConsultaProfissional(path(proxy.getProfissional())));
        containerAtendimento.add(new AutoCompleteConsultaTabelaCbo(path(proxy.getTabelaCbo())));
        containerAtendimento.add(new AutoCompleteConsultaProcedimento(path(proxy.getProcedimento())));
        containerAtendimento.add(new AutoCompleteConsultaExameProcedimento(path(proxy.getExameProcedimento())));
        containerAtendimento.add(autoCompleteConsultaProdutoPrescrito = new AutoCompleteConsultaProduto(path(proxy.getMedicamentoPrescrito())));
        autoCompleteConsultaProdutoPrescrito.setMedicamento(SubGrupo.MEDICAMENTO_SIM);
        containerAtendimento.add(new PnlDatePeriod(path(proxy.getPeriodoAtendimento())));
        getForm().add(containerAtendimento);

        containerUsuarioCadsus = new WebMarkupContainer("containerUsuarioCadsus");
        containerUsuarioCadsus.add(getSexoDropDown(path(proxy.getSexo())));
        containerUsuarioCadsus.add(getDropDownIdade());
        containerUsuarioCadsus.add(idadeInicial = new LongField(path(proxy.getIdadeInicial())).setVMax(1800l));
        containerUsuarioCadsus.add(idadeFinal = new LongField(path(proxy.getIdadeFinal())).setVMax(1800l));
        containerUsuarioCadsus.add(dropDownEscolaridade = (DropDown) DropDownUtil.getIEnumDropDown(path(proxy.getEscolaridade()), GeorrefereniamentoUsuarioCadsusDTOParam.EscolaridadeGeo.values(), true, false));
        dropDownEscolaridade.setLabel(new Model(bundle("escolaridade")));
        containerUsuarioCadsus.add((getDropDownRaca(path(proxy.getRaca()))).setLabel(new Model<String>(bundle("racaCor"))));
        getForm().add(containerUsuarioCadsus);

        containerDomicilio = new WebMarkupContainer("containerDomicilio");
        containerDomicilio.add(DropDownUtil.getIEnumDropDown(path(proxy.getTipoDomicilio()), EnderecoDomicilioEsus.TipoDomicilio.values(), true));
        containerDomicilio.add(getDropDownEquipeArea(path(proxy.getEquipeArea())));
        containerDomicilio.add(DropDownUtil.getIEnumDropDown(path(proxy.getSituacaoMoradia()), EnderecoDomicilioEsus.SituacaoMoradia.values(), true));
        containerDomicilio.add((DropDown) DropDownUtil.getIEnumDropDown(path(proxy.getLocalizacao()), EnderecoDomicilioEsus.Localizacao.values(), true, false)
                .setLabel(new Model<>(bundle("localizacao"))));
        getForm().add(containerDomicilio);

        containerVacina = new WebMarkupContainer("containerVacina");
        containerVacina.add(new AutoCompleteConsultaTipoVacina(path(proxy.getTipoVacina())));
        containerVacina.add(createDropDownEstrategia(path(proxy.getCalendario())));
        containerVacina.add(new PnlDatePeriod(path(proxy.getPeriodoVacina())));
        containerVacina.add(DropDownUtil.getIEnumDropDown(path(proxy.getAprazamento()), GeorrefereniamentoUsuarioCadsusDTOParam.Aprazamento.values(), true));
        containerVacina.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getComunicanteHanseniase()), true));
        containerVacina.add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getFlagGestante()), true));
        containerVacina.add(getSexoDropDown(path(proxy.getSexoVacina())));
        getForm().add(containerVacina);

        containerDispensacoes = new WebMarkupContainer("containerDispensacoes");
        containerDispensacoes.add(autoCompleteConsultaProduto = new AutoCompleteConsultaProduto(path(proxy.getProdutoMedicamento())));
        containerDispensacoes.add(new AutoCompleteConsultaEmpresa(path(proxy.getEmpresaDispensacao())));
        autoCompleteConsultaProduto.setMedicamento(SubGrupo.MEDICAMENTO_SIM);
        containerDispensacoes.add(new PnlDatePeriod(path(proxy.getPeriodoDispensacao())));
        containerDispensacoes.add(dropDownTipoReceita = getDropDownTipoReceita(path(proxy.getTipoReceita())));
        getForm().add(containerDispensacoes);

        getForm().add(new AbstractAjaxButton("btnFiltrar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                filtrar(target);
            }
        });

        getForm().add(map);
        map.setOutputMarkupId(true);
        map.setStreetViewControlEnabled(true);
        map.setScaleControlEnabled(true);
        map.setScrollWheelZoomEnabled(true);
        map.setDraggingEnabled(true);
        map.setPanControlEnabled(true);
        map.setDoubleClickZoomEnabled(false);
        map.setMapTypeControlEnabled(true);

        GLatLng center;
        LatitudeLongitudeEndereco latitudeLongitudeEndereco = new LatitudeLongitudeEndereco(SessaoAplicacaoImp.getInstance().getEmpresa().getCidade());
        if (latitudeLongitudeEndereco.getLatitude() != 0.0 && latitudeLongitudeEndereco.getLongitude() != 0.0) {
            center = new GLatLng(latitudeLongitudeEndereco.getLatitude(), latitudeLongitudeEndereco.getLongitude());
        } else {
            center = new GLatLng(-28.7282759, -49.3674942); // loc. de Criciúma
        }
        map.setCenter(center);
        map.setZoom(10);

        initPermissoes();

        add(getForm());
    }

    public DropDown<String> getSexoDropDown(String id) {
        DropDown<String> dropDown = new DropDown<>(id);
        dropDown.addChoice(null, BundleManager.getString("ambos"));
        dropDown.addChoice(UsuarioCadsus.SEXO_MASCULINO, BundleManager.getString("masculino"));
        dropDown.addChoice(UsuarioCadsus.SEXO_FEMININO, BundleManager.getString("feminino"));

        return dropDown;
    }

    private void initPermissoes() {
        Usuario usuarioLogado = ApplicationSession.get().getSessaoAplicacao().getUsuario();
        containerRegistroAgravo.setVisible(isActionPermitted(usuarioLogado, Permissions.GEO_FILTRO_AGRAVO));
        containerAtendimento.setVisible(isActionPermitted(usuarioLogado, Permissions.GEO_FILTRO_ATENDIMENTO));
        containerDomicilio.setVisible(isActionPermitted(usuarioLogado, Permissions.GEO_FILTRO_DOMICILIO));
        containerUsuarioCadsus.setVisible(isActionPermitted(usuarioLogado, Permissions.GEO_FILTRO_PACIENTE));
        containerVacina.setVisible(isActionPermitted(usuarioLogado, Permissions.GEO_FILTRO_VACINA));
        containerDispensacoes.setVisible(isActionPermitted(usuarioLogado, Permissions.GEO_FILTRO_DISPENSACOES));
    }

    private Form getForm() {
        if (this.form == null) {
            this.form = new Form("form", new CompoundPropertyModel(param = new GeorrefereniamentoUsuarioCadsusDTOParam()));
        }
        return form;
    }

    public DropDown getDropDownStatus(String id) {
        DropDown dropDownStatus = new DropDown(id);

        dropDownStatus.addChoice(null, bundle("todos"));
        dropDownStatus.addChoice(RegistroAgravo.Status.PENDENTE.value(), RegistroAgravo.Status.PENDENTE.descricao());
        dropDownStatus.addChoice(RegistroAgravo.Status.MONITORAMENTO.value(), RegistroAgravo.Status.MONITORAMENTO.descricao());
        dropDownStatus.addChoice(RegistroAgravo.Status.CONCLUIDO.value(), RegistroAgravo.Status.CONCLUIDO.descricao());

        dropDownStatus.setOutputMarkupId(true);
        dropDownStatus.addAjaxUpdateValue();

        return dropDownStatus;
    }

    public void filtrar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        map.removeAllOverlays();

        ArrayList<String> msgErroList = new ArrayList<>();

        try {
            param.setMaxLimit((Long) BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("maxResultGeorreferenciamento"));
        } catch (DAOException e) {
            Loggable.log.error(e.getMessage(), e);
        }

        param.setTipoEndereco(LocalizacaoRegistroAgravoDTOParam.TipoEndereco.ENDERECO_AGRAVO.value());
        GeorreferenciamentoUsuarioCadsusDTO georreferenciamentoUsuarioCadsusDTO = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).consultarGeorreferenciamentoUsuarioCadsus(param);

        if (CollectionUtils.isNotNullEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoRegistroAgravoDTOList())) {
            if (georreferenciamentoUsuarioCadsusDTO.getGeoRegistroAgravoDTOList().size() >= param.getMaxLimit()) {
                msgErroList.add("Sua pesquisa para Registro de Agravo retornou mais de " + param.getMaxLimit() + " registros, redefina os critérios de pesquisa.");
            } else {
                //Registro Agravo
                List<GeorrefereniamentoUsuarioCadsusRegistroAgravoDTO> geoRegistroAgravoDTOList = georreferenciamentoUsuarioCadsusDTO.getGeoRegistroAgravoDTOList();
                if (CollectionUtils.isNotNullEmpty(geoRegistroAgravoDTOList)) {
                    buildMapRegistroAgavo(geoRegistroAgravoDTOList);
                }
            }
        }
        if (CollectionUtils.isNotNullEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoAtendimentosDTOList())) {
            if (georreferenciamentoUsuarioCadsusDTO.getGeoAtendimentosDTOList().size() >= param.getMaxLimit()) {
                msgErroList.add("Sua pesquisa para Atendimentos retornou mais de " + param.getMaxLimit() + " registros, redefina os critérios de pesquisa.");
            } else {
                //Atendimentos
                List<GeorreferenciamentoUsuarioCadsusAtendimentosDTO> geoAtendimentosDTOList = georreferenciamentoUsuarioCadsusDTO.getGeoAtendimentosDTOList();
                if (CollectionUtils.isNotNullEmpty(geoAtendimentosDTOList)) {
                    buildMapAtendimento(geoAtendimentosDTOList);
                }
            }
        }
        if (CollectionUtils.isNotNullEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoUsuarioCadsusDTOList())) {
            if (georreferenciamentoUsuarioCadsusDTO.getGeoUsuarioCadsusDTOList().size() >= param.getMaxLimit()) {
                msgErroList.add("Sua pesquisa para Pacientes retornou mais de " + param.getMaxLimit() + " registros, redefina os critérios de pesquisa.");
            } else {
                //UsuarioCadsus
                List<GeorreferenciamentoUsuarioCadsusUsuarioCadsusDTO> geoUsuarioCadsusDTOList = georreferenciamentoUsuarioCadsusDTO.getGeoUsuarioCadsusDTOList();
                if (CollectionUtils.isNotNullEmpty(geoUsuarioCadsusDTOList)) {
                    buildMapUsuarioCadsus(geoUsuarioCadsusDTOList);
                }
            }
        }
        if (CollectionUtils.isNotNullEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoDomicilioDTOList())) {
            if (georreferenciamentoUsuarioCadsusDTO.getGeoDomicilioDTOList().size() >= param.getMaxLimit()) {
                msgErroList.add("Sua pesquisa para Domicílio retornou mais de " + param.getMaxLimit() + " registros, redefina os critérios de pesquisa.");
            } else {
                //UsuarioCadsus
                List<GeorreferenciamentoUsuarioCadsusDomicilioDTO> geoUsuarioCadsusDTOList = georreferenciamentoUsuarioCadsusDTO.getGeoDomicilioDTOList();
                if (CollectionUtils.isNotNullEmpty(geoUsuarioCadsusDTOList)) {
                    buildMapDomicilio(geoUsuarioCadsusDTOList);
                }
            }
        }
        if (CollectionUtils.isNotNullEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoVacinasDTOList())) {
            if (georreferenciamentoUsuarioCadsusDTO.getGeoVacinasDTOList().size() >= param.getMaxLimit()) {
                msgErroList.add("Sua pesquisa para Vacinas retornou mais de " + param.getMaxLimit() + " registros, redefina os critérios de pesquisa.");
            } else {
                //UsuarioCadsus
                List<GeorreferenciamentoUsuarioCadsusVacinasDTO> geoUsuarioCadsusDTOList = georreferenciamentoUsuarioCadsusDTO.getGeoVacinasDTOList();
                if (CollectionUtils.isNotNullEmpty(geoUsuarioCadsusDTOList)) {
                    buildMapVacinas(geoUsuarioCadsusDTOList);
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoDispensacoesDTOList())) {
            if (georreferenciamentoUsuarioCadsusDTO.getGeoDispensacoesDTOList().size() >= param.getMaxLimit()) {
                msgErroList.add("Sua pesquisa para Dispensações retornou mais de " + param.getMaxLimit() + " registros, redefina os critérios de pesquisa.");
            } else {
                //UsuarioCadsus
                List<GeorreferenciamentoUsuarioCadsusDispensacoesDTO> geoDispensacoesDTOList = georreferenciamentoUsuarioCadsusDTO.getGeoDispensacoesDTOList();
                if (CollectionUtils.isNotNullEmpty(geoDispensacoesDTOList)) {
                    buildMapDispensacoes(geoDispensacoesDTOList);
                }
            }
        }

        if (CollectionUtils.isNotNullEmpty(msgErroList)) {
            StringBuilder sbErro = new StringBuilder();
            for (String s : msgErroList) {
                sbErro.append(s);
                sbErro.append("\n");
            }
            info(target, sbErro.toString());
        }
        if (CollectionUtils.isAllEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoUsuarioCadsusDTOList())
                && CollectionUtils.isAllEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoRegistroAgravoDTOList())
                && CollectionUtils.isAllEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoAtendimentosDTOList())
                && CollectionUtils.isAllEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoDomicilioDTOList())
                && CollectionUtils.isAllEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoVacinasDTOList())
                && CollectionUtils.isAllEmpty(georreferenciamentoUsuarioCadsusDTO.getGeoDispensacoesDTOList())
        ) {
            info(target, "Sua pesquisa não retornou nenhum registro, redefina os critérios de pesquisa ou execute o processo de atualização dos dados - Agendador de Processos (419)");
        }
        target.add(map);
    }

    private void buildMapRegistroAgavo(List<GeorrefereniamentoUsuarioCadsusRegistroAgravoDTO> list) {
        for (GeorrefereniamentoUsuarioCadsusRegistroAgravoDTO ra : list) {
            GLatLng latlng = new GLatLng(ra.getLatitude(), ra.getLongitude());
            StringBuilder builderPonto = new StringBuilder();
            builderPonto.append("Paciente: ");
            builderPonto.append(ra.getRegistroAgravo().getUsuarioCadsus().getNomeSocial());
            builderPonto.append("\n");
            builderPonto.append("CID: ");
            builderPonto.append(ra.getRegistroAgravo().getCid().getDescricaoFormatado());
            builderPonto.append("\n");
            builderPonto.append("Data Registro: ");
            builderPonto.append(Data.formatar(ra.getRegistroAgravo().getDataRegistro()));
            builderPonto.append("\n");
            builderPonto.append("Situação: ");
            builderPonto.append(ra.getRegistroAgravo().getDescricaoStatus());

            //http://tancro.e-central.tv/grandmaster/markers/google-icons/mapfiles-ms-micons.html
            GIcon gIcon = new GIcon("http://maps.google.com/mapfiles/ms/micons/blue-dot.png");
            GMarkerOptions options = getgMarkerOptions(latlng, builderPonto, gIcon);
            map.addOverlay(new GMarker(options));
        }
    }

    @NotNull
    private GMarkerOptions getgMarkerOptions(GLatLng latlng, StringBuilder builderPonto, GIcon gIcon) {
        GMarkerOptions options = new GMarkerOptions(map, latlng, builderPonto.toString(), gIcon);
//        options.bouncy(true);
        options.draggable(true);
//        options.autoPan(true);
        options.clickable(true);
        return options;
    }

    private void buildMapAtendimento(List<GeorreferenciamentoUsuarioCadsusAtendimentosDTO> list) {
        for (GeorreferenciamentoUsuarioCadsusAtendimentosDTO ate : list) {
            if (ate.getLatitude() != null && ate.getLongitude() != null) {
                GLatLng latlng = new GLatLng(ate.getLatitude(), ate.getLongitude());
                StringBuilder builderPonto = new StringBuilder();
                builderPonto.append("Paciente: ");
                builderPonto.append(ate.getAtendimento().getUsuarioCadsus().getNomeSocial());
                builderPonto.append("\n");
                builderPonto.append("Unidade: ");
                builderPonto.append(ate.getAtendimento().getEmpresa().getDescricao());
                builderPonto.append("\n");
                builderPonto.append("Data Atendimento: ");
                builderPonto.append(Data.formatar(ate.getAtendimento().getDataAtendimento()));
                builderPonto.append("\n");
                builderPonto.append("Tipo Atendimento: ");
                builderPonto.append(ate.getDescricaoTipoAtendimento());
                GIcon gIcon = new GIcon("http://maps.google.com/mapfiles/ms/micons/red-dot.png");
                GMarkerOptions options = getgMarkerOptions(latlng, builderPonto, gIcon);
                map.addOverlay(new GMarker(options));
            }
        }
    }

    private void buildMapUsuarioCadsus(List<GeorreferenciamentoUsuarioCadsusUsuarioCadsusDTO> list) {
        for (GeorreferenciamentoUsuarioCadsusUsuarioCadsusDTO usu : list) {
            if (usu.getLatitude() != null && usu.getLongitude() != null) {
                GLatLng latlng = new GLatLng(usu.getLatitude(), usu.getLongitude());
                StringBuilder builderPonto = new StringBuilder();
                builderPonto.append("Paciente: ");
                builderPonto.append(usu.getUsuarioCadsus().getNomeSocial());
                builderPonto.append("\n");
                builderPonto.append("Data Nascimento: ");
                builderPonto.append(usu.getUsuarioCadsus().getDataNascimentoFormatado());
                builderPonto.append("\n");
                builderPonto.append("Nome da Mãe: ");
                builderPonto.append(usu.getUsuarioCadsus().getNomeMae());
                builderPonto.append("\n");
                builderPonto.append("CNS: ");
                builderPonto.append(usu.getUsuarioCadsus().getCns());
                GIcon gIcon = new GIcon("http://maps.google.com/mapfiles/ms/micons/green-dot.png");
                GMarkerOptions options = getgMarkerOptions(latlng, builderPonto, gIcon);
                map.addOverlay(new GMarker(options));
            }
        }
    }

    private void buildMapDomicilio(List<GeorreferenciamentoUsuarioCadsusDomicilioDTO> list) {
        for (GeorreferenciamentoUsuarioCadsusDomicilioDTO domicilio : list) {
            if (domicilio.getLatitude() != null && domicilio.getLongitude() != null) {
                GLatLng latlng = new GLatLng(domicilio.getLatitude(), domicilio.getLongitude());
                StringBuilder builderPonto = new StringBuilder();
                builderPonto.append("Responsável: ");
                builderPonto.append(domicilio.getResponsavel());
                builderPonto.append("\n");
                builderPonto.append("Nº da Família: ");
                builderPonto.append(domicilio.getEnderecoDomicilio().getNumeroFamilia());
                builderPonto.append("\n");
                builderPonto.append("Unidade: ");
                if (domicilio.getEnderecoDomicilio().getEquipeMicroArea() != null && domicilio.getEnderecoDomicilio().getEquipeMicroArea().getEmpresa() != null) {
                    builderPonto.append(domicilio.getEnderecoDomicilio().getEquipeMicroArea().getEmpresa().getDescricao());
                } else {
                    builderPonto.append("Não informado");
                }

                GIcon gIcon = new GIcon("http://maps.google.com/mapfiles/ms/micons/purple-dot.png");
                GMarkerOptions options = getgMarkerOptions(latlng, builderPonto, gIcon);
                map.addOverlay(new GMarker(options));
            }
        }
    }

    private void buildMapVacinas(List<GeorreferenciamentoUsuarioCadsusVacinasDTO> list) {
        for (GeorreferenciamentoUsuarioCadsusVacinasDTO vacina : list) {
            if (vacina.getLatitude() != null && vacina.getLongitude() != null) {
                GLatLng latlng = new GLatLng(vacina.getLatitude(), vacina.getLongitude());
                StringBuilder builderPonto = new StringBuilder();
                builderPonto.append("Paciente: ");
                builderPonto.append(vacina.getUsuarioCadsus().getNomeSocial());
                builderPonto.append("\n");
                builderPonto.append("Data de Nascimento: ");
                builderPonto.append(Data.formatar(vacina.getUsuarioCadsus().getDataNascimento()));
                builderPonto.append("\n");
                builderPonto.append("Vacina: ");
                builderPonto.append(vacina.getVacinaAplicacao().getDescricaoVacina());
                builderPonto.append("\n");
                builderPonto.append("Dose: ");
                builderPonto.append(vacina.getVacinaAplicacao().getDescricaoDoses());

                GIcon gIcon = new GIcon("http://maps.google.com/mapfiles/ms/micons/yellow-dot.png");
                GMarkerOptions options = getgMarkerOptions(latlng, builderPonto, gIcon);
                map.addOverlay(new GMarker(options));
            }
        }
    }

    private void buildMapDispensacoes(List<GeorreferenciamentoUsuarioCadsusDispensacoesDTO> list) {
        for (GeorreferenciamentoUsuarioCadsusDispensacoesDTO dispensacao : list) {
            if (dispensacao.getLatitude() != null && dispensacao.getLongitude() != null) {
                GLatLng latlng = new GLatLng(dispensacao.getLatitude(), dispensacao.getLongitude());
                StringBuilder builderPonto = new StringBuilder();
                builderPonto.append("Paciente: ");
                builderPonto.append(dispensacao.getUsuarioCadsus().getNomeSocial());
                builderPonto.append("\n");
                builderPonto.append("Data da Dispensação: ");
                builderPonto.append(Data.formatar(dispensacao.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getDataDispensacao()));
                builderPonto.append("\n");
                builderPonto.append("Medicamento: ");
                builderPonto.append(dispensacao.getDispensacaoMedicamentoItem().getProduto().getDescricaoFormatado());
                builderPonto.append("\n");
                builderPonto.append("Tipo da Receita: ");
                builderPonto.append(dispensacao.getDispensacaoMedicamentoItem().getDispensacaoMedicamento().getTipoReceita().getDescricao());

                GIcon gIcon = new GIcon("http://maps.google.com/mapfiles/ms/micons/pink-dot.png");
                GMarkerOptions options = getgMarkerOptions(latlng, builderPonto, gIcon);
                map.addOverlay(new GMarker(options));
            }
        }
    }

    private DropDown<String> getDropDownIdade() {
        tipoIdade = new DropDown<String>("idadeEm");
        tipoIdade.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            public void onConfigure(Component component) {
                idadeInicial.setEnabled(false);
                idadeFinal.setEnabled(false);
            }

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (tipoIdade.getModel() != null && tipoIdade.getModel().getObject() != null) {
                    idadeInicial.setEnabled(true);
                    idadeFinal.setEnabled(true);
                    idadeFinal.setConvertZeroToNull(false);
                } else {
                    idadeInicial.setEnabled(false);
                    idadeFinal.setEnabled(false);
                    idadeInicial.limpar(target);
                    idadeFinal.limpar(target);
                    idadeFinal.setConvertZeroToNull(true);
                }

                target.add(idadeFinal, idadeInicial);
            }
        });
        tipoIdade.addChoice(null, "");
        tipoIdade.addChoice(GeorrefereniamentoUsuarioCadsusDTOParam.TipoIdade.DIAS.getValue(), GeorrefereniamentoUsuarioCadsusDTOParam.TipoIdade.DIAS.getValue());
        tipoIdade.addChoice(GeorrefereniamentoUsuarioCadsusDTOParam.TipoIdade.MESES.getValue(), GeorrefereniamentoUsuarioCadsusDTOParam.TipoIdade.MESES.getValue());
        tipoIdade.addChoice(GeorrefereniamentoUsuarioCadsusDTOParam.TipoIdade.ANOS.getValue(), GeorrefereniamentoUsuarioCadsusDTOParam.TipoIdade.ANOS.getValue());
        return tipoIdade;
    }

    private DropDown<Raca> getDropDownRaca(String id) {
        DropDown<Raca> dropDown = new DropDown<Raca>(id);
        List<Raca> raca = LoadManager.getInstance(Raca.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Raca.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        dropDown.addChoice(null, "");
        for (Raca raca_ : raca) {
            dropDown.addChoice(raca_, raca_.getDescricao());
        }

        return dropDown;
    }

    private DropDown<EquipeArea> getDropDownEquipeArea(String id) {
        DropDown<EquipeArea> dropDown = new DropDown<>(id);
        Cidade cidade = ApplicationSession.get().getSessaoAplicacao().getEmpresa().getCidade();
        dropDown.addChoice(null, "");
        if (cidade != null) {
            List<EquipeArea> equipeAreas = LoadManager.getInstance(EquipeArea.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(EquipeArea.PROP_CIDADE), cidade))
                    .addSorter(new QueryCustom.QueryCustomSorter(EquipeArea.PROP_DESCRICAO))
                    .start().getList();

            if (CollectionUtils.isNotNullEmpty(equipeAreas)) {
                for (EquipeArea equipeArea : equipeAreas) {
                    dropDown.addChoice(equipeArea, equipeArea.getDescricao());
                }
            }
        }
        return dropDown;
    }

    private DropDown createDropDownEstrategia(String id) {
        DropDown dropDown = new DropDown(id);
        List<Calendario> calendariosList = LoadManager.getInstance(Calendario.class)
                .addSorter(new QueryCustom.QueryCustomSorter(Calendario.PROP_DESCRICAO, BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(calendariosList)) {
            for (Calendario calendario : calendariosList) {
                dropDown.addChoice(null, Bundle.getStringApplication("rotulo_todos_maiusculo"));
                dropDown.addChoice(calendario, calendario.getDescricao());
            }
        }
        return dropDown;
    }

    public DropDown getDropDownTipoReceita(String id) {
        if (dropDownTipoReceita == null) {
            dropDownTipoReceita = new DropDown<TipoReceita>(id);
            List<String> receitas = Arrays.asList(TipoReceita.RECEITA_BRANCA, TipoReceita.RECEITA_BASICA, TipoReceita.RECEITA_AZUL, TipoReceita.RECEITA_AMARELA, TipoReceita.RECEITA_CONTROLADAS, TipoReceita.RECEITA_PRESCRICAO_ATENDIMENTO);
            List<TipoReceita> tipoReceitaList = LoadManager.getInstance(TipoReceita.class)
                    .addProperties(new HQLProperties(TipoReceita.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(TipoReceita.PROP_TIPO_RECEITA, QueryCustom.QueryCustomParameter.IN, receitas))
                    .addSorter(new QueryCustom.QueryCustomSorter(TipoReceita.PROP_DESCRICAO))
                    .start().getList();

            dropDownTipoReceita.addChoice(null, "");
            for (TipoReceita tipoReceita : tipoReceitaList) {
                dropDownTipoReceita.addChoice(tipoReceita, tipoReceita.getDescricao());
            }
        }
        return dropDownTipoReceita;
    }

    @Override
    public String getTituloPrograma() {
        return bundle("georreferenciamentoRegistroCidadao");
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssReferenceHeaderItem.forReference(new CssResourceReference(ConsultaGeorreferenciamentoUsuarioCadusPage.class, "ConsultaGeorreferenciamentoUsuarioCadus.css")));
    }
}
