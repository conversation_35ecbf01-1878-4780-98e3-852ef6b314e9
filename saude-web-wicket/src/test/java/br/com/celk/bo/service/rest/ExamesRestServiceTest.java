package br.com.celk.bo.service.rest;

import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.log.Loggable;
import org.apache.commons.io.FileUtils;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyClientBuilder;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;
import org.jboss.resteasy.plugins.providers.multipart.MultipartFormDataOutput;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class ExamesRestServiceTest {

//    @Test
    public void registrarResultadoExame() throws IOException {
        ResteasyClient client = (ResteasyClient) new ResteasyClientBuilder().build();
        ResteasyWebTarget target = client.target("http://localhost:8080/rest/ext/1.1/exames/prescricao/144156/resultadoItem/144157");
//        ResteasyWebTarget target = client.target("https://desenvolvimento.celk.com.br/rest/ext/1.0/exames/prescricao/3265011/resultadoItem/3265012");
        
        String ID = "mf5u38fi5kjcwy3869gsdjcjhrnh3ydf7306";
        String CHAVE = "alcjkh28c2058v72n67g5f6569g3s1g87630";
        Date d = new Date();
        
        MultipartFormDataOutput mdo = new MultipartFormDataOutput();
        
        byte[] bFile = FileUtils.readFileToByteArray(new File("/home/<USER>/CELK.pdf"));
        mdo.addFormData("firstPart", new ByteArrayInputStream(bFile), new MediaType("application", "pdf"));
        
//        byte[] bFile2 = FileUtils.readFileToByteArray(new File("/home/<USER>/Desktop/resultados/7647-01-COLESTEROL.JSON"));
//        mdo.addFormData("secondPart", new ByteArrayInputStream(bFile2), new MediaType("application", "json"));
        
        mdo.addFormData("secondPart", "{\n" +
"	\"dataResultado\": 1434994488117,\n" +
"	\"tags\": [{\n" +
"		\"idTag\": \"432\",\n" +
"		\"descricaoTag\": \"descricão1\",\n" +
"		\"resultadoIndividual\": \"resultãdo1\",\n" +
"		\"unidade\": \"unidae\",\n" +
"		\"valorReferenciaMaximo\": \"33\",\n" +
"		\"valorReferenciaMinimo\": \"10\"\n" +
"		},	\n" +
"		{\n" +
"		\"idTag\": \"43222\",\n" +
"		\"descricaoTag\": \"descricao2\",\n" +
"		\"resultadoIndividual\": \"resultado2\",\n" +
"		\"unidade\": \"unidae2\",\n" +
"		\"valorReferenciaMaximo\": \"332\",\n" +
"		\"valorReferenciaMinimo\": \"102\"\n" +
"		}]\n" +
"}", MediaType.APPLICATION_JSON_TYPE);
        String log = "";
        Response response = (Response) target.request()
                .header("id", ID)
                .header("data", String.valueOf(d.getTime()))
                .header("hash", Util.criptografarSenha(ID + d.getTime() + CHAVE))
                .post(Entity.entity(mdo, MediaType.MULTIPART_FORM_DATA_TYPE));
        log += ("TEMPO: "+ (new Date().getTime()-d.getTime())) + "\n";
        log += ("@@@@@@@@ RESULTADO @@@@@@@@") + "\n";
        log += (response.getEntity()) + "\n";
        log += (response.getStatus()) + "\n";
        
        for (Map.Entry<String, List<Object>> entrySet : response.getMetadata().entrySet()) {
            log += (entrySet.getKey() + ": "+entrySet.getValue()) + "\n";
        }
        Loggable.log.info(log);
    }
}
