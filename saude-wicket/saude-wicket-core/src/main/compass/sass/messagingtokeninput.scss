/* Example tokeninput style #2: Facebook style */
ul.token-input-list-messaging {
    overflow: hidden; 
    height: auto !important; 
    height: 1%;
    width: 400px;
    border: 1px solid #8496ba;
    cursor: text;
    font-size: 12px;
    font-family: Verdana;
    min-height: 1px;
    z-index: 999;
    margin: 0;
    padding: 2px;
    background-color: #fff;
    list-style-type: none;
    clear: left;
    -webkit-border-radius: 2px;
    -moz-border-radius: 2px;
    -ms-border-radius: 2px;
    -o-border-radius: 2px;
    border-radius: 2px;
}

ul.token-input-list-messaging li input {
    border: 0;
    /*width: 100%;*/
    background-color: white;
    margin: 0;
    -webkit-appearance: caret;
}

li.token-input-token-messaging {
    overflow: hidden; 
    height: auto !important; 
    height: 15px;
    margin: 1px;
    padding: 0px 3px;
    background-color: #eff2f7;
    color: #000;
    float:left;
    cursor: default;
    border: 1px solid #ccd5e4;
    font-size: 11px;
    border-radius: 5px;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
}

li.token-input-token-messaging p {
    display: inline;
    padding: 0;
    margin: 0;
}

li.token-input-token-messaging span {
    color: #a6b3cf;
    margin-left: 5px;
    font-weight: bold;
    cursor: pointer;
    float: right;
    margin-right: 5px;
}

li.token-input-selected-token-messaging {
    background-color: #536A77;
    border-color: #2D363F;
    color: #fff;
}

li.token-input-input-token-messaging {
    float: left;
    margin: 0;
    padding: 0;
    list-style-type: none;
}

div.token-input-dropdown-messaging {
    position: absolute;
    width: 400px;
    background-color: #fff;
    overflow: hidden;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    cursor: default;
    font-size: 11px;
    font-family: Verdana;
    /*z-index: 1;*/
}

div.token-input-dropdown-messaging p {
    margin: 0;
    padding: 5px;
    font-weight: bold;
    color: #777;
}

div.token-input-dropdown-messaging ul {
    margin: 0;
    padding: 0;
}

div.token-input-dropdown-messaging ul li {
    background-color: #fff;
    padding: 3px;
    margin: 0;
    list-style-type: none;
}

div.token-input-dropdown-messaging ul li.token-input-dropdown-item-messaging {
    background-color: #fff;
}

div.token-input-dropdown-messaging ul li.token-input-dropdown-item2-messaging {
    background-color: #F3F5F9;
}

div.token-input-dropdown-messaging ul li em {
    font-weight: bold;
    font-style: normal;
}

div.token-input-dropdown-messaging ul li.token-input-selected-dropdown-item-messaging {
    background-color: #CCD5E4;
    color: #fff;
}

div.token-input-dropdown-messaging ul li div.nivel-1 {
    font-weight: bold;
    font-style: normal;
}

div.token-input-dropdown-messaging ul li div.nivel-2 {
    font-size: 11px;
}

div.token-input-dropdown-celk ul li div.nivel-3 {
    font-size: 9px;
}

ul.token-input-disabled-messaging,
ul.token-input-disabled-messaging li input {
  background-color: #E8E8E8;
}

ul.token-input-disabled-messaging li.token-input-token-messaging {
  background-color: #CCD5E4;
  color: #7D7D7D
}

ul.token-input-disabled-messaging li.token-input-token-messaging span {
  cursor: default;
  display: none;
}