package br.com.celk.view.atendimento.prontuario.panel.odonto.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.model.LoadableObjectModel;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoFicha;
import static ch.lambdaj.Lambda.on;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConcluirTratamentoOdonto extends Panel{
    
    private LoadableObjectModel<AtendimentoOdontoFicha> model;
    private InputArea motivo;
    
    public PnlConcluirTratamentoOdonto(String id){
        super(id);
        init();
    }

    private void init() {
        Form form = new Form<AtendimentoOdontoFicha>("form", new CompoundPropertyModel(model = new LoadableObjectModel<AtendimentoOdontoFicha>(AtendimentoOdontoFicha.class, false)));
        AtendimentoOdontoFicha proxy = on(AtendimentoOdontoFicha.class);

        form.add(new InputField(path(proxy.getDataConclusao())));
        form.add(motivo = new InputArea(path(proxy.getObservacaoConclusao())));
        
        form.add(new AbstractAjaxButton("btnConcluir") {
            
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(validarMotivoBaixa(target)){
                    onConcluir(target, model.getObject());
                }
            }
        });
        
        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));
        
        add(form);
    }
    
    public boolean validarMotivoBaixa(AjaxRequestTarget target) {
        try {
            if(model.getObject().getDataConclusao() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_data_conclusao"));
            }
            if (model.getObject().getDataConclusao().after(DataUtil.getDataAtual())) {
                throw new ValidacaoException(Bundle.getStringApplication("msg_data_conclusao_nao_pode_ser_maior_data_atual"));
            }
            if(motivo.getComponentValue() == null){
                throw new ValidacaoException(Bundle.getStringApplication("msg_informe_motivo"));
            }
        } catch (ValidacaoException e) {              
            MessageUtil.modalWarn(target, this, e);
            return false;
        }
        
        return true;
    }    
   
    public abstract void onConcluir(AjaxRequestTarget target, AtendimentoOdontoFicha atendimentoOdontoFicha) throws ValidacaoException, DAOException;
    
    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
    
    public void limpar(AjaxRequestTarget target){}
    
    public void setAtendimentoOdontoFicha(AtendimentoOdontoFicha atendimentoOdontoFicha){
        atendimentoOdontoFicha.setDataConclusao(DataUtil.getDataAtual());
        this.model.setObject(atendimentoOdontoFicha);
    }
}