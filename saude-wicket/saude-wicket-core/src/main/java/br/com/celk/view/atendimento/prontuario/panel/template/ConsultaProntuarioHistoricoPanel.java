package br.com.celk.view.atendimento.prontuario.panel.template;

import br.com.celk.component.button.ProcurarButton;
import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.table.pageable.PageableTable;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public abstract class ConsultaProntuarioHistoricoPanel<T extends Serializable, E> extends ProntuarioHistoricoPanel{

    private IPagerProvider pagerProvider;
    private PageableTable<T> pageableTable;
    private List<IColumn> columns;
    private ProcurarButton<E> btnProcurar;
    private boolean procurarAoAbrir = true;
    
    public ConsultaProntuarioHistoricoPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        
        Form form = new Form("form");
        
        form.add(pageableTable = newPageableTable("table", getColumns(), getPagerProvider(), 10));

        form.add(btnProcurar = new ProcurarButton<E>("btnProcurar", pageableTable) {

            @Override
            public E getParam() {
                return getParameters();
            }
        });
        
        initForm(form);
        
        add(form);
        
        if (procurarAoAbrir) {
            btnProcurar.procurar();
        }
    }
    
    public PageableTable<T> newPageableTable(String tableId, List<IColumn> columns, IPagerProvider pagerProvider, int rowsPerPage){
        return new PageableTable(tableId, columns, pagerProvider);
    }
    
    public abstract void initForm(Form form);
    
    public List<IColumn> getColumns(){
        if (this.columns == null) {
            this.columns = new ArrayList<IColumn>();
        }
        
        return getColumns(columns);
    }
    
    public abstract List<IColumn> getColumns(List<IColumn> columns);
    
    public IPagerProvider getPagerProvider(){
        if (this.pagerProvider == null) {
            this.pagerProvider = getPagerProviderInstance();
        }
        
        return this.pagerProvider;
    }
    
    public abstract IPagerProvider getPagerProviderInstance();
    
    public abstract E getParameters();
    
}
