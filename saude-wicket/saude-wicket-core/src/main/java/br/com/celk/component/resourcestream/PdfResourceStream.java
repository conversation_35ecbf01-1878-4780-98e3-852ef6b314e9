package br.com.celk.component.resourcestream;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Locale;
import org.apache.wicket.util.lang.Bytes;
import org.apache.wicket.util.resource.IResourceStream;
import org.apache.wicket.util.resource.ResourceStreamNotFoundException;
import org.apache.wicket.util.time.Time;

/**
 *
 * <AUTHOR>
 */
public class PdfResourceStream implements IResourceStream{

    private Locale locale;
    private String style;
    private String variation;
    private byte[] bytes;

    public PdfResourceStream(byte[] bytes) {
        this.bytes = bytes;
    }
    
    public String getContentType() {
        return "application/pdf";
    }

    public Bytes length() {
        return Bytes.bytes(bytes.length);
    }

    public InputStream getInputStream() throws ResourceStreamNotFoundException {
        return new ByteArrayInputStream(bytes);
    }

    public void close() throws IOException {}

    public Locale getLocale() {
        return locale;
    }

    public void setLocale(Locale locale) {
        this.locale = locale;
    }

    public String getStyle() {
        return style;
    }

    public void setStyle(String style) {
        this.style = style;
    }

    public String getVariation() {
        return variation;
    }

    public void setVariation(String variation) {
        this.variation = variation;
    }

    public Time lastModifiedTime() {
        return Time.now();
    }

}
