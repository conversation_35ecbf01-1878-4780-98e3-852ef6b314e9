package br.com.celk.component.dateperiod;

import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.link.AbstractAjaxLink;
import br.com.celk.component.radio.AjaxRadio;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.celk.system.util.ComponentWicketUtil;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.DatePeriod;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.FormComponentPanel;
import org.apache.wicket.markup.html.form.RadioGroup;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;
import org.odlabs.wiquery.core.javascript.helper.DateHelper;
import org.odlabs.wiquery.ui.datepicker.DateOption;
import org.odlabs.wiquery.ui.datepicker.DatePicker;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

/**
 *
 * <AUTHOR>
 */
public class PnlChoicePeriod extends FormComponentPanel<DatePeriod> {

    private WebMarkupContainer container;
    private WebMarkupContainer containerChoice1;
    private WebMarkupContainer containerChoice2;
    private DatePicker<Date> dataInicial;
    private DatePicker<Date> dataFinal;
    private AbstractAjaxLink btnDataInicial;
    private AbstractAjaxLink btnDataFinal;
    private DropDown<Long> cbxMeses;
    private DropDown<Long> cbxAnos;
    private DropDown<Integer> cbxOpcaoPeriodo;

    private Integer periodo;
    private Long mes;
    private Long ano;
    private Integer containerRadio;
    
    public PnlChoicePeriod(String id) {
        super(id);
        init();
    }

    public PnlChoicePeriod(String id, IModel<DatePeriod> model) {
        super(id, model);
        init();
    }
    
    private void init() {
        setOutputMarkupId(true);
        
        containerRadio = 1;
        
        add(container = new RadioGroup("containerRadio", new PropertyModel(this, "containerRadio")));
        
        container.add(new AjaxRadio("radio1", new Model(1)) {

            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                containerChoice1.setEnabled(true);
                containerChoice2.setEnabled(false);
                target.add(containerChoice1);
                target.add(containerChoice2);
                target.appendJavaScript(JScript.initMasks());
            }
        });
        
        container.add(new AjaxRadio("radio2", new Model(2)) {

            @Override
            public void onAjaxEvent(AjaxRequestTarget target) {
                containerChoice1.setEnabled(false);
                containerChoice2.setEnabled(true);
                target.add(containerChoice1);
                target.add(containerChoice2);
                target.appendJavaScript(JScript.initMasks());
            }
        });
        
        container.add(containerChoice1 = new WebMarkupContainer("containerChoice1"));
        container.add(containerChoice2 = new WebMarkupContainer("containerChoice2"));
        
        containerChoice1.setOutputMarkupId(true);
        containerChoice2.setOutputMarkupId(true);
        
        containerChoice1.add(DropDownUtil.setMesesChoices(cbxMeses = new DropDown("cbxMeses", new PropertyModel(this, "mes")), false));
        containerChoice1.add(DropDownUtil.setAnoChoices(cbxAnos = new DropDown("cbxAnos", new PropertyModel(this, "ano")), false, false));
        
        containerChoice2.add(getDropDownPeriodo());
        
        containerChoice2.add(dataInicial = new DatePicker<Date>("dataInicial", new Model<Date>(), Date.class));
        
        containerChoice2.add(btnDataInicial = new AbstractAjaxLink("btnDataInicial") {

            @Override
            public void onAction(AjaxRequestTarget target) {
                dataInicial.show(target);
            }
        });
        
        containerChoice2.add(dataFinal = new DatePicker<Date>("dataFinal", new Model<Date>(), Date.class));
        containerChoice2.add(btnDataFinal = new AbstractAjaxLink("btnDataFinal") {

            @Override
            public void onAction(AjaxRequestTarget target) {
                dataFinal.show(target);
            }
        });
        
        dataInicial.setOutputMarkupId(true);
        dataFinal.setOutputMarkupId(true);
        
        dataInicial.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dataInicial.getModelObject()!=null) {
                    dataFinal.setMinDate(new DateOption(dataInicial.getModelObject()));
                    target.appendJavaScript("$( '#"+dataFinal.getMarkupId()+"' ).datepicker( 'option', 'minDate', "+DateHelper.getJSDate(dataInicial.getModelObject())+" );");
                } else {
                    dataFinal.setMinDate(null);
                    target.add(dataFinal);
                }
                target.appendJavaScript(JScript.initMasks());
            }
        });
        
        dataFinal.add(new AjaxFormComponentUpdatingBehavior("onchange") {

            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                if (dataFinal.getModelObject()!=null) {
                    dataInicial.setMaxDate(new DateOption(dataFinal.getModelObject()));
                    target.appendJavaScript("$( '#"+dataInicial.getMarkupId()+"' ).datepicker( 'option', 'maxDate', "+DateHelper.getJSDate(dataFinal.getModelObject())+" );");
                } else {
                    dataInicial.setMaxDate(null);
                    target.add(dataInicial);
                }
                target.appendJavaScript(JScript.initMasks());
            }
        });
        
        btnDataInicial.setLoadingAnimation(false);
        btnDataFinal.setLoadingAnimation(false);
        containerChoice2.setEnabled(false);
        
        periodo = 999;
        Date dataAtual = Data.getDataAtual();
        Calendar calendar = GregorianCalendar.getInstance();
        calendar.setTime(dataAtual);
        mes = new Long(calendar.get(Calendar.MONTH)+1);
        ano = new Long(calendar.get(Calendar.YEAR));
    }

    public DropDown getDropDownPeriodo(){
        if(cbxOpcaoPeriodo == null){
            cbxOpcaoPeriodo = new DropDown("cbxPeriodo", new PropertyModel(this, "periodo"));

            cbxOpcaoPeriodo.addChoice(15, BundleManager.getString("ultimos15dias"));
            cbxOpcaoPeriodo.addChoice(30, BundleManager.getString("ultimos30dias"));
            cbxOpcaoPeriodo.addChoice(60, BundleManager.getString("ultimos60dias"));
            cbxOpcaoPeriodo.addChoice(90, BundleManager.getString("ultimos90dias"));
            cbxOpcaoPeriodo.addChoice(365, BundleManager.getString("ultimoAno"));
            cbxOpcaoPeriodo.addChoice(999, BundleManager.getString("outro"));

            cbxOpcaoPeriodo.add(new AjaxFormComponentUpdatingBehavior("onchange") {

                @Override
                protected void onUpdate(AjaxRequestTarget target) {
                    if (periodo!=999) {
                        DatePeriod datePeriod = new DatePeriod(Data.removeDias(Data.getDataAtual(), periodo - 1), Data.getDataAtual());
                        dataInicial.setModelObject(datePeriod.getDataInicial());
                        dataFinal.setModelObject(datePeriod.getDataFinal());
                        dataInicial.setEnabled(false);
                        dataFinal.setEnabled(false);
                    } else {
                        dataInicial.setEnabled(true);
                        dataFinal.setEnabled(true);
                    }
                    target.add(dataInicial);
                    target.add(dataFinal);
                    target.appendJavaScript(JScript.initMasks());
                }
            });
        }
        
        return cbxOpcaoPeriodo;
    }
    
    @Override
    protected void convertInput() {
        if (containerRadio == 1) {
            if (cbxMeses.getConvertedInput()!=null && cbxAnos.getConvertedInput()!=null) {
                Calendar calendar = GregorianCalendar.getInstance();
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getMinimum(Calendar.DAY_OF_MONTH));
                calendar.set(Calendar.MONTH, cbxMeses.getConvertedInput().intValue()-1);
                calendar.set(Calendar.YEAR, cbxAnos.getConvertedInput().intValue());
                Date time = calendar.getTime();
                setConvertedInput(Data.adjustRangeDay(time));
            } else {
                setConvertedInput(null);
            }
        } else if(containerRadio == 2) {
            if (dataInicial.getConvertedInput()!=null || dataFinal.getConvertedInput()!=null) {
                DatePeriod datePeriod = new DatePeriod();
                datePeriod.setDataInicial(dataInicial.getConvertedInput());
                datePeriod.setDataFinal(dataFinal.getConvertedInput());

                datePeriod = Data.adjustRangeHour(datePeriod);

                setConvertedInput(datePeriod);
            } else if(periodo!=999){
                DatePeriod datePeriod = new DatePeriod();
                datePeriod.setDataInicial(dataInicial.getModelObject());
                datePeriod.setDataFinal(dataFinal.getModelObject());

                datePeriod = Data.adjustRangeHour(datePeriod);

                setConvertedInput(datePeriod);
            } else {
                setConvertedInput(null);
            }
        }
    }
    
    public void setDefaultOutro(){
        containerChoice1.setEnabled(false);
        containerChoice2.setEnabled(true);
        mes = null;
        ano = null;
        containerRadio = 2;
    }

    public void setPeriod(DatePeriod datePeriod) {
        setDefaultOutro();
        this.dataInicial.setModelObject(datePeriod.getDataInicial());
        this.dataFinal.setModelObject(datePeriod.getDataFinal());
    }
    
    @Override
    protected void onInvalid() {
        validateHighlightError();
    }

    @Override
    protected void onValid() {
        validateHighlightError();
    }

    private void validateHighlightError() {
        if (hasErrorMessage()) {
            addErrorClass();
        } else {
            removeErrorClass();
        }
    }

    public void addErrorClass() {
        ComponentWicketUtil.addErrorClass(dataInicial);
        ComponentWicketUtil.addErrorClass(dataFinal);
    }

    public void removeErrorClass() {
        ComponentWicketUtil.removeErrorClass(dataInicial);
        ComponentWicketUtil.removeErrorClass(dataFinal);
    }

    public void addRequiredClass(){
        dataInicial.add(new AttributeAppender("class", " required"));
        dataFinal.add(new AttributeAppender("class", " required"));
    }

    public DatePicker<Date> getDataInicial() {
        return dataInicial;
    }

    public DatePicker<Date> getDataFinal() {
        return dataFinal;
    }

}
