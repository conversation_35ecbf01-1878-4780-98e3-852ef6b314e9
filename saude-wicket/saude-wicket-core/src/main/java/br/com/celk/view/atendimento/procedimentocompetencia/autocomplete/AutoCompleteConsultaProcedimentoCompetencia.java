package br.com.celk.view.atendimento.procedimentocompetencia.autocomplete;

import br.com.celk.component.autocompleteconsulta.AutoCompleteConsulta;
import br.com.celk.component.consulta.configurator.IConsultaConfigurator;
import br.com.celk.component.consulta.configurator.autocomplete.IAutoCompleteSettings;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.procedimentocompetencia.autocomplete.settings.ProcedimentoCompetenciaAutoCompleteSettings;
import br.com.celk.view.atendimento.procedimentocompetencia.configurator.ConsultaConfiguratorProcedimentoCompetencia;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import java.util.Date;
import java.util.List;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteConsultaProcedimentoCompetencia extends AutoCompleteConsulta<ProcedimentoCompetencia> {

    public AutoCompleteConsultaProcedimentoCompetencia(String id) {
        super(id);
    }

    public AutoCompleteConsultaProcedimentoCompetencia(String id, boolean required) {
        super(id, required);
    }

    public AutoCompleteConsultaProcedimentoCompetencia(String id, IModel<ProcedimentoCompetencia> model) {
        super(id, model);
    }

    public AutoCompleteConsultaProcedimentoCompetencia(String id, IModel<ProcedimentoCompetencia> model, boolean required) {
        super(id, model, required);
    }

    @Override
    public int getMinDialogHeight() {
        return 600;
    }

    @Override
    public int getMinDialogWidth() {
        return 900;
    }
    
    @Override
    public IConsultaConfigurator getConsultaConfigurator() {
        return new ConsultaConfiguratorProcedimentoCompetencia(){
            
            @Override
            public IAutoCompleteSettings getAutoCompleteSettingsInstance() {
                return new ProcedimentoCompetenciaAutoCompleteSettings();
            }
        };
    }

    @Override
    public String getTitle() {
        return BundleManager.getString("procedimentos");
    }

    public AutoCompleteConsultaProcedimentoCompetencia setUnidadeAtendimento(Empresa empresa) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setEmpresa(empresa);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setUsuarioCadsus(usuarioCadsus);
        return this;
    }
    
    public AutoCompleteConsultaProcedimentoCompetencia setProfissional(Profissional profissional) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setProfissional(profissional);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setTabelaCbo(TabelaCbo tabelaCbo) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setTabelaCbo(tabelaCbo);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setValidarComplexidadeProcedimento(boolean validarComplexidadeProcedimento) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setValidarComplexidadeProcedimento(validarComplexidadeProcedimento);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setValidarModalidade(boolean validarModalidade) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setValidarModalidade(validarModalidade);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setValidarProcedimentoServico(boolean validarProcedimentoServico) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setValidarProcedimentoServico(validarProcedimentoServico);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setValidarNaoFaturaveis(boolean validarNaoFaturaveis) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setValidarNaoFaturaveis(validarNaoFaturaveis);
        return this;
    }
    
    public AutoCompleteConsultaProcedimentoCompetencia setDataCompetencia(Date dataCompetencia) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setDataCompetencia(dataCompetencia);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setCid(Cid cid) {
        ((ConsultaConfiguratorProcedimentoCompetencia) getConfigurator()).setCid(cid);
        return this;
    }
    
//    public AutoCompleteConsultaProcedimentoCompetencia setValidarProcedimentoRegistro(boolean validarProcedimentoRegistro) {
//        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setValidarProcedimentoRegistro(validarProcedimentoRegistro);
//        return this;
//    }
    
    public AutoCompleteConsultaProcedimentoCompetencia setValidarProcedimentoRegistro(List<Long> procedimentosList) {
        ((ConsultaConfiguratorProcedimentoCompetencia)getConfigurator()).setValidarProcedimentoRegistro(procedimentosList);
        return this;
    }

    public AutoCompleteConsultaProcedimentoCompetencia setTipoTabelaProcedimentoList(List<Long> tipoTabelaProcedimentoList) {
        ((ConsultaConfiguratorProcedimentoCompetencia) getConfigurator()).setTipoTabelaProcedimentoList(tipoTabelaProcedimentoList);
        return this;
    }
}
