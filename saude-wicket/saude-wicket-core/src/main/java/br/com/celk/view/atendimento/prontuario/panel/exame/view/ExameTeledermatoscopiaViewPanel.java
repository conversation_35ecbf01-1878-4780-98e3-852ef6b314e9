package br.com.celk.view.atendimento.prontuario.panel.exame.view;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.checkbox.CheckBoxLongValue;
import br.com.celk.component.checkbox.CheckBoxUtil;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.doublefield.DoubleField;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.longfield.LongField;
import br.com.celk.component.tooltip.Tooltip;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.celk.util.Coalesce;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.RegistroEspecializadoPanel;
import br.com.celk.view.atendimento.prontuario.panel.SoapPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoExamesPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoLacenPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoExameEletrocardiogramaDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoTeledermatoscopia;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.ajax.form.AjaxFormComponentUpdatingBehavior;
import org.apache.wicket.markup.head.CssHeaderItem;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnLoadHeaderItem;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.Model;
import org.apache.wicket.request.resource.CssResourceReference;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.solicitacaoagendamento.ValidacaoDadosPacienteAtendimento.validaDadosPaciente;
import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class ExameTeledermatoscopiaViewPanel extends ProntuarioCadastroPanel {

    private Form<RequisicaoTeledermatoscopia> form;
    private WebMarkupContainer containerTabagismo;
    private WebMarkupContainer containerTempoDoenca;
    private WebMarkupContainer containerInfeccoes;
    private WebMarkupContainer containerComorbidades;
    private WebMarkupContainer containerLesoesEritematosas;
    private WebMarkupContainer containerPadraoMorfologico;
    private WebMarkupContainer containerHanseniase;
    private WebMarkupContainer containerFourF;
    private WebMarkupContainer containerFiveA;
    private WebMarkupContainer containerFiveB;
    private WebMarkupContainer containerFiveC;
    private WebMarkupContainer containerFiveD;
    private WebMarkupContainer containerFiveE;
    private WebMarkupContainer containerFiveF;
    
    private DropDown dropDownTabagismo;
    private DropDown dropDownQuantidadeTempoDoenca;
    private DropDown dropDownLesaoEritematosas;
    private DropDown dropDownLesaoJoelho;
    private DropDown dropDownOrvalhoSangrante;
    private DropDown dropDownLesaoHipocromicas;
    private DropDown dropDownEspessamentoNeural;
    private DropDown dropDownSinalNeurite;
    private DropDown dropDownTipoPsoriase;
    private InputField txtCigarrosDia;
    private InputField txtAnosFuma;
    private InputField txtTempoDoenca;

    private CheckBoxLongValue checkBoxInfeccoesHepatiteB;
    private CheckBoxLongValue checkBoxInfeccoesHepatiteC;
    private CheckBoxLongValue checkBoxInfeccoesHiv;
    
    private CheckBoxLongValue checkBoxComorbidadesHas;
    private CheckBoxLongValue checkBoxComorbidadesDm;
    private CheckBoxLongValue checkBoxComorbidadesIcc;
    private CheckBoxLongValue checkBoxComorbidadesDislipidemia;
    
    private CheckBoxLongValue checkBoxPadraoMorfologicoVulgar;
    private CheckBoxLongValue checkBoxPadraoMorfologicoEritrodermica;
    private CheckBoxLongValue checkBoxPadraoMorfologicoComprArticular;
    private CheckBoxLongValue checkBoxPadraoMorfologicoGutata;
    private CheckBoxLongValue checkBoxPadraoMorfologicoPalmoplantar;
    private CheckBoxLongValue checkBoxPadraoMorfologicoUngueal;
    private CheckBoxLongValue checkBoxPadraoMorfologicoPustulosa;
    private CheckBoxLongValue checkBoxPadraoMorfologicoInvertida;
    private CheckBoxLongValue checkBoxPadraoMorfologicoMucosa;
    
    private DlgImpressaoObject<Long> dlgConfirmacaoImpressao;

    private final List<CheckBoxLongValue> lstCheckBoxInfeccoes = new ArrayList<CheckBoxLongValue>();
    private final List<CheckBoxLongValue> lstCheckBoxComorbidades = new ArrayList<CheckBoxLongValue>();
    private final List<CheckBoxLongValue> lstCheckBoxPadraoMorfologico = new ArrayList<CheckBoxLongValue>();

    private TipoExame tipoExame;
    private final Exame exame;

    private DropDown cbxTipo;
    private SoapDTO.ContainerTelaSoap containerTelaSoap;

    private final String CSS_FILE = "ExameTeledermatoscopiaViewPanel.css";

    public ExameTeledermatoscopiaViewPanel(String id, Exame exame, TipoExame tipoExame) {
        super(id, bundle("solicitacaoTeledermatoscopia"));
        this.exame = exame;
        this.tipoExame = tipoExame;
    }

    public ExameTeledermatoscopiaViewPanel(String id, Exame exame, TipoExame tipoExame, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("solicitacaoTeledermatoscopia"));
        this.exame = exame;
        this.tipoExame = tipoExame;
        this.containerTelaSoap = containerTelaSoap;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        RequisicaoTeledermatoscopia proxy = on(RequisicaoTeledermatoscopia.class);

        getForm().add(new DoubleField(path(proxy.getPeso())));
        getForm().add(new DoubleField(path(proxy.getAltura())));
        getForm().add(cbxTipo = DropDownUtil.getIEnumDropDown(path(proxy.getTipo()), RequisicaoTeledermatoscopia.Tipo.values(), true, "", true, false, true));
        cbxTipo.setLabel(Model.of("tipo"));
        getForm().add(new InputField(path(proxy.getDescricaoRequisicao())));
        getForm().add(new InputField(path(proxy.getDescricaoProcedencia())));
        getForm().add(new InputField(path(proxy.getDescricaoProfissional())));

        getForm().add(new AutoCompleteConsultaCid(path(proxy.getCid()), true).setLabel(new Model<>(bundle("cid"))));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getFototipo()), RequisicaoTeledermatoscopia.Fototipo.values(), true, "", true, false, true).add(new Tooltip().setHtmlText(bundle("legendaFototipo", this))));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getEtilismo()), RequisicaoTeledermatoscopia.Etilismo.values(), true, "", true, false, true));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getPrurido()), true, true));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getHistoriaMorbidaFamilia()), true, true));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getHistoriaMorbidaPregressa()), true, true));
        getForm().add(dropDownTipoPsoriase = DropDownUtil.getIEnumDropDown(path(proxy.getTipoPsoriase()), RequisicaoTeledermatoscopia.TipoPsoriase.values(), true, "", true, false, true));
        dropDownTipoPsoriase.setLabel(new Model<>(bundle("tipoPsoriase", this)));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getMotivoEncaminhamento()), RequisicaoTeledermatoscopia.MotivoEncaminhamento.values(), true, "", false, false, true));
        getForm().add(new InputField(path(proxy.getExposicaoSolHoraDia())));
        getForm().add(new InputField(path(proxy.getExposicaoSolMesesAno())));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getHistoriaMorbidaCancerPele()), true));
        getForm().add(DropDownUtil.getNaoSimLongDropDown(path(proxy.getHistoriaMorbidaCancerOutros()), true));
        getForm().add(DropDownUtil.getIEnumDropDown(path(proxy.getFotoprotetor()), RequisicaoTeledermatoscopia.Fotoprotetor.values(), true, "", false, false, true));

        /* INICIO - inicializar container's */
        containerTabagismo = new WebMarkupContainer("containerTabagismo");
        containerTempoDoenca = new WebMarkupContainer("containerTempoDoenca");
        containerInfeccoes = new WebMarkupContainer("containerInfeccoes");
        containerComorbidades = new WebMarkupContainer("containerComorbidades");
        containerLesoesEritematosas = new WebMarkupContainer("containerLesoesEritematosas");
        containerPadraoMorfologico = new WebMarkupContainer("containerPadraoMorfologico");
        containerHanseniase = new WebMarkupContainer("containerHanseniase");

        /* FIM- inicializar container's */
        
//        /* INICIO - containerTabagismo */
        containerTabagismo.add(dropDownTabagismo = DropDownUtil.getIEnumDropDown(path(proxy.getTabagismo()), RequisicaoTeledermatoscopia.Tabagismo.values(), true, "", true, false, true));
        containerTabagismo.add(txtCigarrosDia = (InputField) new InputField(path(proxy.getCigarrosDia())).setLabel(new Model<>(bundle("cigarrosPorDia", this))));
        containerTabagismo.add(txtAnosFuma = (InputField) new InputField(path(proxy.getAnosFuma())).setLabel(new Model<>(bundle("anosQueFuma", this))));
        
        dropDownTabagismo.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarContainerTabagismo(target, false);
            }
        });

//        /* FIM - containerTabagismo */
        
//        /* INICIO - containerTempoDoenca */
        containerTempoDoenca.add(txtTempoDoenca = new InputField(path(proxy.getTempoDoenca())));
        containerTempoDoenca.add(dropDownQuantidadeTempoDoenca = DropDownUtil.getIEnumDropDown(path(proxy.getQuantidadeTempoDoenca()), RequisicaoTeledermatoscopia.QuantidadeTempoDoenca.values(), true, "", false, false, true));
        dropDownQuantidadeTempoDoenca.setLabel(Model.of("tempoEm"));
        
        txtTempoDoenca.add(new AjaxFormComponentUpdatingBehavior("onBlur") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarContainerTempoDoenca(target, false);
            }
        });

//        /* FIM - containerTempoDoenca */
        
//        /* INICIO - containerInfeccoes */
        containerInfeccoes.add(checkBoxInfeccoesHepatiteB = new CheckBoxLongValue("infeccoesHepatiteB", RequisicaoTeledermatoscopia.Infeccoes.HEPATITE_B.value(), new Model<Long>()));
        containerInfeccoes.add(checkBoxInfeccoesHepatiteC = new CheckBoxLongValue("infeccoesHepatiteC", RequisicaoTeledermatoscopia.Infeccoes.HEPATITE_C.value(), new Model<Long>()));
        containerInfeccoes.add(checkBoxInfeccoesHiv = new CheckBoxLongValue("infeccoesHiv", RequisicaoTeledermatoscopia.Infeccoes.HIV.value(), new Model<Long>()));

        lstCheckBoxInfeccoes.add(checkBoxInfeccoesHepatiteB);
        lstCheckBoxInfeccoes.add(checkBoxInfeccoesHepatiteC);
        lstCheckBoxInfeccoes.add(checkBoxInfeccoesHiv);

        containerInfeccoes.add(new InputField(path(proxy.getDescricaoInfeccoes())));

//        /* FIM - containerInfeccoes */
        
//        /* INICIO - containerComorbidades */
        containerComorbidades.add(checkBoxComorbidadesHas = new CheckBoxLongValue("comorbidadesHas", RequisicaoTeledermatoscopia.Comorbidades.HAS.value(), new Model<Long>()));
        containerComorbidades.add(checkBoxComorbidadesDm = new CheckBoxLongValue("comorbidadesDm", RequisicaoTeledermatoscopia.Comorbidades.DM.value(), new Model<Long>()));
        containerComorbidades.add(checkBoxComorbidadesIcc = new CheckBoxLongValue("comorbidadesIcc", RequisicaoTeledermatoscopia.Comorbidades.ICC.value(), new Model<Long>()));
        containerComorbidades.add(checkBoxComorbidadesDislipidemia = new CheckBoxLongValue("comorbidadesDislipidemia", RequisicaoTeledermatoscopia.Comorbidades.DISLIPIDEMIA.value(), new Model<Long>()));

        lstCheckBoxComorbidades.add(checkBoxComorbidadesHas);
        lstCheckBoxComorbidades.add(checkBoxComorbidadesDm);
        lstCheckBoxComorbidades.add(checkBoxComorbidadesIcc);
        lstCheckBoxComorbidades.add(checkBoxComorbidadesDislipidemia);

        containerComorbidades.add(new InputField(path(proxy.getDescricaoComorbidades())));

//        /* FIM - containerComorbidades */
        
//        /* INICIO - containerLesoesEritematosas */
        containerLesoesEritematosas.add(dropDownLesaoEritematosas = (DropDown) DropDownUtil.getNaoSimLongDropDown(path(proxy.getLesaoEritematosas()), true, true).setLabel(new Model<>(bundle("pacienteApresentaLesoesEritematosasDescamativasInfiltradas", this))));
        containerLesoesEritematosas.add(dropDownLesaoJoelho = (DropDown) DropDownUtil.getNaoSimLongDropDown(path(proxy.getLesaoJoelho()), true).add(new Tooltip().setHtmlText(bundle("apresentaLesoesJoelhoCotovelosCouroCabeludoFenomenoKoebner", this))));
        dropDownLesaoJoelho.setLabel(new Model<>(bundle("lesoes", this)));
        containerLesoesEritematosas.add(dropDownOrvalhoSangrante = DropDownUtil.getNaoSimLongDropDown(path(proxy.getOrvalhoSangrante()), true, true));
        dropDownOrvalhoSangrante.setLabel(new Model<>(bundle("apresentaOrvalhoSangrante", this)));
        
        dropDownLesaoEritematosas.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarContainerLesoesEritematosas(target, false);
            }
        });
        
//        /* FIM - containerLesoesEritematosas */
        
//        /* INICIO - containerPadraoMorfologico */
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoVulgar = new CheckBoxLongValue("padraoMorfologicoVulgar", RequisicaoTeledermatoscopia.PadraoMorfologico.VULGAR.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoEritrodermica = new CheckBoxLongValue("padraoMorfologicoEritrodermica", RequisicaoTeledermatoscopia.PadraoMorfologico.ERITRODERMICA.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoComprArticular = new CheckBoxLongValue("padraoMorfologicoComprArticular", RequisicaoTeledermatoscopia.PadraoMorfologico.COMPR_ARTICULAR.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoGutata = new CheckBoxLongValue("padraoMorfologicoGutata", RequisicaoTeledermatoscopia.PadraoMorfologico.GUTATA.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoPalmoplantar = new CheckBoxLongValue("padraoMorfologicoPalmoplantar", RequisicaoTeledermatoscopia.PadraoMorfologico.PALMOPLANTAR.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoUngueal = new CheckBoxLongValue("padraoMorfologicoUngueal", RequisicaoTeledermatoscopia.PadraoMorfologico.UNGUEAL.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoPustulosa = new CheckBoxLongValue("padraoMorfologicoPustulosa", RequisicaoTeledermatoscopia.PadraoMorfologico.PUSTULOSA.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoInvertida = new CheckBoxLongValue("padraoMorfologicoInvertida", RequisicaoTeledermatoscopia.PadraoMorfologico.INVERTIDA.value(), new Model<Long>()));
        containerPadraoMorfologico.add(checkBoxPadraoMorfologicoMucosa = new CheckBoxLongValue("padraoMorfologicoMucosa", RequisicaoTeledermatoscopia.PadraoMorfologico.MUCOSA.value(), new Model<Long>()));

        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoVulgar);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoEritrodermica);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoComprArticular);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoGutata);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoPalmoplantar);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoUngueal);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoPustulosa);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoInvertida);
        lstCheckBoxPadraoMorfologico.add(checkBoxPadraoMorfologicoMucosa);

        containerPadraoMorfologico.add(new InputField(path(proxy.getDescricaoPadraoMorfologico())));

//        /* FIM - containerPadraoMorfologico */
        
//        /* INICIO - containerHanseniase */
        containerHanseniase.add(dropDownLesaoHipocromicas = (DropDown) DropDownUtil.getIEnumDropDown(path(proxy.getLesaoHipocromicas()), RequisicaoTeledermatoscopia.LesaoHipocromicas.values(), true, "", true, false, true).setLabel(new Model<>(bundle("hanseniase", this))));
        containerHanseniase.add(dropDownEspessamentoNeural = (DropDown) DropDownUtil.getIEnumDropDown(path(proxy.getEspessamentoNeural()), RequisicaoTeledermatoscopia.EspessamentoNeural.values(), true, "", false, false, true).setLabel(new Model<>(bundle("espessamentoNeural", this))));
        containerHanseniase.add(dropDownSinalNeurite = (DropDown) DropDownUtil.getIEnumDropDown(path(proxy.getSinalNeurite()), RequisicaoTeledermatoscopia.SinalNeurite.values(), true, "", false, false, true).setLabel(new Model<>(bundle("sinalNeurite", this))));
        
        dropDownLesaoHipocromicas.add(new AjaxFormComponentUpdatingBehavior("onchange") {
            @Override
            protected void onUpdate(AjaxRequestTarget target) {
                habilitarContainerHanseniase(target, false);
            }
        });

//        /* FIM - containerHanseniase */    

        getForm().add(containerTabagismo);
        getForm().add(containerTempoDoenca);
        getForm().add(containerInfeccoes);
        getForm().add(containerComorbidades);
        getForm().add(containerLesoesEritematosas);
        getForm().add(containerPadraoMorfologico);
        getForm().add(containerHanseniase);

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
            }

            @Override
            public boolean isVisible() {
                return SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO.equals(containerTelaSoap);
            }
        }.setDefaultFormProcessing(false));

        getForm().add(new AbstractAjaxButton("btnSalvar") {

            @Override
            protected void onError(AjaxRequestTarget target, Form<?> form) {
                super.onError(target, form);
                habilitarContainerTabagismo(target, true);
                habilitarContainerTempoDoenca(target, true);
                habilitarContainerLesoesEritematosas(target, true);
                habilitarContainerHanseniase(target, true);
            }

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarExameTeledermatoscopia(target);
            }
        });

        add(getForm());
        if (this.tipoExame == null) {
            this.tipoExame = new TipoExame();
        } else {
            carregarExameTeledermatoscopia();
        }
        
        habilitarContainerTabagismo(null, false);
        habilitarContainerTempoDoenca(null,false);
        habilitarContainerLesoesEritematosas(null,false);
        habilitarContainerHanseniase(null,false);
    }

    private Form<RequisicaoTeledermatoscopia> getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", new CompoundPropertyModel<>(new RequisicaoTeledermatoscopia()));
        }

        return this.form;
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(CssHeaderItem.forReference(new CssResourceReference(this.getClass(), CSS_FILE)));
        
        if (RequisicaoTeledermatoscopia.Tabagismo.FUMANTE.value().equals(getForm().getModel().getObject().getTabagismo())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerTabagismo)));
        } else {
            response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerTabagismo)));            
        }
        if (Coalesce.asLong(getForm().getModel().getObject().getTempoDoenca()) > 0L) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerTempoDoenca)));
        } else {
            response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerTempoDoenca)));            
        }
        if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getLesaoEritematosas())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerLesoesEritematosas)));
        } else {
            response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerLesoesEritematosas)));            
        }
        if (RequisicaoTeledermatoscopia.LesaoHipocromicas.DUVIDA.value().equals(getForm().getModel().getObject().getLesaoHipocromicas())
                || RequisicaoTeledermatoscopia.LesaoHipocromicas.NAO.value().equals(getForm().getModel().getObject().getLesaoHipocromicas())) {
            response.render(OnLoadHeaderItem.forScript(JScript.toggleFieldset(containerHanseniase)));
        } else {
            response.render(OnLoadHeaderItem.forScript(JScript.hideFieldset(containerHanseniase)));
        }
    }
    
    private void salvarExameTeledermatoscopia(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (tipoExame.getExameProcedimentoPadrao() == null) {
            throw new ValidacaoException(bundle("tipoExameSemProcedimentoPadrao", this));
        }

        validaDadosPaciente(getAtendimento().getUsuarioCadsus());

        RequisicaoTeledermatoscopia requisicaoTeledermatoscopia = getForm().getModelObject();

        requisicaoTeledermatoscopia.setComorbidades(CheckBoxUtil.getSomatorio(lstCheckBoxComorbidades));
        requisicaoTeledermatoscopia.setInfeccoes(CheckBoxUtil.getSomatorio(lstCheckBoxInfeccoes));
        requisicaoTeledermatoscopia.setPadraoMorfologico(CheckBoxUtil.getSomatorio(lstCheckBoxPadraoMorfologico));

        ExameCadastroAprovacaoDTO dto = new ExameCadastroAprovacaoDTO();

        if (this.exame != null) {
            dto.setCodigoExameCadastrado(this.exame.getCodigo());
            dto.setDataSolicitacao(this.exame.getDataSolicitacao());
            dto.setAtendimento(this.exame.getAtendimento());
            dto.setCodigoUnidade(this.exame.getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(this.exame.getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(this.exame.getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(this.exame.getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(this.exame.getAtendimento().getUsuarioCadsus().getNomeSocial());
        } else {
            dto.setDataSolicitacao(DataUtil.getDataAtual());
            dto.setAtendimento(getAtendimento());
            dto.setCodigoUnidade(getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(getAtendimento().getUsuarioCadsus().getNomeSocial());
        }

        ExameProcedimentoDTO exameProcedimento = new ExameProcedimentoDTO();

        exameProcedimento.setExameProcedimento(tipoExame.getExameProcedimentoPadrao());
        exameProcedimento.setQuantidade(1L);
        exameProcedimento.setComplemento(null);
        exameProcedimento.setValor(0D);

        List<ExameProcedimentoDTO> item = new ArrayList<>();
        item.add(exameProcedimento);

        dto.setExameProcedimentoDTOs(item);
        Long codigoExameCadastrado = BOFactoryWicket.getBO(ExameFacade.class).cadastrarSolicitacaoTeledermatoscopia(dto, requisicaoTeledermatoscopia);

        initDialogImpressao(target);
        dlgConfirmacaoImpressao.show(target, codigoExameCadastrado);
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<Long>(getProntuarioController().newWindowId(), "Deseja imprimir a solicitação?") {
                @Override
                public DataReport getDataReport(Long codigoExameCadastrado) throws ReportException {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();

                    ImpressaoExameEletrocardiogramaDTOParam param = new ImpressaoExameEletrocardiogramaDTOParam();
                    param.setCodigoExame(codigoExameCadastrado);
                    param.setAtendimento(exameCadastrado.getAtendimento());

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).impressaoExameTeledermatoscopia(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, Long codigoExameCadastrado) throws ValidacaoException, DAOException {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addProperty(VOUtils.montarPath(Exame.PROP_TIPO_EXAME, TipoExame.PROP_TIPO))
                            .addParameter(new QueryCustom.QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();

                    Boolean isLacen = RepositoryComponentDefault.Tipo.LACEN.value().equals(exameCadastrado.getTipoExame().getTipo());
                    if(isLacen){
                        getProntuarioController().changePanel(target, new SolicitacaoLacenPanel(getProntuarioController().panelId()));
                    } else {
                        if(containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
                        } else if(containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
                        } else {
                            getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap));
                        }
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private void carregarExameTeledermatoscopia() {
        ExameRequisicao exameRequisicao = LoadManager.getInstance(ExameRequisicao.class)
                .addProperty(ExameRequisicao.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, tipoExame.getExameProcedimentoPadrao()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_STATUS), ExameRequisicao.Status.ABERTO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(Exame.STATUS_AUTORIZADO, Exame.STATUS_DESVINCULADO, Exame.STATUS_RECEBIDO, Exame.STATUS_SOLICITADO)))
                .start().getVO();

        if (exameRequisicao != null) {
            RequisicaoTeledermatoscopia requisicaoTeledermatoscopia = LoadManager.getInstance(RequisicaoTeledermatoscopia.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(RequisicaoTeledermatoscopia.PROP_EXAME_REQUISICAO, exameRequisicao))
                    .start().getVO();

            if (requisicaoTeledermatoscopia != null) {
                getForm().setModelObject(requisicaoTeledermatoscopia);

                CheckBoxUtil.selecionarSomatorio(lstCheckBoxComorbidades, requisicaoTeledermatoscopia.getComorbidades());
                CheckBoxUtil.selecionarSomatorio(lstCheckBoxInfeccoes, requisicaoTeledermatoscopia.getInfeccoes());
                CheckBoxUtil.selecionarSomatorio(lstCheckBoxPadraoMorfologico, requisicaoTeledermatoscopia.getPadraoMorfologico());
            }
        } else {
            carregarPesoAltura();
        }
    }
    
    private void habilitarContainerTabagismo(AjaxRequestTarget target, boolean salvar){
        if (RequisicaoTeledermatoscopia.Tabagismo.FUMANTE.value().equals(getForm().getModel().getObject().getTabagismo())) {
            txtCigarrosDia.setEnabled(true);
            txtAnosFuma.setEnabled(true);
            txtCigarrosDia.setRequired(true);
            txtAnosFuma.setRequired(true);
            txtCigarrosDia.addRequiredClass();
            txtAnosFuma.addRequiredClass();
        } else {
            txtCigarrosDia.setEnabled(false);
            txtAnosFuma.setEnabled(false);
            txtCigarrosDia.setRequired(false);
            txtAnosFuma.setRequired(false);
            txtCigarrosDia.removeRequiredClass();
            txtAnosFuma.removeRequiredClass();
        }
        
        if(target != null){
            if(!salvar){
                txtCigarrosDia.limpar(target);
                txtAnosFuma.limpar(target);
            }
            if (RequisicaoTeledermatoscopia.Tabagismo.FUMANTE.value().equals(getForm().getModel().getObject().getTabagismo())) {
                target.appendJavaScript(JScript.toggleFieldset(containerTabagismo));
                target.appendJavaScript(JScript.showFieldset(containerTabagismo));
            } else {
                target.appendJavaScript(JScript.hideFieldset(containerTabagismo));
            }
        }
    }
    
    private void habilitarContainerTempoDoenca(AjaxRequestTarget target, boolean salvar){
        if (Coalesce.asLong(getForm().getModel().getObject().getTempoDoenca()) > 0L) {
            dropDownQuantidadeTempoDoenca.setEnabled(true);
            dropDownQuantidadeTempoDoenca.setRequired(true);
            dropDownQuantidadeTempoDoenca.addRequiredClass();
        } else {
            dropDownQuantidadeTempoDoenca.setEnabled(false);
            dropDownQuantidadeTempoDoenca.setRequired(false);
            dropDownQuantidadeTempoDoenca.removeRequiredClass();
        }
        
        if(target != null){
            if (!salvar) {
                dropDownQuantidadeTempoDoenca.limpar(target);
            }
            if (Coalesce.asLong(getForm().getModel().getObject().getTempoDoenca()) > 0L) {
                target.appendJavaScript(JScript.toggleFieldset(containerTempoDoenca));
                target.appendJavaScript(JScript.showFieldset(containerTempoDoenca));
            } else {
                target.appendJavaScript(JScript.hideFieldset(containerTempoDoenca));
            }
        }
    }
    
    private void habilitarContainerLesoesEritematosas(AjaxRequestTarget target, boolean salvar){
        if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getLesaoEritematosas())) {
            dropDownLesaoJoelho.setEnabled(true);
            dropDownOrvalhoSangrante.setEnabled(true);
            
            dropDownTipoPsoriase.setRequired(true);
            dropDownLesaoJoelho.setRequired(true);
            dropDownOrvalhoSangrante.setRequired(true);
            
            dropDownTipoPsoriase.addRequiredClass();
            dropDownLesaoJoelho.addRequiredClass();
            dropDownOrvalhoSangrante.addRequiredClass();
        } else {
            dropDownLesaoJoelho.setEnabled(false);
            dropDownOrvalhoSangrante.setEnabled(false);
            
            dropDownTipoPsoriase.setRequired(false);
            dropDownLesaoJoelho.setRequired(false);
            dropDownOrvalhoSangrante.setRequired(false);
            
            dropDownTipoPsoriase.removeRequiredClass();
            dropDownLesaoJoelho.removeRequiredClass();
            dropDownOrvalhoSangrante.removeRequiredClass();
        }
        
        if(target != null){
            target.appendJavaScript(JScript.initMasks());
//            dropDownTipoPsoriase.limpar(target);
            if (!salvar) {
                dropDownLesaoJoelho.limpar(target);
                dropDownOrvalhoSangrante.limpar(target);
            }
            if (RepositoryComponentDefault.SIM_LONG.equals(getForm().getModel().getObject().getLesaoEritematosas())) {
                target.appendJavaScript(JScript.toggleFieldset(containerLesoesEritematosas));
                target.appendJavaScript(JScript.showFieldset(containerLesoesEritematosas));
            } else {
                target.appendJavaScript(JScript.hideFieldset(containerLesoesEritematosas));
            }
        }
    }
    
    private void habilitarContainerHanseniase(AjaxRequestTarget target, boolean salvar){
        if (RequisicaoTeledermatoscopia.LesaoHipocromicas.DUVIDA.value().equals(getForm().getModel().getObject().getLesaoHipocromicas())
                || RequisicaoTeledermatoscopia.LesaoHipocromicas.NAO.value().equals(getForm().getModel().getObject().getLesaoHipocromicas())) {
            dropDownEspessamentoNeural.setEnabled(true);
            dropDownSinalNeurite.setEnabled(true);
            
            dropDownEspessamentoNeural.setRequired(true);
            dropDownSinalNeurite.setRequired(true);
            
            dropDownEspessamentoNeural.addRequiredClass();
            dropDownSinalNeurite.addRequiredClass();
        } else {
            dropDownEspessamentoNeural.setEnabled(false);
            dropDownSinalNeurite.setEnabled(false);
            
            dropDownEspessamentoNeural.setRequired(false);
            dropDownSinalNeurite.setRequired(false);
            
            dropDownEspessamentoNeural.removeRequiredClass();
            dropDownSinalNeurite.removeRequiredClass();
        }
        
        if(target != null){
            target.appendJavaScript(JScript.initMasks());
            if (!salvar) {
                dropDownEspessamentoNeural.limpar(target);
                dropDownSinalNeurite.limpar(target);
            }
            if (RequisicaoTeledermatoscopia.LesaoHipocromicas.DUVIDA.value().equals(getForm().getModel().getObject().getLesaoHipocromicas())
                    || RequisicaoTeledermatoscopia.LesaoHipocromicas.NAO.value().equals(getForm().getModel().getObject().getLesaoHipocromicas())) {
                target.appendJavaScript(JScript.toggleFieldset(containerHanseniase));
                target.appendJavaScript(JScript.showFieldset(containerHanseniase));
            } else {
                target.appendJavaScript(JScript.hideFieldset(containerHanseniase));
            }
        }
    }
    
    private void carregarPesoAltura() {
        UsuarioCadsusDado usuarioCadsusDado = LoadManager.getInstance(UsuarioCadsusDado.class)
                .addParameter(new QueryCustom.QueryCustomParameter(UsuarioCadsusDado.PROP_CODIGO, getAtendimento().getUsuarioCadsus().getCodigo()))
                .start().getVO();

        if (usuarioCadsusDado != null) {
            getForm().getModelObject().setPeso(usuarioCadsusDado.getPeso());
            getForm().getModelObject().setAltura(usuarioCadsusDado.getAltura());
        }
    }
}
