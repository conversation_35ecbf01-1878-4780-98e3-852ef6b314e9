package br.com.celk.component.telefonefield;

import br.com.celk.component.inputfield.InputField;
import br.com.celk.util.StringUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.model.IModel;
import org.apache.wicket.util.string.Strings;

/**
 *
 * <AUTHOR>
 */
public class TelefoneField extends InputField<String>  {

    public TelefoneField(String id, IModel<String> model) {
        super(id, model, String.class);
        init();
    }

    public TelefoneField(String id) {
        super(id, String.class);
        init();
    }
    
    private void init(){
        
        add(new AttributeModifier("class", "fone"));
        add(new AttributeModifier("size", "16"));
    }
    
    @Override
    protected void convertInput() {
        String[] value = getInputAsArray();
        String tmp = value != null && value.length > 0 ? value[0] : null;
        if (getConvertEmptyInputStringToNull() && Strings.isEmpty(tmp)) {
            setConvertedInput(null);
        } else {
            String telefone = StringUtil.getDigits(tmp);
            if (StringUtils.trimToNull(telefone) == null) {
                 setConvertedInput(telefone);
            } else {
                setConvertedInput(telefone);
            }
        }
    }
    
    @Override
    public String getInput() {
        String[] value = getInputAsArray();
        String tmp = value != null && value.length > 0 ? value[0] : null;
        if (value == null || value.length == 0) {
            return null;
        } else {
            String telefone = StringUtil.getDigits(tmp);
            if (StringUtils.trimToNull(telefone) == null) {
                 return null;
            }
            return trim(value[0]);
        }
    }
}
