package br.com.celk.component.lote.saida.dialog;

import br.com.celk.component.window.Window;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.ajax.markup.html.modal.ModalWindow;

/**
 * Created by sulivan on 11/12/17.
 */
public abstract class DlgJustificativaLotePosterior extends Window {

    private String titulo;
    private PnlJustificativaLotePosterior pnlJustificativaLotePosterior;
    private MovimentoGrupoEstoqueItemDTO dto;
    private boolean exibirAcaoFechar = false;

    public DlgJustificativaLotePosterior(String id, String titulo) {
        super(id);
        this.titulo = titulo;
        init();
    }

    private void init() {
        setOutputMarkupId(true);

        setInitialWidth(600);
        setInitialHeight(200);

        setResizable(false);

        setTitle(titulo);

        setContent(pnlJustificativaLotePosterior = new PnlJustificativaLotePosterior(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, String justificatica) throws ValidacaoException, DAOException {
                close(target);
                DlgJustificativaLotePosterior.this.onConfirmar(target, justificatica, DlgJustificativaLotePosterior.this.dto);
            }

            @Override
            public Long getMaxLengthMotivo() {
                return DlgJustificativaLotePosterior.this.getMaxLengthMotivo();
            }
        });

        setCloseButtonCallback(new ModalWindow.CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
//                if(isExibirAcaoFechar()){
//                    fechar(target);
//                    return true;
//                }
                return false;
            }
        });
    }

    private void fechar(AjaxRequestTarget target) {
        DlgJustificativaLotePosterior.this.onFechar(target);
        close(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String justificativa, MovimentoGrupoEstoqueItemDTO dto) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target);

    public Long getMaxLengthMotivo() {
        return 200L;// Tamanho default
    }

    public void setDto(MovimentoGrupoEstoqueItemDTO dto) {
        this.dto = dto;
    }

    public boolean isExibirAcaoFechar() {
        return exibirAcaoFechar;
    }

    public void setExibirAcaoFechar(boolean exibirAcaoFechar) {
        this.exibirAcaoFechar = exibirAcaoFechar;
    }

    @Override
    public void show(AjaxRequestTarget target) {
        pnlJustificativaLotePosterior.limpar(target, isExibirAcaoFechar());
        super.show(target);
    }
}
