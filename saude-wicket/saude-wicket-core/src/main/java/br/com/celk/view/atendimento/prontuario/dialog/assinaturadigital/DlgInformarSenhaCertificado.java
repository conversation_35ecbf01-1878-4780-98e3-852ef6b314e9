package br.com.celk.view.atendimento.prontuario.dialog.assinaturadigital;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.panel.assinaturadigital.PnlInformarSenhaCertificado;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtilDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 * <AUTHOR>
 */
public abstract class DlgInformarSenhaCertificado extends Window {

    private PnlInformarSenhaCertificado pnlInformarSenhaCertificado;
    private final AssinaturaDigitalUtilDTO dto;

    public DlgInformarSenhaCertificado(String id, AssinaturaDigitalUtilDTO dto) {
        super(id);
        this.dto = dto;
        init();
    }

    private void init() {
        setInitialHeight(70);
        setInitialWidth(400);
        setResizable(false);
        setTitle(BundleManager.getString("confirmacaoDadosBasico"));
        setContent(new PnlInformarSenhaCertificado(getContentId(), this.dto) {
            @Override
            public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgInformarSenhaCertificado.this.onConfirmar(target);
                close(target);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }
        });
    }

    public abstract void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException;
}