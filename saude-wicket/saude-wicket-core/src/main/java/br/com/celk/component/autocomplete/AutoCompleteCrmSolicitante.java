package br.com.celk.component.autocomplete;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.prontuario.exame.AtendimentoExame;
import ch.lambdaj.Lambda;
import ch.lambdaj.collection.LambdaCollection;
import ch.lambdaj.collection.LambdaCollections;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class AutoCompleteCrmSolicitante extends AutoComplete<String> {

    private List<String> lstCrm = new ArrayList<String>();

    public AutoCompleteCrmSolicitante(String id, IModel object) {
        super(id, object);
        init();
    }

    public AutoCompleteCrmSolicitante(String id) {
        super(id);
        init();
    }

    private void init() {
        getAutoCompleteSettings().setThrottleDelay(600);
        add(new AttributeModifier("class", "uppercase"));
    }

    @Override
    protected Iterator<String> getChoices(String input) {
        lstCrm.clear();
        if (StringUtils.trimToNull(input) != null) {
            List<AtendimentoExame> lstAux = LoadManager.getInstance(AtendimentoExame.class)
                    .addProperty(AtendimentoExame.PROP_CODIGO)
                    .addProperty(AtendimentoExame.PROP_CRM)
                    .addProperty(AtendimentoExame.PROP_NOME_PROFISSIONAL)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoExame.PROP_CRM), BuilderQueryCustom.QueryParameter.ILIKE, input))
                    .addSorter(new QueryCustom.QueryCustomSorter(AtendimentoExame.PROP_DATA_EXAME, "desc"))
                    .setMaxResults(10)
                    .start().getList();

//            Collection<AtendimentoExame> selectDistinctArgument = Lambda.selectDistinctArgument(lstAux, Lambda.on(AtendimentoExame.class).getCrm());
//            lstCrm = Lambda.extract(selectDistinctArgument, Lambda.on(AtendimentoExame.class).getCrm());
            
            LambdaCollection<AtendimentoExame> lambdaCollection = LambdaCollections.with(lstAux);
            lstCrm = new ArrayList<String>(lambdaCollection.extract(Lambda.on(AtendimentoExame.class).getCrm()).distinct());

        }
        return lstCrm.iterator();
    }

    @Override
    public String getTextValue(String object) {
        return object;
    }

}
