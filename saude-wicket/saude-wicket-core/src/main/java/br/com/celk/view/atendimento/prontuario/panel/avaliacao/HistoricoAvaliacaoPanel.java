package br.com.celk.view.atendimento.prontuario.panel.avaliacao;

import br.com.celk.component.consulta.dataprovider.pager.IPagerProvider;
import br.com.celk.component.consulta.dataprovider.pager.QueryPagerProvider;
import br.com.celk.component.table.column.DoubleColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.AvaliacaoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ConsultaProntuarioHistoricoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.command.QueryLoadParameters;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoPrimario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.ksisolucoes.dao.HQLProperties;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
public class HistoricoAvaliacaoPanel extends ConsultaProntuarioHistoricoPanel<AtendimentoPrimario, QueryLoadParameters>{ 

    public HistoricoAvaliacaoPanel(String id) {
        super(id, BundleManager.getString("avaliacao"));
    }

    @Override
    public DefaultProntuarioPanel newProntuarioPanel(String id) {
        return new AvaliacaoPanel(id);
    }

    @Override
    public void initForm(Form form) {
    }

    @Override
    public List<IColumn> getColumns(List<IColumn> columns) {
        AtendimentoPrimario proxy = on(AtendimentoPrimario.class);
        
        columns.add(createColumn(bundle("data"), proxy.getAtendimento().getDataAtendimento()));
        columns.add(new DoubleColumn(bundle("pesoKg"), path(proxy.getPeso())).setCasasDecimais(3));
        columns.add(createColumn(bundle("temperatura"), proxy.getTemperatura()));
        columns.add(createColumn(bundle("pas"), proxy.getPressaoArterialSistolica()));
        columns.add(createColumn(bundle("pad"), proxy.getPressaoArterialDiastolica()));
        columns.add(new DoubleColumn(bundle("alturaCm"), path(proxy.getAltura())).setCasasDecimais(1));
        columns.add(createColumn(bundle("glicemia"), proxy.getGlicemia()));
        
        return columns;
    }

    @Override
    public IPagerProvider getPagerProviderInstance() {
        return new QueryPagerProvider<AtendimentoPrimario, QueryLoadParameters>() {

            @Override
            public DataPagingResult<AtendimentoPrimario> executeQueryPager(DataPaging<QueryLoadParameters> dataPaging) throws DAOException, ValidacaoException {
                Atendimento atendimento = getAtendimento();
                TipoAtendimento tipoAtendimento = atendimento.getNaturezaProcuraTipoAtendimento().getTipoAtendimento();
                UsuarioCadsus usuarioCadsus = atendimento.getUsuarioCadsus();

                QueryLoadParameters query = dataPaging.getParam();
                        
                query.addProperties(new HQLProperties(AtendimentoPrimario.class).getProperties());
                query.addProperty(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO));
                query.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()));
                query.addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO), BuilderQueryCustom.QueryParameter.DIFERENTE, atendimento.getCodigo()));
                query.addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AtendimentoPrimario.PROP_ATENDIMENTO, Atendimento.PROP_DATA_ATENDIMENTO), BuilderQueryCustom.QuerySorter.DECRESCENTE));
                
                return BOFactoryWicket.getBO(CommomFacade.class).customPager(dataPaging);
            }
        };
    }

    @Override
    public QueryLoadParameters getParameters() {
        QueryLoadParameters query = new QueryLoadParameters(AtendimentoPrimario.class);
        
        
        return query;
    }

}
