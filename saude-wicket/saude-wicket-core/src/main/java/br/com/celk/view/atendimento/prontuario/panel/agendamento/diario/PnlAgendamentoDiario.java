package br.com.celk.view.atendimento.prontuario.panel.agendamento.diario;


import br.com.celk.agendamento.AgendamentoHelper;
import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.action.link.ModelActionLinkPanel;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.dialog.DlgConfirmacaoSimNao;
import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.interfaces.ConsultaListener;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.interfaces.RemoveListener;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.CustomColumn;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.component.template.Panel;
import br.com.celk.component.window.WindowUtil;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.agendamento.MarcacaoAgendamentoColumnPanel;
import br.com.celk.view.atendimento.prontuario.panel.agendamento.MarcacaoAgendamentoHorarioTableColor;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgRecomendacoesAgenda;
import br.com.celk.view.atendimento.prontuario.panel.dialog.DlgRecomendacoesAgendaAntesConfirmar;
import br.com.celk.view.basico.profissional.autocomplete.AutoCompleteConsultaProfissional;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendamentoListaEsperaDTOParam;
import br.com.ksisolucoes.bo.agendamento.interfaces.facade.AgendamentoFacade;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.solicitacaoagendamento.SolicitacaoAgendamentoFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.basico.interfaces.facade.AtendimentoReportFacade;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.basico.interfaces.dto.RelatorioImprimirComprovanteAgendamentoDTOParam;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.agendamento.Agenda;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamentoItem;
import br.com.ksisolucoes.vo.agendamento.TipoAtendimentoAgenda;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import br.com.ksisolucoes.vo.prontuario.exame.SolicitacaoAgendamentoExame;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.extract;
import static ch.lambdaj.Lambda.on;

/**
 * Created by laudecir
 */
public abstract class PnlAgendamentoDiario extends Panel {

    private AgendaGradeAtendimentoDTOParam param = new AgendaGradeAtendimentoDTOParam();
    private AutoCompleteConsultaProfissional autoCompleteConsultaProfissional;
    private SelectionTable<AgendaGradeAtendimentoDTO> tblAgendamentoSolicitacao;
    private List<AgendaGradeAtendimentoDTO> agendaGradeAtendimentoDTOList;
    private DlgRecomendacoesAgendaAntesConfirmar dlgRecomendacoesAgendaAntesConfirmar;
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNaoRecomendacoes;
    private LoteSolicitacaoAgendamentoItem loteSolicitacaoAgendamentoItem;
    private String profissionalAgenda;
    private InputField txtProfissionalAgenda;
    private String unidadeAgenda;
    private Profissional profissional;
    private InputField txtUnidadeAgenda;
    private Table<AgendaGradeAtendimentoDTO> tblAgendamentos;
    private List<AgendaGradeAtendimentoPacienteDTO> agendamentosList;
    private final List<AgendaGradeAtendimentoDTO> agendarList = new ArrayList<>();
    private DlgConfirmacaoSimNao dlgConfirmacaoSimNao;
    private DlgRecomendacoesAgenda dlgRecomendacoesAgenda;
    private AgendamentoListaEsperaDTOParam paraLista;

    public PnlAgendamentoDiario(String id, AgendaGradeAtendimentoDTOParam param) {
        super(id);
        this.param = param;
        init();
    }

    private void init() {

        add(autoCompleteConsultaProfissional = new AutoCompleteConsultaProfissional("profissional", new PropertyModel<Profissional>(this, "profissional")));
        if(profissional != null && profissional.getCodigo() != null) {
            autoCompleteConsultaProfissional.setEnabled(false);
        }
        autoCompleteConsultaProfissional.add(new ConsultaListener<Profissional>() {

            @Override
            public void valueObjectLoaded(AjaxRequestTarget target, Profissional object) {
                carregarAgendaGradeAtendimento(target, object);
            }
        });

        autoCompleteConsultaProfissional.add(new RemoveListener<Profissional>() {

            @Override
            public void valueObjectUnLoaded(AjaxRequestTarget target, Profissional object) {
                carregarAgendaGradeAtendimento(target, null);
            }
        });

        add(txtProfissionalAgenda = new DisabledInputField("profissionalAgenda", new PropertyModel(this, "profissionalAgenda")));
        add(txtUnidadeAgenda = new DisabledInputField("unidadeAgenda", new PropertyModel(this, "unidadeAgenda")));

        add(tblAgendamentoSolicitacao = new MarcacaoAgendamentoHorarioTableColor("tblAgendamentoSolicitacao", getColumns(), getCollectionProvider()));
        tblAgendamentoSolicitacao.populate();
        tblAgendamentoSolicitacao.setScrollY("150px");
        tblAgendamentoSolicitacao.addSelectionAction(new ISelectionAction<AgendaGradeAtendimentoDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, AgendaGradeAtendimentoDTO object) {
                if (object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional() != null) {
                    profissionalAgenda = object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getNome();
                    target.add(txtProfissionalAgenda);
                }
                if (object.getEmpresa() != null) {
                    unidadeAgenda = object.getEmpresa().getDescricao();
                    target.add(txtUnidadeAgenda);
                }

                try {
                    selecionarAgenda(target, object);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }

                carregarRecomendacoes(target, object.getAgendaGradeAtendimento().getAgendaGrade().getAgenda());
            }
        });

        add(tblAgendamentos = new Table("tblAgendamentos", getColumnsAgenda(), getCollectionProviderAgenda()));
        tblAgendamentos.populate();
        tblAgendamentos.setScrollY("150px");

        add(buildFormBtnAgenda());

        carregarAgendaGradeAtendimento(null, null);
        verificarComparecimento();
        carregarOrientacaoDocumentoRetido();
    }

    private void carregarOrientacaoDocumentoRetido() {
        TipoProcedimento tp = LoadManager.getInstance(TipoProcedimento.class)
                .addProperty(TipoProcedimento.PROP_CODIGO)
                .addProperty(TipoProcedimento.PROP_DESCRICAO)
                .addProperty(TipoProcedimento.PROP_FLAG_DOCUMENTO_RETIDO)
                .addParameter(new QueryCustom.QueryCustomParameter(TipoProcedimento.PROP_CODIGO, getParam().getTipoProcedimento().getCodigo()))
                .start().getVO();
        if (RepositoryComponentDefault.SIM_LONG.equals(tp.getFlagDocumentoRetido())) {
            warn(bundle("msgDocumentoDeveFicarRetidoEstabelecimento"));
        }
    }

    private AbstractAjaxButton getBtnAgendar() {
        return new AbstractAjaxButton("btnAgendar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (CollectionUtils.isEmpty(agendarList)) {
                    throw new ValidacaoException(BundleManager.getString("msgSelecionePeloMenosUmaAgendaParaAgendar"));
                }

                if (dlgConfirmacaoSimNao == null) {
                    AgendaGradeAtendimentoDTOParam param = getParam();

                    WindowUtil.addModal(target, this,
                            dlgConfirmacaoSimNao = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this), Bundle.getStringApplication("msg_confirma_agendamento_itens_selecionados_atendimento_X", param.getTipoProcedimento().getDescricao())) {
                                @Override
                                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                                    agendarActionSelecionados(target);
                                }
                            });
                }
                dlgConfirmacaoSimNao.show(target);
            }
        };
    }

    private void agendarActionSelecionados(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        AgendaGradeAtendimentoDTOParam param = getParam();
        if (CollectionUtils.isNotNullEmpty(agendarList)) {
            for (AgendaGradeAtendimentoDTO aga : agendarList) {
                aga.setUsuarioCadsus(param.getUsuarioCadsus());
                aga.setNomePaciente(param.getUsuarioCadsus().getNomeSocial());
                aga.setProcedimento(param.getSolicitacaoAgendamento().getProcedimento());
                aga.setSolicitacaoAgendamento(param.getSolicitacaoAgendamento());
                aga.setEmpresaAgenda(getParam().getEmpresaAgenda());
                aga.setExameProcedimentoList(getParam().getExameProcedimentoList());
                if (br.com.celk.util.CollectionUtils.isNotNullEmpty(this.param.getExameProcedimentoList())) {
                    aga.setQuantidadeVagasOcupadas(calcularVagasExames());
                    aga.getSolicitacaoAgendamento().setValidarExisteOutraSolicitacao(false);
                }
                if (loteSolicitacaoAgendamentoItem != null) {
                    aga.setLoteSolicitacaoAgendamentoItem(loteSolicitacaoAgendamentoItem);
                }
            }
            List<AgendaGradeAtendimentoHorario> agahList = BOFactoryWicket.getBO(AgendamentoFacade.class).registrarAgendamentosSelecionados(
                    agendarList, getParam().getEmpresaOrigem(),
                    param.getUsuarioCadsus(),
                    param.getSolicitacaoAgendamento().getProcedimento(), null, null);
            finalizarAcaoAgendamento(target, agahList);
        }
    }

    private void viewDlgImpressao(AjaxRequestTarget target, final List<AgendaGradeAtendimentoHorario> agahList) {
        if (dlgRecomendacoesAgenda == null) {
            WindowUtil.addModal(target, this, dlgRecomendacoesAgenda = new DlgRecomendacoesAgenda(WindowUtil.newModalId(this)) {
                @Override
                public DataReport onImprimir() throws ReportException {
                    return imprimir(agahList);
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    closeAgendamentoAction(target);
                }
            });
        }

        dlgRecomendacoesAgenda.show(target, agahList, true);

    }

    private DataReport imprimir(List<AgendaGradeAtendimentoHorario> agahList) throws ReportException {
        RelatorioImprimirComprovanteAgendamentoDTOParam paramRelatorio = new RelatorioImprimirComprovanteAgendamentoDTOParam();
        paramRelatorio.setAgendaGradeAtendimentoHorarioList(agahList);
        return BOFactoryWicket.getBO(AtendimentoReportFacade.class).relatorioImprimirComprovanteAgendamentoSemSolicitacao(paramRelatorio);
    }

    public abstract void closeAgendamentoAction(AjaxRequestTarget target);

    private void verificarComparecimento() {
        List<AgendaGradeAtendimentoHorario> agahList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperties(new HQLProperties(AgendaGradeAtendimentoHorario.class).getProperties())
                .addProperties(new HQLProperties(Empresa.class, AgendaGradeAtendimentoHorario.PROP_LOCAL_AGENDAMENTO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, param.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_TIPO_PROCEDIMENTO, param.getTipoProcedimento()))
                .addSorter(new QueryCustom.QueryCustomSorter(AgendaGradeAtendimentoHorario.PROP_CODIGO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();

        if (CollectionUtils.isNotNullEmpty(agahList)) {
            for (AgendaGradeAtendimentoHorario agah : agahList) {
                if (AgendaGradeAtendimentoHorario.STATUS_NAO_COMPARECEU.equals(agah.getStatus())) {
                    warn(bundle("msgPacienteNaoCompareceuUltimoAgendamentoRealizadoParaEsteTipoDataAgendamentoXLocalX",
                            agah.getDataHoraAgendamentoFormatado(), agah.getLocalAgendamento().getDescricao()));
                }
                break;
            }
        }
    }

    public List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        AgendaGradeAtendimentoDTO proxy = on(AgendaGradeAtendimentoDTO.class);

        if (getParam().getTipoProcedimento().habilitaAgendamentoGrupo()) {
            columns.add(getSelectionActionColumn());
        }

        columns.add(getActionColumn());

        columns.add(new DateColumn(bundle("dia"), path(proxy.getAgendaGradeAtendimento().getAgendaGrade().getData())).setPattern("dd/MM"));
        columns.add(createColumn(bundle("semana"), proxy.getDiaSemanaAbv()));
        columns.add(createColumn(bundle("tipo"), proxy.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao()));
        columns.add(createColumn(bundle("inicio"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoHoraInicial()));
        columns.add(createColumn(bundle("vagas"), proxy.getVagasDisponiveis()));
        columns.add(createColumn(bundle("profissional"), proxy.getAgendaGradeAtendimento().getAgendaGrade().getAgenda().getProfissional().getNome()));

        return columns;
    }

    private IColumn getSelectionActionColumn() {
        return new CustomColumn<AgendaGradeAtendimentoDTO>() {
            @Override
            public Component getComponent(String componentId, final AgendaGradeAtendimentoDTO rowObject) {
                return new MarcacaoAgendamentoColumnPanel(componentId, rowObject.getTipoProcedimento().habilitaAgendamentoGrupo(), rowObject.getVagasDisponiveis()) {
                    @Override
                    public void onSelectionAction(AjaxRequestTarget target, boolean selectionAction) {
                        if (selectionAction) {
                            agendarList.add(rowObject);
                        } else {
                            agendarList.remove(rowObject);
                        }

                        carregarRecomendacoes(target, rowObject.getAgendaGradeAtendimento().getAgendaGrade().getAgenda());
                    }
                };
            }
        };
    }

    private IColumn getActionColumn() {
        return new MultipleActionCustomColumn<AgendaGradeAtendimentoDTO>() {
            @Override
            public void customizeColumn(AgendaGradeAtendimentoDTO rowObject) {
                ModelActionLinkPanel alp = addAction(ActionType.AGENDAR, rowObject, new IModelAction<AgendaGradeAtendimentoDTO>() {
                    @Override
                    public void action(AjaxRequestTarget target, AgendaGradeAtendimentoDTO modelObject) throws ValidacaoException, DAOException {
                        if (AgendamentoHelper.exibirRecomendacoes(modelObject.getAgendaGradeAtendimento().getAgendaGrade().getAgenda())) {
                            initDlgRecomendacoesAgendaAntesConfirmar(target, modelObject);
                        } else {
                            agendarAction(target, modelObject);
                        }
                    }
                });
                alp.setTitleBundleKey("agendar");
                alp.setQuestionDialogBundleKey(AgendamentoHelper.exibirRecomendacoes(rowObject.getAgendaGradeAtendimento().getAgendaGrade().getAgenda()) ? null : "msgConfirmarAgendamento_X",
                        rowObject.getTipoProcedimento().getDescricao(),
                        rowObject.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial(),
                        rowObject.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao());
                alp.setEnabled(rowObject.getVagasDisponiveis() > 0L);
            }
        };
    }

    private void agendarAction(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws DAOException, ValidacaoException {
        AgendaGradeAtendimentoDTOParam param = getParam();
        dto.setUsuarioCadsus(param.getUsuarioCadsus());
        dto.setNomePaciente(param.getUsuarioCadsus().getNomeSocial());
        dto.setProcedimento(param.getSolicitacaoAgendamento().getProcedimento());
        dto.setSolicitacaoAgendamento(param.getSolicitacaoAgendamento());
        dto.setExameProcedimentoList(param.getExameProcedimentoList());
        if (br.com.celk.util.CollectionUtils.isNotNullEmpty(dto.getExameProcedimentoList())) {
            dto.setQuantidadeVagasOcupadas(calcularVagasExames());
            dto.getSolicitacaoAgendamento().setValidarExisteOutraSolicitacao(false);
            dto.setValidarAgendamentoExistente(false);
        }

        dto.setEmpresaAgenda(getParam().getEmpresaAgenda());

        if (loteSolicitacaoAgendamentoItem != null) {
            dto.setLoteSolicitacaoAgendamentoItem(loteSolicitacaoAgendamentoItem);
        }

        BOFactory.getBO(SolicitacaoAgendamentoFacade.class).validarSolicitacaoAgendada(dto.getSolicitacaoAgendamento());
        AgendaGradeAtendimentoHorario agah = BOFactoryWicket.getBO(AgendamentoFacade.class).registrarAgendamento(dto, getParam().getEmpresaOrigem(), getParam().getEmpresaAgenda());

        List<AgendaGradeAtendimentoHorario> agahList = new ArrayList<>();
        agahList.add(agah);

        finalizarAcaoAgendamento(target, agahList);

    }

    private void finalizarAcaoAgendamento(AjaxRequestTarget target, List<AgendaGradeAtendimentoHorario> agahList) throws ValidacaoException, DAOException {
        for (AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario : agahList) {
            agendaGradeAtendimentoHorario.setFlagNotificado(RepositoryComponentDefault.SIM_LONG);
            BOFactoryWicket.save(agendaGradeAtendimentoHorario);
        }

        SolicitacaoAgendamento solicitacaoAgendamento = agahList.get(0).getSolicitacaoAgendamento();
        BOFactoryWicket.getBO(AgendamentoFacade.class).confirmarContatoSolicitacaoAgendamento(solicitacaoAgendamento.getCodigo(), " Usuário: " + SessaoAplicacaoImp.getInstance().getUsuario().getNome() + " Data: " + Data.formatarDataHora(DataUtil.getDataAtual()) + " via Programa 825");
        viewDlgImpressao(target, agahList);
    }

    private Long calcularVagasExames() {
        Long vagas = ((Integer) param.getExameProcedimentoList().size()).longValue();
        vagas = vagas / param.getTipoProcedimento().getProcedimentoVaga();
        Long resto = ((Integer) param.getExameProcedimentoList().size()).longValue() % param.getTipoProcedimento().getProcedimentoVaga();
        if (resto != 0) {
            vagas++;
        }
        return vagas;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return agendaGradeAtendimentoDTOList;
            }
        };
    }

    private void carregarAgendaGradeAtendimento(AjaxRequestTarget target, Profissional profissional) {
        agendamentosList = new ArrayList<>();

        param.setValidarUnidadeInformatizada(true);
        param.setValidarEmpresaOrigem(true);
        if(param.isSomenteVagaInterna()){
            param.setTipoAtendimentoAgendaList(Arrays.asList(TipoAtendimentoAgenda.TIPO_INTERNA));
        }

        param.setApenasAgendasComVagas(false);
        param.setProfissional(profissional);

        try {
            agendaGradeAtendimentoDTOList = BOFactoryWicket.getBO(AgendamentoFacade.class).consultarVagasDisponiveisAgendaExame(param);
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (ValidacaoException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        if (target != null) {
            tblAgendamentoSolicitacao.update(target);
            tblAgendamentos.update(target);
        }
    }

    private void selecionarAgenda(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
        carregarAgenda(dto);
        if (target != null) {
            tblAgendamentos.populate(target);
            tblAgendamentos.update(target);
        } else {
            tblAgendamentos.populate();
        }
    }

    private void carregarAgenda(AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
        agendamentosList = new ArrayList<>();

        List<AgendaGradeAtendimentoHorario> agahList = LoadManager.getInstance(AgendaGradeAtendimentoHorario.class)
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_APELIDO))
                .addProperty(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_UTILIZA_NOME_SOCIAL))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_AGENDA_GRADE_ATENDIMENTO, dto.getAgendaGradeAtendimento()))
                .addParameter(new QueryCustom.QueryCustomParameter(AgendaGradeAtendimentoHorario.PROP_STATUS, BuilderQueryCustom.QueryParameter.DIFERENTE, AgendaGradeAtendimentoHorario.STATUS_CANCELADO))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(AgendaGradeAtendimentoHorario.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_NOME), BuilderQueryCustom.QuerySorter.CRESCENTE))
                .start().getList();

        for (AgendaGradeAtendimentoHorario agah : agahList) {
            AgendaGradeAtendimentoPacienteDTO agendaGradeAtendimentoPacienteDTO = new AgendaGradeAtendimentoPacienteDTO();
            agendaGradeAtendimentoPacienteDTO.setNomePaciente(agah.getUsuarioCadsus().getNomeSocial());

            agendamentosList.add(agendaGradeAtendimentoPacienteDTO);
        }
    }

    public List<IColumn> getColumnsAgenda() {
        List<IColumn> columns = new ArrayList<>();
        AgendaGradeAtendimentoPacienteDTO proxy = on(AgendaGradeAtendimentoPacienteDTO.class);

        columns.add(createColumn(bundle("paciente"), proxy.getNomePaciente()));

        return columns;
    }

    public ICollectionProvider getCollectionProviderAgenda() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object o) throws DAOException, ValidacaoException {
                return agendamentosList;
            }
        };
    }

    private void carregarRecomendacoes(AjaxRequestTarget target, Agenda agenda) {
        clearNotifications(target);
        if (AgendamentoHelper.exibirRecomendacoes(agenda)) {
            info(target, bundle("recomendacoesX", agenda.getRecomendacoes()));
        }
    }

    private void initDlgRecomendacoesAgendaAntesConfirmar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) {
        if (dlgRecomendacoesAgendaAntesConfirmar == null) {
            WindowUtil.addModal(target, this, dlgRecomendacoesAgendaAntesConfirmar = new DlgRecomendacoesAgendaAntesConfirmar(WindowUtil.newModalId(this)) {

                @Override
                public void onFechar(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) throws ValidacaoException, DAOException {
                    initDlgSimNao(target, dto);
                }
            });
        }

        dlgRecomendacoesAgendaAntesConfirmar.show(target, dto);
    }

    public void initDlgSimNao(AjaxRequestTarget target, AgendaGradeAtendimentoDTO dto) {
        if (dlgConfirmacaoSimNaoRecomendacoes == null) {
            WindowUtil.addModal(target, this, dlgConfirmacaoSimNaoRecomendacoes = new DlgConfirmacaoSimNao(WindowUtil.newModalId(this),
                    bundle("msgConfirmarAgendamento_X", dto.getTipoProcedimento().getDescricao(),
                            dto.getAgendaGradeAtendimento().getAgendaGrade().getDescricaoDataHoraInicial(),
                            dto.getAgendaGradeAtendimento().getTipoAtendimentoAgenda().getDescricao())) {
                @Override
                public void onConfirmar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                    agendarAction(target, (AgendaGradeAtendimentoDTO) getObject());
                }

                @Override
                public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                }
            });
        }
        dlgConfirmacaoSimNaoRecomendacoes.setObject(dto);
        dlgConfirmacaoSimNaoRecomendacoes.show(target);
    }

    public AgendaGradeAtendimentoDTOParam getParam() {
        if (param != null) {
            param.setProfissional(profissional);
        }
        return param;
    }

    private Form buildFormBtnAgenda() {
        Form form = new Form("formBtnAgendar");
        AbstractAjaxButton btnAgendar = getBtnAgendar();
        btnAgendar.setOutputMarkupPlaceholderTag(true);
        btnAgendar.setVisible(getParam().getTipoProcedimento().habilitaAgendamentoGrupo());
        form.add(btnAgendar);
        return form;
    }
}