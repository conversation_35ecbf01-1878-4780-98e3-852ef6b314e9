package br.com.celk.component.consulta.configurator.autocomplete;

import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import com.google.gson.Gson;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public abstract class AbstractAutoCompleteSettings<T extends PesquisaObjectInterface> implements Serializable, IAutoCompleteSettings<T> {

    @Override
    public String getResultsFormatter() {
        StringBuilder builder = new StringBuilder();
        builder.append("<li>");
            builder.append("<div style=\"display: inline-block; padding-left: 10px;\">");
                builder.append("<div class=\"nivel-1\"> '+item.name+' </div>");
            builder.append("</div>");
        builder.append("</li>");
        
        return builder.toString();
    }

    @Override
    public String getTokenFormatter() {
        return null;
    }

    @Override
    public final String getJsonForOptions(List<T> options) {
        List<Object> optionList = new ArrayList<Object>(options.size());
        for (T o : options) {
            Map<String, String> jsonMap = new HashMap<String, String>();
            for (Map.Entry<String, String> entry : getJsonPropertyMap(o).entrySet()) {
                jsonMap.put(entry.getKey(), entry.getValue());
            }
            optionList.add(jsonMap);
        }

        Gson json = new Gson();
        return json.toJson(optionList);
    }

    public abstract Map<String, String> getJsonPropertyMap(T o);
}
