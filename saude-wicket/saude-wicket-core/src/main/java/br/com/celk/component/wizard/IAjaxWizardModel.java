package br.com.celk.component.wizard;

import java.util.Iterator;

import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.util.io.IClusterable;

public interface IAjaxWizardModel extends IClusterable {

    void cancel(AjaxRequestTarget target);

    void finish(AjaxRequestTarget target);

    void last(AjaxRequestTarget target);

    void next(AjaxRequestTarget target);

    void previous(AjaxRequestTarget target);
    
    void reset();
    
    void reset(AjaxRequestTarget target);

    IAjaxWizardStep getActiveStep();

    boolean isCancelVisible();

    boolean isLastAvailable();

    boolean isLastStep(IAjaxWizardStep step);

    boolean isLastVisible();

    boolean isNextAvailable();

    boolean isPreviousAvailable();

    void addListener(IAjaxWizardModelListener listener);

    void removeListener(IAjaxWizardModelListener listener);

    Iterator<IAjaxWizardStep> stepIterator();
}
