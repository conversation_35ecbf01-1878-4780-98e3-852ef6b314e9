package br.com.celk.view.atendimento.prontuario.panel.prescricaointerna.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.util.MessageUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.prontuario.enfermagem.interfaces.dto.ImpressaoReceituarioDTOParam;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgImpressaoPrescricaoInternaReceitaControlada extends Window {

    private PnlImpressaoPrescricaoInternaReceitaControlada pnlImpressao;

    public DlgImpressaoPrescricaoInternaReceitaControlada(String id) {
        super(id);
        init();
    }

    private void init() {
        setInitialHeight(60);
        setInitialWidth(500);
        setResizable(false);

        setTitle(BundleManager.getString("visualizarImpressao"));

        setContent(pnlImpressao = new PnlImpressaoPrescricaoInternaReceitaControlada(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgImpressaoPrescricaoInternaReceitaControlada.this.onFechar(target);
                close(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                try {
                    DlgImpressaoPrescricaoInternaReceitaControlada.this.onFechar(target);
                } catch (ValidacaoException ex) {
                    MessageUtil.modalWarn(target, DlgImpressaoPrescricaoInternaReceitaControlada.this, ex);
                    return false;
                } catch (DAOException ex) {
                    MessageUtil.modalError(target, DlgImpressaoPrescricaoInternaReceitaControlada.this, ex);
                    return false;
                }
                return true;
            }
        });
    }

    public void show(AjaxRequestTarget target, ImpressaoReceituarioDTOParam param) {
        super.show(target);
        pnlImpressao.setParam(param);
    }

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }
}
