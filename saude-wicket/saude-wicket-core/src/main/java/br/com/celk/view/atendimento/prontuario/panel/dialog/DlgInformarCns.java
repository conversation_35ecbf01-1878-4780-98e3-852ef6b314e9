package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import com.amazonaws.util.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarCns extends Window {

    private PnlInformarCns pnlInformarCns;

    public DlgInformarCns(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("informarCns"));
        setInitialWidth(620);
        setInitialHeight(180);
        setResizable(true);

        setContent(pnlInformarCns = new PnlInformarCns(getContentId()) {
            @Override
            public void onConfirmar(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus, String cns, String cpf) throws DAOException, ValidacaoException {
                close(target);
                DlgInformarCns.this.onConfirmar(target, usuarioCadsus, cns, cpf);
            }

            @Override
            public void onFechar(AjaxRequestTarget target) {
                close(target);
            }

            @Override
            public void onContinuar(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException {
                close(target);
                DlgInformarCns.this.onContinuar(target, usuarioCadsus);
            }
        });
    }

    public void show(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        super.show(target);

        if(StringUtils.isNullOrEmpty(usuarioCadsus.getCpfSemPontos())){
            this.pnlInformarCns.setUsuarioCadsus(target, usuarioCadsus);
        }
    }

    public abstract void onContinuar(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) throws DAOException, ValidacaoException;

    public abstract void onConfirmar(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus, String cns, String cpf) throws DAOException, ValidacaoException;
}
