package br.com.celk.view.cadsus.usuariocadsus.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.inputfield.upper.UpperField;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.StringUtil;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ServicoCnsUsuarioCadsusDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.ServicoCnsUsuarioCadsusDTOParam;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusEnderecoWSDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.dto.UsuarioCadsusWSDTO;
import br.com.ksisolucoes.bo.cadsus.interfaces.facade.UsuarioCadsusFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.CnsValidator;
import br.com.ksisolucoes.util.validacao.CpfCnpJValidator;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.commons.lang.StringUtils;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * Created by sulivan on 10/11/17.
 */
public abstract class PnlServicoCnsUsuarioCadsus extends Panel {

    private Form<ServicoCnsUsuarioCadsusDTOParam> form;

    private SelectionTable table;
    private List<ServicoCnsUsuarioCadsusDTO> result;

    private InputField txtNome;
    private InputField txtNomeMae;
    private InputField txtCpf;
    private InputField txtRg;
    private InputField txtCns;
    private InputField txtDataNascimento;

    private AbstractAjaxButton btnFechar;

    public PnlServicoCnsUsuarioCadsus(String id) {
        super(id);
        init();
    }

    private void init() {
        ServicoCnsUsuarioCadsusDTOParam proxy = on(ServicoCnsUsuarioCadsusDTOParam.class);

        form = new Form("form", new CompoundPropertyModel(new ServicoCnsUsuarioCadsusDTOParam()));

        form.add(txtNome = new UpperField(path(proxy.getNomePaciente())));
        form.add(txtCns = new InputField(path(proxy.getCns())));
        form.add(txtCpf = new InputField(path(proxy.getCpf())));
        form.add(txtRg = new InputField(path(proxy.getRg())));
        form.add(txtDataNascimento = new InputField(path(proxy.getDataNascimento())));
        form.add(txtNomeMae = new UpperField(path(proxy.getNomeMae())));

        form.add(table = new SelectionTable("selectionTable", getColumns(), getCollectionProvider()));
        table.setScrollY("280px");
        table.addSelectionAction(new ISelectionAction<ServicoCnsUsuarioCadsusDTO>() {
            @Override
            public void onSelection(AjaxRequestTarget target, ServicoCnsUsuarioCadsusDTO dto) {
                try {
                    onSelectionTable(target, dto);
                } catch (DAOException | ValidacaoException e) {
                    MessageUtil.modalWarn(target, PnlServicoCnsUsuarioCadsus.this, e);
                }
            }
        });

        form.add(new AbstractAjaxButton("btnProcurar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {
                procurar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(form);

        setOutputMarkupPlaceholderTag(true);
    }

    private void procurar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
        ServicoCnsUsuarioCadsusDTOParam param = form.getModelObject();
        validarFiltros(param);

        result = new ArrayList();
        table.populate(target);

        result = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).usuariosCadsusWS(param);
        table.populate(target);
    }

    private void validarFiltros(ServicoCnsUsuarioCadsusDTOParam param) throws ValidacaoException {
        if (StringUtils.trimToNull(param.getCns()) != null && !CnsValidator.validaCns(StringUtil.getDigits(param.getCns()))) {
            throw new ValidacaoException(bundle("msgCNSInformadoInvalido"));
        }

        if (StringUtils.trimToNull(param.getCpf()) != null && !CpfCnpJValidator.CPFIsValid(StringUtil.getDigits(param.getCpf()))) {
            throw new ValidacaoException(bundle("msgCPFInformadoInvalido"));
        }

        boolean docsIsNull = StringUtils.trimToNull(param.getCns()) == null && StringUtils.trimToNull(param.getCpf()) == null && StringUtils.trimToNull(param.getRg()) == null;
        if (docsIsNull) {
            if (StringUtils.trimToNull(param.getNomePaciente()) == null) {
                throw new ValidacaoException(bundle("msgRefineBuscaInformandoFiltrosObrigatoriosNomeCnsCpfRg"));
            } else if (StringUtils.trimToNull(param.getNomePaciente()).length() < 3) {
                throw new ValidacaoException(bundle("msgDeveInformarTresCaracteresFiltroNome"));
            }
        }
    }

    private void onSelectionTable(AjaxRequestTarget target, ServicoCnsUsuarioCadsusDTO dto) throws ValidacaoException, DAOException {
        UsuarioCadsusWSDTO usuarioCadsusWSDTO = dto.getUsuarioCadsusWSDTO();
        if (usuarioCadsusWSDTO == null) {
            ServicoCnsUsuarioCadsusDTO servicoCnsUsuarioCadsusDTO = BOFactoryWicket.getBO(UsuarioCadsusFacade.class).usuarioCadsusWS(dto.getCns());
            onSelectionTable(target, servicoCnsUsuarioCadsusDTO);
            return;
        }

        onSelected(target, usuarioCadsusWSDTO);
    }

    private void printResultConsole(UsuarioCadsusWSDTO usuarioCadsusWSDTO) {
        String log = "";
        log += ("NOME: " + usuarioCadsusWSDTO.getNomeCompleto()) + "\n";
        log += ("NOME MÃE: " + usuarioCadsusWSDTO.getNomeMae()) + "\n";
        log += ("NOME PAI: " + usuarioCadsusWSDTO.getNomePai()) + "\n";
        log += ("APELIDO: " + usuarioCadsusWSDTO.getNomeSocial()) + "\n";
        log += ("EMAIL: " + usuarioCadsusWSDTO.getEmail()) + "\n";
        log += ("SEXO: " + usuarioCadsusWSDTO.getSexo()) + "\n";
        log += ("DATA NASCIMENTO: " + new SimpleDateFormat("dd/MM/yyyy").format(usuarioCadsusWSDTO.getDataNascimento())) + "\n";
        log += ("CNS: " + usuarioCadsusWSDTO.getCns()) + "\n";
        log += ("CPF: " + usuarioCadsusWSDTO.getCpf()) + "\n";
        log += ("RG: " + usuarioCadsusWSDTO.getRg()) + "\n";

        if (usuarioCadsusWSDTO.getDataFalecimento() != null) {
            log += ("DATA ÓBITO: " + new SimpleDateFormat("dd/MM/yyyy").format(usuarioCadsusWSDTO.getDataFalecimento()));
        }

        log += ("DOADOR ÓRGÃOS: " + (usuarioCadsusWSDTO.getDoadorOrgaos() != null && usuarioCadsusWSDTO.getDoadorOrgaos() ? "SIM" : "NÃO"));

        if (usuarioCadsusWSDTO.getPaisNacionalidade() != null) {
            log += ("NACIONALIDADE: " + usuarioCadsusWSDTO.getPaisNacionalidade().getDescricao());
        }

        if (usuarioCadsusWSDTO.getRaca() != null) {
            log += ("COR/RAÇA: " + usuarioCadsusWSDTO.getRaca().getDescricao());
        }

        if (usuarioCadsusWSDTO.getEstadoCivil() != null) {
            log += ("ESTADO CIVIL: " + usuarioCadsusWSDTO.getEstadoCivil().getDescricao());
        }

        if (usuarioCadsusWSDTO.getEtniaIndigena() != null) {
            log += ("ETNIA: " + usuarioCadsusWSDTO.getEtniaIndigena().getDescricao());
        }

        if (usuarioCadsusWSDTO.getNivelEscolaridade() != null) {
            log += ("GRAU INSTRUÇÃO: " + usuarioCadsusWSDTO.getNivelEscolaridade().descricao());
        }

        log += ("RELIGIÃO: " + usuarioCadsusWSDTO.getCodigoReligiao());

        if (usuarioCadsusWSDTO.getCidadeNaturalidade() != null) {
            log += ("NATURALIDADE(CIDADE): " + usuarioCadsusWSDTO.getCidadeNaturalidade().getDescricao());
        }

        if (usuarioCadsusWSDTO.getPaisNaturalidade() != null) {
            log += ("NATURALIDADE(PAÍS): " + usuarioCadsusWSDTO.getPaisNaturalidade().getDescricao());
        }

        log += ("TELEFONE RESIDENCIAL: " + usuarioCadsusWSDTO.getTelefoneResidencial());
        log += ("TELEFONE CELULAR: " + usuarioCadsusWSDTO.getTelefoneCelular()) + "\n";
        log += ("TELEFONE COMERCIAL: " + usuarioCadsusWSDTO.getTelefoneComercial()) + "\n";
        log += ("TELEFONE CONTATO: " + usuarioCadsusWSDTO.getTelefoneContato()) + "\n";
        log += ("TELEFONE OUTROS: " + usuarioCadsusWSDTO.getTelefoneOutros()) + "\n";

        UsuarioCadsusEnderecoWSDTO enderecoWSDTO = usuarioCadsusWSDTO.getEnderecoDTO();
        if (enderecoWSDTO != null) {
            if (enderecoWSDTO.getTipoLogradouroCadsus() != null) {
                log += ("TIPO LOGRADOURO: " + enderecoWSDTO.getTipoLogradouroCadsus().getDescricao()) + "\n";
            }
            log += ("LOGRADOURO: " + enderecoWSDTO.getLogradouro()) + "\n";
            log += ("NÚMERO: " + enderecoWSDTO.getNumero()) + "\n";
            log += ("BAIRRO: " + enderecoWSDTO.getBairro()) + "\n";
            log += ("CEP: " + enderecoWSDTO.getCep()) + "\n";
            log += ("COMPLEMENTO: " + enderecoWSDTO.getComplemento()) + "\n";
            if (enderecoWSDTO.getCidade() != null) {
                log += ("CIDADE: " + enderecoWSDTO.getCidade().getDescricao()) + "\n";
            }
            log += ("UF: " + enderecoWSDTO.getUf()) + "\n";
            if (enderecoWSDTO.getPais() != null) {
                log += ("PAÍS: " + enderecoWSDTO.getPais().getDescricao()) + "\n";
            }
        }
        Loggable.log.debug(log);
    }

    private List<IColumn> getColumns() {
        List<IColumn> columns = new ArrayList<>();
        ServicoCnsUsuarioCadsusDTO proxy = on(ServicoCnsUsuarioCadsusDTO.class);

        columns.add(createColumn(bundle("numeroCartao"), proxy.getCnsFormatado()));
        columns.add(createColumn(bundle("nome"), proxy.getNome()));
        columns.add(createColumn(bundle("nomeMae"), proxy.getNomeMae()));
        columns.add(createColumn(bundle("dataNascimento"), proxy.getDataNascimento()));
        columns.add(createColumn(bundle("sexo"), proxy.getSexoFormatado()));

        return columns;
    }

    private ICollectionProvider getCollectionProvider() {
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if (result == null) {
                    result = new ArrayList<>();
                }
                return result;
            }
        };
    }

    public void limpar(AjaxRequestTarget target) {
        form.getModel().setObject(new ServicoCnsUsuarioCadsusDTOParam());
        table.clearSelection(target);
        table.limpar(target);
        target.add(this);
    }

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

    public abstract void onSelected(AjaxRequestTarget target, UsuarioCadsusWSDTO dto);
}