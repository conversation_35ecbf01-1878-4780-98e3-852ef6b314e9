package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.system.methods.WicketMethods;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.caps.MotivoDestinoSaida;
import ch.lambdaj.Lambda;
import java.util.List;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlMotivoDestinoSaida extends Panel {

    private DropDown<MotivoDestinoSaida> dropDownDestinoPaciente;
    private TabelaCbo tabelaCbo;
    private MotivoDestinoSaida motivoDestinoSaida;

    public PnlMotivoDestinoSaida(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));
        form.add(getDropDownDestinoPaciente("motivoDestinoSaida"));

        form.add(new AbstractAjaxButton("btnConfirmar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if(motivoDestinoSaida == null){
                    throw new ValidacaoException(WicketMethods.bundle("msgMotivoDestinoObrigatorio"));
                }
                PnlMotivoDestinoSaida.this.onConfirmar(target, motivoDestinoSaida);
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                PnlMotivoDestinoSaida.this.onFechar(target);
            }
        });

        add(form);
    }

    public DropDown getDropDownDestinoPaciente(String id) {
        dropDownDestinoPaciente = new DropDown(id);
        dropDownDestinoPaciente.addAjaxUpdateValue();

        return dropDownDestinoPaciente;
    }

    private List<MotivoDestinoSaida> carregarListaMotivoSaida() {
        LoadManager load = LoadManager.getInstance(MotivoDestinoSaida.class)
                .addSorter(new QueryCustom.QueryCustomSorter(MotivoDestinoSaida.PROP_DESCRICAO))
                .addParameter(new QueryCustom.QueryCustomParameter(MotivoDestinoSaida.PROP_CODIGO_RAAS, QueryCustom.QueryCustomParameter.DIFERENTE, "00"))
                .addProperties(new HQLProperties(MotivoDestinoSaida.class).getProperties());
        if (tabelaCbo != null) {
            load.addInterceptor(new LoadInterceptor() {
                @Override
                public void customHQL(HQLHelper hql, String alias) {
                    hql.addToWhereWhithAnd("NOT EXISTS(SELECT 1 FROM ItemMotivoDestinoSaida item WHERE item.motivoDestinoSaida.codigo = "
                            + alias + ".codigo) OR EXISTS(SELECT 1 FROM ItemMotivoDestinoSaida item WHERE item.motivoDestinoSaida.codigo = "
                            + alias + ".codigo AND item.tabelaCbo.cbo = '" + tabelaCbo.getCbo() + "')");
                    hql.addToOrder(MotivoDestinoSaida.PROP_DESCRICAO);
                }
            });
        }
        List<MotivoDestinoSaida> lst = load.start().getList();
        
        for(MotivoDestinoSaida md : lst){
            if("00".equals(md.getCodigoRaas())){
                lst.remove(md);
                break;
            }
        }

        return lst;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, MotivoDestinoSaida mds) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target);

    public void setTabelaCbo(AjaxRequestTarget target, TabelaCbo tabelaCbo) {
        this.tabelaCbo = tabelaCbo;
        dropDownDestinoPaciente.limpar(target);
        recarregarDropDown(target);
    }

    public void recarregarDropDown(AjaxRequestTarget target) {
        List<MotivoDestinoSaida> lst = carregarListaMotivoSaida();
        
        dropDownDestinoPaciente.addChoice(null, "");
        
        for (MotivoDestinoSaida mds : lst) {
            dropDownDestinoPaciente.addChoice(mds, mds.getDescricao());
        }
        target.add(dropDownDestinoPaciente);
    }
}
