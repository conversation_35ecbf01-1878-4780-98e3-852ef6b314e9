package br.com.celk.component.action.link;

import br.com.celk.component.action.IHtmlReportAction;
import br.com.celk.component.action.IHtmlReportActionPanel;
import br.com.celk.component.link.HtmlAjaxReportLink;
import br.com.celk.report.HtmlReport;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.link.AbstractLink;
import org.apache.wicket.model.Model;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class HtmlReportActionLinkPanel extends AbstractActionLinkPanel implements IHtmlReportActionPanel{
    
    private IHtmlReportAction iColumnAction;

    public HtmlReportActionLinkPanel(String id, ActionType actionType) {
        super(id, actionType);
    }
    
    @Override
    public void setAction(IHtmlReportAction iColumnAction) {
        this.iColumnAction = iColumnAction;
    }

    @Override
    public void setModelObject(Serializable modelObject) {
        setDefaultModel(new Model(modelObject));
    }

    @Override
    protected AbstractLink newLink(String id) {
        return new HtmlAjaxReportLink(id) {

            @Override
            public HtmlReport getReport(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                if(iColumnAction == null){
                    throw new UnsupportedOperationException(BundleManager.getString("acaoNaoImplementada"));
                }
                return iColumnAction.action(target, HtmlReportActionLinkPanel.this.getDefaultModel().getObject());
            }
        };
    }

    @Override
    public void action(AjaxRequestTarget target) throws ValidacaoException, DAOException {}

}
