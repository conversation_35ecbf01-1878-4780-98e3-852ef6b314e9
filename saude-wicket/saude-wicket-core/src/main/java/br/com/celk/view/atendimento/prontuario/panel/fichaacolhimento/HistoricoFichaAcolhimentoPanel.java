package br.com.celk.view.atendimento.prontuario.panel.fichaacolhimento;

import br.com.celk.component.action.IReportAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.view.atendimento.prontuario.panel.FichaAcolhimentoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.DefaultProntuarioPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioHistoricoPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.FichaAcolhimento;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import static br.com.celk.system.methods.WicketMethods.*;
import br.com.celk.view.atendimento.prontuario.panel.ConsultaFichaAcolhimentoPanel;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.TipoEncaminhamento;
import static ch.lambdaj.Lambda.*;

/**
 *
 * <AUTHOR>
 */
public class HistoricoFichaAcolhimentoPanel extends ProntuarioHistoricoPanel{

    public HistoricoFichaAcolhimentoPanel(String id) {
        super(id, BundleManager.getString("fichaAcolhimento"));
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Table tblHistorico;
        add(tblHistorico = new Table("tblHistorico", getColumns(), getCollectionProvider()));
        tblHistorico.populate();
    }
    
    private List<IColumn> getColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        FichaAcolhimento on = on(FichaAcolhimento.class);
        
        columns.add(getActionColumn());
        columns.add(createColumn(bundle("dataAdmissao"), on.getDataAdmissao()));
        columns.add(createColumn(bundle("tipoEncaminhamento"), on.getTipoEncaminhamento().getDescricao()));
        columns.add(createColumn(bundle("profissional"), on.getAtendimento().getProfissional().getNome()));
        columns.add(createColumn(bundle("cid"), on.getCid().getCodigo()));
        
        return columns;
    }
    
    private IColumn getActionColumn(){
        return new MultipleActionCustomColumn<FichaAcolhimento>() {

            @Override
            public void customizeColumn(FichaAcolhimento rowObject) {
                addAction(ActionType.IMPRIMIR, rowObject, new IReportAction<FichaAcolhimento>() {

                    @Override
                    public DataReport action(FichaAcolhimento modelObject) throws ReportException {
                        return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoFichaAcolhimento(modelObject);
                    }
                });
            }
        };
    }
    
    private ICollectionProvider getCollectionProvider(){
        return new CollectionProvider() {

            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return LoadManager.getInstance(FichaAcolhimento.class)
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_DATA_ADMISSAO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_PROFISSIONAL, Profissional.PROP_NOME))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_CID, Cid.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_CID, Cid.PROP_DESCRICAO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_CODIGO))
                        .addProperty(VOUtils.montarPath(FichaAcolhimento.PROP_TIPO_ENCAMINHAMENTO, TipoEncaminhamento.PROP_DESCRICAO))
                        .addParameter(new QueryCustom.QueryCustomParameter(FichaAcolhimento.PROP_ATENDIMENTO, BuilderQueryCustom.QueryParameter.DIFERENTE, getAtendimento()))
                        .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FichaAcolhimento.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                        .addSorter(new QueryCustom.QueryCustomSorter(FichaAcolhimento.PROP_DATA_ADMISSAO, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                        .start().getList();
            }
        };
    }

    @Override
    public DefaultProntuarioPanel newProntuarioPanel(String id) {
        return new ConsultaFichaAcolhimentoPanel(id);
    }

}
