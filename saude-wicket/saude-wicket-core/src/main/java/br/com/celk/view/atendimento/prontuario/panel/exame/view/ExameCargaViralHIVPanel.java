package br.com.celk.view.atendimento.prontuario.panel.exame.view;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dialog.DlgImpressaoObject;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.util.DataUtil;
import br.com.celk.view.atendimento.prontuario.panel.RegistroEspecializadoPanel;
import br.com.celk.view.atendimento.prontuario.panel.SoapPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoExamesPanel;
import br.com.celk.view.atendimento.prontuario.panel.SolicitacaoLacenPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.prontuario.basico.cid.autocomplete.AutoCompleteConsultaCid;
import br.com.ksisolucoes.agendamento.exame.dto.ExameCadastroAprovacaoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.ExameProcedimentoDTO;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.SoapDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.ExameFacade;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.prontuario.exame.interfaces.dto.ImpressaoRequisicaoCargaViralHIVDTOParam;
import br.com.ksisolucoes.report.prontuario.interfaces.facade.ProntuarioReportFacade;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.RequisicaoCargaViralHIV;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR> Daros
 */
public class ExameCargaViralHIVPanel extends ProntuarioCadastroPanel {

    private Form<RequisicaoCargaViralHIV> form;

    private DropDown dropDownMotivoSolicitacao;
    private AutoCompleteConsultaCid autoCompleteConsultaCid;
    private AbstractAjaxButton btnSalvar;
    private DlgImpressaoObject<Long> dlgConfirmacaoImpressao;

    private TipoExame tipoExame;
    private Exame exame;
    private InputField<String> inputDiagnostico;
    private SoapDTO.ContainerTelaSoap containerTelaSoap;
    private Long flagOrigem;

    public ExameCargaViralHIVPanel(String id, Exame exame, TipoExame tipoExame, Long flagOrigem) {
        super(id, bundle("quantificacaoAcicoNucleicoCargaViralHiv"));
        this.exame = exame;
        this.tipoExame = tipoExame;
        this.flagOrigem = flagOrigem;
    }

    public ExameCargaViralHIVPanel(String id, Exame exame, TipoExame tipoExame, Long flagOrigem, SoapDTO.ContainerTelaSoap containerTelaSoap) {
        super(id, bundle("quantificacaoAcicoNucleicoCargaViralHiv"));
        this.exame = exame;
        this.tipoExame = tipoExame;
        this.flagOrigem = flagOrigem;
        this.containerTelaSoap = containerTelaSoap;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        RequisicaoCargaViralHIV proxy = on(RequisicaoCargaViralHIV.class);

        getForm().add(dropDownMotivoSolicitacao = DropDownUtil.getIEnumDropDown(path(proxy.getMotivoSolicitacao()), RequisicaoCargaViralHIV.MotivoSolicitacao.values()));
        dropDownMotivoSolicitacao.setOutputMarkupId(true);
        dropDownMotivoSolicitacao.addAjaxUpdateValue();

        getForm().add(inputDiagnostico =  new InputField<String>(path(proxy.getDiagnostico())));
        getForm().add(autoCompleteConsultaCid = new AutoCompleteConsultaCid(path(proxy.getCid())));

        getForm().add(btnSalvar = new AbstractAjaxButton("btnSalvar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                salvarExameCargaViralHIV(target);
            }
        });

        getForm().add(new AbstractAjaxButton("btnVoltar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO));
            }

            @Override
            public boolean isVisible() {
                return SoapDTO.ContainerTelaSoap.CONTAINER_ACOES_PLANO.equals(containerTelaSoap);
            }
        }.setDefaultFormProcessing(false));
        add(getForm());

        if (this.tipoExame == null) {
            this.tipoExame = new TipoExame();
        } else {
            carregarRequisicaoCargaViralHIV();
        }
    }

    private Form<RequisicaoCargaViralHIV> getForm() {
        if (this.form == null) {
            this.form = new Form<>("form", new CompoundPropertyModel<>(new RequisicaoCargaViralHIV()));
        }

        return this.form;
    }

    private void salvarExameCargaViralHIV(AjaxRequestTarget target) throws DAOException, ValidacaoException {
        if (tipoExame.getExameProcedimentoPadrao() == null) {
            throw new ValidacaoException(bundle("tipoExameSemProcedimentoPadrao", this));
        }

        RequisicaoCargaViralHIV requisicaoCargaViralHIV = getForm().getModel().getObject();

        if(requisicaoCargaViralHIV.getCid() == null) {
            throw new ValidacaoException(bundle("informeCid"));
        }

        requisicaoCargaViralHIV.setDataCadastro(DataUtil.getDataAtual());

        ExameCadastroAprovacaoDTO dto = new ExameCadastroAprovacaoDTO();
        if (this.exame != null) {
            dto.setCodigoExameCadastrado(this.exame.getCodigo());
            dto.setDataSolicitacao(this.exame.getDataSolicitacao());
            dto.setAtendimento(this.exame.getAtendimento());
            dto.setCodigoUnidade(this.exame.getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(this.exame.getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(this.exame.getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(this.exame.getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(this.exame.getAtendimento().getUsuarioCadsus().getNomeSocial());
        } else {
            dto.setDataSolicitacao(DataUtil.getDataAtual());
            dto.setAtendimento(getAtendimento());
            dto.setCodigoUnidade(getAtendimento().getEmpresa().getCodigo());
            dto.setCodigoProfissional(getAtendimento().getProfissional().getCodigo());
            dto.setNomeProfissional(getAtendimento().getProfissional().getNome());
            dto.setCodigoPaciente(getAtendimento().getUsuarioCadsus().getCodigo());
            dto.setNomePaciente(getAtendimento().getUsuarioCadsus().getNomeSocial());
            dto.setOrigem(flagOrigem);
        }

        ExameProcedimentoDTO exameProcedimento = new ExameProcedimentoDTO();

        exameProcedimento.setExameProcedimento(tipoExame.getExameProcedimentoPadrao());
        exameProcedimento.setQuantidade(1L);
        exameProcedimento.setComplemento(null);
        exameProcedimento.setValor(0D);

        List<ExameProcedimentoDTO> item = new ArrayList<>();
        item.add(exameProcedimento);

        dto.setExameProcedimentoDTOs(item);
        Long codigoExameCadastrado = BOFactoryWicket.getBO(ExameFacade.class).cadastrarExameCargaViralHIV(dto, requisicaoCargaViralHIV);

        limpar(target);

        initDialogImpressao(target);
        dlgConfirmacaoImpressao.show(target, codigoExameCadastrado);
    }

    private void limpar(AjaxRequestTarget target) {
        autoCompleteConsultaCid.limpar(target);
        inputDiagnostico.limpar(target);
    }

    private void initDialogImpressao(AjaxRequestTarget target) {
        if (dlgConfirmacaoImpressao == null) {
            dlgConfirmacaoImpressao = new DlgImpressaoObject<Long>(getProntuarioController().newWindowId(), bundle("desejaImprimirExame", this)) {
                @Override
                public DataReport getDataReport(Long codigoExameCadastrado) throws ReportException {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addParameter(new QueryCustom.QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();

                    ImpressaoRequisicaoCargaViralHIVDTOParam param = new ImpressaoRequisicaoCargaViralHIVDTOParam();
                    param.setCodigoExame(codigoExameCadastrado);
                    param.setAtendimento(exameCadastrado.getAtendimento());

                    return BOFactoryWicket.getBO(ProntuarioReportFacade.class).relatorioImpressaoRequisicaoCargaViralHIV(param);
                }

                @Override
                public void onFechar(AjaxRequestTarget target, Long codigoExameCadastrado) throws ValidacaoException, DAOException {
                    Exame exameCadastrado = LoadManager.getInstance(Exame.class)
                            .addProperty(VOUtils.montarPath(Exame.PROP_TIPO_EXAME, TipoExame.PROP_TIPO))
                            .addParameter(new QueryCustom.QueryCustomParameter(Exame.PROP_CODIGO, codigoExameCadastrado))
                            .start().getVO();

                    Boolean isLacen = RepositoryComponentDefault.Tipo.LACEN.value().equals(exameCadastrado.getTipoExame().getTipo());
                    if(isLacen){
                        getProntuarioController().changePanel(target, new SolicitacaoLacenPanel(getProntuarioController().panelId()));
                    } else {
                        if(containerTelaSoap != null && RepositoryComponentDefault.NO_SOAP.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new SoapPanel(getProntuarioController().panelId(), BundleManager.getString("evolucaoSoap"), containerTelaSoap));
                        } else if(containerTelaSoap != null && RepositoryComponentDefault.NO_REGISTRO_ESPECIALIZADO.equals(containerTelaSoap.descricao())){
                            getProntuarioController().changePanel(target, new RegistroEspecializadoPanel(getProntuarioController().panelId(), BundleManager.getString("registroEspecializado"), containerTelaSoap));
                        } else {
                            getProntuarioController().changePanel(target, new SolicitacaoExamesPanel(getProntuarioController().panelId(), containerTelaSoap));
                        }
                    }
                }
            };
            getProntuarioController().addWindow(target, dlgConfirmacaoImpressao);
        }
    }

    private void carregarRequisicaoCargaViralHIV() {
        ExameRequisicao exameRequisicao = LoadManager.getInstance(ExameRequisicao.class)
                .addProperty(ExameRequisicao.PROP_CODIGO)
                .addParameter(new QueryCustom.QueryCustomParameter(ExameRequisicao.PROP_EXAME_PROCEDIMENTO, tipoExame.getExameProcedimentoPadrao()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_USUARIO_CADSUS), getAtendimento().getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_STATUS), ExameRequisicao.Status.ABERTO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(ExameRequisicao.PROP_EXAME, Exame.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN, Arrays.asList(Exame.STATUS_AUTORIZADO, Exame.STATUS_DESVINCULADO, Exame.STATUS_RECEBIDO, Exame.STATUS_SOLICITADO)))
                .start().getVO();


        if (exameRequisicao != null) {
            RequisicaoCargaViralHIV requisicaoCargaViralHIV = LoadManager.getInstance(RequisicaoCargaViralHIV.class)
                    .addProperties(new HQLProperties(RequisicaoCargaViralHIV.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(RequisicaoCargaViralHIV.PROP_EXAME_REQUISICAO, exameRequisicao))
                    .start().getVO();

            if (requisicaoCargaViralHIV != null) {
                getForm().getModel().setObject(requisicaoCargaViralHIV);
            }
        }
    }
}