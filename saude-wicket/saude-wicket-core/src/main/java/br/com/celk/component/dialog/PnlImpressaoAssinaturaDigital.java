package br.com.celk.component.dialog;

import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalService;
import br.com.celk.bo.service.rest.assinaturadigital.Assinadores;
import br.com.celk.bo.service.rest.assinaturadigital.AssinaturaDigitalGlobaltech;
import br.com.celk.bo.service.rest.assinaturadigital.bry.service.AssinaturaDigitalBry;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.ReportLink;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import net.sf.jasperreports.engine.JRException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

import java.io.IOException;


/**
 * <AUTHOR>
 */
public abstract class PnlImpressaoAssinaturaDigital extends Panel {

    private final String IMG = "img-info";
    private WebMarkupContainer image;
    private MultiLineLabel label;
    private ReportLink btnImprimir;
    private AbstractAjaxButton btnAssinar;
    private AbstractAjaxButton btnFechar;
    private Label labelImprimir;
    private Label labelAssinar;
    private Label labelFechar;
    private Label labelSenha;
    private String senhaCertificado;
    private InputField txtSenhaCertificado;
    private Form form;
    private String message;
    private final boolean exibeAssinatura;

    public PnlImpressaoAssinaturaDigital(String id, String message, boolean exibeAssinatura) {
        super(id);
        this.message = message;
        this.exibeAssinatura = exibeAssinatura;
        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);

        form.add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));

        form.add(label = new MultiLineLabel("message", message));

        label.setOutputMarkupId(true);

        AbstractAssinaturaDigitalService service = new AbstractAssinaturaDigitalService();
        form.add(txtSenhaCertificado = new InputField("senhaCertificado", new PropertyModel<String>(this, "senhaCertificado")));        form.add(labelSenha = new Label("senha", BundleManager.getString("senha")));
        txtSenhaCertificado.setOutputMarkupId(true);
        labelSenha.setOutputMarkupId(true);
        try {
            boolean senhaVisible;
            if (!exibeAssinatura) {
                senhaVisible = false;
            } else {
                senhaVisible = Usuario.TipoCertificado.PFX.value().equals(AssinaturaDigitalUtil.loadUsuario().getTipoCertificado()) &&
                        Assinadores.BRY.getId().equals(service.carregarProvedor()) &&
                        new AssinaturaDigitalBry().isAssinaturaEnabled();
            }
            txtSenhaCertificado.setVisible(senhaVisible);
            labelSenha.setVisible(senhaVisible);
            if (!senhaVisible) labelSenha.setDefaultModelObject("");
        } catch (ValidacaoException e) {
            txtSenhaCertificado.setVisible(false);
            labelSenha.setVisible(false);
            labelSenha.setDefaultModelObject("");
        }

        form.add(btnImprimir = new ReportLink("btnImprimir") {

            @Override
            public DataReport getDataReport() throws ReportException {
                return PnlImpressaoAssinaturaDigital.this.onImprimir();
            }

        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        btnFechar.add(labelFechar = new Label("labelFechar", BundleManager.getString("fechar")));
        labelFechar.setOutputMarkupId(true);

        form.add(btnAssinar = new AbstractAjaxButton("btnAssinar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, JRException, ReportException, IOException {
                onAssinar(target, txtSenhaCertificado.getValue());
            }
        });
        btnAssinar.setOutputMarkupId(true);
        btnAssinar.setVisible(exibeAssinatura && new AssinaturaDigitalBry().isAssinaturaEnabled());

        btnAssinar.add(labelAssinar = new Label("labelAssinar", BundleManager.getString("assinar_digitalmente")));
        labelAssinar.setOutputMarkupId(true);

        btnImprimir.add(labelImprimir = new Label("labelImprimir", BundleManager.getString("imprimir")));
        labelImprimir.setOutputMarkupId(true);
        add(form);
    }


    public abstract void onAssinar(AjaxRequestTarget target, String senha) throws ValidacaoException, DAOException, JRException, ReportException, IOException;

    public abstract DataReport onImprimir() throws ReportException;

    public void onAssinar(AjaxRequestTarget target) throws ValidacaoException, DAOException, JRException, ReportException, IOException {
    }

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        label.setDefaultModel(new Model<String>(message));
        target.add(label);
    }

    public AbstractAjaxButton getBtnFechar() {
        return btnFechar;
    }

    public void setLabelFechar(AjaxRequestTarget target, String label) {
        labelFechar.setDefaultModelObject(label);
        target.add(labelFechar);
    }

    public AbstractAjaxButton getBtnAssinar() {
        return btnAssinar;
    }

    public void setLabelAssinar(AjaxRequestTarget target, String label) {
        labelAssinar.setDefaultModelObject(label);
        target.add(labelAssinar);
    }

    public void setLabelImprimir(AjaxRequestTarget target, String label) {
        labelImprimir.setDefaultModelObject(label);
        target.add(labelImprimir);
    }
}
