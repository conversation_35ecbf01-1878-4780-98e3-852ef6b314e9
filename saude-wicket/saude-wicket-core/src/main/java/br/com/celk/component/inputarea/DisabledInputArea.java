package br.com.celk.component.inputarea;

import java.io.Serializable;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class DisabledInputArea<T extends Serializable> extends InputArea<T> {

    public DisabledInputArea(String id) {
        super(id);
        init();
    }

    public DisabledInputArea(String id, IModel<T> model) {
        super(id, model);
        init();
    }

    private void init(){
        setEnabled(false);
    }

}
