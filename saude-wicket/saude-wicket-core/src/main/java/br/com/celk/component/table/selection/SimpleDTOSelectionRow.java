package br.com.celk.component.table.selection;

import br.com.celk.component.table.TableRow;
import java.io.Serializable;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.behavior.AttributeAppender;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public class SimpleDTOSelectionRow<T extends Serializable> extends TableRow<DTOSelection<T>> implements ISimpleSelectionRow<DTOSelection<T>> {

    private AttributeAppender activeClassAppender = new AttributeAppender("class", " active");
//    private static AttributeRemover activeClassRemover = new AttributeRemover("class", "active");
    
    private DTOSelection<T> object;
    private ISimpleSelectionTable table;
    
    public SimpleDTOSelectionRow(String id, int index, final IModel<DTOSelection<T>> model, ISimpleSelectionTable table) {
        super(id, index, model);
        
        this.object = model.getObject();
        
        this.table = table;
        
        setOutputMarkupId(true);
    }
    
    @Override
    public void onSelection() {
        if (!object.isSelected()) {
            table.onSelection(object.getBean(), this);
            setSelected(true);
        }
    }
    
    @Override
    public void onSelection(AjaxRequestTarget target){
        if (!object.isSelected()) {
            table.onSelection(target, object.getBean(), this);
            setSelected(target, true);
        }
    }
    
    @Override
    public void setSelected(boolean selected) {
        this.object.setSelected(selected);
        resolveClass(selected);
    }
    
    @Override
    public void setSelected(AjaxRequestTarget target, boolean selected){
        setSelected(selected);
        target.add(this);
    }
    
    private void resolveClass(boolean selected){
        if (selected) {
            addActiveClass();
        } else {
            removeActiveClass();
        }
    }

    private void addActiveClass(){
//        if (getBehaviors().contains(activeClassRemover)) {
//            remove(activeClassRemover);
//        }
        if (! getBehaviors().contains(activeClassAppender)) {
            add(activeClassAppender);
        }
    }
    
    private void removeActiveClass(){
        if (getBehaviors().contains(activeClassAppender)) {
            remove(activeClassAppender);
        }
//        if (! getBehaviors().contains(activeClassRemover)) {
//            add(activeClassRemover);
//        }
    }

    @Override
    protected void onBeforeRender() {
        super.onBeforeRender();
        if (object.getBean().equals(table.getSelectedObject())
                || (object.getBean() != null && object.getBean() == table.getSelectedObject())) {
            onSelection();
        }
    }

    @Override
    public DTOSelection<T> getRowObject() {
        return object;
    }
    
}
