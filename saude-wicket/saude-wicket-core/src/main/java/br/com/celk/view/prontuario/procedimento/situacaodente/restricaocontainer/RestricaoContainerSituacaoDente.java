package br.com.celk.view.prontuario.procedimento.situacaodente.restricaocontainer;

import br.com.celk.component.consulta.restricao.IRestricaoContainer;
import br.com.celk.component.inputfield.InputField;
import br.com.ksisolucoes.bo.prontuario.procedimento.interfaces.dto.QueryConsultaProcedimentoDTOParam;
import br.com.ksisolucoes.bo.situacaodente.interfaces.dto.QueryConsultaSituacaoDenteDTOParam;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public class RestricaoContainerSituacaoDente extends Panel implements IRestricaoContainer<QueryConsultaSituacaoDenteDTOParam> {

    private InputField<String> txtDescricao;
    
    private QueryConsultaSituacaoDenteDTOParam param = new QueryConsultaSituacaoDenteDTOParam();
    
    public RestricaoContainerSituacaoDente(String id) {
        super(id);
        
        WebMarkupContainer root = new WebMarkupContainer("root", new CompoundPropertyModel(param));
        
        root.add(txtDescricao = new InputField<String>("descricao"));
        
        add(root);
    }

    @Override
    public QueryConsultaSituacaoDenteDTOParam getRestricoes() {
        return param;
    }

    @Override
    public void limpar(AjaxRequestTarget target) {
        txtDescricao.limpar(target);
    }

    @Override
    public Component getComponentRequestFocus() {
        return txtDescricao;
    }

}
