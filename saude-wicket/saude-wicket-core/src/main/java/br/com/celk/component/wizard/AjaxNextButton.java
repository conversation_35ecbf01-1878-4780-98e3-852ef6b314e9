package br.com.celk.component.wizard;

import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;

/**
 *
 * <AUTHOR>
 */
public class AjaxNextButton extends AjaxWizardButton {

    public AjaxNextButton(final String id, final IAjaxWizard wizard) {
        super(id, wizard);
    }

    @Override
    public boolean isEnabled() {
        return getWizardModel().isNextAvailable();
    }

    @Override
    public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
        getWizardModel().next(target);
        updateWizard(target);
    }

}
