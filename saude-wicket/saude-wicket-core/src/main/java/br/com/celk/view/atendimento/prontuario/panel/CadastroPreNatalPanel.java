package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.tabbedpanel.cadastro.CadastroTab;
import br.com.celk.component.tabbedpanel.cadastro.ITabPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.celk.view.atendimento.prontuario.tabbedpanel.*;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoPreNatalDTO;
import org.apache.wicket.extensions.markup.html.tabs.ITab;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.bundle;

/**
 * <AUTHOR>
 */
public class CadastroPreNatalPanel extends ProntuarioCadastroPanel {

    private NoPreNatalDTO noPreNatalDTO;
    private boolean viewOnly;

    public CadastroPreNatalPanel(String id, NoPreNatalDTO noPreNatalDTO, boolean viewOnly) {
        super(id, bundle("preNatal"));
        this.noPreNatalDTO = noPreNatalDTO;
        this.viewOnly = viewOnly;
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        List<ITab> tabs = new ArrayList<ITab>();

        tabs.add(new CadastroTab<NoPreNatalDTO>(noPreNatalDTO) {
            @Override
            public ITabPanel<NoPreNatalDTO> newTabPanel(String id, NoPreNatalDTO preNatal) {
                return new PreNatalGestacaoAtualTab(id, preNatal);
            }
        });
        tabs.add(new CadastroTab<NoPreNatalDTO>(noPreNatalDTO) {

            @Override
            public ITabPanel<NoPreNatalDTO> newTabPanel(String id, NoPreNatalDTO preNatal) {
                return new PreNatalHistoricoObstetricoTab(id, preNatal);
            }
        });
        tabs.add(new CadastroTab<NoPreNatalDTO>(noPreNatalDTO) {

            @Override
            public ITabPanel<NoPreNatalDTO> newTabPanel(String id, NoPreNatalDTO preNatal) {
                return new PreNatalExamesTab(id, preNatal);
            }
        });
        if  (viewOnly) {
            tabs.add(new CadastroTab<NoPreNatalDTO>(noPreNatalDTO) {

                @Override
                public ITabPanel<NoPreNatalDTO> newTabPanel(String id, NoPreNatalDTO preNatal) {
                    return new PreNatalAvalicaoGestanteTab(id, preNatal);
                }
            });
            tabs.add(new CadastroTab<NoPreNatalDTO>(noPreNatalDTO) {

                @Override
                public ITabPanel<NoPreNatalDTO> newTabPanel(String id, NoPreNatalDTO preNatal) {
                    return new PreNatalAvaliacaoPartoTab(id, preNatal);
                }
            });
        }
        add(new PreNatalTabbedPanel("wizard", noPreNatalDTO, viewOnly, tabs, getProntuarioController()));
    }
}
