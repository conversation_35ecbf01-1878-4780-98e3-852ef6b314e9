package br.com.celk.component.table;

import br.com.celk.component.table.selection.DTOSelection;
import br.com.celk.component.table.selection.IMultiSelectionTable;
import br.com.celk.component.table.selection.deprecated.MultiDTOSelectionRowOld;
import java.io.Serializable;
import org.apache.wicket.model.IModel;

/**
 *
 * <AUTHOR>
 */
public abstract class CustomColorMultiSelectionTableRow<T extends Serializable> extends MultiDTOSelectionRowOld<T> {

    public CustomColorMultiSelectionTableRow(String id, int index, final IModel<DTOSelection<T>> model, IMultiSelectionTable table) {
        super(id, index, model, table);
    }
    
    public abstract TableColorEnum getColor();

    @Override
    public final String getDefaultClass() {
        return getColor().getClasse();
    }
}
