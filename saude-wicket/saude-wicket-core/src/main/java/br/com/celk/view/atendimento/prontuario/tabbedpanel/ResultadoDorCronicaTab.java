package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.InputArea;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.util.Util;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.DorCronicaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public class ResultadoDorCronicaTab extends TabPanel<DorCronicaDTO> {

    private DorCronicaDTO dorCronicaDTO;
    private WebMarkupContainer containerResultados;
    private InputField txtResultado;
    private InputArea txtConduta;

    public ResultadoDorCronicaTab(String id, DorCronicaDTO object) {
        super(id, object);
        dorCronicaDTO = object;
        init();
    }

    private void init() {
        setDefaultModel(new CompoundPropertyModel(dorCronicaDTO));
        DorCronicaDTO proxy = on(DorCronicaDTO.class);

        calcularResultado();

        add(txtResultado = new InputField(path(proxy.getResultado())));
        add(txtConduta = new InputArea(path(proxy.getConduta())));
        add(new AbstractAjaxButton("btnAdicionarSolucao") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException, ReportException {

            }
        });
    }

    private void calcularResultado() {
        if (!isAnyEmpty()) {
            long totalFearAvoidanceBeliefs1 = dorCronicaDTO.getFearAvoidanceBeliefs().getItem2() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem3() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem4() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem5();

            long totalFearAvoidanceBeliefs2 = dorCronicaDTO.getFearAvoidanceBeliefs().getItem6() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem7() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem8() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem10() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem11() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem12() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem13() +
                    dorCronicaDTO.getFearAvoidanceBeliefs().getItem15();

            long totalIndiceOswestry = dorCronicaDTO.getIndiceOswestry().getIntencidadeDor() +
                    dorCronicaDTO.getIndiceOswestry().getCuidadosPessoais() +
                    dorCronicaDTO.getIndiceOswestry().getPesos() +
                    dorCronicaDTO.getIndiceOswestry().getAndar() +
                    dorCronicaDTO.getIndiceOswestry().getSentar() +
                    dorCronicaDTO.getIndiceOswestry().getDePe() +
                    dorCronicaDTO.getIndiceOswestry().getSono() +
                    dorCronicaDTO.getIndiceOswestry().getVidaSexual() +
                    dorCronicaDTO.getIndiceOswestry().getVidaSocial() +
                    dorCronicaDTO.getIndiceOswestry().getViagens();

            long totalStartBacklScreeningTool = dorCronicaDTO.getStartBackScreeningTool().getItem1() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem2() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem3() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem4() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem5() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem6() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem7() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem8() +
                    dorCronicaDTO.getStartBackScreeningTool().getItem9();
        }
    }

    private boolean isAnyEmpty() {
        return dorCronicaDTO.getFearAvoidanceBeliefs() != null && (
                Util.isEmptyAny(dorCronicaDTO.getFearAvoidanceBeliefs().getItem1(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem2(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem3(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem4(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem5(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem6(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem7(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem8(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem9(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem10(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem11(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem12(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem13(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem14(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem15(),
                        dorCronicaDTO.getFearAvoidanceBeliefs().getItem16()));
    }


    @Override
    public String getTitle() {
        return bundle("fearAvoidanceBeliefes");
    }
}
