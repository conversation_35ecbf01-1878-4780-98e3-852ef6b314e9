package br.com.celk.view.atendimento.prontuario.nodes;

import br.com.celk.atendimento.prontuario.NodesAtendimentoRef;
import br.com.celk.resources.Icon32;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.nodes.annotations.ProntuarioNode;
import br.com.celk.view.atendimento.prontuario.panel.ReceituarioLivrePanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;

/**
 *
 * <AUTHOR>
 */
@ProntuarioNode(NodesAtendimentoRef.RECEITUARIO_LIVRE)
public class ReceituarioLivreNode extends ProntuarioNodeImp {

    @Override
    public ProntuarioCadastroPanel getPanel(String id) {
        return new ReceituarioLivrePanel(id);
    }

    @Override
    public Icon32 getIcone() {
        return Icon32.PILLS;
    }

    @Override
    public String getTitulo() {
        return BundleManager.getString("receituarioLivre");
    }
}
