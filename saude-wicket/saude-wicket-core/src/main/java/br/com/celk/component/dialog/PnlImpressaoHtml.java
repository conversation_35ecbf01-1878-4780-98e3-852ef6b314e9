package br.com.celk.component.dialog;

import br.com.celk.bo.service.rest.assinaturadigital.AbstractAssinaturaDigitalService;
import br.com.celk.bo.service.rest.assinaturadigital.Assinadores;
import br.com.celk.bo.service.rest.assinaturadigital.AssinaturaDigitalGlobaltech;
import br.com.celk.component.behavior.AjaxPreviewBlank;
import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputfield.InputField;
import br.com.celk.component.link.HtmlAjaxReportLink;
import br.com.celk.report.HtmlReport;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtil;
import br.com.celk.view.atendimento.prontuario.panel.utils.assinaturadigital.AssinaturaDigitalUtilDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.controle.Usuario;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.Label;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

/**
 * <AUTHOR>
 */
public abstract class PnlImpressaoHtml extends Panel {

    private final String IMG = "img-info";
    private Form form;
    private WebMarkupContainer image;
    private MultiLineLabel label;
    private HtmlAjaxReportLink btnImprimir;
    private AbstractAjaxButton btnFechar;
    private AbstractAjaxButton btnAssinarDigitalmente;
    private Label labelImprimir;
    private Label labelAssinar;
    private Label labelSenha;
    private String message;
    private String senhaCertificado;
    private InputField txtSenhaCertificado;
    private final AssinaturaDigitalUtilDTO dto;
    private AjaxPreviewBlank ajaxPreviewBlank;

    public PnlImpressaoHtml(String id, String message) {
        this(id, message, new AssinaturaDigitalUtilDTO());
    }

    public PnlImpressaoHtml(String id, String message, AssinaturaDigitalUtilDTO dto) {
        super(id);
        this.message = message;
        this.dto = dto;

        init();
    }

    private void init() {
        form = new Form("form");
        setOutputMarkupId(true);

        form.add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));
        form.add(ajaxPreviewBlank = new AjaxPreviewBlank());

        form.add(label = new MultiLineLabel("message", message));

        label.setOutputMarkupId(true);

        AbstractAssinaturaDigitalService service = new AbstractAssinaturaDigitalService();
        form.add(txtSenhaCertificado = new InputField("senhaCertificado", new PropertyModel<String>(dto, "senhaCertificado")));
        form.add(labelSenha = new Label("senha", BundleManager.getString("senha")));
        txtSenhaCertificado.setOutputMarkupId(true);
        labelSenha.setOutputMarkupId(true);
        try {
            boolean senhaVisible = Usuario.TipoCertificado.PFX.value().equals(AssinaturaDigitalUtil.loadUsuario().getTipoCertificado()) &&
                    Assinadores.BRY.getId().equals(service.carregarProvedor()) &&
                    new AssinaturaDigitalGlobaltech().isAssinaturaEnabled();
            txtSenhaCertificado.setVisible(senhaVisible);
            labelSenha.setVisible(senhaVisible);
            if (!senhaVisible) labelSenha.setDefaultModelObject("");
        } catch (ValidacaoException e) {
            txtSenhaCertificado.setVisible(false);
            labelSenha.setVisible(false);
            labelSenha.setDefaultModelObject("");
        }

        form.add(btnImprimir = new HtmlAjaxReportLink("btnImprimir") {
            @Override
            public HtmlReport getReport(AjaxRequestTarget target) throws DAOException, ValidacaoException {
                return PnlImpressaoHtml.this.onImprimir(target);
            }
        });

        form.add(btnAssinarDigitalmente = new AbstractAjaxButton("btnAssinarDigitalmente") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                dto.setSenhaCertificado(txtSenhaCertificado.getValue());
                onAssinarDigitalmente(target);
            }
        });
        btnAssinarDigitalmente.setVisible(new AssinaturaDigitalGlobaltech().isAssinaturaEnabled());

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        btnImprimir.add(labelImprimir = new Label("labelImprimir", BundleManager.getString("imprimir")));
        btnAssinarDigitalmente.add(labelAssinar = new Label("labelAssinar", BundleManager.getString("assinarDigitalmente")));
        labelImprimir.setOutputMarkupId(true);
        labelAssinar.setOutputMarkupId(true);

        add(form);
    }

    private void assinarSalvarDocumento(AssinaturaDigitalUtilDTO dto, AjaxRequestTarget target, HtmlReport htmlReport) throws ValidacaoException, DAOException {
        AssinaturaDigitalUtil util = new AssinaturaDigitalUtil(dto, htmlReport);
        util.exibirDocumento(target);
    }

    public abstract HtmlReport onImprimir(AjaxRequestTarget target) throws DAOException, ValidacaoException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public void onAssinarDigitalmente(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        label.setDefaultModel(new Model<String>(message));
        target.add(label);
    }

    public AbstractAjaxButton getBtnFechar() {
        return btnFechar;
    }

    public void setLabelImprimir(AjaxRequestTarget target, String label) {
        labelImprimir.setDefaultModelObject(label);
        target.add(labelImprimir);
    }
}
