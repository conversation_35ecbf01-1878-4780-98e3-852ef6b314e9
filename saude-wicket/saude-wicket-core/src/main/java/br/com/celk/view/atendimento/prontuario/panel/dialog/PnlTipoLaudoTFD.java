package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.PropertyModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by silvio on 27/06/18.
 */
public abstract class PnlTipoLaudoTFD extends Panel {

    private BpaProcesso bpaProcesso;
    private WebMarkupContainer image;
    private AbstractAjaxButton btnAvancar;
    private AbstractAjaxButton btnFechar;
    private DropDown dropDownTipoLaudo;
    private TipoLaudoTFD.TipoLaudo tipoLaudo;

    private final String IMG = "img-info";

    public PnlTipoLaudoTFD(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form");
        form.add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));

        form.add(dropDownTipoLaudo = getDropDownTipoLaudo("tipoLaudo"));

        form.add(btnAvancar = new AbstractAjaxButton("btnAvancar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onAvancar(target, tipoLaudo);
                onFechar(target);
            }
        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(form);
    }

    public abstract void onAvancar(AjaxRequestTarget target, TipoLaudoTFD.TipoLaudo tipoLaudo) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

    public void setObject(AjaxRequestTarget target) {
        tipoLaudo = null;

        dropDownTipoLaudo.removeAllChoices();

        List<TipoLaudoTFD> tipoLaudoTFDs = new ArrayList<>();
        TipoLaudoTFD tipoLaudoTFD;

        tipoLaudoTFD = new TipoLaudoTFD();
        tipoLaudoTFD.setTipoLaudo(TipoLaudoTFD.TipoLaudo.INTRAESTADUAL);
        tipoLaudoTFDs.add(tipoLaudoTFD);

        tipoLaudoTFD = new TipoLaudoTFD();
        tipoLaudoTFD.setTipoLaudo(TipoLaudoTFD.TipoLaudo.INTERESTADUAL);
        tipoLaudoTFDs.add(tipoLaudoTFD);

        if (CollectionUtils.isNotNullEmpty(tipoLaudoTFDs)) {
            for (TipoLaudoTFD dto : tipoLaudoTFDs) {
                dropDownTipoLaudo.addChoice(dto.getTipoLaudo(), dto.getTipoLaudo().descricao());
            }
            tipoLaudo = tipoLaudoTFDs.get(0).getTipoLaudo();
        }
        dropDownTipoLaudo.addAjaxUpdateValue();

        target.add(dropDownTipoLaudo);
        target.focusComponent(dropDownTipoLaudo);
    }

    public AbstractAjaxButton getBtnAvancar() {
        return btnAvancar;
    }

    public AbstractAjaxButton getBtnFechar() {
        return btnFechar;
    }

    private DropDown getDropDownTipoLaudo(String id) {
        return new DropDown(id, new PropertyModel(this, "tipoLaudo"));
    }

}
