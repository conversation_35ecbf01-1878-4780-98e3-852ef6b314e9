package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.AttributeModifier;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.WebMarkupContainer;
import org.apache.wicket.markup.html.basic.MultiLineLabel;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.form.TextArea;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.Model;
import org.apache.wicket.model.PropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlConfirmacaoJustificativa extends Panel {

    private Form form;
    private WebMarkupContainer image;
    private MultiLineLabel label;
    private AbstractAjaxButton btnConfirmar;
    private AbstractAjaxButton btnFechar;

    private final String IMG = "img-warn";

    private String message;

    private String justificativa;
    private TextArea<String> txtJustificativa;

    public PnlConfirmacaoJustificativa(String id, String message) {
        super(id);
        this.message = message;
        init();
    }


    private void init() {
        form = new Form("form");
        form.add(image = new WebMarkupContainer("img"));
        image.add(new AttributeModifier("class", IMG));

        form.add(label = new MultiLineLabel("message", message));
        label.setOutputMarkupId(true);
        label.setEscapeModelStrings(false);
        form.add(txtJustificativa = new TextArea<String>("justificativa", new PropertyModel(this, "justificativa")));
        txtJustificativa.setOutputMarkupId(true);

        form.add(btnConfirmar = new AbstractAjaxButton("btnConfirmar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onConfirmar(target, justificativa);
            }

        });

        form.add(btnFechar = new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        });

        add(form);

    }

    public void setMessage(AjaxRequestTarget target, String message) {
        this.message = message;
        label.setDefaultModel(new Model<String>(message));
        target.add(label);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, String justificativa) throws ValidacaoException, DAOException;

    public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
    }

}
