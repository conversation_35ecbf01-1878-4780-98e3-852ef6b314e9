package br.com.celk.view.atendimento.prontuario.panel.tuberculose.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.datechooser.DateChooser;
import br.com.celk.component.dropdown.DropDown;
import br.com.celk.component.dropdown.util.DropDownUtil;
import br.com.celk.system.util.MessageUtil;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.tuberculose.TuberculoseAcompanhamento;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 * <AUTHOR>
 */
public abstract class PnlConclusaoTuberculose extends Panel {

    private Form form;
    private CompoundPropertyModel<TuberculoseAcompanhamento> model;
    private TuberculoseAcompanhamento tuberculoseAcompanhamento;
    private DateChooser dchData;
    private DropDown dropDownMotivoEncerramento;

    public PnlConclusaoTuberculose(String id, TuberculoseAcompanhamento tuberculoseAcompanhamento) {
        super(id);
        this.tuberculoseAcompanhamento = tuberculoseAcompanhamento;
        init();
    }

    private void init() {
        form = new Form<>("form", model = new CompoundPropertyModel(tuberculoseAcompanhamento));
        TuberculoseAcompanhamento proxy = on(TuberculoseAcompanhamento.class);
        setOutputMarkupId(true);

        form.add(dchData = new DateChooser(path(proxy.getDataEncerramento())));
        form.add(dropDownMotivoEncerramento = DropDownUtil.getIEnumDropDown(path(proxy.getMotivoEncerramento()), TuberculoseAcompanhamento.MotivoEncerramento.values(), true, true, false, true));

        form.add(new AbstractAjaxButton("btnSalvar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                if (validarCadastro(target)) {
//                    model.setObject(tuberculoseSintomatico);
                    onConfirmar(target, model.getObject());
                }
            }
        });

        form.add(new AbstractAjaxButton("btnFechar") {

            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                onFechar(target);
            }
        }.setDefaultFormProcessing(false));

        add(form);
    }

    public boolean validarCadastro(AjaxRequestTarget target) {
        try {
            if (dchData.getComponentValue() == null) {
                throw new ValidacaoException(bundle("informeData"));
            } else if (Data.adjustRangeHour(dchData.getComponentValue()).getDataInicial().after(Data.adjustRangeHour(DataUtil.getDataAtual()).getDataInicial())) {
                throw new ValidacaoException(bundle("msgDataNaoPodeSerMaiorDataAtual"));
            }
        } catch (ValidacaoException e) {
            MessageUtil.modalWarn(target, this, e);
            return false;
        }

        return true;
    }

    public abstract void onConfirmar(AjaxRequestTarget target, TuberculoseAcompanhamento tuberculoseAcompanhamento) throws ValidacaoException, DAOException;

    public abstract void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException;

}