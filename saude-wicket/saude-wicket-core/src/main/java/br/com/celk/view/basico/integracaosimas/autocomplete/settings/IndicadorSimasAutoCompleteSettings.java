package br.com.celk.view.basico.integracaosimas.autocomplete.settings;

import br.com.celk.component.consulta.configurator.autocomplete.AbstractAutoCompleteSettings;
import br.com.ksisolucoes.vo.geral.IndicadorSimas;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class IndicadorSimasAutoCompleteSettings extends AbstractAutoCompleteSettings<IndicadorSimas> implements Serializable {

    @Override
    public Map<String, String> getJsonPropertyMap(IndicadorSimas indicadorSimas) {
        Map<String, String> propertiesMap = new HashMap<>();

        propertiesMap.put("id", indicadorSimas.getIdentificador());
        propertiesMap.put("name", indicadorSimas.getDescricao());

        return propertiesMap;
    }

}