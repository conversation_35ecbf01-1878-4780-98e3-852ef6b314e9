package br.com.celk.component.dialog;

import br.com.celk.component.button.AbstractAjaxButton;
import br.com.celk.component.inputarea.DisabledInputArea;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.panel.Panel;
import org.apache.wicket.model.CompoundPropertyModel;

/**
 *
 * <AUTHOR>
 */
public abstract class PnlVisualizarObservacao extends Panel {

    private DisabledInputArea txtObservacao;
    private String observacao;

    public PnlVisualizarObservacao(String id) {
        super(id);
        init();
    }

    private void init() {
        Form form = new Form("form", new CompoundPropertyModel(this));

        form.add(txtObservacao = new DisabledInputArea("observacao"));
        form.add(new AbstractAjaxButton("btnFechar") {
            @Override
            public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
                txtObservacao.limpar(target);
                PnlVisualizarObservacao.this.onFechar(target);
            }
        });

        add(form);
    }

    public abstract void onFechar(AjaxRequestTarget target);

    public void setObservacao(AjaxRequestTarget target, String observacao) {
        this.observacao = observacao;
        target.add(this);
    }
}
