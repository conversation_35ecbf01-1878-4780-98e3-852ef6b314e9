package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.prontuario.procedimentocompetencia.interfaces.dto.ProcedimentoCompetenciaDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoServicoClassificacao;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarServicoClassificacao extends Window {

    private PnlInformarServicoClassificacao pnlInformarServicoClassificacao;

    public DlgInformarServicoClassificacao(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("informarServicoClassificacao"));
        setInitialWidth(700);
        setInitialHeight(220);
        setResizable(true);

        setContent(pnlInformarServicoClassificacao = new PnlInformarServicoClassificacao(getContentId()) {

            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgInformarServicoClassificacao.this.close(target, false);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, ProcedimentoServicoClassificacao procedimentoServicoClassificacao, ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO) throws ValidacaoException, DAOException {
                DlgInformarServicoClassificacao.this.onConfirmar(target, procedimentoServicoClassificacao, procedimentoCompetenciaDTO);
                DlgInformarServicoClassificacao.this.close(target, true);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                try {
                    DlgInformarServicoClassificacao.this.close(target, false);
                    target.appendJavaScript(JScript.removeAutoCompleteDrop());
                } catch (ValidacaoException ex) {
                    Logger.getLogger(DlgInformarServicoClassificacao.class.getName()).log(Level.SEVERE, null, ex);
                } catch (DAOException ex) {
                    Logger.getLogger(DlgInformarServicoClassificacao.class.getName()).log(Level.SEVERE, null, ex);
                }
                return true;
            }
        });
    }

    public void close(AjaxRequestTarget target, boolean abrirDialog) throws ValidacaoException, DAOException{
        super.close(target);
        if (abrirDialog) {
            changeDlg(target);
        }
    }

    public void show(AjaxRequestTarget target, ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO) {
        pnlInformarServicoClassificacao.setProcedimentoCompetenciaDTO(target, procedimentoCompetenciaDTO);
        super.show(target);
        pnlInformarServicoClassificacao.setFocus(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, ProcedimentoServicoClassificacao procedimentoServicoClassificacao, ProcedimentoCompetenciaDTO procedimentoCompetenciaDTO) throws ValidacaoException, DAOException;

    public abstract void changeDlg(AjaxRequestTarget target) throws ValidacaoException, DAOException;

}
