package br.com.celk.component.button;

import br.com.celk.component.table.ITable;
import br.com.celk.component.table.column.DateColumn;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.materiais.horus.interfaces.dto.ConsultaDadosEnviadosHorusDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.materiais.horus.SincronizacaoHorusProcesso.TipoSincronizacao;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;

import java.util.ArrayList;
import java.util.List;

import static br.com.celk.system.methods.WicketMethods.createSortableColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public abstract class ProcurarButton<T> extends AbstractAjaxButton{

    private ITable targetComponent;
    
    public ProcurarButton(String id, ITable target) {
        super(id);
        this.targetComponent = target;
    }

    public ProcurarButton(String id) {
        super(id);
    }

    public void setTargetComponent(ITable targetComponent) {
        this.targetComponent = targetComponent;
    }

    @Override
    public void onAction(AjaxRequestTarget target, Form form) throws ValidacaoException, DAOException {
        antesProcurar(target);
        procurar(target);
        depoisProcurar(target);
    }
    
    public void procurar(AjaxRequestTarget target){
        getTargetComponent().getDataProvider().setParameters(getParam());
        getTargetComponent().populate(target);
    }

    public void procurar(){
        getTargetComponent().getDataProvider().setParameters(getParam());
        getTargetComponent().populate();
    }
    
    public void antesProcurar(AjaxRequestTarget target) throws ValidacaoException{}
    public void depoisProcurar(AjaxRequestTarget target) throws ValidacaoException{}

    public abstract T getParam();
    
    public ITable getTargetComponent() {
        return targetComponent;
    }
}
