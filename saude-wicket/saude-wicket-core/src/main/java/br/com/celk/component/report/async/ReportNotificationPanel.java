package br.com.celk.component.report.async;

import br.com.celk.component.lazypanel.LazyPanel;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.vo.service.AsyncProcess;
import org.apache.wicket.Component;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.markup.html.panel.Panel;

/**
 *
 * <AUTHOR>
 */
public class ReportNotificationPanel extends Panel{

    private SimpleReportsPanel simpleReportsPanel;
    private Class pageClass;
    
    public ReportNotificationPanel(String id, Class pageClass) {
        super(id);
        this.pageClass = pageClass;
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        add(new LazyPanel("ajaxLazyLoad") {

            @Override
            public Component getLazyLoadComponent(String markupId, AjaxRequestTarget target) {
                simpleReportsPanel = new SimpleReportsPanel(markupId, pageClass);
                target.appendJavaScript(JScript.resizeReportView());
                return simpleReportsPanel;
            }
        });
    }

    public void notifyReport(AjaxRequestTarget target, AsyncProcess asyncProcess) {
        if (simpleReportsPanel!=null) {
            simpleReportsPanel.notifyReport(target, asyncProcess);
        }
    }

}
