package br.com.celk.view.atendimento.prontuario.tabbedpanel;

import br.com.celk.component.inputfield.DisabledInputField;
import br.com.celk.component.tabbedpanel.cadastro.TabPanel;
import br.com.celk.system.factory.BOFactoryWicket;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.NoHistoricoClinicoDTO;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.facade.AtendimentoFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import org.apache.wicket.markup.head.IHeaderResponse;
import org.apache.wicket.markup.head.OnDomReadyHeaderItem;
import org.apache.wicket.model.CompoundPropertyModel;
import org.apache.wicket.model.IModel;
import org.apache.wicket.model.LoadableDetachableModel;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
public class DadosPacienteTab extends TabPanel<NoHistoricoClinicoDTO> {

    private IModel<NoHistoricoClinicoDTO> model;
    private Atendimento atendimento;

    public DadosPacienteTab(String id, NoHistoricoClinicoDTO object) {
        super(id, object);
        atendimento = object.getAtendimento();
        init();
    }

    public void init() {
        setDefaultModel(model = new CompoundPropertyModel<NoHistoricoClinicoDTO>(new LoadableDetachableModel<NoHistoricoClinicoDTO>() {
            @Override
            protected NoHistoricoClinicoDTO load() {
                try {
                    NoHistoricoClinicoDTO hcDTO = BOFactoryWicket.getBO(AtendimentoFacade.class).carregarNodoHistoricoClinico(atendimento.getAtendimentoPrincipal().getCodigo(), atendimento.getUsuarioCadsus().getCodigo());
                    return hcDTO;
                } catch (DAOException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                } catch (ValidacaoException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
                return null;
            }
        }));

        NoHistoricoClinicoDTO proxy = on(NoHistoricoClinicoDTO.class);

        add(new DisabledInputField(path(proxy.getDadosClinicoDTO().getUsuarioCadsus().getNome())));
        add(new DisabledInputField(path(proxy.getDadosClinicoDTO().getUsuarioCadsus().getApelido())));
        add(new DisabledInputField(path(proxy.getDadosClinicoDTO().getUsuarioCadsus().getCpfFormatado())));
        add(new DisabledInputField(path(proxy.getDadosClinicoDTO().getUsuarioCadsus().getRg())));
        add(new DisabledInputField(path(proxy.getDadosClinicoDTO().getUsuarioCadsusCns().getNumeroCartaoFormatado())));
        add(new DisabledInputField(path(proxy.getDadosClinicoDTO().getEnderecoUsuarioCadsus().getEnderecoFormatadoComCidade())));
    }

    @Override
    public void renderHead(IHeaderResponse response) {
        super.renderHead(response);
        response.render(OnDomReadyHeaderItem.forScript(JScript.initMasks()));
        response.render(OnDomReadyHeaderItem.forScript(JScript.initExpandLinks()));
    }

    @Override
    public String getTitle() {
        return bundle("dadosPaciente");
    }
}
