package br.com.celk.view.cadsus.usuariocadsus.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import java.util.ArrayList;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.List;

/**
 * <AUTHOR>
 */
public abstract class DlgCnsPaciente extends Window {

    private PnlCnsPaciente pnlDocumentoNecessarios;

    public DlgCnsPaciente(String id) {
        super(id);
        init();
    }

    private void init() {
        setOutputMarkupId(true);
        setOutputMarkupPlaceholderTag(true);
        setTitle(BundleManager.getString("cartoesPaciente"));

        setInitialWidth(600);
        setInitialHeight(280);
        setResizable(true);

        setContent(pnlDocumentoNecessarios = new PnlCnsPaciente(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                close(target);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, UsuarioCadsusCns usuarioCadsusCns) {
                DlgCnsPaciente.this.onConfirmar(target, usuarioCadsusCns);
                close(target);
            }
        });

        setCloseButtonCallback(new CloseButtonCallback() {

            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget art) {
                close(art);
                return true;
            }
        });
    }


    public void show(AjaxRequestTarget target, UsuarioCadsus usuarioCadsus) {
        pnlDocumentoNecessarios.setDTO(target, carregarCns(usuarioCadsus));
        show(target);
    }

    private List<UsuarioCadsusCns> carregarCns(UsuarioCadsus usuarioCadsus) {
        if(usuarioCadsus != null && usuarioCadsus.getCodigo() != null){
            return LoadManager.getInstance(UsuarioCadsusCns.class)
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(UsuarioCadsusCns.PROP_USUARIO_CADSUS), usuarioCadsus))
                    .start().getList();
        }
        return new ArrayList<>();
    }

    public abstract void onConfirmar(AjaxRequestTarget target, UsuarioCadsusCns usuarioCadsusCns);

}
