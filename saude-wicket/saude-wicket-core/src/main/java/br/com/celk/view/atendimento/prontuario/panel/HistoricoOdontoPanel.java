package br.com.celk.view.atendimento.prontuario.panel;

import br.com.celk.component.action.IModelAction;
import br.com.celk.component.action.link.ActionType;
import br.com.celk.component.consulta.dataprovider.collection.CollectionProvider;
import br.com.celk.component.consulta.dataprovider.collection.ICollectionProvider;
import br.com.celk.component.interfaces.ISelectionAction;
import br.com.celk.component.table.SelectionTable;
import br.com.celk.component.table.Table;
import br.com.celk.component.table.column.MultipleActionCustomColumn;
import br.com.celk.resources.Resources;
import br.com.celk.view.atendimento.prontuario.panel.odonto.DetalhesHistoricoOdontoPanel;
import br.com.celk.view.atendimento.prontuario.panel.template.ProntuarioCadastroPanel;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.ResumoSituacaoDenteDTO;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.CollectionUtils;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.AtendimentoOdontoPlano;
import br.com.ksisolucoes.vo.prontuario.basico.Dente;
import br.com.ksisolucoes.vo.prontuario.basico.SituacaoDente;
import org.apache.wicket.ajax.AjaxRequestTarget;
import org.apache.wicket.extensions.markup.html.repeater.data.table.IColumn;
import org.apache.wicket.markup.html.form.Form;
import org.apache.wicket.markup.html.image.Image;

import java.util.*;

import static br.com.celk.system.methods.WicketMethods.bundle;
import static br.com.celk.system.methods.WicketMethods.createColumn;
import static br.com.ksisolucoes.system.methods.CoreMethods.path;
import static ch.lambdaj.Lambda.on;

/**
 *
 * <AUTHOR>
 */
@Deprecated
public class HistoricoOdontoPanel extends ProntuarioCadastroPanel{

    private List<Dente> denteList = new ArrayList<Dente>();
    private List<AtendimentoOdontoPlano> odontoList;
    private List<ResumoSituacaoDenteDTO> resumoList;
    private Map<Dente, List<AtendimentoOdontoPlano>> map = new LinkedHashMap<Dente, List<AtendimentoOdontoPlano>>();
    private SelectionTable<Dente> tblDentes;
    private Table tblHistorico;
    private Table tblResumo;

    public HistoricoOdontoPanel(String id, String titulo) {
        super(id, titulo);
    }

    @Override
    public void postConstruct() {
        super.postConstruct();
        Form form = new Form("form");
        
        form.add(new Image("imagemOdontograma", Resources.IMG_ODONTOGRAMA));
        form.add(tblDentes = new SelectionTable("tblDentes", getDenteColumns(), getDenteCollectionProvider()));
        form.add(tblHistorico = new Table("tblHistorico", getHistoricoColumns(), getHistoricoCollectionProvider()));
        form.add(tblResumo = new Table("tblResumo", getResumoColumns(), getResumoCollectionProvider()));

        tblDentes.addSelectionAction(new ISelectionAction<Dente>() {
            @Override
            public void onSelection(AjaxRequestTarget target, Dente dente) {
                odontoList = map.get(dente);
                target.add(tblHistorico);
            }
        });
        tblDentes.populate();
        tblHistorico.populate();
        tblResumo.populate();
        
        add(form);
        carregarItens();
    }
    
    private List<IColumn> getDenteColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        Dente dente = on(Dente.class);
        
        columns.add(createColumn(bundle("dente", this), dente.getNome()));
        return columns;
    }

    private ICollectionProvider getDenteCollectionProvider(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return denteList;
            }
        };
    }

    private List<IColumn> getHistoricoColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();
        
        AtendimentoOdontoPlano plano = on(AtendimentoOdontoPlano.class);
        
        columns.add(getCustomActionColumn());
        columns.add(createColumn(bundle("data"), plano.getDataCadastro()));
        columns.add(createColumn(bundle("face", this), plano.getDescricaoFaceFormatado()));
        columns.add(createColumn(bundle("situacao"), plano.getSituacaoDente().getDescricaoFormatado()));
        columns.add(createColumn(bundle("status"), plano.getDescricaoStatus()));
        return columns;
    }

    private List<IColumn> getResumoColumns(){
        List<IColumn> columns = new ArrayList<IColumn>();

        ResumoSituacaoDenteDTO resumo = on(ResumoSituacaoDenteDTO.class);

        columns.add(createColumn(bundle("cariado"), resumo.getQuantidadeCariado()));
        columns.add(createColumn(bundle("perdido"), resumo.getQuantidadePerdido()));
        columns.add(createColumn(bundle("obturadoRestaurado"), resumo.getQuantidadeObturadoRestaurado()));
        return columns;
    }

    private ICollectionProvider getHistoricoCollectionProvider(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                return odontoList;
            }
        };
    }

    private ICollectionProvider getResumoCollectionProvider(){
        return new CollectionProvider() {
            @Override
            public Collection getCollection(Object param) throws DAOException, ValidacaoException {
                if(br.com.celk.util.CollectionUtils.isEmpty(resumoList)){
                    carregarSituacaoDenteResumo();
                }
                return resumoList;
            }
        };
    }

    private void carregarItens() {
        AtendimentoOdontoPlano proxy = on(AtendimentoOdontoPlano.class);
        List<AtendimentoOdontoPlano> registros = LoadManager.getInstance(AtendimentoOdontoPlano.class)
            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getAtendimento().getUsuarioCadsus().getCodigo()), getAtendimento().getUsuarioCadsus().getCodigo()))
            .addParameter(new QueryCustom.QueryCustomParameter(path(proxy.getDente()), BuilderQueryCustom.QueryParameter.IS_NOT_NULL))
            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDente().getNome())))
            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getDataCadastro()), BuilderQueryCustom.QuerySorter.DECRESCENTE))
            .addSorter(new QueryCustom.QueryCustomSorter(path(proxy.getCodigo())))
            .start().getList();
        if(CollectionUtils.isNotNullEmpty(registros)){
            List<List<AtendimentoOdontoPlano>> agrupamento = CollectionUtils.groupList(registros, path(proxy.getDente().getCodigo()));
            for (List<AtendimentoOdontoPlano> list : agrupamento) {
                map.put(list.get(0).getDente(), list);
                denteList.add(list.get(0).getDente());
            }
            tblDentes.setSelectedObject(agrupamento.get(0).get(0).getDente());
            odontoList = agrupamento.get(0);
        }
    }

    private void carregarSituacaoDenteResumo() {
        resumoList = new ArrayList();
        ResumoSituacaoDenteDTO resumoSituacaoDenteDTO = new ResumoSituacaoDenteDTO();
        resumoSituacaoDenteDTO.setQuantidadeCariado((Long) LoadManager.getInstance(AtendimentoOdontoPlano.class)
                .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, SituacaoDente.PROP_CARIADO), BuilderQueryCustom.QueryGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), getAtendimento().getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, SituacaoDente.PROP_CARIADO), RepositoryComponentDefault.SIM_LONG))
                .setMaxResults(1).start().getVO());

        resumoSituacaoDenteDTO.setQuantidadePerdido((Long) LoadManager.getInstance(AtendimentoOdontoPlano.class)
                .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, SituacaoDente.PROP_PERDIDO), BuilderQueryCustom.QueryGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), getAtendimento().getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, SituacaoDente.PROP_PERDIDO), RepositoryComponentDefault.SIM_LONG))
                .setMaxResults(1).start().getVO());

        resumoSituacaoDenteDTO.setQuantidadeObturadoRestaurado((Long) LoadManager.getInstance(AtendimentoOdontoPlano.class)
                .addGroup(new QueryCustom.QueryCustomGroup(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, SituacaoDente.PROP_OBTURADO_RESTAURADO), BuilderQueryCustom.QueryGroup.COUNT))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_ATENDIMENTO, Atendimento.PROP_USUARIO_CADSUS, UsuarioCadsus.PROP_CODIGO), getAtendimento().getUsuarioCadsus().getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(AtendimentoOdontoPlano.PROP_SITUACAO_DENTE, SituacaoDente.PROP_OBTURADO_RESTAURADO), RepositoryComponentDefault.SIM_LONG))
                .setMaxResults(1).start().getVO());
        resumoList.add(resumoSituacaoDenteDTO);
    }

    private IColumn getCustomActionColumn() {
        return new MultipleActionCustomColumn<AtendimentoOdontoPlano>() {
            @Override
            public void customizeColumn(AtendimentoOdontoPlano rowObject) {
                addAction(ActionType.CONSULTAR, rowObject, new IModelAction<AtendimentoOdontoPlano>() {

                    @Override
                    public void action(AjaxRequestTarget target, AtendimentoOdontoPlano modelObject) throws ValidacaoException, DAOException {
                        getProntuarioController().changePanel(target, new DetalhesHistoricoOdontoPanel(getProntuarioController().panelId(), modelObject));
                    }

                }).setTitleBundleKey("detalhesTratamento");
            }
        };
    }
}
