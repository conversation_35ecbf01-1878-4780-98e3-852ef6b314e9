package br.com.celk.view.atendimento.prontuario.panel.dialog;

import br.com.celk.component.window.Window;
import br.com.celk.system.bundle.BundleManager;
import br.com.celk.system.javascript.JScript;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.CodigoBarrasProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import org.apache.wicket.ajax.AjaxRequestTarget;

import java.util.logging.Level;
import java.util.logging.Logger;

/**
 *
 * <AUTHOR>
 */
public abstract class DlgInformarCidDispensacao extends Window {

    private PnlInformarCidDispensacao pnlInformarCidDispensacao;

    public DlgInformarCidDispensacao(String id) {
        super(id);
        init();
    }

    private void init() {
        setTitle(BundleManager.getString("informarCid"));
        setInitialWidth(600);
        setInitialHeight(120);
        setResizable(true);

        setContent(pnlInformarCidDispensacao = new PnlInformarCidDispensacao(getContentId()) {
            @Override
            public void onFechar(AjaxRequestTarget target) throws ValidacaoException, DAOException {
                DlgInformarCidDispensacao.this.close(target, false);
            }

            @Override
            public void onConfirmar(AjaxRequestTarget target, Cid cid, Produto produto, CodigoBarrasProduto codigoBarrasProduto, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException {
                DlgInformarCidDispensacao.this.onConfirmar(target, cid, produto, codigoBarrasProduto, dispensacaoMedicamentoItem);
                DlgInformarCidDispensacao.this.close(target, true);
            }

        });

        setCloseButtonCallback(new CloseButtonCallback() {
            @Override
            public boolean onCloseButtonClicked(AjaxRequestTarget target) {
                try {
                    DlgInformarCidDispensacao.this.close(target, false);
                    target.appendJavaScript(JScript.removeAutoCompleteDrop());
                } catch (ValidacaoException ex) {
                    Logger.getLogger(DlgInformarCidDispensacao.class.getName()).log(Level.SEVERE, null, ex);
                } catch (DAOException ex) {
                    Logger.getLogger(DlgInformarCidDispensacao.class.getName()).log(Level.SEVERE, null, ex);
                }
                return true;
            }
        });
    }

    public void close(AjaxRequestTarget target, boolean abrirDialog) throws ValidacaoException, DAOException{
        super.close(target);
        if (abrirDialog) {
            changeDlg(target);
        }
    }

    public void show(AjaxRequestTarget target, String msg, Produto produto, CodigoBarrasProduto codigoBarrasProduto, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) {
        pnlInformarCidDispensacao.setObjeto(target, msg, produto, codigoBarrasProduto, dispensacaoMedicamentoItem);
        super.show(target);
        pnlInformarCidDispensacao.setFocus(target);
    }

    public abstract void onConfirmar(AjaxRequestTarget target, Cid cid, Produto produto, CodigoBarrasProduto codigoBarrasProduto, DispensacaoMedicamentoItem dispensacaoMedicamentoItem) throws ValidacaoException, DAOException;

    public abstract void changeDlg(AjaxRequestTarget target) throws ValidacaoException, DAOException;

}
