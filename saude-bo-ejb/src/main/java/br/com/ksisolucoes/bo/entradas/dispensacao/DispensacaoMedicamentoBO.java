package br.com.ksisolucoes.bo.entradas.dispensacao;

import br.com.ksisolucoes.bo.BOGenericImpl;
import br.com.ksisolucoes.bo.entradas.devolucao.interfaces.dto.DispensacaoMedicamentoContaPacienteDTO;
import br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaoestatistica.GerarDispensacaoEstatistica;
import br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaogeral.QueryConsultaImpressaoPrescricoes;
import br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaogeral.QueryConsultaMedicamentosPaciente;
import br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamento.*;
import br.com.ksisolucoes.bo.entradas.dispensacao.dispensacaomedicamentoitem.QueryDispensacaoMedicamentoItemForLiberacaoItem;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.dto.QueryConsultaImpressaoPrescricoesDTOParam;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.dto.QueryConsultaMedicamentosPacienteDTOParam;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.dto.ReceituarioStatusDTO;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade._DispensacaoMedicamentoFacadeLocal;
import br.com.ksisolucoes.bo.entradas.dispensacao.interfaces.facade._DispensacaoMedicamentoFacadeRemote;
import br.com.ksisolucoes.bo.entradas.dispensacao.liberacaoreceita.SalvarLiberacaoReceita;
import br.com.ksisolucoes.bo.materiais.dispensacao.QueryPagerConsultaReceituarioItem;
import br.com.ksisolucoes.bo.materiais.dispensacao.interfaces.dto.QueryPagerConsultaReceituarioItemDTOparam;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoGeral;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.dispensacao.LiberacaoReceita;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;

import javax.ejb.Stateless;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR> Giordani
 */

@Stateless
public class DispensacaoMedicamentoBO extends BOGenericImpl implements _DispensacaoMedicamentoFacadeLocal, _DispensacaoMedicamentoFacadeRemote {

    /**
     * {@inheritDoc}
     */
    @Override
    public void delete(Object vo) throws DAOException, ValidacaoException {
        this.executor.execute(new DeleteDispensacaoMedicamento(vo));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Serializable save(Object vo) throws DAOException, ValidacaoException {
        SaveDispensacaoMedicamento save = (SaveDispensacaoMedicamento) this.executor.executeReturn(new SaveDispensacaoMedicamento(vo));
        return save.getDispensacaoMedicamento();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public final Class getReferenceClass() {
        return DispensacaoMedicamento.class;
    }

    @Override
    public List<DispensacaoMedicamentoItem> getDispensacaoMedicamentoItemForLiberacaoItem(UsuarioCadsus usuarioCadsus, Produto produto) throws DAOException, ValidacaoException {
        QueryDispensacaoMedicamentoItemForLiberacaoItem query = (QueryDispensacaoMedicamentoItemForLiberacaoItem) this.executor.executeReturn(new QueryDispensacaoMedicamentoItemForLiberacaoItem(usuarioCadsus, produto));
        return query.getListDispensacaoMedicamento();
    }

    @Override
    public void gerarDispensacaoEstatistica(Long codigoUsuarioCadsus, List<String> produtos) throws DAOException, ValidacaoException {
        this.executor.execute(new GerarDispensacaoEstatistica(codigoUsuarioCadsus, produtos));
    }

    @Override
    public List<DispensacaoGeral> consultaMedicamentosPaciente(QueryConsultaMedicamentosPacienteDTOParam param) throws DAOException, ValidacaoException {
        return ((QueryConsultaMedicamentosPaciente) this.executor.executeQuery(new QueryConsultaMedicamentosPaciente(param))).getResult();
    }

    @Override
    public List<Receituario> consultaImpressaoPrescricoes(QueryConsultaImpressaoPrescricoesDTOParam param) throws DAOException, ValidacaoException {
        return ((QueryConsultaImpressaoPrescricoes) this.executor.executeQuery(new QueryConsultaImpressaoPrescricoes(param))).getResult();
    }

    @Override
    public Collection<Receituario> queryReceituarioPacienteSemDispensacao(Long param, boolean empresaPossuiAntimicrobiano, Long permiteDispensarSemDataLimite) throws DAOException, ValidacaoException {
        return ((QueryReceituarioPacienteSemDispensacao) this.executor.executeQuery(new QueryReceituarioPacienteSemDispensacao(param, empresaPossuiAntimicrobiano, permiteDispensarSemDataLimite))).getResult();
    }

    @Override
    public DispensacaoMedicamento dispensarPrescricao(DispensacaoMedicamento dispensacaoMedicamento, Receituario receituario) throws DAOException, ValidacaoException {
        return ((DispensarPrescricao) this.executor.executeReturn(new DispensarPrescricao(dispensacaoMedicamento, receituario))).getDispensacaoMedicamento();
    }
    
    @Override
    public DispensacaoMedicamento dispensarPrescricao(DispensacaoMedicamento dispensacaoMedicamento, Receituario receituario, ReceituarioStatusDTO receituarioStatusDTO) throws DAOException, ValidacaoException {
        return ((DispensarPrescricao) this.executor.executeReturn(new DispensarPrescricao(dispensacaoMedicamento, receituario, receituarioStatusDTO))).getDispensacaoMedicamento();
    }

    @Override
    public Receituario setarImpressoReceituario(Receituario receituario) throws DAOException, ValidacaoException {
        return ((SetarImpressoReceituario) this.executor.executeReturn(new SetarImpressoReceituario(receituario))).getObject();
    }
    
     @Override
    public DataPagingResult consultarReceituarioItemPager(DataPaging<QueryPagerConsultaReceituarioItemDTOparam> dataPaging) throws DAOException, ValidacaoException {
        QueryPagerConsultaReceituarioItem command = new QueryPagerConsultaReceituarioItem(dataPaging.getParam());
        command.setDataPaging(dataPaging);
        this.executor.executeReturn(command);
        return command.getDataPagingResult();
    }
    
    @Override
    public void salvarLiberacaoReceita(LiberacaoReceita liberacaoReceita, List<Produto> produtos) throws DAOException, ValidacaoException {
        this.executor.execute(new SalvarLiberacaoReceita(liberacaoReceita, produtos));
    }
    
    @Override
    public List<DispensacaoMedicamentoContaPacienteDTO> consultarDispensacaoAdministracaoMedicamentoProcedimento(Long codigoDispensacaoMedicamento) throws DAOException, ValidacaoException {
        return ((QueryDispensacaoAdministracaoMedicamentoProcedimento) this.executor.executeQuery(new QueryDispensacaoAdministracaoMedicamentoProcedimento(codigoDispensacaoMedicamento))).getResult();
    }
    
    @Override
    public List<DispensacaoMedicamentoContaPacienteDTO> consultarDispensacaoUnidadeCboProcedimento(Long codigoDispensacaoMedicamento) throws DAOException, ValidacaoException {
        return ((QueryDispensacaoUnidadeCboProcedimento) this.executor.executeQuery(new QueryDispensacaoUnidadeCboProcedimento(codigoDispensacaoMedicamento))).getResult();
    }
    
    @Override
    public List<DispensacaoMedicamentoContaPacienteDTO> consultarDispensacoesFaturaveis(Long codigoDispensacaoMedicamento) throws DAOException, ValidacaoException {
        return ((QueryDispensacoesFaturaveis) this.executor.executeQuery(new QueryDispensacoesFaturaveis(codigoDispensacaoMedicamento))).getResult();
    }
}
