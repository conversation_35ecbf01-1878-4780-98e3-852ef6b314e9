package br.com.celk.bo.cache;

import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.log.Loggable;
import org.infinispan.Cache;
import org.infinispan.manager.DefaultCacheManager;

import javax.annotation.PostConstruct;
import javax.ejb.Lock;
import javax.ejb.LockType;
import javax.ejb.Singleton;
import javax.ejb.Startup;
import javax.inject.Inject;
import java.util.concurrent.Callable;

@Singleton
@Startup
public class CacheHelper {
    
    @Inject
    private DefaultCacheManager cacheManager;
    
    @SuppressWarnings("unchecked")
    @Lock(LockType.READ)
    public <T> T getCachedOrBuild(String cacheName, Object key, Callable<T> callable) {
        CacheTenant cacheTenant = new CacheTenant(key, TenantContext.getContext());
        Cache<Object, Object> cache = cacheManager.getCache(cacheName);
        if (cache.containsKey(cacheTenant)) {
            return (T) cache.get(cacheTenant);
        }
        try {
            T callbackResult = callable.call();
            cache.put(cacheTenant, callbackResult);
            return callbackResult;
        } catch (Exception e) {
            Loggable.log.error(e.getLocalizedMessage(), e);
        }
        return null;
    }

    @PostConstruct
    public void initCaches() {
        for(CacheType definition: CacheType.values()) {
            cacheManager.getCache(definition.name());
        }
    }
}
