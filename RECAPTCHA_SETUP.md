# Configuração do Google reCAPTCHA

Este documento descreve como configurar o Google reCAPTCHA na aplicação para prevenir acesso automatizado por bots na página de consulta de medicamentos públicos.

## Implementação Realizada

### 1. Componentes Criados

- **ReCaptchaComponent.java**: Componente Wicket customizado para integração com Google reCAPTCHA
- **ReCaptchaComponent.html**: Template HTML do componente
- **ReCaptchaUtil.java**: Classe utilitária para validação do reCAPTCHA via API do Google

### 2. Modificações na Página

- **ConsultaMedicamentoPublicoPage.java**: Adicionado componente reCAPTCHA e validação no método `antesProcurar()` antes da consulta
- **ConsultaMedicamentoPublicoPage.html**: Incluído o componente reCAPTCHA no formulário

### 3. Dependências

- Adicionada dependência `gson` no `pom.xml` para processamento JSON

## Configuração Necessária

### 1. Obter Chaves do Google reCAPTCHA

1. Acesse [Google reCAPTCHA Admin Console](https://www.google.com/recaptcha/admin)
2. Crie um novo site com as seguintes configurações:
   - **Tipo**: reCAPTCHA v2 ("I'm not a robot" Checkbox)
   - **Domínios**: Adicione os domínios onde a aplicação será executada
3. Anote as chaves geradas:
   - **Site Key** (chave pública)
   - **Secret Key** (chave privada)

### 2. Configurar Parâmetros no Sistema

No sistema, configure os seguintes parâmetros no módulo SISTEMA:

```sql
-- Inserir parâmetros do reCAPTCHA
INSERT INTO parametro_sistema (modulo, nome, valor, descricao) VALUES 
('SISTEMA', 'GoogleReCaptchaSiteKey', 'SUA_SITE_KEY_AQUI', 'Chave pública do Google reCAPTCHA'),
('SISTEMA', 'GoogleReCaptchaSecretKey', 'SUA_SECRET_KEY_AQUI', 'Chave privada do Google reCAPTCHA');
```

**Importante**: Substitua `SUA_SITE_KEY_AQUI` e `SUA_SECRET_KEY_AQUI` pelas chaves reais obtidas do Google.

### 3. Verificar Configuração

1. Acesse a página de consulta de medicamentos: `/consulta-medicamento`
2. Verifique se o reCAPTCHA aparece no formulário
3. Teste a funcionalidade:
   - Tente fazer uma consulta sem completar o reCAPTCHA (deve mostrar erro)
   - Complete o reCAPTCHA e faça uma consulta (deve funcionar normalmente)

## Funcionamento

### Fluxo de Validação

1. **Carregamento da Página**: O componente reCAPTCHA é carregado com a Site Key
2. **Interação do Usuário**: Usuário completa o desafio reCAPTCHA
3. **Submissão do Formulário**: Ao clicar "Procurar", o sistema valida:
   - Se o reCAPTCHA foi completado
   - Se a resposta é válida (via API do Google)
4. **Execução da Consulta**: Apenas se a validação for bem-sucedida

### Segurança

- **Validação Server-Side**: A validação é feita no servidor usando a Secret Key
- **Proteção contra Replay**: Cada resposta do reCAPTCHA é válida apenas uma vez
- **Rate Limiting**: O Google reCAPTCHA possui proteções contra abuso

## Troubleshooting

### reCAPTCHA não aparece

1. Verifique se a Site Key está configurada corretamente
2. Verifique se o domínio está autorizado no Google reCAPTCHA Admin
3. Verifique o console do navegador para erros JavaScript

### Validação sempre falha

1. Verifique se a Secret Key está configurada corretamente
2. Verifique a conectividade com `https://www.google.com/recaptcha/api/siteverify`
3. Verifique os logs da aplicação para erros detalhados

### Erro "Chave não configurada"

1. Verifique se os parâmetros foram inseridos no banco de dados
2. Verifique se o módulo está correto (SISTEMA)
3. Reinicie a aplicação após inserir os parâmetros

## Desabilitação Temporária

Se necessário desabilitar temporariamente o reCAPTCHA:

1. Remova ou comente as chaves dos parâmetros do sistema
2. O componente detectará automaticamente e não exibirá o reCAPTCHA
3. A validação será ignorada

## Monitoramento

- Monitore os logs da aplicação para tentativas de bypass
- Acompanhe as estatísticas no Google reCAPTCHA Admin Console
- Considere implementar alertas para falhas de validação excessivas
