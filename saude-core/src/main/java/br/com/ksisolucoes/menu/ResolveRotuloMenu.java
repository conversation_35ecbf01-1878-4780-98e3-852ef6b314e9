/**
 *
 */
package br.com.ksisolucoes.menu;

import br.com.ksisolucoes.util.Bundle;

/**
 * <AUTHOR>
 *
 */
public class ResolveRotuloMenu {

    public static String resolve(IRotulo target) {
        if( target.getRotulo() != null && !target.getRotulo().trim().equals( "" )) {
            return Bundle.getStringMenu( target.getRotulo() );
        } else {
            return target.getRotuloAlternativo();
        }
    }

}
