package br.com.ksisolucoes.dao.hibernate;

import java.io.Serializable;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Types;

import org.apache.commons.lang.StringUtils;
import org.hibernate.HibernateException;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.usertype.UserType;


/**
 * <AUTHOR>
 *
 * Resolve a tipagem char para o Oracle.
 * Tipo criado para resolver problemas de consultas relacionadas a bancos com
 *  comparao de char que obrigam a passagem 
 */
public class ResolveCharType implements UserType {
    
    public int[] sqlTypes() {
        return new int[] { Types.CHAR };
    }
    
    public Class returnedClass() {
        return String.class;
    }
    
    public boolean equals( Object x, Object y ) throws HibernateException {
        return ( x == y ) || ( x != null && y != null && ( x.equals( y ) ) );
    }
    
    public Object nullSafeGet( ResultSet rs, String[] names, Object owner ) throws HibernateException, SQLException {
        String val = rs.getString( names[0] );
        if ( null == val ) {
            return null;
        } else {
            String trimmed = StringUtils.stripEnd( val, " " );
            return trimmed;
        }
    }
    
    public void nullSafeSet( PreparedStatement st, Object value, int index ) throws HibernateException, SQLException {
        st.setString( index, ( String ) value );
    }
    
    public Object deepCopy( Object value ) throws HibernateException {
        if ( value == null )
            return null;
        return new String( ( String ) value );
    }
    
    public boolean isMutable() {
        return false;
    }
    
    public int hashCode( Object x ) throws HibernateException {
        return x.hashCode();
    }

    public Serializable disassemble(Object value) throws HibernateException {
        return (Serializable) value;
    }

    public Object assemble(Serializable cached, Object owner) throws HibernateException {
        return cached;
    }
    
    public Object replace( Object original, Object target, Object owner ) throws HibernateException {
        return original;
    }

    @Override
    public Object nullSafeGet(ResultSet rs, String[] strings, SessionImplementor si, Object o) throws HibernateException, SQLException {
        throw new UnsupportedOperationException("Not supported yet.");
    }

    @Override
    public void nullSafeSet(PreparedStatement ps, Object o, int i, SessionImplementor si) throws HibernateException, SQLException {
        throw new UnsupportedOperationException("Not supported yet.");
    }

}