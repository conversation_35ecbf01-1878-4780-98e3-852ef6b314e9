/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.dao.paginacao;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class DataPagingImpl<T> implements DataPaging<T> {

    private List list;

    private T param;

    private int amountResults;

    private int firstResult;

    private int maxResult;

    private Type type;

    private Map mapClientProperty;

    public DataPagingImpl(Type type) {
        this.type = type;
    }

    public List getList() {
        return this.list;
    }

    public int getAmountResults() {
        return this.amountResults;
    }

    public int getFirstResult() {
        return this.firstResult;
    }

    public int getMaxResult() {
        return this.maxResult;
    }

    public void setFirstResult(int value) {
        this.firstResult = value;
    }

    public void setMaxResult(int value) {
        this.maxResult = value;
    }

    public void setType(Type type) {
        this.type = type;
    }

    public Type getType() {
        return type;
    }

    public T getParam() {
        return this.param;
    }

    public void setParam(T param) {
        this.param = param;
    }

    public void putClientProperty(Object key, Object value) {
        if (this.mapClientProperty == null) {
            this.mapClientProperty = new HashMap();
        }
        this.mapClientProperty.put(key, value);
    }

    public Object getClientProperty(Object key) {
        if (this.mapClientProperty != null) {
            return this.mapClientProperty.get(key);
        }
        return null;
    }

}
