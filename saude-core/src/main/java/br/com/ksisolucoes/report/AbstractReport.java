package br.com.ksisolucoes.report;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.bo.command.ITransferDataReport;
import br.com.ksisolucoes.bo.server.interfaces.facade.HibernateSessionFactoryFacade;
import br.com.ksisolucoes.command.CommandCustomSession;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.report.datasource.CollectionDataSource;
import br.com.ksisolucoes.report.datasource.MapDataSource;
import br.com.ksisolucoes.report.datasource.ReportDataSource;
import br.com.ksisolucoes.report.datatransfer.CollectionReportDataTransfer;
import br.com.ksisolucoes.report.datatransfer.JasperReportDataTransfer;
import br.com.ksisolucoes.report.datatransfer.ReportDataTransfer;
import br.com.ksisolucoes.report.embededreportconfig.ReportBuildInParametersConfig;
import br.com.ksisolucoes.report.embededreportconfig.ReportDescriptionConfig;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.AbstractSessaoAplicacao;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.descricaoparametro.DescricaoParametroManager;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.hibernate.Session;

import java.util.*;

/**
 * Report
 *
 * <AUTHOR>
 */
public abstract class AbstractReport<T> implements Report {

    private final Map<String, Object> mapParameters = new LinkedHashMap<String, Object>();
    private final Map<String, String> mapReportDescriptions = new LinkedHashMap<String, String>();
    private List<ReportConfig> configurations;
    public T param;
    protected AbstractSessaoAplicacao sessao;
    public boolean usarDbLeitura = false;
    public boolean isDinamico = false;
    public boolean limitarResultado = true;

    public AbstractReport() {
        this.sessao = SessaoAplicacaoImp.getInstance();
    }

    public AbstractReport(T param) {
        this();
        this.param = param;
    }
    public AbstractReport(T param, boolean limitarResultado) {
        this();
        this.param = param;
        this.limitarResultado = limitarResultado;
    }

    public AbstractSessaoAplicacao getSessao() {
        return sessao;
    }

    public void setUsarDbLeitura(boolean usarDbLeitura) {
        this.usarDbLeitura = usarDbLeitura;
    }

    public abstract String getXML();

    protected void customDTOParam(T param) throws ValidacaoException {
    }

    @Override
    public LinkedHashMap<String, Object> getMapeamentoPlanilha() {
        return null;
    }

    @Override
    public ReportDataTransfer getDataSource() throws ReportException {
        try {
            customReport();
            if (TipoRelatorio.PDF_TEMPLATE.equals(getTipoRelatorio())) {

                Collection collection = null;
                customDTOParam(param);

                if (collection == null) {
                    collection = getCollection();
                }
                if (collection == null) {
                    collection = getMapList();
                }
                if (collection == null) {
                    collection = executeQuery();
                }

                return new CollectionReportDataTransfer(customizeCollection(collection));

            } else if (TipoRelatorio.XLS2.equals(getTipoRelatorio())
                    || TipoRelatorio.CSV.equals(getTipoRelatorio())) {
                Collection collection = getCollection();
                List<Map<String, ?>> maps = getMapList();
                customDTOParam(param);

                if (collection == null) {
                    collection = executeQuery();

                } else if (maps != null) {
                    throw new ValidacaoException("Não implementado");
                }

                return new CollectionReportDataTransfer(customizeCollection(collection));

            } else {
                Collection collection = null;
                if (collection == null) {
                    collection = getCollection();
                }
                customDTOParam(param);
                List<Map<String, ?>> maps = getMapList();
                ReportDataSource reportDataSource = null;
                if (collection != null) {
                    reportDataSource = new CollectionDataSource(customizeCollection(collection)).setMapParameters(mapParameters);
                } else if (maps != null) {
                    reportDataSource = new MapDataSource(maps).setMapParameters(mapParameters);
                } else {
                    reportDataSource = new CollectionDataSource(customizeCollection(executeQuery())).setMapParameters(mapParameters);
                }

                return new JasperReportDataTransfer(reportDataSource);
            }
        } catch (DAOException | ValidacaoException ex) {
            throw new ReportException(ex);
        }
    }

    private Collection executeQuery() throws DAOException, ValidacaoException {
        if (limitarResultado && limiteResultadosRelatorios() != null && limiteResultadosRelatorios() > 0L) {
            return executeQuery(limiteResultadosRelatorios());
        }

        ITransferDataReport dataReport = getQuery();
        dataReport.setDTOParam(param);
        if (dataReport instanceof CommandCustomSession && usarDbLeitura) {//Relatorio asyncrono
            //executar relatorios em base exclusiva de leitura
            Session s = BOFactory.getBO(HibernateSessionFactoryFacade.class).getSessionLeitura();
            ((CommandCustomSession) dataReport).start(s);
        } else {
            dataReport.start();
        }
        return dataReport.getResult();
    }

    private Collection executeQuery(Long maxResult) throws DAOException, ValidacaoException {
        ITransferDataReport dataReport = getQuery();
        dataReport.setDTOParam(param);
        if (dataReport instanceof CommandCustomSession && usarDbLeitura) {//Relatorio asyncrono
            //executar relatorios em base exclusiva de leitura
            Session session = BOFactory.getBO(HibernateSessionFactoryFacade.class).getSessionLeitura();
            ((CommandCustomSession) dataReport).start(session, maxResult + 1);
        } else {
            ((CommandCustomSession) dataReport).start(maxResult + 1);
        }
        return dataReport.getResult();
    }

    private Long limiteResultadosRelatorios() {
        return CargaBasicoPadrao.getInstance().getParametroPadrao().getLimiteResultadosRelatorios();
    }

    protected Collection customizeCollection(Collection collection) {
        return collection;
    }

    public ITransferDataReport getQuery() throws ValidacaoException {
        return null;
    }

    public Collection getCollection() throws DAOException, ValidacaoException {
        return null;
    }

    public List<Map<String, ?>> getMapList() throws DAOException, ValidacaoException {
        return null;
    }

    public void addParametro(String name, Object value) {
        mapParameters.put(name, value);
    }

    public void addDescricaoParametro(Object descricaoParametroObject) {
        this.mapReportDescriptions.putAll(DescricaoParametroManager.build(descricaoParametroObject));
    }

    public void addDescricaoParametro(String name, String value) {
        mapReportDescriptions.put(name, value);
    }

    public List<ReportConfig> getListReportConfiguration() {
        List<ReportConfig> _configurations = new ArrayList<ReportConfig>();
        _configurations.add(getReportBuildInParametersConfig());
        _configurations.add(getReportDescriptionConfig());
        _configurations.addAll(getConfigurations());

        return _configurations;
    }

    public List<ReportConfig> getConfigurations() {
        if (this.configurations == null) {
            this.configurations = new ArrayList<ReportConfig>();
        }
        return configurations;
    }

    public void addReportConfiguration(ReportConfig reportConfiguration) {
        getConfigurations().add(reportConfiguration);
    }

    public ReportConfig getReportBuildInParametersConfig() {
        return new ReportBuildInParametersConfig(getSessao(), getTitulo());
    }

    public ReportConfig getReportDescriptionConfig() {
        Map<String, String> _mapReportDescriptions = new HashMap<String, String>();

        if (this.param != null) {
            Map temp = DescricaoParametroManager.build(this.param);
            if (!temp.isEmpty()) {
                _mapReportDescriptions.putAll(temp);
            }
        }

        _mapReportDescriptions.putAll(this.mapReportDescriptions);

        return new ReportDescriptionConfig(_mapReportDescriptions);
    }

    public T getParam() {
        return param;
    }

    protected void customReport() throws DAOException, ValidacaoException {
    }

    public Map<String, Object> getMapParameters() {
        return mapParameters;
    }

    @Override
    public TipoRelatorio getTipoRelatorio() {
        return TipoRelatorio.PDF;
    }

    @Override
    public void processamentoInicial() {
    }

    @Override
    public Boolean isDinamico() {
        return isDinamico;
    }
}
