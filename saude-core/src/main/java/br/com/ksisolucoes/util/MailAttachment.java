package br.com.ksisolucoes.util;

import java.io.File;

import br.com.ksisolucoes.vo.service.IMailAttachment;

/**
 *
 * <AUTHOR>
 */
public class MailAttachment implements IMailAttachment {
    private File file;
    private String extension;
    private String name;
    private String cid;
    private byte[] byteArray;

    public MailAttachment(File file, String extension) {
        this.file = file;
        this.extension = extension;
    }

    public MailAttachment(File file, String extension, String name) {
        this.file = file;
        this.extension = extension;
        this.name = name;
    }

    public MailAttachment(String cid, byte[] byteArray, String name, String extension) {
        this.cid = cid;
        this.byteArray = byteArray;
        this.name = name;
        this.extension = extension;
    }

    @Override
    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    @Override
    public String getExtension() {
        return extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    @Override
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public byte[] getByteArray() {
        return byteArray;
    }

    public void setByteArray(byte[] byteArray) {
        this.byteArray = byteArray;
    }

    @Override
    public String getCid() {
        return this.cid;
    }

    public void setCid(String cid) {
        this.cid = cid;
    }

}
