/*
 * Bundle.java
 *
 * Created on 31 de Maio de 2004, 08:59
 */

package br.com.ksisolucoes.util;

import java.util.Locale;

import javax.swing.event.EventListenerList;

import br.com.ksisolucoes.util.i18n.*;
import org.apache.commons.lang.SystemUtils;

import br.com.ksisolucoes.events.BundleEvent;
import br.com.ksisolucoes.events.BundleListener;
import br.com.ksisolucoes.util.exception.BundleException;
import br.com.ksisolucoes.util.log.Loggable;

/**
 *
 * <AUTHOR>
 */
public class Bundle implements Loggable, ApplicationMessageBundleInterface, MenuMessageBundleInterface, SwingMessageBundleInterface, BusinessObjectMessageBundleInterface, ValueObjectMessageBundleInterface, PersistenceMessageBundleInterface, ComponentsMessageBundleInterface, GuiasTissApplicationMessageBundleInterface, ProjectPropertiesInterface {

    public static final Locale BRASIL = new Locale( "pt", "BR" );

    public static final Locale US = Locale.US;

    public static final Locale MEXICO = new Locale( "es", "MX" );

    public static Locale LOCALE = Locale.getDefault();

    private static EventListenerList listenerList = new EventListenerList();

    public static final Locale getLocale(){
        return LOCALE;
    }

    public static final void setLocale( Locale newLocale ) throws BundleException{
        try{
            if ( !newLocale.equals( LOCALE ) ) {
                Locale oldLocale = getLocale();
                log.info("Modificando locale... Antigo: " + oldLocale + " - Novo: " + newLocale);

                Locale.setDefault( newLocale );
                LOCALE = newLocale;
                fireLocaleChanged( oldLocale, newLocale);
            }
        } catch ( SecurityException e ){
            log.error( "SecurityException ao tentar modificar locale.", e);
            new BundleException( getStringApplication( "msg_bundle_security" ) );
        } catch ( NullPointerException e ){
            log.error( "NullPointerException ao tentar modificar locale.", e);
            new BundleException( getStringApplication( "msg_bundle_invalido" ) );
        }

    }

    /**
     * Dispara o evento <code>BundleEvent</code> a todos os ouvintes
     * publicados. Mais especificamente, este dispara o evento de modificao de
     * <code>Locale</code>.
     *
     * @param oldLocale
     *            o <code>Locale</code> anterior a atualizao
     * @param newLocale
     *            o novo <code>Locale</code> informado
     * @since 1.0
     * <AUTHOR> Graciano
     */
    private static void fireLocaleChanged(Locale oldLocale, Locale newLocale) {
        // Guaranteed to return a non-null array
        Object[] listeners = listenerList.getListenerList();
        BundleEvent e = null;
        // Process the listeners last to first, notifying
        // those that are interested in this event
        for (int i = listeners.length - 2; i >= 0; i -= 2) {
            if (listeners[i] == BundleListener.class) {
                // Lazily create the event:
                if (e == null) {
                    e = new BundleEvent(oldLocale, newLocale);
                }
                ((BundleListener) listeners[i + 1]).localeChanged(e);
            }
        }
    }

    /**
     * Adiciona uma <code>BundleListener</code> a esta classe. Deve-se ter
     * cuidado com esta operao por esta classe ser esttica, ento, no momento
     * que este evento no for mais necessrio, deve-se removelo, utilizando o
     * mtodo <code>removeBundleListener</code>.
     *
     * @see removeBundleListener
     * @param bundleListener
     * @since 1.0
     * <AUTHOR> Graciano
     */
    public static void addBundleListener(BundleListener bundleListener) {
        listenerList.add(BundleListener.class, bundleListener);
    }

    /**
     * Remove os <code>BundleListener</code> s adicionados a esta classe. Deve
     * sempre ser utilizada aps a no necessidade de utilizao do evento.
     *
     * @see addBundleListener
     * @param bundleListener
     * @since 1.0
     * <AUTHOR> Graciano
     */
    public static void removeBundleListener(BundleListener bundleListener) {
        listenerList.remove(BundleListener.class, bundleListener);
    }

    /**
     * Retorna um array com todos os <code>BundleListener</code> s adicionados
     * ao Bundle com o mtodo addBindleListener().
     *
     * @return todos os <code>BundleListener</code> s adicionados ou um array
     *         vazio se no houverem listeners adicionados.
     * @since 1.0
     * <AUTHOR> Graciano
     */
    public static BundleListener[] getBundleListeners() {
        return (BundleListener[]) (listenerList
                .getListeners(BundleListener.class));
    }

    /**
     * Retorna uma <code>String</code> com base na chave informada. Esta <code>String</code>  armazanada em um arquivo de resource bumdle
     * chamado application.properties. O <code>Locale</code> utilizado tambm  informado.
     * @return
     * @param locale
     * @param key
     */
    public static final String getStringApplication( String key, Locale locale ){
        try{
            return application.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception", locale ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    public static final String getStringComponents( String key, Locale locale ){
        try{
            return components.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception", locale ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     * Retorna uma <code>String</code> com base na chave informada. Esta <code>String</code>  armazanada em um arquivo de resource bumdle
     * chamado menu.properties. O <code>Locale</code> utilizado tambm  informado.
     * @return
     * @param locale
     * @param key
     */
    public static final String getStringMenu( String key, Locale locale ){
        try{
            return menu.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception", locale ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     * Retorna uma <code>String</code> com base na chave informada. Esta <code>String</code>  armazanada em um arquivo de resource bumdle
     * chamado application.properties. O <code>Locale</code> utilizado  o padro do sistema no momento da chamada.
     * @param key
     * @return
     */
    public static final String getStringApplication( String key ){
        return getStringApplication( key, LOCALE );
    }

    public static final String getStringComponents( String key ){
        return getStringComponents( key, LOCALE );
    }

    /**
     * Retorna uma <code>String</code> com base na chave informada. Esta <code>String</code>  armazanada em um arquivo de resource bumdle
     * chamado menu.properties. O <code>Locale</code> utilizado  o padro do sistema no momento da chamada.
     * @param key
     * @return
     */
    public static final String getStringMenu( String key ){
        return getStringMenu( key, LOCALE );
    }

    /**
     *
     * @return
     * @param locale
     * @param key
     * @param params
     */
    public static final String getStringApplication( String key, Object[] params, Locale locale ){
        try{
            return application.getMessage( key, params, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @param params
     * @return
     */
    public static final String getStringApplication( String key, Object... params ){
        return getStringApplication( key, params, LOCALE );
    }
    
    /**
     *
     * @return
     * @param locale
     * @param key
     * @param param
     */
    public static final String getStringApplication( String key, Object param, Locale locale ){
        Object[] params = new Object[] { param };
        return getStringApplication( key, params, locale );
    }

    /**
     *
     * @param key
     * @param param
     * @return
     */
    public static final String getStringApplication( String key, Object param ){
        return getStringApplication( key, param, LOCALE );
    }

    public static final String getStringGuiasTiss( String key ){
        return getStringGuiasTiss( key, LOCALE );
    }

    public static final String getStringGuiasTiss( String key, Locale locale ){
        try{
            return guiasTiss.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception", locale ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    public static final String getStringGuiasTiss( String key, Object param ){
        return getStringGuiasTiss( key, param, LOCALE );
    }

    public static final String getStringGuiasTiss( String key, Object param, Locale locale ){
        Object[] params = new Object[] { param };
        return getStringGuiasTiss( key, params, locale );
    }

    public static final String getStringGuiasTiss( String key, Object[] params, Locale locale ){
        try{
            return guiasTiss.getMessage( key, params, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    public static final String getStringProject( String key ){
        return getStringProject( key, LOCALE );
    }

    public static final String getStringProject( String key, Locale locale ){
        try{
            return project.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception", locale ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    public static final String getStringProject( String key, Object param ){
        return getStringProject( key, param, LOCALE );
    }

    public static final String getStringProject( String key, Object param, Locale locale ){
        Object[] params = new Object[] { param };
        return getStringProject( key, params, locale );
    }

    public static final String getStringProject( String key, Object[] params, Locale locale ){
        try{
            return project.getMessage( key, params, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @return
     */
    public static final String getStringSwing( String key, Locale locale ){
        try{
            return swing.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @return
     */
    public static final String getStringSwing( String key ){
        return getStringSwing( key, LOCALE );
    }

    /**
     *
     * @param key
     * @param params
     * @param locale
     * @return
     */
    public static final String getStringSwing( String key, Object[] params, Locale locale ){
        try{
            return swing.getMessage( key, params, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @param params
     * @return
     */
    public static final String getStringSwing( String key, Object[] params ){
        return getStringSwing( key, params, LOCALE );
    }

    /**
     *
     * @param key
     * @param param
     * @param locale
     * @return
     */
    public static final String getStringSwing( String key, Object param, Locale locale ){
        Object[] params = new Object[] { param };
        return getStringSwing( key, params, locale );
    }

    /**
     *
     * @param key
     * @param param
     * @return
     */
    public static final String getStringSwing( String key, Object param ){
        return getStringSwing( key, param, LOCALE );
    }

    /**
     *
     * @param key
     * @param locale
     * @return
     */
    public static final String getStringBO( String key, Locale locale ){
        try{
            return bo.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @return
     */
    public static final String getStringBO( String key ){
        return getStringBO( key, LOCALE );
    }

    /**
     *
     * @param key
     * @param params
     * @param locale
     * @return
     */
    public static final String getStringBO( String key, Object[] params, Locale locale ){
        try{
            return bo.getMessage( key, params, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @param params
     * @return
     */
    public static final String getStringBO( String key, Object[] params ){
        return getStringBO( key, params, LOCALE );
    }

    /**
     *
     * @param key
     * @param param
     * @param locale
     * @return
     */
    public static final String getStringBO( String key, Object param, Locale locale ){
        Object[] params = new Object[] { param };
        return getStringBO( key, params, locale );
    }

    /**
     *
     * @param key
     * @param param
     * @return
     */
    public static final String getStringBO( String key, Object param ){
        return getStringBO( key, param, LOCALE );
    }

    /**
     *
     * @param key
     * @param locale
     * @return
     */
    public static final String getStringVO( String key, Locale locale ){
        try{
            return vo.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @return
     */
    public static final String getStringVO( String key ){
        return getStringVO( key, LOCALE );
    }

    /**
     *
     * @param key
     * @param params
     * @param locale
     * @return
     */
    public static final String getStringVO( String key, Object[] params, Locale locale ){
        try{
            return vo.getMessage( key, params, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @param params
     * @return
     */
    public static final String getStringVO( String key, Object[] params ){
        return getStringVO( key, params, LOCALE );
    }

    /**
     *
     * @param key
     * @param param
     * @param locale
     * @return
     */
    public static final String getStringVO( String key, Object param, Locale locale ){
        Object[] params = new Object[] { param };
        return getStringVO( key, params, locale );
    }

    /**
     *
     * @param key
     * @param param
     * @return
     */
    public static final String getStringVO( String key, Object param ){
        return getStringVO( key, param, LOCALE );
    }

    /**
     *
     * @param key
     * @param locale
     * @return
     */
    public static final String getStringPersistence( String key, Locale locale ){
        try{
            return persistence.getMessage( key, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @return
     */
    public static final String getStringPersistence( String key ){
        return getStringPersistence( key, LOCALE );
    }

    /**
     *
     * @param key
     * @param params
     * @param locale
     * @return
     */
    public static final String getStringPersistence( String key, Object[] params, Locale locale ){
        try{
            return persistence.getMessage( key, params, Coalesce.asLocale( locale ) );
        } catch ( Exception e ){
            return getStringApplication( "msg_resource_bundle_exception" ) + SystemUtils.LINE_SEPARATOR + "(" + key + ")";
        }
    }

    /**
     *
     * @param key
     * @param params
     * @return
     */
    public static final String getStringPersistence( String key, Object[] params ){
        return getStringPersistence( key, params, LOCALE );
    }

    /**
     *
     * @param key
     * @param param
     * @param locale
     * @return
     */
    public static final String getStringPersistence( String key, Object param, Locale locale ){
        Object[] params = new Object[] { param };
        return getStringPersistence( key, params, locale );
    }

    /**
     *
     * @param key
     * @param param
     * @return
     */
    public static final String getStringPersistence( String key, Object param ){
        return getStringPersistence( key, param, LOCALE );
    }
}
