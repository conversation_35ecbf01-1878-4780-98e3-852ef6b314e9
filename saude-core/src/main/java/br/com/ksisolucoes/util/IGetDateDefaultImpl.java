package br.com.ksisolucoes.util;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

public class IGetDateDefaultImpl implements Serializable {
    
    public Date getDate() {
        GregorianCalendar calendar = new GregorianCalendar();
//        calendar.setTimeZone(Data.TZ_BRASIL);
        return new Date(calendar.getTimeInMillis()) {
            public String toString() {
                DateFormat formatter = new SimpleDateFormat("EEE MMM dd HH:mm zzz yyyy");
                
                synchronized (formatter) {
                    formatter.setTimeZone(TimeZone.getDefault());
                    return formatter.format(this);
                }
            }
        };
    }
    
}
