/*
 *                 Sun Public License Notice
 *
 * The contents of this file are subject to the Sun Public License
 * Version 1.0 (the "License"). You may not use this file except in
 * compliance with the License. A copy of the License is available at
 * http://www.sun.com/
 *
 * The Original Code is NetBeans. The Initial Developer of the Original
 * Code is Sun Microsystems, Inc. Portions Copyright 1997-2004 Sun
 * Microsystems, Inc. All Rights Reserved.
 */

package br.com.ksisolucoes.util;

import br.com.ksisolucoes.util.log.Loggable;
import java.awt.Component;
import java.awt.Image;
import java.awt.MediaTracker;
import java.awt.Toolkit;
import java.awt.image.ImageObserver;
import java.lang.ref.*;
import java.util.*;

/** Registers all loaded images into the AbstractNode, so nothing is loaded twice.
 *
 * <AUTHOR>
 */
final class IconManager extends Object {
    
    /** map of resource name to loaded icon (String, SoftRefrence (Image)) or (String, NO_ICON) */
    private static final HashMap map = new HashMap();
    
    /**
     * Key used for composite images -- it holds image identities
     */
    private static class CompositeImageKey {
        Image baseImage, overlayImage;
        int x, y;
        
        CompositeImageKey(Image base, Image overlay, int x, int y) {
            this.x = x;
            this.y = y;
            this.baseImage = base;
            this.overlayImage = overlay;
        }
        
        public boolean equals(Object other) {
            if (!(other instanceof CompositeImageKey))
                return false;
            CompositeImageKey k = (CompositeImageKey)other;
            return (x == k.x) && (y == k.y) && (baseImage == k.baseImage) &&
                    (overlayImage == k.overlayImage);
        }
        
        public int hashCode() {
            int hash =  ((x << 3) ^ y) << 4;
            hash = hash ^ baseImage.hashCode() ^ overlayImage.hashCode();
            return hash;
        }
        
        public String toString() {
            return "Composite key for " + baseImage + " + " + overlayImage + " at [" + x + ", " + y + "]"; // NOI18N
        }
    }
    
    /**
     * Method that attempts to find the merged image in the cache first, then
     * creates the image if it was not found.
     */
    static final Image mergeImages(Image im1, Image im2, int x, int y) {
        CompositeImageKey k = new CompositeImageKey(im1, im2, x, y);
        Image cached;
        
        synchronized (map) {
            Reference r = (Reference)map.get(k);
            if (r != null) {
                cached = (Image)r.get();
                if (cached != null)
                    return cached;
            }
            cached = doMergeImages(im1, im2, x, y);
            map.put(k, new ActiveRef(cached, map, k));
            return cached;
        }
    }
    
    private static final Component component = new Component() {};
    private static final MediaTracker tracker = new MediaTracker(component);
    private static int mediaTrackerID;
    
    private static void ensureLoaded(Image image) {
        if ((Toolkit.getDefaultToolkit().checkImage(image, -1, -1, null) &
                (ImageObserver.ALLBITS | ImageObserver.FRAMEBITS)) != 0) return;
        
        synchronized(tracker) {
            int id = ++mediaTrackerID;
            
            tracker.addImage(image, id);
            try {
                tracker.waitForID(id, 0);
            } catch (InterruptedException e) {
                Loggable.log.error("INTERRUPTED while loading Image");
                Loggable.log.error(e);
            }
            assert (tracker.statusID(id, false) == MediaTracker.COMPLETE) : "Image loaded";
            tracker.removeImage(image, id);
        }
    }
    
    private static final Image doMergeImages(Image image1, Image image2, int x, int y) {
        ensureLoaded(image1);
        ensureLoaded(image2);
        
        int w = Math.max(image1.getWidth(null), x+image2.getWidth(null));
        int h = Math.max(image1.getHeight(null), y+image2.getHeight(null));
        
        java.awt.image.ColorModel model = java.awt.GraphicsEnvironment.getLocalGraphicsEnvironment().
                getDefaultScreenDevice().getDefaultConfiguration().
                getColorModel(java.awt.Transparency.BITMASK);
        java.awt.image.BufferedImage buffImage = new java.awt.image.BufferedImage(model,
                model.createCompatibleWritableRaster(w, h), model.isAlphaPremultiplied(), null);
        
        java.awt.Graphics g = buffImage.createGraphics();
        g.drawImage(image1, 0, 0, null);
        g.drawImage(image2, x, y, null);
        g.dispose();
        
        return buffImage;
    }
    
    /** Cleaning reference. */
    private static final class ActiveRef extends SoftReference implements Runnable {
        private Map holder;
        private Object key;
        
        public ActiveRef(Object o, Map holder, Object key) {
            super(o, activeReferenceQueue());
            this.holder = holder;
            this.key = key;
        }
        
        public void run() {
            synchronized (holder) {
                holder.remove(key);
            }
        }
    } // end of ActiveRef
    
    /** variable holding the activeReferenceQueue */
    private static ReferenceQueue activeReferenceQueue;
    
    /** Useful queue for all parts of system that use java.lang.ref.References
     * together with some ReferenceQueue and need to do some clean up
     * when the reference is enqued. Usually, in order to be notified about that, one
     * needs to either create a dedicated thread that blocks on the queue and is
     * Object.notify-ed, which is the right approach but consumes
     * valuable system resources (threads) or one can peridically check the content
     * of the queue by RequestProcessor.Task.schedule which is
     * completelly wrong, because it wakes up the system every (say) 15 seconds.
     * In order to provide useful support for this problem, this queue has been
     * provided.
     *
     *
     * If you have a reference that needs clean up, make it implement Runnable * inteface and register it with the activeReferenceQueue: *
     *
     * class MyReference extends WeakReference implements Runnable {
     *   private Object dataToCleanUp;
     *
     *   public MyReference (Object ref, Object data) {
     *     super (ref, Utilities.activeReferenceQueue ()); // here you specify the queue
     *     dataToCleanUp = data;
     *   }
     *
     *   public void run () {
     *     // clean up your data
     *   }
     * }
     *
     *
     *
     * When the ref object is garbage collected, your run method
     * will be invoked by calling
     * ((Runnable)reference).run ()
     * and you can perform what ever cleanup is necessary. Be sure not to block
     * in such cleanup for a long time as this prevents other waiting references
     * to cleanup themselves.
     *
     *
     * Please do not call any methods of the ReferenceQueue yourself. They * will throw exceptions. * * @since 3.11 */
    public static synchronized ReferenceQueue activeReferenceQueue() {
        if (activeReferenceQueue == null) {
            activeReferenceQueue = new ActiveQueue(false);
        }
        return activeReferenceQueue;
    }
    
    /** Implementation of the active queue.
     */
    private static final class ActiveQueue extends ReferenceQueue
            implements Runnable {
        private boolean running;
        private boolean deprecated;
        
        public ActiveQueue(boolean deprecated) {
            this.deprecated = deprecated;
            
            Thread t = new Thread(this, "Active Reference Queue Daemon"); // NOI18N
            t.setPriority(Thread.MIN_PRIORITY);
            t.setDaemon(true); // to not prevent exit of VM
            t.start();
        }
        
        
        public Reference poll() {
            throw new java.lang.UnsupportedOperationException();
        }
        
        public Reference remove(long timeout) throws IllegalArgumentException, InterruptedException {
            throw new java.lang.InterruptedException();
        }
        
        public Reference remove() throws InterruptedException {
            throw new java.lang.InterruptedException();
        }
        
        /** Called either from Thread.run or RequestProcessor.post. In first case
         * calls scanTheQueue (only once) in the second and nexts calls cleanTheQueue
         */
        public void run() {
            synchronized (this) {
                if (running) {
                    return;
                }
                running = true;
            }
            
            for (;;) {
                try {
                    Reference ref = super.remove(0);
                    if (! (ref instanceof Runnable)) {
                        Loggable.log.error(
                                "A reference not implementing runnable has been added to the Utilities.activeReferenceQueue (): " + ref.getClass() // NOI18N
                                );
                        continue;
                    }
                    
                    if (deprecated) {
                        Loggable.log.error(
                                "Utilities.ACTIVE_REFERENCE_QUEUE has been deprecated for " + ref.getClass() + " use Utilities.activeReferenceQueue" // NOI18N
                                );
                    }
                    
                    
                    // do the cleanup
                    try {
                        ((Runnable)ref).run();
                    } catch (ThreadDeath td) {
                        throw td;
                    } catch (Throwable t) {
                        // Should not happen.
                        // If it happens, it is a bug in client code, notify!
                        Loggable.log.warn(t.getMessage(), t);
                    } finally {
                        // to allow GC
                        ref = null;
                    }
                } catch (InterruptedException ex) {
                    Loggable.log.debug(ex.getMessage(), ex);
                }
            }
        }
    }
}