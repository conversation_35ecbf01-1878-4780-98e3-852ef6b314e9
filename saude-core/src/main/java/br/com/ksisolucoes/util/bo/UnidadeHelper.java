package br.com.ksisolucoes.util.bo;

import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.UnidadeException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.Unidade;

/**
 *
 * <AUTHOR>
 *
 */
public class UnidadeHelper {

    /**
     * Verifica se a Unidade possui ou no casas decimais.
     * Caso no possua e a mesma tenha sido informada lana uma exception
     *
     * @param valor
     * @param produto
     * @throws ValidacaoException
     */
    public static void validarValorRefUnidade(Double valor, Produto produto) throws UnidadeException {
        validarValorRefUnidade(valor, produto.getUnidade());
    }

    /**
     * Verifica se a Unidade possui ou no casas decimais.
     * Caso no possua e a mesma tenha sido informada lana uma exception
     *
     * @param valor
     * @param produto
     * @throws ValidacaoException
     */
    public static void validarValorRefUnidade(Double valor, Unidade unidade) throws UnidadeException {
        if( RepositoryComponentDefault.NAO.equals(unidade.getFlagCasaDecimal() ) ) {
            if(valor == null){
                valor = 0D;
            }
            if( valor.intValue() != valor.doubleValue() ) {
                // TODO Fabricio - Colocar Bundle
                throw new UnidadeException("O valor " + valor + " no pode conter casas decimais, segundo a definio da unidade: " + unidade.getUnidade());
            }
        }
    }

    /**
     * Verifica conforme parâmetro "CentralAgendamento" se é central de agendamento ou unidade.
     * Central de Agendamento = PARAMETRO_GEM.CENTRAL_AGENDAMENTO = UNIDADE LOGADA
     * Unidade = PARAMETRO_GEM.CENTRAL_AGENDAMENTO <> UNIDADE LOGADA
     *
     * @return 
     */
    public static boolean isCentralAgendamento() {
        try {
            Empresa empresaGEM = (Empresa) BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("CentralAgendamento");
            if (empresaGEM != null) {
                if (empresaGEM.equals(SessaoAplicacaoImp.getInstance().<Empresa>getEmpresa())) {
                    return true;
                }
            }
        } catch (DAOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return false;
    }
}