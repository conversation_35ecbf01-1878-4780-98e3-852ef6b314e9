package br.com.ksisolucoes.util.esus;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoPaciente;
import br.com.ksisolucoes.vo.atividadegrupo.LocalAtividadeGrupo;
import br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo;
import br.com.ksisolucoes.vo.esus.dto.EsusValidacoesFichasDTOParam;
import org.jrimum.utilix.Objects;

import static br.com.ksisolucoes.util.esus.EsusHelper.*;

/**
 * Created by laudecir on 06/09/17.
 */
public class EsusValidacoesFichaAtividadeGrupoHelper {

    private EsusValidacoesFichaAtividadeGrupoHelper() { }

    public static String validate(EsusValidacoesFichasDTOParam validationParam) throws ValidacaoException {
        StringBuilder stringBuilder = new StringBuilder();

        stringBuilder.append(getInconsistencies(validationParam, validateCareUnit(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateActivityType(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, validateINEP(validationParam)));
        stringBuilder.append(getInconsistencies(validationParam, executeSpecificValidations(validationParam)));

        getReturnMessage(validationParam, stringBuilder.toString());

        return stringBuilder.toString();
    }

    public static String validateINEP(EsusValidacoesFichasDTOParam validationParam) {
        Long numeroInep = validationParam.getAtividadeGrupoDTO().getAtividadeGrupo().getNumeroInep();
        StringBuilder stringBuilder = new StringBuilder();

        if (Objects.isNull(numeroInep)
                && (RepositoryComponentDefault.SIM_LONG.equals(validationParam.getAtividadeGrupoDTO().getAtividadeGrupo().getPseEducacao())
                || RepositoryComponentDefault.SIM_LONG.equals(validationParam.getAtividadeGrupoDTO().getAtividadeGrupo().getPseSaude()))) {
            appendInconsitency(stringBuilder, "Número do INEP é obrigatório pata atividades do Programa Saúde na Escola");
        }

        return stringBuilder.toString();
    }

    public static String validateActivityType(EsusValidacoesFichasDTOParam validationParam) {
        AtividadeGrupo atividadeGrupo = validationParam.getAtividadeGrupoDTO().getAtividadeGrupo();
        Long codigoEsus = atividadeGrupo.getTipoAtividadeGrupo().getCodigoEsus();
        StringBuilder stringBuilder = new StringBuilder();

        if (RepositoryComponentDefault.SIM_LONG.equals(atividadeGrupo.getPseEducacao())
                && !RepositoryComponentDefault.SIM_LONG.equals(atividadeGrupo.getPseSaude())
                && (TipoAtividadeGrupo.CodigoEsus.REUNIAO_EQUIPE.value().equals(codigoEsus)
                || TipoAtividadeGrupo.CodigoEsus.REUNIAO_OUTRAS_EQUIPES.value().equals(codigoEsus)
                || TipoAtividadeGrupo.CodigoEsus.REUNIAO_INTERSETORIAL.value().equals(codigoEsus)
                || TipoAtividadeGrupo.CodigoEsus.ATENDIMENTO_GRUPO.value().equals(codigoEsus))) {
            appendInconsitency(stringBuilder, "Tipo de Atividade incompatível");
        }

        return stringBuilder.toString();
    }

    public static String executeSpecificValidations(EsusValidacoesFichasDTOParam validationParam) {
        AtividadeGrupo atividadeGrupo = validationParam.getAtividadeGrupoDTO().getAtividadeGrupo();
        StringBuilder stringBuilder = new StringBuilder();


        if (Objects.isNull(atividadeGrupo.getLocalAtividadeGrupo())) {
            appendInconsitency(stringBuilder, "Atividade em Grupo sem local definido");
        } else if (Objects.isNull(atividadeGrupo.getLocalAtividadeGrupo().getTipoLocal())) {
            appendInconsitency(stringBuilder, "O Local " + atividadeGrupo.getLocalAtividadeGrupo().getDescricao() + " não possui um Tipo definido.");
        } else if (LocalAtividadeGrupo.TipoLocal.ESCOLA.value().equals(atividadeGrupo.getLocalAtividadeGrupo().getTipoLocal()) && Objects.isNull(atividadeGrupo.getNumeroInep())) {
            appendInconsitency(stringBuilder, "Informe o Número do INEP da Escola.");
        }

        validateItem(Objects.isNull(atividadeGrupo.getDataInicio()), stringBuilder, "A Data é obrigatória.");
        validateItem(Objects.isNull(atividadeGrupo.getTurno()), stringBuilder, "O Turno é obrigatório.");
        validateItem(Objects.isNull(atividadeGrupo.getTipoAtividadeGrupo().getCodigoEsus()), stringBuilder, "Tipo de Atividade: " + atividadeGrupo.getTipoAtividadeGrupo().getDescricao() + ", sem Código e-SUS definido.");
        validateItem(atividadeGrupo.getParticipantes() == null || atividadeGrupo.getParticipantes() < 1 || atividadeGrupo.getParticipantes() > 999, stringBuilder, "A quantidade de participantes deve estar entre 1 e 999");

        return stringBuilder.toString();
    }

    private static boolean shouldInformCnsCpf(TipoAtividadeGrupo tipoAtividadeGrupo) {
        if (tipoAtividadeGrupo.getCodigoEsus() == null)
            return false;
        return tipoAtividadeGrupo.getCodigoEsus().equals(TipoAtividadeGrupo.CodigoEsus.ATENDIMENTO_GRUPO.value()) || tipoAtividadeGrupo.getCodigoEsus().equals(TipoAtividadeGrupo.CodigoEsus.AVALIACAO.value());
    }

    public static String validateParticipant(EsusValidacoesFichasDTOParam validationParam) {
        AtividadeGrupo atividadeGrupo = validationParam.getAtividadeGrupoDTO().getAtividadeGrupo();
        TipoAtividadeGrupo tipoAtividadeGrupo = atividadeGrupo.getTipoAtividadeGrupo();
        AtividadeGrupoPaciente atividadeGrupoPaciente = validationParam.getAtividadeGrupoDTO().getAtividadeGrupoPaciente();

        StringBuilder stringBuilder = new StringBuilder();

        boolean shouldInformCnsCpf = shouldInformCnsCpf(tipoAtividadeGrupo);
        boolean didntInformedCnsCpf = Objects.isNull(atividadeGrupoPaciente.getCns()) && Objects.isNull(atividadeGrupoPaciente.getCpf());

        validateItem(shouldInformCnsCpf && didntInformedCnsCpf, stringBuilder, "É obrigatório informar o CNS ou CPF dos participantes.");
        validateItem(Objects.isNull(atividadeGrupoPaciente.getSexo()), stringBuilder, "É obrigatório informar o Sexo dos participantes.");

        if (Objects.isNull(atividadeGrupoPaciente.getDataNascimento())) {
            appendInconsitency(stringBuilder, "É obrigatório informar a Data de Nascimento dos participantes.");
        } else if (atividadeGrupo.getDataInicio().before(atividadeGrupoPaciente.getDataNascimento())) {
            appendInconsitency(stringBuilder, "A Data de Nascimento " + Data.formatar(atividadeGrupoPaciente.getDataNascimento()) + " não pode ser maior que a data da atividade em grupo.");
        }

        return stringBuilder.toString();
    }
}
