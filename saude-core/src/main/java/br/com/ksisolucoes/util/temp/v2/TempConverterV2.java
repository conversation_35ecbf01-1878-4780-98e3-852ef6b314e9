package br.com.ksisolucoes.util.temp.v2;

import br.com.ksisolucoes.util.log.Loggable;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.hibernate4.Hibernate4Module;
import java.io.IOException;

/**
 *
 * <AUTHOR>
 */
public class TempConverterV2 {

    private ObjectMapper mapper;
    
    public <T> T toType(Class<T> type, String json) {
        try {
            return getObjectMapper().readValue(json, type);
        } catch (JsonParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (JsonMappingException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }

    public String toJson(Object object) {
        try {
            return getObjectMapper().writeValueAsString(object);
        } catch (JsonParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (JsonMappingException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }
    
    private ObjectMapper getObjectMapper(){
        if (this.mapper == null) {
            this.mapper = new ObjectMapper();

            
            this.mapper.registerModule(new Hibernate4Module()).setVisibilityChecker(this.mapper.getSerializationConfig().getDefaultVisibilityChecker()
                    .withFieldVisibility(JsonAutoDetect.Visibility.ANY)
                    .withGetterVisibility(JsonAutoDetect.Visibility.NONE)
                    .withIsGetterVisibility(JsonAutoDetect.Visibility.NONE)
                    .withSetterVisibility(JsonAutoDetect.Visibility.NONE)
                    .withCreatorVisibility(JsonAutoDetect.Visibility.NONE));
            this.mapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        }
        
        return this.mapper;
    }
}
