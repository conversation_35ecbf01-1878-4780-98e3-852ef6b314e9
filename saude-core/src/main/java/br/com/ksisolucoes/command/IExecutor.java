/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.command;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.bo.command.CommandQueryPager;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.dao.paginacao.DataPaging;
import br.com.ksisolucoes.dao.paginacao.DataPagingResult;
import br.com.ksisolucoes.report.CommandFileReport;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.FileReport;
import br.com.ksisolucoes.report.Report;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public interface IExecutor {

    public void execute(InterfaceCommand command) throws DAOException, ValidacaoException;

    public InterfaceCommand executeReturn(InterfaceCommand command) throws DAOException, ValidacaoException;

    public void execute(InterfaceCommand command, String nomeProcesso) throws DAOException, ValidacaoException;

    public CommandQuery executeQuery(CommandQuery command) throws DAOException, ValidacaoException;

    public DataPagingResult executeQueryPager(DataPaging dataPaging, CommandQueryPager command) throws DAOException, ValidacaoException;

    public DataReport executeDataReport(Report report) throws ReportException;

    public FileReport executeFileReport(CommandFileReport report) throws ReportException;
}
