package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoInclusaoResponsabilidade;

import java.io.Serializable;



public class RequerimentoInclusaoResponsabilidade extends BaseRequerimentoInclusaoResponsabilidade implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoInclusaoResponsabilidade () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoInclusaoResponsabilidade (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoInclusaoResponsabilidade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {

		super (
			codigo,
			requerimentoVigilancia,
			responsavelTecnico,
			estabelecimento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}