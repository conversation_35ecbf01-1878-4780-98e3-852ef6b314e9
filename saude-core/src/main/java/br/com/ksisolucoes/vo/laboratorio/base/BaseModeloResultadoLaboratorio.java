package br.com.ksisolucoes.vo.laboratorio.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the modelo_resultado_lab table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="modelo_resultado_lab"
 */

public abstract class BaseModeloResultadoLaboratorio extends BaseRootVO implements Serializable {

	public static String REF = "ModeloResultadoLaboratorio";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_USUARIO_CADASTRO = "usuarioCadastro";
	public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";
	public static final String PROP_DESCRICAO_MODELO_RESULTADO = "descricaoModeloResultado";


	// constructors
	public BaseModeloResultadoLaboratorio () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseModeloResultadoLaboratorio (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseModeloResultadoLaboratorio (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.String descricaoModeloResultado,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setExameProcedimento(exameProcedimento);
		this.setUsuarioCadastro(usuarioCadastro);
		this.setDescricaoModeloResultado(descricaoModeloResultado);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricaoModeloResultado;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_modelo_result_lab"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_modelo_resultado
	 */
	public java.lang.String getDescricaoModeloResultado () {
		return getPropertyValue(this, descricaoModeloResultado, PROP_DESCRICAO_MODELO_RESULTADO); 
	}

	/**
	 * Set the value related to the column: ds_modelo_resultado
	 * @param descricaoModeloResultado the ds_modelo_resultado value
	 */
	public void setDescricaoModeloResultado (java.lang.String descricaoModeloResultado) {
//        java.lang.String descricaoModeloResultadoOld = this.descricaoModeloResultado;
		this.descricaoModeloResultado = descricaoModeloResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoModeloResultado", descricaoModeloResultadoOld, descricaoModeloResultado);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_exame_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento getExameProcedimento () {
		return getPropertyValue(this, exameProcedimento, PROP_EXAME_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_exame_procedimento
	 * @param exameProcedimento the cd_exame_procedimento value
	 */
	public void setExameProcedimento (br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento exameProcedimentoOld = this.exameProcedimento;
		this.exameProcedimento = exameProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("exameProcedimento", exameProcedimentoOld, exameProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cadastro
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCadastro () {
		return getPropertyValue(this, usuarioCadastro, PROP_USUARIO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cadastro
	 * @param usuarioCadastro the cd_usuario_cadastro value
	 */
	public void setUsuarioCadastro (br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCadastroOld = this.usuarioCadastro;
		this.usuarioCadastro = usuarioCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadastro", usuarioCadastroOld, usuarioCadastro);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.laboratorio.ModeloResultadoLaboratorio)) return false;
		else {
			br.com.ksisolucoes.vo.laboratorio.ModeloResultadoLaboratorio modeloResultadoLaboratorio = (br.com.ksisolucoes.vo.laboratorio.ModeloResultadoLaboratorio) obj;
			if (null == this.getCodigo() || null == modeloResultadoLaboratorio.getCodigo()) return false;
			else return (this.getCodigo().equals(modeloResultadoLaboratorio.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}