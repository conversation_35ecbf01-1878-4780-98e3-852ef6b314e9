package br.com.ksisolucoes.vo.basico.pesquisa;

import br.com.celk.integracao.IntegracaoRest;
import java.io.Serializable;

import br.com.ksisolucoes.vo.basico.pesquisa.base.BasePesquisaPergunta;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;


@IntegracaoRest
public class PesquisaPergunta extends BasePesquisaPergunta implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PesquisaPergunta () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PesquisaPergunta (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PesquisaPergunta (
		java.lang.Long codigo,
		java.lang.Long versionAll) {

		super (
			codigo,
			versionAll);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}