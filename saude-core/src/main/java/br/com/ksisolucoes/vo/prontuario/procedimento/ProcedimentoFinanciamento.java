package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;
import java.util.Date;

import br.com.ksisolucoes.associacao.annotations.ColumnNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.IdNameSIGTAP;
import br.com.ksisolucoes.associacao.annotations.TableNameSIGTAP;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoFinanciamento;


@TableNameSIGTAP("tb_financiamento")
public class ProcedimentoFinanciamento extends BaseProcedimentoFinanciamento implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public static final Long PAB = 1L;
        public static final Long FAEC = 4L;
        public static final Long MAC_INCENTIVO = 5L;
        public static final Long MAC = 6L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoFinanciamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcedimentoFinanciamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ProcedimentoFinanciamento (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.util.Date dataCompetencia) {

		super (
			codigo,
			descricao,
			dataCompetencia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoFormatado(){
        return Util.getDescricaoFormatado(this.getCodigo(), this.getDescricao());
    }

    @IdNameSIGTAP("CO_FINANCIAMENTO")
    @Override
    public Long getCodigo() {
        return super.getCodigo();
    }

    @ColumnNameSIGTAP("DT_COMPETENCIA")
    @Override
    public Date getDataCompetencia() {
        return super.getDataCompetencia();
    }

    @ColumnNameSIGTAP("NO_FINANCIAMENTO")
    @Override
    public String getDescricao() {
        return super.getDescricao();
    }

    @Override
    public void setCodigo(Long codigo) {
        super.setCodigo(codigo);
    }

    @Override
    public void setDataCompetencia(Date dataCompetencia) {
        super.setDataCompetencia(dataCompetencia);
    }

    @Override
    public void setDescricao(String descricao) {
        super.setDescricao(descricao);
    }


}