package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the profissional_indicador table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="profissional_indicador"
 */

public abstract class BaseProfissionalIndicador extends BaseRootVO implements Serializable {

	public static String REF = "ProfissionalIndicador";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_CODIGO_PNI = "codigoPni";


	// constructors
	public BaseProfissionalIndicador () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProfissionalIndicador (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProfissionalIndicador (
		java.lang.Long codigo,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long codigoPni;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_profissional_indicador"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_profissional_indicador
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_profissional_indicador
	 * @param descricao the ds_profissional_indicador value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cod_pni
	 */
	public java.lang.Long getCodigoPni () {
		return getPropertyValue(this, codigoPni, PROP_CODIGO_PNI); 
	}

	/**
	 * Set the value related to the column: cod_pni
	 * @param codigoPni the cod_pni value
	 */
	public void setCodigoPni (java.lang.Long codigoPni) {
//        java.lang.Long codigoPniOld = this.codigoPni;
		this.codigoPni = codigoPni;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoPni", codigoPniOld, codigoPni);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.ProfissionalIndicador)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.ProfissionalIndicador profissionalIndicador = (br.com.ksisolucoes.vo.vacina.ProfissionalIndicador) obj;
			if (null == this.getCodigo() || null == profissionalIndicador.getCodigo()) return false;
			else return (this.getCodigo().equals(profissionalIndicador.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}