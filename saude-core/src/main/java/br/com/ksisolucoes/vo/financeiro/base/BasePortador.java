package br.com.ksisolucoes.vo.financeiro.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;



/**
 * This class has been automatically generated by Hibernate Synchronizer.
 * For more information or documentation, visit The Hibernate Synchronizer page
 * at http://www.binamics.com/hibernatesync or contact <PERSON> at <EMAIL>.
 *
 * Este objeto est relacionado com a tabela portador.
 * No modifique esta classe, pois, sincronizaes com a 
 * base sobrescrevero as alteraes.
 *
 * @hibernate.class
 *  table="portador"
 */
public abstract class BasePortador  extends BaseRootVO implements Serializable, ValidacaoExceptionInterface {

	public static String PROP_SWIFT = "swift";
	public static String PROP_AGENCIA = "agencia";
	public static String PROP_CODIGO = "codigo";
	public static String PROP_DESCRICAO = "descricao";
	public static String PROP_CIDADE = "cidade";

	public RetornoValidacao retornoValidacao = new RetornoValidacao();

	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long  codigo;

	// fields
	private java.lang.String swift;
	private java.lang.String agencia;
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.basico.Cidade cidade;


	// construtores
	public BasePortador () {}

	/**
	 * Construtor para a chave primria.
	 */
	public BasePortador (java.lang.Long codigo) {
		this.setCodigo(codigo);
	}

	/**
	 * Construtor para os atributos requeridos.
	 */
	public BasePortador (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Cidade cidade,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setCidade(cidade);
		this.setDescricao(descricao);
	}



	/**
	 * Retorna o identificador nico da classe.
	 *
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cod_por"
     *
	 * @return codigo
	 */
	public java.lang.Long getCodigo() {
		return codigo;
	}

	/**
	 * Seta o identificador nico da classe.
	 *
	 * @param codigo para o aributo codigo
	 */
	public void setCodigo(java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}


    /**
     * Retorna o valor do atributo swift
     *
     * @return swift
     */
	public java.lang.String getSwift() {
		return swift;
	}

	/**
	 * Setar um valor para o atributo: swift.
     *
	 * @param swift valor para o atributo swift.
	 */
	public void setSwift(java.lang.String swift) {
		this.swift = swift;
	}


    /**
     * Retorna o valor do atributo agencia
     *
     * @return agencia
     */
	public java.lang.String getAgencia() {
		return agencia;
	}

	/**
	 * Setar um valor para o atributo: agencia.
     *
	 * @param agencia valor para o atributo agencia.
	 */
	public void setAgencia(java.lang.String agencia) {
		this.agencia = agencia;
	}


    /**
     * Retorna o valor do atributo descricao
     *
     * @return descricao
     */
	public java.lang.String getDescricao() {
		return descricao;
	}

	/**
	 * Setar um valor para o atributo: descricao.
     *
	 * @param descricao valor para o atributo descricao.
	 */
	public void setDescricao(java.lang.String descricao) {
		this.descricao = descricao;
	}


    /**
     * Retorna o valor do atributo cidade
     *
     * @hibernate.property
     *  column=cod_cid
	 * not-null=true
     *
     * @return cidade
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidade () {
		return this.cidade;
	}

	/**
	 * Setar um valor para o atributo: cidade.
     *
	 * @param cidade valor para o atributo cidade.
	 */
	public void setCidade(br.com.ksisolucoes.vo.basico.Cidade cidade) {
		this.cidade = cidade;
	}


	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.financeiro.base.BasePortador)) return false;
		else {
			br.com.ksisolucoes.vo.financeiro.base.BasePortador mObj = (br.com.ksisolucoes.vo.financeiro.base.BasePortador) obj;
			if (null == this.getCodigo() || null == mObj.getCodigo()) return false;
			else return (this.getCodigo().equals(mObj.getCodigo()));
		}
	}


	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}



    /* (non-Javadoc)
     * @see br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface#getRetornoValidacao()
     */
    public RetornoValidacao getRetornoValidacao() {
        return this.retornoValidacao;
    }

   public String toString() {
       StringBuffer s = new StringBuffer();
   // primary key
       s.append("codigo: " + codigo + ".\n");

   // fields
       s.append("swift: " + swift + ".\n");
       s.append("agencia: " + agencia + ".\n");
       s.append("descricao: " + descricao + ".\n");

   // many to one
       s.append("cidade: " + cidade + ".\n");

       return s.toString();
   }


}