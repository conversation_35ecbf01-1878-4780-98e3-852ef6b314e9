package br.com.ksisolucoes.vo.cadsus;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.cadsus.base.BaseEstadoCivil;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class EstadoCivil extends BaseEstadoCivil implements CodigoManager, PesquisaObjectInterface{
	private static final long serialVersionUID = 1L;
        
        public enum CodigoEstadoCivil implements IEnum {
            SOLTEIRO(1L, Bundle.getStringApplication("rotulo_solteiro")),
            CASADO(2L, Bundle.getStringApplication("rotulo_casado")),
            VIUVO(3L, Bundle.getStringApplication("rotulo_viuvo")),
            SEPARADO_JUDICIALMENTE(4L, Bundle.getStringApplication("rotulo_separado_judicialmente")),
            UNIAO_CONSENSUAL(5L, Bundle.getStringApplication("rotulo_uniao_consensual")),
            IGNORADO(9L, Bundle.getStringApplication("rotulo_ignorado")),
            ;

            private Long value;
            private String descricao;

            private CodigoEstadoCivil(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static CodigoEstadoCivil valeuOf(Long value) {
                for (CodigoEstadoCivil codigoEstadoCivil : CodigoEstadoCivil.values()) {
                    if (codigoEstadoCivil.value().equals(value)) {
                        return codigoEstadoCivil;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }      
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstadoCivil () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EstadoCivil (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EstadoCivil (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}
/*[CONSTRUCTOR MARKER END]*/

    public Serializable getCodigoManager() {
        return Coalesce.asString(getCodigo());

    }

    public void setCodigoManager(Serializable srlzbl) {
        setCodigo((Long) srlzbl);
    }

    public String getDescricaoVO() {
        return this.getDescricao();
    }

    public String getIdentificador() {
        return this.getCodigo().toString();
    }

}