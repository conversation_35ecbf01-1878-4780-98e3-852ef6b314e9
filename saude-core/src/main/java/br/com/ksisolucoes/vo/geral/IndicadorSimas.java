package br.com.ksisolucoes.vo.geral;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.geral.base.BaseIndicadorSimas;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;


public class IndicadorSimas extends BaseIndicadorSimas implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public IndicadorSimas () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public IndicadorSimas (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	@Override
	public String getDescricaoVO() {
		return getDescricao();
	}

	@Override
	public String getIdentificador() {
		return Coalesce.asString(getCodigo());
	}

	public enum Situacao implements IEnum<IndicadorSimas.Situacao> {

		ATIVO(1L, Bundle.getStringApplication("rotulo_ativo")),
		INATIVO(0L, Bundle.getStringApplication("rotulo_inativo"));


		private final Long value;
		private final String descricao;

		Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static IndicadorSimas.Situacao valeuOf(Long value) {
			for (IndicadorSimas.Situacao situacao : IndicadorSimas.Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}
	}

	public enum Indicadores implements IEnum<IndicadorSimas.Indicadores> {

		ATEND_DE_URGENCIA_C_OBS_24H_ATENCAO_ESPECIALIZADA(1L, Bundle.getStringApplication("rotulo_atend_urg_24h_esp")),
		ATEND_MEDICO_EM_UPA_24H_PRONTO_ATENDIMENTO(2L, Bundle.getStringApplication("rotulo_atend_medico_upa_24h")),
		ATEND_ORTOPEDICO_C_IMOBILIZACAO_PROVISORIA(3L, Bundle.getStringApplication("rotulo_atend_ortopedico_imobilizacao")),
		ACOLHIMENTO_C_CLASSIFICACAO_DE_RISCO_UPA_PED(4L, Bundle.getStringApplication("rotulo_acolhimento_risco_upa_ped")),
		ATEND_DE_URGENCIA_C_OBS_24H_ATENCAO_ESPECIALIZADA_DUPLICADO(5L, Bundle.getStringApplication("rotulo_atend_urg_24h_esp_dup")),
		ATEND_MEDICO_EM_UPA_24H_DE_PRONTO_ATENDIMENTO_DUPLICADO(6L, Bundle.getStringApplication("rotulo_atend_medico_upa_24h_dup")),
		CONSULTA_ODONTOLOGICA(7L, Bundle.getStringApplication("rotulo_consulta_odontologica_simas")),
		PROCEDIMENTO_ODONTOLOGICO(8L, Bundle.getStringApplication("rotulo_procedimento_odontologico")),
		VISITA_DOMICILIAR_ACS(9L, Bundle.getStringApplication("rotulo_visita_domiciliar_acs_simas")),
		CONSULTA_MEDICA(10L, Bundle.getStringApplication("rotulo_consulta_medica_simas")),
		CONSULTA_ENFERMAGEM(11L, Bundle.getStringApplication("rotulo_consulta_enfermagem_simas"));

		private final Long value;
		private final String descricao;

		Indicadores(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static IndicadorSimas.Indicadores valueOf(Long value) {
			for (IndicadorSimas.Indicadores indicador : IndicadorSimas.Indicadores.values()) {
				if (indicador.value().equals(value)) {
					return indicador;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return this.value;
		}

		@Override
		public String descricao() {
			return this.descricao;
		}
	}

	public String getSituacaoFormatada() {
		return IndicadorSimas.Situacao.valeuOf(this.getSituacao()).descricao();
	}

	public String getCodigoDescricaoFormatado() {
		return Util.getDescricaoFormatado(
				Coalesce.asString(this.getCodigo().toString()),
				Coalesce.asString(this.getDescricao()));
	}

}