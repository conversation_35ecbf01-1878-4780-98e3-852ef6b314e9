<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="EstratificacaoRisco" table="estratificacao_risco" >
        
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_estratificacao_risco"
        >
            <generator class="sequence">
                <param name="sequence">seq_estratificacao_risco</param>
            </generator>
        </id>
		<version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                name="atendimento"
                column="nr_atendimento"
                not-null="true"
        />

        <property
                name="flagClassificacaoRisco"
                type="java.lang.Long"
                column="flag_classificacao_risco"
                not-null="true"
        />

        <property
                name="formulario"
                type="java.lang.Long"
                column="formulario"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.Profissional"
                column="cd_profissional"
                name="profissional"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                column="cd_cbo"
                name="cbo"
                not-null="true"
        />
          
        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                name="empresa"
                column="empresa"
                not-null="true"
         />
          
        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuario"
                column="cd_usuario"
                not-null="true"
         />
          
        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="java.util.Date"
                not-null="true"
        />
         
    </class>
</hibernate-mapping>