package br.com.ksisolucoes.vo.geral;

import br.com.ksisolucoes.vo.geral.base.BaseEstruturaEquipamentoRevisaoPK;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoRevisaoPKInterface;

public class EstruturaEquipamentoRevisaoPK extends BaseEstruturaEquipamentoRevisaoPK implements EstruturaEquipamentoRevisaoPKInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstruturaEquipamentoRevisaoPK () {}
	
	public EstruturaEquipamentoRevisaoPK (
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.entradas.estoque.Produto componente,
		java.lang.Long item,
		java.lang.Long revisao) {

		super (
			produto,
			componente,
			item,
			revisao);
	}
/*[CONSTRUCTOR MARKER END]*/


}