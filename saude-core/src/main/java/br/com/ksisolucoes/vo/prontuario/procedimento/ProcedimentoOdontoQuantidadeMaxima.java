package br.com.ksisolucoes.vo.prontuario.procedimento;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoOdontoQuantidadeMaxima;

import java.io.Serializable;


public class ProcedimentoOdontoQuantidadeMaxima extends BaseProcedimentoOdontoQuantidadeMaxima implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public ProcedimentoOdontoQuantidadeMaxima() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public ProcedimentoOdontoQuantidadeMaxima(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public ProcedimentoOdontoQuantidadeMaxima(
            java.lang.Long codigo,
            java.lang.String referenciaSigtap,
            java.lang.String descricao,
            java.lang.Long flagLocal,
            java.lang.Long quantidadeMaxima) {

        super(
                codigo,
                referenciaSigtap,
                descricao,
                flagLocal,
                quantidadeMaxima);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public enum Local implements IEnum {
        ARCADA(0L, Bundle.getStringApplication("rotulo_arcada")),
        DENTE(1L, Bundle.getStringApplication("rotulo_dente")),
        SEXTANTE(2L, Bundle.getStringApplication("rotulo_sextante")),
        ;

        private Long value;
        private String descricao;

        Local(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Local valeuOf(Long value) {
            for (Local local : Local.values()) {
                if (local.value().equals(value)) {
                    return local;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
}