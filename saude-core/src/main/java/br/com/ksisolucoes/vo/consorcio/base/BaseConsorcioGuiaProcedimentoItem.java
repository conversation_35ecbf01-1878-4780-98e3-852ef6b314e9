package br.com.ksisolucoes.vo.consorcio.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the consorcio_guia_proc_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="consorcio_guia_proc_item"
 */

public abstract class BaseConsorcioGuiaProcedimentoItem extends BaseRootVO implements Serializable {

	public static String REF = "ConsorcioGuiaProcedimentoItem";
	public static final String PROP_USUARIO_SITUACAO_SISREG = "usuarioSituacaoSisreg";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_VALOR_PROCEDIMENTO_IMPOSTO = "valorProcedimentoImposto";
	public static final String PROP_QUANTIDADE_APLICACAO = "quantidadeAplicacao";
	public static final String PROP_CONSORCIO_GUIA_PROCEDIMENTO = "consorcioGuiaProcedimento";
	public static final String PROP_CODIGO_SISREG = "codigoSisreg";
	public static final String PROP_VALOR_PROCEDIMENTO = "valorProcedimento";
	public static final String PROP_NUMERO_SISREG = "numeroSisreg";
	public static final String PROP_STATUS = "status";
	public static final String PROP_CONSORCIO_PROCEDIMENTO = "consorcioProcedimento";
	public static final String PROP_SITUACAO_SISREG = "situacaoSisreg";
	public static final String PROP_CID = "cid";
	public static final String PROP_DATA_SISREG = "dataSisreg";
	public static final String PROP_DATA_SITUACAO_SISREG = "dataSituacaoSisreg";
	public static final String PROP_QUANTIDADE = "quantidade";


	// constructors
	public BaseConsorcioGuiaProcedimentoItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseConsorcioGuiaProcedimentoItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseConsorcioGuiaProcedimentoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento,
		br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuarioSituacaoSisreg,
		java.lang.Long status,
		java.lang.Double valorProcedimento,
		java.lang.Long quantidade,
		java.lang.Long situacaoSisreg) {

		this.setCodigo(codigo);
		this.setConsorcioGuiaProcedimento(consorcioGuiaProcedimento);
		this.setConsorcioProcedimento(consorcioProcedimento);
		this.setUsuarioSituacaoSisreg(usuarioSituacaoSisreg);
		this.setStatus(status);
		this.setValorProcedimento(valorProcedimento);
		this.setQuantidade(quantidade);
		this.setSituacaoSisreg(situacaoSisreg);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long status;
	private java.lang.Double valorProcedimento;
	private java.lang.Double valorProcedimentoImposto;
	private java.lang.Long quantidade;
	private java.lang.Long quantidadeAplicacao;
	private java.lang.String numeroSisreg;
	private java.lang.String codigoSisreg;
	private java.util.Date dataSisreg;
	private java.lang.Long situacaoSisreg;
	private java.util.Date dataSituacaoSisreg;

	// many to one
	private br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento;
	private br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimento;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid cid;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioSituacaoSisreg;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_guia_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: vl_procedimento
	 */
	public java.lang.Double getValorProcedimento () {
		return getPropertyValue(this, valorProcedimento, PROP_VALOR_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: vl_procedimento
	 * @param valorProcedimento the vl_procedimento value
	 */
	public void setValorProcedimento (java.lang.Double valorProcedimento) {
//        java.lang.Double valorProcedimentoOld = this.valorProcedimento;
		this.valorProcedimento = valorProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("valorProcedimento", valorProcedimentoOld, valorProcedimento);
	}



	/**
	 * Return the value associated with the column: vl_procedimento_imposto
	 */
	public java.lang.Double getValorProcedimentoImposto () {
		return getPropertyValue(this, valorProcedimentoImposto, PROP_VALOR_PROCEDIMENTO_IMPOSTO); 
	}

	/**
	 * Set the value related to the column: vl_procedimento_imposto
	 * @param valorProcedimentoImposto the vl_procedimento_imposto value
	 */
	public void setValorProcedimentoImposto (java.lang.Double valorProcedimentoImposto) {
//        java.lang.Double valorProcedimentoImpostoOld = this.valorProcedimentoImposto;
		this.valorProcedimentoImposto = valorProcedimentoImposto;
//        this.getPropertyChangeSupport().firePropertyChange ("valorProcedimentoImposto", valorProcedimentoImpostoOld, valorProcedimentoImposto);
	}



	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: qtdade_aplicacao
	 */
	public java.lang.Long getQuantidadeAplicacao () {
		return getPropertyValue(this, quantidadeAplicacao, PROP_QUANTIDADE_APLICACAO); 
	}

	/**
	 * Set the value related to the column: qtdade_aplicacao
	 * @param quantidadeAplicacao the qtdade_aplicacao value
	 */
	public void setQuantidadeAplicacao (java.lang.Long quantidadeAplicacao) {
//        java.lang.Long quantidadeAplicacaoOld = this.quantidadeAplicacao;
		this.quantidadeAplicacao = quantidadeAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeAplicacao", quantidadeAplicacaoOld, quantidadeAplicacao);
	}



	/**
	 * Return the value associated with the column: nr_sisreg
	 */
	public java.lang.String getNumeroSisreg () {
		return getPropertyValue(this, numeroSisreg, PROP_NUMERO_SISREG); 
	}

	/**
	 * Set the value related to the column: nr_sisreg
	 * @param numeroSisreg the nr_sisreg value
	 */
	public void setNumeroSisreg (java.lang.String numeroSisreg) {
//        java.lang.String numeroSisregOld = this.numeroSisreg;
		this.numeroSisreg = numeroSisreg;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroSisreg", numeroSisregOld, numeroSisreg);
	}



	/**
	 * Return the value associated with the column: cd_sisreg
	 */
	public java.lang.String getCodigoSisreg () {
		return getPropertyValue(this, codigoSisreg, PROP_CODIGO_SISREG); 
	}

	/**
	 * Set the value related to the column: cd_sisreg
	 * @param codigoSisreg the cd_sisreg value
	 */
	public void setCodigoSisreg (java.lang.String codigoSisreg) {
//        java.lang.String codigoSisregOld = this.codigoSisreg;
		this.codigoSisreg = codigoSisreg;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoSisreg", codigoSisregOld, codigoSisreg);
	}



	/**
	 * Return the value associated with the column: dt_sisreg
	 */
	public java.util.Date getDataSisreg () {
		return getPropertyValue(this, dataSisreg, PROP_DATA_SISREG); 
	}

	/**
	 * Set the value related to the column: dt_sisreg
	 * @param dataSisreg the dt_sisreg value
	 */
	public void setDataSisreg (java.util.Date dataSisreg) {
//        java.util.Date dataSisregOld = this.dataSisreg;
		this.dataSisreg = dataSisreg;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSisreg", dataSisregOld, dataSisreg);
	}



	/**
	 * Return the value associated with the column: situacao_sisreg
	 */
	public java.lang.Long getSituacaoSisreg () {
		return getPropertyValue(this, situacaoSisreg, PROP_SITUACAO_SISREG); 
	}

	/**
	 * Set the value related to the column: situacao_sisreg
	 * @param situacaoSisreg the situacao_sisreg value
	 */
	public void setSituacaoSisreg (java.lang.Long situacaoSisreg) {
//        java.lang.Long situacaoSisregOld = this.situacaoSisreg;
		this.situacaoSisreg = situacaoSisreg;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoSisreg", situacaoSisregOld, situacaoSisreg);
	}



	/**
	 * Return the value associated with the column: dt_situacao_sisreg
	 */
	public java.util.Date getDataSituacaoSisreg () {
		return getPropertyValue(this, dataSituacaoSisreg, PROP_DATA_SITUACAO_SISREG); 
	}

	/**
	 * Set the value related to the column: dt_situacao_sisreg
	 * @param dataSituacaoSisreg the dt_situacao_sisreg value
	 */
	public void setDataSituacaoSisreg (java.util.Date dataSituacaoSisreg) {
//        java.util.Date dataSituacaoSisregOld = this.dataSituacaoSisreg;
		this.dataSituacaoSisreg = dataSituacaoSisreg;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSituacaoSisreg", dataSituacaoSisregOld, dataSituacaoSisreg);
	}



	/**
	 * Return the value associated with the column: cd_guia
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento getConsorcioGuiaProcedimento () {
		return getPropertyValue(this, consorcioGuiaProcedimento, PROP_CONSORCIO_GUIA_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_guia
	 * @param consorcioGuiaProcedimento the cd_guia value
	 */
	public void setConsorcioGuiaProcedimento (br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimento) {
//        br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento consorcioGuiaProcedimentoOld = this.consorcioGuiaProcedimento;
		this.consorcioGuiaProcedimento = consorcioGuiaProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("consorcioGuiaProcedimento", consorcioGuiaProcedimentoOld, consorcioGuiaProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_consorcio_procedimento
	 */
	public br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento getConsorcioProcedimento () {
		return getPropertyValue(this, consorcioProcedimento, PROP_CONSORCIO_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_consorcio_procedimento
	 * @param consorcioProcedimento the cd_consorcio_procedimento value
	 */
	public void setConsorcioProcedimento (br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimento) {
//        br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento consorcioProcedimentoOld = this.consorcioProcedimento;
		this.consorcioProcedimento = consorcioProcedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("consorcioProcedimento", consorcioProcedimentoOld, consorcioProcedimento);
	}



	/**
	 * Return the value associated with the column: cd_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getCid () {
		return getPropertyValue(this, cid, PROP_CID); 
	}

	/**
	 * Set the value related to the column: cd_cid
	 * @param cid the cd_cid value
	 */
	public void setCid (br.com.ksisolucoes.vo.prontuario.basico.Cid cid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid cidOld = this.cid;
		this.cid = cid;
//        this.getPropertyChangeSupport().firePropertyChange ("cid", cidOld, cid);
	}



	/**
	 * Return the value associated with the column: cd_usu_situacao_sisreg
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioSituacaoSisreg () {
		return getPropertyValue(this, usuarioSituacaoSisreg, PROP_USUARIO_SITUACAO_SISREG); 
	}

	/**
	 * Set the value related to the column: cd_usu_situacao_sisreg
	 * @param usuarioSituacaoSisreg the cd_usu_situacao_sisreg value
	 */
	public void setUsuarioSituacaoSisreg (br.com.ksisolucoes.vo.controle.Usuario usuarioSituacaoSisreg) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioSituacaoSisregOld = this.usuarioSituacaoSisreg;
		this.usuarioSituacaoSisreg = usuarioSituacaoSisreg;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioSituacaoSisreg", usuarioSituacaoSisregOld, usuarioSituacaoSisreg);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem)) return false;
		else {
			br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem consorcioGuiaProcedimentoItem = (br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimentoItem) obj;
			if (null == this.getCodigo() || null == consorcioGuiaProcedimentoItem.getCodigo()) return false;
			else return (this.getCodigo().equals(consorcioGuiaProcedimentoItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}