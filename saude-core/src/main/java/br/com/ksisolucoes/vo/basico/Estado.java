package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.celk.integracao.IntegracaoRest;
import br.com.ksisolucoes.vo.basico.base.BaseEstado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * Esta classe est relacionado com a tabela estado.
 * Classe dimensionada para customizaes.
 *
 */
@IntegracaoRest
@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"})
public class Estado extends BaseEstado implements PesquisaObjectInterface, CodigoManager{
    
    private static final long serialVersionUID = 1L;
    /*[CONSTRUCTOR MARKER BEGIN]*/
	public Estado () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Estado (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Estado (
		java.lang.Long codigo,
		java.lang.String sigla,
		java.lang.String descricao,
		java.lang.Long versionAll,
		java.lang.Long codigoEsus) {

		super (
			codigo,
			sigla,
			descricao,
			versionAll,
			codigoEsus);
	}

    /*[CONSTRUCTOR MARKER END]*/
    
    public String getDescricaoVO() {
        return this.getDescricao();
    }
    
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
    
    
        /* (non-Javadoc)
         * @see br.com.ksisolucoes.vo.interfaces.CodigoManager#getCodigoManager()
         */
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
        /* (non-Javadoc)
         * @see br.com.ksisolucoes.vo.interfaces.CodigoManager#setCodigoManager(java.lang.Long)
         */
    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long)key);
    }

	public String getCodigoEsusFormatado () {
    	final long NUMERO_MAX_UM_CARACTERE = 9L;
		Long codigoEsus = getCodigoEsus();
		String codigoEsusFormatado = null;

		if (codigoEsus != null) {
			codigoEsusFormatado = codigoEsus.toString();
			if (codigoEsus <= NUMERO_MAX_UM_CARACTERE) {
				codigoEsusFormatado = "0" + codigoEsusFormatado;
			}
		}
		return codigoEsusFormatado;
	}
}
