package br.com.ksisolucoes.vo.esus;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.esus.base.BaseTermoRecusaCadastroIndividual;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;


public class TermoRecusaCadastroIndividual extends BaseTermoRecusaCadastroIndividual implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public TermoRecusaCadastroIndividual () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TermoRecusaCadastroIndividual (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TermoRecusaCadastroIndividual (
		java.lang.Long codigo,
		java.util.Date dataPreenchimento) {

		super (
			codigo,
			dataPreenchimento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public String getSexoFormatado() {
		if (RepositoryComponentDefault.SEXO_MASCULINO.equals(getSexo())) {
			return Bundle.getStringApplication("rotulo_masculino");
		} else if (RepositoryComponentDefault.SEXO_FEMININO.equals(getSexo())) {
			return Bundle.getStringApplication("rotulo_feminino");
		} else {
			return null;
		}
	}
}