<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.integracao">
    <class name="DocumentoAssinado" table="documento_assinado">
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_documento_assinado"
        >
            <generator class="sequence">
                <param name="sequence">seq_documento_assinado</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                column="cd_gerenciador_arquivo_original"
                name="gerenciadorArquivoOriginal"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.comunicacao.GerenciadorArquivo"
                column="cd_gerenciador_arquivo_assinado"
                name="gerenciadorArquivoAssinado"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                column="nr_atendimento"
                name="atendimento"
                not-null="false"
        />

        <property
                column="flag_assinado"
                name="flagAssinado"
                not-null="true"
                type="java.lang.Long"
        />

        <property
                column="documento_origem"
                name="documentoOrigem"
                not-null="true"
                type="java.lang.Long"
        />

        <property
                column="tipo_documento_origem"
                name="tipoDocumentoOrigem"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                type="timestamp"
                not-null="true"
        />

        <property
                column="dt_link_retorno"
                name="dataLinkRetorno"
                type="timestamp"
                not-null="true"
        />

        <property
                column="uuid"
                name="uuid"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="link_retorno"
                name="linkRetorno"
                not-null="false"
                type="java.lang.String"
        />

        <property
                column="ds_tipo_arquivo"
                name="tipoArquivo"
                not-null="false"
                type="java.lang.String"
        />
    </class>
</hibernate-mapping>
