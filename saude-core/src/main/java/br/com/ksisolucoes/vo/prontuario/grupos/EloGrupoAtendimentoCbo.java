package br.com.ksisolucoes.vo.prontuario.grupos;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.grupos.base.BaseEloGrupoAtendimentoCbo;



public class EloGrupoAtendimentoCbo extends BaseEloGrupoAtendimentoCbo implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloGrupoAtendimentoCbo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloGrupoAtendimentoCbo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloGrupoAtendimentoCbo (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo grupoAtendimentoCbo,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {

		super (
			codigo,
			grupoAtendimentoCbo,
			tabelaCbo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

//	public String getFlagObrigatorioInformarCiapFormatado() {
//		if (RepositoryComponentDefault.SIM_LONG.equals(getFlagObrigatorioInformarCiap())) {
//			return Bundle.getStringApplication("rotulo_sim");
//		}
//		return Bundle.getStringApplication("rotulo_nao");
//	}
}