package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_acidente_animal_peconhento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_acidente_animal_peconhento"
 */

public abstract class BaseInvestigacaoAgravoAcidenteAnimalPeconhento extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoAcidenteAnimalPeconhento";
	public static final String PROP_MANIFESTACAO_SISTEMICA = "manifestacaoSistemica";
	public static final String PROP_TIPO_COMPLICACAO_SISTEMICA_INSUFICIENCIA_RESPIRATORIA = "tipoComplicacaoSistemicaInsuficienciaRespiratoria";
	public static final String PROP_NR_AMPOLAS_ANTICROLATICO = "nrAmpolasAnticrolatico";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_ACIDENTE_RELACIONADO_TRABALHO = "acidenteRelacionadoTrabalho";
	public static final String PROP_CASO_AUTOCTONE = "casoAutoctone";
	public static final String PROP_DISTRITO_LOCAL_ACIDENTE = "distritoLocalAcidente";
	public static final String PROP_COMPLICOES_LOCAIS = "complicoesLocais";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_TIPO_MANIFESTACAO_SISTEMICA_RENAIS = "tipoManifestacaoSistemicaRenais";
	public static final String PROP_DATA_ACIDENTE = "dataAcidente";
	public static final String PROP_TEMPO_DECORRIDO_PICADA_ATENDIMENTO = "tempoDecorridoPicadaAtendimento";
	public static final String PROP_TEMPO_COAGULACAO = "tempoCoagulacao";
	public static final String PROP_TIPO_COMPLICACAO_LOCAL_SINDROME_COMPARTIMENTAL = "tipoComplicacaoLocalSindromeCompartimental";
	public static final String PROP_TIPO_ACIDENTE_OUTROS_STR = "tipoAcidenteOutrosStr";
	public static final String PROP_TIPO_MANIFESTACAO_LOCAL_OUTROS_STR = "tipoManifestacaoLocalOutrosStr";
	public static final String PROP_NR_AMPOLAS_ANTIELAPIDICO = "nrAmpolasAntielapidico";
	public static final String PROP_TIPO_MANIFESTACAO_SISTEMICA_OUTROS = "tipoManifestacaoSistemicaOutros";
	public static final String PROP_TIPO_ACIDENTE_LAGARTA = "tipoAcidenteLagarta";
	public static final String PROP_TIPO_MANIFESTACAO_SISTEMICA_HEMOLITICA = "tipoManifestacaoSistemicaHemolitica";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NR_AMPOLAS_ANTIBOTROPICO_LAQUETICO = "nrAmpolasAntibotropicoLaquetico";
	public static final String PROP_TIPO_MANIFESTACAO_SISTEMICA_NEUROPARALITICA = "tipoManifestacaoSistemicaNeuroparalitica";
	public static final String PROP_TIPO_COMPLICACAO_LOCAL_AMPUTACAO = "tipoComplicacaoLocalAmputacao";
	public static final String PROP_LOCALIDADE_OCORRENCIA = "localidadeOcorrencia";
	public static final String PROP_TIPO_COMPLICACAO_LOCAL_NECROSE_EXTENSA = "tipoComplicacaoLocalNecroseExtensa";
	public static final String PROP_TIPO_COMPLICACAO_SISTEMICA_CHOQUE = "tipoComplicacaoSistemicaChoque";
	public static final String PROP_TIPO_MANIFESTACAO_SISTEMICA_HEMORRAGICA = "tipoManifestacaoSistemicaHemorragica";
	public static final String PROP_TIPO_MANIFESTACAO_LOCAL_DOR = "tipoManifestacaoLocalDor";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_NR_AMPOLAS_ANTILIXISCELICO = "nrAmpolasAntilixiscelico";
	public static final String PROP_CLASSIFICACAO_CASO = "classificacaoCaso";
	public static final String PROP_COMPLICACOES_SISTEMICAS = "complicacoesSistemicas";
	public static final String PROP_NR_AMPOLAS_ANTILONOMICO = "nrAmpolasAntilonomico";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_LOCAL_PICADA_CORPO = "localPicadaCorpo";
	public static final String PROP_NR_AMPOLAS_ANTIESCORPIANICO = "nrAmpolasAntiescorpianico";
	public static final String PROP_ZONA_OCORRENCIA = "zonaOcorrencia";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_TIPO_ACIDENTE_ARANHA = "tipoAcidenteAranha";
	public static final String PROP_TIPO_COMPLICACAO_SISTEMICA_INSUFICIENCIA_RENAL = "tipoComplicacaoSistemicaInsuficienciaRenal";
	public static final String PROP_BAIRRO_LOCAL_ACIDENTE = "bairroLocalAcidente";
	public static final String PROP_TIPO_COMPLICACAO_LOCAL_DEFICIT_FUNCIONAL = "tipoComplicacaoLocalDeficitFuncional";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_TIPO_ACIDENTE_SERPENTE = "tipoAcidenteSerpente";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_TIPO_MANIFESTACAO_SISTEMICA_VAGAIS = "tipoManifestacaoSistemicaVagais";
	public static final String PROP_CIDADE_LOCAL_ACIDENTE = "cidadeLocalAcidente";
	public static final String PROP_NR_AMPOLAS_ANTIBOTROPICO = "nrAmpolasAntibotropico";
	public static final String PROP_TIPO_MANIFESTACAO_LOCAL_EDEMA = "tipoManifestacaoLocalEdema";
	public static final String PROP_NR_AMPOLAS_ANTIARACNIDICO = "nrAmpolasAntiaracnidico";
	public static final String PROP_CIDADE_LOCAL_OCORRENCIA = "cidadeLocalOcorrencia";
	public static final String PROP_TIPO_COMPLICACAO_SISTEMICA_SEPTICEMIA = "tipoComplicacaoSistemicaSepticemia";
	public static final String PROP_TIPO_MANIFESTACAO_LOCAL_NECROSE = "tipoManifestacaoLocalNecrose";
	public static final String PROP_NR_AMPOLAS_ANTIBOTROPICO_CROLATICO = "nrAmpolasAntibotropicoCrolatico";
	public static final String PROP_TIPO_MANIFESTACAO_LOCAL_OUTROS = "tipoManifestacaoLocalOutros";
	public static final String PROP_TIPO_MANIFESTACAO_SISTEMICA_OUTROS_STR = "tipoManifestacaoSistemicaOutrosStr";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_TIPO_COMPLICACAO_LOCAL_INFECCAO_SECUNDARIA = "tipoComplicacaoLocalInfeccaoSecundaria";
	public static final String PROP_TIPO_ACIDENTE = "tipoAcidente";
	public static final String PROP_SOROTERAPIA = "soroterapia";
	public static final String PROP_TIPO_MANIFESTACAO_LOCAL_EQUIMOSE = "tipoManifestacaoLocalEquimose";
	public static final String PROP_MANIFESTACAO_LOCAL = "manifestacaoLocal";
	public static final String PROP_PAIS_LOCAL_ACIDENTE = "paisLocalAcidente";


	// constructors
	public BaseInvestigacaoAgravoAcidenteAnimalPeconhento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoAcidenteAnimalPeconhento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoAcidenteAnimalPeconhento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.lang.Long casoAutoctone;
	private java.lang.String distritoLocalAcidente;
	private java.lang.String bairroLocalAcidente;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;
	private java.util.Date dataAcidente;
	private java.lang.String localidadeOcorrencia;
	private java.lang.Long zonaOcorrencia;
	private java.lang.Long tempoDecorridoPicadaAtendimento;
	private java.lang.Long localPicadaCorpo;
	private java.lang.Long manifestacaoLocal;
	private java.lang.Long tipoManifestacaoLocalDor;
	private java.lang.Long tipoManifestacaoLocalEdema;
	private java.lang.Long tipoManifestacaoLocalEquimose;
	private java.lang.Long tipoManifestacaoLocalNecrose;
	private java.lang.Long tipoManifestacaoLocalOutros;
	private java.lang.String tipoManifestacaoLocalOutrosStr;
	private java.lang.Long manifestacaoSistemica;
	private java.lang.Long tipoManifestacaoSistemicaNeuroparalitica;
	private java.lang.Long tipoManifestacaoSistemicaHemorragica;
	private java.lang.Long tipoManifestacaoSistemicaVagais;
	private java.lang.Long tipoManifestacaoSistemicaHemolitica;
	private java.lang.Long tipoManifestacaoSistemicaRenais;
	private java.lang.Long tipoManifestacaoSistemicaOutros;
	private java.lang.String tipoManifestacaoSistemicaOutrosStr;
	private java.lang.Long tempoCoagulacao;
	private java.lang.Long tipoAcidente;
	private java.lang.String tipoAcidenteOutrosStr;
	private java.lang.Long tipoAcidenteSerpente;
	private java.lang.Long tipoAcidenteAranha;
	private java.lang.Long tipoAcidenteLagarta;
	private java.lang.Long classificacaoCaso;
	private java.lang.Long soroterapia;
	private java.lang.Long nrAmpolasAntibotropico;
	private java.lang.Long nrAmpolasAntibotropicoLaquetico;
	private java.lang.Long nrAmpolasAntibotropicoCrolatico;
	private java.lang.Long nrAmpolasAnticrolatico;
	private java.lang.Long nrAmpolasAntielapidico;
	private java.lang.Long nrAmpolasAntiescorpianico;
	private java.lang.Long nrAmpolasAntiaracnidico;
	private java.lang.Long nrAmpolasAntilixiscelico;
	private java.lang.Long nrAmpolasAntilonomico;
	private java.lang.Long complicoesLocais;
	private java.lang.Long tipoComplicacaoLocalInfeccaoSecundaria;
	private java.lang.Long tipoComplicacaoLocalNecroseExtensa;
	private java.lang.Long tipoComplicacaoLocalSindromeCompartimental;
	private java.lang.Long tipoComplicacaoLocalDeficitFuncional;
	private java.lang.Long tipoComplicacaoLocalAmputacao;
	private java.lang.Long complicacoesSistemicas;
	private java.lang.Long tipoComplicacaoSistemicaInsuficienciaRenal;
	private java.lang.Long tipoComplicacaoSistemicaInsuficienciaRespiratoria;
	private java.lang.Long tipoComplicacaoSistemicaSepticemia;
	private java.lang.Long tipoComplicacaoSistemicaChoque;
	private java.lang.Long acidenteRelacionadoTrabalho;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalAcidente;
	private br.com.ksisolucoes.vo.basico.Pais paisLocalAcidente;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;
	private br.com.ksisolucoes.vo.basico.Cidade cidadeLocalOcorrencia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_invest_agr_acidente_animal_peconhento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: caso_autoctone
	 */
	public java.lang.Long getCasoAutoctone () {
		return getPropertyValue(this, casoAutoctone, PROP_CASO_AUTOCTONE); 
	}

	/**
	 * Set the value related to the column: caso_autoctone
	 * @param casoAutoctone the caso_autoctone value
	 */
	public void setCasoAutoctone (java.lang.Long casoAutoctone) {
//        java.lang.Long casoAutoctoneOld = this.casoAutoctone;
		this.casoAutoctone = casoAutoctone;
//        this.getPropertyChangeSupport().firePropertyChange ("casoAutoctone", casoAutoctoneOld, casoAutoctone);
	}



	/**
	 * Return the value associated with the column: str_distrito_acidente
	 */
	public java.lang.String getDistritoLocalAcidente () {
		return getPropertyValue(this, distritoLocalAcidente, PROP_DISTRITO_LOCAL_ACIDENTE); 
	}

	/**
	 * Set the value related to the column: str_distrito_acidente
	 * @param distritoLocalAcidente the str_distrito_acidente value
	 */
	public void setDistritoLocalAcidente (java.lang.String distritoLocalAcidente) {
//        java.lang.String distritoLocalAcidenteOld = this.distritoLocalAcidente;
		this.distritoLocalAcidente = distritoLocalAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("distritoLocalAcidente", distritoLocalAcidenteOld, distritoLocalAcidente);
	}



	/**
	 * Return the value associated with the column: str_bairro_acidente
	 */
	public java.lang.String getBairroLocalAcidente () {
		return getPropertyValue(this, bairroLocalAcidente, PROP_BAIRRO_LOCAL_ACIDENTE); 
	}

	/**
	 * Set the value related to the column: str_bairro_acidente
	 * @param bairroLocalAcidente the str_bairro_acidente value
	 */
	public void setBairroLocalAcidente (java.lang.String bairroLocalAcidente) {
//        java.lang.String bairroLocalAcidenteOld = this.bairroLocalAcidente;
		this.bairroLocalAcidente = bairroLocalAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("bairroLocalAcidente", bairroLocalAcidenteOld, bairroLocalAcidente);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: data_acidente
	 */
	public java.util.Date getDataAcidente () {
		return getPropertyValue(this, dataAcidente, PROP_DATA_ACIDENTE); 
	}

	/**
	 * Set the value related to the column: data_acidente
	 * @param dataAcidente the data_acidente value
	 */
	public void setDataAcidente (java.util.Date dataAcidente) {
//        java.util.Date dataAcidenteOld = this.dataAcidente;
		this.dataAcidente = dataAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAcidente", dataAcidenteOld, dataAcidente);
	}



	/**
	 * Return the value associated with the column: str_localidade_ocorrencia
	 */
	public java.lang.String getLocalidadeOcorrencia () {
		return getPropertyValue(this, localidadeOcorrencia, PROP_LOCALIDADE_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: str_localidade_ocorrencia
	 * @param localidadeOcorrencia the str_localidade_ocorrencia value
	 */
	public void setLocalidadeOcorrencia (java.lang.String localidadeOcorrencia) {
//        java.lang.String localidadeOcorrenciaOld = this.localidadeOcorrencia;
		this.localidadeOcorrencia = localidadeOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("localidadeOcorrencia", localidadeOcorrenciaOld, localidadeOcorrencia);
	}



	/**
	 * Return the value associated with the column: zona_ocorrencia
	 */
	public java.lang.Long getZonaOcorrencia () {
		return getPropertyValue(this, zonaOcorrencia, PROP_ZONA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: zona_ocorrencia
	 * @param zonaOcorrencia the zona_ocorrencia value
	 */
	public void setZonaOcorrencia (java.lang.Long zonaOcorrencia) {
//        java.lang.Long zonaOcorrenciaOld = this.zonaOcorrencia;
		this.zonaOcorrencia = zonaOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("zonaOcorrencia", zonaOcorrenciaOld, zonaOcorrencia);
	}



	/**
	 * Return the value associated with the column: tempo_decorrido_picada_atendimento
	 */
	public java.lang.Long getTempoDecorridoPicadaAtendimento () {
		return getPropertyValue(this, tempoDecorridoPicadaAtendimento, PROP_TEMPO_DECORRIDO_PICADA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: tempo_decorrido_picada_atendimento
	 * @param tempoDecorridoPicadaAtendimento the tempo_decorrido_picada_atendimento value
	 */
	public void setTempoDecorridoPicadaAtendimento (java.lang.Long tempoDecorridoPicadaAtendimento) {
//        java.lang.Long tempoDecorridoPicadaAtendimentoOld = this.tempoDecorridoPicadaAtendimento;
		this.tempoDecorridoPicadaAtendimento = tempoDecorridoPicadaAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoDecorridoPicadaAtendimento", tempoDecorridoPicadaAtendimentoOld, tempoDecorridoPicadaAtendimento);
	}



	/**
	 * Return the value associated with the column: local_picada_corpo
	 */
	public java.lang.Long getLocalPicadaCorpo () {
		return getPropertyValue(this, localPicadaCorpo, PROP_LOCAL_PICADA_CORPO); 
	}

	/**
	 * Set the value related to the column: local_picada_corpo
	 * @param localPicadaCorpo the local_picada_corpo value
	 */
	public void setLocalPicadaCorpo (java.lang.Long localPicadaCorpo) {
//        java.lang.Long localPicadaCorpoOld = this.localPicadaCorpo;
		this.localPicadaCorpo = localPicadaCorpo;
//        this.getPropertyChangeSupport().firePropertyChange ("localPicadaCorpo", localPicadaCorpoOld, localPicadaCorpo);
	}



	/**
	 * Return the value associated with the column: manifestacoes_locais
	 */
	public java.lang.Long getManifestacaoLocal () {
		return getPropertyValue(this, manifestacaoLocal, PROP_MANIFESTACAO_LOCAL); 
	}

	/**
	 * Set the value related to the column: manifestacoes_locais
	 * @param manifestacaoLocal the manifestacoes_locais value
	 */
	public void setManifestacaoLocal (java.lang.Long manifestacaoLocal) {
//        java.lang.Long manifestacaoLocalOld = this.manifestacaoLocal;
		this.manifestacaoLocal = manifestacaoLocal;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoLocal", manifestacaoLocalOld, manifestacaoLocal);
	}



	/**
	 * Return the value associated with the column: tp_man_local_dor
	 */
	public java.lang.Long getTipoManifestacaoLocalDor () {
		return getPropertyValue(this, tipoManifestacaoLocalDor, PROP_TIPO_MANIFESTACAO_LOCAL_DOR); 
	}

	/**
	 * Set the value related to the column: tp_man_local_dor
	 * @param tipoManifestacaoLocalDor the tp_man_local_dor value
	 */
	public void setTipoManifestacaoLocalDor (java.lang.Long tipoManifestacaoLocalDor) {
//        java.lang.Long tipoManifestacaoLocalDorOld = this.tipoManifestacaoLocalDor;
		this.tipoManifestacaoLocalDor = tipoManifestacaoLocalDor;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoLocalDor", tipoManifestacaoLocalDorOld, tipoManifestacaoLocalDor);
	}



	/**
	 * Return the value associated with the column: tp_man_local_edema
	 */
	public java.lang.Long getTipoManifestacaoLocalEdema () {
		return getPropertyValue(this, tipoManifestacaoLocalEdema, PROP_TIPO_MANIFESTACAO_LOCAL_EDEMA); 
	}

	/**
	 * Set the value related to the column: tp_man_local_edema
	 * @param tipoManifestacaoLocalEdema the tp_man_local_edema value
	 */
	public void setTipoManifestacaoLocalEdema (java.lang.Long tipoManifestacaoLocalEdema) {
//        java.lang.Long tipoManifestacaoLocalEdemaOld = this.tipoManifestacaoLocalEdema;
		this.tipoManifestacaoLocalEdema = tipoManifestacaoLocalEdema;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoLocalEdema", tipoManifestacaoLocalEdemaOld, tipoManifestacaoLocalEdema);
	}



	/**
	 * Return the value associated with the column: tp_man_local_equimose
	 */
	public java.lang.Long getTipoManifestacaoLocalEquimose () {
		return getPropertyValue(this, tipoManifestacaoLocalEquimose, PROP_TIPO_MANIFESTACAO_LOCAL_EQUIMOSE); 
	}

	/**
	 * Set the value related to the column: tp_man_local_equimose
	 * @param tipoManifestacaoLocalEquimose the tp_man_local_equimose value
	 */
	public void setTipoManifestacaoLocalEquimose (java.lang.Long tipoManifestacaoLocalEquimose) {
//        java.lang.Long tipoManifestacaoLocalEquimoseOld = this.tipoManifestacaoLocalEquimose;
		this.tipoManifestacaoLocalEquimose = tipoManifestacaoLocalEquimose;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoLocalEquimose", tipoManifestacaoLocalEquimoseOld, tipoManifestacaoLocalEquimose);
	}



	/**
	 * Return the value associated with the column: tp_man_local_necrose
	 */
	public java.lang.Long getTipoManifestacaoLocalNecrose () {
		return getPropertyValue(this, tipoManifestacaoLocalNecrose, PROP_TIPO_MANIFESTACAO_LOCAL_NECROSE); 
	}

	/**
	 * Set the value related to the column: tp_man_local_necrose
	 * @param tipoManifestacaoLocalNecrose the tp_man_local_necrose value
	 */
	public void setTipoManifestacaoLocalNecrose (java.lang.Long tipoManifestacaoLocalNecrose) {
//        java.lang.Long tipoManifestacaoLocalNecroseOld = this.tipoManifestacaoLocalNecrose;
		this.tipoManifestacaoLocalNecrose = tipoManifestacaoLocalNecrose;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoLocalNecrose", tipoManifestacaoLocalNecroseOld, tipoManifestacaoLocalNecrose);
	}



	/**
	 * Return the value associated with the column: tp_man_local_outros
	 */
	public java.lang.Long getTipoManifestacaoLocalOutros () {
		return getPropertyValue(this, tipoManifestacaoLocalOutros, PROP_TIPO_MANIFESTACAO_LOCAL_OUTROS); 
	}

	/**
	 * Set the value related to the column: tp_man_local_outros
	 * @param tipoManifestacaoLocalOutros the tp_man_local_outros value
	 */
	public void setTipoManifestacaoLocalOutros (java.lang.Long tipoManifestacaoLocalOutros) {
//        java.lang.Long tipoManifestacaoLocalOutrosOld = this.tipoManifestacaoLocalOutros;
		this.tipoManifestacaoLocalOutros = tipoManifestacaoLocalOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoLocalOutros", tipoManifestacaoLocalOutrosOld, tipoManifestacaoLocalOutros);
	}



	/**
	 * Return the value associated with the column: tp_man_local_outros_str
	 */
	public java.lang.String getTipoManifestacaoLocalOutrosStr () {
		return getPropertyValue(this, tipoManifestacaoLocalOutrosStr, PROP_TIPO_MANIFESTACAO_LOCAL_OUTROS_STR); 
	}

	/**
	 * Set the value related to the column: tp_man_local_outros_str
	 * @param tipoManifestacaoLocalOutrosStr the tp_man_local_outros_str value
	 */
	public void setTipoManifestacaoLocalOutrosStr (java.lang.String tipoManifestacaoLocalOutrosStr) {
//        java.lang.String tipoManifestacaoLocalOutrosStrOld = this.tipoManifestacaoLocalOutrosStr;
		this.tipoManifestacaoLocalOutrosStr = tipoManifestacaoLocalOutrosStr;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoLocalOutrosStr", tipoManifestacaoLocalOutrosStrOld, tipoManifestacaoLocalOutrosStr);
	}



	/**
	 * Return the value associated with the column: manifestacoes_sistemicas
	 */
	public java.lang.Long getManifestacaoSistemica () {
		return getPropertyValue(this, manifestacaoSistemica, PROP_MANIFESTACAO_SISTEMICA); 
	}

	/**
	 * Set the value related to the column: manifestacoes_sistemicas
	 * @param manifestacaoSistemica the manifestacoes_sistemicas value
	 */
	public void setManifestacaoSistemica (java.lang.Long manifestacaoSistemica) {
//        java.lang.Long manifestacaoSistemicaOld = this.manifestacaoSistemica;
		this.manifestacaoSistemica = manifestacaoSistemica;
//        this.getPropertyChangeSupport().firePropertyChange ("manifestacaoSistemica", manifestacaoSistemicaOld, manifestacaoSistemica);
	}



	/**
	 * Return the value associated with the column: tp_man_sistemicas_neuroparalitica
	 */
	public java.lang.Long getTipoManifestacaoSistemicaNeuroparalitica () {
		return getPropertyValue(this, tipoManifestacaoSistemicaNeuroparalitica, PROP_TIPO_MANIFESTACAO_SISTEMICA_NEUROPARALITICA); 
	}

	/**
	 * Set the value related to the column: tp_man_sistemicas_neuroparalitica
	 * @param tipoManifestacaoSistemicaNeuroparalitica the tp_man_sistemicas_neuroparalitica value
	 */
	public void setTipoManifestacaoSistemicaNeuroparalitica (java.lang.Long tipoManifestacaoSistemicaNeuroparalitica) {
//        java.lang.Long tipoManifestacaoSistemicaNeuroparaliticaOld = this.tipoManifestacaoSistemicaNeuroparalitica;
		this.tipoManifestacaoSistemicaNeuroparalitica = tipoManifestacaoSistemicaNeuroparalitica;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoSistemicaNeuroparalitica", tipoManifestacaoSistemicaNeuroparaliticaOld, tipoManifestacaoSistemicaNeuroparalitica);
	}



	/**
	 * Return the value associated with the column: tp_man_sistemicas_hemorragica
	 */
	public java.lang.Long getTipoManifestacaoSistemicaHemorragica () {
		return getPropertyValue(this, tipoManifestacaoSistemicaHemorragica, PROP_TIPO_MANIFESTACAO_SISTEMICA_HEMORRAGICA); 
	}

	/**
	 * Set the value related to the column: tp_man_sistemicas_hemorragica
	 * @param tipoManifestacaoSistemicaHemorragica the tp_man_sistemicas_hemorragica value
	 */
	public void setTipoManifestacaoSistemicaHemorragica (java.lang.Long tipoManifestacaoSistemicaHemorragica) {
//        java.lang.Long tipoManifestacaoSistemicaHemorragicaOld = this.tipoManifestacaoSistemicaHemorragica;
		this.tipoManifestacaoSistemicaHemorragica = tipoManifestacaoSistemicaHemorragica;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoSistemicaHemorragica", tipoManifestacaoSistemicaHemorragicaOld, tipoManifestacaoSistemicaHemorragica);
	}



	/**
	 * Return the value associated with the column: tp_man_sistemicas_vagais
	 */
	public java.lang.Long getTipoManifestacaoSistemicaVagais () {
		return getPropertyValue(this, tipoManifestacaoSistemicaVagais, PROP_TIPO_MANIFESTACAO_SISTEMICA_VAGAIS); 
	}

	/**
	 * Set the value related to the column: tp_man_sistemicas_vagais
	 * @param tipoManifestacaoSistemicaVagais the tp_man_sistemicas_vagais value
	 */
	public void setTipoManifestacaoSistemicaVagais (java.lang.Long tipoManifestacaoSistemicaVagais) {
//        java.lang.Long tipoManifestacaoSistemicaVagaisOld = this.tipoManifestacaoSistemicaVagais;
		this.tipoManifestacaoSistemicaVagais = tipoManifestacaoSistemicaVagais;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoSistemicaVagais", tipoManifestacaoSistemicaVagaisOld, tipoManifestacaoSistemicaVagais);
	}



	/**
	 * Return the value associated with the column: tp_man_sistemicas_miolitica_hemolitica
	 */
	public java.lang.Long getTipoManifestacaoSistemicaHemolitica () {
		return getPropertyValue(this, tipoManifestacaoSistemicaHemolitica, PROP_TIPO_MANIFESTACAO_SISTEMICA_HEMOLITICA); 
	}

	/**
	 * Set the value related to the column: tp_man_sistemicas_miolitica_hemolitica
	 * @param tipoManifestacaoSistemicaHemolitica the tp_man_sistemicas_miolitica_hemolitica value
	 */
	public void setTipoManifestacaoSistemicaHemolitica (java.lang.Long tipoManifestacaoSistemicaHemolitica) {
//        java.lang.Long tipoManifestacaoSistemicaHemoliticaOld = this.tipoManifestacaoSistemicaHemolitica;
		this.tipoManifestacaoSistemicaHemolitica = tipoManifestacaoSistemicaHemolitica;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoSistemicaHemolitica", tipoManifestacaoSistemicaHemoliticaOld, tipoManifestacaoSistemicaHemolitica);
	}



	/**
	 * Return the value associated with the column: tp_man_sistemicas_renais
	 */
	public java.lang.Long getTipoManifestacaoSistemicaRenais () {
		return getPropertyValue(this, tipoManifestacaoSistemicaRenais, PROP_TIPO_MANIFESTACAO_SISTEMICA_RENAIS); 
	}

	/**
	 * Set the value related to the column: tp_man_sistemicas_renais
	 * @param tipoManifestacaoSistemicaRenais the tp_man_sistemicas_renais value
	 */
	public void setTipoManifestacaoSistemicaRenais (java.lang.Long tipoManifestacaoSistemicaRenais) {
//        java.lang.Long tipoManifestacaoSistemicaRenaisOld = this.tipoManifestacaoSistemicaRenais;
		this.tipoManifestacaoSistemicaRenais = tipoManifestacaoSistemicaRenais;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoSistemicaRenais", tipoManifestacaoSistemicaRenaisOld, tipoManifestacaoSistemicaRenais);
	}



	/**
	 * Return the value associated with the column: tp_man_sistemicas_outros
	 */
	public java.lang.Long getTipoManifestacaoSistemicaOutros () {
		return getPropertyValue(this, tipoManifestacaoSistemicaOutros, PROP_TIPO_MANIFESTACAO_SISTEMICA_OUTROS); 
	}

	/**
	 * Set the value related to the column: tp_man_sistemicas_outros
	 * @param tipoManifestacaoSistemicaOutros the tp_man_sistemicas_outros value
	 */
	public void setTipoManifestacaoSistemicaOutros (java.lang.Long tipoManifestacaoSistemicaOutros) {
//        java.lang.Long tipoManifestacaoSistemicaOutrosOld = this.tipoManifestacaoSistemicaOutros;
		this.tipoManifestacaoSistemicaOutros = tipoManifestacaoSistemicaOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoSistemicaOutros", tipoManifestacaoSistemicaOutrosOld, tipoManifestacaoSistemicaOutros);
	}



	/**
	 * Return the value associated with the column: tp_man_sistemicas_outros_str
	 */
	public java.lang.String getTipoManifestacaoSistemicaOutrosStr () {
		return getPropertyValue(this, tipoManifestacaoSistemicaOutrosStr, PROP_TIPO_MANIFESTACAO_SISTEMICA_OUTROS_STR); 
	}

	/**
	 * Set the value related to the column: tp_man_sistemicas_outros_str
	 * @param tipoManifestacaoSistemicaOutrosStr the tp_man_sistemicas_outros_str value
	 */
	public void setTipoManifestacaoSistemicaOutrosStr (java.lang.String tipoManifestacaoSistemicaOutrosStr) {
//        java.lang.String tipoManifestacaoSistemicaOutrosStrOld = this.tipoManifestacaoSistemicaOutrosStr;
		this.tipoManifestacaoSistemicaOutrosStr = tipoManifestacaoSistemicaOutrosStr;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoManifestacaoSistemicaOutrosStr", tipoManifestacaoSistemicaOutrosStrOld, tipoManifestacaoSistemicaOutrosStr);
	}



	/**
	 * Return the value associated with the column: tempo_coagulacao
	 */
	public java.lang.Long getTempoCoagulacao () {
		return getPropertyValue(this, tempoCoagulacao, PROP_TEMPO_COAGULACAO); 
	}

	/**
	 * Set the value related to the column: tempo_coagulacao
	 * @param tempoCoagulacao the tempo_coagulacao value
	 */
	public void setTempoCoagulacao (java.lang.Long tempoCoagulacao) {
//        java.lang.Long tempoCoagulacaoOld = this.tempoCoagulacao;
		this.tempoCoagulacao = tempoCoagulacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tempoCoagulacao", tempoCoagulacaoOld, tempoCoagulacao);
	}



	/**
	 * Return the value associated with the column: tipo_acidente
	 */
	public java.lang.Long getTipoAcidente () {
		return getPropertyValue(this, tipoAcidente, PROP_TIPO_ACIDENTE); 
	}

	/**
	 * Set the value related to the column: tipo_acidente
	 * @param tipoAcidente the tipo_acidente value
	 */
	public void setTipoAcidente (java.lang.Long tipoAcidente) {
//        java.lang.Long tipoAcidenteOld = this.tipoAcidente;
		this.tipoAcidente = tipoAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAcidente", tipoAcidenteOld, tipoAcidente);
	}



	/**
	 * Return the value associated with the column: tp_acidente_outros_str
	 */
	public java.lang.String getTipoAcidenteOutrosStr () {
		return getPropertyValue(this, tipoAcidenteOutrosStr, PROP_TIPO_ACIDENTE_OUTROS_STR); 
	}

	/**
	 * Set the value related to the column: tp_acidente_outros_str
	 * @param tipoAcidenteOutrosStr the tp_acidente_outros_str value
	 */
	public void setTipoAcidenteOutrosStr (java.lang.String tipoAcidenteOutrosStr) {
//        java.lang.String tipoAcidenteOutrosStrOld = this.tipoAcidenteOutrosStr;
		this.tipoAcidenteOutrosStr = tipoAcidenteOutrosStr;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAcidenteOutrosStr", tipoAcidenteOutrosStrOld, tipoAcidenteOutrosStr);
	}



	/**
	 * Return the value associated with the column: tp_acidente_serpente
	 */
	public java.lang.Long getTipoAcidenteSerpente () {
		return getPropertyValue(this, tipoAcidenteSerpente, PROP_TIPO_ACIDENTE_SERPENTE); 
	}

	/**
	 * Set the value related to the column: tp_acidente_serpente
	 * @param tipoAcidenteSerpente the tp_acidente_serpente value
	 */
	public void setTipoAcidenteSerpente (java.lang.Long tipoAcidenteSerpente) {
//        java.lang.Long tipoAcidenteSerpenteOld = this.tipoAcidenteSerpente;
		this.tipoAcidenteSerpente = tipoAcidenteSerpente;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAcidenteSerpente", tipoAcidenteSerpenteOld, tipoAcidenteSerpente);
	}



	/**
	 * Return the value associated with the column: tp_acidente_aranha
	 */
	public java.lang.Long getTipoAcidenteAranha () {
		return getPropertyValue(this, tipoAcidenteAranha, PROP_TIPO_ACIDENTE_ARANHA); 
	}

	/**
	 * Set the value related to the column: tp_acidente_aranha
	 * @param tipoAcidenteAranha the tp_acidente_aranha value
	 */
	public void setTipoAcidenteAranha (java.lang.Long tipoAcidenteAranha) {
//        java.lang.Long tipoAcidenteAranhaOld = this.tipoAcidenteAranha;
		this.tipoAcidenteAranha = tipoAcidenteAranha;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAcidenteAranha", tipoAcidenteAranhaOld, tipoAcidenteAranha);
	}



	/**
	 * Return the value associated with the column: tp_acidente_lagarta
	 */
	public java.lang.Long getTipoAcidenteLagarta () {
		return getPropertyValue(this, tipoAcidenteLagarta, PROP_TIPO_ACIDENTE_LAGARTA); 
	}

	/**
	 * Set the value related to the column: tp_acidente_lagarta
	 * @param tipoAcidenteLagarta the tp_acidente_lagarta value
	 */
	public void setTipoAcidenteLagarta (java.lang.Long tipoAcidenteLagarta) {
//        java.lang.Long tipoAcidenteLagartaOld = this.tipoAcidenteLagarta;
		this.tipoAcidenteLagarta = tipoAcidenteLagarta;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAcidenteLagarta", tipoAcidenteLagartaOld, tipoAcidenteLagarta);
	}



	/**
	 * Return the value associated with the column: classificacao_caso
	 */
	public java.lang.Long getClassificacaoCaso () {
		return getPropertyValue(this, classificacaoCaso, PROP_CLASSIFICACAO_CASO); 
	}

	/**
	 * Set the value related to the column: classificacao_caso
	 * @param classificacaoCaso the classificacao_caso value
	 */
	public void setClassificacaoCaso (java.lang.Long classificacaoCaso) {
//        java.lang.Long classificacaoCasoOld = this.classificacaoCaso;
		this.classificacaoCaso = classificacaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoCaso", classificacaoCasoOld, classificacaoCaso);
	}



	/**
	 * Return the value associated with the column: soroterapia
	 */
	public java.lang.Long getSoroterapia () {
		return getPropertyValue(this, soroterapia, PROP_SOROTERAPIA); 
	}

	/**
	 * Set the value related to the column: soroterapia
	 * @param soroterapia the soroterapia value
	 */
	public void setSoroterapia (java.lang.Long soroterapia) {
//        java.lang.Long soroterapiaOld = this.soroterapia;
		this.soroterapia = soroterapia;
//        this.getPropertyChangeSupport().firePropertyChange ("soroterapia", soroterapiaOld, soroterapia);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antibotropico
	 */
	public java.lang.Long getNrAmpolasAntibotropico () {
		return getPropertyValue(this, nrAmpolasAntibotropico, PROP_NR_AMPOLAS_ANTIBOTROPICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antibotropico
	 * @param nrAmpolasAntibotropico the nr_ampolas_antibotropico value
	 */
	public void setNrAmpolasAntibotropico (java.lang.Long nrAmpolasAntibotropico) {
//        java.lang.Long nrAmpolasAntibotropicoOld = this.nrAmpolasAntibotropico;
		this.nrAmpolasAntibotropico = nrAmpolasAntibotropico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntibotropico", nrAmpolasAntibotropicoOld, nrAmpolasAntibotropico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antibotropico_laquetico
	 */
	public java.lang.Long getNrAmpolasAntibotropicoLaquetico () {
		return getPropertyValue(this, nrAmpolasAntibotropicoLaquetico, PROP_NR_AMPOLAS_ANTIBOTROPICO_LAQUETICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antibotropico_laquetico
	 * @param nrAmpolasAntibotropicoLaquetico the nr_ampolas_antibotropico_laquetico value
	 */
	public void setNrAmpolasAntibotropicoLaquetico (java.lang.Long nrAmpolasAntibotropicoLaquetico) {
//        java.lang.Long nrAmpolasAntibotropicoLaqueticoOld = this.nrAmpolasAntibotropicoLaquetico;
		this.nrAmpolasAntibotropicoLaquetico = nrAmpolasAntibotropicoLaquetico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntibotropicoLaquetico", nrAmpolasAntibotropicoLaqueticoOld, nrAmpolasAntibotropicoLaquetico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antibotropico_crolatico
	 */
	public java.lang.Long getNrAmpolasAntibotropicoCrolatico () {
		return getPropertyValue(this, nrAmpolasAntibotropicoCrolatico, PROP_NR_AMPOLAS_ANTIBOTROPICO_CROLATICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antibotropico_crolatico
	 * @param nrAmpolasAntibotropicoCrolatico the nr_ampolas_antibotropico_crolatico value
	 */
	public void setNrAmpolasAntibotropicoCrolatico (java.lang.Long nrAmpolasAntibotropicoCrolatico) {
//        java.lang.Long nrAmpolasAntibotropicoCrolaticoOld = this.nrAmpolasAntibotropicoCrolatico;
		this.nrAmpolasAntibotropicoCrolatico = nrAmpolasAntibotropicoCrolatico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntibotropicoCrolatico", nrAmpolasAntibotropicoCrolaticoOld, nrAmpolasAntibotropicoCrolatico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_anticrolatico
	 */
	public java.lang.Long getNrAmpolasAnticrolatico () {
		return getPropertyValue(this, nrAmpolasAnticrolatico, PROP_NR_AMPOLAS_ANTICROLATICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_anticrolatico
	 * @param nrAmpolasAnticrolatico the nr_ampolas_anticrolatico value
	 */
	public void setNrAmpolasAnticrolatico (java.lang.Long nrAmpolasAnticrolatico) {
//        java.lang.Long nrAmpolasAnticrolaticoOld = this.nrAmpolasAnticrolatico;
		this.nrAmpolasAnticrolatico = nrAmpolasAnticrolatico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAnticrolatico", nrAmpolasAnticrolaticoOld, nrAmpolasAnticrolatico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antielapidico
	 */
	public java.lang.Long getNrAmpolasAntielapidico () {
		return getPropertyValue(this, nrAmpolasAntielapidico, PROP_NR_AMPOLAS_ANTIELAPIDICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antielapidico
	 * @param nrAmpolasAntielapidico the nr_ampolas_antielapidico value
	 */
	public void setNrAmpolasAntielapidico (java.lang.Long nrAmpolasAntielapidico) {
//        java.lang.Long nrAmpolasAntielapidicoOld = this.nrAmpolasAntielapidico;
		this.nrAmpolasAntielapidico = nrAmpolasAntielapidico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntielapidico", nrAmpolasAntielapidicoOld, nrAmpolasAntielapidico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antiescorpianico
	 */
	public java.lang.Long getNrAmpolasAntiescorpianico () {
		return getPropertyValue(this, nrAmpolasAntiescorpianico, PROP_NR_AMPOLAS_ANTIESCORPIANICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antiescorpianico
	 * @param nrAmpolasAntiescorpianico the nr_ampolas_antiescorpianico value
	 */
	public void setNrAmpolasAntiescorpianico (java.lang.Long nrAmpolasAntiescorpianico) {
//        java.lang.Long nrAmpolasAntiescorpianicoOld = this.nrAmpolasAntiescorpianico;
		this.nrAmpolasAntiescorpianico = nrAmpolasAntiescorpianico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntiescorpianico", nrAmpolasAntiescorpianicoOld, nrAmpolasAntiescorpianico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antiaracnidico
	 */
	public java.lang.Long getNrAmpolasAntiaracnidico () {
		return getPropertyValue(this, nrAmpolasAntiaracnidico, PROP_NR_AMPOLAS_ANTIARACNIDICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antiaracnidico
	 * @param nrAmpolasAntiaracnidico the nr_ampolas_antiaracnidico value
	 */
	public void setNrAmpolasAntiaracnidico (java.lang.Long nrAmpolasAntiaracnidico) {
//        java.lang.Long nrAmpolasAntiaracnidicoOld = this.nrAmpolasAntiaracnidico;
		this.nrAmpolasAntiaracnidico = nrAmpolasAntiaracnidico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntiaracnidico", nrAmpolasAntiaracnidicoOld, nrAmpolasAntiaracnidico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antilixiscelico
	 */
	public java.lang.Long getNrAmpolasAntilixiscelico () {
		return getPropertyValue(this, nrAmpolasAntilixiscelico, PROP_NR_AMPOLAS_ANTILIXISCELICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antilixiscelico
	 * @param nrAmpolasAntilixiscelico the nr_ampolas_antilixiscelico value
	 */
	public void setNrAmpolasAntilixiscelico (java.lang.Long nrAmpolasAntilixiscelico) {
//        java.lang.Long nrAmpolasAntilixiscelicoOld = this.nrAmpolasAntilixiscelico;
		this.nrAmpolasAntilixiscelico = nrAmpolasAntilixiscelico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntilixiscelico", nrAmpolasAntilixiscelicoOld, nrAmpolasAntilixiscelico);
	}



	/**
	 * Return the value associated with the column: nr_ampolas_antilonomico
	 */
	public java.lang.Long getNrAmpolasAntilonomico () {
		return getPropertyValue(this, nrAmpolasAntilonomico, PROP_NR_AMPOLAS_ANTILONOMICO); 
	}

	/**
	 * Set the value related to the column: nr_ampolas_antilonomico
	 * @param nrAmpolasAntilonomico the nr_ampolas_antilonomico value
	 */
	public void setNrAmpolasAntilonomico (java.lang.Long nrAmpolasAntilonomico) {
//        java.lang.Long nrAmpolasAntilonomicoOld = this.nrAmpolasAntilonomico;
		this.nrAmpolasAntilonomico = nrAmpolasAntilonomico;
//        this.getPropertyChangeSupport().firePropertyChange ("nrAmpolasAntilonomico", nrAmpolasAntilonomicoOld, nrAmpolasAntilonomico);
	}



	/**
	 * Return the value associated with the column: complicacoes_locais
	 */
	public java.lang.Long getComplicoesLocais () {
		return getPropertyValue(this, complicoesLocais, PROP_COMPLICOES_LOCAIS); 
	}

	/**
	 * Set the value related to the column: complicacoes_locais
	 * @param complicoesLocais the complicacoes_locais value
	 */
	public void setComplicoesLocais (java.lang.Long complicoesLocais) {
//        java.lang.Long complicoesLocaisOld = this.complicoesLocais;
		this.complicoesLocais = complicoesLocais;
//        this.getPropertyChangeSupport().firePropertyChange ("complicoesLocais", complicoesLocaisOld, complicoesLocais);
	}



	/**
	 * Return the value associated with the column: tp_comp_local_infeccao_secundaria
	 */
	public java.lang.Long getTipoComplicacaoLocalInfeccaoSecundaria () {
		return getPropertyValue(this, tipoComplicacaoLocalInfeccaoSecundaria, PROP_TIPO_COMPLICACAO_LOCAL_INFECCAO_SECUNDARIA); 
	}

	/**
	 * Set the value related to the column: tp_comp_local_infeccao_secundaria
	 * @param tipoComplicacaoLocalInfeccaoSecundaria the tp_comp_local_infeccao_secundaria value
	 */
	public void setTipoComplicacaoLocalInfeccaoSecundaria (java.lang.Long tipoComplicacaoLocalInfeccaoSecundaria) {
//        java.lang.Long tipoComplicacaoLocalInfeccaoSecundariaOld = this.tipoComplicacaoLocalInfeccaoSecundaria;
		this.tipoComplicacaoLocalInfeccaoSecundaria = tipoComplicacaoLocalInfeccaoSecundaria;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoLocalInfeccaoSecundaria", tipoComplicacaoLocalInfeccaoSecundariaOld, tipoComplicacaoLocalInfeccaoSecundaria);
	}



	/**
	 * Return the value associated with the column: tp_comp_local_necrose_extensa
	 */
	public java.lang.Long getTipoComplicacaoLocalNecroseExtensa () {
		return getPropertyValue(this, tipoComplicacaoLocalNecroseExtensa, PROP_TIPO_COMPLICACAO_LOCAL_NECROSE_EXTENSA); 
	}

	/**
	 * Set the value related to the column: tp_comp_local_necrose_extensa
	 * @param tipoComplicacaoLocalNecroseExtensa the tp_comp_local_necrose_extensa value
	 */
	public void setTipoComplicacaoLocalNecroseExtensa (java.lang.Long tipoComplicacaoLocalNecroseExtensa) {
//        java.lang.Long tipoComplicacaoLocalNecroseExtensaOld = this.tipoComplicacaoLocalNecroseExtensa;
		this.tipoComplicacaoLocalNecroseExtensa = tipoComplicacaoLocalNecroseExtensa;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoLocalNecroseExtensa", tipoComplicacaoLocalNecroseExtensaOld, tipoComplicacaoLocalNecroseExtensa);
	}



	/**
	 * Return the value associated with the column: tp_comp_local_sindrome_compartimental
	 */
	public java.lang.Long getTipoComplicacaoLocalSindromeCompartimental () {
		return getPropertyValue(this, tipoComplicacaoLocalSindromeCompartimental, PROP_TIPO_COMPLICACAO_LOCAL_SINDROME_COMPARTIMENTAL); 
	}

	/**
	 * Set the value related to the column: tp_comp_local_sindrome_compartimental
	 * @param tipoComplicacaoLocalSindromeCompartimental the tp_comp_local_sindrome_compartimental value
	 */
	public void setTipoComplicacaoLocalSindromeCompartimental (java.lang.Long tipoComplicacaoLocalSindromeCompartimental) {
//        java.lang.Long tipoComplicacaoLocalSindromeCompartimentalOld = this.tipoComplicacaoLocalSindromeCompartimental;
		this.tipoComplicacaoLocalSindromeCompartimental = tipoComplicacaoLocalSindromeCompartimental;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoLocalSindromeCompartimental", tipoComplicacaoLocalSindromeCompartimentalOld, tipoComplicacaoLocalSindromeCompartimental);
	}



	/**
	 * Return the value associated with the column: tp_comp_local_deficit_funcional
	 */
	public java.lang.Long getTipoComplicacaoLocalDeficitFuncional () {
		return getPropertyValue(this, tipoComplicacaoLocalDeficitFuncional, PROP_TIPO_COMPLICACAO_LOCAL_DEFICIT_FUNCIONAL);
	}

	/**
	 * Set the value related to the column: tp_comp_local_deficit_funcional
	 * @param tipoComplicacaoLocalDeficitFuncional the tp_comp_local_deficit_funcional value
	 */
	public void setTipoComplicacaoLocalDeficitFuncional (java.lang.Long tipoComplicacaoLocalDeficitFuncional) {
//        java.lang.Long tipoComplicacaoLocalDeficitFuncionalOld = this.tipoComplicacaoLocalDeficitFuncional;
		this.tipoComplicacaoLocalDeficitFuncional = tipoComplicacaoLocalDeficitFuncional;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoLocalDeficitFuncional", tipoComplicacaoLocalDeficitFuncionalOld, tipoComplicacaoLocalDeficitFuncional);
	}



	/**
	 * Return the value associated with the column: tp_comp_local_amputacao
	 */
	public java.lang.Long getTipoComplicacaoLocalAmputacao () {
		return getPropertyValue(this, tipoComplicacaoLocalAmputacao, PROP_TIPO_COMPLICACAO_LOCAL_AMPUTACAO); 
	}

	/**
	 * Set the value related to the column: tp_comp_local_amputacao
	 * @param tipoComplicacaoLocalAmputacao the tp_comp_local_amputacao value
	 */
	public void setTipoComplicacaoLocalAmputacao (java.lang.Long tipoComplicacaoLocalAmputacao) {
//        java.lang.Long tipoComplicacaoLocalAmputacaoOld = this.tipoComplicacaoLocalAmputacao;
		this.tipoComplicacaoLocalAmputacao = tipoComplicacaoLocalAmputacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoLocalAmputacao", tipoComplicacaoLocalAmputacaoOld, tipoComplicacaoLocalAmputacao);
	}



	/**
	 * Return the value associated with the column: complicacoes_sistemicas
	 */
	public java.lang.Long getComplicacoesSistemicas () {
		return getPropertyValue(this, complicacoesSistemicas, PROP_COMPLICACOES_SISTEMICAS); 
	}

	/**
	 * Set the value related to the column: complicacoes_sistemicas
	 * @param complicacoesSistemicas the complicacoes_sistemicas value
	 */
	public void setComplicacoesSistemicas (java.lang.Long complicacoesSistemicas) {
//        java.lang.Long complicacoesSistemicasOld = this.complicacoesSistemicas;
		this.complicacoesSistemicas = complicacoesSistemicas;
//        this.getPropertyChangeSupport().firePropertyChange ("complicacoesSistemicas", complicacoesSistemicasOld, complicacoesSistemicas);
	}



	/**
	 * Return the value associated with the column: tp_comp_sistemica_insuficiencia_renal
	 */
	public java.lang.Long getTipoComplicacaoSistemicaInsuficienciaRenal () {
		return getPropertyValue(this, tipoComplicacaoSistemicaInsuficienciaRenal, PROP_TIPO_COMPLICACAO_SISTEMICA_INSUFICIENCIA_RENAL); 
	}

	/**
	 * Set the value related to the column: tp_comp_sistemica_insuficiencia_renal
	 * @param tipoComplicacaoSistemicaInsuficienciaRenal the tp_comp_sistemica_insuficiencia_renal value
	 */
	public void setTipoComplicacaoSistemicaInsuficienciaRenal (java.lang.Long tipoComplicacaoSistemicaInsuficienciaRenal) {
//        java.lang.Long tipoComplicacaoSistemicaInsuficienciaRenalOld = this.tipoComplicacaoSistemicaInsuficienciaRenal;
		this.tipoComplicacaoSistemicaInsuficienciaRenal = tipoComplicacaoSistemicaInsuficienciaRenal;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoSistemicaInsuficienciaRenal", tipoComplicacaoSistemicaInsuficienciaRenalOld, tipoComplicacaoSistemicaInsuficienciaRenal);
	}



	/**
	 * Return the value associated with the column: tp_comp_sistemica_insuficiencia_respiratoria
	 */
	public java.lang.Long getTipoComplicacaoSistemicaInsuficienciaRespiratoria () {
		return getPropertyValue(this, tipoComplicacaoSistemicaInsuficienciaRespiratoria, PROP_TIPO_COMPLICACAO_SISTEMICA_INSUFICIENCIA_RESPIRATORIA); 
	}

	/**
	 * Set the value related to the column: tp_comp_sistemica_insuficiencia_respiratoria
	 * @param tipoComplicacaoSistemicaInsuficienciaRespiratoria the tp_comp_sistemica_insuficiencia_respiratoria value
	 */
	public void setTipoComplicacaoSistemicaInsuficienciaRespiratoria (java.lang.Long tipoComplicacaoSistemicaInsuficienciaRespiratoria) {
//        java.lang.Long tipoComplicacaoSistemicaInsuficienciaRespiratoriaOld = this.tipoComplicacaoSistemicaInsuficienciaRespiratoria;
		this.tipoComplicacaoSistemicaInsuficienciaRespiratoria = tipoComplicacaoSistemicaInsuficienciaRespiratoria;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoSistemicaInsuficienciaRespiratoria", tipoComplicacaoSistemicaInsuficienciaRespiratoriaOld, tipoComplicacaoSistemicaInsuficienciaRespiratoria);
	}



	/**
	 * Return the value associated with the column: tp_comp_sistemica_septicemia
	 */
	public java.lang.Long getTipoComplicacaoSistemicaSepticemia () {
		return getPropertyValue(this, tipoComplicacaoSistemicaSepticemia, PROP_TIPO_COMPLICACAO_SISTEMICA_SEPTICEMIA); 
	}

	/**
	 * Set the value related to the column: tp_comp_sistemica_septicemia
	 * @param tipoComplicacaoSistemicaSepticemia the tp_comp_sistemica_septicemia value
	 */
	public void setTipoComplicacaoSistemicaSepticemia (java.lang.Long tipoComplicacaoSistemicaSepticemia) {
//        java.lang.Long tipoComplicacaoSistemicaSepticemiaOld = this.tipoComplicacaoSistemicaSepticemia;
		this.tipoComplicacaoSistemicaSepticemia = tipoComplicacaoSistemicaSepticemia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoSistemicaSepticemia", tipoComplicacaoSistemicaSepticemiaOld, tipoComplicacaoSistemicaSepticemia);
	}



	/**
	 * Return the value associated with the column: tp_comp_sistemica_choque
	 */
	public java.lang.Long getTipoComplicacaoSistemicaChoque () {
		return getPropertyValue(this, tipoComplicacaoSistemicaChoque, PROP_TIPO_COMPLICACAO_SISTEMICA_CHOQUE); 
	}

	/**
	 * Set the value related to the column: tp_comp_sistemica_choque
	 * @param tipoComplicacaoSistemicaChoque the tp_comp_sistemica_choque value
	 */
	public void setTipoComplicacaoSistemicaChoque (java.lang.Long tipoComplicacaoSistemicaChoque) {
//        java.lang.Long tipoComplicacaoSistemicaChoqueOld = this.tipoComplicacaoSistemicaChoque;
		this.tipoComplicacaoSistemicaChoque = tipoComplicacaoSistemicaChoque;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoComplicacaoSistemicaChoque", tipoComplicacaoSistemicaChoqueOld, tipoComplicacaoSistemicaChoque);
	}



	/**
	 * Return the value associated with the column: acidente_relacionado_trabalho
	 */
	public java.lang.Long getAcidenteRelacionadoTrabalho () {
		return getPropertyValue(this, acidenteRelacionadoTrabalho, PROP_ACIDENTE_RELACIONADO_TRABALHO); 
	}

	/**
	 * Set the value related to the column: acidente_relacionado_trabalho
	 * @param acidenteRelacionadoTrabalho the acidente_relacionado_trabalho value
	 */
	public void setAcidenteRelacionadoTrabalho (java.lang.Long acidenteRelacionadoTrabalho) {
//        java.lang.Long acidenteRelacionadoTrabalhoOld = this.acidenteRelacionadoTrabalho;
		this.acidenteRelacionadoTrabalho = acidenteRelacionadoTrabalho;
//        this.getPropertyChangeSupport().firePropertyChange ("acidenteRelacionadoTrabalho", acidenteRelacionadoTrabalhoOld, acidenteRelacionadoTrabalho);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: data_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: data_obito
	 * @param dataObito the data_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: cd_cidade_acidente
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalAcidente () {
		return getPropertyValue(this, cidadeLocalAcidente, PROP_CIDADE_LOCAL_ACIDENTE); 
	}

	/**
	 * Set the value related to the column: cd_cidade_acidente
	 * @param cidadeLocalAcidente the cd_cidade_acidente value
	 */
	public void setCidadeLocalAcidente (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalAcidente) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalAcidenteOld = this.cidadeLocalAcidente;
		this.cidadeLocalAcidente = cidadeLocalAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalAcidente", cidadeLocalAcidenteOld, cidadeLocalAcidente);
	}



	/**
	 * Return the value associated with the column: cd_pais_acidente
	 */
	public br.com.ksisolucoes.vo.basico.Pais getPaisLocalAcidente () {
		return getPropertyValue(this, paisLocalAcidente, PROP_PAIS_LOCAL_ACIDENTE); 
	}

	/**
	 * Set the value related to the column: cd_pais_acidente
	 * @param paisLocalAcidente the cd_pais_acidente value
	 */
	public void setPaisLocalAcidente (br.com.ksisolucoes.vo.basico.Pais paisLocalAcidente) {
//        br.com.ksisolucoes.vo.basico.Pais paisLocalAcidenteOld = this.paisLocalAcidente;
		this.paisLocalAcidente = paisLocalAcidente;
//        this.getPropertyChangeSupport().firePropertyChange ("paisLocalAcidente", paisLocalAcidenteOld, paisLocalAcidente);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}



	/**
	 * Return the value associated with the column: cidade_ocorrencia
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidadeLocalOcorrencia () {
		return getPropertyValue(this, cidadeLocalOcorrencia, PROP_CIDADE_LOCAL_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: cidade_ocorrencia
	 * @param cidadeLocalOcorrencia the cidade_ocorrencia value
	 */
	public void setCidadeLocalOcorrencia (br.com.ksisolucoes.vo.basico.Cidade cidadeLocalOcorrencia) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeLocalOcorrenciaOld = this.cidadeLocalOcorrencia;
		this.cidadeLocalOcorrencia = cidadeLocalOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("cidadeLocalOcorrencia", cidadeLocalOcorrenciaOld, cidadeLocalOcorrencia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteAnimalPeconhento)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteAnimalPeconhento investigacaoAgravoAcidenteAnimalPeconhento = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoAcidenteAnimalPeconhento) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoAcidenteAnimalPeconhento.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoAcidenteAnimalPeconhento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}