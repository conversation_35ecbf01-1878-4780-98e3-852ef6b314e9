package br.com.ksisolucoes.vo.prontuario.procedimento;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.procedimento.base.BaseProcedimentoTipoTabela;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

public class ProcedimentoTipoTabela extends BaseProcedimentoTipoTabela implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public ProcedimentoTipoTabela () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ProcedimentoTipoTabela (java.lang.Long codigo) {
		super(codigo);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}
