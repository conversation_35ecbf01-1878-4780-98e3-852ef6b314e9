package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the atendimento_soap table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="atendimento_soap"
 */

public abstract class BaseAtendimentoSoap extends BaseRootVO implements Serializable {

	public static String REF = "AtendimentoSoap";
	public static final String PROP_RESULTADO_EXAME_LABORATORIAL = "resultadoExameLaboratorial";
	public static final String PROP_USUARIO = "Usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_COM_SANGUE = "comSangue";
	public static final String PROP_SUBJETIVO = "subjetivo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DESCRICAO_ALERGICO = "descricaoAlergico";
	public static final String PROP_DATA_PRIMEIROS_SINTOMAS = "dataPrimeirosSintomas";
	public static final String PROP_PLANO_TRATAMENTO = "planoTratamento";
	public static final String PROP_ATENDIMENTO = "atendimento";
	public static final String PROP_OBJETIVO = "objetivo";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_AVALIACAO = "avaliacao";
	public static final String PROP_PLANO = "plano";


	// constructors
	public BaseAtendimentoSoap () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAtendimentoSoap (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAtendimentoSoap (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String subjetivo;
	private java.lang.String objetivo;
	private java.lang.String avaliacao;
	private java.lang.String plano;
	private java.util.Date dataCadastro;
	private java.lang.String descricaoAlergico;
	private java.util.Date dataPrimeirosSintomas;
	private java.lang.Long comSangue;
	private java.lang.String resultadoExameLaboratorial;
	private java.lang.String planoTratamento;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_atendimento_soap"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: subjetivo
	 */
	public java.lang.String getSubjetivo () {
		return getPropertyValue(this, subjetivo, PROP_SUBJETIVO); 
	}

	/**
	 * Set the value related to the column: subjetivo
	 * @param subjetivo the subjetivo value
	 */
	public void setSubjetivo (java.lang.String subjetivo) {
//        java.lang.String subjetivoOld = this.subjetivo;
		this.subjetivo = subjetivo;
//        this.getPropertyChangeSupport().firePropertyChange ("subjetivo", subjetivoOld, subjetivo);
	}



	/**
	 * Return the value associated with the column: objetivo
	 */
	public java.lang.String getObjetivo () {
		return getPropertyValue(this, objetivo, PROP_OBJETIVO); 
	}

	/**
	 * Set the value related to the column: objetivo
	 * @param objetivo the objetivo value
	 */
	public void setObjetivo (java.lang.String objetivo) {
//        java.lang.String objetivoOld = this.objetivo;
		this.objetivo = objetivo;
//        this.getPropertyChangeSupport().firePropertyChange ("objetivo", objetivoOld, objetivo);
	}



	/**
	 * Return the value associated with the column: avaliacao
	 */
	public java.lang.String getAvaliacao () {
		return getPropertyValue(this, avaliacao, PROP_AVALIACAO); 
	}

	/**
	 * Set the value related to the column: avaliacao
	 * @param avaliacao the avaliacao value
	 */
	public void setAvaliacao (java.lang.String avaliacao) {
//        java.lang.String avaliacaoOld = this.avaliacao;
		this.avaliacao = avaliacao;
//        this.getPropertyChangeSupport().firePropertyChange ("avaliacao", avaliacaoOld, avaliacao);
	}



	/**
	 * Return the value associated with the column: plano
	 */
	public java.lang.String getPlano () {
		return getPropertyValue(this, plano, PROP_PLANO); 
	}

	/**
	 * Set the value related to the column: plano
	 * @param plano the plano value
	 */
	public void setPlano (java.lang.String plano) {
//        java.lang.String planoOld = this.plano;
		this.plano = plano;
//        this.getPropertyChangeSupport().firePropertyChange ("plano", planoOld, plano);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: ds_alergico
	 */
	public java.lang.String getDescricaoAlergico () {
		return getPropertyValue(this, descricaoAlergico, PROP_DESCRICAO_ALERGICO); 
	}

	/**
	 * Set the value related to the column: ds_alergico
	 * @param descricaoAlergico the ds_alergico value
	 */
	public void setDescricaoAlergico (java.lang.String descricaoAlergico) {
//        java.lang.String descricaoAlergicoOld = this.descricaoAlergico;
		this.descricaoAlergico = descricaoAlergico;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoAlergico", descricaoAlergicoOld, descricaoAlergico);
	}



	/**
	 * Return the value associated with the column: dt_primeiros_sintomas
	 */
	public java.util.Date getDataPrimeirosSintomas () {
		return getPropertyValue(this, dataPrimeirosSintomas, PROP_DATA_PRIMEIROS_SINTOMAS); 
	}

	/**
	 * Set the value related to the column: dt_primeiros_sintomas
	 * @param dataPrimeirosSintomas the dt_primeiros_sintomas value
	 */
	public void setDataPrimeirosSintomas (java.util.Date dataPrimeirosSintomas) {
//        java.util.Date dataPrimeirosSintomasOld = this.dataPrimeirosSintomas;
		this.dataPrimeirosSintomas = dataPrimeirosSintomas;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPrimeirosSintomas", dataPrimeirosSintomasOld, dataPrimeirosSintomas);
	}



	/**
	 * Return the value associated with the column: com_sangue
	 */
	public java.lang.Long getComSangue () {
		return getPropertyValue(this, comSangue, PROP_COM_SANGUE); 
	}

	/**
	 * Set the value related to the column: com_sangue
	 * @param comSangue the com_sangue value
	 */
	public void setComSangue (java.lang.Long comSangue) {
//        java.lang.Long comSangueOld = this.comSangue;
		this.comSangue = comSangue;
//        this.getPropertyChangeSupport().firePropertyChange ("comSangue", comSangueOld, comSangue);
	}



	/**
	 * Return the value associated with the column: resultado_exame_lab
	 */
	public java.lang.String getResultadoExameLaboratorial () {
		return getPropertyValue(this, resultadoExameLaboratorial, PROP_RESULTADO_EXAME_LABORATORIAL); 
	}

	/**
	 * Set the value related to the column: resultado_exame_lab
	 * @param resultadoExameLaboratorial the resultado_exame_lab value
	 */
	public void setResultadoExameLaboratorial (java.lang.String resultadoExameLaboratorial) {
//        java.lang.String resultadoExameLaboratorialOld = this.resultadoExameLaboratorial;
		this.resultadoExameLaboratorial = resultadoExameLaboratorial;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoExameLaboratorial", resultadoExameLaboratorialOld, resultadoExameLaboratorial);
	}



	/**
	 * Return the value associated with the column: plano_tratamento
	 */
	public java.lang.String getPlanoTratamento () {
		return getPropertyValue(this, planoTratamento, PROP_PLANO_TRATAMENTO); 
	}

	/**
	 * Set the value related to the column: plano_tratamento
	 * @param planoTratamento the plano_tratamento value
	 */
	public void setPlanoTratamento (java.lang.String planoTratamento) {
//        java.lang.String planoTratamentoOld = this.planoTratamento;
		this.planoTratamento = planoTratamento;
//        this.getPropertyChangeSupport().firePropertyChange ("planoTratamento", planoTratamentoOld, planoTratamento);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AtendimentoSoap)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AtendimentoSoap atendimentoSoap = (br.com.ksisolucoes.vo.prontuario.basico.AtendimentoSoap) obj;
			if (null == this.getCodigo() || null == atendimentoSoap.getCodigo()) return false;
			else return (this.getCodigo().equals(atendimentoSoap.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}