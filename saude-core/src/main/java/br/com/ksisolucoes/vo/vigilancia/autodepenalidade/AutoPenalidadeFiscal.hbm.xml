<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.autodepenalidade">
    <class name="AutoPenalidadeFiscal" table="auto_penalidade_fiscal">
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_auto_penalidade_fiscal"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <many-to-one class="br.com.ksisolucoes.vo.cadsus.Profissional"
                     name="profissional" not-null="true">
            <column name="cd_profissional"/>
        </many-to-one>
        
        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade"
                     name="autoPenalidade" not-null="false">
            <column name="cd_auto_penalidade"/>
        </many-to-one>
    </class>
</hibernate-mapping>
