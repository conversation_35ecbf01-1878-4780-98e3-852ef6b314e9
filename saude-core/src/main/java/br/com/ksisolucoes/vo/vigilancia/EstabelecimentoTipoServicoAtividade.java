package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseEstabelecimentoTipoServicoAtividade;

import java.io.Serializable;



public class EstabelecimentoTipoServicoAtividade extends BaseEstabelecimentoTipoServicoAtividade implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstabelecimentoTipoServicoAtividade () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EstabelecimentoTipoServicoAtividade (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EstabelecimentoTipoServicoAtividade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento atividadeEstabelecimento,
		br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimentoTipoServico atividadeEstabelecimentoTipoServico) {

		super (
			codigo,
			estabelecimento,
			atividadeEstabelecimento,
			atividadeEstabelecimentoTipoServico);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}