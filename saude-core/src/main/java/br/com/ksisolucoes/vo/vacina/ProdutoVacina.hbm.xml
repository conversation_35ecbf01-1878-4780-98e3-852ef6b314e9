<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vacina"  >
	<class name="ProdutoVacina" table="produto_vacina" >

        <id
            column="cd_produto_vacina"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            column="cod_pro"
            name="produto"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.vacina.TipoVacina"
            column="cd_vacina"
            name="tipoVacina"
            not-null="true"
        />
        
        <property
            column="qt_dose"
            name="quantidadeDose"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property
            column="cod_pni"
            name="codigoPni"
            type="java.lang.Long"
        />
        
	</class>
</hibernate-mapping>
