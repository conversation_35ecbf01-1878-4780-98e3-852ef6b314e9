package br.com.ksisolucoes.vo.vigilancia.cva;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.cva.base.BaseCvaRacaAnimal;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class CvaRacaAnimal extends BaseCvaRacaAnimal implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public CvaRacaAnimal () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public CvaRacaAnimal (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public CvaRacaAnimal (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.EspecieAnimal especieAnimal,
		java.lang.String descricao) {

		super (
			codigo,
			especieAnimal,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
        @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }
}