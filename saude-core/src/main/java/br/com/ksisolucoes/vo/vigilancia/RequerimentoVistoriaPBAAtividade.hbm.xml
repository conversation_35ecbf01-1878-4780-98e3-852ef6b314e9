<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="RequerimentoVistoriaPBAAtividade" table="requerimento_vistoria_pba_ativ">
        <id
            column="cd_req_vistoria_pba_ativ"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_requerimento_vistoria_pba_ativ</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />

 		<many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura"
            column="cd_requerimento_vistoria_pba"
            name="requerimentoVistoriaProjetoBasicoArquitetura"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia"
            column="cd_atividades_vigilancia"
            name="atividadesVigilancia"
            not-null="true"
        />
        
    </class>
</hibernate-mapping>
