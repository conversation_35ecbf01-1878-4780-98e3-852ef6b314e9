package br.com.ksisolucoes.vo.prontuario.hospital.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the balanco_hidrico table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="balanco_hidrico"
 */

public abstract class BaseBalancoHidrico extends BaseRootVO implements Serializable {

	public static String REF = "BalancoHidrico";
	public static final String PROP_PROFISSIONAL_LANCAMENTO = "profissionalLancamento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_LANCAMENTO = "dataLancamento";
	public static final String PROP_TIPO_GANHO_PERDA = "tipoGanhoPerda";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_VOLUME = "volume";
	public static final String PROP_TIPO_TURNO = "tipoTurno";
	public static final String PROP_DATA_BALANCO = "dataBalanco";
	public static final String PROP_DATA_INATIVACAO = "dataInativacao";
	public static final String PROP_ATIVO = "ativo";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_ATENDIMENTO = "atendimento";


	// constructors
	public BaseBalancoHidrico () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBalancoHidrico (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseBalancoHidrico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento,
		br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda tipoGanhoPerda,
		br.com.ksisolucoes.vo.cadsus.Profissional profissionalLancamento,
		java.lang.Long tipoTurno,
		java.util.Date dataLancamento,
		java.util.Date dataBalanco,
		java.lang.Double volume,
		java.lang.Long ativo,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setAtendimento(atendimento);
		this.setTipoGanhoPerda(tipoGanhoPerda);
		this.setProfissionalLancamento(profissionalLancamento);
		this.setTipoTurno(tipoTurno);
		this.setDataLancamento(dataLancamento);
		this.setDataBalanco(dataBalanco);
		this.setVolume(volume);
		this.setAtivo(ativo);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long tipoTurno;
	private java.util.Date dataLancamento;
	private java.util.Date dataBalanco;
	private java.lang.Double volume;
	private java.lang.String observacao;
	private java.lang.Long ativo;
	private java.util.Date dataCadastro;
	private java.util.Date dataInativacao;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento;
	private br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda tipoGanhoPerda;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalLancamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_balanco_hidrico"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: tp_turno
	 */
	public java.lang.Long getTipoTurno () {
		return getPropertyValue(this, tipoTurno, PROP_TIPO_TURNO); 
	}

	/**
	 * Set the value related to the column: tp_turno
	 * @param tipoTurno the tp_turno value
	 */
	public void setTipoTurno (java.lang.Long tipoTurno) {
//        java.lang.Long tipoTurnoOld = this.tipoTurno;
		this.tipoTurno = tipoTurno;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoTurno", tipoTurnoOld, tipoTurno);
	}



	/**
	 * Return the value associated with the column: dt_lancto
	 */
	public java.util.Date getDataLancamento () {
		return getPropertyValue(this, dataLancamento, PROP_DATA_LANCAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_lancto
	 * @param dataLancamento the dt_lancto value
	 */
	public void setDataLancamento (java.util.Date dataLancamento) {
//        java.util.Date dataLancamentoOld = this.dataLancamento;
		this.dataLancamento = dataLancamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLancamento", dataLancamentoOld, dataLancamento);
	}



	/**
	 * Return the value associated with the column: dt_balanco
	 */
	public java.util.Date getDataBalanco () {
		return getPropertyValue(this, dataBalanco, PROP_DATA_BALANCO); 
	}

	/**
	 * Set the value related to the column: dt_balanco
	 * @param dataBalanco the dt_balanco value
	 */
	public void setDataBalanco (java.util.Date dataBalanco) {
//        java.util.Date dataBalancoOld = this.dataBalanco;
		this.dataBalanco = dataBalanco;
//        this.getPropertyChangeSupport().firePropertyChange ("dataBalanco", dataBalancoOld, dataBalanco);
	}



	/**
	 * Return the value associated with the column: volume
	 */
	public java.lang.Double getVolume () {
		return getPropertyValue(this, volume, PROP_VOLUME); 
	}

	/**
	 * Set the value related to the column: volume
	 * @param volume the volume value
	 */
	public void setVolume (java.lang.Double volume) {
//        java.lang.Double volumeOld = this.volume;
		this.volume = volume;
//        this.getPropertyChangeSupport().firePropertyChange ("volume", volumeOld, volume);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: ativo
	 */
	public java.lang.Long getAtivo () {
		return getPropertyValue(this, ativo, PROP_ATIVO); 
	}

	/**
	 * Set the value related to the column: ativo
	 * @param ativo the ativo value
	 */
	public void setAtivo (java.lang.Long ativo) {
//        java.lang.Long ativoOld = this.ativo;
		this.ativo = ativo;
//        this.getPropertyChangeSupport().firePropertyChange ("ativo", ativoOld, ativo);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_inativacao
	 */
	public java.util.Date getDataInativacao () {
		return getPropertyValue(this, dataInativacao, PROP_DATA_INATIVACAO); 
	}

	/**
	 * Set the value related to the column: dt_inativacao
	 * @param dataInativacao the dt_inativacao value
	 */
	public void setDataInativacao (java.util.Date dataInativacao) {
//        java.util.Date dataInativacaoOld = this.dataInativacao;
		this.dataInativacao = dataInativacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInativacao", dataInativacaoOld, dataInativacao);
	}



	/**
	 * Return the value associated with the column: nr_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Atendimento getAtendimento () {
		return getPropertyValue(this, atendimento, PROP_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: nr_atendimento
	 * @param atendimento the nr_atendimento value
	 */
	public void setAtendimento (br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.Atendimento atendimentoOld = this.atendimento;
		this.atendimento = atendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("atendimento", atendimentoOld, atendimento);
	}



	/**
	 * Return the value associated with the column: cd_tp_ganho_perda
	 */
	public br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda getTipoGanhoPerda () {
		return getPropertyValue(this, tipoGanhoPerda, PROP_TIPO_GANHO_PERDA); 
	}

	/**
	 * Set the value related to the column: cd_tp_ganho_perda
	 * @param tipoGanhoPerda the cd_tp_ganho_perda value
	 */
	public void setTipoGanhoPerda (br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda tipoGanhoPerda) {
//        br.com.ksisolucoes.vo.prontuario.hospital.TipoGanhoPerda tipoGanhoPerdaOld = this.tipoGanhoPerda;
		this.tipoGanhoPerda = tipoGanhoPerda;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoGanhoPerda", tipoGanhoPerdaOld, tipoGanhoPerda);
	}



	/**
	 * Return the value associated with the column: cd_profissional_lancto
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalLancamento () {
		return getPropertyValue(this, profissionalLancamento, PROP_PROFISSIONAL_LANCAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_profissional_lancto
	 * @param profissionalLancamento the cd_profissional_lancto value
	 */
	public void setProfissionalLancamento (br.com.ksisolucoes.vo.cadsus.Profissional profissionalLancamento) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalLancamentoOld = this.profissionalLancamento;
		this.profissionalLancamento = profissionalLancamento;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalLancamento", profissionalLancamentoOld, profissionalLancamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.hospital.BalancoHidrico)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.hospital.BalancoHidrico balancoHidrico = (br.com.ksisolucoes.vo.prontuario.hospital.BalancoHidrico) obj;
			if (null == this.getCodigo() || null == balancoHidrico.getCodigo()) return false;
			else return (this.getCodigo().equals(balancoHidrico.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}