<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class 
        name="TipoExameCbo"
        table="tipo_exame_cbo"
    >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_tp_exame_cbo"
        >
            <generator class="sequence">
                <param name="sequence">seq_id_tipo_exame_cbo</param>
            </generator>
        </id> 
        
        <version column="version" name="version" type="long" />

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.TipoExame"
                     name="tipoExame">
            <column name="cd_tp_exame" />
        </many-to-one>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                     name="cbo">
            <column name="cd_cbo" />
        </many-to-one>

    </class>
</hibernate-mapping>