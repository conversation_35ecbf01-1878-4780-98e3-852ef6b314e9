package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoReceitaATalao;

import java.io.Serializable;



public class RequerimentoReceitaATalao extends BaseRequerimentoReceitaATalao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoReceitaATalao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoReceitaATalao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoReceitaATalao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoReceitaA requerimentoReceitaA,
		br.com.ksisolucoes.vo.vigilancia.TalonarioReceitaA talonarioReceitaA) {

		super (
			codigo,
			requerimentoReceitaA,
			talonarioReceitaA);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}