package br.com.ksisolucoes.vo.outraunidade.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the visualizacao_paciente_atendido_outra_unidade_usuario table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="visualizacao_paciente_atendido_outra_unidade_usuario"
 */

public abstract class BaseVisualizacaoPacienteAtendidoOutraUnidadeUsuario extends BaseRootVO implements Serializable {

	public static String REF = "VisualizacaoPacienteAtendidoOutraUnidadeUsuario";
	public static final String PROP_PACIENTE_ATENDIDO_OUTRA_UNIDADE = "pacienteAtendidoOutraUnidade";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_DATA_VISALIZACAO = "dataVisalizacao";
	public static final String PROP_CODIGO = "codigo";


	// constructors
	public BaseVisualizacaoPacienteAtendidoOutraUnidadeUsuario () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseVisualizacaoPacienteAtendidoOutraUnidadeUsuario (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseVisualizacaoPacienteAtendidoOutraUnidadeUsuario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataVisalizacao) {

		this.setCodigo(codigo);
		this.setPacienteAtendidoOutraUnidade(pacienteAtendidoOutraUnidade);
		this.setUsuario(usuario);
		this.setDataVisalizacao(dataVisalizacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataVisalizacao;

	// many to one
	private br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_visualizacao_paciente_atendido_outra_unidade_usuario"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_visalizacao
	 */
	public java.util.Date getDataVisalizacao () {
		return getPropertyValue(this, dataVisalizacao, PROP_DATA_VISALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_visalizacao
	 * @param dataVisalizacao the dt_visalizacao value
	 */
	public void setDataVisalizacao (java.util.Date dataVisalizacao) {
//        java.util.Date dataVisalizacaoOld = this.dataVisalizacao;
		this.dataVisalizacao = dataVisalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataVisalizacao", dataVisalizacaoOld, dataVisalizacao);
	}



	/**
	 * Return the value associated with the column: cd_atend_outra_unid
	 */
	public br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade getPacienteAtendidoOutraUnidade () {
		return getPropertyValue(this, pacienteAtendidoOutraUnidade, PROP_PACIENTE_ATENDIDO_OUTRA_UNIDADE); 
	}

	/**
	 * Set the value related to the column: cd_atend_outra_unid
	 * @param pacienteAtendidoOutraUnidade the cd_atend_outra_unid value
	 */
	public void setPacienteAtendidoOutraUnidade (br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidade) {
//        br.com.ksisolucoes.vo.outraunidade.PacienteAtendidoOutraUnidade pacienteAtendidoOutraUnidadeOld = this.pacienteAtendidoOutraUnidade;
		this.pacienteAtendidoOutraUnidade = pacienteAtendidoOutraUnidade;
//        this.getPropertyChangeSupport().firePropertyChange ("pacienteAtendidoOutraUnidade", pacienteAtendidoOutraUnidadeOld, pacienteAtendidoOutraUnidade);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.outraunidade.VisualizacaoPacienteAtendidoOutraUnidadeUsuario)) return false;
		else {
			br.com.ksisolucoes.vo.outraunidade.VisualizacaoPacienteAtendidoOutraUnidadeUsuario visualizacaoPacienteAtendidoOutraUnidadeUsuario = (br.com.ksisolucoes.vo.outraunidade.VisualizacaoPacienteAtendidoOutraUnidadeUsuario) obj;
			if (null == this.getCodigo() || null == visualizacaoPacienteAtendidoOutraUnidadeUsuario.getCodigo()) return false;
			else return (this.getCodigo().equals(visualizacaoPacienteAtendidoOutraUnidadeUsuario.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}