package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseAgendamentoLoteProcesso;

import java.io.Serializable;



public class AgendamentoLoteProcesso extends BaseAgendamentoLoteProcesso implements CodigoManager {
	private static final long serialVersionUID = 1L;

	public enum Status implements IEnum<Status> {

		CONCLUIDO(1L, Bundle.getStringApplication("rotulo_concluido")),
		GERANDO(2L, Bundle.getStringApplication("rotulo_gerando")),
		ERRO(3L, Bundle.getStringApplication("rotulo_erro")),
		SEM_REGISTRO(4L, Bundle.getStringApplication("rotulo_sem_registro")),
		CANCELADO(5L, Bundle.getStringApplication("rotulo_cancelado")),
		CONCLUIDO_ERRO(6L, Bundle.getStringApplication("rotulo_concluido_com_erros")),
		FILA_ESPERA(7L, Bundle.getStringApplication("rotulo_fila_espera")),
		;

		private final Long value;
		private final String descricao;

		Status(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static Status valueOf(Long value) {
			for (Status status : Status.values()) {
				if (status.value().equals(value)) {
					return status;
				}
			}
			return null;
		}

		@Override
		public String descricao() {
			return descricao;
		}

		@Override
		public Long value() {
			return value;
		}
	}

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AgendamentoLoteProcesso () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AgendamentoLoteProcesso (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AgendamentoLoteProcesso (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento tipoProcedimento,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataGeracao,
		java.lang.Long quantidade,
		java.lang.Long status) {

		super (
			codigo,
			tipoProcedimento,
			usuario,
			dataGeracao,
			quantidade,
			status);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public String getDescricaoStatus(){
		Status status = Status.valueOf(getStatus());
		if (status != null && status.descricao != null) {
			return status.descricao();
		}
		return "";
	}

	public String getPermiteQuebraFormatado(){
		switch(Coalesce.asString(getQuebraSolicitacao())) {
			case "S":
				return Bundle.getStringApplication("rotulo_sim");
			case "N":
				return Bundle.getStringApplication("rotulo_nao");
			default:
				return "";
		}
	}

	public String getResetFilaFormatado() {
		if (RepositoryComponentDefault.SIM_LONG.equals(getResetFila())) {
			return Bundle.getStringApplication("rotulo_sim");
		} else if (RepositoryComponentDefault.NAO_LONG.equals(getResetFila())) {
			return Bundle.getStringApplication("rotulo_nao");
		}
		return "";
	}
}