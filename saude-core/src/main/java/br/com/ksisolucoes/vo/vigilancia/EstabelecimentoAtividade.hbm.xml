<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia"  >
    <class name="EstabelecimentoAtividade" table="estabelecimento_atividade">
        <id
            column="cd_estabelecimento_atividade"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
            column="cd_estabelecimento"
            not-null="true"
            name="estabelecimento"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento"
                column="cd_atividade_estabelecimento"
                not-null="true"
                name="atividadeEstabelecimento"
        />
        
        <property
            column="flag_principal"
            name="flagPrincipal"
            not-null="true"
            type="java.lang.Long"
        />

        <property
                column="qtd_taxa"
                name="quantidadeTaxa"
                not-null="false"
                type="java.lang.Double"
        />

        <property
                column="qtd_taxa_alvara_inicial"
                name="quantidadeTaxaAlvaraInicial"
                not-null="false"
                type="java.lang.Double"
        />

        <property
                column="qtd_taxa_alvara_revalidacao"
                name="quantidadeTaxaAlvaraRevalidacao"
                not-null="false"
                type="java.lang.Double"
        />

        <property
                column="isento_taxa"
                name="isentoTaxa"
                not-null="false"
                type="java.lang.Long"
        />

        <property
                column="impressao_alvara"
                name="impressaoAlvara"
                not-null="true"
                type="java.lang.Long"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.QuestinarioFatorRiscoCnae"
                column="cd_questionario"
                name="questionario"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.ClassificacaoGrupoEstabelecimento"
                column="cd_grupo_estabelecimento"
                name="grupoEstabelecimento"
        />

        <property
                column="atividade_licenciavel"
                name="atividadeLicenciavel"
                type="java.lang.Long"
        />

        <property
                column="atividade_licenciave_principal"
                name="atividadeLicenciavelPrincipal"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
