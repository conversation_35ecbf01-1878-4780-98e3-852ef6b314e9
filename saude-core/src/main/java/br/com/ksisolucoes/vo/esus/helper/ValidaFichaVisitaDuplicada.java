package br.com.ksisolucoes.vo.esus.helper;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.cadsus.MotivoVisitaDomiciliar;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliar;
import br.com.ksisolucoes.vo.cadsus.VisitaDomiciliarMotivo;
import ch.lambdaj.Lambda;

import java.util.Collections;
import java.util.List;

public class ValidaFichaVisitaDuplicada {

    private VisitaDomiciliar visitaDomiciliar;

    public ValidaFichaVisitaDuplicada(VisitaDomiciliar visitaDomiciliar) {
        this.visitaDomiciliar = visitaDomiciliar;
    }

    public boolean validar(List<MotivoVisitaDomiciliar> motivosSelecionados) throws ValidacaoException {
        if (visitaDomiciliar.getUsuarioCadsus() == null || visitaDomiciliar.getUsuarioCadsus().getCodigo() == null) {
            return false;
        }
        List<VisitaDomiciliar> visitasDuplicadas = findVisitasDuplicadas(visitaDomiciliar);
        return visitasDuplicadas.size() > 0 && this.validaMotivosVisita(motivosSelecionados, visitasDuplicadas);
    }

    private boolean validaMotivosVisita(List<MotivoVisitaDomiciliar> motivosSelecionados, List<VisitaDomiciliar> visitasDuplicadas) throws ValidacaoException {
        for (VisitaDomiciliar visitaDomiciliar : visitasDuplicadas) {
            List<MotivoVisitaDomiciliar> motivosBanco = findMotivosFrom(visitaDomiciliar);
            List<Long> idsMotivosBanco = Lambda.extractProperty(motivosBanco, MotivoVisitaDomiciliar.PROP_CODIGO);
            List<Long> idsMotivosSelecionados = Lambda.extractProperty(motivosSelecionados, MotivoVisitaDomiciliar.PROP_CODIGO);
            Collections.sort(idsMotivosBanco);
            Collections.sort(idsMotivosSelecionados);
            if (idsMotivosBanco.equals(idsMotivosSelecionados))
                return true;
        }
        return false;
    }

    private List<MotivoVisitaDomiciliar> findMotivosFrom(VisitaDomiciliar visita) {
        List<VisitaDomiciliarMotivo> motivos = LoadManager.getInstance(VisitaDomiciliarMotivo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliarMotivo.PROP_VISITA, BuilderQueryCustom.QueryParameter.IN, visita.getCodigo()))
                .start().getList();

        return Lambda.extractProperty(motivos, VisitaDomiciliarMotivo.PROP_MOTIVO_VISITA_DOMICILAR);
    }

    private List<VisitaDomiciliar> findVisitasDuplicadas(VisitaDomiciliar visitaDomiciliar) {
        return LoadManager.getInstance(VisitaDomiciliar.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliar.PROP_CODIGO, BuilderQueryCustom.QueryParameter.DIFERENTE, visitaDomiciliar.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliar.PROP_USUARIO_CADSUS, visitaDomiciliar.getUsuarioCadsus()))
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliar.PROP_DATA_VISITA, visitaDomiciliar.getDataVisita()))
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliar.PROP_DESFECHO, visitaDomiciliar.getDesfecho()))
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliar.PROP_SITUACAO, BuilderQueryCustom.QueryParameter.DIFERENTE, VisitaDomiciliar.Situacao.CANCELADO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliar.PROP_TURNO, visitaDomiciliar.getTurno()))
                .addParameter(new QueryCustom.QueryCustomParameter(VisitaDomiciliar.PROP_PROFISSIONAL, visitaDomiciliar.getProfissional()))
                .start().getList();
    }
}
