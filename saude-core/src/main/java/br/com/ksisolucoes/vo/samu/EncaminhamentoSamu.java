package br.com.ksisolucoes.vo.samu;

import java.io.Serializable;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.samu.base.BaseEncaminhamentoSamu;



public class EncaminhamentoSamu extends BaseEncaminhamentoSamu implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EncaminhamentoSamu () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EncaminhamentoSamu (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EncaminhamentoSamu (
		java.lang.Long codigo,
		java.lang.String descricaoEncaminhamento) {

		super (
			codigo,
			descricaoEncaminhamento);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    @Override
    public String getDescricaoVO() {
        return getDescricaoEncaminhamento();
    }

    @Override
    public String getIdentificador() {
        return Coalesce.asString(getCodigo());
    }
}