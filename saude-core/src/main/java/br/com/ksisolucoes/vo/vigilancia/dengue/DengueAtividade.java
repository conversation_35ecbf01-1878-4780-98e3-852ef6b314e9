package br.com.ksisolucoes.vo.vigilancia.dengue;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.dengue.base.BaseDengueAtividade;
import java.io.Serializable;

public class DengueAtividade extends BaseDengueAtividade implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public DengueAtividade() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public DengueAtividade(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public DengueAtividade(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
            java.lang.String descricao,
            java.lang.Long flagInformaPontoEstrategico,
            java.lang.Long flagLevantamentoIndice,
            java.util.Date dataCadastro) {

        super(
                codigo,
                usuarioCadastro,
                descricao,
                flagInformaPontoEstrategico,
                flagLevantamentoIndice,
                dataCadastro);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public String getFlagInformaPontoEstrategicoFormatado() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagInformaPontoEstrategico())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

    public String getFlagLevantamentoIndiceFormatado() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getFlagLevantamentoIndice())) {
            return Bundle.getStringApplication("rotulo_sim");
        }
        return Bundle.getStringApplication("rotulo_nao");
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }

}
