package br.com.ksisolucoes.vo.vigilancia.autoinfracao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the auto_infracao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="auto_infracao"
 */

public abstract class BaseAutoInfracao extends BaseRootVO implements Serializable {

	public static String REF = "AutoInfracao";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_RECUSOU_ASSINAR = "recusouAssinar";
	public static final String PROP_ENVIADO = "enviado";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_VIGILANCIA_PESSOA = "vigilanciaPessoa";
	public static final String PROP_AUTO_INTIMACAO_SUBSISTENTE = "autoIntimacaoSubsistente";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DENUNCIA = "denuncia";
	public static final String PROP_DATA_RECEBIMENTO = "dataRecebimento";
	public static final String PROP_DATA_ENTREGA = "dataEntrega";
	public static final String PROP_REGISTRO_INSPECAO = "registroInspecao";
	public static final String PROP_VIGILANCIA_ENDERECO = "vigilanciaEndereco";
	public static final String PROP_TESTEMUNHA = "testemunha";
	public static final String PROP_USUARIO_EDICAO = "usuarioEdicao";
	public static final String PROP_DENUNCIADO = "denunciado";
	public static final String PROP_DATA_INFRACAO = "dataInfracao";
	public static final String PROP_NOME_RESPONSAVEL_DEFESA = "nomeResponsavelDefesa";
	public static final String PROP_SERIE = "serie";
	public static final String PROP_AUTUADO_RECUSOU_AUTO = "autuadoRecusouAuto";
	public static final String PROP_FLAG_CRIADO_PELO_APP_FRU = "flagCriadoPeloAppFru";
	public static final String PROP_REQUERIMENTO_VIGILANCIA = "requerimentoVigilancia";
	public static final String PROP_PRAZO_DEFESA = "prazoDefesa";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO = "processoAdministrativoAutenticacao";
	public static final String PROP_NOME_RESPONSAVEL = "nomeResponsavel";
	public static final String PROP_DATA_CONCLUSAO = "dataConclusao";
	public static final String PROP_TIPO_DENUNCIADO = "tipoDenunciado";
	public static final String PROP_FLAG_EMAIL_ENVIADO_FRU = "flagEmailEnviadoFru";
	public static final String PROP_ENQUADRAMENTO_LEGAL = "enquadramentoLegal";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_RELATORIO_INSPECAO = "relatorioInspecao";
	public static final String PROP_AUTO_INTIMACAO = "autoIntimacao";
	public static final String PROP_MOTIVO_RETORNO = "motivoRetorno";
	public static final String PROP_NUMERO_FORMULARIO = "numeroFormulario";
	public static final String PROP_UUID_APP_FRU = "uuidAppFru";


	// constructors
	public BaseAutoInfracao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAutoInfracao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAutoInfracao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioEdicao,
		java.util.Date dataInfracao,
		java.lang.Long tipoDenunciado,
		java.lang.String denunciado,
		java.lang.Long situacao,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.Long flagCriadoPeloAppFru,
		java.lang.Long flagEmailEnviadoFru) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setUsuarioEdicao(usuarioEdicao);
		this.setDataInfracao(dataInfracao);
		this.setTipoDenunciado(tipoDenunciado);
		this.setDenunciado(denunciado);
		this.setSituacao(situacao);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		this.setFlagCriadoPeloAppFru(flagCriadoPeloAppFru);
		this.setFlagEmailEnviadoFru(flagEmailEnviadoFru);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeroFormulario;
	private java.lang.String serie;
	private java.util.Date dataInfracao;
	private java.lang.Long tipoDenunciado;
	private java.lang.String denunciado;
	private java.lang.String enquadramentoLegal;
	private java.lang.String nomeResponsavel;
	private java.util.Date dataRecebimento;
	private java.lang.String testemunha;
	private java.lang.Long recusouAssinar;
	private java.lang.Long situacao;
	private java.lang.String nomeResponsavelDefesa;
	private java.util.Date dataEntrega;
	private java.util.Date dataConclusao;
	private java.util.Date dataCadastro;
	private java.util.Date dataUsuario;
	private java.util.Date prazoDefesa;
	private java.lang.Long enviado;
	private java.lang.Long numero;
	private java.lang.Long autuadoRecusouAuto;
	private java.lang.Long flagCriadoPeloAppFru;
	private java.lang.Long flagEmailEnviadoFru;
	private java.lang.String uuidAppFru;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia denuncia;
	private br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao;
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEdicao;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento;
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoa;
	private br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao registroInspecao;
	private br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno motivoRetorno;
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao relatorioInspecao;
	private br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao;
	private br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacaoSubsistente;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_auto_infracao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: numero_formulario
	 */
	public java.lang.Long getNumeroFormulario () {
		return getPropertyValue(this, numeroFormulario, PROP_NUMERO_FORMULARIO); 
	}

	/**
	 * Set the value related to the column: numero_formulario
	 * @param numeroFormulario the numero_formulario value
	 */
	public void setNumeroFormulario (java.lang.Long numeroFormulario) {
//        java.lang.Long numeroFormularioOld = this.numeroFormulario;
		this.numeroFormulario = numeroFormulario;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroFormulario", numeroFormularioOld, numeroFormulario);
	}



	/**
	 * Return the value associated with the column: serie
	 */
	public java.lang.String getSerie () {
		return getPropertyValue(this, serie, PROP_SERIE); 
	}

	/**
	 * Set the value related to the column: serie
	 * @param serie the serie value
	 */
	public void setSerie (java.lang.String serie) {
//        java.lang.String serieOld = this.serie;
		this.serie = serie;
//        this.getPropertyChangeSupport().firePropertyChange ("serie", serieOld, serie);
	}



	/**
	 * Return the value associated with the column: dt_infracao
	 */
	public java.util.Date getDataInfracao () {
		return getPropertyValue(this, dataInfracao, PROP_DATA_INFRACAO); 
	}

	/**
	 * Set the value related to the column: dt_infracao
	 * @param dataInfracao the dt_infracao value
	 */
	public void setDataInfracao (java.util.Date dataInfracao) {
//        java.util.Date dataInfracaoOld = this.dataInfracao;
		this.dataInfracao = dataInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInfracao", dataInfracaoOld, dataInfracao);
	}



	/**
	 * Return the value associated with the column: tp_denunciado
	 */
	public java.lang.Long getTipoDenunciado () {
		return getPropertyValue(this, tipoDenunciado, PROP_TIPO_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: tp_denunciado
	 * @param tipoDenunciado the tp_denunciado value
	 */
	public void setTipoDenunciado (java.lang.Long tipoDenunciado) {
//        java.lang.Long tipoDenunciadoOld = this.tipoDenunciado;
		this.tipoDenunciado = tipoDenunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDenunciado", tipoDenunciadoOld, tipoDenunciado);
	}



	/**
	 * Return the value associated with the column: denunciado
	 */
	public java.lang.String getDenunciado () {
		return getPropertyValue(this, denunciado, PROP_DENUNCIADO); 
	}

	/**
	 * Set the value related to the column: denunciado
	 * @param denunciado the denunciado value
	 */
	public void setDenunciado (java.lang.String denunciado) {
//        java.lang.String denunciadoOld = this.denunciado;
		this.denunciado = denunciado;
//        this.getPropertyChangeSupport().firePropertyChange ("denunciado", denunciadoOld, denunciado);
	}



	/**
	 * Return the value associated with the column: enquadramento_legal
	 */
	public java.lang.String getEnquadramentoLegal () {
		return getPropertyValue(this, enquadramentoLegal, PROP_ENQUADRAMENTO_LEGAL); 
	}

	/**
	 * Set the value related to the column: enquadramento_legal
	 * @param enquadramentoLegal the enquadramento_legal value
	 */
	public void setEnquadramentoLegal (java.lang.String enquadramentoLegal) {
//        java.lang.String enquadramentoLegalOld = this.enquadramentoLegal;
		this.enquadramentoLegal = enquadramentoLegal;
//        this.getPropertyChangeSupport().firePropertyChange ("enquadramentoLegal", enquadramentoLegalOld, enquadramentoLegal);
	}



	/**
	 * Return the value associated with the column: nm_responsavel
	 */
	public java.lang.String getNomeResponsavel () {
		return getPropertyValue(this, nomeResponsavel, PROP_NOME_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: nm_responsavel
	 * @param nomeResponsavel the nm_responsavel value
	 */
	public void setNomeResponsavel (java.lang.String nomeResponsavel) {
//        java.lang.String nomeResponsavelOld = this.nomeResponsavel;
		this.nomeResponsavel = nomeResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeResponsavel", nomeResponsavelOld, nomeResponsavel);
	}



	/**
	 * Return the value associated with the column: dt_recebimento
	 */
	public java.util.Date getDataRecebimento () {
		return getPropertyValue(this, dataRecebimento, PROP_DATA_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_recebimento
	 * @param dataRecebimento the dt_recebimento value
	 */
	public void setDataRecebimento (java.util.Date dataRecebimento) {
//        java.util.Date dataRecebimentoOld = this.dataRecebimento;
		this.dataRecebimento = dataRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRecebimento", dataRecebimentoOld, dataRecebimento);
	}



	/**
	 * Return the value associated with the column: testemunha
	 */
	public java.lang.String getTestemunha () {
		return getPropertyValue(this, testemunha, PROP_TESTEMUNHA); 
	}

	/**
	 * Set the value related to the column: testemunha
	 * @param testemunha the testemunha value
	 */
	public void setTestemunha (java.lang.String testemunha) {
//        java.lang.String testemunhaOld = this.testemunha;
		this.testemunha = testemunha;
//        this.getPropertyChangeSupport().firePropertyChange ("testemunha", testemunhaOld, testemunha);
	}



	/**
	 * Return the value associated with the column: flag_recusou_assinar
	 */
	public java.lang.Long getRecusouAssinar () {
		return getPropertyValue(this, recusouAssinar, PROP_RECUSOU_ASSINAR); 
	}

	/**
	 * Set the value related to the column: flag_recusou_assinar
	 * @param recusouAssinar the flag_recusou_assinar value
	 */
	public void setRecusouAssinar (java.lang.Long recusouAssinar) {
//        java.lang.Long recusouAssinarOld = this.recusouAssinar;
		this.recusouAssinar = recusouAssinar;
//        this.getPropertyChangeSupport().firePropertyChange ("recusouAssinar", recusouAssinarOld, recusouAssinar);
	}



	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: status
	 * @param situacao the status value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: nm_responsavel_defesa
	 */
	public java.lang.String getNomeResponsavelDefesa () {
		return getPropertyValue(this, nomeResponsavelDefesa, PROP_NOME_RESPONSAVEL_DEFESA); 
	}

	/**
	 * Set the value related to the column: nm_responsavel_defesa
	 * @param nomeResponsavelDefesa the nm_responsavel_defesa value
	 */
	public void setNomeResponsavelDefesa (java.lang.String nomeResponsavelDefesa) {
//        java.lang.String nomeResponsavelDefesaOld = this.nomeResponsavelDefesa;
		this.nomeResponsavelDefesa = nomeResponsavelDefesa;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeResponsavelDefesa", nomeResponsavelDefesaOld, nomeResponsavelDefesa);
	}



	/**
	 * Return the value associated with the column: dt_entrega
	 */
	public java.util.Date getDataEntrega () {
		return getPropertyValue(this, dataEntrega, PROP_DATA_ENTREGA); 
	}

	/**
	 * Set the value related to the column: dt_entrega
	 * @param dataEntrega the dt_entrega value
	 */
	public void setDataEntrega (java.util.Date dataEntrega) {
//        java.util.Date dataEntregaOld = this.dataEntrega;
		this.dataEntrega = dataEntrega;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEntrega", dataEntregaOld, dataEntrega);
	}



	/**
	 * Return the value associated with the column: dt_conclusao
	 */
	public java.util.Date getDataConclusao () {
		return getPropertyValue(this, dataConclusao, PROP_DATA_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: dt_conclusao
	 * @param dataConclusao the dt_conclusao value
	 */
	public void setDataConclusao (java.util.Date dataConclusao) {
//        java.util.Date dataConclusaoOld = this.dataConclusao;
		this.dataConclusao = dataConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConclusao", dataConclusaoOld, dataConclusao);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: prazo_defesa
	 */
	public java.util.Date getPrazoDefesa () {
		return getPropertyValue(this, prazoDefesa, PROP_PRAZO_DEFESA); 
	}

	/**
	 * Set the value related to the column: prazo_defesa
	 * @param prazoDefesa the prazo_defesa value
	 */
	public void setPrazoDefesa (java.util.Date prazoDefesa) {
//        java.util.Date prazoDefesaOld = this.prazoDefesa;
		this.prazoDefesa = prazoDefesa;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoDefesa", prazoDefesaOld, prazoDefesa);
	}



	/**
	 * Return the value associated with the column: enviado
	 */
	public java.lang.Long getEnviado () {
		return getPropertyValue(this, enviado, PROP_ENVIADO); 
	}

	/**
	 * Set the value related to the column: enviado
	 * @param enviado the enviado value
	 */
	public void setEnviado (java.lang.Long enviado) {
//        java.lang.Long enviadoOld = this.enviado;
		this.enviado = enviado;
//        this.getPropertyChangeSupport().firePropertyChange ("enviado", enviadoOld, enviado);
	}



	/**
	 * Return the value associated with the column: num_auto_infracao
	 */
	public java.lang.Long getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: num_auto_infracao
	 * @param numero the num_auto_infracao value
	 */
	public void setNumero (java.lang.Long numero) {
//        java.lang.Long numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: autuado_recusou_auto
	 */
	public java.lang.Long getAutuadoRecusouAuto () {
		return getPropertyValue(this, autuadoRecusouAuto, PROP_AUTUADO_RECUSOU_AUTO); 
	}

	/**
	 * Set the value related to the column: autuado_recusou_auto
	 * @param autuadoRecusouAuto the autuado_recusou_auto value
	 */
	public void setAutuadoRecusouAuto (java.lang.Long autuadoRecusouAuto) {
//        java.lang.Long autuadoRecusouAutoOld = this.autuadoRecusouAuto;
		this.autuadoRecusouAuto = autuadoRecusouAuto;
//        this.getPropertyChangeSupport().firePropertyChange ("autuadoRecusouAuto", autuadoRecusouAutoOld, autuadoRecusouAuto);
	}



	/**
	 * Return the value associated with the column: flag_criado_app_fru
	 */
	public java.lang.Long getFlagCriadoPeloAppFru () {
		return getPropertyValue(this, flagCriadoPeloAppFru, PROP_FLAG_CRIADO_PELO_APP_FRU); 
	}

	/**
	 * Set the value related to the column: flag_criado_app_fru
	 * @param flagCriadoPeloAppFru the flag_criado_app_fru value
	 */
	public void setFlagCriadoPeloAppFru (java.lang.Long flagCriadoPeloAppFru) {
//        java.lang.Long flagCriadoPeloAppFruOld = this.flagCriadoPeloAppFru;
		this.flagCriadoPeloAppFru = flagCriadoPeloAppFru;
//        this.getPropertyChangeSupport().firePropertyChange ("flagCriadoPeloAppFru", flagCriadoPeloAppFruOld, flagCriadoPeloAppFru);
	}



	/**
	 * Return the value associated with the column: flag_email_enviado_fru
	 */
	public java.lang.Long getFlagEmailEnviadoFru () {
		return getPropertyValue(this, flagEmailEnviadoFru, PROP_FLAG_EMAIL_ENVIADO_FRU); 
	}

	/**
	 * Set the value related to the column: flag_email_enviado_fru
	 * @param flagEmailEnviadoFru the flag_email_enviado_fru value
	 */
	public void setFlagEmailEnviadoFru (java.lang.Long flagEmailEnviadoFru) {
//        java.lang.Long flagEmailEnviadoFruOld = this.flagEmailEnviadoFru;
		this.flagEmailEnviadoFru = flagEmailEnviadoFru;
//        this.getPropertyChangeSupport().firePropertyChange ("flagEmailEnviadoFru", flagEmailEnviadoFruOld, flagEmailEnviadoFru);
	}



	/**
	 * Return the value associated with the column: uuid_app_fru
	 */
	public java.lang.String getUuidAppFru () {
		return getPropertyValue(this, uuidAppFru, PROP_UUID_APP_FRU); 
	}

	/**
	 * Set the value related to the column: uuid_app_fru
	 * @param uuidAppFru the uuid_app_fru value
	 */
	public void setUuidAppFru (java.lang.String uuidAppFru) {
//        java.lang.String uuidAppFruOld = this.uuidAppFru;
		this.uuidAppFru = uuidAppFru;
//        this.getPropertyChangeSupport().firePropertyChange ("uuidAppFru", uuidAppFruOld, uuidAppFru);
	}



	/**
	 * Return the value associated with the column: cd_denuncia
	 */
	public br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia getDenuncia () {
		return getPropertyValue(this, denuncia, PROP_DENUNCIA); 
	}

	/**
	 * Set the value related to the column: cd_denuncia
	 * @param denuncia the cd_denuncia value
	 */
	public void setDenuncia (br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia denuncia) {
//        br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia denunciaOld = this.denuncia;
		this.denuncia = denuncia;
//        this.getPropertyChangeSupport().firePropertyChange ("denuncia", denunciaOld, denuncia);
	}



	/**
	 * Return the value associated with the column: cd_auto_intimacao
	 */
	public br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao getAutoIntimacao () {
		return getPropertyValue(this, autoIntimacao, PROP_AUTO_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: cd_auto_intimacao
	 * @param autoIntimacao the cd_auto_intimacao value
	 */
	public void setAutoIntimacao (br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao) {
//        br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacaoOld = this.autoIntimacao;
		this.autoIntimacao = autoIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("autoIntimacao", autoIntimacaoOld, autoIntimacao);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getVigilanciaEndereco () {
		return getPropertyValue(this, vigilanciaEndereco, PROP_VIGILANCIA_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param vigilanciaEndereco the cd_vigilancia_endereco value
	 */
	public void setVigilanciaEndereco (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEnderecoOld = this.vigilanciaEndereco;
		this.vigilanciaEndereco = vigilanciaEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaEndereco", vigilanciaEnderecoOld, vigilanciaEndereco);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_edicao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEdicao () {
		return getPropertyValue(this, usuarioEdicao, PROP_USUARIO_EDICAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_edicao
	 * @param usuarioEdicao the cd_usuario_edicao value
	 */
	public void setUsuarioEdicao (br.com.ksisolucoes.vo.controle.Usuario usuarioEdicao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEdicaoOld = this.usuarioEdicao;
		this.usuarioEdicao = usuarioEdicao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEdicao", usuarioEdicaoOld, usuarioEdicao);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimento the cd_estabelecimento value
	 */
	public void setEstabelecimento (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_pessoa
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa getVigilanciaPessoa () {
		return getPropertyValue(this, vigilanciaPessoa, PROP_VIGILANCIA_PESSOA); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_pessoa
	 * @param vigilanciaPessoa the cd_vigilancia_pessoa value
	 */
	public void setVigilanciaPessoa (br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoa) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoaOld = this.vigilanciaPessoa;
		this.vigilanciaPessoa = vigilanciaPessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaPessoa", vigilanciaPessoaOld, vigilanciaPessoa);
	}



	/**
	 * Return the value associated with the column: cd_roteiro_inspecao
	 */
	public br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao getRegistroInspecao () {
		return getPropertyValue(this, registroInspecao, PROP_REGISTRO_INSPECAO); 
	}

	/**
	 * Set the value related to the column: cd_roteiro_inspecao
	 * @param registroInspecao the cd_roteiro_inspecao value
	 */
	public void setRegistroInspecao (br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao registroInspecao) {
//        br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao registroInspecaoOld = this.registroInspecao;
		this.registroInspecao = registroInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("registroInspecao", registroInspecaoOld, registroInspecao);
	}



	/**
	 * Return the value associated with the column: cd_motivo_retorno
	 */
	public br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno getMotivoRetorno () {
		return getPropertyValue(this, motivoRetorno, PROP_MOTIVO_RETORNO); 
	}

	/**
	 * Set the value related to the column: cd_motivo_retorno
	 * @param motivoRetorno the cd_motivo_retorno value
	 */
	public void setMotivoRetorno (br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno motivoRetorno) {
//        br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno motivoRetornoOld = this.motivoRetorno;
		this.motivoRetorno = motivoRetorno;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoRetorno", motivoRetornoOld, motivoRetorno);
	}



	/**
	 * Return the value associated with the column: cd_req_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia getRequerimentoVigilancia () {
		return getPropertyValue(this, requerimentoVigilancia, PROP_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_req_vigilancia
	 * @param requerimentoVigilancia the cd_req_vigilancia value
	 */
	public void setRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilanciaOld = this.requerimentoVigilancia;
		this.requerimentoVigilancia = requerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilancia", requerimentoVigilanciaOld, requerimentoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_relatorio_inspecao
	 */
	public br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao getRelatorioInspecao () {
		return getPropertyValue(this, relatorioInspecao, PROP_RELATORIO_INSPECAO); 
	}

	/**
	 * Set the value related to the column: cd_relatorio_inspecao
	 * @param relatorioInspecao the cd_relatorio_inspecao value
	 */
	public void setRelatorioInspecao (br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao relatorioInspecao) {
//        br.com.ksisolucoes.vo.vigilancia.RelatorioInspecao relatorioInspecaoOld = this.relatorioInspecao;
		this.relatorioInspecao = relatorioInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("relatorioInspecao", relatorioInspecaoOld, relatorioInspecao);
	}



	/**
	 * Return the value associated with the column: cd_processo_adm_autenticacao
	 */
	public br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao getProcessoAdministrativoAutenticacao () {
		return getPropertyValue(this, processoAdministrativoAutenticacao, PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO); 
	}

	/**
	 * Set the value related to the column: cd_processo_adm_autenticacao
	 * @param processoAdministrativoAutenticacao the cd_processo_adm_autenticacao value
	 */
	public void setProcessoAdministrativoAutenticacao (br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao) {
//        br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacaoOld = this.processoAdministrativoAutenticacao;
		this.processoAdministrativoAutenticacao = processoAdministrativoAutenticacao;
//        this.getPropertyChangeSupport().firePropertyChange ("processoAdministrativoAutenticacao", processoAdministrativoAutenticacaoOld, processoAdministrativoAutenticacao);
	}



	/**
	 * Return the value associated with the column: cd_auto_intimacao_subsistente
	 */
	public br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao getAutoIntimacaoSubsistente () {
		return getPropertyValue(this, autoIntimacaoSubsistente, PROP_AUTO_INTIMACAO_SUBSISTENTE); 
	}

	/**
	 * Set the value related to the column: cd_auto_intimacao_subsistente
	 * @param autoIntimacaoSubsistente the cd_auto_intimacao_subsistente value
	 */
	public void setAutoIntimacaoSubsistente (br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacaoSubsistente) {
//        br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacaoSubsistenteOld = this.autoIntimacaoSubsistente;
		this.autoIntimacaoSubsistente = autoIntimacaoSubsistente;
//        this.getPropertyChangeSupport().firePropertyChange ("autoIntimacaoSubsistente", autoIntimacaoSubsistenteOld, autoIntimacaoSubsistente);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao autoInfracao = (br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao) obj;
			if (null == this.getCodigo() || null == autoInfracao.getCodigo()) return false;
			else return (this.getCodigo().equals(autoInfracao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}