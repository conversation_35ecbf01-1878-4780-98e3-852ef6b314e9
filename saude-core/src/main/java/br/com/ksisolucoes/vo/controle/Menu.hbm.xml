<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.controle"  >
	<class
		name="Menu"
		table="menus"
	>
		<id
			name="codigo"
			type="java.lang.Long"
			column="cd_menu"
		>
			<generator class="assigned" />
			<!--generator class="native">
                            <param name="sequence">menus_cd_menu_seq</param>
                        </generator-->
		</id> <version column="version" name="version" type="long" />

		<property
			name="sequencia"
			column="sequencia"
			type="java.lang.Long"
		/>

		<property
			name="codigoPai"
			column="cd_pai"
			type="java.lang.Long"
			not-null="false"
			length="4"
		/>
		<property
			name="nome"
			column="nm_menu"
			type="string"
			not-null="true"
			length="50"
		/>
		<property
			name="rotulo"
			column="rotulo"
			type="string"
			not-null="true"
			length="50"
		/>
		<property
			name="imagem"
			column="ds_imagem"
			type="string"
			not-null="false"
			length="50"
		/>

                <many-to-one
			name="modulo"
			class="Modulo"
			not-null="true"
		>
			<column name="cd_modulo"/>
		</many-to-one>

	</class>
</hibernate-mapping>