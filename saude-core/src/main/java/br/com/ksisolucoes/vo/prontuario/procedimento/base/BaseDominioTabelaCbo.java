package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the dom_cbo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dom_cbo"
 */

public abstract class BaseDominioTabelaCbo extends BaseRootVO implements Serializable {

	public static String REF = "DominioTabelaCbo";
	public static final String PROP_TABELA_CBO = "tabelaCbo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_KEYWORD = "keyword";
	public static final String PROP_DESCRICAO = "descricao";


	// constructors
	public BaseDominioTabelaCbo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDominioTabelaCbo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String keyword;
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dominio"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: keyword
	 */
	public java.lang.String getKeyword () {
		return getPropertyValue(this, keyword, PROP_KEYWORD); 
	}

	/**
	 * Set the value related to the column: keyword
	 * @param keyword the keyword value
	 */
	public void setKeyword (java.lang.String keyword) {
//        java.lang.String keywordOld = this.keyword;
		this.keyword = keyword;
//        this.getPropertyChangeSupport().firePropertyChange ("keyword", keywordOld, keyword);
	}



	/**
	 * Return the value associated with the column: ds_cidade
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_cidade
	 * @param descricao the ds_cidade value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getTabelaCbo () {
		return getPropertyValue(this, tabelaCbo, PROP_TABELA_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param tabelaCbo the cd_cbo value
	 */
	public void setTabelaCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo tabelaCboOld = this.tabelaCbo;
		this.tabelaCbo = tabelaCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("tabelaCbo", tabelaCboOld, tabelaCbo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.DominioTabelaCbo)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.DominioTabelaCbo dominioTabelaCbo = (br.com.ksisolucoes.vo.prontuario.procedimento.DominioTabelaCbo) obj;
			if (null == this.getCodigo() || null == dominioTabelaCbo.getCodigo()) return false;
			else return (this.getCodigo().equals(dominioTabelaCbo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}