<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.agravo"  >
    <class name="TipoOcorrenciaAgravo" table="tipo_ocorrencia_agravo">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_tp_oco_agravo"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
        
        <property
            name="descricao"
            column="ds_tp_oco_agravo"
            type="java.lang.String"
            length="50" 
        />
        
    </class>
</hibernate-mapping>
