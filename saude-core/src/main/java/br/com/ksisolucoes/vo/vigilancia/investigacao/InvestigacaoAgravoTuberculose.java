package br.com.ksisolucoes.vo.vigilancia.investigacao;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoTuberculose;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class InvestigacaoAgravoTuberculose extends BaseInvestigacaoAgravoTuberculose implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public InvestigacaoAgravoTuberculose () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public InvestigacaoAgravoTuberculose (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public InvestigacaoAgravoTuberculose (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		super (
			codigo,
			registroAgravo,
			flagInformacoesComplementares);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}