package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requerimento_vistoria_pba table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_vistoria_pba"
 */

public abstract class BaseRequerimentoVistoriaProjetoBasicoArquitetura extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoVistoriaProjetoBasicoArquitetura";
	public static final String PROP_COMPLEMENTO_OBRA = "complementoObra";
	public static final String PROP_REQUERIMENTO_VIGILANCIA = "requerimentoVigilancia";
	public static final String PROP_NUMERO_OBRA_AO_LADO = "numeroObraAoLado";
	public static final String PROP_LOTE_OBRA = "loteObra";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_PROJETO_ARQUITETONICO = "numeroProjetoArquitetonico";
	public static final String PROP_NUMERACAO_PARECER_CONFORMIDADE_TECNICA = "numeracaoParecerConformidadeTecnica";
	public static final String PROP_DATA_VISTORIA = "dataVistoria";
	public static final String PROP_VIGILANCIA_ENDERECO = "vigilanciaEndereco";
	public static final String PROP_QUADRA_OBRA = "quadraObra";
	public static final String PROP_AREA = "area";
	public static final String PROP_REQUERIMENTO_ANALISE_PROJETO = "requerimentoAnaliseProjeto";
	public static final String PROP_TIPO_PROJETO_VIGILANCIA = "tipoProjetoVigilancia";
	public static final String PROP_NUMERO_LOTEAMENTO_OBRA = "numeroLoteamentoObra";
	public static final String PROP_NUMERO_OBRA = "numeroObra";
	public static final String PROP_DATA_INSPECAO = "dataInspecao";


	// constructors
	public BaseRequerimentoVistoriaProjetoBasicoArquitetura () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoVistoriaProjetoBasicoArquitetura (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoVistoriaProjetoBasicoArquitetura (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto requerimentoAnaliseProjeto,
		br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia) {

		this.setCodigo(codigo);
		this.setRequerimentoVigilancia(requerimentoVigilancia);
		this.setRequerimentoAnaliseProjeto(requerimentoAnaliseProjeto);
		this.setTipoProjetoVigilancia(tipoProjetoVigilancia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String numeroProjetoArquitetonico;
	private java.lang.Double area;
	private java.util.Date dataVistoria;
	private java.lang.String numeroObra;
	private java.lang.String quadraObra;
	private java.lang.String numeroObraAoLado;
	private java.lang.String loteObra;
	private java.lang.String complementoObra;
	private java.lang.String numeroLoteamentoObra;
	private java.lang.Long numeracaoParecerConformidadeTecnica;
	private java.util.Date dataInspecao;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto requerimentoAnaliseProjeto;
	private br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia;
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_requerimento_vistoria_pba"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nr_projeto_arquitetonico
	 */
	public java.lang.String getNumeroProjetoArquitetonico () {
		return getPropertyValue(this, numeroProjetoArquitetonico, PROP_NUMERO_PROJETO_ARQUITETONICO); 
	}

	/**
	 * Set the value related to the column: nr_projeto_arquitetonico
	 * @param numeroProjetoArquitetonico the nr_projeto_arquitetonico value
	 */
	public void setNumeroProjetoArquitetonico (java.lang.String numeroProjetoArquitetonico) {
//        java.lang.String numeroProjetoArquitetonicoOld = this.numeroProjetoArquitetonico;
		this.numeroProjetoArquitetonico = numeroProjetoArquitetonico;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroProjetoArquitetonico", numeroProjetoArquitetonicoOld, numeroProjetoArquitetonico);
	}



	/**
	 * Return the value associated with the column: area
	 */
	public java.lang.Double getArea () {
		return getPropertyValue(this, area, PROP_AREA); 
	}

	/**
	 * Set the value related to the column: area
	 * @param area the area value
	 */
	public void setArea (java.lang.Double area) {
//        java.lang.Double areaOld = this.area;
		this.area = area;
//        this.getPropertyChangeSupport().firePropertyChange ("area", areaOld, area);
	}



	/**
	 * Return the value associated with the column: dt_vistoria
	 */
	public java.util.Date getDataVistoria () {
		return getPropertyValue(this, dataVistoria, PROP_DATA_VISTORIA); 
	}

	/**
	 * Set the value related to the column: dt_vistoria
	 * @param dataVistoria the dt_vistoria value
	 */
	public void setDataVistoria (java.util.Date dataVistoria) {
//        java.util.Date dataVistoriaOld = this.dataVistoria;
		this.dataVistoria = dataVistoria;
//        this.getPropertyChangeSupport().firePropertyChange ("dataVistoria", dataVistoriaOld, dataVistoria);
	}



	/**
	 * Return the value associated with the column: nr_obra
	 */
	public java.lang.String getNumeroObra () {
		return getPropertyValue(this, numeroObra, PROP_NUMERO_OBRA); 
	}

	/**
	 * Set the value related to the column: nr_obra
	 * @param numeroObra the nr_obra value
	 */
	public void setNumeroObra (java.lang.String numeroObra) {
//        java.lang.String numeroObraOld = this.numeroObra;
		this.numeroObra = numeroObra;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroObra", numeroObraOld, numeroObra);
	}



	/**
	 * Return the value associated with the column: quadra_obra
	 */
	public java.lang.String getQuadraObra () {
		return getPropertyValue(this, quadraObra, PROP_QUADRA_OBRA); 
	}

	/**
	 * Set the value related to the column: quadra_obra
	 * @param quadraObra the quadra_obra value
	 */
	public void setQuadraObra (java.lang.String quadraObra) {
//        java.lang.String quadraObraOld = this.quadraObra;
		this.quadraObra = quadraObra;
//        this.getPropertyChangeSupport().firePropertyChange ("quadraObra", quadraObraOld, quadraObra);
	}



	/**
	 * Return the value associated with the column: nr_lado_obra
	 */
	public java.lang.String getNumeroObraAoLado () {
		return getPropertyValue(this, numeroObraAoLado, PROP_NUMERO_OBRA_AO_LADO); 
	}

	/**
	 * Set the value related to the column: nr_lado_obra
	 * @param numeroObraAoLado the nr_lado_obra value
	 */
	public void setNumeroObraAoLado (java.lang.String numeroObraAoLado) {
//        java.lang.String numeroObraAoLadoOld = this.numeroObraAoLado;
		this.numeroObraAoLado = numeroObraAoLado;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroObraAoLado", numeroObraAoLadoOld, numeroObraAoLado);
	}



	/**
	 * Return the value associated with the column: lote_obra
	 */
	public java.lang.String getLoteObra () {
		return getPropertyValue(this, loteObra, PROP_LOTE_OBRA); 
	}

	/**
	 * Set the value related to the column: lote_obra
	 * @param loteObra the lote_obra value
	 */
	public void setLoteObra (java.lang.String loteObra) {
//        java.lang.String loteObraOld = this.loteObra;
		this.loteObra = loteObra;
//        this.getPropertyChangeSupport().firePropertyChange ("loteObra", loteObraOld, loteObra);
	}



	/**
	 * Return the value associated with the column: complemento_obra
	 */
	public java.lang.String getComplementoObra () {
		return getPropertyValue(this, complementoObra, PROP_COMPLEMENTO_OBRA); 
	}

	/**
	 * Set the value related to the column: complemento_obra
	 * @param complementoObra the complemento_obra value
	 */
	public void setComplementoObra (java.lang.String complementoObra) {
//        java.lang.String complementoObraOld = this.complementoObra;
		this.complementoObra = complementoObra;
//        this.getPropertyChangeSupport().firePropertyChange ("complementoObra", complementoObraOld, complementoObra);
	}



	/**
	 * Return the value associated with the column: nr_loteamento_obra
	 */
	public java.lang.String getNumeroLoteamentoObra () {
		return getPropertyValue(this, numeroLoteamentoObra, PROP_NUMERO_LOTEAMENTO_OBRA); 
	}

	/**
	 * Set the value related to the column: nr_loteamento_obra
	 * @param numeroLoteamentoObra the nr_loteamento_obra value
	 */
	public void setNumeroLoteamentoObra (java.lang.String numeroLoteamentoObra) {
//        java.lang.String numeroLoteamentoObraOld = this.numeroLoteamentoObra;
		this.numeroLoteamentoObra = numeroLoteamentoObra;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLoteamentoObra", numeroLoteamentoObraOld, numeroLoteamentoObra);
	}



	/**
	 * Return the value associated with the column: num_parecer_conformidade_tecnica
	 */
	public java.lang.Long getNumeracaoParecerConformidadeTecnica () {
		return getPropertyValue(this, numeracaoParecerConformidadeTecnica, PROP_NUMERACAO_PARECER_CONFORMIDADE_TECNICA); 
	}

	/**
	 * Set the value related to the column: num_parecer_conformidade_tecnica
	 * @param numeracaoParecerConformidadeTecnica the num_parecer_conformidade_tecnica value
	 */
	public void setNumeracaoParecerConformidadeTecnica (java.lang.Long numeracaoParecerConformidadeTecnica) {
//        java.lang.Long numeracaoParecerConformidadeTecnicaOld = this.numeracaoParecerConformidadeTecnica;
		this.numeracaoParecerConformidadeTecnica = numeracaoParecerConformidadeTecnica;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoParecerConformidadeTecnica", numeracaoParecerConformidadeTecnicaOld, numeracaoParecerConformidadeTecnica);
	}



	/**
	 * Return the value associated with the column: dt_inspecao
	 */
	public java.util.Date getDataInspecao () {
		return getPropertyValue(this, dataInspecao, PROP_DATA_INSPECAO); 
	}

	/**
	 * Set the value related to the column: dt_inspecao
	 * @param dataInspecao the dt_inspecao value
	 */
	public void setDataInspecao (java.util.Date dataInspecao) {
//        java.util.Date dataInspecaoOld = this.dataInspecao;
		this.dataInspecao = dataInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInspecao", dataInspecaoOld, dataInspecao);
	}



	/**
	 * Return the value associated with the column: cd_req_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia getRequerimentoVigilancia () {
		return getPropertyValue(this, requerimentoVigilancia, PROP_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_req_vigilancia
	 * @param requerimentoVigilancia the cd_req_vigilancia value
	 */
	public void setRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilanciaOld = this.requerimentoVigilancia;
		this.requerimentoVigilancia = requerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilancia", requerimentoVigilanciaOld, requerimentoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_requerimento_analise_projeto
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto getRequerimentoAnaliseProjeto () {
		return getPropertyValue(this, requerimentoAnaliseProjeto, PROP_REQUERIMENTO_ANALISE_PROJETO); 
	}

	/**
	 * Set the value related to the column: cd_requerimento_analise_projeto
	 * @param requerimentoAnaliseProjeto the cd_requerimento_analise_projeto value
	 */
	public void setRequerimentoAnaliseProjeto (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto requerimentoAnaliseProjeto) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto requerimentoAnaliseProjetoOld = this.requerimentoAnaliseProjeto;
		this.requerimentoAnaliseProjeto = requerimentoAnaliseProjeto;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoAnaliseProjeto", requerimentoAnaliseProjetoOld, requerimentoAnaliseProjeto);
	}



	/**
	 * Return the value associated with the column: cd_tp_projeto_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia getTipoProjetoVigilancia () {
		return getPropertyValue(this, tipoProjetoVigilancia, PROP_TIPO_PROJETO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_tp_projeto_vigilancia
	 * @param tipoProjetoVigilancia the cd_tp_projeto_vigilancia value
	 */
	public void setTipoProjetoVigilancia (br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.TipoProjetoVigilancia tipoProjetoVigilanciaOld = this.tipoProjetoVigilancia;
		this.tipoProjetoVigilancia = tipoProjetoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoProjetoVigilancia", tipoProjetoVigilanciaOld, tipoProjetoVigilancia);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getVigilanciaEndereco () {
		return getPropertyValue(this, vigilanciaEndereco, PROP_VIGILANCIA_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param vigilanciaEndereco the cd_vigilancia_endereco value
	 */
	public void setVigilanciaEndereco (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEnderecoOld = this.vigilanciaEndereco;
		this.vigilanciaEndereco = vigilanciaEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaEndereco", vigilanciaEnderecoOld, vigilanciaEndereco);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura) obj;
			if (null == this.getCodigo() || null == requerimentoVistoriaProjetoBasicoArquitetura.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoVistoriaProjetoBasicoArquitetura.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}