package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_tracoma table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_tracoma"
 */

public abstract class BaseInvestigacaoAgravoTracoma extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoTracoma";
	public static final String PROP_NUMERO_CASOS_POSITIVOS = "numeroCasosPositivos";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_INQUERITO = "inquerito";
	public static final String PROP_NUMERO_PESSOAS_EXAMINADAS = "numeroPessoasExaminadas";


	// constructors
	public BaseInvestigacaoAgravoTracoma () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoTracoma (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoTracoma (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.lang.Long inquerito;
	private java.lang.String numeroPessoasExaminadas;
	private java.lang.String numeroCasosPositivos;
	private java.lang.String observacao;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="codigo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: inquerito
	 */
	public java.lang.Long getInquerito () {
		return getPropertyValue(this, inquerito, PROP_INQUERITO); 
	}

	/**
	 * Set the value related to the column: inquerito
	 * @param inquerito the inquerito value
	 */
	public void setInquerito (java.lang.Long inquerito) {
//        java.lang.Long inqueritoOld = this.inquerito;
		this.inquerito = inquerito;
//        this.getPropertyChangeSupport().firePropertyChange ("inquerito", inqueritoOld, inquerito);
	}



	/**
	 * Return the value associated with the column: nro_pessoas_examinadas
	 */
	public java.lang.String getNumeroPessoasExaminadas () {
		return getPropertyValue(this, numeroPessoasExaminadas, PROP_NUMERO_PESSOAS_EXAMINADAS); 
	}

	/**
	 * Set the value related to the column: nro_pessoas_examinadas
	 * @param numeroPessoasExaminadas the nro_pessoas_examinadas value
	 */
	public void setNumeroPessoasExaminadas (java.lang.String numeroPessoasExaminadas) {
//        java.lang.String numeroPessoasExaminadasOld = this.numeroPessoasExaminadas;
		this.numeroPessoasExaminadas = numeroPessoasExaminadas;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroPessoasExaminadas", numeroPessoasExaminadasOld, numeroPessoasExaminadas);
	}



	/**
	 * Return the value associated with the column: nro_casos_positivos
	 */
	public java.lang.String getNumeroCasosPositivos () {
		return getPropertyValue(this, numeroCasosPositivos, PROP_NUMERO_CASOS_POSITIVOS); 
	}

	/**
	 * Set the value related to the column: nro_casos_positivos
	 * @param numeroCasosPositivos the nro_casos_positivos value
	 */
	public void setNumeroCasosPositivos (java.lang.String numeroCasosPositivos) {
//        java.lang.String numeroCasosPositivosOld = this.numeroCasosPositivos;
		this.numeroCasosPositivos = numeroCasosPositivos;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCasosPositivos", numeroCasosPositivosOld, numeroCasosPositivos);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma investigacaoAgravoTracoma = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoTracoma) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoTracoma.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoTracoma.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}