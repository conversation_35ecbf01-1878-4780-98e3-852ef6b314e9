package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.vo.basico.base.BaseClassificacaoCids;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;

import java.io.Serializable;



public class ClassificacaoCids extends BaseClassificacaoCids implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ClassificacaoCids () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ClassificacaoCids (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ClassificacaoCids (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long permiteNotificacaoConcomitante) {

		super (
			codigo,
			descricao,
			permiteNotificacaoConcomitante);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }
}