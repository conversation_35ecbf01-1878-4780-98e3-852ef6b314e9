package br.com.ksisolucoes.vo.patrimonio;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.patrimonio.base.BaseBemFormaAquisicao;



public class BemFormaAquisicao extends BaseBemFormaAquisicao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public BemFormaAquisicao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public BemFormaAquisicao (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public BemFormaAquisicao (
		java.lang.Long codigo,
		java.lang.String descricao) {

		super (
			codigo,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}