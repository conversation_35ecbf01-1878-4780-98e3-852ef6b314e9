package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseVinculacaoTipoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_VINCULACAO = "vinculacao";
	public static String PROP_CODIGO = "codigo";

	private br.com.ksisolucoes.vo.cadsus.Vinculacao vinculacao;
	private java.lang.String codigo;


	public BaseVinculacaoTipoPK () {}
	
	public BaseVinculacaoTipoPK (
		br.com.ksisolucoes.vo.cadsus.Vinculacao vinculacao,
		java.lang.String codigo) {

		this.setVinculacao(vinculacao);
		this.setCodigo(codigo);
	}


	/**
	 * Return the value associated with the column: cd_vinculacao
	 */
	public br.com.ksisolucoes.vo.cadsus.Vinculacao getVinculacao () {
		return getPropertyValue(this, vinculacao, PROP_VINCULACAO); 
	}

	/**
	 * Set the value related to the column: cd_vinculacao
	 * @param vinculacao the cd_vinculacao value
	 */
	public void setVinculacao (br.com.ksisolucoes.vo.cadsus.Vinculacao vinculacao) {
//        br.com.ksisolucoes.vo.cadsus.Vinculacao vinculacaoOld = this.vinculacao;
		this.vinculacao = vinculacao;
//        this.getPropertyChangeSupport().firePropertyChange ("vinculacao", vinculacaoOld, vinculacao);
	}



	/**
	 * Return the value associated with the column: cd_tipo_vinculo
	 */
	public java.lang.String getCodigo () {
		return getPropertyValue(this, codigo, PROP_CODIGO); 
	}

	/**
	 * Set the value related to the column: cd_tipo_vinculo
	 * @param codigo the cd_tipo_vinculo value
	 */
	public void setCodigo (java.lang.String codigo) {
//        java.lang.String codigoOld = this.codigo;
		this.codigo = codigo;
//        this.getPropertyChangeSupport().firePropertyChange ("codigo", codigoOld, codigo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.VinculacaoTipoPK)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.VinculacaoTipoPK mObj = (br.com.ksisolucoes.vo.cadsus.VinculacaoTipoPK) obj;
			if (null != this.getVinculacao() && null != mObj.getVinculacao()) {
				if (!this.getVinculacao().equals(mObj.getVinculacao())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getCodigo() && null != mObj.getCodigo()) {
				if (!this.getCodigo().equals(mObj.getCodigo())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getVinculacao()) {
				sb.append(this.getVinculacao().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getCodigo()) {
				sb.append(this.getCodigo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}