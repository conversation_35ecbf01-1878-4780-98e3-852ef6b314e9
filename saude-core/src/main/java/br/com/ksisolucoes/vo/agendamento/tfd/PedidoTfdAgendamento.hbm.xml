<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento.tfd"  >
    <class name="PedidoTfdAgendamento" table="pedido_tfd_agendamento" >

        <id
            name="codigo"
            type="java.lang.Long"   
            column="cd_ped_tfd_agendamento"
        > 
            <generator class="sequence">
                <param name="sequence">seq_pedido_tfd</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />
		
        <many-to-one
            class="br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd"
            name="pedidoTfd"
        >
            <column name="cd_pedido_tfd"/>
        </many-to-one>

        <property
            name="dataAgendamento"
            type="java.util.Date" 
            column="dt_agendamento"
        >
        </property>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="localAgendamento"
        >
            <column name="local_agendamento"/>
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento"
            name="solicitacaoAgendamento"
        >
            <column name="cd_solicitacao"/>
        </many-to-one>

        <property
            name="status"
            type="java.lang.Long"
            column="status"
        >
        </property>
        
        <property
            name="observacao"
            type="java.lang.String"
            column="observacao"
        > 
        </property>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
        >
            <column name="cd_usuario"/>
        </many-to-one>
        
        <property
            name="dataUsuario" 
            type="java.util.Date"
            column="dt_usuario"
        >
        </property>
        
    </class>
</hibernate-mapping>
