package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;

public class InvestigacaoAgravoViolenciaAutoEnum {

    public enum UnidadeNotificadoraEnum implements IEnum {
        UNIDADE_SAUDE(1L, "1- Unidade de Saúde"),
        UNIDADE_ASSISTENCIA(2L, "2- Unidade de Assistência Social"),
        ESTABELECIMENTO_ENSINO(3L, "3- Estabelecimento de Ensino"),
        CONSELHO_TUTELAR(4L, "4- <PERSON>selho Tutelar"),
        UNIDADE_SAUDE_INDIGENA(5L, "5- Unidade de Saúde Indígena"),
        CENTRO_MULHER(6L, "6- Centro Especializado de Atendimento à Mulher"),
        OUTROS(7L, "7- Outros");

        private Long value;
        private String descricao;

        UnidadeNotificadoraEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static UnidadeNotificadoraEnum valueOf(Long value) {
            for (UnidadeNotificadoraEnum v : UnidadeNotificadoraEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum TempoIdadeEnum implements IEnum{
        HORA(1L, "1 - Hora"),
        DIA(2L, "2 - Dia"),
        MES(3L, "3 - Mês"),
        ANO(4L, "4 - Ano");

        private Long value;
        private String descricao;

        TempoIdadeEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static TempoIdadeEnum valueOf(Long value) {
            for (TempoIdadeEnum v : TempoIdadeEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum SexoEnum implements IEnum{
        MASCULINO_1(1L, "1 - Masculino"),
        FEMININO_2(2L, "2 - Feminino"),
        AMBOS(3L, "3 - Ambos os sexos"),
        MASCULINO_M(4L, "M - Masculino"),
        FEMININO_F(5L, "F - Feminino"),
        IGNORADO_I(6L, "I - Ignorado"),
        IGNORADO(4L, "9 - Ignorado");

        private Long value;
        private String descricao;

        SexoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SexoEnum valueOf(Long value) {
            for (SexoEnum v : SexoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }

        public static IEnum[] getSexoNumeral() {
            IEnum[] arr = {SexoEnum.MASCULINO_1, SexoEnum.FEMININO_2, SexoEnum.IGNORADO};
            return arr;
        }

        public static IEnum[] getSexoLetras() {
            IEnum[] arr = {SexoEnum.MASCULINO_M, SexoEnum.FEMININO_F, SexoEnum.IGNORADO_I};
            return arr;
        }
    }

    public enum GestanteEnum implements IEnum{
        TRIMESTRE_1(1L, "1-1ºTrimestre"),
        TRIMESTRE_2(2L, "2-2ºTrimestre"),
        TRIMESTRE_3(3L, "3-3ºTrimestre"),
        IDADE_IGNORADA(4L, "4- Idade gestacional ignorada"),
        NAO(5L, "5-Não"),
        NAO_SE_APLICA(6L, "6- Não se aplica"),
        IGNORADO(9L, "9-Ignorado");

        private Long value;
        private String descricao;

        GestanteEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static GestanteEnum valueOf(Long value) {
            for (GestanteEnum v : GestanteEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum RacaEnum implements IEnum{
        BRANCA(1L, "1-Branca"),
        PRETA(2L, "2-Preta"),
        AMARELA(3L, "3-Amarela"),
        PARDA(4L, "4-Parda"),
        INDIGENA(5L, "5-Indígena"),
        IGNORADO(9L, "9-Ignorado");

        private Long value;
        private String descricao;

        RacaEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static RacaEnum valueOf(Long value) {
            for (RacaEnum v : RacaEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum EscolaridadeEnum implements IEnum{
        ANALFABETO(0L, "0-Analfabeto"),
        PRIMARIO_1_4(1L, "1-1ª a 4ª série incompleta do EF (antigo primário ou 1º grau)"),
        PRIMARIO_2_4(2L, "2-4ª série completa do EF (antigo primário ou 1º grau)"),
        PRIMARIO_3_5(3L, "3-5ª à 8ª série incompleta do EF (antigo ginásio ou 1º grau)"),
        FUNDAMENTAL(4L, "4-Ensino fundamental completo (antigo ginásio ou 1º grau)"),
        ENSINO_MEDIO_INCOMPLETO(5L, "5-Ensino médio incompleto (antigo colegial ou 2º grau )"),
        ENSINO_MEDIO_COMPLETO(6L, "6-Ensino médio completo (antigo colegial ou 2º grau )"),
        SUPERIOR_INCOMPLETO(7L, "7-Educação superior incompleta"),
        SUPERIOR_COMPLETO(8L, "8-Educação superior completa"),
        IGNORADO(9L, "9-Ignorado"),
        NAO_SE_APLICA(9L, "10- Não se aplica");

        private Long value;
        private String descricao;

        EscolaridadeEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static EscolaridadeEnum valueOf(Long value) {
            for (EscolaridadeEnum v : EscolaridadeEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum ZonaEnum implements IEnum {
        URBANA(1L, "1 - Urbana"),
        RURAL(2L, "2 - Rural"),
        PERIURBANA(3L, "3 - Periurbana"),
        IGNORADO(9L, "9 - Ignorado");

        private Long value;
        private String descricao;

        ZonaEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static ZonaEnum valueOf(Long value) {
            for (ZonaEnum v : ZonaEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum SituacaoConjugalEnum implements IEnum {
        SOLTEIRO(1L, "1 - Solteiro"),
        CASADO(2L, "2 - Casado/união consensual"),
        VIUVO(3L, "3 - Viúvo"),
        SEPARADO(4L, "4 - Separado"),
        NAO_SE_APLICA(8L, "8 - Não se aplica"),
        IGNORADO(9L, "9 - Ignorado");

        private Long value;
        private String descricao;

        SituacaoConjugalEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SituacaoConjugalEnum valueOf(Long value) {
            for (SituacaoConjugalEnum v : SituacaoConjugalEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum OrientacaoSexualEnum implements IEnum {
        HETERO(1L, "1-Heterossexual"),
        HOMO(2L, "2-Homossexual (gay/lésbica)"),
        BISSEXUAL(3L, "3-Bissexual"),
        NAO_SE_APLICA(8L, "8 - Não se aplica"),
        IGNORADO(9L, "9 - Ignorado");

        private Long value;
        private String descricao;

        OrientacaoSexualEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static OrientacaoSexualEnum valueOf(Long value) {
            for (OrientacaoSexualEnum v : OrientacaoSexualEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum IdentidadeGeneroEnum implements IEnum {
        TRAVESTI(1L, "1-Travesti"),
        MULHER_TRANSEXUAL(2L, "2-Mulher Transexual"),
        HOMEM_TRANSEXUAL(3L, "3-Homem Transexual"),
        NAO_SE_APLICA(8L, "8 - Não se aplica"),
        IGNORADO(9L, "9 - Ignorado");

        private Long value;
        private String descricao;

        IdentidadeGeneroEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static IdentidadeGeneroEnum valueOf(Long value) {
            for (IdentidadeGeneroEnum v : IdentidadeGeneroEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }
    }

    public enum SimNaoEnum implements IEnum {
        SIM(1L, "1- Sim"),
        NAO(2L, "2- Não"),
        NAO_SE_APLICA(8L, "8 - Não se aplica"),
        IGNORADO(9L, "9- Ignorado");

        private Long value;
        private String descricao;

        SimNaoEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        public static SimNaoEnum valueOf(Long value) {
            for (SimNaoEnum v : SimNaoEnum.values()) {
                if (v.value().equals(value)) {
                    return v;
                }
            }
            return null;
        }

        public static IEnum[] getSimNaoIgnorado() {
            IEnum[] arr = {SimNaoEnum.SIM, SimNaoEnum.NAO, SimNaoEnum.IGNORADO};
            return arr;
        }
    }

    public enum LocalOcorrenciaEnum implements IEnum {
        RESIDENCIA(1L, "01- Residência"),
        HABITACAO_COLETIVA(2L, "02- Habitação coletiva"),
        ESCOLA(3L, "03- Escola"),
        LOCAL_PRATICA_ESPORTIVA(4L, "04- Local de prática esportiva"),
        BAR_SIMILAR(5L, "05- Bar ou similar"),
        VIA_PUBLICA(6L, "06- Via pública"),
        COMERCIO_SERVICOS(7L, "07- Comércio/serviços"),
        INDUSTRIAS_CONSTRUCAO(8L, "08- Indústrias/construção"),
        OUTRO(9L, "09- Outro"),
        IGNORADO(99L, "99- Ignorado"),
        ;

        private final Long value;
        private final String descricao;

        private LocalOcorrenciaEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static LocalOcorrenciaEnum valueOf(Long value) {
            for (LocalOcorrenciaEnum localOcorrencia : LocalOcorrenciaEnum.values()) {
                if (localOcorrencia.value().equals(value)) {
                    return localOcorrencia;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum EssaViolenciaEnum implements IEnum {
        SEXISMO(1L, "01-Sexismo"),
        HOMOFOBIA(2L, "02-Homofobia/Lesbofobia/Bifobia/Transfobia"),
        RACISMO(3L, "03-Racismo"),
        INTOL_RELIGIOSA(4L, "04-Intolerância religiosa"),
        XENOFOBIA(5L, "05-Xenofobia"),
        CONFLITO(6L, "06-Conflito geracional"),
        SITUACAO_RUA(7L, "07-Situação de rua"),
        DEFICIENCIA(8L, "08-Deficiência"),
        OUTRO(9L, "09- Outro"),
        NAO_SE_APLICA(88L, "88- Não se aplica"),
        IGNORADO(99L, "99- Ignorado"),
        ;

        private final Long value;
        private final String descricao;

        private EssaViolenciaEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static EssaViolenciaEnum valueOf(Long value) {
            for (EssaViolenciaEnum localOcorrencia : EssaViolenciaEnum.values()) {
                if (localOcorrencia.value().equals(value)) {
                    return localOcorrencia;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum NumEnvolvidosEnum implements IEnum {
        UM(1L, "1 - Um"),
        DOIS_MAIS(2L, "2 - Dois ou mais"),
        IGNORADO(9L, "9- Ignorado"),
        ;

        private final Long value;
        private final String descricao;

        private NumEnvolvidosEnum(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static NumEnvolvidosEnum valueOf(Long value) {
            for (NumEnvolvidosEnum localOcorrencia : NumEnvolvidosEnum.values()) {
                if (localOcorrencia.value().equals(value)) {
                    return localOcorrencia;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum CicloVidaProvavelAutor implements IEnum {
        CRIANCA(1L, "1- Criança (0 a 9 anos)"),
        ADOLESCENTE(2L, "2- Adolescente (10 a 19 anos)"),
        JOVEM(3L, "3-   Jovem (20 a 24 anos)"),
        PESSOA_ADULTA(4L, "4- Pessoa adulta (25 a 59 anos)"),
        PESSOA_IDOSA(5L, "5- Pessoa idosa (60 anos ou mais)"),
        IGNORADO(9L, "9- Ignorado"),
        ;

        private final Long value;
        private final String descricao;

        private CicloVidaProvavelAutor(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static InvestigacaoAgravoViolencia.CicloVidaProvavelAutor valueOf(Long value) {
            for (InvestigacaoAgravoViolencia.CicloVidaProvavelAutor cicloVidaProvavelAutor : InvestigacaoAgravoViolencia.CicloVidaProvavelAutor.values()) {
                if (cicloVidaProvavelAutor.value().equals(value)) {
                    return cicloVidaProvavelAutor;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }




}
