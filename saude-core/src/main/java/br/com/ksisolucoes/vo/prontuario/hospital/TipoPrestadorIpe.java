package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseTipoPrestadorIpe;

public class TipoPrestadorIpe extends BaseTipoPrestadorIpe implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public enum TipoDocumento {

        CPF(1L),
        CNPJ(2L);
        private Long value;

        private TipoDocumento(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public String getTipoDocumentoDescricao() {
        if (TipoDocumento.CPF.value.equals(getTipoDocumento())) {
            return Bundle.getStringApplication("rotulo_cpf");
        } else {
            return Bundle.getStringApplication("rotulo_cnpj");
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public TipoPrestadorIpe() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public TipoPrestadorIpe(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public TipoPrestadorIpe(
            java.lang.Long codigo,
            java.lang.String tipo,
            java.lang.String descricaoTipo,
            java.lang.Long tipoDocumento,
            java.lang.Long tipoAtividadeConsultaMedica,
            java.lang.Long tipoAtividadeAtendimentoComplementar,
            java.lang.Long tipoAtividadeProntoAtendimento,
            java.lang.Long tipoAtividadeContaHospitalar,
            java.lang.Long tipoAtividadeContaAmbulatorial) {

        super(
                codigo,
                tipo,
                descricaoTipo,
                tipoDocumento,
                tipoAtividadeConsultaMedica,
                tipoAtividadeAtendimentoComplementar,
                tipoAtividadeProntoAtendimento,
                tipoAtividadeContaHospitalar,
                tipoAtividadeContaAmbulatorial);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}