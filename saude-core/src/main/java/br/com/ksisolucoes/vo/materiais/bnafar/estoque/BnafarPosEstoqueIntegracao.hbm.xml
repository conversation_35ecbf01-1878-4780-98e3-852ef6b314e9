<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.materiais.bnafar.estoque"  >
    <class 
        name="BnafarPosEstoqueIntegracao"
        table="bnafar_pos_estoque_integracao"
    >
	
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_bnafar_pos_estoque_integracao"
        >
            <generator class="sequence">
                <param name="sequence">seq_bnafar_pos_estoque_integracao</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />

        <property
                column="json_envio"
                name="jsonEnvio"
                type="java.lang.String"
                not-null="true"
        />

        <property
                column="dt_envio"
                name="dataEnvio"
                type="timestamp"
                not-null="true"
        />

        <property
                column="json_retorno"
                name="jsonRetorno"
                type="java.lang.String"
        />

        <property
                column="dt_retorno"
                name="dataRetorno"
                type="timestamp"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

        <property
                column="mensagem_retorno_original"
                name="mensagemRetornoOriginal"
                type="java.lang.String"
                not-null="true"
        />

    </class>
</hibernate-mapping>