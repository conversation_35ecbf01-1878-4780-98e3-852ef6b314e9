<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="EventoSistema" table="evento_sistema" >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_evento_sistema"
        >
            <generator class="sequence">
                <param name="sequence">seq_auditoria</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

        <property
            column="ds_evento"
            name="descricao"
            not-null="true"
            type="java.lang.String"
        />

        <property
            column="dt_registro"
            name="dataRegistro"
            not-null="true"
            type="timestamp"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
        />

        <property
            column="nivel_criticidade"
            name="nivelCriticidade"
            not-null="true"
            type="java.lang.Long"
        />

        <property
            column="fonte_evento"
            name="fonteEvento"
            not-null="true"
            type="java.lang.String"
        />

        <property
            column="tipo_evento"
            name="tipoEvento"
            not-null="true"
            type="java.lang.Long"
        />

        <property
            column="maquina"
            name="maquina"
            not-null="true"
            type="java.lang.String"
        />

        <property
            column="keyword"
            name="keyword"
            not-null="true"
            type="java.lang.String"
        />

        <property
            column="id_evento"
            name="idEvento"
            not-null="true"
            type="java.lang.Long"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            column="cd_usu_cadsus"
            name="usuarioCadsus"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.EventoSistema"
            column="cd_evento_sistema_pai"
            name="eventoSistemaPai"
        />

        <property
            column="codigo"
            name="codigoDocumento"
            type="java.lang.String"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresa"
            column="empresa"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            name="profissional"
            column="cd_profissional"
        />

        <property
            column="ds_empresa"
            name="descricaoEmpresa"
            type="java.lang.String"
        />

        <property
            column="ds_profissional"
            name="descricaoProfissional"
            type="java.lang.String"
        />

        <property
            column="ds_usuario"
            name="descricaoUsuario"
            type="java.lang.String"
        />

        <property
            column="ds_cadsus"
            name="descricaoUsuarioCadsus"
            type="java.lang.String"
        />
    </class>
</hibernate-mapping>
