<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="AcaoProgramatica" table="acao_programatica" >
        <id
            name="codigo"
            column="cd_acao_programatica"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id>
        
        <version column="version" name="version" type="long" />
		
        <property 
            name="descricao"
            column="ds_acao_programatica"
            type="java.lang.String"
            length="100"
            not-null="false"
		/>      

        <property 
            name="descricaoReduzidaAcaoProgramatica"
            column="ds_red_acao_programatica"
            type="java.lang.String"
            length="50"
            not-null="false"
		/>        
        
        
    </class>
</hibernate-mapping>
