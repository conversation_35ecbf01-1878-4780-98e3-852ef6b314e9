package br.com.ksisolucoes.vo.cadsus.cds.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the esus_ficha_zika table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="esus_ficha_zika"
 */

public abstract class BaseEsusFichaZika extends BaseRootVO implements Serializable {

	public static String REF = "EsusFichaZika";
	public static final String PROP_DATA_TOMOGRAFIA_COMP = "dataTomografiaComp";
	public static final String PROP_DATA_TESTE_OLHINHO = "dataTesteOlhinho";
	public static final String PROP_US_TRANSFONTANELA = "usTransfontanela";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_TESTE_ORELHINHA = "testeOrelhinha";
	public static final String PROP_EXAME_FUNDO_OLHO = "exameFundoOlho";
	public static final String PROP_DATA_US_TRANSFONTANELA = "dataUsTransfontanela";
	public static final String PROP_CODIGO_INE = "codigoIne";
	public static final String PROP_DATA_ATENDIMENTO = "dataAtendimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CBO = "cbo";
	public static final String PROP_DATA_RESSONANCIA_MAG = "dataRessonanciaMag";
	public static final String PROP_TOMOGRAFIA_COMP = "tomografiaComp";
	public static final String PROP_TURNO = "turno";
	public static final String PROP_PROFISSIONAL = "profissional";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_TESTE_OLHINHO = "testeOlhinho";
	public static final String PROP_RESSONANCIA_MAG = "ressonanciaMag";
	public static final String PROP_DATA_EXAME_FUNDO_OLHO = "dataExameFundoOlho";
	public static final String PROP_CNS = "cns";
	public static final String PROP_DATA_TESTE_ORELHINHA = "dataTesteOrelhinha";


	// constructors
	public BaseEsusFichaZika () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEsusFichaZika (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEsusFichaZika (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.Profissional profissional,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo,
		java.util.Date dataCadastro,
		java.util.Date dataAtendimento,
		java.lang.Long turno,
		java.lang.Long cns) {

		this.setCodigo(codigo);
		this.setProfissional(profissional);
		this.setUsuario(usuario);
		this.setEmpresa(empresa);
		this.setCbo(cbo);
		this.setDataCadastro(dataCadastro);
		this.setDataAtendimento(dataAtendimento);
		this.setTurno(turno);
		this.setCns(cns);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;
	private java.lang.String codigoIne;
	private java.util.Date dataAtendimento;
	private java.lang.Long turno;
	private java.lang.Long cns;
	private java.util.Date dataTesteOlhinho;
	private java.lang.Long testeOlhinho;
	private java.util.Date dataExameFundoOlho;
	private java.lang.Long exameFundoOlho;
	private java.util.Date dataTesteOrelhinha;
	private java.lang.Long testeOrelhinha;
	private java.util.Date dataUsTransfontanela;
	private java.lang.Long usTransfontanela;
	private java.util.Date dataTomografiaComp;
	private java.lang.Long tomografiaComp;
	private java.util.Date dataRessonanciaMag;
	private java.lang.Long ressonanciaMag;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissional;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_esus_ficha_zika"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cod_ine
	 */
	public java.lang.String getCodigoIne () {
		return getPropertyValue(this, codigoIne, PROP_CODIGO_INE); 
	}

	/**
	 * Set the value related to the column: cod_ine
	 * @param codigoIne the cod_ine value
	 */
	public void setCodigoIne (java.lang.String codigoIne) {
//        java.lang.String codigoIneOld = this.codigoIne;
		this.codigoIne = codigoIne;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoIne", codigoIneOld, codigoIne);
	}



	/**
	 * Return the value associated with the column: dt_atendimento
	 */
	public java.util.Date getDataAtendimento () {
		return getPropertyValue(this, dataAtendimento, PROP_DATA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_atendimento
	 * @param dataAtendimento the dt_atendimento value
	 */
	public void setDataAtendimento (java.util.Date dataAtendimento) {
//        java.util.Date dataAtendimentoOld = this.dataAtendimento;
		this.dataAtendimento = dataAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtendimento", dataAtendimentoOld, dataAtendimento);
	}



	/**
	 * Return the value associated with the column: turno
	 */
	public java.lang.Long getTurno () {
		return getPropertyValue(this, turno, PROP_TURNO); 
	}

	/**
	 * Set the value related to the column: turno
	 * @param turno the turno value
	 */
	public void setTurno (java.lang.Long turno) {
//        java.lang.Long turnoOld = this.turno;
		this.turno = turno;
//        this.getPropertyChangeSupport().firePropertyChange ("turno", turnoOld, turno);
	}



	/**
	 * Return the value associated with the column: cns
	 */
	public java.lang.Long getCns () {
		return getPropertyValue(this, cns, PROP_CNS); 
	}

	/**
	 * Set the value related to the column: cns
	 * @param cns the cns value
	 */
	public void setCns (java.lang.Long cns) {
//        java.lang.Long cnsOld = this.cns;
		this.cns = cns;
//        this.getPropertyChangeSupport().firePropertyChange ("cns", cnsOld, cns);
	}



	/**
	 * Return the value associated with the column: dt_teste_olhinho
	 */
	public java.util.Date getDataTesteOlhinho () {
		return getPropertyValue(this, dataTesteOlhinho, PROP_DATA_TESTE_OLHINHO); 
	}

	/**
	 * Set the value related to the column: dt_teste_olhinho
	 * @param dataTesteOlhinho the dt_teste_olhinho value
	 */
	public void setDataTesteOlhinho (java.util.Date dataTesteOlhinho) {
//        java.util.Date dataTesteOlhinhoOld = this.dataTesteOlhinho;
		this.dataTesteOlhinho = dataTesteOlhinho;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTesteOlhinho", dataTesteOlhinhoOld, dataTesteOlhinho);
	}



	/**
	 * Return the value associated with the column: teste_olhinho
	 */
	public java.lang.Long getTesteOlhinho () {
		return getPropertyValue(this, testeOlhinho, PROP_TESTE_OLHINHO); 
	}

	/**
	 * Set the value related to the column: teste_olhinho
	 * @param testeOlhinho the teste_olhinho value
	 */
	public void setTesteOlhinho (java.lang.Long testeOlhinho) {
//        java.lang.Long testeOlhinhoOld = this.testeOlhinho;
		this.testeOlhinho = testeOlhinho;
//        this.getPropertyChangeSupport().firePropertyChange ("testeOlhinho", testeOlhinhoOld, testeOlhinho);
	}



	/**
	 * Return the value associated with the column: dt_exame_fundo_olho
	 */
	public java.util.Date getDataExameFundoOlho () {
		return getPropertyValue(this, dataExameFundoOlho, PROP_DATA_EXAME_FUNDO_OLHO); 
	}

	/**
	 * Set the value related to the column: dt_exame_fundo_olho
	 * @param dataExameFundoOlho the dt_exame_fundo_olho value
	 */
	public void setDataExameFundoOlho (java.util.Date dataExameFundoOlho) {
//        java.util.Date dataExameFundoOlhoOld = this.dataExameFundoOlho;
		this.dataExameFundoOlho = dataExameFundoOlho;
//        this.getPropertyChangeSupport().firePropertyChange ("dataExameFundoOlho", dataExameFundoOlhoOld, dataExameFundoOlho);
	}



	/**
	 * Return the value associated with the column: exame_fundo_olho
	 */
	public java.lang.Long getExameFundoOlho () {
		return getPropertyValue(this, exameFundoOlho, PROP_EXAME_FUNDO_OLHO); 
	}

	/**
	 * Set the value related to the column: exame_fundo_olho
	 * @param exameFundoOlho the exame_fundo_olho value
	 */
	public void setExameFundoOlho (java.lang.Long exameFundoOlho) {
//        java.lang.Long exameFundoOlhoOld = this.exameFundoOlho;
		this.exameFundoOlho = exameFundoOlho;
//        this.getPropertyChangeSupport().firePropertyChange ("exameFundoOlho", exameFundoOlhoOld, exameFundoOlho);
	}



	/**
	 * Return the value associated with the column: dt_teste_orelhinha
	 */
	public java.util.Date getDataTesteOrelhinha () {
		return getPropertyValue(this, dataTesteOrelhinha, PROP_DATA_TESTE_ORELHINHA); 
	}

	/**
	 * Set the value related to the column: dt_teste_orelhinha
	 * @param dataTesteOrelhinha the dt_teste_orelhinha value
	 */
	public void setDataTesteOrelhinha (java.util.Date dataTesteOrelhinha) {
//        java.util.Date dataTesteOrelhinhaOld = this.dataTesteOrelhinha;
		this.dataTesteOrelhinha = dataTesteOrelhinha;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTesteOrelhinha", dataTesteOrelhinhaOld, dataTesteOrelhinha);
	}



	/**
	 * Return the value associated with the column: teste_orelhinha
	 */
	public java.lang.Long getTesteOrelhinha () {
		return getPropertyValue(this, testeOrelhinha, PROP_TESTE_ORELHINHA); 
	}

	/**
	 * Set the value related to the column: teste_orelhinha
	 * @param testeOrelhinha the teste_orelhinha value
	 */
	public void setTesteOrelhinha (java.lang.Long testeOrelhinha) {
//        java.lang.Long testeOrelhinhaOld = this.testeOrelhinha;
		this.testeOrelhinha = testeOrelhinha;
//        this.getPropertyChangeSupport().firePropertyChange ("testeOrelhinha", testeOrelhinhaOld, testeOrelhinha);
	}



	/**
	 * Return the value associated with the column: dt_us_transfontanela
	 */
	public java.util.Date getDataUsTransfontanela () {
		return getPropertyValue(this, dataUsTransfontanela, PROP_DATA_US_TRANSFONTANELA); 
	}

	/**
	 * Set the value related to the column: dt_us_transfontanela
	 * @param dataUsTransfontanela the dt_us_transfontanela value
	 */
	public void setDataUsTransfontanela (java.util.Date dataUsTransfontanela) {
//        java.util.Date dataUsTransfontanelaOld = this.dataUsTransfontanela;
		this.dataUsTransfontanela = dataUsTransfontanela;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsTransfontanela", dataUsTransfontanelaOld, dataUsTransfontanela);
	}



	/**
	 * Return the value associated with the column: us_transfontanela
	 */
	public java.lang.Long getUsTransfontanela () {
		return getPropertyValue(this, usTransfontanela, PROP_US_TRANSFONTANELA); 
	}

	/**
	 * Set the value related to the column: us_transfontanela
	 * @param usTransfontanela the us_transfontanela value
	 */
	public void setUsTransfontanela (java.lang.Long usTransfontanela) {
//        java.lang.Long usTransfontanelaOld = this.usTransfontanela;
		this.usTransfontanela = usTransfontanela;
//        this.getPropertyChangeSupport().firePropertyChange ("usTransfontanela", usTransfontanelaOld, usTransfontanela);
	}



	/**
	 * Return the value associated with the column: dt_tomografia_comp
	 */
	public java.util.Date getDataTomografiaComp () {
		return getPropertyValue(this, dataTomografiaComp, PROP_DATA_TOMOGRAFIA_COMP); 
	}

	/**
	 * Set the value related to the column: dt_tomografia_comp
	 * @param dataTomografiaComp the dt_tomografia_comp value
	 */
	public void setDataTomografiaComp (java.util.Date dataTomografiaComp) {
//        java.util.Date dataTomografiaCompOld = this.dataTomografiaComp;
		this.dataTomografiaComp = dataTomografiaComp;
//        this.getPropertyChangeSupport().firePropertyChange ("dataTomografiaComp", dataTomografiaCompOld, dataTomografiaComp);
	}



	/**
	 * Return the value associated with the column: tomografia_comp
	 */
	public java.lang.Long getTomografiaComp () {
		return getPropertyValue(this, tomografiaComp, PROP_TOMOGRAFIA_COMP); 
	}

	/**
	 * Set the value related to the column: tomografia_comp
	 * @param tomografiaComp the tomografia_comp value
	 */
	public void setTomografiaComp (java.lang.Long tomografiaComp) {
//        java.lang.Long tomografiaCompOld = this.tomografiaComp;
		this.tomografiaComp = tomografiaComp;
//        this.getPropertyChangeSupport().firePropertyChange ("tomografiaComp", tomografiaCompOld, tomografiaComp);
	}



	/**
	 * Return the value associated with the column: dt_ressonancia_mag
	 */
	public java.util.Date getDataRessonanciaMag () {
		return getPropertyValue(this, dataRessonanciaMag, PROP_DATA_RESSONANCIA_MAG); 
	}

	/**
	 * Set the value related to the column: dt_ressonancia_mag
	 * @param dataRessonanciaMag the dt_ressonancia_mag value
	 */
	public void setDataRessonanciaMag (java.util.Date dataRessonanciaMag) {
//        java.util.Date dataRessonanciaMagOld = this.dataRessonanciaMag;
		this.dataRessonanciaMag = dataRessonanciaMag;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRessonanciaMag", dataRessonanciaMagOld, dataRessonanciaMag);
	}



	/**
	 * Return the value associated with the column: ressonancia_mag
	 */
	public java.lang.Long getRessonanciaMag () {
		return getPropertyValue(this, ressonanciaMag, PROP_RESSONANCIA_MAG); 
	}

	/**
	 * Set the value related to the column: ressonancia_mag
	 * @param ressonanciaMag the ressonancia_mag value
	 */
	public void setRessonanciaMag (java.lang.Long ressonanciaMag) {
//        java.lang.Long ressonanciaMagOld = this.ressonanciaMag;
		this.ressonanciaMag = ressonanciaMag;
//        this.getPropertyChangeSupport().firePropertyChange ("ressonanciaMag", ressonanciaMagOld, ressonanciaMag);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissional () {
		return getPropertyValue(this, profissional, PROP_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissional the cd_profissional value
	 */
	public void setProfissional (br.com.ksisolucoes.vo.cadsus.Profissional profissional) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalOld = this.profissional;
		this.profissional = profissional;
//        this.getPropertyChangeSupport().firePropertyChange ("profissional", profissionalOld, profissional);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getCbo () {
		return getPropertyValue(this, cbo, PROP_CBO); 
	}

	/**
	 * Set the value related to the column: cd_cbo
	 * @param cbo the cd_cbo value
	 */
	public void setCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo cboOld = this.cbo;
		this.cbo = cbo;
//        this.getPropertyChangeSupport().firePropertyChange ("cbo", cboOld, cbo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.cds.EsusFichaZika)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.cds.EsusFichaZika esusFichaZika = (br.com.ksisolucoes.vo.cadsus.cds.EsusFichaZika) obj;
			if (null == this.getCodigo() || null == esusFichaZika.getCodigo()) return false;
			else return (this.getCodigo().equals(esusFichaZika.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}