package br.com.ksisolucoes.vo.formulario;

import java.io.Serializable;

import br.com.ksisolucoes.vo.formulario.base.BaseFormularioItemDado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class FormularioItemDado extends BaseFormularioItemDado implements CodigoManager {
	private static final long serialVersionUID = 1L;

        private Object valorCalc;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public FormularioItemDado () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public FormularioItemDado (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public FormularioItemDado (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.formulario.FormularioItem formularioItem) {

		super (
			codigo,
			formularioItem);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public Object getValorCalc() {
        return valorCalc;
    }

    public void setValorCalc(Object valorCalc) {
        this.valorCalc = valorCalc;
    }

}