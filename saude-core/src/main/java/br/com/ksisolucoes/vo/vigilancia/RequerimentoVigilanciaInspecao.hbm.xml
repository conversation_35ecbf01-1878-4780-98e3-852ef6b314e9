<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia">
    <class name="RequerimentoVigilanciaInspecao" table="requerimento_vigilancia_inspecao">
        <id
                column="cd_requerimento_vigilancia_inspecao"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_requerimento_vigilancia_inspecao</param>
            </generator>

        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia"
                column="cd_req_vigilancia"
                not-null="true"
                name="requerimentoVigilancia"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.Profissional"
                column="cd_profissional"
                not-null="true"
                name="profissional"
        />

        <property
                column="tp_inspecao"
                name="tipoInspecao"
                type="java.lang.Long"
        />

        <property
                column="dt_inspecao"
                name="dataInspecao"
                type="java.util.Date"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                not-null="true"
                type="timestamp"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuario"
                column="cd_usuario"
                not-null="true"
        />

    </class>
</hibernate-mapping>
