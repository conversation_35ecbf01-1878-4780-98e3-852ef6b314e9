package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the tipo_logradouro_cadsus table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_logradouro_cadsus"
 */

public abstract class BaseTipoLogradouroCadsus extends BaseRootVO implements Serializable {

	public static String REF = "TipoLogradouroCadsus";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SIGLA = "sigla";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_VERSION_ALL = "versionAll";


	// constructors
	public BaseTipoLogradouroCadsus () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoLogradouroCadsus (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoLogradouroCadsus (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long versionAll) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setVersionAll(versionAll);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String sigla;
	private java.lang.Long versionAll;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tipo_logradouro"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_tipo_logradouro
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_tipo_logradouro
	 * @param descricao the ds_tipo_logradouro value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: ds_sigla_logradouro
	 */
	public java.lang.String getSigla () {
		return getPropertyValue(this, sigla, PROP_SIGLA); 
	}

	/**
	 * Set the value related to the column: ds_sigla_logradouro
	 * @param sigla the ds_sigla_logradouro value
	 */
	public void setSigla (java.lang.String sigla) {
//        java.lang.String siglaOld = this.sigla;
		this.sigla = sigla;
//        this.getPropertyChangeSupport().firePropertyChange ("sigla", siglaOld, sigla);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus tipoLogradouroCadsus = (br.com.ksisolucoes.vo.cadsus.TipoLogradouroCadsus) obj;
			if (null == this.getCodigo() || null == tipoLogradouroCadsus.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoLogradouroCadsus.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}