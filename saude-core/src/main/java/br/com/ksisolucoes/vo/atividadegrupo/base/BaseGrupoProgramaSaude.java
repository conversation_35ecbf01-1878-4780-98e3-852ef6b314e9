package br.com.ksisolucoes.vo.atividadegrupo.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the grupo_programa_saude table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="grupo_programa_saude"
 */

public abstract class BaseGrupoProgramaSaude extends BaseRootVO implements Serializable {

	public static String REF = "GrupoProgramaSaude";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROGRAMA_SAUDE = "programaSaude";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_DESCRICAO = "descricao";


	// constructors
	public BaseGrupoProgramaSaude () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseGrupoProgramaSaude (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseGrupoProgramaSaude (
		java.lang.Long codigo,
		java.lang.String descricao) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;

	// many to one
	private br.com.ksisolucoes.vo.programasaude.ProgramaSaude programaSaude;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_grupo_paciente"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_grupo_paciente
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_grupo_paciente
	 * @param descricao the ds_grupo_paciente value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: cd_programa_saude
	 */
	public br.com.ksisolucoes.vo.programasaude.ProgramaSaude getProgramaSaude () {
		return getPropertyValue(this, programaSaude, PROP_PROGRAMA_SAUDE); 
	}

	/**
	 * Set the value related to the column: cd_programa_saude
	 * @param programaSaude the cd_programa_saude value
	 */
	public void setProgramaSaude (br.com.ksisolucoes.vo.programasaude.ProgramaSaude programaSaude) {
//        br.com.ksisolucoes.vo.programasaude.ProgramaSaude programaSaudeOld = this.programaSaude;
		this.programaSaude = programaSaude;
//        this.getPropertyChangeSupport().firePropertyChange ("programaSaude", programaSaudeOld, programaSaude);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.atividadegrupo.GrupoProgramaSaude)) return false;
		else {
			br.com.ksisolucoes.vo.atividadegrupo.GrupoProgramaSaude grupoProgramaSaude = (br.com.ksisolucoes.vo.atividadegrupo.GrupoProgramaSaude) obj;
			if (null == this.getCodigo() || null == grupoProgramaSaude.getCodigo()) return false;
			else return (this.getCodigo().equals(grupoProgramaSaude.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}