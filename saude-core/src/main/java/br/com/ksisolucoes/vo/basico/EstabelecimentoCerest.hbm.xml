<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class
        name="EstabelecimentoCerest"
        table="estabelecimento_cerest"
        >
        <id
            name="codigo"
            type="java.lang.Long" 
            column="cd_estabelecimento_cerest"
            >
            <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />
 
        <property
            name="razaoSocial"
            column="razao_social"
            type="java.lang.String"
            not-null="true"
            length="100"
            />

        <property
                name="fantasia"
                column="fantasia"
                type="java.lang.String"
                not-null="false"
                length="50"
        />

        <property
                name="cnpj"
                column="cnpj"
                type="java.lang.String"
                not-null="true"
                length="15"
        />

        <property
                name="matriz"
                column="matriz"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="dataInicioFuncionamento"
                column="dt_inicio_funcionamento"
                type="java.util.Date"
                not-null="false"
        />

        <property
                name="email"
                column="email"
                type="java.lang.String"
                not-null="false"
                length="100"
        />

        <property
                name="telefone"
                column="telefone"
                type="java.lang.String"
                not-null="false"
                length="15"
        />

        <property
                name="logradouro"
                column="logradouro"
                type="java.lang.String"
                not-null="false"
                length="100"
        />

        <property
                name="numeroLogradouro"
                column="nr_logradouro"
                type="java.lang.String"
                not-null="false"
                length="6"
        />

        <property
                name="bairro"
                column="bairro"
                type="java.lang.String"
                not-null="false"
                length="100"
        />

        <property
                name="complemento"
                column="complemento"
                type="java.lang.String"
                not-null="false"
                length="100"
        />

        <property
                name="cep"
                column="cep"
                type="java.lang.String"
                not-null="false"
                length="10"
        />

        <many-to-one
                name="cidade"
                class="br.com.ksisolucoes.vo.basico.Cidade"
        >
            <column name="cod_cid"/>
        </many-to-one>

        <many-to-one
                name="estado"
                class="br.com.ksisolucoes.vo.basico.Estado"
        >
            <column name="cod_est"/>
        </many-to-one>

        <property
                name="situacao"
                column="situacao"
                type="java.lang.Long"
                not-null="true"
                length="10"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="java.util.Date"
                not-null="true"
        />

        <many-to-one
                name="usuario"
                class="br.com.ksisolucoes.vo.controle.Usuario"
        >
            <column name="cd_usuario"/>
        </many-to-one>

        <property
                name="dataCancelamento"
                column="dt_cancelamento"
                type="java.util.Date"
                not-null="false"
        />

        <many-to-one
                name="usuarioCan"
                class="br.com.ksisolucoes.vo.controle.Usuario"
        >
            <column name="cd_usuario_can"/>
        </many-to-one>
    </class>
</hibernate-mapping>
