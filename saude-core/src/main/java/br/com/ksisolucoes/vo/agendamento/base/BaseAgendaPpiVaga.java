package br.com.ksisolucoes.vo.agendamento.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the agenda_ppi_vaga table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="agenda_ppi_vaga"
 */

public abstract class BaseAgendaPpiVaga extends BaseRootVO implements Serializable {

	public static String REF = "AgendaPpiVaga";
	public static final String PROP_QUANTIDADE_VAGA_TOTAL = "quantidadeVagaTotal";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";


	// constructors
	public BaseAgendaPpiVaga () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAgendaPpiVaga (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAgendaPpiVaga (
		java.lang.Long codigo,
		java.lang.Long quantidadeVagaTotal) {

		this.setCodigo(codigo);
		this.setQuantidadeVagaTotal(quantidadeVagaTotal);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long quantidadeVagaTotal;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_agenda_ppi"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: qt_vaga_total
	 */
	public java.lang.Long getQuantidadeVagaTotal () {
		return getPropertyValue(this, quantidadeVagaTotal, PROP_QUANTIDADE_VAGA_TOTAL); 
	}

	/**
	 * Set the value related to the column: qt_vaga_total
	 * @param quantidadeVagaTotal the qt_vaga_total value
	 */
	public void setQuantidadeVagaTotal (java.lang.Long quantidadeVagaTotal) {
//        java.lang.Long quantidadeVagaTotalOld = this.quantidadeVagaTotal;
		this.quantidadeVagaTotal = quantidadeVagaTotal;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidadeVagaTotal", quantidadeVagaTotalOld, quantidadeVagaTotal);
	}



	/**
	 * Return the value associated with the column: empresa_ppi
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa_ppi
	 * @param empresa the empresa_ppi value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.agendamento.AgendaPpiVaga)) return false;
		else {
			br.com.ksisolucoes.vo.agendamento.AgendaPpiVaga agendaPpiVaga = (br.com.ksisolucoes.vo.agendamento.AgendaPpiVaga) obj;
			if (null == this.getCodigo() || null == agendaPpiVaga.getCodigo()) return false;
			else return (this.getCodigo().equals(agendaPpiVaga.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}