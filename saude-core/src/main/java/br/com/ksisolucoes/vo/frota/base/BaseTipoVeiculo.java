package br.com.ksisolucoes.vo.frota.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the tipo_veiculo table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="tipo_veiculo"
 */

public abstract class BaseTipoVeiculo extends BaseRootVO implements Serializable {

	public static String REF = "TipoVeiculo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REFERENCIA = "referencia";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_FLAG_TRANSPORTE_PACIENTE = "flagTransportePaciente";


	// constructors
	public BaseTipoVeiculo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseTipoVeiculo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseTipoVeiculo (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.Long flagTransportePaciente) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setFlagTransportePaciente(flagTransportePaciente);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String referencia;
	private java.lang.String descricao;
	private java.lang.Long flagTransportePaciente;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_tp_veiculo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: referencia
	 */
	public java.lang.String getReferencia () {
		return getPropertyValue(this, referencia, PROP_REFERENCIA); 
	}

	/**
	 * Set the value related to the column: referencia
	 * @param referencia the referencia value
	 */
	public void setReferencia (java.lang.String referencia) {
//        java.lang.String referenciaOld = this.referencia;
		this.referencia = referencia;
//        this.getPropertyChangeSupport().firePropertyChange ("referencia", referenciaOld, referencia);
	}



	/**
	 * Return the value associated with the column: ds_tp_veiculo
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_tp_veiculo
	 * @param descricao the ds_tp_veiculo value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: flag_trans_paci
	 */
	public java.lang.Long getFlagTransportePaciente () {
		return getPropertyValue(this, flagTransportePaciente, PROP_FLAG_TRANSPORTE_PACIENTE); 
	}

	/**
	 * Set the value related to the column: flag_trans_paci
	 * @param flagTransportePaciente the flag_trans_paci value
	 */
	public void setFlagTransportePaciente (java.lang.Long flagTransportePaciente) {
//        java.lang.Long flagTransportePacienteOld = this.flagTransportePaciente;
		this.flagTransportePaciente = flagTransportePaciente;
//        this.getPropertyChangeSupport().firePropertyChange ("flagTransportePaciente", flagTransportePacienteOld, flagTransportePaciente);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.frota.TipoVeiculo)) return false;
		else {
			br.com.ksisolucoes.vo.frota.TipoVeiculo tipoVeiculo = (br.com.ksisolucoes.vo.frota.TipoVeiculo) obj;
			if (null == this.getCodigo() || null == tipoVeiculo.getCodigo()) return false;
			else return (this.getCodigo().equals(tipoVeiculo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}