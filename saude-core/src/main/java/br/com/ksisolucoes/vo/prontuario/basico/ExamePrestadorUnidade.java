package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExamePrestadorUnidade;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class ExamePrestadorUnidade extends BaseExamePrestadorUnidade implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ExamePrestadorUnidade () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ExamePrestadorUnidade (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ExamePrestadorUnidade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.basico.ExamePrestador examePrestador,
		br.com.ksisolucoes.vo.basico.Empresa empresa) {

		super (
			codigo,
			examePrestador,
			empresa);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}