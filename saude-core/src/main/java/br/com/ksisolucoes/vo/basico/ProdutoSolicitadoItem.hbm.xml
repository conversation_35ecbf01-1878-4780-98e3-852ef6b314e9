<?xml version="1.0"?> 
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class 
        name="ProdutoSolicitadoItem"
        table="produto_solicitado_item"
    >
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_produto_solicitado_item">
            <generator class="assigned"/>
        </id><version column="version" name="version" type="long" />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.basico.ProdutoSolicitado" 
            name="produtoSolicitado"
            not-null="true">
            <column name="cd_prod_solic"/>
        </many-to-one>
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            name="produto"
            not-null="true">
            <column name="cod_pro"/>
        </many-to-one>
	
        <property
            name="quantidadeMensal"
            column="qtdade_mensal"
            type="java.lang.Double"
            not-null="true"
        />
	
        <property
            name="dataCadastro"
            column="dt_cadastro"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            name="observacao"
            column="observacao"
            type="java.lang.String"
            length="250"
        />                  

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
            not-null="true">
            <column name="cd_usuario" />
        </many-to-one>
	
        <property
            name="status"
            column="status"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            name="dataCancelamento"
            column="dt_cancelamento"
            type="timestamp"
            not-null="false"
        />
        
        <property
            name="dataValidade"
            column="dt_validade"
            type="java.util.Date"
            not-null="false"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuarioCancelamento"
            not-null="false">
            <column name="cd_usu_cancelamento" />
        </many-to-one>    
    </class>
</hibernate-mapping>