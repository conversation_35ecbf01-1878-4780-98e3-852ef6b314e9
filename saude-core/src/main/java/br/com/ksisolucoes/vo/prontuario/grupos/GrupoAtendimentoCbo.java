package br.com.ksisolucoes.vo.prontuario.grupos;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.prontuario.grupos.base.BaseGrupoAtendimentoCbo;

public class GrupoAtendimentoCbo extends BaseGrupoAtendimentoCbo implements CodigoManager, PesquisaObjectInterface {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public GrupoAtendimentoCbo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public GrupoAtendimentoCbo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public GrupoAtendimentoCbo (
		java.lang.Long codigo,
		java.lang.Long tipoFiltroAtendimento,
		java.lang.Long filtrarMaterial) {

		super (
			codigo,
			tipoFiltroAtendimento,
			filtrarMaterial);
	}

    /*[CONSTRUCTOR MARKER END]*/
    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getDescricao();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    public static enum TipoFiltroAtendimento implements IEnum<TipoFiltroAtendimento> {

        PROFISSIONAL_SEM_PROFISSIONAL(2L, Bundle.getStringApplication("rotulo_profissional_sem_profissional")),
        PROFISSIONAL(1L, Bundle.getStringApplication("rotulo_profissional")),
        TODOS(0L, Bundle.getStringApplication("rotulo_todos"));
        private Long value;
        private String descricao;

        private TipoFiltroAtendimento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static TipoFiltroAtendimento valeuOf(Long value) {
            for (TipoFiltroAtendimento tipoFiltroAtendimento : TipoFiltroAtendimento.values()) {
                if (tipoFiltroAtendimento.value().equals(value)) {
                    return tipoFiltroAtendimento;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }

    public static enum Filtros implements IEnum<Filtros> {

        NENHUM(2L, Bundle.getStringApplication("rotulo_nenhum")),
        ALGUNS(1L, Bundle.getStringApplication("rotulo_alguns")),
        TODOS(0L, Bundle.getStringApplication("rotulo_todos"));

        private Long value;
        private String descricao;

        private Filtros(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Filtros valueOf(Long value) {
            for (Filtros filtro : Filtros.values()) {
                if (filtro.value().equals(value)) {
                    return filtro;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }

    }

    public static enum NivelAtendimento implements IEnum<NivelAtendimento> {

        ATENCAO_BASICA(0L, Bundle.getStringApplication("rotulo_atencao_basica_descricao")),
        ATENCAO_ESPECIALIZADA(1L, Bundle.getStringApplication("rotulo_atencao_especializada"));
        private Long value;
        private String descricao;

        private NivelAtendimento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static NivelAtendimento valeuOf(Long value) {
            for (NivelAtendimento nivelAtendimento : NivelAtendimento.values()) {
                if (nivelAtendimento.value().equals(value)) {
                    return nivelAtendimento;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }
}
