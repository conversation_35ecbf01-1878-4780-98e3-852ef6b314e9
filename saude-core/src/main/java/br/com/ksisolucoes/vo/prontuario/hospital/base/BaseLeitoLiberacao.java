package br.com.ksisolucoes.vo.prontuario.hospital.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the leito_liberacao table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="leito_liberacao"
 */

public abstract class BaseLeitoLiberacao extends BaseRootVO implements Serializable {

	public static String REF = "LeitoLiberacao";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_LEITO_QUARTO = "leitoQuarto";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_LIBERACAO = "dataLiberacao";
	public static final String PROP_MOTIVO = "motivo";
	public static final String PROP_TIPO_LIBERACAO = "tipoLiberacao";


	// constructors
	public BaseLeitoLiberacao () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseLeitoLiberacao (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseLeitoLiberacao (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataLiberacao,
		java.lang.Long tipoLiberacao) {

		this.setCodigo(codigo);
		this.setLeitoQuarto(leitoQuarto);
		this.setUsuario(usuario);
		this.setDataLiberacao(dataLiberacao);
		this.setTipoLiberacao(tipoLiberacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataLiberacao;
	private java.lang.Long tipoLiberacao;
	private java.lang.String motivo;

	// many to one
	private br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_leito_liberacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_liberacao
	 */
	public java.util.Date getDataLiberacao () {
		return getPropertyValue(this, dataLiberacao, PROP_DATA_LIBERACAO); 
	}

	/**
	 * Set the value related to the column: dt_liberacao
	 * @param dataLiberacao the dt_liberacao value
	 */
	public void setDataLiberacao (java.util.Date dataLiberacao) {
//        java.util.Date dataLiberacaoOld = this.dataLiberacao;
		this.dataLiberacao = dataLiberacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataLiberacao", dataLiberacaoOld, dataLiberacao);
	}



	/**
	 * Return the value associated with the column: tipo_liberacao
	 */
	public java.lang.Long getTipoLiberacao () {
		return getPropertyValue(this, tipoLiberacao, PROP_TIPO_LIBERACAO); 
	}

	/**
	 * Set the value related to the column: tipo_liberacao
	 * @param tipoLiberacao the tipo_liberacao value
	 */
	public void setTipoLiberacao (java.lang.Long tipoLiberacao) {
//        java.lang.Long tipoLiberacaoOld = this.tipoLiberacao;
		this.tipoLiberacao = tipoLiberacao;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoLiberacao", tipoLiberacaoOld, tipoLiberacao);
	}



	/**
	 * Return the value associated with the column: motivo
	 */
	public java.lang.String getMotivo () {
		return getPropertyValue(this, motivo, PROP_MOTIVO); 
	}

	/**
	 * Set the value related to the column: motivo
	 * @param motivo the motivo value
	 */
	public void setMotivo (java.lang.String motivo) {
//        java.lang.String motivoOld = this.motivo;
		this.motivo = motivo;
//        this.getPropertyChangeSupport().firePropertyChange ("motivo", motivoOld, motivo);
	}



	/**
	 * Return the value associated with the column: cd_leito
	 */
	public br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto getLeitoQuarto () {
		return getPropertyValue(this, leitoQuarto, PROP_LEITO_QUARTO); 
	}

	/**
	 * Set the value related to the column: cd_leito
	 * @param leitoQuarto the cd_leito value
	 */
	public void setLeitoQuarto (br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuarto) {
//        br.com.ksisolucoes.vo.prontuario.hospital.LeitoQuarto leitoQuartoOld = this.leitoQuarto;
		this.leitoQuarto = leitoQuarto;
//        this.getPropertyChangeSupport().firePropertyChange ("leitoQuarto", leitoQuartoOld, leitoQuarto);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.hospital.LeitoLiberacao)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.hospital.LeitoLiberacao leitoLiberacao = (br.com.ksisolucoes.vo.prontuario.hospital.LeitoLiberacao) obj;
			if (null == this.getCodigo() || null == leitoLiberacao.getCodigo()) return false;
			else return (this.getCodigo().equals(leitoLiberacao.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}