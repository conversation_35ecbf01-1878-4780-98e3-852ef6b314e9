package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the pessoa_endereco table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="pessoa_endereco"
 */

public abstract class BasePessoaEndereco extends BaseRootVO implements Serializable {

	public static String REF = "PessoaEndereco";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_ANO_FIXACAO = "anoFixacao";
	public static final String PROP_FAX = "fax";
	public static final String PROP_CEP = "cep";
	public static final String PROP_CELULAR = "celular";
	public static final String PROP_RO_PESSOA = "roPessoa";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_COMPLEMENTO = "complemento";
	public static final String PROP_CIDADE = "cidade";
	public static final String PROP_LOGRADOURO = "logradouro";
	public static final String PROP_NOME_LOGRADOURO = "nomeLogradouro";
	public static final String PROP_DDI = "ddi";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_EMAIL = "email";
	public static final String PROP_HOME_PAGE = "homePage";
	public static final String PROP_ID = "id";
	public static final String PROP_LOCALIDADE = "localidade";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_RO_TIPO_ENDERECO = "roTipoEndereco";
	public static final String PROP_BAIRRO = "bairro";
	public static final String PROP_DDD = "ddd";


	// constructors
	public BasePessoaEndereco () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BasePessoaEndereco (br.com.ksisolucoes.vo.basico.PessoaEnderecoPK id) {
		this.setId(id);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BasePessoaEndereco (
		br.com.ksisolucoes.vo.basico.PessoaEnderecoPK id,
		br.com.ksisolucoes.vo.basico.Cidade cidade,
		java.lang.String nomeLogradouro,
		java.lang.Long usuario,
		java.lang.String cep,
		java.util.Date dataUsuario) {

		this.setId(id);
		this.setCidade(cidade);
		this.setNomeLogradouro(nomeLogradouro);
		this.setUsuario(usuario);
		this.setCep(cep);
		this.setDataUsuario(dataUsuario);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private br.com.ksisolucoes.vo.basico.PessoaEnderecoPK id;

	// fields
	private java.lang.String bairro;
	private java.lang.String celular;
	private java.lang.String complemento;
	private java.lang.String logradouro;
	private java.lang.String fax;
	private java.lang.String nomeLogradouro;
	private java.lang.Long usuario;
	private java.lang.Long numero;
	private java.lang.String homePage;
	private java.lang.String cep;
	private java.lang.String ddd;
	private java.lang.String ddi;
	private java.lang.String email;
	private java.lang.String telefone;
	private java.util.Date dataUsuario;
	private java.lang.Long anoFixacao;

	// many to one
	private br.com.ksisolucoes.vo.basico.TipoEndereco roTipoEndereco;
	private br.com.ksisolucoes.vo.basico.Pessoa roPessoa;
	private br.com.ksisolucoes.vo.basico.Cidade cidade;
	private br.com.ksisolucoes.vo.basico.Localidade localidade;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     */
	public br.com.ksisolucoes.vo.basico.PessoaEnderecoPK getId () {
	    return getPropertyValue(this,  id, "id" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param id the new ID
	 */
	public void setId (br.com.ksisolucoes.vo.basico.PessoaEnderecoPK id) {
		this.id = id;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: bairro
	 */
	public java.lang.String getBairro () {
		return getPropertyValue(this, bairro, PROP_BAIRRO); 
	}

	/**
	 * Set the value related to the column: bairro
	 * @param bairro the bairro value
	 */
	public void setBairro (java.lang.String bairro) {
//        java.lang.String bairroOld = this.bairro;
		this.bairro = bairro;
//        this.getPropertyChangeSupport().firePropertyChange ("bairro", bairroOld, bairro);
	}



	/**
	 * Return the value associated with the column: celular
	 */
	public java.lang.String getCelular () {
		return getPropertyValue(this, celular, PROP_CELULAR); 
	}

	/**
	 * Set the value related to the column: celular
	 * @param celular the celular value
	 */
	public void setCelular (java.lang.String celular) {
//        java.lang.String celularOld = this.celular;
		this.celular = celular;
//        this.getPropertyChangeSupport().firePropertyChange ("celular", celularOld, celular);
	}



	/**
	 * Return the value associated with the column: complemento
	 */
	public java.lang.String getComplemento () {
		return getPropertyValue(this, complemento, PROP_COMPLEMENTO); 
	}

	/**
	 * Set the value related to the column: complemento
	 * @param complemento the complemento value
	 */
	public void setComplemento (java.lang.String complemento) {
//        java.lang.String complementoOld = this.complemento;
		this.complemento = complemento;
//        this.getPropertyChangeSupport().firePropertyChange ("complemento", complementoOld, complemento);
	}



	/**
	 * Return the value associated with the column: logradouro
	 */
	public java.lang.String getLogradouro () {
		return getPropertyValue(this, logradouro, PROP_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: logradouro
	 * @param logradouro the logradouro value
	 */
	public void setLogradouro (java.lang.String logradouro) {
//        java.lang.String logradouroOld = this.logradouro;
		this.logradouro = logradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("logradouro", logradouroOld, logradouro);
	}



	/**
	 * Return the value associated with the column: fax
	 */
	public java.lang.String getFax () {
		return getPropertyValue(this, fax, PROP_FAX); 
	}

	/**
	 * Set the value related to the column: fax
	 * @param fax the fax value
	 */
	public void setFax (java.lang.String fax) {
//        java.lang.String faxOld = this.fax;
		this.fax = fax;
//        this.getPropertyChangeSupport().firePropertyChange ("fax", faxOld, fax);
	}



	/**
	 * Return the value associated with the column: nome_logradouro
	 */
	public java.lang.String getNomeLogradouro () {
		return getPropertyValue(this, nomeLogradouro, PROP_NOME_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: nome_logradouro
	 * @param nomeLogradouro the nome_logradouro value
	 */
	public void setNomeLogradouro (java.lang.String nomeLogradouro) {
//        java.lang.String nomeLogradouroOld = this.nomeLogradouro;
		this.nomeLogradouro = nomeLogradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeLogradouro", nomeLogradouroOld, nomeLogradouro);
	}



	/**
	 * Return the value associated with the column: usuario
	 */
	public java.lang.Long getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: usuario
	 * @param usuario the usuario value
	 */
	public void setUsuario (java.lang.Long usuario) {
//        java.lang.Long usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: numero
	 */
	public java.lang.Long getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: numero
	 * @param numero the numero value
	 */
	public void setNumero (java.lang.Long numero) {
//        java.lang.Long numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: home_page
	 */
	public java.lang.String getHomePage () {
		return getPropertyValue(this, homePage, PROP_HOME_PAGE); 
	}

	/**
	 * Set the value related to the column: home_page
	 * @param homePage the home_page value
	 */
	public void setHomePage (java.lang.String homePage) {
//        java.lang.String homePageOld = this.homePage;
		this.homePage = homePage;
//        this.getPropertyChangeSupport().firePropertyChange ("homePage", homePageOld, homePage);
	}



	/**
	 * Return the value associated with the column: cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: cep
	 * @param cep the cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: ddd
	 */
	public java.lang.String getDdd () {
		return getPropertyValue(this, ddd, PROP_DDD); 
	}

	/**
	 * Set the value related to the column: ddd
	 * @param ddd the ddd value
	 */
	public void setDdd (java.lang.String ddd) {
//        java.lang.String dddOld = this.ddd;
		this.ddd = ddd;
//        this.getPropertyChangeSupport().firePropertyChange ("ddd", dddOld, ddd);
	}



	/**
	 * Return the value associated with the column: ddi
	 */
	public java.lang.String getDdi () {
		return getPropertyValue(this, ddi, PROP_DDI); 
	}

	/**
	 * Set the value related to the column: ddi
	 * @param ddi the ddi value
	 */
	public void setDdi (java.lang.String ddi) {
//        java.lang.String ddiOld = this.ddi;
		this.ddi = ddi;
//        this.getPropertyChangeSupport().firePropertyChange ("ddi", ddiOld, ddi);
	}



	/**
	 * Return the value associated with the column: email
	 */
	public java.lang.String getEmail () {
		return getPropertyValue(this, email, PROP_EMAIL); 
	}

	/**
	 * Set the value related to the column: email
	 * @param email the email value
	 */
	public void setEmail (java.lang.String email) {
//        java.lang.String emailOld = this.email;
		this.email = email;
//        this.getPropertyChangeSupport().firePropertyChange ("email", emailOld, email);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: ano_fixacao
	 */
	public java.lang.Long getAnoFixacao () {
		return getPropertyValue(this, anoFixacao, PROP_ANO_FIXACAO); 
	}

	/**
	 * Set the value related to the column: ano_fixacao
	 * @param anoFixacao the ano_fixacao value
	 */
	public void setAnoFixacao (java.lang.Long anoFixacao) {
//        java.lang.Long anoFixacaoOld = this.anoFixacao;
		this.anoFixacao = anoFixacao;
//        this.getPropertyChangeSupport().firePropertyChange ("anoFixacao", anoFixacaoOld, anoFixacao);
	}



	/**
	 * Return the value associated with the column: cod_tip_end
	 */
	public br.com.ksisolucoes.vo.basico.TipoEndereco getRoTipoEndereco () {
		return getPropertyValue(this, roTipoEndereco, PROP_RO_TIPO_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cod_tip_end
	 * @param roTipoEndereco the cod_tip_end value
	 */
	public void setRoTipoEndereco (br.com.ksisolucoes.vo.basico.TipoEndereco roTipoEndereco) {
//        br.com.ksisolucoes.vo.basico.TipoEndereco roTipoEnderecoOld = this.roTipoEndereco;
		this.roTipoEndereco = roTipoEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("roTipoEndereco", roTipoEnderecoOld, roTipoEndereco);
	}



	/**
	 * Return the value associated with the column: cod_pessoa
	 */
	public br.com.ksisolucoes.vo.basico.Pessoa getRoPessoa () {
		return getPropertyValue(this, roPessoa, PROP_RO_PESSOA); 
	}

	/**
	 * Set the value related to the column: cod_pessoa
	 * @param roPessoa the cod_pessoa value
	 */
	public void setRoPessoa (br.com.ksisolucoes.vo.basico.Pessoa roPessoa) {
//        br.com.ksisolucoes.vo.basico.Pessoa roPessoaOld = this.roPessoa;
		this.roPessoa = roPessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("roPessoa", roPessoaOld, roPessoa);
	}



	/**
	 * Return the value associated with the column: cod_cid
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidade () {
		return getPropertyValue(this, cidade, PROP_CIDADE); 
	}

	/**
	 * Set the value related to the column: cod_cid
	 * @param cidade the cod_cid value
	 */
	public void setCidade (br.com.ksisolucoes.vo.basico.Cidade cidade) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeOld = this.cidade;
		this.cidade = cidade;
//        this.getPropertyChangeSupport().firePropertyChange ("cidade", cidadeOld, cidade);
	}



	/**
	 * Return the value associated with the column: cod_loc
	 */
	public br.com.ksisolucoes.vo.basico.Localidade getLocalidade () {
		return getPropertyValue(this, localidade, PROP_LOCALIDADE); 
	}

	/**
	 * Set the value related to the column: cod_loc
	 * @param localidade the cod_loc value
	 */
	public void setLocalidade (br.com.ksisolucoes.vo.basico.Localidade localidade) {
//        br.com.ksisolucoes.vo.basico.Localidade localidadeOld = this.localidade;
		this.localidade = localidade;
//        this.getPropertyChangeSupport().firePropertyChange ("localidade", localidadeOld, localidade);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.PessoaEndereco)) return false;
		else {
			br.com.ksisolucoes.vo.basico.PessoaEndereco pessoaEndereco = (br.com.ksisolucoes.vo.basico.PessoaEndereco) obj;
			if (null == this.getId() || null == pessoaEndereco.getId()) return false;
			else return (this.getId().equals(pessoaEndereco.getId()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getId()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getId().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}