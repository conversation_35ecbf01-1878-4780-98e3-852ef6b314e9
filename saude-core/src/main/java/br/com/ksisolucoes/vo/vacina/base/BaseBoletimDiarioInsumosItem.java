package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the boletim_diario_insumos_item table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="boletim_diario_insumos_item"
 */

public abstract class BaseBoletimDiarioInsumosItem extends BaseRootVO implements Serializable {

	public static String REF = "BoletimDiarioInsumosItem";
	public static final String PROP_VACINA_APLICACAO_INSUMO = "vacinaAplicacaoInsumo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_BOLETIM_DIARIO_INSUMOS = "boletimDiarioInsumos";


	// constructors
	public BaseBoletimDiarioInsumosItem () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseBoletimDiarioInsumosItem (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseBoletimDiarioInsumosItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumos boletimDiarioInsumos,
		br.com.ksisolucoes.vo.vacina.VacinaAplicacaoInsumo vacinaAplicacaoInsumo,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setBoletimDiarioInsumos(boletimDiarioInsumos);
		this.setVacinaAplicacaoInsumo(vacinaAplicacaoInsumo);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumos boletimDiarioInsumos;
	private br.com.ksisolucoes.vo.vacina.VacinaAplicacaoInsumo vacinaAplicacaoInsumo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_boletim_diario_insumos_item"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_boletim_diario_insumos
	 */
	public br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumos getBoletimDiarioInsumos () {
		return getPropertyValue(this, boletimDiarioInsumos, PROP_BOLETIM_DIARIO_INSUMOS); 
	}

	/**
	 * Set the value related to the column: cd_boletim_diario_insumos
	 * @param boletimDiarioInsumos the cd_boletim_diario_insumos value
	 */
	public void setBoletimDiarioInsumos (br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumos boletimDiarioInsumos) {
//        br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumos boletimDiarioInsumosOld = this.boletimDiarioInsumos;
		this.boletimDiarioInsumos = boletimDiarioInsumos;
//        this.getPropertyChangeSupport().firePropertyChange ("boletimDiarioInsumos", boletimDiarioInsumosOld, boletimDiarioInsumos);
	}



	/**
	 * Return the value associated with the column: cd_vac_aplicacao_insumo
	 */
	public br.com.ksisolucoes.vo.vacina.VacinaAplicacaoInsumo getVacinaAplicacaoInsumo () {
		return getPropertyValue(this, vacinaAplicacaoInsumo, PROP_VACINA_APLICACAO_INSUMO); 
	}

	/**
	 * Set the value related to the column: cd_vac_aplicacao_insumo
	 * @param vacinaAplicacaoInsumo the cd_vac_aplicacao_insumo value
	 */
	public void setVacinaAplicacaoInsumo (br.com.ksisolucoes.vo.vacina.VacinaAplicacaoInsumo vacinaAplicacaoInsumo) {
//        br.com.ksisolucoes.vo.vacina.VacinaAplicacaoInsumo vacinaAplicacaoInsumoOld = this.vacinaAplicacaoInsumo;
		this.vacinaAplicacaoInsumo = vacinaAplicacaoInsumo;
//        this.getPropertyChangeSupport().firePropertyChange ("vacinaAplicacaoInsumo", vacinaAplicacaoInsumoOld, vacinaAplicacaoInsumo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumosItem)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumosItem boletimDiarioInsumosItem = (br.com.ksisolucoes.vo.vacina.BoletimDiarioInsumosItem) obj;
			if (null == this.getCodigo() || null == boletimDiarioInsumosItem.getCodigo()) return false;
			else return (this.getCodigo().equals(boletimDiarioInsumosItem.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}