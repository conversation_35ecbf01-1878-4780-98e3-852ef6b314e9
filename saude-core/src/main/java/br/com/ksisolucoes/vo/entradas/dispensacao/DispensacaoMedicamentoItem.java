package br.com.ksisolucoes.vo.entradas.dispensacao;

import br.com.celk.util.Coalesce;
import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.entradas.dispensacao.base.BaseDispensacaoMedicamentoItem;
import br.com.ksisolucoes.vo.entradas.estoque.CodigoBarrasProduto;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoGrupoEstoqueItemDTO;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItemKit;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DispensacaoMedicamentoItem extends BaseDispensacaoMedicamentoItem implements CodigoManager {

    private static final long serialVersionUID = 1L;

    public static final String PROP_SALDO = "saldo";
    public static final String PROP_SALDO_VISUALIZACAO = "saldoVisualizacao";
    public static final String PROP_QUANTIDADE_DISPENSAR = "quantidadeDispensar";
    public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
    public static final String PROP_DESCRICAO_STATUS_VALIDADE = "descricaoStatusValidade";
    public static final String PROP_DIAS_PROXIMA_DISPENSACAO = "diasProximaDispensacao";
    public static final String PROP_DESCRICAO_LOTE = "descricaoLote";
    public static final String PROP_POSOLOGIA_FORMATADO = "posologiaFormatado";
    public static final String PROP_STATUS_FORMATADO = "statusFormatado";
    public static final String PROP_DATA_PROXIMA_DISPENSACAO_CALCULADA = "dataProximaDispensacaoCalculada";

    public static final Long STATUS_NORMAL = 0L;
    public static final Long STATUS_SEM_ESTOQUE = 1L;
    public static final Long STATUS_DISPENSADO_PARCIALMENTE = 2L;
    public static final Long STATUS_NAO_DISPENSADO = 3L;

    public static final String DISPENSACAO_GOTAS = "G";
    public static final String DISPENSACAO_UI = "U";
    public static final String DISPENSACAO_ML = "D";
    public static final String DISPENSACAO_MCG = "M";
    public static final String DISPENSACAO_BISNAGA = "B";
    public static final String DISPENSACAO_POMADA = "P";
    public static final String DISPENSACAO_SPRAY = "S";
    public static final String DISPENSACAO_JATO = "J";

    public static final String DISPENSACAO_SEM_CONTROLE = "T";

    //VARIAVEL CRIADA PARA CONTROLE NO CADASTRO DE DispensacaoMedicamentoItem
    private boolean controleTabela = true;
    //VARIAVEL CRIADA PARA CONTROLE NO CADASTRO DE DispensacaoMedicamentoItem

    private List<MovimentoGrupoEstoqueItemDTO> movimentoGrupoEstoqueItemDTOList;
    private List<CodigoBarrasProduto> lstCodigoBarrasProduto;
    private ReceituarioItemKit historicoKit;
    private Long origem;

    public enum Origem {

        PRESCRICAO(0L),
        MEDICAMENTO_MATERIAL(1L),;

        private Origem(Long value) {
            this.value = value;
        }

        private final Long value;

        public Long getValue() {
            return value;
        }

    }

    public enum Tipo implements IEnum {

        ITEM(0L, Bundle.getStringApplication("rotulo_item")),
        COMPONENTE(1L, Bundle.getStringApplication("rotulo_componente_solucao")),
        KIT(2L, Bundle.getStringApplication("rotulo_item_kit"));

        private Tipo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        private final Long value;
        private final String descricao;

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public enum TipoUso implements IEnum<TipoUso> {

        DIA(0L, Bundle.getStringApplication("rotulo_dia")),
        SEMANA(1L, Bundle.getStringApplication("rotulo_semana")),
        MES(2L, Bundle.getStringApplication("rotulo_mes")),
        TRIMESTRE(3L, Bundle.getStringApplication("rotulo_trimestre")),
        ANUAL(4L, Bundle.getStringApplication("rotulo_anual"));

        private Long value;
        private String descricao;

        private TipoUso(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    public enum Frequencia implements IEnum<Frequencia> {

        DIARIO(0L, Bundle.getStringApplication("rotulo_diario")),
        HORA(1L, Bundle.getStringApplication("rotulo_hora")),
        SEMANAL(2L, Bundle.getStringApplication("rotulo_semanal")),
        MENSAL(3L, Bundle.getStringApplication("rotulo_mensal")),
        TRIMESTRAL(4L, Bundle.getStringApplication("rotulo_trimestral")),
        ANUAL(5L, Bundle.getStringApplication("rotulo_anual"));

        private Long value;
        private String descricao;

        private Frequencia(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }
    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public DispensacaoMedicamentoItem () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DispensacaoMedicamentoItem (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DispensacaoMedicamentoItem (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		java.lang.Double quantidadePrescrita,
		java.lang.Double quantidadeDispensada,
		java.lang.Double quantidadeDispensadaOriginal,
		java.lang.Long status,
		java.util.Date dataValidadeReceita) {

		super (
			codigo,
			produto,
			quantidadePrescrita,
			quantidadeDispensada,
			quantidadeDispensadaOriginal,
			status,
			dataValidadeReceita);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public boolean isControleTabela() {
        return controleTabela;
    }

    public void setControleTabela(boolean controleTabela) {
        this.controleTabela = controleTabela;
    }

//    public Produto getProdutoNovo() {
//        return produtoNovo;
//    }
//
//    public void setProdutoNovo(Produto produtoNovo) {
//        this.produtoNovo = produtoNovo;
//    }
    /**
     * Saldo do item de dispensao.<br>
     * QuantidadePrescrita - QuantidadeDispensada
     *
     * @return
     */
    public Double getSaldo() {
        return DispensacaoMedicamentoItemHelper.getSaldo(this);
    }

    /**
     *
     * @return
     */
    public Date getDataControleSaldo() {
        return DispensacaoMedicamentoItemHelper.getDataControleSaldo(this);
    }

    /**
     * Descrio do status do item.
     *
     * @return Descrio do status
     */
    public String getDescricaoStatus() {
        if(getStatus().equals(DispensacaoMedicamentoItem.STATUS_SEM_ESTOQUE)){
            return DispensacaoMedicamentoItemHelper.getDescricaoStatus(this.getStatus());
        }else {
            AtribuirZeroSeQuantidadeIgualNulo();
            final double resultado = subtrairQuantidadeDispensadaDaPrescrita();
            definirStatusDispensacao(resultado);
            return DispensacaoMedicamentoItemHelper.getDescricaoStatus(this.getStatus());
        }
    }

    private void AtribuirZeroSeQuantidadeIgualNulo() {
        if (this.getQuantidadePrescrita() == null) {
            this.setQuantidadePrescrita(0D);
        }
        if (this.getQuantidadeDispensada() == null) {
            this.setQuantidadeDispensada(0D);
        }
    }

    private double subtrairQuantidadeDispensadaDaPrescrita() {
        return this.getQuantidadePrescrita() - this.getQuantidadeDispensada();
    }

    private void definirStatusDispensacao(double quantidade) {
        if (quantidade == 0d) {
            this.setStatus(STATUS_NORMAL);
        }
        if (dispensadaParcialmente(quantidade)) {
            this.setStatus(STATUS_DISPENSADO_PARCIALMENTE);
        }
        if (naoDispensada(quantidade)) {
            this.setStatus(STATUS_NAO_DISPENSADO);
        }
    }

    private boolean dispensadaParcialmente(double quantidade) {
        return quantidade > 0 && quantidade < this.getQuantidadePrescrita();
    }

    private boolean naoDispensada(double quantidade) {
        return quantidade == this.getQuantidadePrescrita();
    }

    /**
     * Retorna os dias restantes para a prxima dispensao com base no item
     * informado.
     *
     * @return qtd de dias para proxima dispensacao
     */
    public Long getDiasProximaDispensacao() {
        return DispensacaoMedicamentoItemHelper.getDiasProximaDispensacao(this);
    }

    /**
     * Holds value of property quantidadeDispensar.
     */
    private Double quantidadeDispensar;

    /**
     * Getter for property quantidadeDispensar.
     *
     * @return Value of property quantidadeDispensar.
     */
    public Double getQuantidadeDispensar() {
        return Coalesce.asDouble(this.quantidadeDispensar);
    }

    /**
     * Setter for property quantidadeDispensar.
     *
     * @param quantidadeDispensar New value of property quantidadeDispensar.
     */
    public void setQuantidadeDispensar(Double quantidadeDispensar) {
        this.quantidadeDispensar = quantidadeDispensar;
    }

    public boolean isTipoReceitaBasico() {
        return DispensacaoMedicamentoItemHelper.isTipoReceitaBasico(this);
    }

    public Double getCoalesceQuantidadeDispensada() {
        return Coalesce.asDouble(super.getQuantidadeDispensada());
    }

    public List<MovimentoGrupoEstoqueItemDTO> getMovimentoGrupoEstoqueItemDTOList() {
        return movimentoGrupoEstoqueItemDTOList;
    }

    public void setMovimentoGrupoEstoqueItemDTOList(List<MovimentoGrupoEstoqueItemDTO> MovimentoGrupoEstoqueItemDTOList) {
        this.movimentoGrupoEstoqueItemDTOList = MovimentoGrupoEstoqueItemDTOList;
    }

    public List<CodigoBarrasProduto> getLstCodigoBarrasProduto() {
        if (lstCodigoBarrasProduto == null) {
            lstCodigoBarrasProduto = new ArrayList<CodigoBarrasProduto>();
        }
        return lstCodigoBarrasProduto;
    }

    public void setLstCodigoBarrasProduto(List<CodigoBarrasProduto> lstCodigoBarrasProduto) {
        this.lstCodigoBarrasProduto = lstCodigoBarrasProduto;
    }

    public String getDescricaoLote() {
        String descricaoLote = "";
        if (CollectionUtils.isNotNullEmpty(getMovimentoGrupoEstoqueItemDTOList())) {
            for (int i = 0; i < getMovimentoGrupoEstoqueItemDTOList().size(); i++) {
                MovimentoGrupoEstoqueItemDTO dto = getMovimentoGrupoEstoqueItemDTOList().get(i);

                descricaoLote += dto.getGrupoEstoque();
                if (i + 1 < getMovimentoGrupoEstoqueItemDTOList().size()) {
                    descricaoLote += " - ";
                }
            }
        }
        return descricaoLote;
    }

    public ReceituarioItemKit getHistoricoKit() {
        return historicoKit;
    }

    public void setHistoricoKit(ReceituarioItemKit historicoKit) {
        this.historicoKit = historicoKit;
    }

    public Long getOrigem() {
        return origem;
    }

    public void setOrigem(Long origem) {
        this.origem = origem;
    }

    public String getPosologiaFormatado() {
        if(getProduto() != null) {
            return DispensacaoMedicamentoItemHelper
                    .getDescricaoPosologiaFormatado(getPosologia(), getTipoUso(), getProduto().getFlagDispensacaoEspecial());
        }
        return "";
    }

    public Double getSaldoDevolucaoDisponivel() {
        return br.com.celk.util.Coalesce.asDouble(getQuantidadeDispensada()) - br.com.celk.util.Coalesce.asDouble(getQuantidadeDevolvida());
    }

    public String getDescricaoProduto() {
        String descricao = null;
        if (getReceituarioItem() != null) {
            descricao = getReceituarioItem().getDescricaoProdutoFormatadoOuNomeProduto();
        } else if (getProduto() != null) {
            descricao = getProduto().getDescricaoFormatado();
        }
        return descricao;
    }

    public String getStatusFormatado() {
        if (STATUS_NORMAL.equals(getStatus())) {
            return Bundle.getStringApplication("rotulo_normal");
        } else if (STATUS_SEM_ESTOQUE.equals(getStatus())) {
            return Bundle.getStringApplication("rotulo_sem_estoque");
        }

        return "";
    }

    public Date getDataProximaDispensacaoCalculada() {
        return DispensacaoMedicamentoItemHelper.calculaDataProximaDispensacao(this);
    }
}
