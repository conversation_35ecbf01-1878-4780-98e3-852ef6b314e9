package br.com.ksisolucoes.vo.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the almoxarifado_empresas table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="almoxarifado_empresas"
 */

public abstract class BaseAlmoxarifadoEmpresas extends BaseRootVO implements Serializable {

	public static String REF = "AlmoxarifadoEmpresas";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_ALMOXARIFADO = "almoxarifado";


	// constructors
	public BaseAlmoxarifadoEmpresas () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAlmoxarifadoEmpresas (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa almoxarifado;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_almoxarifado_empresa"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: almoxarifado
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getAlmoxarifado () {
		return getPropertyValue(this, almoxarifado, PROP_ALMOXARIFADO); 
	}

	/**
	 * Set the value related to the column: almoxarifado
	 * @param almoxarifado the almoxarifado value
	 */
	public void setAlmoxarifado (br.com.ksisolucoes.vo.basico.Empresa almoxarifado) {
//        br.com.ksisolucoes.vo.basico.Empresa almoxarifadoOld = this.almoxarifado;
		this.almoxarifado = almoxarifado;
//        this.getPropertyChangeSupport().firePropertyChange ("almoxarifado", almoxarifadoOld, almoxarifado);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.AlmoxarifadoEmpresas)) return false;
		else {
			br.com.ksisolucoes.vo.basico.AlmoxarifadoEmpresas almoxarifadoEmpresas = (br.com.ksisolucoes.vo.basico.AlmoxarifadoEmpresas) obj;
			if (null == this.getCodigo() || null == almoxarifadoEmpresas.getCodigo()) return false;
			else return (this.getCodigo().equals(almoxarifadoEmpresas.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}