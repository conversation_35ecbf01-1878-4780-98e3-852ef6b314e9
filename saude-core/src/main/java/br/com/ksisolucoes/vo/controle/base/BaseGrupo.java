package br.com.ksisolucoes.vo.controle.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the grupos table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="grupos"
 */

public abstract class BaseGrupo extends BaseRootVO implements Serializable {

	public static String REF = "Grupo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_UTILIDADE = "utilidade";
	public static final String PROP_NOME = "nome";


	// constructors
	public BaseGrupo () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseGrupo (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nome;
	private java.lang.String utilidade;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_grupo"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nm_grupo
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nm_grupo
	 * @param nome the nm_grupo value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: utilidade
	 */
	public java.lang.String getUtilidade () {
		return getPropertyValue(this, utilidade, PROP_UTILIDADE); 
	}

	/**
	 * Set the value related to the column: utilidade
	 * @param utilidade the utilidade value
	 */
	public void setUtilidade (java.lang.String utilidade) {
//        java.lang.String utilidadeOld = this.utilidade;
		this.utilidade = utilidade;
//        this.getPropertyChangeSupport().firePropertyChange ("utilidade", utilidadeOld, utilidade);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.controle.Grupo)) return false;
		else {
			br.com.ksisolucoes.vo.controle.Grupo grupo = (br.com.ksisolucoes.vo.controle.Grupo) obj;
			if (null == this.getCodigo() || null == grupo.getCodigo()) return false;
			else return (this.getCodigo().equals(grupo.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}