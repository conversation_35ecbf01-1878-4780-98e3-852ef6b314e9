package br.com.ksisolucoes.vo.vacina.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the aplicacao_vacina_campanha table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="aplicacao_vacina_campanha"
 */

public abstract class BaseAplicacaoVacinaCampanha extends BaseRootVO implements Serializable {

	public static String REF = "AplicacaoVacinaCampanha";
	public static final String PROP_STATUS = "status";
	public static final String PROP_USUARIO_CANCELAMENTO = "usuarioCancelamento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_TURNO = "turno";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_DATA_APLICACAO_FIM = "dataAplicacaoFim";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ESTRATEGIA = "estrategia";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_VACINA_CALENDARIO = "vacinaCalendario";
	public static final String PROP_DATA_CANCELAMENTO = "dataCancelamento";
	public static final String PROP_PRODUTO_VACINA = "produtoVacina";
	public static final String PROP_DATA_CONCLUSAO = "dataConclusao";
	public static final String PROP_LOTE = "lote";
	public static final String PROP_TIPO_VACINA = "tipoVacina";
	public static final String PROP_PROFISSIONAL_APLICACAO = "profissionalAplicacao";
	public static final String PROP_LOCAL_ATENDIMENTO = "localAtendimento";
	public static final String PROP_DATA_APLICACAO = "dataAplicacao";
	public static final String PROP_USUARIO_CONCLUSAO = "usuarioConclusao";


	// constructors
	public BaseAplicacaoVacinaCampanha () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAplicacaoVacinaCampanha (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAplicacaoVacinaCampanha (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioConclusao,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento,
		java.lang.Long status,
		java.util.Date dataCadastro,
		java.util.Date dataConclusao,
		java.util.Date dataCancelamento) {

		this.setCodigo(codigo);
		this.setTipoVacina(tipoVacina);
		this.setEmpresa(empresa);
		this.setUsuario(usuario);
		this.setUsuarioConclusao(usuarioConclusao);
		this.setUsuarioCancelamento(usuarioCancelamento);
		this.setStatus(status);
		this.setDataCadastro(dataCadastro);
		this.setDataConclusao(dataConclusao);
		this.setDataCancelamento(dataCancelamento);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long status;
	private java.util.Date dataAplicacao;
	private java.util.Date dataAplicacaoFim;
	private java.lang.Long localAtendimento;
	private java.lang.Long turno;
	private java.lang.String lote;
	private java.util.Date dataCadastro;
	private java.util.Date dataConclusao;
	private java.util.Date dataCancelamento;

	// many to one
	private br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina;
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalAplicacao;
	private br.com.ksisolucoes.vo.vacina.ProdutoVacina produtoVacina;
	private br.com.ksisolucoes.vo.vacina.Calendario estrategia;
	private br.com.ksisolucoes.vo.vacina.VacinaCalendario vacinaCalendario;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioConclusao;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_aplicacao_vacina_campanha"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: status
	 */
	public java.lang.Long getStatus () {
		return getPropertyValue(this, status, PROP_STATUS); 
	}

	/**
	 * Set the value related to the column: status
	 * @param status the status value
	 */
	public void setStatus (java.lang.Long status) {
//        java.lang.Long statusOld = this.status;
		this.status = status;
//        this.getPropertyChangeSupport().firePropertyChange ("status", statusOld, status);
	}



	/**
	 * Return the value associated with the column: dt_aplicacao
	 */
	public java.util.Date getDataAplicacao () {
		return getPropertyValue(this, dataAplicacao, PROP_DATA_APLICACAO); 
	}

	/**
	 * Set the value related to the column: dt_aplicacao
	 * @param dataAplicacao the dt_aplicacao value
	 */
	public void setDataAplicacao (java.util.Date dataAplicacao) {
//        java.util.Date dataAplicacaoOld = this.dataAplicacao;
		this.dataAplicacao = dataAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAplicacao", dataAplicacaoOld, dataAplicacao);
	}



	/**
	 * Return the value associated with the column: dt_aplicacao_fim
	 */
	public java.util.Date getDataAplicacaoFim () {
		return getPropertyValue(this, dataAplicacaoFim, PROP_DATA_APLICACAO_FIM); 
	}

	/**
	 * Set the value related to the column: dt_aplicacao_fim
	 * @param dataAplicacaoFim the dt_aplicacao_fim value
	 */
	public void setDataAplicacaoFim (java.util.Date dataAplicacaoFim) {
//        java.util.Date dataAplicacaoFimOld = this.dataAplicacaoFim;
		this.dataAplicacaoFim = dataAplicacaoFim;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAplicacaoFim", dataAplicacaoFimOld, dataAplicacaoFim);
	}



	/**
	 * Return the value associated with the column: local_atendimento
	 */
	public java.lang.Long getLocalAtendimento () {
		return getPropertyValue(this, localAtendimento, PROP_LOCAL_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: local_atendimento
	 * @param localAtendimento the local_atendimento value
	 */
	public void setLocalAtendimento (java.lang.Long localAtendimento) {
//        java.lang.Long localAtendimentoOld = this.localAtendimento;
		this.localAtendimento = localAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("localAtendimento", localAtendimentoOld, localAtendimento);
	}



	/**
	 * Return the value associated with the column: turno
	 */
	public java.lang.Long getTurno () {
		return getPropertyValue(this, turno, PROP_TURNO); 
	}

	/**
	 * Set the value related to the column: turno
	 * @param turno the turno value
	 */
	public void setTurno (java.lang.Long turno) {
//        java.lang.Long turnoOld = this.turno;
		this.turno = turno;
//        this.getPropertyChangeSupport().firePropertyChange ("turno", turnoOld, turno);
	}



	/**
	 * Return the value associated with the column: lote
	 */
	public java.lang.String getLote () {
		return getPropertyValue(this, lote, PROP_LOTE); 
	}

	/**
	 * Set the value related to the column: lote
	 * @param lote the lote value
	 */
	public void setLote (java.lang.String lote) {
//        java.lang.String loteOld = this.lote;
		this.lote = lote;
//        this.getPropertyChangeSupport().firePropertyChange ("lote", loteOld, lote);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_conclusao
	 */
	public java.util.Date getDataConclusao () {
		return getPropertyValue(this, dataConclusao, PROP_DATA_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: dt_conclusao
	 * @param dataConclusao the dt_conclusao value
	 */
	public void setDataConclusao (java.util.Date dataConclusao) {
//        java.util.Date dataConclusaoOld = this.dataConclusao;
		this.dataConclusao = dataConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConclusao", dataConclusaoOld, dataConclusao);
	}



	/**
	 * Return the value associated with the column: dt_cancelamento
	 */
	public java.util.Date getDataCancelamento () {
		return getPropertyValue(this, dataCancelamento, PROP_DATA_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_cancelamento
	 * @param dataCancelamento the dt_cancelamento value
	 */
	public void setDataCancelamento (java.util.Date dataCancelamento) {
//        java.util.Date dataCancelamentoOld = this.dataCancelamento;
		this.dataCancelamento = dataCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCancelamento", dataCancelamentoOld, dataCancelamento);
	}



	/**
	 * Return the value associated with the column: cd_vacina
	 */
	public br.com.ksisolucoes.vo.vacina.TipoVacina getTipoVacina () {
		return getPropertyValue(this, tipoVacina, PROP_TIPO_VACINA); 
	}

	/**
	 * Set the value related to the column: cd_vacina
	 * @param tipoVacina the cd_vacina value
	 */
	public void setTipoVacina (br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacina) {
//        br.com.ksisolucoes.vo.vacina.TipoVacina tipoVacinaOld = this.tipoVacina;
		this.tipoVacina = tipoVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoVacina", tipoVacinaOld, tipoVacina);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalAplicacao () {
		return getPropertyValue(this, profissionalAplicacao, PROP_PROFISSIONAL_APLICACAO); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissionalAplicacao the cd_profissional value
	 */
	public void setProfissionalAplicacao (br.com.ksisolucoes.vo.cadsus.Profissional profissionalAplicacao) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalAplicacaoOld = this.profissionalAplicacao;
		this.profissionalAplicacao = profissionalAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalAplicacao", profissionalAplicacaoOld, profissionalAplicacao);
	}



	/**
	 * Return the value associated with the column: cd_produto_vacina
	 */
	public br.com.ksisolucoes.vo.vacina.ProdutoVacina getProdutoVacina () {
		return getPropertyValue(this, produtoVacina, PROP_PRODUTO_VACINA); 
	}

	/**
	 * Set the value related to the column: cd_produto_vacina
	 * @param produtoVacina the cd_produto_vacina value
	 */
	public void setProdutoVacina (br.com.ksisolucoes.vo.vacina.ProdutoVacina produtoVacina) {
//        br.com.ksisolucoes.vo.vacina.ProdutoVacina produtoVacinaOld = this.produtoVacina;
		this.produtoVacina = produtoVacina;
//        this.getPropertyChangeSupport().firePropertyChange ("produtoVacina", produtoVacinaOld, produtoVacina);
	}



	/**
	 * Return the value associated with the column: cd_estrategia_calendario
	 */
	public br.com.ksisolucoes.vo.vacina.Calendario getEstrategia () {
		return getPropertyValue(this, estrategia, PROP_ESTRATEGIA); 
	}

	/**
	 * Set the value related to the column: cd_estrategia_calendario
	 * @param estrategia the cd_estrategia_calendario value
	 */
	public void setEstrategia (br.com.ksisolucoes.vo.vacina.Calendario estrategia) {
//        br.com.ksisolucoes.vo.vacina.Calendario estrategiaOld = this.estrategia;
		this.estrategia = estrategia;
//        this.getPropertyChangeSupport().firePropertyChange ("estrategia", estrategiaOld, estrategia);
	}



	/**
	 * Return the value associated with the column: cd_vacina_calendario
	 */
	public br.com.ksisolucoes.vo.vacina.VacinaCalendario getVacinaCalendario () {
		return getPropertyValue(this, vacinaCalendario, PROP_VACINA_CALENDARIO); 
	}

	/**
	 * Set the value related to the column: cd_vacina_calendario
	 * @param vacinaCalendario the cd_vacina_calendario value
	 */
	public void setVacinaCalendario (br.com.ksisolucoes.vo.vacina.VacinaCalendario vacinaCalendario) {
//        br.com.ksisolucoes.vo.vacina.VacinaCalendario vacinaCalendarioOld = this.vacinaCalendario;
		this.vacinaCalendario = vacinaCalendario;
//        this.getPropertyChangeSupport().firePropertyChange ("vacinaCalendario", vacinaCalendarioOld, vacinaCalendario);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_conclusao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioConclusao () {
		return getPropertyValue(this, usuarioConclusao, PROP_USUARIO_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_conclusao
	 * @param usuarioConclusao the cd_usuario_conclusao value
	 */
	public void setUsuarioConclusao (br.com.ksisolucoes.vo.controle.Usuario usuarioConclusao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioConclusaoOld = this.usuarioConclusao;
		this.usuarioConclusao = usuarioConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioConclusao", usuarioConclusaoOld, usuarioConclusao);
	}



	/**
	 * Return the value associated with the column: cd_usuario_cancelamento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioCancelamento () {
		return getPropertyValue(this, usuarioCancelamento, PROP_USUARIO_CANCELAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_cancelamento
	 * @param usuarioCancelamento the cd_usuario_cancelamento value
	 */
	public void setUsuarioCancelamento (br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioCancelamentoOld = this.usuarioCancelamento;
		this.usuarioCancelamento = usuarioCancelamento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCancelamento", usuarioCancelamentoOld, usuarioCancelamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha)) return false;
		else {
			br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha aplicacaoVacinaCampanha = (br.com.ksisolucoes.vo.vacina.AplicacaoVacinaCampanha) obj;
			if (null == this.getCodigo() || null == aplicacaoVacinaCampanha.getCodigo()) return false;
			else return (this.getCodigo().equals(aplicacaoVacinaCampanha.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}