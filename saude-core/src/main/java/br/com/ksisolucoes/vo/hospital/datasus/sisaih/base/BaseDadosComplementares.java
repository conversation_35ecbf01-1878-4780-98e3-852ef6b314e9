package br.com.ksisolucoes.vo.hospital.datasus.sisaih.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;

/**
 * This is an object that contains data related to the dados_complementares
 * table. Do not modify this class because it will be overwritten if the
 * configuration file related to this class is modified.
 *
 * @hibernate.class table="dados_complementares"
 */
public abstract class BaseDadosComplementares extends BaseRootVO implements Serializable {

    public static String REF = "DadosComplementares";
    public static final String PROP_CODIGO = "codigo";
    public static final String PROP_PROCEDIMENTO = "procedimento";
    public static final String PROP_TIPO_DADO = "tipoDado";

    // constructors
    public BaseDadosComplementares() {
        initialize();
    }

    /**
     * Constructor for primary key
     */
    public BaseDadosComplementares(java.lang.Long codigo) {
        this.setCodigo(codigo);
        initialize();
    }

    /**
     * Constructor for required fields
     */
    public BaseDadosComplementares(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento,
            java.lang.Long tipoDado) {

        this.setCodigo(codigo);
        this.setProcedimento(procedimento);
        this.setTipoDado(tipoDado);
        initialize();
    }

    protected void initialize() {
    }

    private int hashCode = Integer.MIN_VALUE;

    // primary key
    private java.lang.Long codigo;

    // fields
    private java.lang.Long tipoDado;

    // many to one
    private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;

    /**
     * Return the unique identifier of this class
     *
     * @hibernate.id generator-class="assigned" column="cd_dado_complementar"
     */
    public java.lang.Long getCodigo() {
        return getPropertyValue(this, codigo, "codigo");
    }

    /**
     * Set the unique identifier of this class
     *
     * @param codigo the new ID
     */
    public void setCodigo(java.lang.Long codigo) {
        this.codigo = codigo;
        this.hashCode = Integer.MIN_VALUE;
    }

    /**
     * Return the value associated with the column: tipo_dado
     */
    public java.lang.Long getTipoDado() {
        return getPropertyValue(this, tipoDado, PROP_TIPO_DADO);
    }

    /**
     * Set the value related to the column: tipo_dado
     *
     * @param tipoDado the tipo_dado value
     */
    public void setTipoDado(java.lang.Long tipoDado) {
//        java.lang.Long tipoDadoOld = this.tipoDado;
        this.tipoDado = tipoDado;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDado", tipoDadoOld, tipoDado);
    }

    /**
     * Return the value associated with the column: cd_procedimento
     */
    public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento() {
        return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO);
    }

    /**
     * Set the value related to the column: cd_procedimento
     *
     * @param procedimento the cd_procedimento value
     */
    public void setProcedimento(br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
        this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
    }

    public boolean equals(Object obj) {
        if (null == obj) {
            return false;
        }
        if (!(obj instanceof br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementares)) {
            return false;
        } else {
            br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementares dadosComplementares = (br.com.ksisolucoes.vo.hospital.datasus.sisaih.DadosComplementares) obj;
            if (null == this.getCodigo() || null == dadosComplementares.getCodigo()) {
                return false;
            } else {
                return (this.getCodigo().equals(dadosComplementares.getCodigo()));
            }
        }
    }

    public int hashCode() {
        if (Integer.MIN_VALUE == this.hashCode) {
            if (null == this.getCodigo()) {
                return super.hashCode();
            } else {
                String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
                this.hashCode = hashStr.hashCode();
            }
        }
        return this.hashCode;
    }

    public String toString() {
        return super.toString();
    }

    private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
        if (this.retornoValidacao == null) {
            this.retornoValidacao = new RetornoValidacao();
        }
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
        this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}
