<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento"  >
    <class
            name="AgendaGradeReservaOcorrencia"
            table="agenda_grade_reserva_ocor"
    >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_agenda_grade_reserva_ocor"
        >
            <generator class="sequence">
                <param name="sequence">seq_gem</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.agendamento.AgendaGradeReserva"
                name="agendaGradeReserva"
                not-null="true"
        >
            <column name="cd_agenda_grade_reserva" />
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                name="usuarioCadastro"
        >
            <column name="cd_usuario" />
        </many-to-one>

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
        />

        <property
                name="motivo"
                column="motivo"
                type="java.lang.String"
                not-null="true"
        />

    </class>
</hibernate-mapping>
