<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.hospital"  >
    <class name="OcorrenciaContaPaciente" table="ocorrencia_conta_paciente" >
        <id
            column="cd_ocorrencia"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />    

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente" name="contaPaciente" not-null="true" >
           	<column name="cd_conta_paciente" />
        </many-to-one>

        <property 
            name="data"
            column="dt_ocorrencia"
            type="timestamp"
            not-null="true"
        />

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario" name="usuario" not-null="true">
            <column name="cd_usuario" />
        </many-to-one>

        <property
            column="ds_ocorrencia"
            name="descricao"
            not-null="true"
            type="java.lang.String"
        />

		<many-to-one class="br.com.ksisolucoes.vo.prontuario.hospital.MotivoOcorrenciaContaPaciente" name="motivoOcorrenciaContaPaciente" not-null="true" >
           	<column name="cd_motivo_ocorrencia" />
        </many-to-one>
    </class>
</hibernate-mapping>
