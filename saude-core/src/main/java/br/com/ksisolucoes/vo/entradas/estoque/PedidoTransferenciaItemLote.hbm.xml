<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque"  >
    <class name="PedidoTransferenciaItemLote" table="pedido_transferencia_item_lote">
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_pedido_item_lote"
        >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one
            class="PedidoTransferenciaItem"
            name="pedidoTransferenciaItem"
            not-null="true"
            column="cd_pedido_item"
        />
  
        <property
            column="lote"
            name="lote"
            type="java.lang.String"
            not-null="true"
        />
  
        <property
            column="qtdade"
            name="quantidade"
            type="java.lang.Double"
            not-null="true"
        />
       
       <property
            column="qtdade"
            name="quantidadeOriginal"
            type="java.lang.Double"
            insert="false"
            update="false"
        />
  
        <property
            column="qtdade_recebida"
            name="quantidadeRecebida"
            type="java.lang.Double"
        />
        
        <property
            name="dataValidade"
            column="dt_validade"
            type="date"
            not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.LocalizacaoEstrutura"
                name="localizacaoEstrutura"
                column="cd_localizacao_estrutura"
                not-null="true"
        />

        <property
                column="justificativa_lote_posterior"
                length="200"
                name="justificativaLotePosterior"
                not-null="false"
                type="string"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.Fabricante"
                name="fabricante"
                column="cd_fabricante"
        />

    </class>
</hibernate-mapping>
