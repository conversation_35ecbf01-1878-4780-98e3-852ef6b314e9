package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseGrupoProblemasCondicoes;

import java.io.Serializable;


public class GrupoProblemasCondicoes extends BaseGrupoProblemasCondicoes implements CodigoManager {
	private static final long serialVersionUID = 1L;


	public static final Long SITUACAO_INATIVO = 99L;


	/*[CONSTRUCTOR MARKER BEGIN]*/
	public GrupoProblemasCondicoes () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public GrupoProblemasCondicoes (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public GrupoProblemasCondicoes (
		java.lang.Long codigo,
		java.lang.Long situacao) {

		super (
			codigo,
			situacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoProblemaCondicao() {
		if (getCiap() != null && getCiap().getDescricaoVO() != null) {
			return getCiap().getDescricaoVO();
		} else if (getCid() != null && getCid().getDescricao() != null) {
			return getCid().getDescricao();
		}
		return null;
	}

	public enum Situacao implements IEnum {
		ATIVO(1L, Bundle.getStringApplication("rotulo_ativo")),
		RESOLVIDO(2L, Bundle.getStringApplication("rotulo_resolvido")),
		LATENTE(3L, Bundle.getStringApplication("rotulo_latente"));

		private Long value;
		private String descricao;

		private Situacao(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		public static GrupoProblemasCondicoes.Situacao valeuOf(Long value) {
			for (GrupoProblemasCondicoes.Situacao situacao : GrupoProblemasCondicoes.Situacao.values()) {
				if (situacao.value().equals(value)) {
					return situacao;
				}
			}
			return null;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

	}

	public String getSituacaoFormatada() {
		if (Situacao.ATIVO.value().equals(getSituacao())) {
			return Bundle.getStringApplication("rotulo_ativo");
		} else if (Situacao.RESOLVIDO.value().equals(getSituacao())) {
			return Bundle.getStringApplication("rotulo_resolvido");
		} else if(Situacao.LATENTE.value().equals(getSituacao())) {
			return Bundle.getStringApplication("rotulo_latente");
		} else if (SITUACAO_INATIVO.equals(getSituacao())) {
			return Bundle.getStringApplication("rotulo_inativo");
		}
		return "";
	}
}