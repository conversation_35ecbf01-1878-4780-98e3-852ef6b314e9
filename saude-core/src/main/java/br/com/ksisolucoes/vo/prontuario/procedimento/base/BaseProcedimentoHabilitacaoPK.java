package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseProcedimentoHabilitacaoPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_PROCEDIMENTO_HABILITACAO_CADASTRO = "procedimentoHabilitacaoCadastro";
	public static String PROP_PROCEDIMENTO_GRUPO_HABILITACAO = "procedimentoGrupoHabilitacao";
	public static String PROP_PROCEDIMENTO_COMPETENCIA = "procedimentoCompetencia";

	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoCadastro procedimentoHabilitacaoCadastro;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupoHabilitacao procedimentoGrupoHabilitacao;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia;


	public BaseProcedimentoHabilitacaoPK () {}
	
	public BaseProcedimentoHabilitacaoPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoCadastro procedimentoHabilitacaoCadastro,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupoHabilitacao procedimentoGrupoHabilitacao,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {

		this.setProcedimentoHabilitacaoCadastro(procedimentoHabilitacaoCadastro);
		this.setProcedimentoGrupoHabilitacao(procedimentoGrupoHabilitacao);
		this.setProcedimentoCompetencia(procedimentoCompetencia);
	}


	/**
	 * Return the value associated with the column: cd_habilitacao
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoCadastro getProcedimentoHabilitacaoCadastro () {
		return getPropertyValue(this, procedimentoHabilitacaoCadastro, PROP_PROCEDIMENTO_HABILITACAO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_habilitacao
	 * @param procedimentoHabilitacaoCadastro the cd_habilitacao value
	 */
	public void setProcedimentoHabilitacaoCadastro (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoCadastro procedimentoHabilitacaoCadastro) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoCadastro procedimentoHabilitacaoCadastroOld = this.procedimentoHabilitacaoCadastro;
		this.procedimentoHabilitacaoCadastro = procedimentoHabilitacaoCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoHabilitacaoCadastro", procedimentoHabilitacaoCadastroOld, procedimentoHabilitacaoCadastro);
	}



	/**
	 * Return the value associated with the column: cd_grupo_habilitacao
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupoHabilitacao getProcedimentoGrupoHabilitacao () {
		return getPropertyValue(this, procedimentoGrupoHabilitacao, PROP_PROCEDIMENTO_GRUPO_HABILITACAO); 
	}

	/**
	 * Set the value related to the column: cd_grupo_habilitacao
	 * @param procedimentoGrupoHabilitacao the cd_grupo_habilitacao value
	 */
	public void setProcedimentoGrupoHabilitacao (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupoHabilitacao procedimentoGrupoHabilitacao) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupoHabilitacao procedimentoGrupoHabilitacaoOld = this.procedimentoGrupoHabilitacao;
		this.procedimentoGrupoHabilitacao = procedimentoGrupoHabilitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoGrupoHabilitacao", procedimentoGrupoHabilitacaoOld, procedimentoGrupoHabilitacao);
	}



	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetencia () {
		return getPropertyValue(this, procedimentoCompetencia, PROP_PROCEDIMENTO_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param procedimentoCompetencia the dt_competencia value
	 */
	public void setProcedimentoCompetencia (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaOld = this.procedimentoCompetencia;
		this.procedimentoCompetencia = procedimentoCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetencia", procedimentoCompetenciaOld, procedimentoCompetencia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoPK)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoPK mObj = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoHabilitacaoPK) obj;
			if (null != this.getProcedimentoHabilitacaoCadastro() && null != mObj.getProcedimentoHabilitacaoCadastro()) {
				if (!this.getProcedimentoHabilitacaoCadastro().equals(mObj.getProcedimentoHabilitacaoCadastro())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getProcedimentoGrupoHabilitacao() && null != mObj.getProcedimentoGrupoHabilitacao()) {
				if (!this.getProcedimentoGrupoHabilitacao().equals(mObj.getProcedimentoGrupoHabilitacao())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getProcedimentoCompetencia() && null != mObj.getProcedimentoCompetencia()) {
				if (!this.getProcedimentoCompetencia().equals(mObj.getProcedimentoCompetencia())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getProcedimentoHabilitacaoCadastro()) {
				sb.append(this.getProcedimentoHabilitacaoCadastro().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getProcedimentoGrupoHabilitacao()) {
				sb.append(this.getProcedimentoGrupoHabilitacao().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getProcedimentoCompetencia()) {
				sb.append(this.getProcedimentoCompetencia().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}