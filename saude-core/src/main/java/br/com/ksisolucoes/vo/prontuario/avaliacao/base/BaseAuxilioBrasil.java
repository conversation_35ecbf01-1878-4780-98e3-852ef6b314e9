package br.com.ksisolucoes.vo.prontuario.avaliacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the auxilio_brasil table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="auxilio_brasil"
 */

public abstract class BaseAuxilioBrasil extends BaseRootVO implements Serializable {

	public static String REF = "AuxilioBrasil";
	public static final String PROP_TIPO_INTEGRANTE = "tipoIntegrante";
	public static final String PROP_SEMESTRE = "semestre";
	public static final String PROP_DATA_ATENDIMENTO = "dataAtendimento";
	public static final String PROP_ANO = "ano";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_ALTURA = "altura";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_GESTANTE = "gestante";
	public static final String PROP_MOTIVO_DE_NAO_VACINACAO = "motivoDeNaoVacinacao";
	public static final String PROP_DATA_PRE_NATAL = "dataPreNatal";
	public static final String PROP_MOTIVO_DE_NAO_REALIZACAO_DO_PRE_NATAL = "motivoDeNaoRealizacaoDoPreNatal";
	public static final String PROP_MOTIVO_DE_DADO_NUTRICIONAL_NAO_COLETADO = "motivoDeDadoNutricionalNaoColetado";
	public static final String PROP_USUARIO_CADSUS = "usuarioCadsus";
	public static final String PROP_PESO = "peso";
	public static final String PROP_MOTIVO_NAO_ACOMPANHAMENTO = "motivoNaoAcompanhamento";
	public static final String PROP_VACINA_EM_DIA = "vacinaEmDia";
	public static final String PROP_DUM = "dum";


	// constructors
	public BaseAuxilioBrasil () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAuxilioBrasil (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAuxilioBrasil (
		java.lang.Long codigo,
		java.lang.Long situacao,
		java.util.Date dataAtendimento,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setSituacao(situacao);
		this.setDataAtendimento(dataAtendimento);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long situacao;
	private java.util.Date dataAtendimento;
	private java.lang.Long gestante;
	private java.util.Date dataPreNatal;
	private java.util.Date dum;
	private java.lang.Long vacinaEmDia;
	private java.lang.Double peso;
	private java.lang.Double altura;
	private java.lang.String ano;
	private java.lang.String semestre;
	private java.util.Date dataCadastro;
	private java.lang.Long motivoNaoAcompanhamento;
	private java.lang.Long motivoDeDadoNutricionalNaoColetado;
	private java.lang.Long motivoDeNaoVacinacao;
	private java.lang.Long motivoDeNaoRealizacaoDoPreNatal;
	private java.lang.Long tipoIntegrante;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_auxilio_brasil"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: dt_atendimento
	 */
	public java.util.Date getDataAtendimento () {
		return getPropertyValue(this, dataAtendimento, PROP_DATA_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_atendimento
	 * @param dataAtendimento the dt_atendimento value
	 */
	public void setDataAtendimento (java.util.Date dataAtendimento) {
//        java.util.Date dataAtendimentoOld = this.dataAtendimento;
		this.dataAtendimento = dataAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataAtendimento", dataAtendimentoOld, dataAtendimento);
	}



	/**
	 * Return the value associated with the column: gestante
	 */
	public java.lang.Long getGestante () {
		return getPropertyValue(this, gestante, PROP_GESTANTE); 
	}

	/**
	 * Set the value related to the column: gestante
	 * @param gestante the gestante value
	 */
	public void setGestante (java.lang.Long gestante) {
//        java.lang.Long gestanteOld = this.gestante;
		this.gestante = gestante;
//        this.getPropertyChangeSupport().firePropertyChange ("gestante", gestanteOld, gestante);
	}



	/**
	 * Return the value associated with the column: dt_pre_natal
	 */
	public java.util.Date getDataPreNatal () {
		return getPropertyValue(this, dataPreNatal, PROP_DATA_PRE_NATAL); 
	}

	/**
	 * Set the value related to the column: dt_pre_natal
	 * @param dataPreNatal the dt_pre_natal value
	 */
	public void setDataPreNatal (java.util.Date dataPreNatal) {
//        java.util.Date dataPreNatalOld = this.dataPreNatal;
		this.dataPreNatal = dataPreNatal;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPreNatal", dataPreNatalOld, dataPreNatal);
	}



	/**
	 * Return the value associated with the column: dum
	 */
	public java.util.Date getDum () {
		return getPropertyValue(this, dum, PROP_DUM); 
	}

	/**
	 * Set the value related to the column: dum
	 * @param dum the dum value
	 */
	public void setDum (java.util.Date dum) {
//        java.util.Date dumOld = this.dum;
		this.dum = dum;
//        this.getPropertyChangeSupport().firePropertyChange ("dum", dumOld, dum);
	}



	/**
	 * Return the value associated with the column: vacina_em_dia
	 */
	public java.lang.Long getVacinaEmDia () {
		return getPropertyValue(this, vacinaEmDia, PROP_VACINA_EM_DIA); 
	}

	/**
	 * Set the value related to the column: vacina_em_dia
	 * @param vacinaEmDia the vacina_em_dia value
	 */
	public void setVacinaEmDia (java.lang.Long vacinaEmDia) {
//        java.lang.Long vacinaEmDiaOld = this.vacinaEmDia;
		this.vacinaEmDia = vacinaEmDia;
//        this.getPropertyChangeSupport().firePropertyChange ("vacinaEmDia", vacinaEmDiaOld, vacinaEmDia);
	}



	/**
	 * Return the value associated with the column: peso
	 */
	public java.lang.Double getPeso () {
		return getPropertyValue(this, peso, PROP_PESO); 
	}

	/**
	 * Set the value related to the column: peso
	 * @param peso the peso value
	 */
	public void setPeso (java.lang.Double peso) {
//        java.lang.Double pesoOld = this.peso;
		this.peso = peso;
//        this.getPropertyChangeSupport().firePropertyChange ("peso", pesoOld, peso);
	}



	/**
	 * Return the value associated with the column: altura
	 */
	public java.lang.Double getAltura () {
		return getPropertyValue(this, altura, PROP_ALTURA); 
	}

	/**
	 * Set the value related to the column: altura
	 * @param altura the altura value
	 */
	public void setAltura (java.lang.Double altura) {
//        java.lang.Double alturaOld = this.altura;
		this.altura = altura;
//        this.getPropertyChangeSupport().firePropertyChange ("altura", alturaOld, altura);
	}



	/**
	 * Return the value associated with the column: ano
	 */
	public java.lang.String getAno () {
		return getPropertyValue(this, ano, PROP_ANO); 
	}

	/**
	 * Set the value related to the column: ano
	 * @param ano the ano value
	 */
	public void setAno (java.lang.String ano) {
//        java.lang.String anoOld = this.ano;
		this.ano = ano;
//        this.getPropertyChangeSupport().firePropertyChange ("ano", anoOld, ano);
	}



	/**
	 * Return the value associated with the column: semestre
	 */
	public java.lang.String getSemestre () {
		return getPropertyValue(this, semestre, PROP_SEMESTRE); 
	}

	/**
	 * Set the value related to the column: semestre
	 * @param semestre the semestre value
	 */
	public void setSemestre (java.lang.String semestre) {
//        java.lang.String semestreOld = this.semestre;
		this.semestre = semestre;
//        this.getPropertyChangeSupport().firePropertyChange ("semestre", semestreOld, semestre);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: motivo_nao_acompanhamento
	 */
	public java.lang.Long getMotivoNaoAcompanhamento () {
		return getPropertyValue(this, motivoNaoAcompanhamento, PROP_MOTIVO_NAO_ACOMPANHAMENTO); 
	}

	/**
	 * Set the value related to the column: motivo_nao_acompanhamento
	 * @param motivoNaoAcompanhamento the motivo_nao_acompanhamento value
	 */
	public void setMotivoNaoAcompanhamento (java.lang.Long motivoNaoAcompanhamento) {
//        java.lang.Long motivoNaoAcompanhamentoOld = this.motivoNaoAcompanhamento;
		this.motivoNaoAcompanhamento = motivoNaoAcompanhamento;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoNaoAcompanhamento", motivoNaoAcompanhamentoOld, motivoNaoAcompanhamento);
	}



	/**
	 * Return the value associated with the column: motivo_de_dado_nutricional_nao_coletado
	 */
	public java.lang.Long getMotivoDeDadoNutricionalNaoColetado () {
		return getPropertyValue(this, motivoDeDadoNutricionalNaoColetado, PROP_MOTIVO_DE_DADO_NUTRICIONAL_NAO_COLETADO); 
	}

	/**
	 * Set the value related to the column: motivo_de_dado_nutricional_nao_coletado
	 * @param motivoDeDadoNutricionalNaoColetado the motivo_de_dado_nutricional_nao_coletado value
	 */
	public void setMotivoDeDadoNutricionalNaoColetado (java.lang.Long motivoDeDadoNutricionalNaoColetado) {
//        java.lang.Long motivoDeDadoNutricionalNaoColetadoOld = this.motivoDeDadoNutricionalNaoColetado;
		this.motivoDeDadoNutricionalNaoColetado = motivoDeDadoNutricionalNaoColetado;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoDeDadoNutricionalNaoColetado", motivoDeDadoNutricionalNaoColetadoOld, motivoDeDadoNutricionalNaoColetado);
	}



	/**
	 * Return the value associated with the column: motivo_de_nao_vacinacao
	 */
	public java.lang.Long getMotivoDeNaoVacinacao () {
		return getPropertyValue(this, motivoDeNaoVacinacao, PROP_MOTIVO_DE_NAO_VACINACAO); 
	}

	/**
	 * Set the value related to the column: motivo_de_nao_vacinacao
	 * @param motivoDeNaoVacinacao the motivo_de_nao_vacinacao value
	 */
	public void setMotivoDeNaoVacinacao (java.lang.Long motivoDeNaoVacinacao) {
//        java.lang.Long motivoDeNaoVacinacaoOld = this.motivoDeNaoVacinacao;
		this.motivoDeNaoVacinacao = motivoDeNaoVacinacao;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoDeNaoVacinacao", motivoDeNaoVacinacaoOld, motivoDeNaoVacinacao);
	}



	/**
	 * Return the value associated with the column: motivo_de_nao_realizacao_do_pre_natal
	 */
	public java.lang.Long getMotivoDeNaoRealizacaoDoPreNatal () {
		return getPropertyValue(this, motivoDeNaoRealizacaoDoPreNatal, PROP_MOTIVO_DE_NAO_REALIZACAO_DO_PRE_NATAL); 
	}

	/**
	 * Set the value related to the column: motivo_de_nao_realizacao_do_pre_natal
	 * @param motivoDeNaoRealizacaoDoPreNatal the motivo_de_nao_realizacao_do_pre_natal value
	 */
	public void setMotivoDeNaoRealizacaoDoPreNatal (java.lang.Long motivoDeNaoRealizacaoDoPreNatal) {
//        java.lang.Long motivoDeNaoRealizacaoDoPreNatalOld = this.motivoDeNaoRealizacaoDoPreNatal;
		this.motivoDeNaoRealizacaoDoPreNatal = motivoDeNaoRealizacaoDoPreNatal;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoDeNaoRealizacaoDoPreNatal", motivoDeNaoRealizacaoDoPreNatalOld, motivoDeNaoRealizacaoDoPreNatal);
	}



	/**
	 * Return the value associated with the column: tipo_integrante
	 */
	public java.lang.Long getTipoIntegrante () {
		return getPropertyValue(this, tipoIntegrante, PROP_TIPO_INTEGRANTE); 
	}

	/**
	 * Set the value related to the column: tipo_integrante
	 * @param tipoIntegrante the tipo_integrante value
	 */
	public void setTipoIntegrante (java.lang.Long tipoIntegrante) {
//        java.lang.Long tipoIntegranteOld = this.tipoIntegrante;
		this.tipoIntegrante = tipoIntegrante;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoIntegrante", tipoIntegranteOld, tipoIntegrante);
	}



	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadsus () {
		return getPropertyValue(this, usuarioCadsus, PROP_USUARIO_CADSUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadsus the cd_usu_cadsus value
	 */
	public void setUsuarioCadsus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadsusOld = this.usuarioCadsus;
		this.usuarioCadsus = usuarioCadsus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadsus", usuarioCadsusOld, usuarioCadsus);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.avaliacao.AuxilioBrasil)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.avaliacao.AuxilioBrasil auxilioBrasil = (br.com.ksisolucoes.vo.prontuario.avaliacao.AuxilioBrasil) obj;
			if (null == this.getCodigo() || null == auxilioBrasil.getCodigo()) return false;
			else return (this.getCodigo().equals(auxilioBrasil.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}