<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class
        name="InstalacaoFisica"
        table="instalacao_fisica"
        >

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_instalacao"
        >
                <generator class="assigned"/>
        </id> <version column="version" name="version" type="long" />

        <property
            name="descricao"
            column="ds_instalacao"
            type="java.lang.String"
            length="60"
            />

        <property
            name="tipo"
            column="tp_instalacao"
            type="java.lang.Long"
            />

        <many-to-one
            name="subTipoInstalacao"
            class="br.com.ksisolucoes.vo.basico.SubTipoInstalacao"
            >
            <column name="cd_subtipo"/>
        </many-to-one>

    </class>
</hibernate-mapping>
