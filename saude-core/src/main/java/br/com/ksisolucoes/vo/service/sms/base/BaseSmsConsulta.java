package br.com.ksisolucoes.vo.service.sms.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the sms_consulta table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="sms_consulta"
 */

public abstract class BaseSmsConsulta extends BaseRootVO implements Serializable {

	public static String REF = "SmsConsulta";
	public static final String PROP_DATA = "data";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_CODIGO_RETORNO = "codigoRetorno";
	public static final String PROP_SMS_MENSAGEM = "smsMensagem";
	public static final String PROP_MENSAGEM_RETORNO = "mensagemRetorno";


	// constructors
	public BaseSmsConsulta () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseSmsConsulta (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseSmsConsulta (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.service.sms.SmsMensagem smsMensagem,
		java.util.Date data) {

		this.setCodigo(codigo);
		this.setSmsMensagem(smsMensagem);
		this.setData(data);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date data;
	private java.lang.Long codigoRetorno;
	private java.lang.String mensagemRetorno;

	// many to one
	private br.com.ksisolucoes.vo.service.sms.SmsMensagem smsMensagem;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_sms_consulta"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: data
	 */
	public java.util.Date getData () {
		return getPropertyValue(this, data, PROP_DATA); 
	}

	/**
	 * Set the value related to the column: data
	 * @param data the data value
	 */
	public void setData (java.util.Date data) {
//        java.util.Date dataOld = this.data;
		this.data = data;
//        this.getPropertyChangeSupport().firePropertyChange ("data", dataOld, data);
	}



	/**
	 * Return the value associated with the column: cod_retorno
	 */
	public java.lang.Long getCodigoRetorno () {
		return getPropertyValue(this, codigoRetorno, PROP_CODIGO_RETORNO); 
	}

	/**
	 * Set the value related to the column: cod_retorno
	 * @param codigoRetorno the cod_retorno value
	 */
	public void setCodigoRetorno (java.lang.Long codigoRetorno) {
//        java.lang.Long codigoRetornoOld = this.codigoRetorno;
		this.codigoRetorno = codigoRetorno;
//        this.getPropertyChangeSupport().firePropertyChange ("codigoRetorno", codigoRetornoOld, codigoRetorno);
	}



	/**
	 * Return the value associated with the column: msg_retorno
	 */
	public java.lang.String getMensagemRetorno () {
		return getPropertyValue(this, mensagemRetorno, PROP_MENSAGEM_RETORNO); 
	}

	/**
	 * Set the value related to the column: msg_retorno
	 * @param mensagemRetorno the msg_retorno value
	 */
	public void setMensagemRetorno (java.lang.String mensagemRetorno) {
//        java.lang.String mensagemRetornoOld = this.mensagemRetorno;
		this.mensagemRetorno = mensagemRetorno;
//        this.getPropertyChangeSupport().firePropertyChange ("mensagemRetorno", mensagemRetornoOld, mensagemRetorno);
	}



	/**
	 * Return the value associated with the column: cd_sms_mensagem
	 */
	public br.com.ksisolucoes.vo.service.sms.SmsMensagem getSmsMensagem () {
		return getPropertyValue(this, smsMensagem, PROP_SMS_MENSAGEM); 
	}

	/**
	 * Set the value related to the column: cd_sms_mensagem
	 * @param smsMensagem the cd_sms_mensagem value
	 */
	public void setSmsMensagem (br.com.ksisolucoes.vo.service.sms.SmsMensagem smsMensagem) {
//        br.com.ksisolucoes.vo.service.sms.SmsMensagem smsMensagemOld = this.smsMensagem;
		this.smsMensagem = smsMensagem;
//        this.getPropertyChangeSupport().firePropertyChange ("smsMensagem", smsMensagemOld, smsMensagem);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.service.sms.SmsConsulta)) return false;
		else {
			br.com.ksisolucoes.vo.service.sms.SmsConsulta smsConsulta = (br.com.ksisolucoes.vo.service.sms.SmsConsulta) obj;
			if (null == this.getCodigo() || null == smsConsulta.getCodigo()) return false;
			else return (this.getCodigo().equals(smsConsulta.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}