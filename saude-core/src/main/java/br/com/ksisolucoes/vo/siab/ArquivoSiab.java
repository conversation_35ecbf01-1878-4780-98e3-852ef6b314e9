package br.com.ksisolucoes.vo.siab;

import java.io.Serializable;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.siab.base.BaseArquivoSiab;



public class ArquivoSiab extends BaseArquivoSiab implements CodigoManager {
	private static final long serialVersionUID = 1L;

        public static final String PROP_DESCRICAO_MES = "descricaoMes";
        
/*[CONSTRUCTOR MARKER BEGIN]*/
	public ArquivoSiab () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ArquivoSiab (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public ArquivoSiab (
		java.lang.Long codigo,
		java.lang.Long mes,
		java.lang.Long ano) {

		super (
			codigo,
			mes,
			ano);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoMes(){
        return Data.getDescricaoMes(getMes().intValue()-1);
    }
}