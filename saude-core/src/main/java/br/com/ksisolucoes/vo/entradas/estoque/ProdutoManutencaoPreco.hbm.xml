<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque">
    <class name="ProdutoManutencaoPreco" table="produto_manutencao_preco">

        <id
            column="cd_manutencao_preco"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />

		<many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresa"
            not-null="true"
            column="empresa"
            />

		<many-to-one
            class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
            name="produto"
            not-null="true"
            column="cod_pro"
            />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
            not-null="true"
            column="cd_usuario"
            />

        <property
            column="preco_custo_anterior"
            name="precoCustoAnterior"
            not-null="true"
            type="java.lang.Double"
            />

		<property
            column="preco_medio_anterior"
            name="precoMedioAnterior"
            not-null="true"
            type="java.lang.Double"
            />

		<property
            column="novo_preco"
            name="novoPreco"
            not-null="true"
            type="java.lang.Double"
            />

		<property
            column="dt_manutencao"
            name="dataManutencao"
            not-null="true"
            type="timestamp"
            />

		<property
            column="motivo"
            name="motivo"
            not-null="true"
            type="java.lang.String"
            length="150"
            />

	</class>
</hibernate-mapping>
