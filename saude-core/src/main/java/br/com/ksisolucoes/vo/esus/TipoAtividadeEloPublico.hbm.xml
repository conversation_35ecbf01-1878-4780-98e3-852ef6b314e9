<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.esus"  >
    <class name="TipoAtividadeEloPublico" table="tipo_atividade_elo_publico" >

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_tipo_elo_publico"
        >
            <generator class="assigned" />
        </id> 

        <version column="version" name="version" type="long" />

		<many-to-one
         	name="tipoAtividadePublico"
         	class="br.com.ksisolucoes.vo.esus.TipoAtividadePublico"
         >
         	<column name="cd_publico_alvo" />
        </many-to-one>

		<many-to-one
         	name="tipoAtividadeGrupo"
         	class="br.com.ksisolucoes.vo.atividadegrupo.TipoAtividadeGrupo"
         >
         	<column name="cd_tp_atv_grupo"/>
        </many-to-one>

        <property
                name="versionAll"
                column="version_all"
                type="java.lang.Long"
        />

    </class>
</hibernate-mapping>
