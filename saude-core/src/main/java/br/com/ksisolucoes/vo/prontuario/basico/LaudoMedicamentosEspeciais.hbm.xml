<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="LaudoMedicamentosEspeciais" table="laudo_medicamentos_especiais" >

        <id
            column="cd_laudo_medicamentos_especiais"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="assigned"/>
        </id>
         
        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
                name="atendimento"
        >
            <column name="nr_atendimento"/>
        </many-to-one>

        <many-to-one  
            class="br.com.ksisolucoes.vo.basico.Empresa"
            name="empresa"
            column="empresa"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            name="profissional"
        >
            <column name="cd_profissional" not-null="false"/>
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usu_cadastro"
            name="usuarioCadastro"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
                name="cid"
                not-null="true"
        >
            <column name="cd_cid"/>
        </many-to-one>

        <property
                name="anamnese"
                type="java.lang.String"
                column="anamnese"
                not-null="true"
        />

        <property
                name="pacienteRealizouTratamentoPreventivo"
                type="java.lang.Long"
                column="pac_realizou_trat_prev"
                not-null="true"
        />

        <property
                name="obsPacienteRealizouTratamentoPreventivo"
                type="java.lang.String"
                column="obs_pac_realizou_trat_prev"
                not-null="false"
        />

        <property
                name="pacienteConsideradoIncapaz"
                type="java.lang.Long"
                column="pac_considerado_incapaz"
                not-null="true"
        />

        <property
                name="nomeResponsavel"
                type="java.lang.String"
                column="nm_responsavel"
                not-null="false"
        />

        <property
                name="status"
                type="java.lang.Long"
                column="status"
                not-null="true"
        />

        <property
                name="dataCadastro"
                type="timestamp"
                column="dt_cadastro"
                not-null="true"
        />

        <property
                name="dataAlteracao"
                type="timestamp"
                column="dt_alteracao"
                not-null="true"
        />
         
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
        >
            <column name="cd_usuario" not-null="true"/>
        </many-to-one>

        <property
                name="peso"
                column="peso"
                not-null="false"
                type="java.lang.Double"
        />
        <property
                name="altura"
                column="altura"
                not-null="false"
                type="java.lang.Double"
        />
        
    </class>
</hibernate-mapping>
