package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseTipoExameUnidade;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class TipoExameUnidade extends BaseTipoExameUnidade implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public TipoExameUnidade () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public TipoExameUnidade (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public TipoExameUnidade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		br.com.ksisolucoes.vo.prontuario.basico.TipoExame tipoExame) {

		super (
			codigo,
			empresa,
			tipoExame);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}