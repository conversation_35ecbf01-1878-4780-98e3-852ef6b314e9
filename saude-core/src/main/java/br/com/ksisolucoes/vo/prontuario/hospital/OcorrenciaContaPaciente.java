package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseOcorrenciaContaPaciente;



public class OcorrenciaContaPaciente extends BaseOcorrenciaContaPaciente implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public OcorrenciaContaPaciente () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public OcorrenciaContaPaciente (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public OcorrenciaContaPaciente (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.hospital.ContaPaciente contaPaciente,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.prontuario.hospital.MotivoOcorrenciaContaPaciente motivoOcorrenciaContaPaciente,
		java.util.Date data,
		java.lang.String descricao) {

		super (
			codigo,
			contaPaciente,
			usuario,
			motivoOcorrenciaContaPaciente,
			data,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}