package br.com.ksisolucoes.vo.programasaude;

import java.io.Serializable;

import br.com.ksisolucoes.vo.programasaude.base.BasePreventivoOcorrencia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PreventivoOcorrencia extends BasePreventivoOcorrencia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PreventivoOcorrencia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PreventivoOcorrencia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public PreventivoOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.programasaude.Preventivo preventivo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataOcorrencia,
		java.lang.String descricaoOcorrencia,
		java.util.Date dataCadastro) {

		super (
			codigo,
			preventivo,
			usuarioCadastro,
			dataOcorrencia,
			descricaoOcorrencia,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}