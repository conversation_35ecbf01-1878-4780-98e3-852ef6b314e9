package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.basico.base.BaseAssistenteSocial;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



public class AssistenteSocial extends BaseAssistenteSocial implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

        public static final String PROP_DESCRICAO_FORMATADO = "descricaoFormatado";
/*[CONSTRUCTOR MARKER BEGIN]*/
	public AssistenteSocial () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AssistenteSocial (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AssistenteSocial (
		java.lang.Long codigo,
		java.lang.String nome) {

		super (
			codigo,
			nome);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    @Override
    public String getDescricaoVO() {
        return this.getNome();
    }

    @Override
    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    public String getDescricaoFormatado(){
        return Util.getDescricaoFormatado(getCodigo(), getNome());
    }
}