package br.com.ksisolucoes.vo.vigilancia;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.base.BaseEloRequerimentoVigilanciaSetorVigilancia;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EloRequerimentoVigilanciaSetorVigilancia extends BaseEloRequerimentoVigilanciaSetorVigilancia implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloRequerimentoVigilanciaSetorVigilancia () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloRequerimentoVigilanciaSetorVigilancia (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloRequerimentoVigilanciaSetorVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.SetorVigilancia setorVigilancia) {

		super (
			codigo,
			requerimentoVigilancia,
			setorVigilancia);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}