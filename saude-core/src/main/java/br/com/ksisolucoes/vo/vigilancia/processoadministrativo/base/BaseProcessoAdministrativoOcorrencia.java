package br.com.ksisolucoes.vo.vigilancia.processoadministrativo.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the processo_adm_ocorrencia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="processo_adm_ocorrencia"
 */

public abstract class BaseProcessoAdministrativoOcorrencia extends BaseRootVO implements Serializable {

	public static String REF = "ProcessoAdministrativoOcorrencia";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DOCUMENTO = "documento";
	public static final String PROP_PROCESSO_ADMINISTRATIVO = "processoAdministrativo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_DATA_OCORRENCIA = "dataOcorrencia";


	// constructors
	public BaseProcessoAdministrativoOcorrencia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProcessoAdministrativoOcorrencia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProcessoAdministrativoOcorrencia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo processoAdministrativo,
		java.util.Date dataOcorrencia,
		java.lang.String descricao,
		java.lang.Long tipo) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setProcessoAdministrativo(processoAdministrativo);
		this.setDataOcorrencia(dataOcorrencia);
		this.setDescricao(descricao);
		this.setTipo(tipo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.util.Date dataOcorrencia;
	private java.lang.String descricao;
	private java.lang.Long tipo;
	private java.lang.Long documento;

	// many to one
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo processoAdministrativo;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_processo_adm_ocorrencia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: dt_ocorrencia
	 */
	public java.util.Date getDataOcorrencia () {
		return getPropertyValue(this, dataOcorrencia, PROP_DATA_OCORRENCIA); 
	}

	/**
	 * Set the value related to the column: dt_ocorrencia
	 * @param dataOcorrencia the dt_ocorrencia value
	 */
	public void setDataOcorrencia (java.util.Date dataOcorrencia) {
//        java.util.Date dataOcorrenciaOld = this.dataOcorrencia;
		this.dataOcorrencia = dataOcorrencia;
//        this.getPropertyChangeSupport().firePropertyChange ("dataOcorrencia", dataOcorrenciaOld, dataOcorrencia);
	}



	/**
	 * Return the value associated with the column: ds_ocorrencia
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_ocorrencia
	 * @param descricao the ds_ocorrencia value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: tipo
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tipo
	 * @param tipo the tipo value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: documento
	 */
	public java.lang.Long getDocumento () {
		return getPropertyValue(this, documento, PROP_DOCUMENTO); 
	}

	/**
	 * Set the value related to the column: documento
	 * @param documento the documento value
	 */
	public void setDocumento (java.lang.Long documento) {
//        java.lang.Long documentoOld = this.documento;
		this.documento = documento;
//        this.getPropertyChangeSupport().firePropertyChange ("documento", documentoOld, documento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_processo_adm
	 */
	public br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo getProcessoAdministrativo () {
		return getPropertyValue(this, processoAdministrativo, PROP_PROCESSO_ADMINISTRATIVO); 
	}

	/**
	 * Set the value related to the column: cd_processo_adm
	 * @param processoAdministrativo the cd_processo_adm value
	 */
	public void setProcessoAdministrativo (br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo processoAdministrativo) {
//        br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo processoAdministrativoOld = this.processoAdministrativo;
		this.processoAdministrativo = processoAdministrativo;
//        this.getPropertyChangeSupport().firePropertyChange ("processoAdministrativo", processoAdministrativoOld, processoAdministrativo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia processoAdministrativoOcorrencia = (br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoOcorrencia) obj;
			if (null == this.getCodigo() || null == processoAdministrativoOcorrencia.getCodigo()) return false;
			else return (this.getCodigo().equals(processoAdministrativoOcorrencia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}