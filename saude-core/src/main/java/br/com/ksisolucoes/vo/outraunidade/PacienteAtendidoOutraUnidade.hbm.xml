<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.outraunidade">
    <class name="PacienteAtendidoOutraUnidade" table="paciente_atendido_outra_unidade">
        <id column="cd_atend_outra_unid" name="codigo" type="java.lang.Long">
            <generator class="sequence">
                <param name="sequence">seq_audit_id_paciente_atendido_outra_unidade</param>
            </generator>
        </id>

        <version column="version" name="version" type="long"/>

        <many-to-one name="atendimento" class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento">
            <column name="nr_atendimento"/>
        </many-to-one>

        <many-to-one name="empresaPaciente" class="br.com.ksisolucoes.vo.basico.Empresa">
            <column name="empresa_paciente"/>
        </many-to-one>

        <many-to-one name="usuarioEncerramento" class="br.com.ksisolucoes.vo.controle.Usuario">
            <column name="cd_usuario_encerramento"/>
        </many-to-one>

        <property
                column="situacao"
                name="situacao"
                type="java.lang.Long"
                not-null="true"/>

        <property
                column="justificativa"
                name="justificativa"
                type="java.lang.String"
                not-null="true"/>

        <property
                column="dt_cadastro"
                name="dataCadastro"
                type="timestamp"
                not-null="true"/>

        <property
                column="dt_encerramento"
                name="dataEncerramento"
                type="timestamp"/>
    </class>
</hibernate-mapping>
