package br.com.ksisolucoes.vo.vigilancia.dengue;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import br.com.ksisolucoes.vo.vigilancia.dengue.base.BaseDengueTipoPontoEstrategico;
import java.io.Serializable;



public class DengueTipoPontoEstrategico extends BaseDengueTipoPontoEstrategico implements CodigoManager,PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public DengueTipoPontoEstrategico () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public DengueTipoPontoEstrategico (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public DengueTipoPontoEstrategico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.lang.String descricao,
		java.util.Date dataCadastro) {

		super (
			codigo,
			usuarioCadastro,
			descricao,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

 @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return getCodigo().toString();
    }
}