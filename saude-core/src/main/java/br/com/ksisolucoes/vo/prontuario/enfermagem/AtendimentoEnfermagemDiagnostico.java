package br.com.ksisolucoes.vo.prontuario.enfermagem;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.enfermagem.base.BaseAtendimentoEnfermagemDiagnostico;

import java.io.Serializable;


public class AtendimentoEnfermagemDiagnostico extends BaseAtendimentoEnfermagemDiagnostico implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public AtendimentoEnfermagemDiagnostico() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public AtendimentoEnfermagemDiagnostico(java.lang.Long codigo) {
        super(codigo);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public String getDescricaoFormatado() {
        if (getDiagnosticoNaoPadronizado() != null && !getDiagnosticoNaoPadronizado().isEmpty()) {
            return getDiagnosticoNaoPadronizado();
        }
        return getDiagnosticoEnfermagemSae() == null ? "" : getDiagnosticoEnfermagemSae().getDescricao();
    }
}