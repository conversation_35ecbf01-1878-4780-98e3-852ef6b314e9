package br.com.ksisolucoes.vo.integracao.branet;

import java.io.Serializable;

import br.com.ksisolucoes.vo.integracao.branet.base.BaseIntegracaoConfirmacaoRecebimentoBranet;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class IntegracaoConfirmacaoRecebimentoBranet extends BaseIntegracaoConfirmacaoRecebimentoBranet implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public IntegracaoConfirmacaoRecebimentoBranet () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public IntegracaoConfirmacaoRecebimentoBranet (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public IntegracaoConfirmacaoRecebimentoBranet (
		java.lang.Long codigo,
		java.util.Date data,
		java.util.Date dataCadastro) {

		super (
			codigo,
			data,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}