package br.com.ksisolucoes.vo.service.sms;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.service.sms.base.BaseSmsCadastro;

import java.io.Serializable;



public class SmsCadastro extends BaseSmsCadastro implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final String PROP_DESCRICAO_STATUS = "descricaoStatus";
        public static final String PROP_DESCRICAO_TIPO_DESTINO = "descricaoTipoDestino";
        
        public enum Status implements IEnum {
            PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
            PROCESSANDO(1L, Bundle.getStringApplication("rotulo_descricao_processando")),
            CONCLUIDO(2L, Bundle.getStringApplication("rotulo_concluido")),
            ERRO(3L, Bundle.getStringApplication("rotulo_erro"))
            ;

            private Long value;
            private String descricao;

            private Status(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static Status valeuOf(Long value) {
                for (Status status : Status.values()) {
                    if (status.value().equals(value)) {
                        return status;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum TipoDestino implements IEnum {
            INDIVIDUAL(0L, Bundle.getStringApplication("rotulo_individual")),
            UNIDADE(1L, Bundle.getStringApplication("rotulo_unidade")),
            AREA(2L, Bundle.getStringApplication("rotulo_area")),
            MICROAREA(3L, Bundle.getStringApplication("rotulo_microarea"))
            ;

            private Long value;
            private String descricao;

            private TipoDestino(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static TipoDestino valeuOf(Long value) {
                for (TipoDestino tipoDestino : TipoDestino.values()) {
                    if (tipoDestino.value().equals(value)) {
                        return tipoDestino;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public SmsCadastro () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public SmsCadastro (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public SmsCadastro (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuarioCadastro,
		java.util.Date dataCadastro,
		java.lang.Long tipoDestino,
		java.lang.String destino,
		java.lang.Long status,
		java.lang.String mensagem,
		java.lang.Long totalSms) {

		super (
			codigo,
			usuarioCadastro,
			dataCadastro,
			tipoDestino,
			destino,
			status,
			mensagem,
			totalSms);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus(){
        Status status = Status.valeuOf(getStatus());
        if (status != null && status.descricao != null) {
            return status.descricao();
        }
        return "";
    }
    
    public String getDescricaoTipoDestino(){
        TipoDestino tipoDestino = TipoDestino.valeuOf(getTipoDestino());
        if (tipoDestino != null && tipoDestino.descricao != null) {
            return tipoDestino.descricao();
        }
        return "";
    }

    public String getSexoFormatado() {
        if (RepositoryComponentDefault.SEXO_MASCULINO.equals(this.getSexo())) {
            return Bundle.getStringApplication("rotulo_masculino");
        } else if (RepositoryComponentDefault.SEXO_FEMININO.equals(this.getSexo())) {
            return Bundle.getStringApplication("rotulo_feminino");
        } else {
            return null;
        }
    }
}