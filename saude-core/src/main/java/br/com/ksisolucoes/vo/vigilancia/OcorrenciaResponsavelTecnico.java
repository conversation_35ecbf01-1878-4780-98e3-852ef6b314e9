package br.com.ksisolucoes.vo.vigilancia;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.base.BaseOcorrenciaResponsavelTecnico;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class OcorrenciaResponsavelTecnico extends BaseOcorrenciaResponsavelTecnico implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public OcorrenciaResponsavelTecnico () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public OcorrenciaResponsavelTecnico (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public OcorrenciaResponsavelTecnico (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico responsavelTecnico,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataAlteracao,
		java.lang.String descricaoAlteracao) {

		super (
			codigo,
			responsavelTecnico,
			usuario,
			dataAlteracao,
			descricaoAlteracao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}