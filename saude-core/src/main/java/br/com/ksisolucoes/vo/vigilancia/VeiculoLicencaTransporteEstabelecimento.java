package br.com.ksisolucoes.vo.vigilancia;

import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseVeiculoLicencaTransporteEstabelecimento;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.ConfiguracaoVigilanciaFinanceiro;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;
import br.com.ksisolucoes.vo.vigilancia.taxa.TaxaIndice;
import ch.lambdaj.Lambda;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.text.NumberFormat;
import java.text.ParseException;
import java.util.List;

import static ch.lambdaj.Lambda.on;
import static ch.lambdaj.Lambda.selectMax;


public class VeiculoLicencaTransporteEstabelecimento extends BaseVeiculoLicencaTransporteEstabelecimento implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public VeiculoLicencaTransporteEstabelecimento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public VeiculoLicencaTransporteEstabelecimento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public VeiculoLicencaTransporteEstabelecimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoLicencaTransporte requerimentoLicencaTransporte) {

		super (
			codigo,
			estabelecimento,
			requerimentoLicencaTransporte);
	}

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public static enum Status implements IEnum<VeiculoLicencaTransporteEstabelecimento.Status> {

        PENDENTE(0L, Bundle.getStringApplication("rotulo_pendente")),
        DEFERIDO(1L, Bundle.getStringApplication("rotulo_deferido")),
        INDEFERIDO(2L, Bundle.getStringApplication("rotulo_indeferido")),
        ;

        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static VeiculoLicencaTransporteEstabelecimento.Status valeuOf(Long value) {
            for (VeiculoLicencaTransporteEstabelecimento.Status status : VeiculoLicencaTransporteEstabelecimento.Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return this.descricao;
        }
    }

    public String getDescricaoCalculoTaxaAutorizacaoSanitaria() {
        BigDecimal totalTaxa = new BigDecimal(0);
        totalTaxa = getCalculoTaxa(true, true);
        NumberFormat nf = NumberFormat.getCurrencyInstance();
        return nf.format(totalTaxa);
    }

    public String getDescricaoCalculoTaxa() {
        BigDecimal totalTaxa = new BigDecimal(0);
        totalTaxa = getCalculoTaxa(true, false);
        NumberFormat nf = NumberFormat.getCurrencyInstance();
        return nf.format(totalTaxa);

    }

    public BigDecimal getCalculoTaxa(boolean retornarValor, boolean autorizacaoSanitaria) {
        BigDecimal valor = new BigDecimal(0D);
        BigDecimal quantidadeTaxa = new BigDecimal(1D);
        TaxaIndice taxaVigente = null;
        ConfiguracaoVigilanciaFinanceiro configuracaoVigilanciaFinanceiro = null;
        try {
            configuracaoVigilanciaFinanceiro = VigilanciaHelper.getConfiguracaoVigilanciaFinanceiro();
        } catch (ValidacaoException e) {
            Loggable.log.error(e.getMessage(), e);
        }
        LoadManager load = LoadManager.getInstance(TaxaIndice.class);
        load.addProperties(new HQLProperties(TaxaIndice.class).getProperties());
        if (RepositoryComponentDefault.SIM_LONG.equals(getRefrigerado())) {
            load.addParameter(new QueryCustom.QueryCustomParameter(TaxaIndice.PROP_TAXA, configuracaoVigilanciaFinanceiro.getTaxaVeiculoRefrigerado()));
        } else if (RepositoryComponentDefault.NAO_LONG.equals(getRefrigerado())) {
            load.addParameter(new QueryCustom.QueryCustomParameter(TaxaIndice.PROP_TAXA, configuracaoVigilanciaFinanceiro.getTaxaVeiculoNaoRefrigerado()));
        } else {
            load.addParameter(new QueryCustom.QueryCustomParameter(TaxaIndice.PROP_TAXA, configuracaoVigilanciaFinanceiro.getTaxaUnicaLicencaTransporte()));
        }
        List<TaxaIndice> indices = load.addParameter(new QueryCustom.QueryCustomParameter(TaxaIndice.PROP_DATA_INICIO_VIGENCIA, QueryCustom.QueryCustomParameter.MENOR_IGUAL, DataUtil.getDataAtual()))
                .start().getList();
        taxaVigente = selectMax(indices, on(TaxaIndice.class).getDataInicioVigencia());

        if (!autorizacaoSanitaria) {

            if (ConfiguracaoVigilanciaFinanceiro.TipoCalculoLicencaTransporte.TAXA_UNICA.value().equals(configuracaoVigilanciaFinanceiro.getTipoCalculoTaxaLicencaTransporte()) &&
                    configuracaoVigilanciaFinanceiro.getQuantidadeTaxaUnicaLicencaTransporte() != null &&
                    taxaVigente.getValorIndice() != null
            ) {

                valor = new Dinheiro(configuracaoVigilanciaFinanceiro.getQuantidadeTaxaUnicaLicencaTransporte(), 4).multiplicar(taxaVigente.getValorIndice(), 4).bigDecimalValue();
                quantidadeTaxa = new Dinheiro(configuracaoVigilanciaFinanceiro.getQuantidadeTaxaUnicaLicencaTransporte(), 4).bigDecimalValue();

            } else if (ConfiguracaoVigilanciaFinanceiro.TipoCalculoLicencaTransporte.CONSIDERAR_REFRIGERACAO.value().equals(configuracaoVigilanciaFinanceiro.getTipoCalculoTaxaLicencaTransporte())) {
                if (RepositoryComponentDefault.SIM_LONG.equals(getRefrigerado())) {
                    if (taxaVigente != null && configuracaoVigilanciaFinanceiro.getQuantidadeTaxaVeiculoRefrigerado() != null) {
                        valor = new Dinheiro(configuracaoVigilanciaFinanceiro.getQuantidadeTaxaVeiculoRefrigerado(), 4).multiplicar(taxaVigente.getValorIndice(), 4).bigDecimalValue();
                        quantidadeTaxa = new Dinheiro(configuracaoVigilanciaFinanceiro.getQuantidadeTaxaVeiculoRefrigerado(), 4).bigDecimalValue();
                    }
                } else if (RepositoryComponentDefault.NAO_LONG.equals(getRefrigerado())) {
                    if (taxaVigente != null && configuracaoVigilanciaFinanceiro.getQuantidadeTaxaVeiculoNaoRefrigerado() != null) {
                        valor = new Dinheiro(configuracaoVigilanciaFinanceiro.getQuantidadeTaxaVeiculoNaoRefrigerado(), 4).multiplicar(taxaVigente.getValorIndice(), 4).bigDecimalValue();
                        quantidadeTaxa = new Dinheiro(configuracaoVigilanciaFinanceiro.getQuantidadeTaxaVeiculoNaoRefrigerado(), 4).bigDecimalValue();
                    }
                } else {
                    valor = new BigDecimal(0D);
                    quantidadeTaxa = new BigDecimal(0D);
                }

            } else if (ConfiguracaoVigilanciaFinanceiro.TipoCalculoLicencaTransporte.ATIVIDADES_ESTABELECIMENTO.value().equals(configuracaoVigilanciaFinanceiro.getTipoCalculoTaxaLicencaTransporte())) {
                if (this.getEstabelecimento() != null) {
                    List<EstabelecimentoAtividade> estabelecimentoAtividades = VigilanciaHelper.buscarEstabelecimentoAtividade(this.getEstabelecimento());
                    valor = Lambda.sum(estabelecimentoAtividades, Lambda.on(EstabelecimentoAtividade.class).getCalculoTaxa(true, true));
                    quantidadeTaxa = Lambda.sum(estabelecimentoAtividades, Lambda.on(EstabelecimentoAtividade.class).getCalculoTaxa(false, true));
                } else {
                    valor = new BigDecimal(0D);
                    quantidadeTaxa = new BigDecimal(0D);
                }
            }
        }

        if (retornarValor) {
            valor = new Dinheiro(valor).round().bigDecimalValue();
            return valor;
        } else {
            quantidadeTaxa = new Dinheiro(quantidadeTaxa).round().bigDecimalValue();
            return quantidadeTaxa;
        }
    }

    public String getPlacaFormatada() {
        if (getPlaca() != null) {
            try {
                if (getPlaca().matches("[A-Z]{3}[0-9][A-Z][0-9]{2}")) {
                    return getPlaca();
                }
                MaskFormatter m = new MaskFormatter("UUU-####");
                m.setValueContainsLiteralCharacters(false);
                return m.valueToString(getPlaca());
            } catch (ParseException ex) {
                Loggable.log.error(ex.getMessage(), ex);
            }
        }
        return "";
    }

    public String getDescricaoRefrigerado() {
        if (getRefrigerado() != null) return getRefrigerado().equals(0l) ? "Não" : "Sim";
        return "";
    }

    public String getDescricaoStatus() {
        if (getStatus() != null) return Status.valeuOf(getStatus()).descricao;
        return "";
    }
}