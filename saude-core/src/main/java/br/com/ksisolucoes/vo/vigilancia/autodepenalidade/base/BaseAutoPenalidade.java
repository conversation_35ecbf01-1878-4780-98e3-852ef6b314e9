package br.com.ksisolucoes.vo.vigilancia.autodepenalidade.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the auto_penalidade table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="auto_penalidade"
 */

public abstract class BaseAutoPenalidade extends BaseRootVO implements Serializable {

	public static String REF = "AutoPenalidade";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_AUTUADO = "autuado";
	public static final String PROP_COMPLEMENTO_LOGRADOURO = "complementoLogradouro";
	public static final String PROP_RECUSOU_ASSINAR = "recusouAssinar";
	public static final String PROP_ENVIADO = "enviado";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_PRAZO_RECURSO = "prazoRecurso";
	public static final String PROP_ESTABELECIMENTO_AUTUADO = "estabelecimentoAutuado";
	public static final String PROP_PROCESSO_ADMINISTRATIVO = "processoAdministrativo";
	public static final String PROP_VIGILANCIA_PESSOA = "vigilanciaPessoa";
	public static final String PROP_SITUACAO = "situacao";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_AUTO_INFRACAO = "autoInfracao";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_DENUNCIA = "denuncia";
	public static final String PROP_DATA_RECEBIMENTO = "dataRecebimento";
	public static final String PROP_REGISTRO_INSPECAO = "registroInspecao";
	public static final String PROP_VIGILANCIA_ENDERECO = "vigilanciaEndereco";
	public static final String PROP_TESTEMUNHA = "testemunha";
	public static final String PROP_TIPO_AUTUADO = "tipoAutuado";
	public static final String PROP_USUARIO_EDICAO = "usuarioEdicao";
	public static final String PROP_NUMERO_LOGRADOURO = "numeroLogradouro";
	public static final String PROP_REQUERIMENTO_VIGILANCIA = "requerimentoVigilancia";
	public static final String PROP_DATA_PENALIDADE = "dataPenalidade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO = "processoAdministrativoAutenticacao";
	public static final String PROP_NOME_RESPONSAVEL = "nomeResponsavel";
	public static final String PROP_ENQUADRAMENTO_LEGAL = "enquadramentoLegal";
	public static final String PROP_DATA_USUARIO = "dataUsuario";
	public static final String PROP_AUTO_INTIMACAO = "autoIntimacao";
	public static final String PROP_MOTIVO_RETORNO = "motivoRetorno";
	public static final String PROP_NUMERO_FORMULARIO = "numeroFormulario";


	// constructors
	public BaseAutoPenalidade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAutoPenalidade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseAutoPenalidade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.controle.Usuario usuarioEdicao,
		java.util.Date dataPenalidade,
		java.lang.Long tipoAutuado,
		java.util.Date dataCadastro,
		java.util.Date dataUsuario,
		java.lang.Long situacao) {

		this.setCodigo(codigo);
		this.setVigilanciaEndereco(vigilanciaEndereco);
		this.setUsuario(usuario);
		this.setUsuarioEdicao(usuarioEdicao);
		this.setDataPenalidade(dataPenalidade);
		this.setTipoAutuado(tipoAutuado);
		this.setDataCadastro(dataCadastro);
		this.setDataUsuario(dataUsuario);
		this.setSituacao(situacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeroFormulario;
	private java.util.Date dataPenalidade;
	private java.lang.Long tipoAutuado;
	private java.lang.String autuado;
	private java.lang.String enquadramentoLegal;
	private java.lang.String nomeResponsavel;
	private java.lang.Long recusouAssinar;
	private java.util.Date dataRecebimento;
	private java.lang.String testemunha;
	private java.lang.String telefone;
	private java.lang.String complementoLogradouro;
	private java.lang.String numeroLogradouro;
	private java.util.Date dataCadastro;
	private java.util.Date dataUsuario;
	private java.lang.Long enviado;
	private java.lang.Long numero;
	private java.util.Date prazoRecurso;
	private java.lang.Long situacao;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia denuncia;
	private br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao;
	private br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao autoInfracao;
	private br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoAutuado;
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoa;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEdicao;
	private br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao registroInspecao;
	private br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno motivoRetorno;
	private br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo processoAdministrativo;
	private br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao;
	private br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_auto_penalidade"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nro_formulario
	 */
	public java.lang.Long getNumeroFormulario () {
		return getPropertyValue(this, numeroFormulario, PROP_NUMERO_FORMULARIO); 
	}

	/**
	 * Set the value related to the column: nro_formulario
	 * @param numeroFormulario the nro_formulario value
	 */
	public void setNumeroFormulario (java.lang.Long numeroFormulario) {
//        java.lang.Long numeroFormularioOld = this.numeroFormulario;
		this.numeroFormulario = numeroFormulario;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroFormulario", numeroFormularioOld, numeroFormulario);
	}



	/**
	 * Return the value associated with the column: dt_penalidade
	 */
	public java.util.Date getDataPenalidade () {
		return getPropertyValue(this, dataPenalidade, PROP_DATA_PENALIDADE); 
	}

	/**
	 * Set the value related to the column: dt_penalidade
	 * @param dataPenalidade the dt_penalidade value
	 */
	public void setDataPenalidade (java.util.Date dataPenalidade) {
//        java.util.Date dataPenalidadeOld = this.dataPenalidade;
		this.dataPenalidade = dataPenalidade;
//        this.getPropertyChangeSupport().firePropertyChange ("dataPenalidade", dataPenalidadeOld, dataPenalidade);
	}



	/**
	 * Return the value associated with the column: tipo_autuado
	 */
	public java.lang.Long getTipoAutuado () {
		return getPropertyValue(this, tipoAutuado, PROP_TIPO_AUTUADO); 
	}

	/**
	 * Set the value related to the column: tipo_autuado
	 * @param tipoAutuado the tipo_autuado value
	 */
	public void setTipoAutuado (java.lang.Long tipoAutuado) {
//        java.lang.Long tipoAutuadoOld = this.tipoAutuado;
		this.tipoAutuado = tipoAutuado;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAutuado", tipoAutuadoOld, tipoAutuado);
	}



	/**
	 * Return the value associated with the column: nm_autuado
	 */
	public java.lang.String getAutuado () {
		return getPropertyValue(this, autuado, PROP_AUTUADO); 
	}

	/**
	 * Set the value related to the column: nm_autuado
	 * @param autuado the nm_autuado value
	 */
	public void setAutuado (java.lang.String autuado) {
//        java.lang.String autuadoOld = this.autuado;
		this.autuado = autuado;
//        this.getPropertyChangeSupport().firePropertyChange ("autuado", autuadoOld, autuado);
	}



	/**
	 * Return the value associated with the column: ds_enquadramento_legal
	 */
	public java.lang.String getEnquadramentoLegal () {
		return getPropertyValue(this, enquadramentoLegal, PROP_ENQUADRAMENTO_LEGAL); 
	}

	/**
	 * Set the value related to the column: ds_enquadramento_legal
	 * @param enquadramentoLegal the ds_enquadramento_legal value
	 */
	public void setEnquadramentoLegal (java.lang.String enquadramentoLegal) {
//        java.lang.String enquadramentoLegalOld = this.enquadramentoLegal;
		this.enquadramentoLegal = enquadramentoLegal;
//        this.getPropertyChangeSupport().firePropertyChange ("enquadramentoLegal", enquadramentoLegalOld, enquadramentoLegal);
	}



	/**
	 * Return the value associated with the column: nm_responsavel
	 */
	public java.lang.String getNomeResponsavel () {
		return getPropertyValue(this, nomeResponsavel, PROP_NOME_RESPONSAVEL); 
	}

	/**
	 * Set the value related to the column: nm_responsavel
	 * @param nomeResponsavel the nm_responsavel value
	 */
	public void setNomeResponsavel (java.lang.String nomeResponsavel) {
//        java.lang.String nomeResponsavelOld = this.nomeResponsavel;
		this.nomeResponsavel = nomeResponsavel;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeResponsavel", nomeResponsavelOld, nomeResponsavel);
	}



	/**
	 * Return the value associated with the column: recusou_assinar
	 */
	public java.lang.Long getRecusouAssinar () {
		return getPropertyValue(this, recusouAssinar, PROP_RECUSOU_ASSINAR); 
	}

	/**
	 * Set the value related to the column: recusou_assinar
	 * @param recusouAssinar the recusou_assinar value
	 */
	public void setRecusouAssinar (java.lang.Long recusouAssinar) {
//        java.lang.Long recusouAssinarOld = this.recusouAssinar;
		this.recusouAssinar = recusouAssinar;
//        this.getPropertyChangeSupport().firePropertyChange ("recusouAssinar", recusouAssinarOld, recusouAssinar);
	}



	/**
	 * Return the value associated with the column: dt_recebimento
	 */
	public java.util.Date getDataRecebimento () {
		return getPropertyValue(this, dataRecebimento, PROP_DATA_RECEBIMENTO); 
	}

	/**
	 * Set the value related to the column: dt_recebimento
	 * @param dataRecebimento the dt_recebimento value
	 */
	public void setDataRecebimento (java.util.Date dataRecebimento) {
//        java.util.Date dataRecebimentoOld = this.dataRecebimento;
		this.dataRecebimento = dataRecebimento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRecebimento", dataRecebimentoOld, dataRecebimento);
	}



	/**
	 * Return the value associated with the column: nm_testemunha
	 */
	public java.lang.String getTestemunha () {
		return getPropertyValue(this, testemunha, PROP_TESTEMUNHA); 
	}

	/**
	 * Set the value related to the column: nm_testemunha
	 * @param testemunha the nm_testemunha value
	 */
	public void setTestemunha (java.lang.String testemunha) {
//        java.lang.String testemunhaOld = this.testemunha;
		this.testemunha = testemunha;
//        this.getPropertyChangeSupport().firePropertyChange ("testemunha", testemunhaOld, testemunha);
	}



	/**
	 * Return the value associated with the column: nr_telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: nr_telefone
	 * @param telefone the nr_telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: comp_logradouro
	 */
	public java.lang.String getComplementoLogradouro () {
		return getPropertyValue(this, complementoLogradouro, PROP_COMPLEMENTO_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: comp_logradouro
	 * @param complementoLogradouro the comp_logradouro value
	 */
	public void setComplementoLogradouro (java.lang.String complementoLogradouro) {
//        java.lang.String complementoLogradouroOld = this.complementoLogradouro;
		this.complementoLogradouro = complementoLogradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("complementoLogradouro", complementoLogradouroOld, complementoLogradouro);
	}



	/**
	 * Return the value associated with the column: nr_logradouro
	 */
	public java.lang.String getNumeroLogradouro () {
		return getPropertyValue(this, numeroLogradouro, PROP_NUMERO_LOGRADOURO); 
	}

	/**
	 * Set the value related to the column: nr_logradouro
	 * @param numeroLogradouro the nr_logradouro value
	 */
	public void setNumeroLogradouro (java.lang.String numeroLogradouro) {
//        java.lang.String numeroLogradouroOld = this.numeroLogradouro;
		this.numeroLogradouro = numeroLogradouro;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroLogradouro", numeroLogradouroOld, numeroLogradouro);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: dt_usuario
	 */
	public java.util.Date getDataUsuario () {
		return getPropertyValue(this, dataUsuario, PROP_DATA_USUARIO); 
	}

	/**
	 * Set the value related to the column: dt_usuario
	 * @param dataUsuario the dt_usuario value
	 */
	public void setDataUsuario (java.util.Date dataUsuario) {
//        java.util.Date dataUsuarioOld = this.dataUsuario;
		this.dataUsuario = dataUsuario;
//        this.getPropertyChangeSupport().firePropertyChange ("dataUsuario", dataUsuarioOld, dataUsuario);
	}



	/**
	 * Return the value associated with the column: enviado
	 */
	public java.lang.Long getEnviado () {
		return getPropertyValue(this, enviado, PROP_ENVIADO); 
	}

	/**
	 * Set the value related to the column: enviado
	 * @param enviado the enviado value
	 */
	public void setEnviado (java.lang.Long enviado) {
//        java.lang.Long enviadoOld = this.enviado;
		this.enviado = enviado;
//        this.getPropertyChangeSupport().firePropertyChange ("enviado", enviadoOld, enviado);
	}



	/**
	 * Return the value associated with the column: num_auto_penalidade
	 */
	public java.lang.Long getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: num_auto_penalidade
	 * @param numero the num_auto_penalidade value
	 */
	public void setNumero (java.lang.Long numero) {
//        java.lang.Long numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: prazo_recurso
	 */
	public java.util.Date getPrazoRecurso () {
		return getPropertyValue(this, prazoRecurso, PROP_PRAZO_RECURSO); 
	}

	/**
	 * Set the value related to the column: prazo_recurso
	 * @param prazoRecurso the prazo_recurso value
	 */
	public void setPrazoRecurso (java.util.Date prazoRecurso) {
//        java.util.Date prazoRecursoOld = this.prazoRecurso;
		this.prazoRecurso = prazoRecurso;
//        this.getPropertyChangeSupport().firePropertyChange ("prazoRecurso", prazoRecursoOld, prazoRecurso);
	}



	/**
	 * Return the value associated with the column: situacao
	 */
	public java.lang.Long getSituacao () {
		return getPropertyValue(this, situacao, PROP_SITUACAO); 
	}

	/**
	 * Set the value related to the column: situacao
	 * @param situacao the situacao value
	 */
	public void setSituacao (java.lang.Long situacao) {
//        java.lang.Long situacaoOld = this.situacao;
		this.situacao = situacao;
//        this.getPropertyChangeSupport().firePropertyChange ("situacao", situacaoOld, situacao);
	}



	/**
	 * Return the value associated with the column: cd_denuncia
	 */
	public br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia getDenuncia () {
		return getPropertyValue(this, denuncia, PROP_DENUNCIA); 
	}

	/**
	 * Set the value related to the column: cd_denuncia
	 * @param denuncia the cd_denuncia value
	 */
	public void setDenuncia (br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia denuncia) {
//        br.com.ksisolucoes.vo.vigilancia.denuncia.Denuncia denunciaOld = this.denuncia;
		this.denuncia = denuncia;
//        this.getPropertyChangeSupport().firePropertyChange ("denuncia", denunciaOld, denuncia);
	}



	/**
	 * Return the value associated with the column: cd_auto_intimacao
	 */
	public br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao getAutoIntimacao () {
		return getPropertyValue(this, autoIntimacao, PROP_AUTO_INTIMACAO); 
	}

	/**
	 * Set the value related to the column: cd_auto_intimacao
	 * @param autoIntimacao the cd_auto_intimacao value
	 */
	public void setAutoIntimacao (br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacao) {
//        br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacao autoIntimacaoOld = this.autoIntimacao;
		this.autoIntimacao = autoIntimacao;
//        this.getPropertyChangeSupport().firePropertyChange ("autoIntimacao", autoIntimacaoOld, autoIntimacao);
	}



	/**
	 * Return the value associated with the column: cd_auto_infracao
	 */
	public br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao getAutoInfracao () {
		return getPropertyValue(this, autoInfracao, PROP_AUTO_INFRACAO); 
	}

	/**
	 * Set the value related to the column: cd_auto_infracao
	 * @param autoInfracao the cd_auto_infracao value
	 */
	public void setAutoInfracao (br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao autoInfracao) {
//        br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao autoInfracaoOld = this.autoInfracao;
		this.autoInfracao = autoInfracao;
//        this.getPropertyChangeSupport().firePropertyChange ("autoInfracao", autoInfracaoOld, autoInfracao);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_endereco
	 */
	public br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco getVigilanciaEndereco () {
		return getPropertyValue(this, vigilanciaEndereco, PROP_VIGILANCIA_ENDERECO); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_endereco
	 * @param vigilanciaEndereco the cd_vigilancia_endereco value
	 */
	public void setVigilanciaEndereco (br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco) {
//        br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEnderecoOld = this.vigilanciaEndereco;
		this.vigilanciaEndereco = vigilanciaEndereco;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaEndereco", vigilanciaEnderecoOld, vigilanciaEndereco);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimentoAutuado () {
		return getPropertyValue(this, estabelecimentoAutuado, PROP_ESTABELECIMENTO_AUTUADO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimentoAutuado the cd_estabelecimento value
	 */
	public void setEstabelecimentoAutuado (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoAutuado) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoAutuadoOld = this.estabelecimentoAutuado;
		this.estabelecimentoAutuado = estabelecimentoAutuado;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimentoAutuado", estabelecimentoAutuadoOld, estabelecimentoAutuado);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_pessoa
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa getVigilanciaPessoa () {
		return getPropertyValue(this, vigilanciaPessoa, PROP_VIGILANCIA_PESSOA); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_pessoa
	 * @param vigilanciaPessoa the cd_vigilancia_pessoa value
	 */
	public void setVigilanciaPessoa (br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoa) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa vigilanciaPessoaOld = this.vigilanciaPessoa;
		this.vigilanciaPessoa = vigilanciaPessoa;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaPessoa", vigilanciaPessoaOld, vigilanciaPessoa);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_usuario_edicao
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEdicao () {
		return getPropertyValue(this, usuarioEdicao, PROP_USUARIO_EDICAO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_edicao
	 * @param usuarioEdicao the cd_usuario_edicao value
	 */
	public void setUsuarioEdicao (br.com.ksisolucoes.vo.controle.Usuario usuarioEdicao) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEdicaoOld = this.usuarioEdicao;
		this.usuarioEdicao = usuarioEdicao;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEdicao", usuarioEdicaoOld, usuarioEdicao);
	}



	/**
	 * Return the value associated with the column: cd_registro_inspecao
	 */
	public br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao getRegistroInspecao () {
		return getPropertyValue(this, registroInspecao, PROP_REGISTRO_INSPECAO); 
	}

	/**
	 * Set the value related to the column: cd_registro_inspecao
	 * @param registroInspecao the cd_registro_inspecao value
	 */
	public void setRegistroInspecao (br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao registroInspecao) {
//        br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RegistroInspecao registroInspecaoOld = this.registroInspecao;
		this.registroInspecao = registroInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("registroInspecao", registroInspecaoOld, registroInspecao);
	}



	/**
	 * Return the value associated with the column: cd_motivo_retorno
	 */
	public br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno getMotivoRetorno () {
		return getPropertyValue(this, motivoRetorno, PROP_MOTIVO_RETORNO); 
	}

	/**
	 * Set the value related to the column: cd_motivo_retorno
	 * @param motivoRetorno the cd_motivo_retorno value
	 */
	public void setMotivoRetorno (br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno motivoRetorno) {
//        br.com.ksisolucoes.vo.vigilancia.autointimacao.MotivoRetorno motivoRetornoOld = this.motivoRetorno;
		this.motivoRetorno = motivoRetorno;
//        this.getPropertyChangeSupport().firePropertyChange ("motivoRetorno", motivoRetornoOld, motivoRetorno);
	}



	/**
	 * Return the value associated with the column: cd_processo_adm
	 */
	public br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo getProcessoAdministrativo () {
		return getPropertyValue(this, processoAdministrativo, PROP_PROCESSO_ADMINISTRATIVO); 
	}

	/**
	 * Set the value related to the column: cd_processo_adm
	 * @param processoAdministrativo the cd_processo_adm value
	 */
	public void setProcessoAdministrativo (br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo processoAdministrativo) {
//        br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo processoAdministrativoOld = this.processoAdministrativo;
		this.processoAdministrativo = processoAdministrativo;
//        this.getPropertyChangeSupport().firePropertyChange ("processoAdministrativo", processoAdministrativoOld, processoAdministrativo);
	}



	/**
	 * Return the value associated with the column: cd_processo_adm_autenticacao
	 */
	public br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao getProcessoAdministrativoAutenticacao () {
		return getPropertyValue(this, processoAdministrativoAutenticacao, PROP_PROCESSO_ADMINISTRATIVO_AUTENTICACAO); 
	}

	/**
	 * Set the value related to the column: cd_processo_adm_autenticacao
	 * @param processoAdministrativoAutenticacao the cd_processo_adm_autenticacao value
	 */
	public void setProcessoAdministrativoAutenticacao (br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacao) {
//        br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativoAutenticacao processoAdministrativoAutenticacaoOld = this.processoAdministrativoAutenticacao;
		this.processoAdministrativoAutenticacao = processoAdministrativoAutenticacao;
//        this.getPropertyChangeSupport().firePropertyChange ("processoAdministrativoAutenticacao", processoAdministrativoAutenticacaoOld, processoAdministrativoAutenticacao);
	}



	/**
	 * Return the value associated with the column: cd_req_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia getRequerimentoVigilancia () {
		return getPropertyValue(this, requerimentoVigilancia, PROP_REQUERIMENTO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_req_vigilancia
	 * @param requerimentoVigilancia the cd_req_vigilancia value
	 */
	public void setRequerimentoVigilancia (br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilanciaOld = this.requerimentoVigilancia;
		this.requerimentoVigilancia = requerimentoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVigilancia", requerimentoVigilanciaOld, requerimentoVigilancia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade autoPenalidade = (br.com.ksisolucoes.vo.vigilancia.autodepenalidade.AutoPenalidade) obj;
			if (null == this.getCodigo() || null == autoPenalidade.getCodigo()) return false;
			else return (this.getCodigo().equals(autoPenalidade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}