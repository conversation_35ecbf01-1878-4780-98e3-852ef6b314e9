package br.com.ksisolucoes.vo.basico;

import java.io.Serializable;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.base.BaseAtendimentoCidNotificavel;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class AtendimentoCidNotificavel extends BaseAtendimentoCidNotificavel implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public AtendimentoCidNotificavel () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public AtendimentoCidNotificavel (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public AtendimentoCidNotificavel (
		java.lang.Long codigo,
		java.lang.Long flagPreencheuNotificacao) {

		super (
			codigo,
			flagPreencheuNotificacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }


	public String getFlagPreencheuNotificacaoFormatado() {
		if (RepositoryComponentDefault.SIM_LONG.equals(this.getFlagPreencheuNotificacao())) {
			return Bundle.getStringApplication("rotulo_sim");
		} else if (RepositoryComponentDefault.NAO_LONG.equals(this.getFlagPreencheuNotificacao())){
			return Bundle.getStringApplication("rotulo_nao");
		}
		return "...";
	}
}