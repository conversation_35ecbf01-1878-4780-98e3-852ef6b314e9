<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus.cds"  >
    <class name="EsusFichaOdonto" table="esus_ficha_odonto">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_esus_ficha_odonto"
        >
            <generator class="assigned" />
        </id> 
        <version column="version" name="version" type="long" />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.basico.Empresa"
            column="empresa"
            name="empresa"
            not-null="true"
            />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional_prin"
            name="profissionalPrincipal"
            not-null="true"
            />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
            column="cd_cbo_prin"
            name="cboPrincipal"
            not-null="true"
            />
        
        <property
            column="cod_ine"
            name="codigoIne"
            type="java.lang.String"
            length="10"
            not-null="false"
    	/>
        
        <property 
            column="dt_atendimento"
            name="dataAtendimento"
            type="timestamp"
            not-null="true"
         />

        <property
            column="dt_atendimento_final"
            name="dataAtendimentoFinal"
            type="timestamp"
            not-null="false"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional_secu"
            name="profissionalSecundario"
            not-null="false"
            />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
            column="cd_cbo_secu"
            name="cboSecundario"
            not-null="false"
            />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.cadsus.Profissional"
            column="cd_profissional_terc"
            name="profissionalTerciario"
            not-null="false"
            />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
            column="cd_cbo_terc"
            name="cboTerciario"
            not-null="false"
            />
	
        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            column="cd_usuario"
            name="usuario"
            not-null="true"
            />
        
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            type="timestamp"
            not-null="true"
    	/>
    </class>
</hibernate-mapping>