<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class name="FichaInvestigacaoAgravo" table="ficha_investigacao_agravo">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_ficha_investigacao_agravo"
        >
            <generator class="sequence">
                <param name="sequence">seq_ficha_investigacao_agravo</param>
            </generator>
        </id>
        <version column="version" name="version" type="long" />

        <property
            name="ordem"
            column="ordem"
            type="java.lang.Long"
            not-null="true"
         />
        
        <property 
            name="dataCadastro"
            column="dt_cadastro"
            type="timestamp"
            not-null="true"
        />
        
        <property
            name="descricao"
            column="descricao"
            type="java.lang.String"
        />

        <property
            name="status"
            column="status"
            type="java.lang.Long"
            not-null="true"
         />

    </class>
</hibernate-mapping>
