<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia">
    <class name="TalonarioReceitaTalidomida" table="talonario_receita_talidomida">
        <id
                column="cd_talonario_rec_talidomida"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_talonario_receita_talidomida</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one class="br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional"
                     name="vigilanciaProfissional">
            <column name="cd_vigilancia_profissional"/>
        </many-to-one>

        <property
                column="nro_inicial"
                name="numeracaoInicial"
                type="java.lang.Long"
        />

        <property
                column="nro_final"
                name="numeracaoFinal"
                type="java.lang.Long"
        />

        <property
                column="dt_entrada"
                name="dataEntrada"
                not-null="true"
                type="java.util.Date"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                not-null="true"
                type="java.util.Date"
        />

        <property
                column="status"
                name="status"
                type="java.lang.Long"
        />

        <many-to-one class="br.com.ksisolucoes.vo.controle.Usuario"
                     name="usuario" not-null="true">
            <column name="cd_usuario"/>
        </many-to-one>

        <many-to-one
                class="br.com.ksisolucoes.vo.vigilancia.Estabelecimento"
                column="cd_estabelecimento"
                not-null="true"
                name="estabelecimento"
        />

    </class>
</hibernate-mapping>
