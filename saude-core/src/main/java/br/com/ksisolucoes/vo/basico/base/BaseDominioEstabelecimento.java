package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the dom_estabelecimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="dom_estabelecimento"
 */

public abstract class BaseDominioEstabelecimento extends BaseRootVO implements Serializable {

	public static String REF = "DominioEstabelecimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_KEYWORD = "keyword";
	public static final String PROP_CNES = "cnes";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_CNPJ_CPF = "cnpjCpf";
	public static final String PROP_SIGLA = "sigla";
	public static final String PROP_TIPO_UNIDADE = "tipoUnidade";
	public static final String PROP_NOME = "nome";


	// constructors
	public BaseDominioEstabelecimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseDominioEstabelecimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseDominioEstabelecimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.String keyword) {

		this.setCodigo(codigo);
		this.setEmpresa(empresa);
		this.setKeyword(keyword);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String keyword;
	private java.lang.String nome;
	private java.lang.String cnes;
	private java.lang.String sigla;
	private java.lang.String cnpjCpf;
	private java.lang.Long tipoUnidade;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_dominio"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: keyword
	 */
	public java.lang.String getKeyword () {
		return getPropertyValue(this, keyword, PROP_KEYWORD); 
	}

	/**
	 * Set the value related to the column: keyword
	 * @param keyword the keyword value
	 */
	public void setKeyword (java.lang.String keyword) {
//        java.lang.String keywordOld = this.keyword;
		this.keyword = keyword;
//        this.getPropertyChangeSupport().firePropertyChange ("keyword", keywordOld, keyword);
	}



	/**
	 * Return the value associated with the column: nome
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nome
	 * @param nome the nome value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: cnes
	 */
	public java.lang.String getCnes () {
		return getPropertyValue(this, cnes, PROP_CNES); 
	}

	/**
	 * Set the value related to the column: cnes
	 * @param cnes the cnes value
	 */
	public void setCnes (java.lang.String cnes) {
//        java.lang.String cnesOld = this.cnes;
		this.cnes = cnes;
//        this.getPropertyChangeSupport().firePropertyChange ("cnes", cnesOld, cnes);
	}



	/**
	 * Return the value associated with the column: sigla
	 */
	public java.lang.String getSigla () {
		return getPropertyValue(this, sigla, PROP_SIGLA); 
	}

	/**
	 * Set the value related to the column: sigla
	 * @param sigla the sigla value
	 */
	public void setSigla (java.lang.String sigla) {
//        java.lang.String siglaOld = this.sigla;
		this.sigla = sigla;
//        this.getPropertyChangeSupport().firePropertyChange ("sigla", siglaOld, sigla);
	}



	/**
	 * Return the value associated with the column: cnpj_cpf
	 */
	public java.lang.String getCnpjCpf () {
		return getPropertyValue(this, cnpjCpf, PROP_CNPJ_CPF); 
	}

	/**
	 * Set the value related to the column: cnpj_cpf
	 * @param cnpjCpf the cnpj_cpf value
	 */
	public void setCnpjCpf (java.lang.String cnpjCpf) {
//        java.lang.String cnpjCpfOld = this.cnpjCpf;
		this.cnpjCpf = cnpjCpf;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpjCpf", cnpjCpfOld, cnpjCpf);
	}



	/**
	 * Return the value associated with the column: cd_tp_unidade
	 */
	public java.lang.Long getTipoUnidade () {
		return getPropertyValue(this, tipoUnidade, PROP_TIPO_UNIDADE); 
	}

	/**
	 * Set the value related to the column: cd_tp_unidade
	 * @param tipoUnidade the cd_tp_unidade value
	 */
	public void setTipoUnidade (java.lang.Long tipoUnidade) {
//        java.lang.Long tipoUnidadeOld = this.tipoUnidade;
		this.tipoUnidade = tipoUnidade;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoUnidade", tipoUnidadeOld, tipoUnidade);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.DominioEstabelecimento)) return false;
		else {
			br.com.ksisolucoes.vo.basico.DominioEstabelecimento dominioEstabelecimento = (br.com.ksisolucoes.vo.basico.DominioEstabelecimento) obj;
			if (null == this.getCodigo() || null == dominioEstabelecimento.getCodigo()) return false;
			else return (this.getCodigo().equals(dominioEstabelecimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}