package br.com.ksisolucoes.vo.vigilancia.requerimentos.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the req_ana_pba_conf_tec table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="req_ana_pba_conf_tec"
 */

public abstract class BaseRequerimentoVistoriaPBAConformidadeTecnica extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoVistoriaPBAConformidadeTecnica";
	public static final String PROP_SITUACAO_CONFORMIDADE = "situacaoConformidade";
	public static final String PROP_DESCRICAO_CONSTATACOES = "descricaoConstatacoes";
	public static final String PROP_DESCRICAO_CARACTERIZACAO = "descricaoCaracterizacao";
	public static final String PROP_DESCRICAO_CONCLUSAO = "descricaoConclusao";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REQUERIMENTO_VISTORIA_PROJETO_BASICO_ARQUITETURA = "requerimentoVistoriaProjetoBasicoArquitetura";
	public static final String PROP_DATA_SAIDA = "dataSaida";
	public static final String PROP_DESCRICAO_OBJETIVO = "descricaoObjetivo";
	public static final String PROP_NUMERACAO_PARECER_CONFORMIDADE_TECNICA = "numeracaoParecerConformidadeTecnica";
	public static final String PROP_DATA_RETORNO = "dataRetorno";
	public static final String PROP_DESCRICAO_RESSALVAS = "descricaoRessalvas";
	public static final String PROP_DATA_INSPECAO = "dataInspecao";


	// constructors
	public BaseRequerimentoVistoriaPBAConformidadeTecnica () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoVistoriaPBAConformidadeTecnica (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoVistoriaPBAConformidadeTecnica (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura) {

		this.setCodigo(codigo);
		this.setRequerimentoVistoriaProjetoBasicoArquitetura(requerimentoVistoriaProjetoBasicoArquitetura);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricaoCaracterizacao;
	private java.lang.Long situacaoConformidade;
	private java.lang.Long numeracaoParecerConformidadeTecnica;
	private java.util.Date dataInspecao;
	private java.lang.String descricaoObjetivo;
	private java.lang.String descricaoConstatacoes;
	private java.lang.String descricaoRessalvas;
	private java.lang.String descricaoConclusao;
	private java.util.Date dataSaida;
	private java.util.Date dataRetorno;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_req_ana_pba_conf_tec"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_caracterizacao
	 */
	public java.lang.String getDescricaoCaracterizacao () {
		return getPropertyValue(this, descricaoCaracterizacao, PROP_DESCRICAO_CARACTERIZACAO); 
	}

	/**
	 * Set the value related to the column: ds_caracterizacao
	 * @param descricaoCaracterizacao the ds_caracterizacao value
	 */
	public void setDescricaoCaracterizacao (java.lang.String descricaoCaracterizacao) {
//        java.lang.String descricaoCaracterizacaoOld = this.descricaoCaracterizacao;
		this.descricaoCaracterizacao = descricaoCaracterizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoCaracterizacao", descricaoCaracterizacaoOld, descricaoCaracterizacao);
	}



	/**
	 * Return the value associated with the column: situacao_conformidade
	 */
	public java.lang.Long getSituacaoConformidade () {
		return getPropertyValue(this, situacaoConformidade, PROP_SITUACAO_CONFORMIDADE); 
	}

	/**
	 * Set the value related to the column: situacao_conformidade
	 * @param situacaoConformidade the situacao_conformidade value
	 */
	public void setSituacaoConformidade (java.lang.Long situacaoConformidade) {
//        java.lang.Long situacaoConformidadeOld = this.situacaoConformidade;
		this.situacaoConformidade = situacaoConformidade;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoConformidade", situacaoConformidadeOld, situacaoConformidade);
	}



	/**
	 * Return the value associated with the column: num_parecer_conformidade_tecnica
	 */
	public java.lang.Long getNumeracaoParecerConformidadeTecnica () {
		return getPropertyValue(this, numeracaoParecerConformidadeTecnica, PROP_NUMERACAO_PARECER_CONFORMIDADE_TECNICA); 
	}

	/**
	 * Set the value related to the column: num_parecer_conformidade_tecnica
	 * @param numeracaoParecerConformidadeTecnica the num_parecer_conformidade_tecnica value
	 */
	public void setNumeracaoParecerConformidadeTecnica (java.lang.Long numeracaoParecerConformidadeTecnica) {
//        java.lang.Long numeracaoParecerConformidadeTecnicaOld = this.numeracaoParecerConformidadeTecnica;
		this.numeracaoParecerConformidadeTecnica = numeracaoParecerConformidadeTecnica;
//        this.getPropertyChangeSupport().firePropertyChange ("numeracaoParecerConformidadeTecnica", numeracaoParecerConformidadeTecnicaOld, numeracaoParecerConformidadeTecnica);
	}



	/**
	 * Return the value associated with the column: dt_inspecao
	 */
	public java.util.Date getDataInspecao () {
		return getPropertyValue(this, dataInspecao, PROP_DATA_INSPECAO); 
	}

	/**
	 * Set the value related to the column: dt_inspecao
	 * @param dataInspecao the dt_inspecao value
	 */
	public void setDataInspecao (java.util.Date dataInspecao) {
//        java.util.Date dataInspecaoOld = this.dataInspecao;
		this.dataInspecao = dataInspecao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInspecao", dataInspecaoOld, dataInspecao);
	}



	/**
	 * Return the value associated with the column: ds_objetivo
	 */
	public java.lang.String getDescricaoObjetivo () {
		return getPropertyValue(this, descricaoObjetivo, PROP_DESCRICAO_OBJETIVO); 
	}

	/**
	 * Set the value related to the column: ds_objetivo
	 * @param descricaoObjetivo the ds_objetivo value
	 */
	public void setDescricaoObjetivo (java.lang.String descricaoObjetivo) {
//        java.lang.String descricaoObjetivoOld = this.descricaoObjetivo;
		this.descricaoObjetivo = descricaoObjetivo;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoObjetivo", descricaoObjetivoOld, descricaoObjetivo);
	}



	/**
	 * Return the value associated with the column: ds_constatacoes
	 */
	public java.lang.String getDescricaoConstatacoes () {
		return getPropertyValue(this, descricaoConstatacoes, PROP_DESCRICAO_CONSTATACOES); 
	}

	/**
	 * Set the value related to the column: ds_constatacoes
	 * @param descricaoConstatacoes the ds_constatacoes value
	 */
	public void setDescricaoConstatacoes (java.lang.String descricaoConstatacoes) {
//        java.lang.String descricaoConstatacoesOld = this.descricaoConstatacoes;
		this.descricaoConstatacoes = descricaoConstatacoes;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoConstatacoes", descricaoConstatacoesOld, descricaoConstatacoes);
	}



	/**
	 * Return the value associated with the column: ds_ressalvas
	 */
	public java.lang.String getDescricaoRessalvas () {
		return getPropertyValue(this, descricaoRessalvas, PROP_DESCRICAO_RESSALVAS); 
	}

	/**
	 * Set the value related to the column: ds_ressalvas
	 * @param descricaoRessalvas the ds_ressalvas value
	 */
	public void setDescricaoRessalvas (java.lang.String descricaoRessalvas) {
//        java.lang.String descricaoRessalvasOld = this.descricaoRessalvas;
		this.descricaoRessalvas = descricaoRessalvas;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoRessalvas", descricaoRessalvasOld, descricaoRessalvas);
	}



	/**
	 * Return the value associated with the column: ds_conclusao
	 */
	public java.lang.String getDescricaoConclusao () {
		return getPropertyValue(this, descricaoConclusao, PROP_DESCRICAO_CONCLUSAO); 
	}

	/**
	 * Set the value related to the column: ds_conclusao
	 * @param descricaoConclusao the ds_conclusao value
	 */
	public void setDescricaoConclusao (java.lang.String descricaoConclusao) {
//        java.lang.String descricaoConclusaoOld = this.descricaoConclusao;
		this.descricaoConclusao = descricaoConclusao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoConclusao", descricaoConclusaoOld, descricaoConclusao);
	}



	/**
	 * Return the value associated with the column: data_saida
	 */
	public java.util.Date getDataSaida () {
		return getPropertyValue(this, dataSaida, PROP_DATA_SAIDA); 
	}

	/**
	 * Set the value related to the column: data_saida
	 * @param dataSaida the data_saida value
	 */
	public void setDataSaida (java.util.Date dataSaida) {
//        java.util.Date dataSaidaOld = this.dataSaida;
		this.dataSaida = dataSaida;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSaida", dataSaidaOld, dataSaida);
	}



	/**
	 * Return the value associated with the column: data_retorno
	 */
	public java.util.Date getDataRetorno () {
		return getPropertyValue(this, dataRetorno, PROP_DATA_RETORNO); 
	}

	/**
	 * Set the value related to the column: data_retorno
	 * @param dataRetorno the data_retorno value
	 */
	public void setDataRetorno (java.util.Date dataRetorno) {
//        java.util.Date dataRetornoOld = this.dataRetorno;
		this.dataRetorno = dataRetorno;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRetorno", dataRetornoOld, dataRetorno);
	}



	/**
	 * Return the value associated with the column: cd_requerimento_vistoria_pba
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura getRequerimentoVistoriaProjetoBasicoArquitetura () {
		return getPropertyValue(this, requerimentoVistoriaProjetoBasicoArquitetura, PROP_REQUERIMENTO_VISTORIA_PROJETO_BASICO_ARQUITETURA); 
	}

	/**
	 * Set the value related to the column: cd_requerimento_vistoria_pba
	 * @param requerimentoVistoriaProjetoBasicoArquitetura the cd_requerimento_vistoria_pba value
	 */
	public void setRequerimentoVistoriaProjetoBasicoArquitetura (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquiteturaOld = this.requerimentoVistoriaProjetoBasicoArquitetura;
		this.requerimentoVistoriaProjetoBasicoArquitetura = requerimentoVistoriaProjetoBasicoArquitetura;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVistoriaProjetoBasicoArquitetura", requerimentoVistoriaProjetoBasicoArquiteturaOld, requerimentoVistoriaProjetoBasicoArquitetura);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnica)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnica requerimentoVistoriaPBAConformidadeTecnica = (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaPBAConformidadeTecnica) obj;
			if (null == this.getCodigo() || null == requerimentoVistoriaPBAConformidadeTecnica.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoVistoriaPBAConformidadeTecnica.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}