package br.com.ksisolucoes.vo.prontuario.basico.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the antecedentes_obstetricos_familiar table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="antecedentes_obstetricos_familiar"
 */

public abstract class BaseAntecedentesObstetricosFamiliar extends BaseRootVO implements Serializable {

	public static String REF = "AntecedentesObstetricosFamiliar";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_USUARIO_CAD_SUS = "usuarioCadSus";
	public static final String PROP_CIAP = "ciap";


	// constructors
	public BaseAntecedentesObstetricosFamiliar () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseAntecedentesObstetricosFamiliar (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadSus;
	private br.com.ksisolucoes.vo.prontuario.basico.Ciap ciap;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_antecedentes_obstetricos_familiar"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_usu_cadsus
	 */
	public br.com.ksisolucoes.vo.cadsus.UsuarioCadsus getUsuarioCadSus () {
		return getPropertyValue(this, usuarioCadSus, PROP_USUARIO_CAD_SUS); 
	}

	/**
	 * Set the value related to the column: cd_usu_cadsus
	 * @param usuarioCadSus the cd_usu_cadsus value
	 */
	public void setUsuarioCadSus (br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadSus) {
//        br.com.ksisolucoes.vo.cadsus.UsuarioCadsus usuarioCadSusOld = this.usuarioCadSus;
		this.usuarioCadSus = usuarioCadSus;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioCadSus", usuarioCadSusOld, usuarioCadSus);
	}



	/**
	 * Return the value associated with the column: cd_ciap
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Ciap getCiap () {
		return getPropertyValue(this, ciap, PROP_CIAP); 
	}

	/**
	 * Set the value related to the column: cd_ciap
	 * @param ciap the cd_ciap value
	 */
	public void setCiap (br.com.ksisolucoes.vo.prontuario.basico.Ciap ciap) {
//        br.com.ksisolucoes.vo.prontuario.basico.Ciap ciapOld = this.ciap;
		this.ciap = ciap;
//        this.getPropertyChangeSupport().firePropertyChange ("ciap", ciapOld, ciap);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.AntecedentesObstetricosFamiliar)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.AntecedentesObstetricosFamiliar antecedentesObstetricosFamiliar = (br.com.ksisolucoes.vo.prontuario.basico.AntecedentesObstetricosFamiliar) obj;
			if (null == this.getCodigo() || null == antecedentesObstetricosFamiliar.getCodigo()) return false;
			else return (this.getCodigo().equals(antecedentesObstetricosFamiliar.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}