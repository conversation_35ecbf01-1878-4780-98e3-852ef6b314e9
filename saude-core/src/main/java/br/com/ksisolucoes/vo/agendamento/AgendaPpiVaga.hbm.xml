<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.agendamento"  >
	<class 
		name="AgendaPpiVaga"
		table="agenda_ppi_vaga"
	>

		<id
			name="codigo" 
			type="java.lang.Long"   
			column="cd_agenda_ppi"  
		> 
			<generator class="assigned"/>

		</id> <version column="version" name="version" type="long" />			

         <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                name="empresa"
        >
            	<column name="empresa_ppi"/>
        </many-to-one>

        <property
                name="quantidadeVagaTotal"
                column="qt_vaga_total"
                type="java.lang.Long"
                not-null="true"
        />

	</class>
</hibernate-mapping>