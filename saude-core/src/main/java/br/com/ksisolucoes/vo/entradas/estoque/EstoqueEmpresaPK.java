package br.com.ksisolucoes.vo.entradas.estoque;

import br.com.ksisolucoes.vo.entradas.estoque.base.BaseEstoqueEmpresaPK;

public class EstoqueEmpresaPK extends BaseEstoqueEmpresaPK {

    public static String PROP_PRODUTO = "produto";

    public static String PROP_EMPRESA = "empresa";

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstoqueEmpresaPK () {}
	
	public EstoqueEmpresaPK (
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.basico.Empresa empresa) {

		super (
			produto,
			empresa);
	}
		/*[CONSTRUCTOR MARKER END]*/
}