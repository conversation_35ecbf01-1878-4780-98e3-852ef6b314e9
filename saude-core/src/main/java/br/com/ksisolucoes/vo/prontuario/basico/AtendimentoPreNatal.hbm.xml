<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class name="AtendimentoPreNatal" table="atendimento_prenatal">
		
        <id
            column="cd_atend_prenatal"
            name="codigo"
            type="java.lang.Long"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
        
        <many-to-one 
            column="cd_prenatal"
            name="preNatal"
            class="br.com.ksisolucoes.vo.prontuario.basico.PreNatal"
            not-null="true"
         />
        
        <many-to-one 
            column="nr_atendimento"
            name="atendimento"
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.integracao.prenatal.SisprenatalProcessoItem"
            column="cd_sisprenatal_processo_item"
            name="sisprenatalProcessoItem"
            not-null="false"
        />
        
    </class>
</hibernate-mapping>