<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos"  >
    <class name="RequerimentoProjetoHidrossanitarioParecerFiscal" table="req_pro_hidros_parecer_fiscal">
        <id
            column="cd_req_pro_hidros_parecer_fiscal"
            name="codigo"
            type="java.lang.Long"
        >
             <generator class="sequence">
                <param name="sequence">seq_req_pro_hidros_parecer_fiscal</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />

 		<many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer"
            column="cd_req_pro_hidros_parecer"
            name="requerimentoProjetoHidrossanitarioParecer"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilanciaFiscal"
            column="cd_req_vig_fiscal"
            name="requerimentoVigilanciaFiscal"
            not-null="true"
        />
        
    </class>
</hibernate-mapping>
