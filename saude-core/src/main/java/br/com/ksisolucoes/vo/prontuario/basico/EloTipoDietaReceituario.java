package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseEloTipoDietaReceituario;



public class EloTipoDietaReceituario extends BaseEloTipoDietaReceituario implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EloTipoDietaReceituario () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EloTipoDietaReceituario (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EloTipoDietaReceituario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.prontuario.hospital.TipoDieta tipoDieta,
		br.com.ksisolucoes.vo.prontuario.basico.Receituario receituario) {

		super (
			codigo,
			tipoDieta,
			receituario);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}