<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.basico"  >
    <class 
        name="ProducaoCadastroEquipeConfiguracao"
        table="producao_cadastro_equipe_cfg"
    >

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_producao_equipe_cfg"
        >
            <generator class="sequence">
                <param name="sequence">seq_gem</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />

        <property
            name="emailDestinatario"
            column="email_destinatario"
            type="java.lang.String"
        />
        
        <property
            name="emailAssunto"
            column="email_assunto"
            type="java.lang.String"
            length="512"
        />
        
        <property
            name="emailCorpo"
            column="email_corpo"
            type="java.lang.String"
        />
        
        <property
            name="tipoRelatorio"
            column="tp_relatorio"
            type="java.lang.Long"
        />
        
    </class>
</hibernate-mapping>