package br.com.ksisolucoes.vo.cadsus.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the endereco_domicilio_esus table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="endereco_domicilio_esus"
 */

public abstract class BaseEnderecoDomicilioEsus extends BaseRootVO implements Serializable {

	public static String REF = "EnderecoDomicilioEsus";
	public static final String PROP_PROFISSIONAL_FORA_AREA = "profissionalForaArea";
	public static final String PROP_CARGO_RESPONSAVEL_TECNICO = "cargoResponsavelTecnico";
	public static final String PROP_LOCAL_PROLIFERACAO_MOSQUITO = "localProliferacaoMosquito";
	public static final String PROP_NUMERO_CARTAO_RESPONSAVEL_TECNICO = "numeroCartaoResponsavelTecnico";
	public static final String PROP_LATITUDE = "latitude";
	public static final String PROP_TELEFONE_RESPONSAVEL_TECNICO = "telefoneResponsavelTecnico";
	public static final String PROP_TRATAMENTO_AGUA = "tratamentoAgua";
	public static final String PROP_CACHORRO = "cachorro";
	public static final String PROP_QUANTOS = "quantos";
	public static final String PROP_ABASTECIMENTO_AGUA = "abastecimentoAgua";
	public static final String PROP_PASSARO = "passaro";
	public static final String PROP_VERSION_ALL = "versionAll";
	public static final String PROP_GATO = "gato";
	public static final String PROP_TIPO_DOMICILIO = "tipoDomicilio";
	public static final String PROP_NOME_INSTITUICAO_PERMANECIA = "nomeInstituicaoPermanecia";
	public static final String PROP_ESGOTAMENTO = "esgotamento";
	public static final String PROP_MATERIAL_DOMINANTE = "materialDominante";
	public static final String PROP_OUTROS = "outros";
	public static final String PROP_FLAG_FORA_AREA = "flagForaArea";
	public static final String PROP_DESTINO_LIXO = "destinoLixo";
	public static final String PROP_TIPO_ACESSO_DOMICILIO = "tipoAcessoDomicilio";
	public static final String PROP_TIPO_IMOVEL = "tipoImovel";
	public static final String PROP_SITUACAO_MORADIA = "situacaoMoradia";
	public static final String PROP_FLAG_OUTROS_PROFISSIONAIS = "flagOutrosProfissionais";
	public static final String PROP_EMPRESA_FORA_AREA = "empresaForaArea";
	public static final String PROP_DATA_COLETA_GPS = "dataColetaGps";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_NUMERO_MORADORES = "numeroMoradores";
	public static final String PROP_CONDICAO_USO_TERRA = "condicaoUsoTerra";
	public static final String PROP_LONGITUDE = "longitude";
	public static final String PROP_NUMERO_COMODOS = "numeroComodos";
	public static final String PROP_UUID_TABLET = "uuidTablet";
	public static final String PROP_ENDERECO_DOMICILIO = "enderecoDomicilio";
	public static final String PROP_LOCALIZACAO = "localizacao";
	public static final String PROP_POSSUI_ENERGIA_ELETRICA = "possuiEnergiaEletrica";
	public static final String PROP_CRIACAO = "criacao";
	public static final String PROP_NOME_RESPONSAVEL_TECNICO = "nomeResponsavelTecnico";


	// constructors
	public BaseEnderecoDomicilioEsus () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEnderecoDomicilioEsus (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEnderecoDomicilioEsus (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio enderecoDomicilio) {

		this.setCodigo(codigo);
		this.setEnderecoDomicilio(enderecoDomicilio);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long situacaoMoradia;
	private java.lang.Long localizacao;
	private java.lang.Long tipoDomicilio;
	private java.lang.Long numeroMoradores;
	private java.lang.Long numeroComodos;
	private java.lang.Long condicaoUsoTerra;
	private java.lang.Long tipoAcessoDomicilio;
	private java.lang.Long materialDominante;
	private java.lang.Long possuiEnergiaEletrica;
	private java.lang.Long abastecimentoAgua;
	private java.lang.Long tratamentoAgua;
	private java.lang.Long esgotamento;
	private java.lang.Long destinoLixo;
	private java.lang.Long gato;
	private java.lang.Long cachorro;
	private java.lang.Long passaro;
	private java.lang.Long criacao;
	private java.lang.Long outros;
	private java.lang.Long quantos;
	private java.lang.Long versionAll;
	private java.util.Date dataColetaGps;
	private java.lang.String longitude;
	private java.lang.String latitude;
	private java.lang.Long tipoImovel;
	private java.lang.String nomeInstituicaoPermanecia;
	private java.lang.Long flagOutrosProfissionais;
	private java.lang.String nomeResponsavelTecnico;
	private java.lang.Long numeroCartaoResponsavelTecnico;
	private java.lang.String cargoResponsavelTecnico;
	private java.lang.String telefoneResponsavelTecnico;
	private java.lang.Long localProliferacaoMosquito;
	private java.lang.Long flagForaArea;
	private java.lang.String uuidTablet;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio enderecoDomicilio;
	private br.com.ksisolucoes.vo.basico.Empresa empresaForaArea;
	private br.com.ksisolucoes.vo.cadsus.Profissional profissionalForaArea;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_end_dom_esus"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: situacao_moradia
	 */
	public java.lang.Long getSituacaoMoradia () {
		return getPropertyValue(this, situacaoMoradia, PROP_SITUACAO_MORADIA); 
	}

	/**
	 * Set the value related to the column: situacao_moradia
	 * @param situacaoMoradia the situacao_moradia value
	 */
	public void setSituacaoMoradia (java.lang.Long situacaoMoradia) {
//        java.lang.Long situacaoMoradiaOld = this.situacaoMoradia;
		this.situacaoMoradia = situacaoMoradia;
//        this.getPropertyChangeSupport().firePropertyChange ("situacaoMoradia", situacaoMoradiaOld, situacaoMoradia);
	}



	/**
	 * Return the value associated with the column: localizacao
	 */
	public java.lang.Long getLocalizacao () {
		return getPropertyValue(this, localizacao, PROP_LOCALIZACAO); 
	}

	/**
	 * Set the value related to the column: localizacao
	 * @param localizacao the localizacao value
	 */
	public void setLocalizacao (java.lang.Long localizacao) {
//        java.lang.Long localizacaoOld = this.localizacao;
		this.localizacao = localizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacao", localizacaoOld, localizacao);
	}



	/**
	 * Return the value associated with the column: tipo_domicilio
	 */
	public java.lang.Long getTipoDomicilio () {
		return getPropertyValue(this, tipoDomicilio, PROP_TIPO_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: tipo_domicilio
	 * @param tipoDomicilio the tipo_domicilio value
	 */
	public void setTipoDomicilio (java.lang.Long tipoDomicilio) {
//        java.lang.Long tipoDomicilioOld = this.tipoDomicilio;
		this.tipoDomicilio = tipoDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoDomicilio", tipoDomicilioOld, tipoDomicilio);
	}



	/**
	 * Return the value associated with the column: nr_moradores
	 */
	public java.lang.Long getNumeroMoradores () {
		return getPropertyValue(this, numeroMoradores, PROP_NUMERO_MORADORES); 
	}

	/**
	 * Set the value related to the column: nr_moradores
	 * @param numeroMoradores the nr_moradores value
	 */
	public void setNumeroMoradores (java.lang.Long numeroMoradores) {
//        java.lang.Long numeroMoradoresOld = this.numeroMoradores;
		this.numeroMoradores = numeroMoradores;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroMoradores", numeroMoradoresOld, numeroMoradores);
	}



	/**
	 * Return the value associated with the column: nr_comodos
	 */
	public java.lang.Long getNumeroComodos () {
		return getPropertyValue(this, numeroComodos, PROP_NUMERO_COMODOS); 
	}

	/**
	 * Set the value related to the column: nr_comodos
	 * @param numeroComodos the nr_comodos value
	 */
	public void setNumeroComodos (java.lang.Long numeroComodos) {
//        java.lang.Long numeroComodosOld = this.numeroComodos;
		this.numeroComodos = numeroComodos;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroComodos", numeroComodosOld, numeroComodos);
	}



	/**
	 * Return the value associated with the column: cond_uso_terra
	 */
	public java.lang.Long getCondicaoUsoTerra () {
		return getPropertyValue(this, condicaoUsoTerra, PROP_CONDICAO_USO_TERRA); 
	}

	/**
	 * Set the value related to the column: cond_uso_terra
	 * @param condicaoUsoTerra the cond_uso_terra value
	 */
	public void setCondicaoUsoTerra (java.lang.Long condicaoUsoTerra) {
//        java.lang.Long condicaoUsoTerraOld = this.condicaoUsoTerra;
		this.condicaoUsoTerra = condicaoUsoTerra;
//        this.getPropertyChangeSupport().firePropertyChange ("condicaoUsoTerra", condicaoUsoTerraOld, condicaoUsoTerra);
	}



	/**
	 * Return the value associated with the column: tipo_acesso_domicilio
	 */
	public java.lang.Long getTipoAcessoDomicilio () {
		return getPropertyValue(this, tipoAcessoDomicilio, PROP_TIPO_ACESSO_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: tipo_acesso_domicilio
	 * @param tipoAcessoDomicilio the tipo_acesso_domicilio value
	 */
	public void setTipoAcessoDomicilio (java.lang.Long tipoAcessoDomicilio) {
//        java.lang.Long tipoAcessoDomicilioOld = this.tipoAcessoDomicilio;
		this.tipoAcessoDomicilio = tipoAcessoDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoAcessoDomicilio", tipoAcessoDomicilioOld, tipoAcessoDomicilio);
	}



	/**
	 * Return the value associated with the column: material_dom
	 */
	public java.lang.Long getMaterialDominante () {
		return getPropertyValue(this, materialDominante, PROP_MATERIAL_DOMINANTE); 
	}

	/**
	 * Set the value related to the column: material_dom
	 * @param materialDominante the material_dom value
	 */
	public void setMaterialDominante (java.lang.Long materialDominante) {
//        java.lang.Long materialDominanteOld = this.materialDominante;
		this.materialDominante = materialDominante;
//        this.getPropertyChangeSupport().firePropertyChange ("materialDominante", materialDominanteOld, materialDominante);
	}



	/**
	 * Return the value associated with the column: possui_energia_elet
	 */
	public java.lang.Long getPossuiEnergiaEletrica () {
		return getPropertyValue(this, possuiEnergiaEletrica, PROP_POSSUI_ENERGIA_ELETRICA); 
	}

	/**
	 * Set the value related to the column: possui_energia_elet
	 * @param possuiEnergiaEletrica the possui_energia_elet value
	 */
	public void setPossuiEnergiaEletrica (java.lang.Long possuiEnergiaEletrica) {
//        java.lang.Long possuiEnergiaEletricaOld = this.possuiEnergiaEletrica;
		this.possuiEnergiaEletrica = possuiEnergiaEletrica;
//        this.getPropertyChangeSupport().firePropertyChange ("possuiEnergiaEletrica", possuiEnergiaEletricaOld, possuiEnergiaEletrica);
	}



	/**
	 * Return the value associated with the column: abastecimento_agua
	 */
	public java.lang.Long getAbastecimentoAgua () {
		return getPropertyValue(this, abastecimentoAgua, PROP_ABASTECIMENTO_AGUA); 
	}

	/**
	 * Set the value related to the column: abastecimento_agua
	 * @param abastecimentoAgua the abastecimento_agua value
	 */
	public void setAbastecimentoAgua (java.lang.Long abastecimentoAgua) {
//        java.lang.Long abastecimentoAguaOld = this.abastecimentoAgua;
		this.abastecimentoAgua = abastecimentoAgua;
//        this.getPropertyChangeSupport().firePropertyChange ("abastecimentoAgua", abastecimentoAguaOld, abastecimentoAgua);
	}



	/**
	 * Return the value associated with the column: tratamento_agua
	 */
	public java.lang.Long getTratamentoAgua () {
		return getPropertyValue(this, tratamentoAgua, PROP_TRATAMENTO_AGUA); 
	}

	/**
	 * Set the value related to the column: tratamento_agua
	 * @param tratamentoAgua the tratamento_agua value
	 */
	public void setTratamentoAgua (java.lang.Long tratamentoAgua) {
//        java.lang.Long tratamentoAguaOld = this.tratamentoAgua;
		this.tratamentoAgua = tratamentoAgua;
//        this.getPropertyChangeSupport().firePropertyChange ("tratamentoAgua", tratamentoAguaOld, tratamentoAgua);
	}



	/**
	 * Return the value associated with the column: esgotamento
	 */
	public java.lang.Long getEsgotamento () {
		return getPropertyValue(this, esgotamento, PROP_ESGOTAMENTO); 
	}

	/**
	 * Set the value related to the column: esgotamento
	 * @param esgotamento the esgotamento value
	 */
	public void setEsgotamento (java.lang.Long esgotamento) {
//        java.lang.Long esgotamentoOld = this.esgotamento;
		this.esgotamento = esgotamento;
//        this.getPropertyChangeSupport().firePropertyChange ("esgotamento", esgotamentoOld, esgotamento);
	}



	/**
	 * Return the value associated with the column: destino_lixo
	 */
	public java.lang.Long getDestinoLixo () {
		return getPropertyValue(this, destinoLixo, PROP_DESTINO_LIXO); 
	}

	/**
	 * Set the value related to the column: destino_lixo
	 * @param destinoLixo the destino_lixo value
	 */
	public void setDestinoLixo (java.lang.Long destinoLixo) {
//        java.lang.Long destinoLixoOld = this.destinoLixo;
		this.destinoLixo = destinoLixo;
//        this.getPropertyChangeSupport().firePropertyChange ("destinoLixo", destinoLixoOld, destinoLixo);
	}



	/**
	 * Return the value associated with the column: gato
	 */
	public java.lang.Long getGato () {
		return getPropertyValue(this, gato, PROP_GATO); 
	}

	/**
	 * Set the value related to the column: gato
	 * @param gato the gato value
	 */
	public void setGato (java.lang.Long gato) {
//        java.lang.Long gatoOld = this.gato;
		this.gato = gato;
//        this.getPropertyChangeSupport().firePropertyChange ("gato", gatoOld, gato);
	}



	/**
	 * Return the value associated with the column: cachorro
	 */
	public java.lang.Long getCachorro () {
		return getPropertyValue(this, cachorro, PROP_CACHORRO); 
	}

	/**
	 * Set the value related to the column: cachorro
	 * @param cachorro the cachorro value
	 */
	public void setCachorro (java.lang.Long cachorro) {
//        java.lang.Long cachorroOld = this.cachorro;
		this.cachorro = cachorro;
//        this.getPropertyChangeSupport().firePropertyChange ("cachorro", cachorroOld, cachorro);
	}



	/**
	 * Return the value associated with the column: passaro
	 */
	public java.lang.Long getPassaro () {
		return getPropertyValue(this, passaro, PROP_PASSARO); 
	}

	/**
	 * Set the value related to the column: passaro
	 * @param passaro the passaro value
	 */
	public void setPassaro (java.lang.Long passaro) {
//        java.lang.Long passaroOld = this.passaro;
		this.passaro = passaro;
//        this.getPropertyChangeSupport().firePropertyChange ("passaro", passaroOld, passaro);
	}



	/**
	 * Return the value associated with the column: criacao
	 */
	public java.lang.Long getCriacao () {
		return getPropertyValue(this, criacao, PROP_CRIACAO); 
	}

	/**
	 * Set the value related to the column: criacao
	 * @param criacao the criacao value
	 */
	public void setCriacao (java.lang.Long criacao) {
//        java.lang.Long criacaoOld = this.criacao;
		this.criacao = criacao;
//        this.getPropertyChangeSupport().firePropertyChange ("criacao", criacaoOld, criacao);
	}



	/**
	 * Return the value associated with the column: outros
	 */
	public java.lang.Long getOutros () {
		return getPropertyValue(this, outros, PROP_OUTROS); 
	}

	/**
	 * Set the value related to the column: outros
	 * @param outros the outros value
	 */
	public void setOutros (java.lang.Long outros) {
//        java.lang.Long outrosOld = this.outros;
		this.outros = outros;
//        this.getPropertyChangeSupport().firePropertyChange ("outros", outrosOld, outros);
	}



	/**
	 * Return the value associated with the column: quantos
	 */
	public java.lang.Long getQuantos () {
		return getPropertyValue(this, quantos, PROP_QUANTOS); 
	}

	/**
	 * Set the value related to the column: quantos
	 * @param quantos the quantos value
	 */
	public void setQuantos (java.lang.Long quantos) {
//        java.lang.Long quantosOld = this.quantos;
		this.quantos = quantos;
//        this.getPropertyChangeSupport().firePropertyChange ("quantos", quantosOld, quantos);
	}



	/**
	 * Return the value associated with the column: version_all
	 */
	public java.lang.Long getVersionAll () {
		return getPropertyValue(this, versionAll, PROP_VERSION_ALL); 
	}

	/**
	 * Set the value related to the column: version_all
	 * @param versionAll the version_all value
	 */
	public void setVersionAll (java.lang.Long versionAll) {
//        java.lang.Long versionAllOld = this.versionAll;
		this.versionAll = versionAll;
//        this.getPropertyChangeSupport().firePropertyChange ("versionAll", versionAllOld, versionAll);
	}



	/**
	 * Return the value associated with the column: dt_coleta_gps
	 */
	public java.util.Date getDataColetaGps () {
		return getPropertyValue(this, dataColetaGps, PROP_DATA_COLETA_GPS); 
	}

	/**
	 * Set the value related to the column: dt_coleta_gps
	 * @param dataColetaGps the dt_coleta_gps value
	 */
	public void setDataColetaGps (java.util.Date dataColetaGps) {
//        java.util.Date dataColetaGpsOld = this.dataColetaGps;
		this.dataColetaGps = dataColetaGps;
//        this.getPropertyChangeSupport().firePropertyChange ("dataColetaGps", dataColetaGpsOld, dataColetaGps);
	}



	/**
	 * Return the value associated with the column: longitude
	 */
	public java.lang.String getLongitude () {
		return getPropertyValue(this, longitude, PROP_LONGITUDE); 
	}

	/**
	 * Set the value related to the column: longitude
	 * @param longitude the longitude value
	 */
	public void setLongitude (java.lang.String longitude) {
//        java.lang.String longitudeOld = this.longitude;
		this.longitude = longitude;
//        this.getPropertyChangeSupport().firePropertyChange ("longitude", longitudeOld, longitude);
	}



	/**
	 * Return the value associated with the column: latitude
	 */
	public java.lang.String getLatitude () {
		return getPropertyValue(this, latitude, PROP_LATITUDE); 
	}

	/**
	 * Set the value related to the column: latitude
	 * @param latitude the latitude value
	 */
	public void setLatitude (java.lang.String latitude) {
//        java.lang.String latitudeOld = this.latitude;
		this.latitude = latitude;
//        this.getPropertyChangeSupport().firePropertyChange ("latitude", latitudeOld, latitude);
	}



	/**
	 * Return the value associated with the column: tp_imovel
	 */
	public java.lang.Long getTipoImovel () {
		return getPropertyValue(this, tipoImovel, PROP_TIPO_IMOVEL); 
	}

	/**
	 * Set the value related to the column: tp_imovel
	 * @param tipoImovel the tp_imovel value
	 */
	public void setTipoImovel (java.lang.Long tipoImovel) {
//        java.lang.Long tipoImovelOld = this.tipoImovel;
		this.tipoImovel = tipoImovel;
//        this.getPropertyChangeSupport().firePropertyChange ("tipoImovel", tipoImovelOld, tipoImovel);
	}



	/**
	 * Return the value associated with the column: nm_instituicao_permanecia
	 */
	public java.lang.String getNomeInstituicaoPermanecia () {
		return getPropertyValue(this, nomeInstituicaoPermanecia, PROP_NOME_INSTITUICAO_PERMANECIA); 
	}

	/**
	 * Set the value related to the column: nm_instituicao_permanecia
	 * @param nomeInstituicaoPermanecia the nm_instituicao_permanecia value
	 */
	public void setNomeInstituicaoPermanecia (java.lang.String nomeInstituicaoPermanecia) {
//        java.lang.String nomeInstituicaoPermaneciaOld = this.nomeInstituicaoPermanecia;
		this.nomeInstituicaoPermanecia = nomeInstituicaoPermanecia;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeInstituicaoPermanecia", nomeInstituicaoPermaneciaOld, nomeInstituicaoPermanecia);
	}



	/**
	 * Return the value associated with the column: flag_outros_profissionais
	 */
	public java.lang.Long getFlagOutrosProfissionais () {
		return getPropertyValue(this, flagOutrosProfissionais, PROP_FLAG_OUTROS_PROFISSIONAIS); 
	}

	/**
	 * Set the value related to the column: flag_outros_profissionais
	 * @param flagOutrosProfissionais the flag_outros_profissionais value
	 */
	public void setFlagOutrosProfissionais (java.lang.Long flagOutrosProfissionais) {
//        java.lang.Long flagOutrosProfissionaisOld = this.flagOutrosProfissionais;
		this.flagOutrosProfissionais = flagOutrosProfissionais;
//        this.getPropertyChangeSupport().firePropertyChange ("flagOutrosProfissionais", flagOutrosProfissionaisOld, flagOutrosProfissionais);
	}



	/**
	 * Return the value associated with the column: nm_resp_tecnico
	 */
	public java.lang.String getNomeResponsavelTecnico () {
		return getPropertyValue(this, nomeResponsavelTecnico, PROP_NOME_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: nm_resp_tecnico
	 * @param nomeResponsavelTecnico the nm_resp_tecnico value
	 */
	public void setNomeResponsavelTecnico (java.lang.String nomeResponsavelTecnico) {
//        java.lang.String nomeResponsavelTecnicoOld = this.nomeResponsavelTecnico;
		this.nomeResponsavelTecnico = nomeResponsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("nomeResponsavelTecnico", nomeResponsavelTecnicoOld, nomeResponsavelTecnico);
	}



	/**
	 * Return the value associated with the column: numero_cartao_resp_tecnico
	 */
	public java.lang.Long getNumeroCartaoResponsavelTecnico () {
		return getPropertyValue(this, numeroCartaoResponsavelTecnico, PROP_NUMERO_CARTAO_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: numero_cartao_resp_tecnico
	 * @param numeroCartaoResponsavelTecnico the numero_cartao_resp_tecnico value
	 */
	public void setNumeroCartaoResponsavelTecnico (java.lang.Long numeroCartaoResponsavelTecnico) {
//        java.lang.Long numeroCartaoResponsavelTecnicoOld = this.numeroCartaoResponsavelTecnico;
		this.numeroCartaoResponsavelTecnico = numeroCartaoResponsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroCartaoResponsavelTecnico", numeroCartaoResponsavelTecnicoOld, numeroCartaoResponsavelTecnico);
	}



	/**
	 * Return the value associated with the column: cargo_resp_tecnico
	 */
	public java.lang.String getCargoResponsavelTecnico () {
		return getPropertyValue(this, cargoResponsavelTecnico, PROP_CARGO_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: cargo_resp_tecnico
	 * @param cargoResponsavelTecnico the cargo_resp_tecnico value
	 */
	public void setCargoResponsavelTecnico (java.lang.String cargoResponsavelTecnico) {
//        java.lang.String cargoResponsavelTecnicoOld = this.cargoResponsavelTecnico;
		this.cargoResponsavelTecnico = cargoResponsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("cargoResponsavelTecnico", cargoResponsavelTecnicoOld, cargoResponsavelTecnico);
	}



	/**
	 * Return the value associated with the column: telefone_resp_tecnico
	 */
	public java.lang.String getTelefoneResponsavelTecnico () {
		return getPropertyValue(this, telefoneResponsavelTecnico, PROP_TELEFONE_RESPONSAVEL_TECNICO); 
	}

	/**
	 * Set the value related to the column: telefone_resp_tecnico
	 * @param telefoneResponsavelTecnico the telefone_resp_tecnico value
	 */
	public void setTelefoneResponsavelTecnico (java.lang.String telefoneResponsavelTecnico) {
//        java.lang.String telefoneResponsavelTecnicoOld = this.telefoneResponsavelTecnico;
		this.telefoneResponsavelTecnico = telefoneResponsavelTecnico;
//        this.getPropertyChangeSupport().firePropertyChange ("telefoneResponsavelTecnico", telefoneResponsavelTecnicoOld, telefoneResponsavelTecnico);
	}



	/**
	 * Return the value associated with the column: local_prolif_mosquito
	 */
	public java.lang.Long getLocalProliferacaoMosquito () {
		return getPropertyValue(this, localProliferacaoMosquito, PROP_LOCAL_PROLIFERACAO_MOSQUITO); 
	}

	/**
	 * Set the value related to the column: local_prolif_mosquito
	 * @param localProliferacaoMosquito the local_prolif_mosquito value
	 */
	public void setLocalProliferacaoMosquito (java.lang.Long localProliferacaoMosquito) {
//        java.lang.Long localProliferacaoMosquitoOld = this.localProliferacaoMosquito;
		this.localProliferacaoMosquito = localProliferacaoMosquito;
//        this.getPropertyChangeSupport().firePropertyChange ("localProliferacaoMosquito", localProliferacaoMosquitoOld, localProliferacaoMosquito);
	}



	/**
	 * Return the value associated with the column: flag_fora_area
	 */
	public java.lang.Long getFlagForaArea () {
		return getPropertyValue(this, flagForaArea, PROP_FLAG_FORA_AREA); 
	}

	/**
	 * Set the value related to the column: flag_fora_area
	 * @param flagForaArea the flag_fora_area value
	 */
	public void setFlagForaArea (java.lang.Long flagForaArea) {
//        java.lang.Long flagForaAreaOld = this.flagForaArea;
		this.flagForaArea = flagForaArea;
//        this.getPropertyChangeSupport().firePropertyChange ("flagForaArea", flagForaAreaOld, flagForaArea);
	}



	/**
	 * Return the value associated with the column: uuid_tablet
	 */
	public java.lang.String getUuidTablet () {
		return getPropertyValue(this, uuidTablet, PROP_UUID_TABLET); 
	}

	/**
	 * Set the value related to the column: uuid_tablet
	 * @param uuidTablet the uuid_tablet value
	 */
	public void setUuidTablet (java.lang.String uuidTablet) {
//        java.lang.String uuidTabletOld = this.uuidTablet;
		this.uuidTablet = uuidTablet;
//        this.getPropertyChangeSupport().firePropertyChange ("uuidTablet", uuidTabletOld, uuidTablet);
	}



	/**
	 * Return the value associated with the column: cd_domicilio
	 */
	public br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio getEnderecoDomicilio () {
		return getPropertyValue(this, enderecoDomicilio, PROP_ENDERECO_DOMICILIO); 
	}

	/**
	 * Set the value related to the column: cd_domicilio
	 * @param enderecoDomicilio the cd_domicilio value
	 */
	public void setEnderecoDomicilio (br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio enderecoDomicilio) {
//        br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio enderecoDomicilioOld = this.enderecoDomicilio;
		this.enderecoDomicilio = enderecoDomicilio;
//        this.getPropertyChangeSupport().firePropertyChange ("enderecoDomicilio", enderecoDomicilioOld, enderecoDomicilio);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaForaArea () {
		return getPropertyValue(this, empresaForaArea, PROP_EMPRESA_FORA_AREA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresaForaArea the empresa value
	 */
	public void setEmpresaForaArea (br.com.ksisolucoes.vo.basico.Empresa empresaForaArea) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaForaAreaOld = this.empresaForaArea;
		this.empresaForaArea = empresaForaArea;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaForaArea", empresaForaAreaOld, empresaForaArea);
	}



	/**
	 * Return the value associated with the column: cd_profissional
	 */
	public br.com.ksisolucoes.vo.cadsus.Profissional getProfissionalForaArea () {
		return getPropertyValue(this, profissionalForaArea, PROP_PROFISSIONAL_FORA_AREA); 
	}

	/**
	 * Set the value related to the column: cd_profissional
	 * @param profissionalForaArea the cd_profissional value
	 */
	public void setProfissionalForaArea (br.com.ksisolucoes.vo.cadsus.Profissional profissionalForaArea) {
//        br.com.ksisolucoes.vo.cadsus.Profissional profissionalForaAreaOld = this.profissionalForaArea;
		this.profissionalForaArea = profissionalForaArea;
//        this.getPropertyChangeSupport().firePropertyChange ("profissionalForaArea", profissionalForaAreaOld, profissionalForaArea);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus enderecoDomicilioEsus = (br.com.ksisolucoes.vo.cadsus.EnderecoDomicilioEsus) obj;
			if (null == this.getCodigo() || null == enderecoDomicilioEsus.getCodigo()) return false;
			else return (this.getCodigo().equals(enderecoDomicilioEsus.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}