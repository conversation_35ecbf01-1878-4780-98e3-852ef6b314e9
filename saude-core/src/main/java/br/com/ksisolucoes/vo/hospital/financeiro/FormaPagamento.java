package br.com.ksisolucoes.vo.hospital.financeiro;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.parametrogem.ParametroPanelConsultaBean;
import java.io.Serializable;

import br.com.ksisolucoes.vo.hospital.financeiro.base.BaseFormaPagamento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;



@ParametroPanelConsultaBean("br.com.celk.view.hospital.financeiro.autocomplete.AutoCompleteConsultaFormaPagamento")
public class FormaPagamento extends BaseFormaPagamento implements CodigoManager, PesquisaObjectInterface {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public FormaPagamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public FormaPagamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public FormaPagamento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.hospital.financeiro.TipoMovimentoContaFinanceira tipoMovimentoContaFinanceira,
		java.lang.String descricao) {

		super (
			codigo,
			tipoMovimentoContaFinanceira,
			descricao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    @Override
    public String getDescricaoVO() {
        return getDescricao();
    }

    @Override
    public String getIdentificador() {
        return Coalesce.asString(getCodigo());
    }
}