<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia.requerimentos"  >
    <class name="RequerimentoVistoriaHidrossanitarioDeclaratorioParecerResposta" table="req_vistoria_hidro_declaratorio_parecer_resp">
        <id
            column="cd_req_vistoria_hidro_declaratorio_parecer_resp"
            name="codigo"
            type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_req_vistoria_hidro_declaratorio_parecer_resp</param>
            </generator>
        </id> 
        <version column="version" name="version" type="long" />

 		<many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaHidrossanitarioDeclaratorioParecer"
            column="cd_req_vistoria_hidro_declaratorio_parecer"
            name="requerimentoVistoriaHidrossanitarioDeclaratorioParecer"
            not-null="true"
        />

        <property
                column="ds_resposta"
                name="descricaoResposta"
                type="java.lang.String"
        />

        <property
                name="dataResposta"
                column="dt_resposta"
                type="timestamp"
                not-null="true"
        />
        
        <property
            column="situacao"
            name="situacao"
            type="java.lang.Long"
        />

        <property
                column="dt_usuario"
                name="dataUsuario"
                type="java.util.Date"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />
    </class>
</hibernate-mapping>
