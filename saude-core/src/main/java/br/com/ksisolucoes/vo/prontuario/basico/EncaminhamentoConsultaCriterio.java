package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseEncaminhamentoConsultaCriterio;

import java.io.Serializable;



public class EncaminhamentoConsultaCriterio extends BaseEncaminhamentoConsultaCriterio implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EncaminhamentoConsultaCriterio () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EncaminhamentoConsultaCriterio (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EncaminhamentoConsultaCriterio (
		java.lang.Long codigo,
		java.lang.String descricaoCriterio,
		java.lang.Long flagObrigatorio) {

		super (
			codigo,
			descricaoCriterio,
			flagObrigatorio);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public String getDescricaoCriterioFormatado() {
    	return (RepositoryComponentDefault.SIM_LONG.equals(getFlagObrigatorio()) ? "* " : "") + getDescricaoCriterio();
	}

}