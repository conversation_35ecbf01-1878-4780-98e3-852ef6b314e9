package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseEstabelecimentoSetoresAtividade;

import java.io.Serializable;


public class EstabelecimentoSetoresAtividade extends BaseEstabelecimentoSetoresAtividade implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstabelecimentoSetoresAtividade() {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EstabelecimentoSetoresAtividade(Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EstabelecimentoSetoresAtividade(
            Long codigo,
            EstabelecimentoSetores estabelecimentoSetores,
            EstabelecimentoAtividade estabelecimentoAtividade) {

		super (
			codigo,
			estabelecimentoSetores,
			estabelecimentoAtividade);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

}