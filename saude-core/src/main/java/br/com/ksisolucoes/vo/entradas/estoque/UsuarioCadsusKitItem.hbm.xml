<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.entradas.estoque"  >
    <class name="UsuarioCadsusKitItem" table="usuario_cadsus_kit_item">
        <id
            name="codigo"
            column="cd_usuario_cadsus_kit_item"
            type="java.lang.Long"
        >
        	<generator class="assigned"/>
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.Produto"
                name="produto"
                not-null="true"
                column="cod_pro"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.entradas.estoque.UsuarioCadsusKit"
                name="usuarioCadsusKit"
                column="cd_usuario_cadsus_kit"
                not-null="true"
        />

        <property
                column="quantidade"
                name="quantidade"
                not-null="true"
                type="java.lang.Double"
        />


	
    </class>
</hibernate-mapping>
