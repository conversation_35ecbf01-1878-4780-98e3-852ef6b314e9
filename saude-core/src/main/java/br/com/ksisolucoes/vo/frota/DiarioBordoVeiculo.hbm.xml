<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.frota">
    <class name="DiarioBordoVeiculo" table="diario_bordo_veiculo">
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_diario_veiculo">
        </id>

        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.frota.Veiculo"
            name="veiculo"
            not-null="true"
        >
            <column name="cd_veiculo " />
        </many-to-one>

        <many-to-one
            class="br.com.ksisolucoes.vo.frota.Motorista"
            name="motorista"
            not-null="true"
        >
            <column name="cd_motorista " />
        </many-to-one>
        
        <many-to-one
            class="br.com.ksisolucoes.vo.frota.RoteiroViagem"
            column="cd_roteiro"
            name="roteiro"
            not-null="false"
        />

        <property
            name="dataCadastro"
            column="dt_cadastro"
            not-null="true"
            type="timestamp"
        />

        <property
            name="dataSaida"
            column="dt_saida"
            not-null="true"
            type="timestamp"
        />

        <property
            name="dataChegada"
            column="dt_chegada"
            not-null="true"
            type="timestamp"
        />

        <property
            name="kmInicial"
            column="km_inicial"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            name="kmFinal"
            column="km_final"
            type="java.lang.Long"
            not-null="true"
        />

        <property
            name="destino"
            column="destino"
            type="java.lang.String"
            length="100"
            not-null="true"
        />

        <property
            name="observacao"
            column="observacao"
            type="java.lang.String"
            length="512"
            not-null="false"
        />
                
        <property
            column="valor_despesa_viagem"
            name="valorDespesaViagem"
            not-null="false"
            type="java.lang.Double"
        />
                
        <property
            column="valor_despesa_veiculo"
            name="valorDespesaVeiculo"
            not-null="false"
            type="java.lang.Double"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.controle.Usuario"
            name="usuario"
            not-null="true"
        >
            <column name="cd_usuario" />
        </many-to-one>

        <property
            name="dataUsuario"
            column="dt_usuario"
            type="timestamp"
            not-null="true"
        />
    </class>
</hibernate-mapping>
