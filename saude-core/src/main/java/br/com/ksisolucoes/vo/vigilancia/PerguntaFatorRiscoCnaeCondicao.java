package br.com.ksisolucoes.vo.vigilancia;

import java.io.Serializable;

import br.com.ksisolucoes.vo.vigilancia.base.BasePerguntaFatorRiscoCnaeCondicao;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class PerguntaFatorRiscoCnaeCondicao extends BasePerguntaFatorRiscoCnaeCondicao implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public PerguntaFatorRiscoCnaeCondicao () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public PerguntaFatorRiscoCnaeCondicao (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}