package br.com.ksisolucoes.vo.vigilancia.investigacao;

import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadInterceptor;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.base.BaseClassificacaoCids;
import br.com.ksisolucoes.vo.basico.base.BaseFichaInvestigacaoAgravo;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemCovid19;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemMorbidadeCovid19;
import br.com.ksisolucoes.vo.cadsus.FormularioTriagemSintomasCovid19;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsus;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseCid;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseExameExterno;
import br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.agravo.base.BaseRegistroAgravo;
import br.com.ksisolucoes.vo.vigilancia.investigacao.base.BaseInvestigacaoAgravoCovid19;
import br.com.ksisolucoes.vo.vigilancia.investigacao.dto.ExameDto;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.*;


public class InvestigacaoAgravoCovid19 extends BaseInvestigacaoAgravoCovid19 implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public InvestigacaoAgravoCovid19() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public InvestigacaoAgravoCovid19(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public InvestigacaoAgravoCovid19(
            java.lang.Long codigo,
            br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
            java.lang.String flagInformacoesComplementares) {

        super(
                codigo,
                registroAgravo,
                flagInformacoesComplementares);
    }

    /*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public static InvestigacaoAgravoCovid19 buscaPorRegistroAgravo(RegistroAgravo registroAgravo) {
        InvestigacaoAgravoCovid19 investigacao = LoadManager.getInstance(InvestigacaoAgravoCovid19.class)
                .addProperties(new HQLProperties(InvestigacaoAgravoCovid19.class).getProperties())
                .addProperties(new HQLProperties(RegistroAgravo.class, VOUtils.montarPath(InvestigacaoAgravoCovid19.PROP_REGISTRO_AGRAVO)).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoCovid19.PROP_REGISTRO_AGRAVO, RegistroAgravo.PROP_CODIGO), registroAgravo.getCodigo()))
                .start().getVO();
        return investigacao;
    }

    public static List<InvestigacaoAgravoCovid19Rastreamento> buscaInvestigacaoAgravoCovid19Rastreamento(InvestigacaoAgravoCovid19 investigacaoAgravoCovid19) {
        if (investigacaoAgravoCovid19 == null) return new ArrayList();
        return LoadManager.getInstance(InvestigacaoAgravoCovid19Rastreamento.class)
                .addProperties(new HQLProperties(InvestigacaoAgravoCovid19Rastreamento.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(InvestigacaoAgravoCovid19Rastreamento.PROP_INVESTIGACAO_AGRAVO_COVID19), investigacaoAgravoCovid19))
                .start().getList();
    }

    public static boolean isRegistroAgravoCovidMaisRecente(UsuarioCadsus usuarioCadsus, Date dataCadastro) {
        List<Cid> cidCovid19 = LoadManager.getInstance(Cid.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseCid.PROP_CID_CLASSIFICACAO, BaseClassificacaoCids.PROP_FICHA_INVESTIGACAO_AGRAVO, BaseFichaInvestigacaoAgravo.PROP_CODIGO), 9L))
                .start().getList();
        Boolean existeMaisRecente = LoadManager.getInstance(RegistroAgravo.class)
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseRegistroAgravo.PROP_USUARIO_CADSUS, BaseUsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseRegistroAgravo.PROP_CID), QueryCustom.QueryCustomParameter.IN, cidCovid19))
                .addParameter(new QueryCustom.QueryCustomParameter(BaseRegistroAgravo.PROP_STATUS, BuilderQueryCustom.QueryParameter.IN, RegistroAgravo.statusAgravoInformarResultadoExameCovid))
                .addParameter(new QueryCustom.QueryCustomParameter(BaseRegistroAgravo.PROP_DATA_CADASTRO, BuilderQueryCustom.QueryParameter.MAIOR, dataCadastro))
                .setMaxResults(1)
                .exists();
        return !existeMaisRecente;
    }

    public static FormularioTriagemCovid19 buscaTriagemCovid19(RegistroAgravo registroAgravo) {
        FormularioTriagemCovid19 triagem = null;
        if (registroAgravo.getAtendimento() != null) {
            List<FormularioTriagemCovid19> listaTriagem = LoadManager.getInstance(FormularioTriagemCovid19.class)
                    .addProperties(new HQLProperties(FormularioTriagemCovid19.class).getProperties())
                    .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FormularioTriagemCovid19.PROP_ATENDIMENTO
                    ), registroAgravo.getAtendimento().getCodigo()))
                    .start().getList();

            if (listaTriagem != null && !listaTriagem.isEmpty()) {
                triagem = listaTriagem.get(listaTriagem.size() - 1);
            }
        }
        return triagem;
    }

    public static List<FormularioTriagemSintomasCovid19> listaSintomas(FormularioTriagemCovid19 triagem) {
        List<FormularioTriagemSintomasCovid19> lista = LoadManager.getInstance(FormularioTriagemSintomasCovid19.class)
                .addProperties(new HQLProperties(FormularioTriagemSintomasCovid19.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FormularioTriagemSintomasCovid19.PROP_FORMULARIO_TRIAGEM_COVID19
                ), triagem.getCodigo()))
                .start().getList();
        return lista;
    }


    public static List<FormularioTriagemMorbidadeCovid19> listaCondicoes(FormularioTriagemCovid19 triagem) {
        List<FormularioTriagemMorbidadeCovid19> lista = LoadManager.getInstance(FormularioTriagemMorbidadeCovid19.class)
                .addProperties(new HQLProperties(FormularioTriagemMorbidadeCovid19.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(FormularioTriagemMorbidadeCovid19.PROP_FORMULARIO_TRIAGEM_COVID19
                ), triagem.getCodigo()))
                .start().getList();
        return lista;
    }

    public static ExameDto buscarTesteRapidoCovid19(UsuarioCadsus usuarioCadsus) {
        List<?> filtroResultado = Arrays.asList(
                TesteRapidoRealizado.Resultado.REAGENTE.value(),
                TesteRapidoRealizado.Resultado.NAO_REAGENTE.value()
        );
        TesteRapidoRealizado testeRapidoRealizadoMaisRecente = LoadManager.getInstance(TesteRapidoRealizado.class)
                .addProperty(TesteRapidoRealizado.PROP_CODIGO)
                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_RESULTADO))
                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO, Atendimento.PROP_CODIGO))
                .addProperty(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO, Atendimento.PROP_DATA_ATENDIMENTO))
                .addParameter(new QueryCustom.QueryCustomParameter(TesteRapidoRealizado.PROP_STATUS, TesteRapidoRealizado.Status.CONCLUIDO.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO, Atendimento.PROP_USUARIO_CADSUS), usuarioCadsus))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(TesteRapidoRealizado.PROP_TIPO_TESTE_RAPIDO, TipoTesteRapido.PROP_TIPO_TESTE), TipoTesteRapido.TipoTeste.COVID_19.value()))
                .addParameter(new QueryCustom.QueryCustomParameter(TesteRapidoRealizado.PROP_RESULTADO, BuilderQueryCustom.QueryParameter.IN, filtroResultado))
                .addSorter(new QueryCustom.QueryCustomSorter(TesteRapidoRealizado.PROP_RESULTADO, QueryCustom.QueryCustomSorter.DECRESCENTE))
                .addSorter(new QueryCustom.QueryCustomSorter(VOUtils.montarPath(TesteRapidoRealizado.PROP_ATENDIMENTO_RESULTADO, Atendimento.PROP_DATA_ATENDIMENTO), QueryCustom.QueryCustomSorter.DECRESCENTE))
                .setMaxResults(1)
                .start().getVO();
        if (testeRapidoRealizadoMaisRecente != null) return new ExameDto(testeRapidoRealizadoMaisRecente);

        return null;
    }

    public static ExameDto buscarExamesExternosCovid19(UsuarioCadsus usuarioCadsus) {
        final List<String> descricoesExames = new ArrayList<>();
        descricoesExames.addAll(ExameExterno.DESCRICAO_EXAME_COVID_19_RT_PCR);
        descricoesExames.addAll(ExameExterno.DESCRICAO_EXAME_COVID_19_TESTE_RAPIDO);
        ExameExterno exameExternoMaisRecente = LoadManager.getInstance(ExameExterno.class)
                .addProperties(new HQLProperties(ExameExterno.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ExameExterno.PROP_USUARIO_CADSUS, usuarioCadsus))
                .addInterceptor(new LoadInterceptor() {
                    @Override
                    public void customHQL(HQLHelper hql, String alias) {
                        hql.addToWhereWhithAnd("upper(" + alias + ".descricao) in", descricoesExames);
                    }
                })
                .addSorter(new QueryCustom.QueryCustomSorter(BaseExameExterno.PROP_DATA_EXAME, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .setMaxResults(1)
                .start().getVO();
        if (exameExternoMaisRecente != null) {
            converterResultadosExamesExternosParaLong(exameExternoMaisRecente);
            return new ExameDto(exameExternoMaisRecente);
        }
        return null;
    }

    private static ExameExterno converterResultadosExamesExternosParaLong(ExameExterno exameExterno) {
        Long resultado = InvestigacaoAgravoCovid19.verificarResultadoTeste(exameExterno);
        exameExterno.setResultado(resultado == null ? null : resultado.toString());
        return exameExterno;
    }

    public static Long verificarResultadoTeste(ExameExterno exameExterno) {
        if (ExameExterno.DS_RESULTADO_POSITIVO_EXAME_COVID_19.contains(StringUtils.upperCase(exameExterno.getResultado()))) {
            return InvestigacaoAgravoCovid19.ResultadoTeste.POSITIVO.getValue();
        } else if (ExameExterno.DS_RESULTADO_NEGATIVO_EXAME_COVID_19.contains(StringUtils.upperCase(exameExterno.getResultado()))) {
            return InvestigacaoAgravoCovid19.ResultadoTeste.NEGATIVO.getValue();
        }
        return null;
    }

    public String getDataObitoFormatado() {
        return Data.formatar(this.getDataObito());
    }

    public enum ProfissionalSaude {

        SIM(1L),
        NAO(2L);

        private Long value;

        ProfissionalSaude(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum SimNao {

        SIM(1L),
        NAO(0L);

        private Long value;

        SimNao(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }


    public enum Sintomas {

        DOR_GARGANTA(1L),
        DISPNEIA(2L),
        FEBRE(3L),
        TOSSE(4L),
        OUTROS(5L);

        private Long value;

        Sintomas(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum Condicoes {
        DOENCAS_RESP_CRONICAS_DESCOMPENSADAS(1L),
        DOENCAS_CARDIACAS_CRONICAS(2L),
        DIABETES(3L),
        DOENCAS_RENAIS_CRONICAS(4L),
        GESTANTE_ALTO_RISCO(5L),
        PORTADOR_DOENCAS_CROMOSSOMICAS(6L);

        private Long value;

        Condicoes(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum EstadoTeste {

        SOLICITADO(1L),
        COLETADO(2L),
        CONCLUIDO(3L);

        private Long value;

        EstadoTeste(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }


    public enum TipoTeste {

        TESTE_RAPIDO_ANTICORPO(1L),
        TESTE_RAPIDO_ANTIGENO(2L),
        RT_PCR(3L),
        ENZIMAIMUNOENSAIO_ELISA(4L),
        ELETROQUIMIOLUMINESCENCIA_ECLIA(5L),
        QUIMIOLUMINESCENCIA_CLIA(6L);

        private Long value;

        TipoTeste(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum ResultadoTeste implements IEnum {

        POSITIVO(1L),
        NEGATIVO(2L),
        INCONCLUSIVO_INDETERMINADO(3L);

        private Long value;

        ResultadoTeste(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }

        public static ResultadoTeste valueOf(Long value) {
            for (ResultadoTeste origem : ResultadoTeste.values()) {
                if (origem.value().equals(value)) {
                    return origem;
                }
            }
            return null;
        }

        @Override
        public Object value() {
            return value;
        }

        @Override
        public String descricao() {
            return this.name();
        }
    }

    public enum ClassificacaoFinal {

        CONFIRMACAO_LABORATORIAL(1L),
        CONFIRMACAO_CLINICO_EPIDEMIOLOGICO(2L),
        DESCARTADO(3L),
        CONFIRMADO_CRITERIO_CLINICO(4L),
        CONFIRMADO_CLINICO_IMAGEM(5L),
        SINDROME_GRIPAL_NAO_ESPECIFICADA(6L);

        private Long value;

        ClassificacaoFinal(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum EvolucaoCaso {

        CANCELADO(1L),
        IGNORADO(2L),
        OBITO(3L),
        CURA(4L),
        INTERNADO(5L),
        INTERNADO_EM_UTI(6L),
        TRATAMENTO_DOMICILIAR(7L);

        private Long value;

        EvolucaoCaso(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    public enum FlagAlteracaoResultadoRealizada {

        RESULTADO_ALTERADO(1L),
        RESULTADO_NAO_ALTERADO(2L);

        private Long value;

        FlagAlteracaoResultadoRealizada(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }
}