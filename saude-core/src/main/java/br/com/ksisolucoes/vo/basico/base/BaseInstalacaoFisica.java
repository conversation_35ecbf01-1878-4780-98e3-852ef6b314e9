package br.com.ksisolucoes.vo.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the instalacao_fisica table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="instalacao_fisica"
 */

public abstract class BaseInstalacaoFisica extends BaseRootVO implements Serializable {

	public static String REF = "InstalacaoFisica";
	public static final String PROP_TIPO = "tipo";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_SUB_TIPO_INSTALACAO = "subTipoInstalacao";
	public static final String PROP_DESCRICAO = "descricao";


	// constructors
	public BaseInstalacaoFisica () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInstalacaoFisica (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.Long tipo;

	// many to one
	private br.com.ksisolucoes.vo.basico.SubTipoInstalacao subTipoInstalacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_instalacao"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_instalacao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_instalacao
	 * @param descricao the ds_instalacao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: tp_instalacao
	 */
	public java.lang.Long getTipo () {
		return getPropertyValue(this, tipo, PROP_TIPO); 
	}

	/**
	 * Set the value related to the column: tp_instalacao
	 * @param tipo the tp_instalacao value
	 */
	public void setTipo (java.lang.Long tipo) {
//        java.lang.Long tipoOld = this.tipo;
		this.tipo = tipo;
//        this.getPropertyChangeSupport().firePropertyChange ("tipo", tipoOld, tipo);
	}



	/**
	 * Return the value associated with the column: cd_subtipo
	 */
	public br.com.ksisolucoes.vo.basico.SubTipoInstalacao getSubTipoInstalacao () {
		return getPropertyValue(this, subTipoInstalacao, PROP_SUB_TIPO_INSTALACAO); 
	}

	/**
	 * Set the value related to the column: cd_subtipo
	 * @param subTipoInstalacao the cd_subtipo value
	 */
	public void setSubTipoInstalacao (br.com.ksisolucoes.vo.basico.SubTipoInstalacao subTipoInstalacao) {
//        br.com.ksisolucoes.vo.basico.SubTipoInstalacao subTipoInstalacaoOld = this.subTipoInstalacao;
		this.subTipoInstalacao = subTipoInstalacao;
//        this.getPropertyChangeSupport().firePropertyChange ("subTipoInstalacao", subTipoInstalacaoOld, subTipoInstalacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.basico.InstalacaoFisica)) return false;
		else {
			br.com.ksisolucoes.vo.basico.InstalacaoFisica instalacaoFisica = (br.com.ksisolucoes.vo.basico.InstalacaoFisica) obj;
			if (null == this.getCodigo() || null == instalacaoFisica.getCodigo()) return false;
			else return (this.getCodigo().equals(instalacaoFisica.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}