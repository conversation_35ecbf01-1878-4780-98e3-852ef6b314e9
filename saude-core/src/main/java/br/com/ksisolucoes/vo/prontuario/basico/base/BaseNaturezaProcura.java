package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;


/**
 * This is an object that contains data related to the natureza_procura table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="natureza_procura"
 */

public abstract class BaseNaturezaProcura extends BaseRootVO implements Serializable {

	public static String REF = "NaturezaProcura";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_DESCRICAO_REDUZIDA = "descricaoReduzida";
	public static final String PROP_PERMITE_AGENDAMENTO = "permiteAgendamento";


	// constructors
	public BaseNaturezaProcura () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseNaturezaProcura (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String descricaoReduzida;
	private java.lang.String permiteAgendamento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  column="cd_nat_procura"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: ds_nat_procura
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: ds_nat_procura
	 * @param descricao the ds_nat_procura value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: ds_red_nat_procura
	 */
	public java.lang.String getDescricaoReduzida () {
		return getPropertyValue(this, descricaoReduzida, PROP_DESCRICAO_REDUZIDA); 
	}

	/**
	 * Set the value related to the column: ds_red_nat_procura
	 * @param descricaoReduzida the ds_red_nat_procura value
	 */
	public void setDescricaoReduzida (java.lang.String descricaoReduzida) {
//        java.lang.String descricaoReduzidaOld = this.descricaoReduzida;
		this.descricaoReduzida = descricaoReduzida;
//        this.getPropertyChangeSupport().firePropertyChange ("descricaoReduzida", descricaoReduzidaOld, descricaoReduzida);
	}



	/**
	 * Return the value associated with the column: permite_agendamento
	 */
	public java.lang.String getPermiteAgendamento () {
		return getPropertyValue(this, permiteAgendamento, PROP_PERMITE_AGENDAMENTO); 
	}

	/**
	 * Set the value related to the column: permite_agendamento
	 * @param permiteAgendamento the permite_agendamento value
	 */
	public void setPermiteAgendamento (java.lang.String permiteAgendamento) {
//        java.lang.String permiteAgendamentoOld = this.permiteAgendamento;
		this.permiteAgendamento = permiteAgendamento;
//        this.getPropertyChangeSupport().firePropertyChange ("permiteAgendamento", permiteAgendamentoOld, permiteAgendamento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura naturezaProcura = (br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura) obj;
			if (null == this.getCodigo() || null == naturezaProcura.getCodigo()) return false;
			else return (this.getCodigo().equals(naturezaProcura.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }

    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}