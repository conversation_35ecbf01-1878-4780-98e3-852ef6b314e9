package br.com.ksisolucoes.vo.entradas.estoque.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseControleInventarioPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_EMPRESA = "empresa";
	public static String PROP_CODIGO = "codigo";

	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private java.lang.Long codigo;


	public BaseControleInventarioPK () {}
	
	public BaseControleInventarioPK (
		br.com.ksisolucoes.vo.basico.Empresa empresa,
		java.lang.Long codigo) {

		this.setEmpresa(empresa);
		this.setCodigo(codigo);
	}


	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: sequencia
	 */
	public java.lang.Long getCodigo () {
		return getPropertyValue(this, codigo, PROP_CODIGO); 
	}

	/**
	 * Set the value related to the column: sequencia
	 * @param codigo the sequencia value
	 */
	public void setCodigo (java.lang.Long codigo) {
//        java.lang.Long codigoOld = this.codigo;
		this.codigo = codigo;
//        this.getPropertyChangeSupport().firePropertyChange ("codigo", codigoOld, codigo);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.entradas.estoque.ControleInventarioPK)) return false;
		else {
			br.com.ksisolucoes.vo.entradas.estoque.ControleInventarioPK mObj = (br.com.ksisolucoes.vo.entradas.estoque.ControleInventarioPK) obj;
			if (null != this.getEmpresa() && null != mObj.getEmpresa()) {
				if (!this.getEmpresa().equals(mObj.getEmpresa())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getCodigo() && null != mObj.getCodigo()) {
				if (!this.getCodigo().equals(mObj.getCodigo())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getEmpresa()) {
				sb.append(this.getEmpresa().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getCodigo()) {
				sb.append(this.getCodigo().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}