package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseTipoGestacao;

public class TipoGestacao extends BaseTipoGestacao implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public TipoGestacao() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public TipoGestacao(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public TipoGestacao(
            java.lang.Long codigo,
            java.lang.String descricao,
            java.lang.Long totalRn) {

        super(
                codigo,
                descricao,
                totalRn);
    }

    /*[CONSTRUCTOR MARKER END]*/
    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}