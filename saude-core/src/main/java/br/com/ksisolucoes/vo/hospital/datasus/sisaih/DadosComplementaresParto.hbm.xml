<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
	
<hibernate-mapping package="br.com.ksisolucoes.vo.hospital.datasus.sisaih">
    <class name="DadosComplementaresParto" table="dados_complementares_parto">

        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_dado_compl_parto"
        >
            <generator class="assigned" />
        </id>
        <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.hospital.ItemContaPaciente"
            name="itemContaPaciente"
            not-null="true"
        >
            <column name="cd_it_cta_paciente" />
        </many-to-one>

        <property
            name="numeroPrenatal"
            column="num_prenatal"
            type="java.lang.String"
            length="1"
        />

        <property
            name="quantidadeVivos"
            column="qtd_vivos"
            type="java.lang.Long"
        />

        <property
            name="quantidadeMortos"
            column="qtd_mortos"
            type="java.lang.Long"
        />

        <property
            name="quantidadeAlta"
            column="qtd_alta"
            type="java.lang.Long"
        />

        <property
            name="quantidadeTransferencia"
            column="qtd_transf"
            type="java.lang.Long"
        />

        <property
            name="quantidadeObito"
            column="qtd_obito"
            type="java.lang.Long"
        />
    </class>
</hibernate-mapping>