package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;


public abstract class BaseProcedimentoRegistroPK extends BaseRootVO implements Serializable {

	protected int hashCode = Integer.MIN_VALUE;

	public static String PROP_PROCEDIMENTO_REGISTRO_CADASTRO = "procedimentoRegistroCadastro";
	public static String PROP_PROCEDIMENTO_COMPETENCIA = "procedimentoCompetencia";

	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro procedimentoRegistroCadastro;
	private br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia;


	public BaseProcedimentoRegistroPK () {}
	
	public BaseProcedimentoRegistroPK (
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro procedimentoRegistroCadastro,
		br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {

		this.setProcedimentoRegistroCadastro(procedimentoRegistroCadastro);
		this.setProcedimentoCompetencia(procedimentoCompetencia);
	}


	/**
	 * Return the value associated with the column: cd_registro
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro getProcedimentoRegistroCadastro () {
		return getPropertyValue(this, procedimentoRegistroCadastro, PROP_PROCEDIMENTO_REGISTRO_CADASTRO); 
	}

	/**
	 * Set the value related to the column: cd_registro
	 * @param procedimentoRegistroCadastro the cd_registro value
	 */
	public void setProcedimentoRegistroCadastro (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro procedimentoRegistroCadastro) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro procedimentoRegistroCadastroOld = this.procedimentoRegistroCadastro;
		this.procedimentoRegistroCadastro = procedimentoRegistroCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoRegistroCadastro", procedimentoRegistroCadastroOld, procedimentoRegistroCadastro);
	}



	/**
	 * Return the value associated with the column: dt_competencia
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia getProcedimentoCompetencia () {
		return getPropertyValue(this, procedimentoCompetencia, PROP_PROCEDIMENTO_COMPETENCIA); 
	}

	/**
	 * Set the value related to the column: dt_competencia
	 * @param procedimentoCompetencia the dt_competencia value
	 */
	public void setProcedimentoCompetencia (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetencia) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia procedimentoCompetenciaOld = this.procedimentoCompetencia;
		this.procedimentoCompetencia = procedimentoCompetencia;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimentoCompetencia", procedimentoCompetenciaOld, procedimentoCompetencia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroPK)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroPK mObj = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroPK) obj;
			if (null != this.getProcedimentoRegistroCadastro() && null != mObj.getProcedimentoRegistroCadastro()) {
				if (!this.getProcedimentoRegistroCadastro().equals(mObj.getProcedimentoRegistroCadastro())) {
					return false;
				}
			}
			else {
				return false;
			}
			if (null != this.getProcedimentoCompetencia() && null != mObj.getProcedimentoCompetencia()) {
				if (!this.getProcedimentoCompetencia().equals(mObj.getProcedimentoCompetencia())) {
					return false;
				}
			}
			else {
				return false;
			}
			return true;
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			StringBuilder sb = new StringBuilder();
			if (null != this.getProcedimentoRegistroCadastro()) {
				sb.append(this.getProcedimentoRegistroCadastro().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			if (null != this.getProcedimentoCompetencia()) {
				sb.append(this.getProcedimentoCompetencia().hashCode());
				sb.append(":");
			}
			else {
				return super.hashCode();
			}
			this.hashCode = sb.toString().hashCode();
		}
		return this.hashCode;
	}

    private java.beans.PropertyChangeSupport propertyChangeSupport;

    protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
        if( this.propertyChangeSupport == null ) {
            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
        }
        return this.propertyChangeSupport;
    }

    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.addPropertyChangeListener(l);
    }

    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
		propertyChangeSupport.addPropertyChangeListener(propertyName, listener);
    }

    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
        propertyChangeSupport.removePropertyChangeListener(l);
    }
}