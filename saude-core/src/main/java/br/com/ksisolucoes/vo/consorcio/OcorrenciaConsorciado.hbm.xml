<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.consorcio"  >
    <class name="OcorrenciaConsorciado" table="ocorrencia_consorciado">
        
        <id
            name="codigo"
            type="java.lang.Long"
            column="cd_ocorrencia_consorciado"
        >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="empresa"
                name="empresa"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

        <property
                name="tipo"
                column="tipo"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                name="dataInicio"
                column="dt_ini"
                type="java.util.Date"
        />

        <property
                name="dataFinal"
                column="dt_fim"
                type="java.util.Date"
        />

        <property
                column="ds_ocorrencia"
                name="descricaoOcorrencia"
                not-null="true"
                type="java.lang.String"
                length="300"
        />
        
        <property
                name="dataOcorrencia"
                column="dt_ocorrencia"
                type="java.util.Date"
        />


    </class>
</hibernate-mapping>
