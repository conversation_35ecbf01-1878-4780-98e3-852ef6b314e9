package br.com.ksisolucoes.vo.prontuario.hospital;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.hospital.base.BaseTipoDieta;

public class TipoDieta extends BaseTipoDieta implements CodigoManager {

    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
    public TipoDieta() {
        super();
    }

    /**
     * Constructor for primary key
     */
    public TipoDieta(java.lang.Long codigo) {
        super(codigo);
    }

    /**
     * Constructor for required fields
     */
    public TipoDieta(
            java.lang.Long codigo,
            java.lang.String descricao,
            java.lang.Long situacao) {

        super(
                codigo,
                descricao,
                situacao);
    }

    /*[CONSTRUCTOR MARKER END]*/
    public static enum Status implements IEnum<Status> {

        NORMAL(0L, Bundle.getStringApplication("rotulo_normal")),
        EXCLUIDO(3L, Bundle.getStringApplication("rotulo_excluido"));
        private Long value;
        private String descricao;

        private Status(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Status valeuOf(Long value) {
            for (Status status : Status.values()) {
                if (status.value().equals(value)) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }
    }
    
    @Override
    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }

    @Override
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}