package br.com.ksisolucoes.vo.vigilancia.externo.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the usuario_vigilancia table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="usuario_vigilancia"
 */

public abstract class BaseUsuarioVigilancia extends BaseRootVO implements Serializable {

	public static String REF = "UsuarioVigilancia";
	public static final String PROP_PERFIL_USUARIO_EXTERNO_VIGILANCIA = "perfilUsuarioExternoVigilancia";
	public static final String PROP_NUMERO = "numero";
	public static final String PROP_CNPJ = "cnpj";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DATA_CONFIRMACAO = "dataConfirmacao";
	public static final String PROP_ESTABELECIMENTO = "estabelecimento";
	public static final String PROP_VIGILANCIA_PROFISSIONAL = "vigilanciaProfissional";
	public static final String PROP_RUA = "rua";
	public static final String PROP_CPF = "cpf";
	public static final String PROP_RAZAO_SOCIAL = "razaoSocial";
	public static final String PROP_CEP = "cep";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_SENHA = "senha";
	public static final String PROP_CHAVE_VERIFICACAO = "chaveVerificacao";
	public static final String PROP_CIDADE = "cidade";
	public static final String PROP_DATA_SOLICITACAO = "dataSolicitacao";
	public static final String PROP_EMAIL = "email";
	public static final String PROP_TELEFONE = "telefone";
	public static final String PROP_BAIRRO = "bairro";
	public static final String PROP_NOME = "nome";
	public static final String PROP_CHAVE_NOVA_SENHA = "chaveNovaSenha";


	// constructors
	public BaseUsuarioVigilancia () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseUsuarioVigilancia (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseUsuarioVigilancia (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.String senha,
		java.lang.String cpf,
		java.util.Date dataSolicitacao) {

		this.setCodigo(codigo);
		this.setUsuario(usuario);
		this.setSenha(senha);
		this.setCpf(cpf);
		this.setDataSolicitacao(dataSolicitacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String nome;
	private java.lang.String senha;
	private java.lang.String email;
	private java.lang.String cpf;
	private java.lang.String razaoSocial;
	private java.lang.String cnpj;
	private java.lang.String chaveNovaSenha;
	private java.lang.String chaveVerificacao;
	private java.util.Date dataSolicitacao;
	private java.util.Date dataConfirmacao;
	private java.lang.String telefone;
	private java.lang.String cep;
	private java.lang.String bairro;
	private java.lang.String rua;
	private java.lang.String numero;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.externo.PerfilUsuarioExternoVigilancia perfilUsuarioExternoVigilancia;
	private br.com.ksisolucoes.vo.basico.Cidade cidade;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;
	private br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional;
	private br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_usu_vigilancia"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nome
	 */
	public java.lang.String getNome () {
		return getPropertyValue(this, nome, PROP_NOME); 
	}

	/**
	 * Set the value related to the column: nome
	 * @param nome the nome value
	 */
	public void setNome (java.lang.String nome) {
//        java.lang.String nomeOld = this.nome;
		this.nome = nome;
//        this.getPropertyChangeSupport().firePropertyChange ("nome", nomeOld, nome);
	}



	/**
	 * Return the value associated with the column: senha
	 */
	public java.lang.String getSenha () {
		return getPropertyValue(this, senha, PROP_SENHA); 
	}

	/**
	 * Set the value related to the column: senha
	 * @param senha the senha value
	 */
	public void setSenha (java.lang.String senha) {
//        java.lang.String senhaOld = this.senha;
		this.senha = senha;
//        this.getPropertyChangeSupport().firePropertyChange ("senha", senhaOld, senha);
	}



	/**
	 * Return the value associated with the column: email
	 */
	public java.lang.String getEmail () {
		return getPropertyValue(this, email, PROP_EMAIL); 
	}

	/**
	 * Set the value related to the column: email
	 * @param email the email value
	 */
	public void setEmail (java.lang.String email) {
//        java.lang.String emailOld = this.email;
		this.email = email;
//        this.getPropertyChangeSupport().firePropertyChange ("email", emailOld, email);
	}



	/**
	 * Return the value associated with the column: cpf
	 */
	public java.lang.String getCpf () {
		return getPropertyValue(this, cpf, PROP_CPF); 
	}

	/**
	 * Set the value related to the column: cpf
	 * @param cpf the cpf value
	 */
	public void setCpf (java.lang.String cpf) {
//        java.lang.String cpfOld = this.cpf;
		this.cpf = cpf;
//        this.getPropertyChangeSupport().firePropertyChange ("cpf", cpfOld, cpf);
	}



	/**
	 * Return the value associated with the column: razao_social
	 */
	public java.lang.String getRazaoSocial () {
		return getPropertyValue(this, razaoSocial, PROP_RAZAO_SOCIAL); 
	}

	/**
	 * Set the value related to the column: razao_social
	 * @param razaoSocial the razao_social value
	 */
	public void setRazaoSocial (java.lang.String razaoSocial) {
//        java.lang.String razaoSocialOld = this.razaoSocial;
		this.razaoSocial = razaoSocial;
//        this.getPropertyChangeSupport().firePropertyChange ("razaoSocial", razaoSocialOld, razaoSocial);
	}



	/**
	 * Return the value associated with the column: cnpj
	 */
	public java.lang.String getCnpj () {
		return getPropertyValue(this, cnpj, PROP_CNPJ); 
	}

	/**
	 * Set the value related to the column: cnpj
	 * @param cnpj the cnpj value
	 */
	public void setCnpj (java.lang.String cnpj) {
//        java.lang.String cnpjOld = this.cnpj;
		this.cnpj = cnpj;
//        this.getPropertyChangeSupport().firePropertyChange ("cnpj", cnpjOld, cnpj);
	}



	/**
	 * Return the value associated with the column: chave_nova_senha
	 */
	public java.lang.String getChaveNovaSenha () {
		return getPropertyValue(this, chaveNovaSenha, PROP_CHAVE_NOVA_SENHA); 
	}

	/**
	 * Set the value related to the column: chave_nova_senha
	 * @param chaveNovaSenha the chave_nova_senha value
	 */
	public void setChaveNovaSenha (java.lang.String chaveNovaSenha) {
//        java.lang.String chaveNovaSenhaOld = this.chaveNovaSenha;
		this.chaveNovaSenha = chaveNovaSenha;
//        this.getPropertyChangeSupport().firePropertyChange ("chaveNovaSenha", chaveNovaSenhaOld, chaveNovaSenha);
	}



	/**
	 * Return the value associated with the column: chave_verificacao
	 */
	public java.lang.String getChaveVerificacao () {
		return getPropertyValue(this, chaveVerificacao, PROP_CHAVE_VERIFICACAO); 
	}

	/**
	 * Set the value related to the column: chave_verificacao
	 * @param chaveVerificacao the chave_verificacao value
	 */
	public void setChaveVerificacao (java.lang.String chaveVerificacao) {
//        java.lang.String chaveVerificacaoOld = this.chaveVerificacao;
		this.chaveVerificacao = chaveVerificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("chaveVerificacao", chaveVerificacaoOld, chaveVerificacao);
	}



	/**
	 * Return the value associated with the column: dt_solicitacao
	 */
	public java.util.Date getDataSolicitacao () {
		return getPropertyValue(this, dataSolicitacao, PROP_DATA_SOLICITACAO); 
	}

	/**
	 * Set the value related to the column: dt_solicitacao
	 * @param dataSolicitacao the dt_solicitacao value
	 */
	public void setDataSolicitacao (java.util.Date dataSolicitacao) {
//        java.util.Date dataSolicitacaoOld = this.dataSolicitacao;
		this.dataSolicitacao = dataSolicitacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataSolicitacao", dataSolicitacaoOld, dataSolicitacao);
	}



	/**
	 * Return the value associated with the column: dt_confirmacao
	 */
	public java.util.Date getDataConfirmacao () {
		return getPropertyValue(this, dataConfirmacao, PROP_DATA_CONFIRMACAO); 
	}

	/**
	 * Set the value related to the column: dt_confirmacao
	 * @param dataConfirmacao the dt_confirmacao value
	 */
	public void setDataConfirmacao (java.util.Date dataConfirmacao) {
//        java.util.Date dataConfirmacaoOld = this.dataConfirmacao;
		this.dataConfirmacao = dataConfirmacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataConfirmacao", dataConfirmacaoOld, dataConfirmacao);
	}



	/**
	 * Return the value associated with the column: telefone
	 */
	public java.lang.String getTelefone () {
		return getPropertyValue(this, telefone, PROP_TELEFONE); 
	}

	/**
	 * Set the value related to the column: telefone
	 * @param telefone the telefone value
	 */
	public void setTelefone (java.lang.String telefone) {
//        java.lang.String telefoneOld = this.telefone;
		this.telefone = telefone;
//        this.getPropertyChangeSupport().firePropertyChange ("telefone", telefoneOld, telefone);
	}



	/**
	 * Return the value associated with the column: cep
	 */
	public java.lang.String getCep () {
		return getPropertyValue(this, cep, PROP_CEP); 
	}

	/**
	 * Set the value related to the column: cep
	 * @param cep the cep value
	 */
	public void setCep (java.lang.String cep) {
//        java.lang.String cepOld = this.cep;
		this.cep = cep;
//        this.getPropertyChangeSupport().firePropertyChange ("cep", cepOld, cep);
	}



	/**
	 * Return the value associated with the column: bairro
	 */
	public java.lang.String getBairro () {
		return getPropertyValue(this, bairro, PROP_BAIRRO); 
	}

	/**
	 * Set the value related to the column: bairro
	 * @param bairro the bairro value
	 */
	public void setBairro (java.lang.String bairro) {
//        java.lang.String bairroOld = this.bairro;
		this.bairro = bairro;
//        this.getPropertyChangeSupport().firePropertyChange ("bairro", bairroOld, bairro);
	}



	/**
	 * Return the value associated with the column: rua
	 */
	public java.lang.String getRua () {
		return getPropertyValue(this, rua, PROP_RUA); 
	}

	/**
	 * Set the value related to the column: rua
	 * @param rua the rua value
	 */
	public void setRua (java.lang.String rua) {
//        java.lang.String ruaOld = this.rua;
		this.rua = rua;
//        this.getPropertyChangeSupport().firePropertyChange ("rua", ruaOld, rua);
	}



	/**
	 * Return the value associated with the column: numero
	 */
	public java.lang.String getNumero () {
		return getPropertyValue(this, numero, PROP_NUMERO); 
	}

	/**
	 * Set the value related to the column: numero
	 * @param numero the numero value
	 */
	public void setNumero (java.lang.String numero) {
//        java.lang.String numeroOld = this.numero;
		this.numero = numero;
//        this.getPropertyChangeSupport().firePropertyChange ("numero", numeroOld, numero);
	}



	/**
	 * Return the value associated with the column: cd_perfil_usuario_externo_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.externo.PerfilUsuarioExternoVigilancia getPerfilUsuarioExternoVigilancia () {
		return getPropertyValue(this, perfilUsuarioExternoVigilancia, PROP_PERFIL_USUARIO_EXTERNO_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_perfil_usuario_externo_vigilancia
	 * @param perfilUsuarioExternoVigilancia the cd_perfil_usuario_externo_vigilancia value
	 */
	public void setPerfilUsuarioExternoVigilancia (br.com.ksisolucoes.vo.vigilancia.externo.PerfilUsuarioExternoVigilancia perfilUsuarioExternoVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.externo.PerfilUsuarioExternoVigilancia perfilUsuarioExternoVigilanciaOld = this.perfilUsuarioExternoVigilancia;
		this.perfilUsuarioExternoVigilancia = perfilUsuarioExternoVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("perfilUsuarioExternoVigilancia", perfilUsuarioExternoVigilanciaOld, perfilUsuarioExternoVigilancia);
	}



	/**
	 * Return the value associated with the column: cod_cid
	 */
	public br.com.ksisolucoes.vo.basico.Cidade getCidade () {
		return getPropertyValue(this, cidade, PROP_CIDADE); 
	}

	/**
	 * Set the value related to the column: cod_cid
	 * @param cidade the cod_cid value
	 */
	public void setCidade (br.com.ksisolucoes.vo.basico.Cidade cidade) {
//        br.com.ksisolucoes.vo.basico.Cidade cidadeOld = this.cidade;
		this.cidade = cidade;
//        this.getPropertyChangeSupport().firePropertyChange ("cidade", cidadeOld, cidade);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}



	/**
	 * Return the value associated with the column: cd_vigilancia_profissional
	 */
	public br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional getVigilanciaProfissional () {
		return getPropertyValue(this, vigilanciaProfissional, PROP_VIGILANCIA_PROFISSIONAL); 
	}

	/**
	 * Set the value related to the column: cd_vigilancia_profissional
	 * @param vigilanciaProfissional the cd_vigilancia_profissional value
	 */
	public void setVigilanciaProfissional (br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissional) {
//        br.com.ksisolucoes.vo.vigilancia.VigilanciaProfissional vigilanciaProfissionalOld = this.vigilanciaProfissional;
		this.vigilanciaProfissional = vigilanciaProfissional;
//        this.getPropertyChangeSupport().firePropertyChange ("vigilanciaProfissional", vigilanciaProfissionalOld, vigilanciaProfissional);
	}



	/**
	 * Return the value associated with the column: cd_estabelecimento
	 */
	public br.com.ksisolucoes.vo.vigilancia.Estabelecimento getEstabelecimento () {
		return getPropertyValue(this, estabelecimento, PROP_ESTABELECIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_estabelecimento
	 * @param estabelecimento the cd_estabelecimento value
	 */
	public void setEstabelecimento (br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimento) {
//        br.com.ksisolucoes.vo.vigilancia.Estabelecimento estabelecimentoOld = this.estabelecimento;
		this.estabelecimento = estabelecimento;
//        this.getPropertyChangeSupport().firePropertyChange ("estabelecimento", estabelecimentoOld, estabelecimento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia usuarioVigilancia = (br.com.ksisolucoes.vo.vigilancia.externo.UsuarioVigilancia) obj;
			if (null == this.getCodigo() || null == usuarioVigilancia.getCodigo()) return false;
			else return (this.getCodigo().equals(usuarioVigilancia.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}