package br.com.ksisolucoes.vo.vigilancia.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the requerimento_vistoria_pba_ativ table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="requerimento_vistoria_pba_ativ"
 */

public abstract class BaseRequerimentoVistoriaPBAAtividade extends BaseRootVO implements Serializable {

	public static String REF = "RequerimentoVistoriaPBAAtividade";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REQUERIMENTO_VISTORIA_PROJETO_BASICO_ARQUITETURA = "requerimentoVistoriaProjetoBasicoArquitetura";
	public static final String PROP_ATIVIDADES_VIGILANCIA = "atividadesVigilancia";


	// constructors
	public BaseRequerimentoVistoriaPBAAtividade () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseRequerimentoVistoriaPBAAtividade (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseRequerimentoVistoriaPBAAtividade (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura,
		br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia) {

		this.setCodigo(codigo);
		this.setRequerimentoVistoriaProjetoBasicoArquitetura(requerimentoVistoriaProjetoBasicoArquitetura);
		this.setAtividadesVigilancia(atividadesVigilancia);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura;
	private br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_req_vistoria_pba_ativ"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: cd_requerimento_vistoria_pba
	 */
	public br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura getRequerimentoVistoriaProjetoBasicoArquitetura () {
		return getPropertyValue(this, requerimentoVistoriaProjetoBasicoArquitetura, PROP_REQUERIMENTO_VISTORIA_PROJETO_BASICO_ARQUITETURA); 
	}

	/**
	 * Set the value related to the column: cd_requerimento_vistoria_pba
	 * @param requerimentoVistoriaProjetoBasicoArquitetura the cd_requerimento_vistoria_pba value
	 */
	public void setRequerimentoVistoriaProjetoBasicoArquitetura (br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquitetura) {
//        br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoVistoriaProjetoBasicoArquitetura requerimentoVistoriaProjetoBasicoArquiteturaOld = this.requerimentoVistoriaProjetoBasicoArquitetura;
		this.requerimentoVistoriaProjetoBasicoArquitetura = requerimentoVistoriaProjetoBasicoArquitetura;
//        this.getPropertyChangeSupport().firePropertyChange ("requerimentoVistoriaProjetoBasicoArquitetura", requerimentoVistoriaProjetoBasicoArquiteturaOld, requerimentoVistoriaProjetoBasicoArquitetura);
	}



	/**
	 * Return the value associated with the column: cd_atividades_vigilancia
	 */
	public br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia getAtividadesVigilancia () {
		return getPropertyValue(this, atividadesVigilancia, PROP_ATIVIDADES_VIGILANCIA); 
	}

	/**
	 * Set the value related to the column: cd_atividades_vigilancia
	 * @param atividadesVigilancia the cd_atividades_vigilancia value
	 */
	public void setAtividadesVigilancia (br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilancia) {
//        br.com.ksisolucoes.vo.vigilancia.faturamento.atividades.AtividadesVigilancia atividadesVigilanciaOld = this.atividadesVigilancia;
		this.atividadesVigilancia = atividadesVigilancia;
//        this.getPropertyChangeSupport().firePropertyChange ("atividadesVigilancia", atividadesVigilanciaOld, atividadesVigilancia);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.RequerimentoVistoriaPBAAtividade)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.RequerimentoVistoriaPBAAtividade requerimentoVistoriaPBAAtividade = (br.com.ksisolucoes.vo.vigilancia.RequerimentoVistoriaPBAAtividade) obj;
			if (null == this.getCodigo() || null == requerimentoVistoriaPBAAtividade.getCodigo()) return false;
			else return (this.getCodigo().equals(requerimentoVistoriaPBAAtividade.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}