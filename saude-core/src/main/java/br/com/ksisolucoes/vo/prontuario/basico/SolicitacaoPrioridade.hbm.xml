<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico">
    <class
            name="SolicitacaoPrioridade"
            table="solicitacao_prioridade"
    >
        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_solicitacao_prioridade"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one class="br.com.ksisolucoes.vo.prontuario.basico.SolicitacaoAgendamento"
                     name="solicitacaoAgendamento"
                     not-null="true">
            <column name="cd_solicitacao"/>
        </many-to-one>

        <property
                name="status"
                column="status"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="dt_cancelamento"
                name="dataCancelamento"
                type="timestamp"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario_can"
                name="usuarioCancelamento"
                not-null="false"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                type="timestamp"
        />

    </class>
</hibernate-mapping>
