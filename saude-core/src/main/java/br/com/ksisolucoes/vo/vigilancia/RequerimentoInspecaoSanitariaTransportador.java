package br.com.ksisolucoes.vo.vigilancia;

import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.base.BaseRequerimentoInspecaoSanitariaTransportador;

import java.io.Serializable;



public class RequerimentoInspecaoSanitariaTransportador extends BaseRequerimentoInspecaoSanitariaTransportador implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoInspecaoSanitariaTransportador() {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoInspecaoSanitariaTransportador(Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoInspecaoSanitariaTransportador(
		Long codigo,
		br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoInspecaoSanitaria requerimentoInspecaoSanitaria,
		String descricaoEmpresa,
		String endereco,
		String numeroAFE) {

		super (
			codigo,
			requerimentoInspecaoSanitaria,
			descricaoEmpresa,
			endereco,
			numeroAFE);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
}