package br.com.ksisolucoes.vo.vigilancia.investigacao.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the investigacao_agr_pararilisa_flacida_aguda table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="investigacao_agr_pararilisa_flacida_aguda"
 */

public abstract class BaseInvestigacaoAgravoParalisiaFlacidaAguda extends BaseRootVO implements Serializable {

	public static String REF = "InvestigacaoAgravoParalisiaFlacidaAguda";
	public static final String PROP_REFLEXOS_AQUILEU_D = "reflexosAquileuD";
	public static final String PROP_REFLEXOS_AQUILEU_E = "reflexosAquileuE";
	public static final String PROP_CD_PAIS_ORIGEM = "cdPaisOrigem";
	public static final String PROP_REFLEXO_CUTANEO_PLANTAR_FLEXAO_D = "reflexoCutaneoPlantarFlexaoD";
	public static final String PROP_LOCALIZACAO_MIE = "localizacaoMie";
	public static final String PROP_LOCALIZACAO_MID = "localizacaoMid";
	public static final String PROP_REFLEXO_CUTANEO_PLANTAR_FLEXAO_E = "reflexoCutaneoPlantarFlexaoE";
	public static final String PROP_SINAIS_SINTOMAS_OUTROS = "sinaisSintomasOutros";
	public static final String PROP_DATA_ENCERRAMENTO = "dataEncerramento";
	public static final String PROP_CONDICOES = "condicoes";
	public static final String PROP_FORCA_MUSCULAR_MSD = "forcaMuscularMsd";
	public static final String PROP_FORCA_MUSCULAR_MSE = "forcaMuscularMse";
	public static final String PROP_COLETADO_MATERIAL_CEREBRO = "coletadoMaterialCerebro";
	public static final String PROP_SENSIBILIDADE_REVISITA_MSE = "sensibilidadeRevisitaMse";
	public static final String PROP_SENSIBILIDADE_REVISITA_MSD = "sensibilidadeRevisitaMsd";
	public static final String PROP_SINAIS_SINTOMAS_SINTOMAS_RESP = "sinaisSintomasSintomasResp";
	public static final String PROP_SINAIS_IRRITACAO_MENINGEA_KERNING = "sinaisIrritacaoMeningeaKerning";
	public static final String PROP_COLETADO_MATERIAL_INTESTINO = "coletadoMaterialIntestino";
	public static final String PROP_REFLEXO_CUTANEO_PLANTAR_EXTENSAO_E = "reflexoCutaneoPlantarExtensaoE";
	public static final String PROP_DEFICIENCIA_MOTORA_FLACIDA = "deficienciaMotoraFlacida";
	public static final String PROP_REFLEXOS_PATELAR_D = "reflexosPatelarD";
	public static final String PROP_CONTATO_INGESTAO_SUBSTANCIA_ESPECIFIQUE = "contatoIngestaoSubstanciaEspecifique";
	public static final String PROP_DT_COLETA = "dtColeta";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_REFLEXOS_PATELAR_E = "reflexosPatelarE";
	public static final String PROP_SENSIBILIDADE_REVISITA_FACE = "sensibilidadeRevisitaFace";
	public static final String PROP_REFLEXO_CUTANEO_PLANTAR_EXTENSAO_D = "reflexoCutaneoPlantarExtensaoD";
	public static final String PROP_FORCA_MUSCULAR_AGUDA_MSE = "forcaMuscularAgudaMse";
	public static final String PROP_REFLEXOS_TRICIPITAL_E = "reflexosTricipitalE";
	public static final String PROP_TONUS_MUSCULAR_REVISITA_FACE = "tonusMuscularRevisitaFace";
	public static final String PROP_FORCA_MUSCULAR_AGUDA_MSD = "forcaMuscularAgudaMsd";
	public static final String PROP_SINAIS_SINTOMAS_OBSTIPACAO = "sinaisSintomasObstipacao";
	public static final String PROP_VIAJOU_RECEBEU_VISITAS = "viajouRecebeuVisitas";
	public static final String PROP_DT_COLETA_MATERIAL = "dtColetaMaterial";
	public static final String PROP_DIAGOSTICO_SUGESTIVO = "diagosticoSugestivo";
	public static final String PROP_DATA_INVESTIGACAO = "dataInvestigacao";
	public static final String PROP_SINAIS_SINTOMAS_VOMITOS = "sinaisSintomasVomitos";
	public static final String PROP_LOCALIZACAO_MSE = "localizacaoMse";
	public static final String PROP_LOCALIZACAO_MSD = "localizacaoMsd";
	public static final String PROP_DEFICIENCIA_MOTORA_AGUDA = "deficienciaMotoraAguda";
	public static final String PROP_REFLEXOS_REVISITA_PATELAR_E = "reflexosRevisitaPatelarE";
	public static final String PROP_REFLEXOS_REVISITA_PATELAR_D = "reflexosRevisitaPatelarD";
	public static final String PROP_SENSIBILIDADE_REVISITA_MID = "sensibilidadeRevisitaMid";
	public static final String PROP_REFLEXOS_TRICIPITAL_D = "reflexosTricipitalD";
	public static final String PROP_DT_ENVIO_LOCAL_ESTADUAL = "dtEnvioLocalEstadual";
	public static final String PROP_SENSIBILIDADE_REVISITA_MIE = "sensibilidadeRevisitaMie";
	public static final String PROP_NUMERO_DOSE = "numeroDose";
	public static final String PROP_DIAGOSTICO_CASO_DESCARTADO = "diagosticoCasoDescartado";
	public static final String PROP_REFLEXOS_REVISITA_TRICIPITAL_E = "reflexosRevisitaTricipitalE";
	public static final String PROP_REFLEXOS_REVISITA_TRICIPITAL_D = "reflexosRevisitaTricipitalD";
	public static final String PROP_TONUS_MUSCULAR_MIE = "tonusMuscularMie";
	public static final String PROP_DEFICIENCIA_MOTORA_DESCENDENTE = "deficienciaMotoraDescendente";
	public static final String PROP_DATA_REVISAO = "dataRevisao";
	public static final String PROP_SENSIBILIDADE_MSD = "sensibilidadeMsd";
	public static final String PROP_SENSIBILIDADE_MSE = "sensibilidadeMse";
	public static final String PROP_REGISTRO_AGRAVO = "registroAgravo";
	public static final String PROP_DEFICIENCIA_MOTORA_ASCENDENTE = "deficienciaMotoraAscendente";
	public static final String PROP_REFLEXOS_REVISITA_AQUILEU_D = "reflexosRevisitaAquileuD";
	public static final String PROP_REFLEXOS_REVISITA_BICIPAL_D = "reflexosRevisitaBicipalD";
	public static final String PROP_REFLEXOS_REVISITA_AQUILEU_E = "reflexosRevisitaAquileuE";
	public static final String PROP_TONUS_MUSCULAR_MID = "tonusMuscularMid";
	public static final String PROP_REFLEXOS_BICIPAL_D = "reflexosBicipalD";
	public static final String PROP_REFLEXOS_BICIPAL_E = "reflexosBicipalE";
	public static final String PROP_REFLEXOS_REVISITA_BICIPAL_E = "reflexosRevisitaBicipalE";
	public static final String PROP_EXAME_AGUDA_DT_EXAME = "exameAgudaDtExame";
	public static final String PROP_SENSIBILIDADE_FACE = "sensibilidadeFace";
	public static final String PROP_DEFICIENCIA_MOTORA_PROGRESSAO3_DIAS = "deficienciaMotoraProgressao3Dias";
	public static final String PROP_COMPROMETIMENTO_MUSC_CERVICAL = "comprometimentoMuscCervical";
	public static final String PROP_SINAIS_SINTOMAS_DIARREIA = "sinaisSintomasDiarreia";
	public static final String PROP_CRITERIO_CLASSIFICACAO = "criterioClassificacao";
	public static final String PROP_TOMOU_VACINA_POLIOMELITE = "tomouVacinaPoliomelite";
	public static final String PROP_SINAIS_IRRITACAO_MENINGEA_RIGIDEZ_NUCA = "sinaisIrritacaoMeningeaRigidezNuca";
	public static final String PROP_FORCA_MUSCULAR_REVISITA_MSD = "forcaMuscularRevisitaMsd";
	public static final String PROP_FORCA_MUSCULAR_REVISITA_MSE = "forcaMuscularRevisitaMse";
	public static final String PROP_DATA_OBITO = "dataObito";
	public static final String PROP_COMPROMETIMENTO_MUSC_RESPIRATORIA = "comprometimentoMuscRespiratoria";
	public static final String PROP_DT_RESULTADO = "dtResultado";
	public static final String PROP_HOSPITAL = "hospital";
	public static final String PROP_DT_INICIO_DEF_MOTORA = "dtInicioDefMotora";
	public static final String PROP_COMPROMETIMENTO_FACE = "comprometimentoFace";
	public static final String PROP_CONTATO_INGESTAO_SUBSTANCIA = "contatoIngestaoSubstancia";
	public static final String PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_EXTENSAO_E = "reflexoRevisitaCutaneoPlantarExtensaoE";
	public static final String PROP_CLASSIFICACAO_FINAL = "classificacaoFinal";
	public static final String PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_EXTENSAO_D = "reflexoRevisitaCutaneoPlantarExtensaoD";
	public static final String PROP_RESULTADO = "resultado";
	public static final String PROP_TONUS_MUSCULAR_REVISITA_MIE = "tonusMuscularRevisitaMie";
	public static final String PROP_TONUS_MUSCULAR_REVISITA_MID = "tonusMuscularRevisitaMid";
	public static final String PROP_EVOLUCAO_CASO = "evolucaoCaso";
	public static final String PROP_DT1_CONSULTA = "dt1Consulta";
	public static final String PROP_DT_REALIZACAO = "dtRealizacao";
	public static final String PROP_ATROFIA_MSD = "atrofiaMsd";
	public static final String PROP_TONUS_MUSCULAR_MSD = "tonusMuscularMsd";
	public static final String PROP_ATROFIA_MSE = "atrofiaMse";
	public static final String PROP_TONUS_MUSCULAR_MSE = "tonusMuscularMse";
	public static final String PROP_SENSIBILIDADE_MID = "sensibilidadeMid";
	public static final String PROP_SENSIBILIDADE_MIE = "sensibilidadeMie";
	public static final String PROP_HIPOTESE_DIAGNOSTICA_CID = "hipoteseDiagnosticaCid";
	public static final String PROP_SINAIS_SINTOMAS_FEBRE = "sinaisSintomasFebre";
	public static final String PROP_SINAIS_SINTOMAS_DORES_MUSCULARES = "sinaisSintomasDoresMusculares";
	public static final String PROP_DT_RECEBIMENTO_LRR = "dtRecebimentoLrr";
	public static final String PROP_FORCA_MUSCULAR_REVISITA_MID = "forcaMuscularRevisitaMid";
	public static final String PROP_FORCA_MUSCULAR_REVISITA_MIE = "forcaMuscularRevisitaMie";
	public static final String PROP_USUARIO_ENCERRAMENTO = "usuarioEncerramento";
	public static final String PROP_SINAIS_IRRITACAO_MENINGEA_BRUDZINSKI = "sinaisIrritacaoMeningeaBrudzinski";
	public static final String PROP_TONUS_MUSCULAR_CERVICAL = "tonusMuscularCervical";
	public static final String PROP_FLAG_INFORMACOES_COMPLEMENTARES = "flagInformacoesComplementares";
	public static final String PROP_RESULTADO_MATERIAL = "resultadoMaterial";
	public static final String PROP_QUANTIDADE = "quantidade";
	public static final String PROP_FORCA_MUSCULAR_AGUDA_MIE = "forcaMuscularAgudaMie";
	public static final String PROP_FORCA_MUSCULAR_AGUDA_MID = "forcaMuscularAgudaMid";
	public static final String PROP_OBSERVACAO = "observacao";
	public static final String PROP_COLETADO_MATERIAL_MEDULA = "coletadoMaterialMedula";
	public static final String PROP_HISTORIA_INJECAO_INTRAMUSCULAR = "historiaInjecaoIntramuscular";
	public static final String PROP_FORCA_MUSCULAR_MID = "forcaMuscularMid";
	public static final String PROP_FORCA_MUSCULAR_MIE = "forcaMuscularMie";
	public static final String PROP_TONUS_MUSCULAR_REVISITA_MUSC_CERVICAL = "tonusMuscularRevisitaMuscCervical";
	public static final String PROP_HOSPITALIZACAO = "hospitalizacao";
	public static final String PROP_DT_REVISITA = "dtRevisita";
	public static final String PROP_OCUPACAO_CBO = "ocupacaoCbo";
	public static final String PROP_LOCAL_APLICACAO = "localAplicacao";
	public static final String PROP_DT_ULTIMA_DOSE = "dtUltimaDose";
	public static final String PROP_TONUS_MUSCULAR_FACE = "tonusMuscularFace";
	public static final String PROP_SINAIS_SINTOMAS_CEFALEIA = "sinaisSintomasCefaleia";
	public static final String PROP_DT_ENVIO_ESTADUAL_LRR = "dtEnvioEstadualLrr";
	public static final String PROP_TONUS_MUSCULAR_REVISITA_MSE = "tonusMuscularRevisitaMse";
	public static final String PROP_DEFICIENCIA_MOTORA_ASSIMETRICA = "deficienciaMotoraAssimetrica";
	public static final String PROP_TONUS_MUSCULAR_REVISITA_MSD = "tonusMuscularRevisitaMsd";
	public static final String PROP_DATA_INTERNACAO = "dataInternacao";
	public static final String PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_FLEXAO_E = "reflexoRevisitaCutaneoPlantarFlexaoE";
	public static final String PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_FLEXAO_D = "reflexoRevisitaCutaneoPlantarFlexaoD";
	public static final String PROP_ATROFIA_MID = "atrofiaMid";
	public static final String PROP_ATROFIA_MIE = "atrofiaMie";


	// constructors
	public BaseInvestigacaoAgravoParalisiaFlacidaAguda () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseInvestigacaoAgravoParalisiaFlacidaAguda (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseInvestigacaoAgravoParalisiaFlacidaAguda (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo,
		br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo,
		java.lang.String flagInformacoesComplementares) {

		this.setCodigo(codigo);
		this.setRegistroAgravo(registroAgravo);
		this.setOcupacaoCbo(ocupacaoCbo);
		this.setFlagInformacoesComplementares(flagInformacoesComplementares);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String flagInformacoesComplementares;
	private java.util.Date dataInvestigacao;
	private java.util.Date dt1Consulta;
	private java.lang.Long tomouVacinaPoliomelite;
	private java.lang.String numeroDose;
	private java.util.Date dtUltimaDose;
	private java.lang.Long viajouRecebeuVisitas;
	private java.lang.Long sinaisSintomasFebre;
	private java.lang.Long sinaisSintomasVomitos;
	private java.lang.Long sinaisSintomasDiarreia;
	private java.lang.Long sinaisSintomasObstipacao;
	private java.lang.Long sinaisSintomasDoresMusculares;
	private java.lang.Long sinaisSintomasCefaleia;
	private java.lang.Long sinaisSintomasSintomasResp;
	private java.lang.String sinaisSintomasOutros;
	private java.util.Date dtInicioDefMotora;
	private java.lang.Long deficienciaMotoraAguda;
	private java.lang.Long deficienciaMotoraFlacida;
	private java.lang.Long deficienciaMotoraAssimetrica;
	private java.lang.Long deficienciaMotoraProgressao3Dias;
	private java.lang.Long deficienciaMotoraAscendente;
	private java.lang.Long deficienciaMotoraDescendente;
	private java.lang.Long forcaMuscularMie;
	private java.lang.Long forcaMuscularMse;
	private java.lang.Long forcaMuscularMid;
	private java.lang.Long forcaMuscularMsd;
	private java.lang.Long localizacaoMie;
	private java.lang.Long localizacaoMse;
	private java.lang.Long localizacaoMid;
	private java.lang.Long localizacaoMsd;
	private java.lang.Long comprometimentoMuscRespiratoria;
	private java.lang.Long comprometimentoMuscCervical;
	private java.lang.Long comprometimentoFace;
	private java.util.Date exameAgudaDtExame;
	private java.lang.Long forcaMuscularAgudaMie;
	private java.lang.Long forcaMuscularAgudaMse;
	private java.lang.Long forcaMuscularAgudaMid;
	private java.lang.Long forcaMuscularAgudaMsd;
	private java.lang.Long tonusMuscularMie;
	private java.lang.Long tonusMuscularMse;
	private java.lang.Long tonusMuscularMid;
	private java.lang.Long tonusMuscularMsd;
	private java.lang.Long tonusMuscularCervical;
	private java.lang.Long tonusMuscularFace;
	private java.lang.Long sensibilidadeMie;
	private java.lang.Long sensibilidadeMse;
	private java.lang.Long sensibilidadeMid;
	private java.lang.Long sensibilidadeMsd;
	private java.lang.Long sensibilidadeFace;
	private java.lang.Long reflexosAquileuE;
	private java.lang.Long reflexosAquileuD;
	private java.lang.Long reflexosPatelarE;
	private java.lang.Long reflexosPatelarD;
	private java.lang.Long reflexosBicipalE;
	private java.lang.Long reflexosBicipalD;
	private java.lang.Long reflexosTricipitalE;
	private java.lang.Long reflexosTricipitalD;
	private java.lang.Long reflexoCutaneoPlantarFlexaoE;
	private java.lang.Long reflexoCutaneoPlantarFlexaoD;
	private java.lang.Long reflexoCutaneoPlantarExtensaoE;
	private java.lang.Long reflexoCutaneoPlantarExtensaoD;
	private java.lang.Long sinaisIrritacaoMeningeaKerning;
	private java.lang.Long sinaisIrritacaoMeningeaRigidezNuca;
	private java.lang.Long sinaisIrritacaoMeningeaBrudzinski;
	private java.lang.Long contatoIngestaoSubstancia;
	private java.lang.String contatoIngestaoSubstanciaEspecifique;
	private java.lang.Long historiaInjecaoIntramuscular;
	private java.lang.Long localAplicacao;
	private java.lang.Long hospitalizacao;
	private java.util.Date dataInternacao;
	private java.util.Date dtColeta;
	private java.util.Date dtEnvioLocalEstadual;
	private java.util.Date dtEnvioEstadualLrr;
	private java.util.Date dtRecebimentoLrr;
	private java.lang.Long quantidade;
	private java.lang.Long condicoes;
	private java.util.Date dtResultado;
	private java.lang.Long resultado;
	private java.util.Date dtRealizacao;
	private java.lang.Long coletadoMaterialCerebro;
	private java.lang.Long coletadoMaterialMedula;
	private java.lang.Long coletadoMaterialIntestino;
	private java.util.Date dtColetaMaterial;
	private java.lang.Long resultadoMaterial;
	private java.util.Date dtRevisita;
	private java.lang.Long forcaMuscularRevisitaMie;
	private java.lang.Long forcaMuscularRevisitaMse;
	private java.lang.Long forcaMuscularRevisitaMid;
	private java.lang.Long forcaMuscularRevisitaMsd;
	private java.lang.Long tonusMuscularRevisitaMie;
	private java.lang.Long tonusMuscularRevisitaMse;
	private java.lang.Long tonusMuscularRevisitaMid;
	private java.lang.Long tonusMuscularRevisitaMsd;
	private java.lang.Long tonusMuscularRevisitaMuscCervical;
	private java.lang.Long tonusMuscularRevisitaFace;
	private java.lang.Long reflexosRevisitaAquileuE;
	private java.lang.Long reflexosRevisitaAquileuD;
	private java.lang.Long reflexosRevisitaPatelarE;
	private java.lang.Long reflexosRevisitaPatelarD;
	private java.lang.Long reflexosRevisitaBicipalE;
	private java.lang.Long reflexosRevisitaBicipalD;
	private java.lang.Long reflexosRevisitaTricipitalE;
	private java.lang.Long reflexosRevisitaTricipitalD;
	private java.lang.Long reflexoRevisitaCutaneoPlantarFlexaoE;
	private java.lang.Long reflexoRevisitaCutaneoPlantarFlexaoD;
	private java.lang.Long reflexoRevisitaCutaneoPlantarExtensaoE;
	private java.lang.Long reflexoRevisitaCutaneoPlantarExtensaoD;
	private java.lang.Long atrofiaMie;
	private java.lang.Long atrofiaMse;
	private java.lang.Long atrofiaMid;
	private java.lang.Long atrofiaMsd;
	private java.lang.Long sensibilidadeRevisitaMie;
	private java.lang.Long sensibilidadeRevisitaMse;
	private java.lang.Long sensibilidadeRevisitaMid;
	private java.lang.Long sensibilidadeRevisitaMsd;
	private java.lang.Long sensibilidadeRevisitaFace;
	private java.util.Date dataRevisao;
	private java.lang.Long classificacaoFinal;
	private java.lang.Long criterioClassificacao;
	private java.lang.Long evolucaoCaso;
	private java.util.Date dataObito;
	private java.lang.String observacao;
	private java.util.Date dataEncerramento;

	// many to one
	private br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo;
	private br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo;
	private br.com.ksisolucoes.vo.basico.Pais cdPaisOrigem;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid hipoteseDiagnosticaCid;
	private br.com.ksisolucoes.vo.basico.Empresa hospital;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid diagosticoSugestivo;
	private br.com.ksisolucoes.vo.prontuario.basico.Cid diagosticoCasoDescartado;
	private br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="sequence"
     *  column="cd_investigacao_agr_pararilisa_flacida_aguda"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: flag_informacoes_complementares
	 */
	public java.lang.String getFlagInformacoesComplementares () {
		return getPropertyValue(this, flagInformacoesComplementares, PROP_FLAG_INFORMACOES_COMPLEMENTARES); 
	}

	/**
	 * Set the value related to the column: flag_informacoes_complementares
	 * @param flagInformacoesComplementares the flag_informacoes_complementares value
	 */
	public void setFlagInformacoesComplementares (java.lang.String flagInformacoesComplementares) {
//        java.lang.String flagInformacoesComplementaresOld = this.flagInformacoesComplementares;
		this.flagInformacoesComplementares = flagInformacoesComplementares;
//        this.getPropertyChangeSupport().firePropertyChange ("flagInformacoesComplementares", flagInformacoesComplementaresOld, flagInformacoesComplementares);
	}



	/**
	 * Return the value associated with the column: dt_investigacao
	 */
	public java.util.Date getDataInvestigacao () {
		return getPropertyValue(this, dataInvestigacao, PROP_DATA_INVESTIGACAO); 
	}

	/**
	 * Set the value related to the column: dt_investigacao
	 * @param dataInvestigacao the dt_investigacao value
	 */
	public void setDataInvestigacao (java.util.Date dataInvestigacao) {
//        java.util.Date dataInvestigacaoOld = this.dataInvestigacao;
		this.dataInvestigacao = dataInvestigacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInvestigacao", dataInvestigacaoOld, dataInvestigacao);
	}



	/**
	 * Return the value associated with the column: dt_1_consulta
	 */
	public java.util.Date getDt1Consulta () {
		return getPropertyValue(this, dt1Consulta, PROP_DT1_CONSULTA); 
	}

	/**
	 * Set the value related to the column: dt_1_consulta
	 * @param dt1Consulta the dt_1_consulta value
	 */
	public void setDt1Consulta (java.util.Date dt1Consulta) {
//        java.util.Date dt1ConsultaOld = this.dt1Consulta;
		this.dt1Consulta = dt1Consulta;
//        this.getPropertyChangeSupport().firePropertyChange ("dt1Consulta", dt1ConsultaOld, dt1Consulta);
	}



	/**
	 * Return the value associated with the column: tomou_vacina_poliomelite
	 */
	public java.lang.Long getTomouVacinaPoliomelite () {
		return getPropertyValue(this, tomouVacinaPoliomelite, PROP_TOMOU_VACINA_POLIOMELITE); 
	}

	/**
	 * Set the value related to the column: tomou_vacina_poliomelite
	 * @param tomouVacinaPoliomelite the tomou_vacina_poliomelite value
	 */
	public void setTomouVacinaPoliomelite (java.lang.Long tomouVacinaPoliomelite) {
//        java.lang.Long tomouVacinaPoliomeliteOld = this.tomouVacinaPoliomelite;
		this.tomouVacinaPoliomelite = tomouVacinaPoliomelite;
//        this.getPropertyChangeSupport().firePropertyChange ("tomouVacinaPoliomelite", tomouVacinaPoliomeliteOld, tomouVacinaPoliomelite);
	}



	/**
	 * Return the value associated with the column: numero_dose
	 */
	public java.lang.String getNumeroDose () {
		return getPropertyValue(this, numeroDose, PROP_NUMERO_DOSE); 
	}

	/**
	 * Set the value related to the column: numero_dose
	 * @param numeroDose the numero_dose value
	 */
	public void setNumeroDose (java.lang.String numeroDose) {
//        java.lang.String numeroDoseOld = this.numeroDose;
		this.numeroDose = numeroDose;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroDose", numeroDoseOld, numeroDose);
	}



	/**
	 * Return the value associated with the column: dt_ultima_dose
	 */
	public java.util.Date getDtUltimaDose () {
		return getPropertyValue(this, dtUltimaDose, PROP_DT_ULTIMA_DOSE); 
	}

	/**
	 * Set the value related to the column: dt_ultima_dose
	 * @param dtUltimaDose the dt_ultima_dose value
	 */
	public void setDtUltimaDose (java.util.Date dtUltimaDose) {
//        java.util.Date dtUltimaDoseOld = this.dtUltimaDose;
		this.dtUltimaDose = dtUltimaDose;
//        this.getPropertyChangeSupport().firePropertyChange ("dtUltimaDose", dtUltimaDoseOld, dtUltimaDose);
	}



	/**
	 * Return the value associated with the column: viajou_recebeu_visitas
	 */
	public java.lang.Long getViajouRecebeuVisitas () {
		return getPropertyValue(this, viajouRecebeuVisitas, PROP_VIAJOU_RECEBEU_VISITAS); 
	}

	/**
	 * Set the value related to the column: viajou_recebeu_visitas
	 * @param viajouRecebeuVisitas the viajou_recebeu_visitas value
	 */
	public void setViajouRecebeuVisitas (java.lang.Long viajouRecebeuVisitas) {
//        java.lang.Long viajouRecebeuVisitasOld = this.viajouRecebeuVisitas;
		this.viajouRecebeuVisitas = viajouRecebeuVisitas;
//        this.getPropertyChangeSupport().firePropertyChange ("viajouRecebeuVisitas", viajouRecebeuVisitasOld, viajouRecebeuVisitas);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_febre
	 */
	public java.lang.Long getSinaisSintomasFebre () {
		return getPropertyValue(this, sinaisSintomasFebre, PROP_SINAIS_SINTOMAS_FEBRE); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_febre
	 * @param sinaisSintomasFebre the sinais_sintomas_febre value
	 */
	public void setSinaisSintomasFebre (java.lang.Long sinaisSintomasFebre) {
//        java.lang.Long sinaisSintomasFebreOld = this.sinaisSintomasFebre;
		this.sinaisSintomasFebre = sinaisSintomasFebre;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasFebre", sinaisSintomasFebreOld, sinaisSintomasFebre);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_vomitos
	 */
	public java.lang.Long getSinaisSintomasVomitos () {
		return getPropertyValue(this, sinaisSintomasVomitos, PROP_SINAIS_SINTOMAS_VOMITOS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_vomitos
	 * @param sinaisSintomasVomitos the sinais_sintomas_vomitos value
	 */
	public void setSinaisSintomasVomitos (java.lang.Long sinaisSintomasVomitos) {
//        java.lang.Long sinaisSintomasVomitosOld = this.sinaisSintomasVomitos;
		this.sinaisSintomasVomitos = sinaisSintomasVomitos;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasVomitos", sinaisSintomasVomitosOld, sinaisSintomasVomitos);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_diarreia
	 */
	public java.lang.Long getSinaisSintomasDiarreia () {
		return getPropertyValue(this, sinaisSintomasDiarreia, PROP_SINAIS_SINTOMAS_DIARREIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_diarreia
	 * @param sinaisSintomasDiarreia the sinais_sintomas_diarreia value
	 */
	public void setSinaisSintomasDiarreia (java.lang.Long sinaisSintomasDiarreia) {
//        java.lang.Long sinaisSintomasDiarreiaOld = this.sinaisSintomasDiarreia;
		this.sinaisSintomasDiarreia = sinaisSintomasDiarreia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasDiarreia", sinaisSintomasDiarreiaOld, sinaisSintomasDiarreia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_obstipacao
	 */
	public java.lang.Long getSinaisSintomasObstipacao () {
		return getPropertyValue(this, sinaisSintomasObstipacao, PROP_SINAIS_SINTOMAS_OBSTIPACAO); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_obstipacao
	 * @param sinaisSintomasObstipacao the sinais_sintomas_obstipacao value
	 */
	public void setSinaisSintomasObstipacao (java.lang.Long sinaisSintomasObstipacao) {
//        java.lang.Long sinaisSintomasObstipacaoOld = this.sinaisSintomasObstipacao;
		this.sinaisSintomasObstipacao = sinaisSintomasObstipacao;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasObstipacao", sinaisSintomasObstipacaoOld, sinaisSintomasObstipacao);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_dores_musculares
	 */
	public java.lang.Long getSinaisSintomasDoresMusculares () {
		return getPropertyValue(this, sinaisSintomasDoresMusculares, PROP_SINAIS_SINTOMAS_DORES_MUSCULARES); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_dores_musculares
	 * @param sinaisSintomasDoresMusculares the sinais_sintomas_dores_musculares value
	 */
	public void setSinaisSintomasDoresMusculares (java.lang.Long sinaisSintomasDoresMusculares) {
//        java.lang.Long sinaisSintomasDoresMuscularesOld = this.sinaisSintomasDoresMusculares;
		this.sinaisSintomasDoresMusculares = sinaisSintomasDoresMusculares;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasDoresMusculares", sinaisSintomasDoresMuscularesOld, sinaisSintomasDoresMusculares);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_cefaleia
	 */
	public java.lang.Long getSinaisSintomasCefaleia () {
		return getPropertyValue(this, sinaisSintomasCefaleia, PROP_SINAIS_SINTOMAS_CEFALEIA); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_cefaleia
	 * @param sinaisSintomasCefaleia the sinais_sintomas_cefaleia value
	 */
	public void setSinaisSintomasCefaleia (java.lang.Long sinaisSintomasCefaleia) {
//        java.lang.Long sinaisSintomasCefaleiaOld = this.sinaisSintomasCefaleia;
		this.sinaisSintomasCefaleia = sinaisSintomasCefaleia;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasCefaleia", sinaisSintomasCefaleiaOld, sinaisSintomasCefaleia);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_sintomas_resp
	 */
	public java.lang.Long getSinaisSintomasSintomasResp () {
		return getPropertyValue(this, sinaisSintomasSintomasResp, PROP_SINAIS_SINTOMAS_SINTOMAS_RESP); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_sintomas_resp
	 * @param sinaisSintomasSintomasResp the sinais_sintomas_sintomas_resp value
	 */
	public void setSinaisSintomasSintomasResp (java.lang.Long sinaisSintomasSintomasResp) {
//        java.lang.Long sinaisSintomasSintomasRespOld = this.sinaisSintomasSintomasResp;
		this.sinaisSintomasSintomasResp = sinaisSintomasSintomasResp;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasSintomasResp", sinaisSintomasSintomasRespOld, sinaisSintomasSintomasResp);
	}



	/**
	 * Return the value associated with the column: sinais_sintomas_outros
	 */
	public java.lang.String getSinaisSintomasOutros () {
		return getPropertyValue(this, sinaisSintomasOutros, PROP_SINAIS_SINTOMAS_OUTROS); 
	}

	/**
	 * Set the value related to the column: sinais_sintomas_outros
	 * @param sinaisSintomasOutros the sinais_sintomas_outros value
	 */
	public void setSinaisSintomasOutros (java.lang.String sinaisSintomasOutros) {
//        java.lang.String sinaisSintomasOutrosOld = this.sinaisSintomasOutros;
		this.sinaisSintomasOutros = sinaisSintomasOutros;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisSintomasOutros", sinaisSintomasOutrosOld, sinaisSintomasOutros);
	}



	/**
	 * Return the value associated with the column: dt_inicio_def_motora
	 */
	public java.util.Date getDtInicioDefMotora () {
		return getPropertyValue(this, dtInicioDefMotora, PROP_DT_INICIO_DEF_MOTORA); 
	}

	/**
	 * Set the value related to the column: dt_inicio_def_motora
	 * @param dtInicioDefMotora the dt_inicio_def_motora value
	 */
	public void setDtInicioDefMotora (java.util.Date dtInicioDefMotora) {
//        java.util.Date dtInicioDefMotoraOld = this.dtInicioDefMotora;
		this.dtInicioDefMotora = dtInicioDefMotora;
//        this.getPropertyChangeSupport().firePropertyChange ("dtInicioDefMotora", dtInicioDefMotoraOld, dtInicioDefMotora);
	}



	/**
	 * Return the value associated with the column: deficiencia_motora_aguda
	 */
	public java.lang.Long getDeficienciaMotoraAguda () {
		return getPropertyValue(this, deficienciaMotoraAguda, PROP_DEFICIENCIA_MOTORA_AGUDA); 
	}

	/**
	 * Set the value related to the column: deficiencia_motora_aguda
	 * @param deficienciaMotoraAguda the deficiencia_motora_aguda value
	 */
	public void setDeficienciaMotoraAguda (java.lang.Long deficienciaMotoraAguda) {
//        java.lang.Long deficienciaMotoraAgudaOld = this.deficienciaMotoraAguda;
		this.deficienciaMotoraAguda = deficienciaMotoraAguda;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaMotoraAguda", deficienciaMotoraAgudaOld, deficienciaMotoraAguda);
	}



	/**
	 * Return the value associated with the column: deficiencia_motora_flacida
	 */
	public java.lang.Long getDeficienciaMotoraFlacida () {
		return getPropertyValue(this, deficienciaMotoraFlacida, PROP_DEFICIENCIA_MOTORA_FLACIDA); 
	}

	/**
	 * Set the value related to the column: deficiencia_motora_flacida
	 * @param deficienciaMotoraFlacida the deficiencia_motora_flacida value
	 */
	public void setDeficienciaMotoraFlacida (java.lang.Long deficienciaMotoraFlacida) {
//        java.lang.Long deficienciaMotoraFlacidaOld = this.deficienciaMotoraFlacida;
		this.deficienciaMotoraFlacida = deficienciaMotoraFlacida;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaMotoraFlacida", deficienciaMotoraFlacidaOld, deficienciaMotoraFlacida);
	}



	/**
	 * Return the value associated with the column: deficiencia_motora_assimetrica
	 */
	public java.lang.Long getDeficienciaMotoraAssimetrica () {
		return getPropertyValue(this, deficienciaMotoraAssimetrica, PROP_DEFICIENCIA_MOTORA_ASSIMETRICA); 
	}

	/**
	 * Set the value related to the column: deficiencia_motora_assimetrica
	 * @param deficienciaMotoraAssimetrica the deficiencia_motora_assimetrica value
	 */
	public void setDeficienciaMotoraAssimetrica (java.lang.Long deficienciaMotoraAssimetrica) {
//        java.lang.Long deficienciaMotoraAssimetricaOld = this.deficienciaMotoraAssimetrica;
		this.deficienciaMotoraAssimetrica = deficienciaMotoraAssimetrica;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaMotoraAssimetrica", deficienciaMotoraAssimetricaOld, deficienciaMotoraAssimetrica);
	}



	/**
	 * Return the value associated with the column: deficiencia_motora_progressao_3_dias
	 */
	public java.lang.Long getDeficienciaMotoraProgressao3Dias () {
		return getPropertyValue(this, deficienciaMotoraProgressao3Dias, PROP_DEFICIENCIA_MOTORA_PROGRESSAO3_DIAS); 
	}

	/**
	 * Set the value related to the column: deficiencia_motora_progressao_3_dias
	 * @param deficienciaMotoraProgressao3Dias the deficiencia_motora_progressao_3_dias value
	 */
	public void setDeficienciaMotoraProgressao3Dias (java.lang.Long deficienciaMotoraProgressao3Dias) {
//        java.lang.Long deficienciaMotoraProgressao3DiasOld = this.deficienciaMotoraProgressao3Dias;
		this.deficienciaMotoraProgressao3Dias = deficienciaMotoraProgressao3Dias;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaMotoraProgressao3Dias", deficienciaMotoraProgressao3DiasOld, deficienciaMotoraProgressao3Dias);
	}



	/**
	 * Return the value associated with the column: deficiencia_motora_ascendente
	 */
	public java.lang.Long getDeficienciaMotoraAscendente () {
		return getPropertyValue(this, deficienciaMotoraAscendente, PROP_DEFICIENCIA_MOTORA_ASCENDENTE); 
	}

	/**
	 * Set the value related to the column: deficiencia_motora_ascendente
	 * @param deficienciaMotoraAscendente the deficiencia_motora_ascendente value
	 */
	public void setDeficienciaMotoraAscendente (java.lang.Long deficienciaMotoraAscendente) {
//        java.lang.Long deficienciaMotoraAscendenteOld = this.deficienciaMotoraAscendente;
		this.deficienciaMotoraAscendente = deficienciaMotoraAscendente;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaMotoraAscendente", deficienciaMotoraAscendenteOld, deficienciaMotoraAscendente);
	}



	/**
	 * Return the value associated with the column: deficiencia_motora_descendente
	 */
	public java.lang.Long getDeficienciaMotoraDescendente () {
		return getPropertyValue(this, deficienciaMotoraDescendente, PROP_DEFICIENCIA_MOTORA_DESCENDENTE); 
	}

	/**
	 * Set the value related to the column: deficiencia_motora_descendente
	 * @param deficienciaMotoraDescendente the deficiencia_motora_descendente value
	 */
	public void setDeficienciaMotoraDescendente (java.lang.Long deficienciaMotoraDescendente) {
//        java.lang.Long deficienciaMotoraDescendenteOld = this.deficienciaMotoraDescendente;
		this.deficienciaMotoraDescendente = deficienciaMotoraDescendente;
//        this.getPropertyChangeSupport().firePropertyChange ("deficienciaMotoraDescendente", deficienciaMotoraDescendenteOld, deficienciaMotoraDescendente);
	}



	/**
	 * Return the value associated with the column: forca_muscular_mie
	 */
	public java.lang.Long getForcaMuscularMie () {
		return getPropertyValue(this, forcaMuscularMie, PROP_FORCA_MUSCULAR_MIE); 
	}

	/**
	 * Set the value related to the column: forca_muscular_mie
	 * @param forcaMuscularMie the forca_muscular_mie value
	 */
	public void setForcaMuscularMie (java.lang.Long forcaMuscularMie) {
//        java.lang.Long forcaMuscularMieOld = this.forcaMuscularMie;
		this.forcaMuscularMie = forcaMuscularMie;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularMie", forcaMuscularMieOld, forcaMuscularMie);
	}



	/**
	 * Return the value associated with the column: forca_muscular_mse
	 */
	public java.lang.Long getForcaMuscularMse () {
		return getPropertyValue(this, forcaMuscularMse, PROP_FORCA_MUSCULAR_MSE); 
	}

	/**
	 * Set the value related to the column: forca_muscular_mse
	 * @param forcaMuscularMse the forca_muscular_mse value
	 */
	public void setForcaMuscularMse (java.lang.Long forcaMuscularMse) {
//        java.lang.Long forcaMuscularMseOld = this.forcaMuscularMse;
		this.forcaMuscularMse = forcaMuscularMse;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularMse", forcaMuscularMseOld, forcaMuscularMse);
	}



	/**
	 * Return the value associated with the column: forca_muscular_mid
	 */
	public java.lang.Long getForcaMuscularMid () {
		return getPropertyValue(this, forcaMuscularMid, PROP_FORCA_MUSCULAR_MID); 
	}

	/**
	 * Set the value related to the column: forca_muscular_mid
	 * @param forcaMuscularMid the forca_muscular_mid value
	 */
	public void setForcaMuscularMid (java.lang.Long forcaMuscularMid) {
//        java.lang.Long forcaMuscularMidOld = this.forcaMuscularMid;
		this.forcaMuscularMid = forcaMuscularMid;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularMid", forcaMuscularMidOld, forcaMuscularMid);
	}



	/**
	 * Return the value associated with the column: forca_muscular_msd
	 */
	public java.lang.Long getForcaMuscularMsd () {
		return getPropertyValue(this, forcaMuscularMsd, PROP_FORCA_MUSCULAR_MSD); 
	}

	/**
	 * Set the value related to the column: forca_muscular_msd
	 * @param forcaMuscularMsd the forca_muscular_msd value
	 */
	public void setForcaMuscularMsd (java.lang.Long forcaMuscularMsd) {
//        java.lang.Long forcaMuscularMsdOld = this.forcaMuscularMsd;
		this.forcaMuscularMsd = forcaMuscularMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularMsd", forcaMuscularMsdOld, forcaMuscularMsd);
	}



	/**
	 * Return the value associated with the column: localizacao_mie
	 */
	public java.lang.Long getLocalizacaoMie () {
		return getPropertyValue(this, localizacaoMie, PROP_LOCALIZACAO_MIE); 
	}

	/**
	 * Set the value related to the column: localizacao_mie
	 * @param localizacaoMie the localizacao_mie value
	 */
	public void setLocalizacaoMie (java.lang.Long localizacaoMie) {
//        java.lang.Long localizacaoMieOld = this.localizacaoMie;
		this.localizacaoMie = localizacaoMie;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacaoMie", localizacaoMieOld, localizacaoMie);
	}



	/**
	 * Return the value associated with the column: localizacao_mse
	 */
	public java.lang.Long getLocalizacaoMse () {
		return getPropertyValue(this, localizacaoMse, PROP_LOCALIZACAO_MSE); 
	}

	/**
	 * Set the value related to the column: localizacao_mse
	 * @param localizacaoMse the localizacao_mse value
	 */
	public void setLocalizacaoMse (java.lang.Long localizacaoMse) {
//        java.lang.Long localizacaoMseOld = this.localizacaoMse;
		this.localizacaoMse = localizacaoMse;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacaoMse", localizacaoMseOld, localizacaoMse);
	}



	/**
	 * Return the value associated with the column: localizacao_mid
	 */
	public java.lang.Long getLocalizacaoMid () {
		return getPropertyValue(this, localizacaoMid, PROP_LOCALIZACAO_MID); 
	}

	/**
	 * Set the value related to the column: localizacao_mid
	 * @param localizacaoMid the localizacao_mid value
	 */
	public void setLocalizacaoMid (java.lang.Long localizacaoMid) {
//        java.lang.Long localizacaoMidOld = this.localizacaoMid;
		this.localizacaoMid = localizacaoMid;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacaoMid", localizacaoMidOld, localizacaoMid);
	}



	/**
	 * Return the value associated with the column: localizacao_msd
	 */
	public java.lang.Long getLocalizacaoMsd () {
		return getPropertyValue(this, localizacaoMsd, PROP_LOCALIZACAO_MSD); 
	}

	/**
	 * Set the value related to the column: localizacao_msd
	 * @param localizacaoMsd the localizacao_msd value
	 */
	public void setLocalizacaoMsd (java.lang.Long localizacaoMsd) {
//        java.lang.Long localizacaoMsdOld = this.localizacaoMsd;
		this.localizacaoMsd = localizacaoMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("localizacaoMsd", localizacaoMsdOld, localizacaoMsd);
	}



	/**
	 * Return the value associated with the column: comprometimento_musc_respiratoria
	 */
	public java.lang.Long getComprometimentoMuscRespiratoria () {
		return getPropertyValue(this, comprometimentoMuscRespiratoria, PROP_COMPROMETIMENTO_MUSC_RESPIRATORIA); 
	}

	/**
	 * Set the value related to the column: comprometimento_musc_respiratoria
	 * @param comprometimentoMuscRespiratoria the comprometimento_musc_respiratoria value
	 */
	public void setComprometimentoMuscRespiratoria (java.lang.Long comprometimentoMuscRespiratoria) {
//        java.lang.Long comprometimentoMuscRespiratoriaOld = this.comprometimentoMuscRespiratoria;
		this.comprometimentoMuscRespiratoria = comprometimentoMuscRespiratoria;
//        this.getPropertyChangeSupport().firePropertyChange ("comprometimentoMuscRespiratoria", comprometimentoMuscRespiratoriaOld, comprometimentoMuscRespiratoria);
	}



	/**
	 * Return the value associated with the column: comprometimento_musc_cervical
	 */
	public java.lang.Long getComprometimentoMuscCervical () {
		return getPropertyValue(this, comprometimentoMuscCervical, PROP_COMPROMETIMENTO_MUSC_CERVICAL); 
	}

	/**
	 * Set the value related to the column: comprometimento_musc_cervical
	 * @param comprometimentoMuscCervical the comprometimento_musc_cervical value
	 */
	public void setComprometimentoMuscCervical (java.lang.Long comprometimentoMuscCervical) {
//        java.lang.Long comprometimentoMuscCervicalOld = this.comprometimentoMuscCervical;
		this.comprometimentoMuscCervical = comprometimentoMuscCervical;
//        this.getPropertyChangeSupport().firePropertyChange ("comprometimentoMuscCervical", comprometimentoMuscCervicalOld, comprometimentoMuscCervical);
	}



	/**
	 * Return the value associated with the column: comprometimento_face
	 */
	public java.lang.Long getComprometimentoFace () {
		return getPropertyValue(this, comprometimentoFace, PROP_COMPROMETIMENTO_FACE); 
	}

	/**
	 * Set the value related to the column: comprometimento_face
	 * @param comprometimentoFace the comprometimento_face value
	 */
	public void setComprometimentoFace (java.lang.Long comprometimentoFace) {
//        java.lang.Long comprometimentoFaceOld = this.comprometimentoFace;
		this.comprometimentoFace = comprometimentoFace;
//        this.getPropertyChangeSupport().firePropertyChange ("comprometimentoFace", comprometimentoFaceOld, comprometimentoFace);
	}



	/**
	 * Return the value associated with the column: exame_aguda_dt_exame
	 */
	public java.util.Date getExameAgudaDtExame () {
		return getPropertyValue(this, exameAgudaDtExame, PROP_EXAME_AGUDA_DT_EXAME); 
	}

	/**
	 * Set the value related to the column: exame_aguda_dt_exame
	 * @param exameAgudaDtExame the exame_aguda_dt_exame value
	 */
	public void setExameAgudaDtExame (java.util.Date exameAgudaDtExame) {
//        java.util.Date exameAgudaDtExameOld = this.exameAgudaDtExame;
		this.exameAgudaDtExame = exameAgudaDtExame;
//        this.getPropertyChangeSupport().firePropertyChange ("exameAgudaDtExame", exameAgudaDtExameOld, exameAgudaDtExame);
	}



	/**
	 * Return the value associated with the column: forca_muscular_aguda_mie
	 */
	public java.lang.Long getForcaMuscularAgudaMie () {
		return getPropertyValue(this, forcaMuscularAgudaMie, PROP_FORCA_MUSCULAR_AGUDA_MIE); 
	}

	/**
	 * Set the value related to the column: forca_muscular_aguda_mie
	 * @param forcaMuscularAgudaMie the forca_muscular_aguda_mie value
	 */
	public void setForcaMuscularAgudaMie (java.lang.Long forcaMuscularAgudaMie) {
//        java.lang.Long forcaMuscularAgudaMieOld = this.forcaMuscularAgudaMie;
		this.forcaMuscularAgudaMie = forcaMuscularAgudaMie;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularAgudaMie", forcaMuscularAgudaMieOld, forcaMuscularAgudaMie);
	}



	/**
	 * Return the value associated with the column: forca_muscular_aguda_mse
	 */
	public java.lang.Long getForcaMuscularAgudaMse () {
		return getPropertyValue(this, forcaMuscularAgudaMse, PROP_FORCA_MUSCULAR_AGUDA_MSE); 
	}

	/**
	 * Set the value related to the column: forca_muscular_aguda_mse
	 * @param forcaMuscularAgudaMse the forca_muscular_aguda_mse value
	 */
	public void setForcaMuscularAgudaMse (java.lang.Long forcaMuscularAgudaMse) {
//        java.lang.Long forcaMuscularAgudaMseOld = this.forcaMuscularAgudaMse;
		this.forcaMuscularAgudaMse = forcaMuscularAgudaMse;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularAgudaMse", forcaMuscularAgudaMseOld, forcaMuscularAgudaMse);
	}



	/**
	 * Return the value associated with the column: forca_muscular_aguda_mid
	 */
	public java.lang.Long getForcaMuscularAgudaMid () {
		return getPropertyValue(this, forcaMuscularAgudaMid, PROP_FORCA_MUSCULAR_AGUDA_MID); 
	}

	/**
	 * Set the value related to the column: forca_muscular_aguda_mid
	 * @param forcaMuscularAgudaMid the forca_muscular_aguda_mid value
	 */
	public void setForcaMuscularAgudaMid (java.lang.Long forcaMuscularAgudaMid) {
//        java.lang.Long forcaMuscularAgudaMidOld = this.forcaMuscularAgudaMid;
		this.forcaMuscularAgudaMid = forcaMuscularAgudaMid;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularAgudaMid", forcaMuscularAgudaMidOld, forcaMuscularAgudaMid);
	}



	/**
	 * Return the value associated with the column: forca_muscular_aguda_msd
	 */
	public java.lang.Long getForcaMuscularAgudaMsd () {
		return getPropertyValue(this, forcaMuscularAgudaMsd, PROP_FORCA_MUSCULAR_AGUDA_MSD); 
	}

	/**
	 * Set the value related to the column: forca_muscular_aguda_msd
	 * @param forcaMuscularAgudaMsd the forca_muscular_aguda_msd value
	 */
	public void setForcaMuscularAgudaMsd (java.lang.Long forcaMuscularAgudaMsd) {
//        java.lang.Long forcaMuscularAgudaMsdOld = this.forcaMuscularAgudaMsd;
		this.forcaMuscularAgudaMsd = forcaMuscularAgudaMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularAgudaMsd", forcaMuscularAgudaMsdOld, forcaMuscularAgudaMsd);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_mie
	 */
	public java.lang.Long getTonusMuscularMie () {
		return getPropertyValue(this, tonusMuscularMie, PROP_TONUS_MUSCULAR_MIE); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_mie
	 * @param tonusMuscularMie the tonus_muscular_mie value
	 */
	public void setTonusMuscularMie (java.lang.Long tonusMuscularMie) {
//        java.lang.Long tonusMuscularMieOld = this.tonusMuscularMie;
		this.tonusMuscularMie = tonusMuscularMie;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularMie", tonusMuscularMieOld, tonusMuscularMie);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_mse
	 */
	public java.lang.Long getTonusMuscularMse () {
		return getPropertyValue(this, tonusMuscularMse, PROP_TONUS_MUSCULAR_MSE); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_mse
	 * @param tonusMuscularMse the tonus_muscular_mse value
	 */
	public void setTonusMuscularMse (java.lang.Long tonusMuscularMse) {
//        java.lang.Long tonusMuscularMseOld = this.tonusMuscularMse;
		this.tonusMuscularMse = tonusMuscularMse;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularMse", tonusMuscularMseOld, tonusMuscularMse);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_mid
	 */
	public java.lang.Long getTonusMuscularMid () {
		return getPropertyValue(this, tonusMuscularMid, PROP_TONUS_MUSCULAR_MID); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_mid
	 * @param tonusMuscularMid the tonus_muscular_mid value
	 */
	public void setTonusMuscularMid (java.lang.Long tonusMuscularMid) {
//        java.lang.Long tonusMuscularMidOld = this.tonusMuscularMid;
		this.tonusMuscularMid = tonusMuscularMid;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularMid", tonusMuscularMidOld, tonusMuscularMid);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_msd
	 */
	public java.lang.Long getTonusMuscularMsd () {
		return getPropertyValue(this, tonusMuscularMsd, PROP_TONUS_MUSCULAR_MSD); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_msd
	 * @param tonusMuscularMsd the tonus_muscular_msd value
	 */
	public void setTonusMuscularMsd (java.lang.Long tonusMuscularMsd) {
//        java.lang.Long tonusMuscularMsdOld = this.tonusMuscularMsd;
		this.tonusMuscularMsd = tonusMuscularMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularMsd", tonusMuscularMsdOld, tonusMuscularMsd);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_cervical
	 */
	public java.lang.Long getTonusMuscularCervical () {
		return getPropertyValue(this, tonusMuscularCervical, PROP_TONUS_MUSCULAR_CERVICAL); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_cervical
	 * @param tonusMuscularCervical the tonus_muscular_cervical value
	 */
	public void setTonusMuscularCervical (java.lang.Long tonusMuscularCervical) {
//        java.lang.Long tonusMuscularCervicalOld = this.tonusMuscularCervical;
		this.tonusMuscularCervical = tonusMuscularCervical;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularCervical", tonusMuscularCervicalOld, tonusMuscularCervical);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_face
	 */
	public java.lang.Long getTonusMuscularFace () {
		return getPropertyValue(this, tonusMuscularFace, PROP_TONUS_MUSCULAR_FACE); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_face
	 * @param tonusMuscularFace the tonus_muscular_face value
	 */
	public void setTonusMuscularFace (java.lang.Long tonusMuscularFace) {
//        java.lang.Long tonusMuscularFaceOld = this.tonusMuscularFace;
		this.tonusMuscularFace = tonusMuscularFace;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularFace", tonusMuscularFaceOld, tonusMuscularFace);
	}



	/**
	 * Return the value associated with the column: sensibilidade_mie
	 */
	public java.lang.Long getSensibilidadeMie () {
		return getPropertyValue(this, sensibilidadeMie, PROP_SENSIBILIDADE_MIE); 
	}

	/**
	 * Set the value related to the column: sensibilidade_mie
	 * @param sensibilidadeMie the sensibilidade_mie value
	 */
	public void setSensibilidadeMie (java.lang.Long sensibilidadeMie) {
//        java.lang.Long sensibilidadeMieOld = this.sensibilidadeMie;
		this.sensibilidadeMie = sensibilidadeMie;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeMie", sensibilidadeMieOld, sensibilidadeMie);
	}



	/**
	 * Return the value associated with the column: sensibilidade_mse
	 */
	public java.lang.Long getSensibilidadeMse () {
		return getPropertyValue(this, sensibilidadeMse, PROP_SENSIBILIDADE_MSE); 
	}

	/**
	 * Set the value related to the column: sensibilidade_mse
	 * @param sensibilidadeMse the sensibilidade_mse value
	 */
	public void setSensibilidadeMse (java.lang.Long sensibilidadeMse) {
//        java.lang.Long sensibilidadeMseOld = this.sensibilidadeMse;
		this.sensibilidadeMse = sensibilidadeMse;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeMse", sensibilidadeMseOld, sensibilidadeMse);
	}



	/**
	 * Return the value associated with the column: sensibilidade_mid
	 */
	public java.lang.Long getSensibilidadeMid () {
		return getPropertyValue(this, sensibilidadeMid, PROP_SENSIBILIDADE_MID); 
	}

	/**
	 * Set the value related to the column: sensibilidade_mid
	 * @param sensibilidadeMid the sensibilidade_mid value
	 */
	public void setSensibilidadeMid (java.lang.Long sensibilidadeMid) {
//        java.lang.Long sensibilidadeMidOld = this.sensibilidadeMid;
		this.sensibilidadeMid = sensibilidadeMid;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeMid", sensibilidadeMidOld, sensibilidadeMid);
	}



	/**
	 * Return the value associated with the column: sensibilidade_msd
	 */
	public java.lang.Long getSensibilidadeMsd () {
		return getPropertyValue(this, sensibilidadeMsd, PROP_SENSIBILIDADE_MSD); 
	}

	/**
	 * Set the value related to the column: sensibilidade_msd
	 * @param sensibilidadeMsd the sensibilidade_msd value
	 */
	public void setSensibilidadeMsd (java.lang.Long sensibilidadeMsd) {
//        java.lang.Long sensibilidadeMsdOld = this.sensibilidadeMsd;
		this.sensibilidadeMsd = sensibilidadeMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeMsd", sensibilidadeMsdOld, sensibilidadeMsd);
	}



	/**
	 * Return the value associated with the column: sensibilidade_face
	 */
	public java.lang.Long getSensibilidadeFace () {
		return getPropertyValue(this, sensibilidadeFace, PROP_SENSIBILIDADE_FACE); 
	}

	/**
	 * Set the value related to the column: sensibilidade_face
	 * @param sensibilidadeFace the sensibilidade_face value
	 */
	public void setSensibilidadeFace (java.lang.Long sensibilidadeFace) {
//        java.lang.Long sensibilidadeFaceOld = this.sensibilidadeFace;
		this.sensibilidadeFace = sensibilidadeFace;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeFace", sensibilidadeFaceOld, sensibilidadeFace);
	}



	/**
	 * Return the value associated with the column: reflexos_aquileu_e
	 */
	public java.lang.Long getReflexosAquileuE () {
		return getPropertyValue(this, reflexosAquileuE, PROP_REFLEXOS_AQUILEU_E); 
	}

	/**
	 * Set the value related to the column: reflexos_aquileu_e
	 * @param reflexosAquileuE the reflexos_aquileu_e value
	 */
	public void setReflexosAquileuE (java.lang.Long reflexosAquileuE) {
//        java.lang.Long reflexosAquileuEOld = this.reflexosAquileuE;
		this.reflexosAquileuE = reflexosAquileuE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosAquileuE", reflexosAquileuEOld, reflexosAquileuE);
	}



	/**
	 * Return the value associated with the column: reflexos_aquileu_d
	 */
	public java.lang.Long getReflexosAquileuD () {
		return getPropertyValue(this, reflexosAquileuD, PROP_REFLEXOS_AQUILEU_D); 
	}

	/**
	 * Set the value related to the column: reflexos_aquileu_d
	 * @param reflexosAquileuD the reflexos_aquileu_d value
	 */
	public void setReflexosAquileuD (java.lang.Long reflexosAquileuD) {
//        java.lang.Long reflexosAquileuDOld = this.reflexosAquileuD;
		this.reflexosAquileuD = reflexosAquileuD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosAquileuD", reflexosAquileuDOld, reflexosAquileuD);
	}



	/**
	 * Return the value associated with the column: reflexos_patelar_e
	 */
	public java.lang.Long getReflexosPatelarE () {
		return getPropertyValue(this, reflexosPatelarE, PROP_REFLEXOS_PATELAR_E); 
	}

	/**
	 * Set the value related to the column: reflexos_patelar_e
	 * @param reflexosPatelarE the reflexos_patelar_e value
	 */
	public void setReflexosPatelarE (java.lang.Long reflexosPatelarE) {
//        java.lang.Long reflexosPatelarEOld = this.reflexosPatelarE;
		this.reflexosPatelarE = reflexosPatelarE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosPatelarE", reflexosPatelarEOld, reflexosPatelarE);
	}



	/**
	 * Return the value associated with the column: reflexos_patelar_d
	 */
	public java.lang.Long getReflexosPatelarD () {
		return getPropertyValue(this, reflexosPatelarD, PROP_REFLEXOS_PATELAR_D); 
	}

	/**
	 * Set the value related to the column: reflexos_patelar_d
	 * @param reflexosPatelarD the reflexos_patelar_d value
	 */
	public void setReflexosPatelarD (java.lang.Long reflexosPatelarD) {
//        java.lang.Long reflexosPatelarDOld = this.reflexosPatelarD;
		this.reflexosPatelarD = reflexosPatelarD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosPatelarD", reflexosPatelarDOld, reflexosPatelarD);
	}



	/**
	 * Return the value associated with the column: reflexos_bicipal_e
	 */
	public java.lang.Long getReflexosBicipalE () {
		return getPropertyValue(this, reflexosBicipalE, PROP_REFLEXOS_BICIPAL_E); 
	}

	/**
	 * Set the value related to the column: reflexos_bicipal_e
	 * @param reflexosBicipalE the reflexos_bicipal_e value
	 */
	public void setReflexosBicipalE (java.lang.Long reflexosBicipalE) {
//        java.lang.Long reflexosBicipalEOld = this.reflexosBicipalE;
		this.reflexosBicipalE = reflexosBicipalE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosBicipalE", reflexosBicipalEOld, reflexosBicipalE);
	}



	/**
	 * Return the value associated with the column: reflexos_bicipal_d
	 */
	public java.lang.Long getReflexosBicipalD () {
		return getPropertyValue(this, reflexosBicipalD, PROP_REFLEXOS_BICIPAL_D); 
	}

	/**
	 * Set the value related to the column: reflexos_bicipal_d
	 * @param reflexosBicipalD the reflexos_bicipal_d value
	 */
	public void setReflexosBicipalD (java.lang.Long reflexosBicipalD) {
//        java.lang.Long reflexosBicipalDOld = this.reflexosBicipalD;
		this.reflexosBicipalD = reflexosBicipalD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosBicipalD", reflexosBicipalDOld, reflexosBicipalD);
	}



	/**
	 * Return the value associated with the column: reflexos_tricipital_e
	 */
	public java.lang.Long getReflexosTricipitalE () {
		return getPropertyValue(this, reflexosTricipitalE, PROP_REFLEXOS_TRICIPITAL_E); 
	}

	/**
	 * Set the value related to the column: reflexos_tricipital_e
	 * @param reflexosTricipitalE the reflexos_tricipital_e value
	 */
	public void setReflexosTricipitalE (java.lang.Long reflexosTricipitalE) {
//        java.lang.Long reflexosTricipitalEOld = this.reflexosTricipitalE;
		this.reflexosTricipitalE = reflexosTricipitalE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosTricipitalE", reflexosTricipitalEOld, reflexosTricipitalE);
	}



	/**
	 * Return the value associated with the column: reflexos_tricipital_d
	 */
	public java.lang.Long getReflexosTricipitalD () {
		return getPropertyValue(this, reflexosTricipitalD, PROP_REFLEXOS_TRICIPITAL_D); 
	}

	/**
	 * Set the value related to the column: reflexos_tricipital_d
	 * @param reflexosTricipitalD the reflexos_tricipital_d value
	 */
	public void setReflexosTricipitalD (java.lang.Long reflexosTricipitalD) {
//        java.lang.Long reflexosTricipitalDOld = this.reflexosTricipitalD;
		this.reflexosTricipitalD = reflexosTricipitalD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosTricipitalD", reflexosTricipitalDOld, reflexosTricipitalD);
	}



	/**
	 * Return the value associated with the column: reflexo_cutaneo_plantar_flexao_e
	 */
	public java.lang.Long getReflexoCutaneoPlantarFlexaoE () {
		return getPropertyValue(this, reflexoCutaneoPlantarFlexaoE, PROP_REFLEXO_CUTANEO_PLANTAR_FLEXAO_E); 
	}

	/**
	 * Set the value related to the column: reflexo_cutaneo_plantar_flexao_e
	 * @param reflexoCutaneoPlantarFlexaoE the reflexo_cutaneo_plantar_flexao_e value
	 */
	public void setReflexoCutaneoPlantarFlexaoE (java.lang.Long reflexoCutaneoPlantarFlexaoE) {
//        java.lang.Long reflexoCutaneoPlantarFlexaoEOld = this.reflexoCutaneoPlantarFlexaoE;
		this.reflexoCutaneoPlantarFlexaoE = reflexoCutaneoPlantarFlexaoE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoCutaneoPlantarFlexaoE", reflexoCutaneoPlantarFlexaoEOld, reflexoCutaneoPlantarFlexaoE);
	}



	/**
	 * Return the value associated with the column: reflexo_cutaneo_plantar_flexao_d
	 */
	public java.lang.Long getReflexoCutaneoPlantarFlexaoD () {
		return getPropertyValue(this, reflexoCutaneoPlantarFlexaoD, PROP_REFLEXO_CUTANEO_PLANTAR_FLEXAO_D); 
	}

	/**
	 * Set the value related to the column: reflexo_cutaneo_plantar_flexao_d
	 * @param reflexoCutaneoPlantarFlexaoD the reflexo_cutaneo_plantar_flexao_d value
	 */
	public void setReflexoCutaneoPlantarFlexaoD (java.lang.Long reflexoCutaneoPlantarFlexaoD) {
//        java.lang.Long reflexoCutaneoPlantarFlexaoDOld = this.reflexoCutaneoPlantarFlexaoD;
		this.reflexoCutaneoPlantarFlexaoD = reflexoCutaneoPlantarFlexaoD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoCutaneoPlantarFlexaoD", reflexoCutaneoPlantarFlexaoDOld, reflexoCutaneoPlantarFlexaoD);
	}



	/**
	 * Return the value associated with the column: reflexo_cutaneo_plantar_extensao_e
	 */
	public java.lang.Long getReflexoCutaneoPlantarExtensaoE () {
		return getPropertyValue(this, reflexoCutaneoPlantarExtensaoE, PROP_REFLEXO_CUTANEO_PLANTAR_EXTENSAO_E); 
	}

	/**
	 * Set the value related to the column: reflexo_cutaneo_plantar_extensao_e
	 * @param reflexoCutaneoPlantarExtensaoE the reflexo_cutaneo_plantar_extensao_e value
	 */
	public void setReflexoCutaneoPlantarExtensaoE (java.lang.Long reflexoCutaneoPlantarExtensaoE) {
//        java.lang.Long reflexoCutaneoPlantarExtensaoEOld = this.reflexoCutaneoPlantarExtensaoE;
		this.reflexoCutaneoPlantarExtensaoE = reflexoCutaneoPlantarExtensaoE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoCutaneoPlantarExtensaoE", reflexoCutaneoPlantarExtensaoEOld, reflexoCutaneoPlantarExtensaoE);
	}



	/**
	 * Return the value associated with the column: reflexo_cutaneo_plantar_extensao_d
	 */
	public java.lang.Long getReflexoCutaneoPlantarExtensaoD () {
		return getPropertyValue(this, reflexoCutaneoPlantarExtensaoD, PROP_REFLEXO_CUTANEO_PLANTAR_EXTENSAO_D); 
	}

	/**
	 * Set the value related to the column: reflexo_cutaneo_plantar_extensao_d
	 * @param reflexoCutaneoPlantarExtensaoD the reflexo_cutaneo_plantar_extensao_d value
	 */
	public void setReflexoCutaneoPlantarExtensaoD (java.lang.Long reflexoCutaneoPlantarExtensaoD) {
//        java.lang.Long reflexoCutaneoPlantarExtensaoDOld = this.reflexoCutaneoPlantarExtensaoD;
		this.reflexoCutaneoPlantarExtensaoD = reflexoCutaneoPlantarExtensaoD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoCutaneoPlantarExtensaoD", reflexoCutaneoPlantarExtensaoDOld, reflexoCutaneoPlantarExtensaoD);
	}



	/**
	 * Return the value associated with the column: sinais_irritacao_meningea_kerning
	 */
	public java.lang.Long getSinaisIrritacaoMeningeaKerning () {
		return getPropertyValue(this, sinaisIrritacaoMeningeaKerning, PROP_SINAIS_IRRITACAO_MENINGEA_KERNING); 
	}

	/**
	 * Set the value related to the column: sinais_irritacao_meningea_kerning
	 * @param sinaisIrritacaoMeningeaKerning the sinais_irritacao_meningea_kerning value
	 */
	public void setSinaisIrritacaoMeningeaKerning (java.lang.Long sinaisIrritacaoMeningeaKerning) {
//        java.lang.Long sinaisIrritacaoMeningeaKerningOld = this.sinaisIrritacaoMeningeaKerning;
		this.sinaisIrritacaoMeningeaKerning = sinaisIrritacaoMeningeaKerning;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisIrritacaoMeningeaKerning", sinaisIrritacaoMeningeaKerningOld, sinaisIrritacaoMeningeaKerning);
	}



	/**
	 * Return the value associated with the column: sinais_irritacao_meningea_rigidez_nuca
	 */
	public java.lang.Long getSinaisIrritacaoMeningeaRigidezNuca () {
		return getPropertyValue(this, sinaisIrritacaoMeningeaRigidezNuca, PROP_SINAIS_IRRITACAO_MENINGEA_RIGIDEZ_NUCA); 
	}

	/**
	 * Set the value related to the column: sinais_irritacao_meningea_rigidez_nuca
	 * @param sinaisIrritacaoMeningeaRigidezNuca the sinais_irritacao_meningea_rigidez_nuca value
	 */
	public void setSinaisIrritacaoMeningeaRigidezNuca (java.lang.Long sinaisIrritacaoMeningeaRigidezNuca) {
//        java.lang.Long sinaisIrritacaoMeningeaRigidezNucaOld = this.sinaisIrritacaoMeningeaRigidezNuca;
		this.sinaisIrritacaoMeningeaRigidezNuca = sinaisIrritacaoMeningeaRigidezNuca;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisIrritacaoMeningeaRigidezNuca", sinaisIrritacaoMeningeaRigidezNucaOld, sinaisIrritacaoMeningeaRigidezNuca);
	}



	/**
	 * Return the value associated with the column: sinais_irritacao_meningea_brudzinski
	 */
	public java.lang.Long getSinaisIrritacaoMeningeaBrudzinski () {
		return getPropertyValue(this, sinaisIrritacaoMeningeaBrudzinski, PROP_SINAIS_IRRITACAO_MENINGEA_BRUDZINSKI); 
	}

	/**
	 * Set the value related to the column: sinais_irritacao_meningea_brudzinski
	 * @param sinaisIrritacaoMeningeaBrudzinski the sinais_irritacao_meningea_brudzinski value
	 */
	public void setSinaisIrritacaoMeningeaBrudzinski (java.lang.Long sinaisIrritacaoMeningeaBrudzinski) {
//        java.lang.Long sinaisIrritacaoMeningeaBrudzinskiOld = this.sinaisIrritacaoMeningeaBrudzinski;
		this.sinaisIrritacaoMeningeaBrudzinski = sinaisIrritacaoMeningeaBrudzinski;
//        this.getPropertyChangeSupport().firePropertyChange ("sinaisIrritacaoMeningeaBrudzinski", sinaisIrritacaoMeningeaBrudzinskiOld, sinaisIrritacaoMeningeaBrudzinski);
	}



	/**
	 * Return the value associated with the column: contato_ingestao_substancia
	 */
	public java.lang.Long getContatoIngestaoSubstancia () {
		return getPropertyValue(this, contatoIngestaoSubstancia, PROP_CONTATO_INGESTAO_SUBSTANCIA); 
	}

	/**
	 * Set the value related to the column: contato_ingestao_substancia
	 * @param contatoIngestaoSubstancia the contato_ingestao_substancia value
	 */
	public void setContatoIngestaoSubstancia (java.lang.Long contatoIngestaoSubstancia) {
//        java.lang.Long contatoIngestaoSubstanciaOld = this.contatoIngestaoSubstancia;
		this.contatoIngestaoSubstancia = contatoIngestaoSubstancia;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoIngestaoSubstancia", contatoIngestaoSubstanciaOld, contatoIngestaoSubstancia);
	}



	/**
	 * Return the value associated with the column: contato_ingestao_substancia_especifique
	 */
	public java.lang.String getContatoIngestaoSubstanciaEspecifique () {
		return getPropertyValue(this, contatoIngestaoSubstanciaEspecifique, PROP_CONTATO_INGESTAO_SUBSTANCIA_ESPECIFIQUE); 
	}

	/**
	 * Set the value related to the column: contato_ingestao_substancia_especifique
	 * @param contatoIngestaoSubstanciaEspecifique the contato_ingestao_substancia_especifique value
	 */
	public void setContatoIngestaoSubstanciaEspecifique (java.lang.String contatoIngestaoSubstanciaEspecifique) {
//        java.lang.String contatoIngestaoSubstanciaEspecifiqueOld = this.contatoIngestaoSubstanciaEspecifique;
		this.contatoIngestaoSubstanciaEspecifique = contatoIngestaoSubstanciaEspecifique;
//        this.getPropertyChangeSupport().firePropertyChange ("contatoIngestaoSubstanciaEspecifique", contatoIngestaoSubstanciaEspecifiqueOld, contatoIngestaoSubstanciaEspecifique);
	}



	/**
	 * Return the value associated with the column: historia_injecao_intramuscular
	 */
	public java.lang.Long getHistoriaInjecaoIntramuscular () {
		return getPropertyValue(this, historiaInjecaoIntramuscular, PROP_HISTORIA_INJECAO_INTRAMUSCULAR); 
	}

	/**
	 * Set the value related to the column: historia_injecao_intramuscular
	 * @param historiaInjecaoIntramuscular the historia_injecao_intramuscular value
	 */
	public void setHistoriaInjecaoIntramuscular (java.lang.Long historiaInjecaoIntramuscular) {
//        java.lang.Long historiaInjecaoIntramuscularOld = this.historiaInjecaoIntramuscular;
		this.historiaInjecaoIntramuscular = historiaInjecaoIntramuscular;
//        this.getPropertyChangeSupport().firePropertyChange ("historiaInjecaoIntramuscular", historiaInjecaoIntramuscularOld, historiaInjecaoIntramuscular);
	}



	/**
	 * Return the value associated with the column: local_aplicacao
	 */
	public java.lang.Long getLocalAplicacao () {
		return getPropertyValue(this, localAplicacao, PROP_LOCAL_APLICACAO); 
	}

	/**
	 * Set the value related to the column: local_aplicacao
	 * @param localAplicacao the local_aplicacao value
	 */
	public void setLocalAplicacao (java.lang.Long localAplicacao) {
//        java.lang.Long localAplicacaoOld = this.localAplicacao;
		this.localAplicacao = localAplicacao;
//        this.getPropertyChangeSupport().firePropertyChange ("localAplicacao", localAplicacaoOld, localAplicacao);
	}



	/**
	 * Return the value associated with the column: hospitalizacao
	 */
	public java.lang.Long getHospitalizacao () {
		return getPropertyValue(this, hospitalizacao, PROP_HOSPITALIZACAO); 
	}

	/**
	 * Set the value related to the column: hospitalizacao
	 * @param hospitalizacao the hospitalizacao value
	 */
	public void setHospitalizacao (java.lang.Long hospitalizacao) {
//        java.lang.Long hospitalizacaoOld = this.hospitalizacao;
		this.hospitalizacao = hospitalizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("hospitalizacao", hospitalizacaoOld, hospitalizacao);
	}



	/**
	 * Return the value associated with the column: dt_internacao
	 */
	public java.util.Date getDataInternacao () {
		return getPropertyValue(this, dataInternacao, PROP_DATA_INTERNACAO); 
	}

	/**
	 * Set the value related to the column: dt_internacao
	 * @param dataInternacao the dt_internacao value
	 */
	public void setDataInternacao (java.util.Date dataInternacao) {
//        java.util.Date dataInternacaoOld = this.dataInternacao;
		this.dataInternacao = dataInternacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataInternacao", dataInternacaoOld, dataInternacao);
	}



	/**
	 * Return the value associated with the column: dt_coleta
	 */
	public java.util.Date getDtColeta () {
		return getPropertyValue(this, dtColeta, PROP_DT_COLETA); 
	}

	/**
	 * Set the value related to the column: dt_coleta
	 * @param dtColeta the dt_coleta value
	 */
	public void setDtColeta (java.util.Date dtColeta) {
//        java.util.Date dtColetaOld = this.dtColeta;
		this.dtColeta = dtColeta;
//        this.getPropertyChangeSupport().firePropertyChange ("dtColeta", dtColetaOld, dtColeta);
	}



	/**
	 * Return the value associated with the column: dt_envio_local_estadual
	 */
	public java.util.Date getDtEnvioLocalEstadual () {
		return getPropertyValue(this, dtEnvioLocalEstadual, PROP_DT_ENVIO_LOCAL_ESTADUAL); 
	}

	/**
	 * Set the value related to the column: dt_envio_local_estadual
	 * @param dtEnvioLocalEstadual the dt_envio_local_estadual value
	 */
	public void setDtEnvioLocalEstadual (java.util.Date dtEnvioLocalEstadual) {
//        java.util.Date dtEnvioLocalEstadualOld = this.dtEnvioLocalEstadual;
		this.dtEnvioLocalEstadual = dtEnvioLocalEstadual;
//        this.getPropertyChangeSupport().firePropertyChange ("dtEnvioLocalEstadual", dtEnvioLocalEstadualOld, dtEnvioLocalEstadual);
	}



	/**
	 * Return the value associated with the column: dt_envio_estadual_lrr
	 */
	public java.util.Date getDtEnvioEstadualLrr () {
		return getPropertyValue(this, dtEnvioEstadualLrr, PROP_DT_ENVIO_ESTADUAL_LRR); 
	}

	/**
	 * Set the value related to the column: dt_envio_estadual_lrr
	 * @param dtEnvioEstadualLrr the dt_envio_estadual_lrr value
	 */
	public void setDtEnvioEstadualLrr (java.util.Date dtEnvioEstadualLrr) {
//        java.util.Date dtEnvioEstadualLrrOld = this.dtEnvioEstadualLrr;
		this.dtEnvioEstadualLrr = dtEnvioEstadualLrr;
//        this.getPropertyChangeSupport().firePropertyChange ("dtEnvioEstadualLrr", dtEnvioEstadualLrrOld, dtEnvioEstadualLrr);
	}



	/**
	 * Return the value associated with the column: dt_recebimento_lrr
	 */
	public java.util.Date getDtRecebimentoLrr () {
		return getPropertyValue(this, dtRecebimentoLrr, PROP_DT_RECEBIMENTO_LRR); 
	}

	/**
	 * Set the value related to the column: dt_recebimento_lrr
	 * @param dtRecebimentoLrr the dt_recebimento_lrr value
	 */
	public void setDtRecebimentoLrr (java.util.Date dtRecebimentoLrr) {
//        java.util.Date dtRecebimentoLrrOld = this.dtRecebimentoLrr;
		this.dtRecebimentoLrr = dtRecebimentoLrr;
//        this.getPropertyChangeSupport().firePropertyChange ("dtRecebimentoLrr", dtRecebimentoLrrOld, dtRecebimentoLrr);
	}



	/**
	 * Return the value associated with the column: quantidade
	 */
	public java.lang.Long getQuantidade () {
		return getPropertyValue(this, quantidade, PROP_QUANTIDADE); 
	}

	/**
	 * Set the value related to the column: quantidade
	 * @param quantidade the quantidade value
	 */
	public void setQuantidade (java.lang.Long quantidade) {
//        java.lang.Long quantidadeOld = this.quantidade;
		this.quantidade = quantidade;
//        this.getPropertyChangeSupport().firePropertyChange ("quantidade", quantidadeOld, quantidade);
	}



	/**
	 * Return the value associated with the column: condicoes
	 */
	public java.lang.Long getCondicoes () {
		return getPropertyValue(this, condicoes, PROP_CONDICOES); 
	}

	/**
	 * Set the value related to the column: condicoes
	 * @param condicoes the condicoes value
	 */
	public void setCondicoes (java.lang.Long condicoes) {
//        java.lang.Long condicoesOld = this.condicoes;
		this.condicoes = condicoes;
//        this.getPropertyChangeSupport().firePropertyChange ("condicoes", condicoesOld, condicoes);
	}



	/**
	 * Return the value associated with the column: dt_resultado
	 */
	public java.util.Date getDtResultado () {
		return getPropertyValue(this, dtResultado, PROP_DT_RESULTADO); 
	}

	/**
	 * Set the value related to the column: dt_resultado
	 * @param dtResultado the dt_resultado value
	 */
	public void setDtResultado (java.util.Date dtResultado) {
//        java.util.Date dtResultadoOld = this.dtResultado;
		this.dtResultado = dtResultado;
//        this.getPropertyChangeSupport().firePropertyChange ("dtResultado", dtResultadoOld, dtResultado);
	}



	/**
	 * Return the value associated with the column: resultado
	 */
	public java.lang.Long getResultado () {
		return getPropertyValue(this, resultado, PROP_RESULTADO); 
	}

	/**
	 * Set the value related to the column: resultado
	 * @param resultado the resultado value
	 */
	public void setResultado (java.lang.Long resultado) {
//        java.lang.Long resultadoOld = this.resultado;
		this.resultado = resultado;
//        this.getPropertyChangeSupport().firePropertyChange ("resultado", resultadoOld, resultado);
	}



	/**
	 * Return the value associated with the column: dt_realizacao
	 */
	public java.util.Date getDtRealizacao () {
		return getPropertyValue(this, dtRealizacao, PROP_DT_REALIZACAO); 
	}

	/**
	 * Set the value related to the column: dt_realizacao
	 * @param dtRealizacao the dt_realizacao value
	 */
	public void setDtRealizacao (java.util.Date dtRealizacao) {
//        java.util.Date dtRealizacaoOld = this.dtRealizacao;
		this.dtRealizacao = dtRealizacao;
//        this.getPropertyChangeSupport().firePropertyChange ("dtRealizacao", dtRealizacaoOld, dtRealizacao);
	}



	/**
	 * Return the value associated with the column: coletado_material_cerebro
	 */
	public java.lang.Long getColetadoMaterialCerebro () {
		return getPropertyValue(this, coletadoMaterialCerebro, PROP_COLETADO_MATERIAL_CEREBRO); 
	}

	/**
	 * Set the value related to the column: coletado_material_cerebro
	 * @param coletadoMaterialCerebro the coletado_material_cerebro value
	 */
	public void setColetadoMaterialCerebro (java.lang.Long coletadoMaterialCerebro) {
//        java.lang.Long coletadoMaterialCerebroOld = this.coletadoMaterialCerebro;
		this.coletadoMaterialCerebro = coletadoMaterialCerebro;
//        this.getPropertyChangeSupport().firePropertyChange ("coletadoMaterialCerebro", coletadoMaterialCerebroOld, coletadoMaterialCerebro);
	}



	/**
	 * Return the value associated with the column: coletado_material_medula
	 */
	public java.lang.Long getColetadoMaterialMedula () {
		return getPropertyValue(this, coletadoMaterialMedula, PROP_COLETADO_MATERIAL_MEDULA); 
	}

	/**
	 * Set the value related to the column: coletado_material_medula
	 * @param coletadoMaterialMedula the coletado_material_medula value
	 */
	public void setColetadoMaterialMedula (java.lang.Long coletadoMaterialMedula) {
//        java.lang.Long coletadoMaterialMedulaOld = this.coletadoMaterialMedula;
		this.coletadoMaterialMedula = coletadoMaterialMedula;
//        this.getPropertyChangeSupport().firePropertyChange ("coletadoMaterialMedula", coletadoMaterialMedulaOld, coletadoMaterialMedula);
	}



	/**
	 * Return the value associated with the column: coletado_material_intestino
	 */
	public java.lang.Long getColetadoMaterialIntestino () {
		return getPropertyValue(this, coletadoMaterialIntestino, PROP_COLETADO_MATERIAL_INTESTINO); 
	}

	/**
	 * Set the value related to the column: coletado_material_intestino
	 * @param coletadoMaterialIntestino the coletado_material_intestino value
	 */
	public void setColetadoMaterialIntestino (java.lang.Long coletadoMaterialIntestino) {
//        java.lang.Long coletadoMaterialIntestinoOld = this.coletadoMaterialIntestino;
		this.coletadoMaterialIntestino = coletadoMaterialIntestino;
//        this.getPropertyChangeSupport().firePropertyChange ("coletadoMaterialIntestino", coletadoMaterialIntestinoOld, coletadoMaterialIntestino);
	}



	/**
	 * Return the value associated with the column: dt_coleta_material
	 */
	public java.util.Date getDtColetaMaterial () {
		return getPropertyValue(this, dtColetaMaterial, PROP_DT_COLETA_MATERIAL); 
	}

	/**
	 * Set the value related to the column: dt_coleta_material
	 * @param dtColetaMaterial the dt_coleta_material value
	 */
	public void setDtColetaMaterial (java.util.Date dtColetaMaterial) {
//        java.util.Date dtColetaMaterialOld = this.dtColetaMaterial;
		this.dtColetaMaterial = dtColetaMaterial;
//        this.getPropertyChangeSupport().firePropertyChange ("dtColetaMaterial", dtColetaMaterialOld, dtColetaMaterial);
	}



	/**
	 * Return the value associated with the column: resultado_material
	 */
	public java.lang.Long getResultadoMaterial () {
		return getPropertyValue(this, resultadoMaterial, PROP_RESULTADO_MATERIAL); 
	}

	/**
	 * Set the value related to the column: resultado_material
	 * @param resultadoMaterial the resultado_material value
	 */
	public void setResultadoMaterial (java.lang.Long resultadoMaterial) {
//        java.lang.Long resultadoMaterialOld = this.resultadoMaterial;
		this.resultadoMaterial = resultadoMaterial;
//        this.getPropertyChangeSupport().firePropertyChange ("resultadoMaterial", resultadoMaterialOld, resultadoMaterial);
	}



	/**
	 * Return the value associated with the column: dt_revisita
	 */
	public java.util.Date getDtRevisita () {
		return getPropertyValue(this, dtRevisita, PROP_DT_REVISITA); 
	}

	/**
	 * Set the value related to the column: dt_revisita
	 * @param dtRevisita the dt_revisita value
	 */
	public void setDtRevisita (java.util.Date dtRevisita) {
//        java.util.Date dtRevisitaOld = this.dtRevisita;
		this.dtRevisita = dtRevisita;
//        this.getPropertyChangeSupport().firePropertyChange ("dtRevisita", dtRevisitaOld, dtRevisita);
	}



	/**
	 * Return the value associated with the column: forca_muscular_revisita_mie
	 */
	public java.lang.Long getForcaMuscularRevisitaMie () {
		return getPropertyValue(this, forcaMuscularRevisitaMie, PROP_FORCA_MUSCULAR_REVISITA_MIE); 
	}

	/**
	 * Set the value related to the column: forca_muscular_revisita_mie
	 * @param forcaMuscularRevisitaMie the forca_muscular_revisita_mie value
	 */
	public void setForcaMuscularRevisitaMie (java.lang.Long forcaMuscularRevisitaMie) {
//        java.lang.Long forcaMuscularRevisitaMieOld = this.forcaMuscularRevisitaMie;
		this.forcaMuscularRevisitaMie = forcaMuscularRevisitaMie;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularRevisitaMie", forcaMuscularRevisitaMieOld, forcaMuscularRevisitaMie);
	}



	/**
	 * Return the value associated with the column: forca_muscular_revisita_mse
	 */
	public java.lang.Long getForcaMuscularRevisitaMse () {
		return getPropertyValue(this, forcaMuscularRevisitaMse, PROP_FORCA_MUSCULAR_REVISITA_MSE); 
	}

	/**
	 * Set the value related to the column: forca_muscular_revisita_mse
	 * @param forcaMuscularRevisitaMse the forca_muscular_revisita_mse value
	 */
	public void setForcaMuscularRevisitaMse (java.lang.Long forcaMuscularRevisitaMse) {
//        java.lang.Long forcaMuscularRevisitaMseOld = this.forcaMuscularRevisitaMse;
		this.forcaMuscularRevisitaMse = forcaMuscularRevisitaMse;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularRevisitaMse", forcaMuscularRevisitaMseOld, forcaMuscularRevisitaMse);
	}



	/**
	 * Return the value associated with the column: forca_muscular_revisita_mid
	 */
	public java.lang.Long getForcaMuscularRevisitaMid () {
		return getPropertyValue(this, forcaMuscularRevisitaMid, PROP_FORCA_MUSCULAR_REVISITA_MID); 
	}

	/**
	 * Set the value related to the column: forca_muscular_revisita_mid
	 * @param forcaMuscularRevisitaMid the forca_muscular_revisita_mid value
	 */
	public void setForcaMuscularRevisitaMid (java.lang.Long forcaMuscularRevisitaMid) {
//        java.lang.Long forcaMuscularRevisitaMidOld = this.forcaMuscularRevisitaMid;
		this.forcaMuscularRevisitaMid = forcaMuscularRevisitaMid;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularRevisitaMid", forcaMuscularRevisitaMidOld, forcaMuscularRevisitaMid);
	}



	/**
	 * Return the value associated with the column: forca_muscular_revisita_msd
	 */
	public java.lang.Long getForcaMuscularRevisitaMsd () {
		return getPropertyValue(this, forcaMuscularRevisitaMsd, PROP_FORCA_MUSCULAR_REVISITA_MSD); 
	}

	/**
	 * Set the value related to the column: forca_muscular_revisita_msd
	 * @param forcaMuscularRevisitaMsd the forca_muscular_revisita_msd value
	 */
	public void setForcaMuscularRevisitaMsd (java.lang.Long forcaMuscularRevisitaMsd) {
//        java.lang.Long forcaMuscularRevisitaMsdOld = this.forcaMuscularRevisitaMsd;
		this.forcaMuscularRevisitaMsd = forcaMuscularRevisitaMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("forcaMuscularRevisitaMsd", forcaMuscularRevisitaMsdOld, forcaMuscularRevisitaMsd);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_revisita_mie
	 */
	public java.lang.Long getTonusMuscularRevisitaMie () {
		return getPropertyValue(this, tonusMuscularRevisitaMie, PROP_TONUS_MUSCULAR_REVISITA_MIE); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_revisita_mie
	 * @param tonusMuscularRevisitaMie the tonus_muscular_revisita_mie value
	 */
	public void setTonusMuscularRevisitaMie (java.lang.Long tonusMuscularRevisitaMie) {
//        java.lang.Long tonusMuscularRevisitaMieOld = this.tonusMuscularRevisitaMie;
		this.tonusMuscularRevisitaMie = tonusMuscularRevisitaMie;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularRevisitaMie", tonusMuscularRevisitaMieOld, tonusMuscularRevisitaMie);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_revisita_mse
	 */
	public java.lang.Long getTonusMuscularRevisitaMse () {
		return getPropertyValue(this, tonusMuscularRevisitaMse, PROP_TONUS_MUSCULAR_REVISITA_MSE); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_revisita_mse
	 * @param tonusMuscularRevisitaMse the tonus_muscular_revisita_mse value
	 */
	public void setTonusMuscularRevisitaMse (java.lang.Long tonusMuscularRevisitaMse) {
//        java.lang.Long tonusMuscularRevisitaMseOld = this.tonusMuscularRevisitaMse;
		this.tonusMuscularRevisitaMse = tonusMuscularRevisitaMse;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularRevisitaMse", tonusMuscularRevisitaMseOld, tonusMuscularRevisitaMse);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_revisita_mid
	 */
	public java.lang.Long getTonusMuscularRevisitaMid () {
		return getPropertyValue(this, tonusMuscularRevisitaMid, PROP_TONUS_MUSCULAR_REVISITA_MID); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_revisita_mid
	 * @param tonusMuscularRevisitaMid the tonus_muscular_revisita_mid value
	 */
	public void setTonusMuscularRevisitaMid (java.lang.Long tonusMuscularRevisitaMid) {
//        java.lang.Long tonusMuscularRevisitaMidOld = this.tonusMuscularRevisitaMid;
		this.tonusMuscularRevisitaMid = tonusMuscularRevisitaMid;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularRevisitaMid", tonusMuscularRevisitaMidOld, tonusMuscularRevisitaMid);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_revisita_msd
	 */
	public java.lang.Long getTonusMuscularRevisitaMsd () {
		return getPropertyValue(this, tonusMuscularRevisitaMsd, PROP_TONUS_MUSCULAR_REVISITA_MSD); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_revisita_msd
	 * @param tonusMuscularRevisitaMsd the tonus_muscular_revisita_msd value
	 */
	public void setTonusMuscularRevisitaMsd (java.lang.Long tonusMuscularRevisitaMsd) {
//        java.lang.Long tonusMuscularRevisitaMsdOld = this.tonusMuscularRevisitaMsd;
		this.tonusMuscularRevisitaMsd = tonusMuscularRevisitaMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularRevisitaMsd", tonusMuscularRevisitaMsdOld, tonusMuscularRevisitaMsd);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_revisita_musc_cervical
	 */
	public java.lang.Long getTonusMuscularRevisitaMuscCervical () {
		return getPropertyValue(this, tonusMuscularRevisitaMuscCervical, PROP_TONUS_MUSCULAR_REVISITA_MUSC_CERVICAL); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_revisita_musc_cervical
	 * @param tonusMuscularRevisitaMuscCervical the tonus_muscular_revisita_musc_cervical value
	 */
	public void setTonusMuscularRevisitaMuscCervical (java.lang.Long tonusMuscularRevisitaMuscCervical) {
//        java.lang.Long tonusMuscularRevisitaMuscCervicalOld = this.tonusMuscularRevisitaMuscCervical;
		this.tonusMuscularRevisitaMuscCervical = tonusMuscularRevisitaMuscCervical;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularRevisitaMuscCervical", tonusMuscularRevisitaMuscCervicalOld, tonusMuscularRevisitaMuscCervical);
	}



	/**
	 * Return the value associated with the column: tonus_muscular_revisita_face
	 */
	public java.lang.Long getTonusMuscularRevisitaFace () {
		return getPropertyValue(this, tonusMuscularRevisitaFace, PROP_TONUS_MUSCULAR_REVISITA_FACE); 
	}

	/**
	 * Set the value related to the column: tonus_muscular_revisita_face
	 * @param tonusMuscularRevisitaFace the tonus_muscular_revisita_face value
	 */
	public void setTonusMuscularRevisitaFace (java.lang.Long tonusMuscularRevisitaFace) {
//        java.lang.Long tonusMuscularRevisitaFaceOld = this.tonusMuscularRevisitaFace;
		this.tonusMuscularRevisitaFace = tonusMuscularRevisitaFace;
//        this.getPropertyChangeSupport().firePropertyChange ("tonusMuscularRevisitaFace", tonusMuscularRevisitaFaceOld, tonusMuscularRevisitaFace);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_aquileu_e
	 */
	public java.lang.Long getReflexosRevisitaAquileuE () {
		return getPropertyValue(this, reflexosRevisitaAquileuE, PROP_REFLEXOS_REVISITA_AQUILEU_E); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_aquileu_e
	 * @param reflexosRevisitaAquileuE the reflexos_revisita_aquileu_e value
	 */
	public void setReflexosRevisitaAquileuE (java.lang.Long reflexosRevisitaAquileuE) {
//        java.lang.Long reflexosRevisitaAquileuEOld = this.reflexosRevisitaAquileuE;
		this.reflexosRevisitaAquileuE = reflexosRevisitaAquileuE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaAquileuE", reflexosRevisitaAquileuEOld, reflexosRevisitaAquileuE);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_aquileu_d
	 */
	public java.lang.Long getReflexosRevisitaAquileuD () {
		return getPropertyValue(this, reflexosRevisitaAquileuD, PROP_REFLEXOS_REVISITA_AQUILEU_D); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_aquileu_d
	 * @param reflexosRevisitaAquileuD the reflexos_revisita_aquileu_d value
	 */
	public void setReflexosRevisitaAquileuD (java.lang.Long reflexosRevisitaAquileuD) {
//        java.lang.Long reflexosRevisitaAquileuDOld = this.reflexosRevisitaAquileuD;
		this.reflexosRevisitaAquileuD = reflexosRevisitaAquileuD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaAquileuD", reflexosRevisitaAquileuDOld, reflexosRevisitaAquileuD);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_patelar_e
	 */
	public java.lang.Long getReflexosRevisitaPatelarE () {
		return getPropertyValue(this, reflexosRevisitaPatelarE, PROP_REFLEXOS_REVISITA_PATELAR_E); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_patelar_e
	 * @param reflexosRevisitaPatelarE the reflexos_revisita_patelar_e value
	 */
	public void setReflexosRevisitaPatelarE (java.lang.Long reflexosRevisitaPatelarE) {
//        java.lang.Long reflexosRevisitaPatelarEOld = this.reflexosRevisitaPatelarE;
		this.reflexosRevisitaPatelarE = reflexosRevisitaPatelarE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaPatelarE", reflexosRevisitaPatelarEOld, reflexosRevisitaPatelarE);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_patelar_d
	 */
	public java.lang.Long getReflexosRevisitaPatelarD () {
		return getPropertyValue(this, reflexosRevisitaPatelarD, PROP_REFLEXOS_REVISITA_PATELAR_D); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_patelar_d
	 * @param reflexosRevisitaPatelarD the reflexos_revisita_patelar_d value
	 */
	public void setReflexosRevisitaPatelarD (java.lang.Long reflexosRevisitaPatelarD) {
//        java.lang.Long reflexosRevisitaPatelarDOld = this.reflexosRevisitaPatelarD;
		this.reflexosRevisitaPatelarD = reflexosRevisitaPatelarD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaPatelarD", reflexosRevisitaPatelarDOld, reflexosRevisitaPatelarD);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_bicipal_e
	 */
	public java.lang.Long getReflexosRevisitaBicipalE () {
		return getPropertyValue(this, reflexosRevisitaBicipalE, PROP_REFLEXOS_REVISITA_BICIPAL_E); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_bicipal_e
	 * @param reflexosRevisitaBicipalE the reflexos_revisita_bicipal_e value
	 */
	public void setReflexosRevisitaBicipalE (java.lang.Long reflexosRevisitaBicipalE) {
//        java.lang.Long reflexosRevisitaBicipalEOld = this.reflexosRevisitaBicipalE;
		this.reflexosRevisitaBicipalE = reflexosRevisitaBicipalE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaBicipalE", reflexosRevisitaBicipalEOld, reflexosRevisitaBicipalE);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_bicipal_d
	 */
	public java.lang.Long getReflexosRevisitaBicipalD () {
		return getPropertyValue(this, reflexosRevisitaBicipalD, PROP_REFLEXOS_REVISITA_BICIPAL_D); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_bicipal_d
	 * @param reflexosRevisitaBicipalD the reflexos_revisita_bicipal_d value
	 */
	public void setReflexosRevisitaBicipalD (java.lang.Long reflexosRevisitaBicipalD) {
//        java.lang.Long reflexosRevisitaBicipalDOld = this.reflexosRevisitaBicipalD;
		this.reflexosRevisitaBicipalD = reflexosRevisitaBicipalD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaBicipalD", reflexosRevisitaBicipalDOld, reflexosRevisitaBicipalD);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_tricipital_e
	 */
	public java.lang.Long getReflexosRevisitaTricipitalE () {
		return getPropertyValue(this, reflexosRevisitaTricipitalE, PROP_REFLEXOS_REVISITA_TRICIPITAL_E); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_tricipital_e
	 * @param reflexosRevisitaTricipitalE the reflexos_revisita_tricipital_e value
	 */
	public void setReflexosRevisitaTricipitalE (java.lang.Long reflexosRevisitaTricipitalE) {
//        java.lang.Long reflexosRevisitaTricipitalEOld = this.reflexosRevisitaTricipitalE;
		this.reflexosRevisitaTricipitalE = reflexosRevisitaTricipitalE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaTricipitalE", reflexosRevisitaTricipitalEOld, reflexosRevisitaTricipitalE);
	}



	/**
	 * Return the value associated with the column: reflexos_revisita_tricipital_d
	 */
	public java.lang.Long getReflexosRevisitaTricipitalD () {
		return getPropertyValue(this, reflexosRevisitaTricipitalD, PROP_REFLEXOS_REVISITA_TRICIPITAL_D); 
	}

	/**
	 * Set the value related to the column: reflexos_revisita_tricipital_d
	 * @param reflexosRevisitaTricipitalD the reflexos_revisita_tricipital_d value
	 */
	public void setReflexosRevisitaTricipitalD (java.lang.Long reflexosRevisitaTricipitalD) {
//        java.lang.Long reflexosRevisitaTricipitalDOld = this.reflexosRevisitaTricipitalD;
		this.reflexosRevisitaTricipitalD = reflexosRevisitaTricipitalD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexosRevisitaTricipitalD", reflexosRevisitaTricipitalDOld, reflexosRevisitaTricipitalD);
	}



	/**
	 * Return the value associated with the column: reflexo_revisita_cutaneo_plantar_flexao_e
	 */
	public java.lang.Long getReflexoRevisitaCutaneoPlantarFlexaoE () {
		return getPropertyValue(this, reflexoRevisitaCutaneoPlantarFlexaoE, PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_FLEXAO_E); 
	}

	/**
	 * Set the value related to the column: reflexo_revisita_cutaneo_plantar_flexao_e
	 * @param reflexoRevisitaCutaneoPlantarFlexaoE the reflexo_revisita_cutaneo_plantar_flexao_e value
	 */
	public void setReflexoRevisitaCutaneoPlantarFlexaoE (java.lang.Long reflexoRevisitaCutaneoPlantarFlexaoE) {
//        java.lang.Long reflexoRevisitaCutaneoPlantarFlexaoEOld = this.reflexoRevisitaCutaneoPlantarFlexaoE;
		this.reflexoRevisitaCutaneoPlantarFlexaoE = reflexoRevisitaCutaneoPlantarFlexaoE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoRevisitaCutaneoPlantarFlexaoE", reflexoRevisitaCutaneoPlantarFlexaoEOld, reflexoRevisitaCutaneoPlantarFlexaoE);
	}



	/**
	 * Return the value associated with the column: reflexo_revisita_cutaneo_plantar_flexao_d
	 */
	public java.lang.Long getReflexoRevisitaCutaneoPlantarFlexaoD () {
		return getPropertyValue(this, reflexoRevisitaCutaneoPlantarFlexaoD, PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_FLEXAO_D); 
	}

	/**
	 * Set the value related to the column: reflexo_revisita_cutaneo_plantar_flexao_d
	 * @param reflexoRevisitaCutaneoPlantarFlexaoD the reflexo_revisita_cutaneo_plantar_flexao_d value
	 */
	public void setReflexoRevisitaCutaneoPlantarFlexaoD (java.lang.Long reflexoRevisitaCutaneoPlantarFlexaoD) {
//        java.lang.Long reflexoRevisitaCutaneoPlantarFlexaoDOld = this.reflexoRevisitaCutaneoPlantarFlexaoD;
		this.reflexoRevisitaCutaneoPlantarFlexaoD = reflexoRevisitaCutaneoPlantarFlexaoD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoRevisitaCutaneoPlantarFlexaoD", reflexoRevisitaCutaneoPlantarFlexaoDOld, reflexoRevisitaCutaneoPlantarFlexaoD);
	}



	/**
	 * Return the value associated with the column: reflexo_revisita_cutaneo_plantar_extensao_e
	 */
	public java.lang.Long getReflexoRevisitaCutaneoPlantarExtensaoE () {
		return getPropertyValue(this, reflexoRevisitaCutaneoPlantarExtensaoE, PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_EXTENSAO_E); 
	}

	/**
	 * Set the value related to the column: reflexo_revisita_cutaneo_plantar_extensao_e
	 * @param reflexoRevisitaCutaneoPlantarExtensaoE the reflexo_revisita_cutaneo_plantar_extensao_e value
	 */
	public void setReflexoRevisitaCutaneoPlantarExtensaoE (java.lang.Long reflexoRevisitaCutaneoPlantarExtensaoE) {
//        java.lang.Long reflexoRevisitaCutaneoPlantarExtensaoEOld = this.reflexoRevisitaCutaneoPlantarExtensaoE;
		this.reflexoRevisitaCutaneoPlantarExtensaoE = reflexoRevisitaCutaneoPlantarExtensaoE;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoRevisitaCutaneoPlantarExtensaoE", reflexoRevisitaCutaneoPlantarExtensaoEOld, reflexoRevisitaCutaneoPlantarExtensaoE);
	}



	/**
	 * Return the value associated with the column: reflexo_revisita_cutaneo_plantar_extensao_d
	 */
	public java.lang.Long getReflexoRevisitaCutaneoPlantarExtensaoD () {
		return getPropertyValue(this, reflexoRevisitaCutaneoPlantarExtensaoD, PROP_REFLEXO_REVISITA_CUTANEO_PLANTAR_EXTENSAO_D); 
	}

	/**
	 * Set the value related to the column: reflexo_revisita_cutaneo_plantar_extensao_d
	 * @param reflexoRevisitaCutaneoPlantarExtensaoD the reflexo_revisita_cutaneo_plantar_extensao_d value
	 */
	public void setReflexoRevisitaCutaneoPlantarExtensaoD (java.lang.Long reflexoRevisitaCutaneoPlantarExtensaoD) {
//        java.lang.Long reflexoRevisitaCutaneoPlantarExtensaoDOld = this.reflexoRevisitaCutaneoPlantarExtensaoD;
		this.reflexoRevisitaCutaneoPlantarExtensaoD = reflexoRevisitaCutaneoPlantarExtensaoD;
//        this.getPropertyChangeSupport().firePropertyChange ("reflexoRevisitaCutaneoPlantarExtensaoD", reflexoRevisitaCutaneoPlantarExtensaoDOld, reflexoRevisitaCutaneoPlantarExtensaoD);
	}



	/**
	 * Return the value associated with the column: atrofia_mie
	 */
	public java.lang.Long getAtrofiaMie () {
		return getPropertyValue(this, atrofiaMie, PROP_ATROFIA_MIE); 
	}

	/**
	 * Set the value related to the column: atrofia_mie
	 * @param atrofiaMie the atrofia_mie value
	 */
	public void setAtrofiaMie (java.lang.Long atrofiaMie) {
//        java.lang.Long atrofiaMieOld = this.atrofiaMie;
		this.atrofiaMie = atrofiaMie;
//        this.getPropertyChangeSupport().firePropertyChange ("atrofiaMie", atrofiaMieOld, atrofiaMie);
	}



	/**
	 * Return the value associated with the column: atrofia_mse
	 */
	public java.lang.Long getAtrofiaMse () {
		return getPropertyValue(this, atrofiaMse, PROP_ATROFIA_MSE); 
	}

	/**
	 * Set the value related to the column: atrofia_mse
	 * @param atrofiaMse the atrofia_mse value
	 */
	public void setAtrofiaMse (java.lang.Long atrofiaMse) {
//        java.lang.Long atrofiaMseOld = this.atrofiaMse;
		this.atrofiaMse = atrofiaMse;
//        this.getPropertyChangeSupport().firePropertyChange ("atrofiaMse", atrofiaMseOld, atrofiaMse);
	}



	/**
	 * Return the value associated with the column: atrofia_mid
	 */
	public java.lang.Long getAtrofiaMid () {
		return getPropertyValue(this, atrofiaMid, PROP_ATROFIA_MID); 
	}

	/**
	 * Set the value related to the column: atrofia_mid
	 * @param atrofiaMid the atrofia_mid value
	 */
	public void setAtrofiaMid (java.lang.Long atrofiaMid) {
//        java.lang.Long atrofiaMidOld = this.atrofiaMid;
		this.atrofiaMid = atrofiaMid;
//        this.getPropertyChangeSupport().firePropertyChange ("atrofiaMid", atrofiaMidOld, atrofiaMid);
	}



	/**
	 * Return the value associated with the column: atrofia_msd
	 */
	public java.lang.Long getAtrofiaMsd () {
		return getPropertyValue(this, atrofiaMsd, PROP_ATROFIA_MSD); 
	}

	/**
	 * Set the value related to the column: atrofia_msd
	 * @param atrofiaMsd the atrofia_msd value
	 */
	public void setAtrofiaMsd (java.lang.Long atrofiaMsd) {
//        java.lang.Long atrofiaMsdOld = this.atrofiaMsd;
		this.atrofiaMsd = atrofiaMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("atrofiaMsd", atrofiaMsdOld, atrofiaMsd);
	}



	/**
	 * Return the value associated with the column: sensibilidade_revisita_mie
	 */
	public java.lang.Long getSensibilidadeRevisitaMie () {
		return getPropertyValue(this, sensibilidadeRevisitaMie, PROP_SENSIBILIDADE_REVISITA_MIE); 
	}

	/**
	 * Set the value related to the column: sensibilidade_revisita_mie
	 * @param sensibilidadeRevisitaMie the sensibilidade_revisita_mie value
	 */
	public void setSensibilidadeRevisitaMie (java.lang.Long sensibilidadeRevisitaMie) {
//        java.lang.Long sensibilidadeRevisitaMieOld = this.sensibilidadeRevisitaMie;
		this.sensibilidadeRevisitaMie = sensibilidadeRevisitaMie;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeRevisitaMie", sensibilidadeRevisitaMieOld, sensibilidadeRevisitaMie);
	}



	/**
	 * Return the value associated with the column: sensibilidade_revisita_mse
	 */
	public java.lang.Long getSensibilidadeRevisitaMse () {
		return getPropertyValue(this, sensibilidadeRevisitaMse, PROP_SENSIBILIDADE_REVISITA_MSE); 
	}

	/**
	 * Set the value related to the column: sensibilidade_revisita_mse
	 * @param sensibilidadeRevisitaMse the sensibilidade_revisita_mse value
	 */
	public void setSensibilidadeRevisitaMse (java.lang.Long sensibilidadeRevisitaMse) {
//        java.lang.Long sensibilidadeRevisitaMseOld = this.sensibilidadeRevisitaMse;
		this.sensibilidadeRevisitaMse = sensibilidadeRevisitaMse;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeRevisitaMse", sensibilidadeRevisitaMseOld, sensibilidadeRevisitaMse);
	}



	/**
	 * Return the value associated with the column: sensibilidade_revisita_mid
	 */
	public java.lang.Long getSensibilidadeRevisitaMid () {
		return getPropertyValue(this, sensibilidadeRevisitaMid, PROP_SENSIBILIDADE_REVISITA_MID); 
	}

	/**
	 * Set the value related to the column: sensibilidade_revisita_mid
	 * @param sensibilidadeRevisitaMid the sensibilidade_revisita_mid value
	 */
	public void setSensibilidadeRevisitaMid (java.lang.Long sensibilidadeRevisitaMid) {
//        java.lang.Long sensibilidadeRevisitaMidOld = this.sensibilidadeRevisitaMid;
		this.sensibilidadeRevisitaMid = sensibilidadeRevisitaMid;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeRevisitaMid", sensibilidadeRevisitaMidOld, sensibilidadeRevisitaMid);
	}



	/**
	 * Return the value associated with the column: sensibilidade_revisita_msd
	 */
	public java.lang.Long getSensibilidadeRevisitaMsd () {
		return getPropertyValue(this, sensibilidadeRevisitaMsd, PROP_SENSIBILIDADE_REVISITA_MSD); 
	}

	/**
	 * Set the value related to the column: sensibilidade_revisita_msd
	 * @param sensibilidadeRevisitaMsd the sensibilidade_revisita_msd value
	 */
	public void setSensibilidadeRevisitaMsd (java.lang.Long sensibilidadeRevisitaMsd) {
//        java.lang.Long sensibilidadeRevisitaMsdOld = this.sensibilidadeRevisitaMsd;
		this.sensibilidadeRevisitaMsd = sensibilidadeRevisitaMsd;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeRevisitaMsd", sensibilidadeRevisitaMsdOld, sensibilidadeRevisitaMsd);
	}



	/**
	 * Return the value associated with the column: sensibilidade_revisita_face
	 */
	public java.lang.Long getSensibilidadeRevisitaFace () {
		return getPropertyValue(this, sensibilidadeRevisitaFace, PROP_SENSIBILIDADE_REVISITA_FACE); 
	}

	/**
	 * Set the value related to the column: sensibilidade_revisita_face
	 * @param sensibilidadeRevisitaFace the sensibilidade_revisita_face value
	 */
	public void setSensibilidadeRevisitaFace (java.lang.Long sensibilidadeRevisitaFace) {
//        java.lang.Long sensibilidadeRevisitaFaceOld = this.sensibilidadeRevisitaFace;
		this.sensibilidadeRevisitaFace = sensibilidadeRevisitaFace;
//        this.getPropertyChangeSupport().firePropertyChange ("sensibilidadeRevisitaFace", sensibilidadeRevisitaFaceOld, sensibilidadeRevisitaFace);
	}



	/**
	 * Return the value associated with the column: dt_revisao
	 */
	public java.util.Date getDataRevisao () {
		return getPropertyValue(this, dataRevisao, PROP_DATA_REVISAO); 
	}

	/**
	 * Set the value related to the column: dt_revisao
	 * @param dataRevisao the dt_revisao value
	 */
	public void setDataRevisao (java.util.Date dataRevisao) {
//        java.util.Date dataRevisaoOld = this.dataRevisao;
		this.dataRevisao = dataRevisao;
//        this.getPropertyChangeSupport().firePropertyChange ("dataRevisao", dataRevisaoOld, dataRevisao);
	}



	/**
	 * Return the value associated with the column: classificacao_final
	 */
	public java.lang.Long getClassificacaoFinal () {
		return getPropertyValue(this, classificacaoFinal, PROP_CLASSIFICACAO_FINAL); 
	}

	/**
	 * Set the value related to the column: classificacao_final
	 * @param classificacaoFinal the classificacao_final value
	 */
	public void setClassificacaoFinal (java.lang.Long classificacaoFinal) {
//        java.lang.Long classificacaoFinalOld = this.classificacaoFinal;
		this.classificacaoFinal = classificacaoFinal;
//        this.getPropertyChangeSupport().firePropertyChange ("classificacaoFinal", classificacaoFinalOld, classificacaoFinal);
	}



	/**
	 * Return the value associated with the column: criterio_classificacao
	 */
	public java.lang.Long getCriterioClassificacao () {
		return getPropertyValue(this, criterioClassificacao, PROP_CRITERIO_CLASSIFICACAO); 
	}

	/**
	 * Set the value related to the column: criterio_classificacao
	 * @param criterioClassificacao the criterio_classificacao value
	 */
	public void setCriterioClassificacao (java.lang.Long criterioClassificacao) {
//        java.lang.Long criterioClassificacaoOld = this.criterioClassificacao;
		this.criterioClassificacao = criterioClassificacao;
//        this.getPropertyChangeSupport().firePropertyChange ("criterioClassificacao", criterioClassificacaoOld, criterioClassificacao);
	}



	/**
	 * Return the value associated with the column: evolucao_caso
	 */
	public java.lang.Long getEvolucaoCaso () {
		return getPropertyValue(this, evolucaoCaso, PROP_EVOLUCAO_CASO); 
	}

	/**
	 * Set the value related to the column: evolucao_caso
	 * @param evolucaoCaso the evolucao_caso value
	 */
	public void setEvolucaoCaso (java.lang.Long evolucaoCaso) {
//        java.lang.Long evolucaoCasoOld = this.evolucaoCaso;
		this.evolucaoCaso = evolucaoCaso;
//        this.getPropertyChangeSupport().firePropertyChange ("evolucaoCaso", evolucaoCasoOld, evolucaoCaso);
	}



	/**
	 * Return the value associated with the column: dt_obito
	 */
	public java.util.Date getDataObito () {
		return getPropertyValue(this, dataObito, PROP_DATA_OBITO); 
	}

	/**
	 * Set the value related to the column: dt_obito
	 * @param dataObito the dt_obito value
	 */
	public void setDataObito (java.util.Date dataObito) {
//        java.util.Date dataObitoOld = this.dataObito;
		this.dataObito = dataObito;
//        this.getPropertyChangeSupport().firePropertyChange ("dataObito", dataObitoOld, dataObito);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}



	/**
	 * Return the value associated with the column: dt_encerramento
	 */
	public java.util.Date getDataEncerramento () {
		return getPropertyValue(this, dataEncerramento, PROP_DATA_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: dt_encerramento
	 * @param dataEncerramento the dt_encerramento value
	 */
	public void setDataEncerramento (java.util.Date dataEncerramento) {
//        java.util.Date dataEncerramentoOld = this.dataEncerramento;
		this.dataEncerramento = dataEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("dataEncerramento", dataEncerramentoOld, dataEncerramento);
	}



	/**
	 * Return the value associated with the column: cd_registro_agravo
	 */
	public br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo getRegistroAgravo () {
		return getPropertyValue(this, registroAgravo, PROP_REGISTRO_AGRAVO); 
	}

	/**
	 * Set the value related to the column: cd_registro_agravo
	 * @param registroAgravo the cd_registro_agravo value
	 */
	public void setRegistroAgravo (br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravo) {
//        br.com.ksisolucoes.vo.vigilancia.agravo.RegistroAgravo registroAgravoOld = this.registroAgravo;
		this.registroAgravo = registroAgravo;
//        this.getPropertyChangeSupport().firePropertyChange ("registroAgravo", registroAgravoOld, registroAgravo);
	}



	/**
	 * Return the value associated with the column: ocupacao_cbo
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo getOcupacaoCbo () {
		return getPropertyValue(this, ocupacaoCbo, PROP_OCUPACAO_CBO); 
	}

	/**
	 * Set the value related to the column: ocupacao_cbo
	 * @param ocupacaoCbo the ocupacao_cbo value
	 */
	public void setOcupacaoCbo (br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCbo) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo ocupacaoCboOld = this.ocupacaoCbo;
		this.ocupacaoCbo = ocupacaoCbo;
//        this.getPropertyChangeSupport().firePropertyChange ("ocupacaoCbo", ocupacaoCboOld, ocupacaoCbo);
	}



	/**
	 * Return the value associated with the column: cd_pais_origem
	 */
	public br.com.ksisolucoes.vo.basico.Pais getCdPaisOrigem () {
		return getPropertyValue(this, cdPaisOrigem, PROP_CD_PAIS_ORIGEM); 
	}

	/**
	 * Set the value related to the column: cd_pais_origem
	 * @param cdPaisOrigem the cd_pais_origem value
	 */
	public void setCdPaisOrigem (br.com.ksisolucoes.vo.basico.Pais cdPaisOrigem) {
//        br.com.ksisolucoes.vo.basico.Pais cdPaisOrigemOld = this.cdPaisOrigem;
		this.cdPaisOrigem = cdPaisOrigem;
//        this.getPropertyChangeSupport().firePropertyChange ("cdPaisOrigem", cdPaisOrigemOld, cdPaisOrigem);
	}



	/**
	 * Return the value associated with the column: hipotese_diagnostica_cid
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getHipoteseDiagnosticaCid () {
		return getPropertyValue(this, hipoteseDiagnosticaCid, PROP_HIPOTESE_DIAGNOSTICA_CID); 
	}

	/**
	 * Set the value related to the column: hipotese_diagnostica_cid
	 * @param hipoteseDiagnosticaCid the hipotese_diagnostica_cid value
	 */
	public void setHipoteseDiagnosticaCid (br.com.ksisolucoes.vo.prontuario.basico.Cid hipoteseDiagnosticaCid) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid hipoteseDiagnosticaCidOld = this.hipoteseDiagnosticaCid;
		this.hipoteseDiagnosticaCid = hipoteseDiagnosticaCid;
//        this.getPropertyChangeSupport().firePropertyChange ("hipoteseDiagnosticaCid", hipoteseDiagnosticaCidOld, hipoteseDiagnosticaCid);
	}



	/**
	 * Return the value associated with the column: unidade_hospital
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getHospital () {
		return getPropertyValue(this, hospital, PROP_HOSPITAL); 
	}

	/**
	 * Set the value related to the column: unidade_hospital
	 * @param hospital the unidade_hospital value
	 */
	public void setHospital (br.com.ksisolucoes.vo.basico.Empresa hospital) {
//        br.com.ksisolucoes.vo.basico.Empresa hospitalOld = this.hospital;
		this.hospital = hospital;
//        this.getPropertyChangeSupport().firePropertyChange ("hospital", hospitalOld, hospital);
	}



	/**
	 * Return the value associated with the column: diagostico_sugestivo
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getDiagosticoSugestivo () {
		return getPropertyValue(this, diagosticoSugestivo, PROP_DIAGOSTICO_SUGESTIVO); 
	}

	/**
	 * Set the value related to the column: diagostico_sugestivo
	 * @param diagosticoSugestivo the diagostico_sugestivo value
	 */
	public void setDiagosticoSugestivo (br.com.ksisolucoes.vo.prontuario.basico.Cid diagosticoSugestivo) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid diagosticoSugestivoOld = this.diagosticoSugestivo;
		this.diagosticoSugestivo = diagosticoSugestivo;
//        this.getPropertyChangeSupport().firePropertyChange ("diagosticoSugestivo", diagosticoSugestivoOld, diagosticoSugestivo);
	}



	/**
	 * Return the value associated with the column: diagostico_caso_descartado
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.Cid getDiagosticoCasoDescartado () {
		return getPropertyValue(this, diagosticoCasoDescartado, PROP_DIAGOSTICO_CASO_DESCARTADO); 
	}

	/**
	 * Set the value related to the column: diagostico_caso_descartado
	 * @param diagosticoCasoDescartado the diagostico_caso_descartado value
	 */
	public void setDiagosticoCasoDescartado (br.com.ksisolucoes.vo.prontuario.basico.Cid diagosticoCasoDescartado) {
//        br.com.ksisolucoes.vo.prontuario.basico.Cid diagosticoCasoDescartadoOld = this.diagosticoCasoDescartado;
		this.diagosticoCasoDescartado = diagosticoCasoDescartado;
//        this.getPropertyChangeSupport().firePropertyChange ("diagosticoCasoDescartado", diagosticoCasoDescartadoOld, diagosticoCasoDescartado);
	}



	/**
	 * Return the value associated with the column: cd_usuario_encerramento
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuarioEncerramento () {
		return getPropertyValue(this, usuarioEncerramento, PROP_USUARIO_ENCERRAMENTO); 
	}

	/**
	 * Set the value related to the column: cd_usuario_encerramento
	 * @param usuarioEncerramento the cd_usuario_encerramento value
	 */
	public void setUsuarioEncerramento (br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramento) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioEncerramentoOld = this.usuarioEncerramento;
		this.usuarioEncerramento = usuarioEncerramento;
//        this.getPropertyChangeSupport().firePropertyChange ("usuarioEncerramento", usuarioEncerramentoOld, usuarioEncerramento);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoParalisiaFlacidaAguda)) return false;
		else {
			br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoParalisiaFlacidaAguda investigacaoAgravoParalisiaFlacidaAguda = (br.com.ksisolucoes.vo.vigilancia.investigacao.InvestigacaoAgravoParalisiaFlacidaAguda) obj;
			if (null == this.getCodigo() || null == investigacaoAgravoParalisiaFlacidaAguda.getCodigo()) return false;
			else return (this.getCodigo().equals(investigacaoAgravoParalisiaFlacidaAguda.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}