<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.vigilancia">
    <class name="AtividadeEstabelecimentoTipoServico" table="atividade_estabelecimento_tipo_servico">
        <id
                column="cd_atividade_estabelecimento_tipo_servico"
                name="codigo"
                type="java.lang.Long"
        >
            <generator class="sequence">
                <param name="sequence">seq_atividade_estabelecimento_tipo_servico</param>
            </generator>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
            class="br.com.ksisolucoes.vo.vigilancia.AtividadeEstabelecimento"
            column="cd_atividade_estabelecimento"
            name="atividadeEstabelecimento"
            not-null="true"
        />

        <property
                name="tipoServico"
                column="tp_servico"
                type="java.lang.String"
                not-null="true"
        />

        <property
                name="dataCadastro"
                column="dt_cadastro"
                type="timestamp"
                not-null="true"
        />

        <property
                name="dataAlteracao"
                column="dt_alteracao"
                type="timestamp"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuarioCadastro"
                not-null="true"
        />

    </class>
</hibernate-mapping>
