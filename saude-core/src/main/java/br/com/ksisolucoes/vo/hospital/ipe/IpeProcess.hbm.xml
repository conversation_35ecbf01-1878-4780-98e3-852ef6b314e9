<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
	
<hibernate-mapping package="br.com.ksisolucoes.vo.hospital.ipe"  >
    <class 
		name="IpeProcess"
		table="ipe_process"
	>

	<id
                name="codigo"
                type="java.lang.Long"
                column="cd_ipe_process"
        >
            <generator class="sequence">
               <param name="sequence">seq_gem</param>
            </generator>
        </id> <version column="version" name="version" type="long" />

        <many-to-one
                class="br.com.ksisolucoes.vo.service.AsyncProcess"
                column="cd_process"
                name="asyncProcess"
                not-null="true"
         />

        <property
                name="totalRegistros"
                column="total_registros"
                type="java.lang.Long"
        />

        <property
                name="totalProcessado"
                column="total_processado"
                type="java.lang.Long"
        />

        <property
                name="dataTermino"
                column="dt_termino"
                type="timestamp"
        />
        
        <property
                name="mensagemValidacao"
                column="msg_validacao"
                type="java.lang.String"
        />
        
	</class>
</hibernate-mapping>