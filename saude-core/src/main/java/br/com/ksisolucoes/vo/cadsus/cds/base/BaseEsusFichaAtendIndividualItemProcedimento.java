package br.com.ksisolucoes.vo.cadsus.cds.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the esus_ficha_atend_individual_item_procedimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="esus_ficha_atend_individual_item_procedimento"
 */

public abstract class BaseEsusFichaAtendIndividualItemProcedimento extends BaseRootVO implements Serializable {

	public static String REF = "EsusFichaAtendIndividualItemProcedimento";
	public static final String PROP_PROCEDIMENTO = "procedimento";
	public static final String PROP_USUARIO = "usuario";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_ESUS_FICHA_ATEND_INDIVIDUAL_ITEM = "esusFichaAtendIndividualItem";
	public static final String PROP_DATA_CADASTRO = "dataCadastro";
	public static final String PROP_SOLICITADO_AVALIADO = "solicitadoAvaliado";
	public static final String PROP_EXAME_ESUS = "exameEsus";


	// constructors
	public BaseEsusFichaAtendIndividualItemProcedimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEsusFichaAtendIndividualItemProcedimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseEsusFichaAtendIndividualItemProcedimento (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem esusFichaAtendIndividualItem,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.util.Date dataCadastro) {

		this.setCodigo(codigo);
		this.setEsusFichaAtendIndividualItem(esusFichaAtendIndividualItem);
		this.setUsuario(usuario);
		this.setDataCadastro(dataCadastro);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long solicitadoAvaliado;
	private java.util.Date dataCadastro;

	// many to one
	private br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem esusFichaAtendIndividualItem;
	private br.com.ksisolucoes.vo.esus.ExameEsus exameEsus;
	private br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento;
	private br.com.ksisolucoes.vo.controle.Usuario usuario;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_ficha_atend_individual_item_proced"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: solicitado_avaliado
	 */
	public java.lang.Long getSolicitadoAvaliado () {
		return getPropertyValue(this, solicitadoAvaliado, PROP_SOLICITADO_AVALIADO); 
	}

	/**
	 * Set the value related to the column: solicitado_avaliado
	 * @param solicitadoAvaliado the solicitado_avaliado value
	 */
	public void setSolicitadoAvaliado (java.lang.Long solicitadoAvaliado) {
//        java.lang.Long solicitadoAvaliadoOld = this.solicitadoAvaliado;
		this.solicitadoAvaliado = solicitadoAvaliado;
//        this.getPropertyChangeSupport().firePropertyChange ("solicitadoAvaliado", solicitadoAvaliadoOld, solicitadoAvaliado);
	}



	/**
	 * Return the value associated with the column: dt_cadastro
	 */
	public java.util.Date getDataCadastro () {
		return getPropertyValue(this, dataCadastro, PROP_DATA_CADASTRO); 
	}

	/**
	 * Set the value related to the column: dt_cadastro
	 * @param dataCadastro the dt_cadastro value
	 */
	public void setDataCadastro (java.util.Date dataCadastro) {
//        java.util.Date dataCadastroOld = this.dataCadastro;
		this.dataCadastro = dataCadastro;
//        this.getPropertyChangeSupport().firePropertyChange ("dataCadastro", dataCadastroOld, dataCadastro);
	}



	/**
	 * Return the value associated with the column: cd_esus_ficha_atend_individual_item
	 */
	public br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem getEsusFichaAtendIndividualItem () {
		return getPropertyValue(this, esusFichaAtendIndividualItem, PROP_ESUS_FICHA_ATEND_INDIVIDUAL_ITEM); 
	}

	/**
	 * Set the value related to the column: cd_esus_ficha_atend_individual_item
	 * @param esusFichaAtendIndividualItem the cd_esus_ficha_atend_individual_item value
	 */
	public void setEsusFichaAtendIndividualItem (br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem esusFichaAtendIndividualItem) {
//        br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItem esusFichaAtendIndividualItemOld = this.esusFichaAtendIndividualItem;
		this.esusFichaAtendIndividualItem = esusFichaAtendIndividualItem;
//        this.getPropertyChangeSupport().firePropertyChange ("esusFichaAtendIndividualItem", esusFichaAtendIndividualItemOld, esusFichaAtendIndividualItem);
	}



	/**
	 * Return the value associated with the column: cd_exame_esus
	 */
	public br.com.ksisolucoes.vo.esus.ExameEsus getExameEsus () {
		return getPropertyValue(this, exameEsus, PROP_EXAME_ESUS); 
	}

	/**
	 * Set the value related to the column: cd_exame_esus
	 * @param exameEsus the cd_exame_esus value
	 */
	public void setExameEsus (br.com.ksisolucoes.vo.esus.ExameEsus exameEsus) {
//        br.com.ksisolucoes.vo.esus.ExameEsus exameEsusOld = this.exameEsus;
		this.exameEsus = exameEsus;
//        this.getPropertyChangeSupport().firePropertyChange ("exameEsus", exameEsusOld, exameEsus);
	}



	/**
	 * Return the value associated with the column: cd_procedimento
	 */
	public br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento getProcedimento () {
		return getPropertyValue(this, procedimento, PROP_PROCEDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_procedimento
	 * @param procedimento the cd_procedimento value
	 */
	public void setProcedimento (br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimento) {
//        br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento procedimentoOld = this.procedimento;
		this.procedimento = procedimento;
//        this.getPropertyChangeSupport().firePropertyChange ("procedimento", procedimentoOld, procedimento);
	}



	/**
	 * Return the value associated with the column: cd_usuario
	 */
	public br.com.ksisolucoes.vo.controle.Usuario getUsuario () {
		return getPropertyValue(this, usuario, PROP_USUARIO); 
	}

	/**
	 * Set the value related to the column: cd_usuario
	 * @param usuario the cd_usuario value
	 */
	public void setUsuario (br.com.ksisolucoes.vo.controle.Usuario usuario) {
//        br.com.ksisolucoes.vo.controle.Usuario usuarioOld = this.usuario;
		this.usuario = usuario;
//        this.getPropertyChangeSupport().firePropertyChange ("usuario", usuarioOld, usuario);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItemProcedimento)) return false;
		else {
			br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItemProcedimento esusFichaAtendIndividualItemProcedimento = (br.com.ksisolucoes.vo.cadsus.cds.EsusFichaAtendIndividualItemProcedimento) obj;
			if (null == this.getCodigo() || null == esusFichaAtendIndividualItemProcedimento.getCodigo()) return false;
			else return (this.getCodigo().equals(esusFichaAtendIndividualItemProcedimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}