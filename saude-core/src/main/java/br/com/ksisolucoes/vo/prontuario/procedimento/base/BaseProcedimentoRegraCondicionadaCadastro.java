package br.com.ksisolucoes.vo.prontuario.procedimento.base;

import br.com.ksisolucoes.util.validacao.RetornoValidacao;
import br.com.ksisolucoes.vo.BaseRootVO;

import java.io.Serializable;


/**
 * This is an object that contains data related to the procedimento_regra_condicionada_cad table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="procedimento_regra_condicionada_cad"
 */

public abstract class BaseProcedimentoRegraCondicionadaCadastro extends BaseRootVO implements Serializable {

	public static String REF = "ProcedimentoRegraCondicionadaCadastro";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_DESCRICAO = "descricao";
	public static final String PROP_OBSERVACAO = "observacao";


	// constructors
	public BaseProcedimentoRegraCondicionadaCadastro () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseProcedimentoRegraCondicionadaCadastro (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	/**
	 * Constructor for required fields
	 */
	public BaseProcedimentoRegraCondicionadaCadastro (
		java.lang.Long codigo,
		java.lang.String descricao,
		java.lang.String observacao) {

		this.setCodigo(codigo);
		this.setDescricao(descricao);
		this.setObservacao(observacao);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.String descricao;
	private java.lang.String observacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_regra_condicionada_cad"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: descricao
	 */
	public java.lang.String getDescricao () {
		return getPropertyValue(this, descricao, PROP_DESCRICAO); 
	}

	/**
	 * Set the value related to the column: descricao
	 * @param descricao the descricao value
	 */
	public void setDescricao (java.lang.String descricao) {
//        java.lang.String descricaoOld = this.descricao;
		this.descricao = descricao;
//        this.getPropertyChangeSupport().firePropertyChange ("descricao", descricaoOld, descricao);
	}



	/**
	 * Return the value associated with the column: observacao
	 */
	public java.lang.String getObservacao () {
		return getPropertyValue(this, observacao, PROP_OBSERVACAO); 
	}

	/**
	 * Set the value related to the column: observacao
	 * @param observacao the observacao value
	 */
	public void setObservacao (java.lang.String observacao) {
//        java.lang.String observacaoOld = this.observacao;
		this.observacao = observacao;
//        this.getPropertyChangeSupport().firePropertyChange ("observacao", observacaoOld, observacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegraCondicionadaCadastro)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegraCondicionadaCadastro procedimentoRegraCondicionadaCadastro = (br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegraCondicionadaCadastro) obj;
			if (null == this.getCodigo() || null == procedimentoRegraCondicionadaCadastro.getCodigo()) return false;
			else return (this.getCodigo().equals(procedimentoRegraCondicionadaCadastro.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}