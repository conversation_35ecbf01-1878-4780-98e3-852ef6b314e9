package br.com.ksisolucoes.vo.vigilancia.requerimentos;

import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Estado;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;
import br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.base.BaseRequerimentoProjetoArquitetonicoSanitario;

import java.io.Serializable;


public class RequerimentoProjetoArquitetonicoSanitario extends BaseRequerimentoProjetoArquitetonicoSanitario implements CodigoManager {
    private static final long serialVersionUID = 1L;

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public RequerimentoProjetoArquitetonicoSanitario () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RequerimentoProjetoArquitetonicoSanitario (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RequerimentoProjetoArquitetonicoSanitario (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia requerimentoVigilancia,
		br.com.ksisolucoes.vo.vigilancia.endereco.VigilanciaEndereco vigilanciaEndereco,
		br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico autorProjeto,
		java.lang.String obraQuadra,
		java.lang.String obraLote,
		java.lang.String numeroCadastroAtividadeEconomicaAutor) {

		super (
			codigo,
			requerimentoVigilancia,
			vigilanciaEndereco,
			autorProjeto,
			obraQuadra,
			obraLote,
			numeroCadastroAtividadeEconomicaAutor);
	}

    /*[CONSTRUCTOR MARKER END]*/

    public static RequerimentoProjetoArquitetonicoSanitario carregarRequerimentoProjetoArquitetonico(RequerimentoVigilancia requerimentoVigilancia) {
        RequerimentoProjetoArquitetonicoSanitario requerimento = new RequerimentoProjetoArquitetonicoSanitario();

        if (requerimentoVigilancia != null && requerimentoVigilancia.getCodigo() != null) {
            requerimento =
                    LoadManager.getInstance(RequerimentoProjetoArquitetonicoSanitario.class)
                            .addProperties(new HQLProperties(RequerimentoProjetoArquitetonicoSanitario.class).getProperties())
                            .addProperties(new HQLProperties(ResponsavelTecnico.class, VOUtils.montarPath(RequerimentoProjetoArquitetonicoSanitario.PROP_AUTOR_PROJETO)).getProperties())
                            .addProperties(new HQLProperties(Estabelecimento.class, VOUtils.montarPath(RequerimentoProjetoArquitetonicoSanitario.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_ESTABELECIMENTO)).getProperties())
                            .addProperties(new HQLProperties(VigilanciaPessoa.class, VOUtils.montarPath(RequerimentoProjetoArquitetonicoSanitario.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_PESSOA)).getProperties())
                            .addProperties(new HQLProperties(Cidade.class, VOUtils.montarPath(RequerimentoProjetoArquitetonicoSanitario.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE)).getProperties())
                            .addProperties(new HQLProperties(Estado.class, VOUtils.montarPath(RequerimentoProjetoArquitetonicoSanitario.PROP_REQUERIMENTO_VIGILANCIA, RequerimentoVigilancia.PROP_VIGILANCIA_ENDERECO, VigilanciaEndereco.PROP_CIDADE, Cidade.PROP_ESTADO)).getProperties())
                            .addProperties(new HQLProperties(RequerimentoVigilancia.class, RequerimentoProjetoArquitetonicoSanitario.PROP_REQUERIMENTO_VIGILANCIA).getProperties())
                            .addProperties(new HQLProperties(VigilanciaEndereco.class, VOUtils.montarPath(RequerimentoProjetoArquitetonicoSanitario.PROP_VIGILANCIA_ENDERECO)).getProperties())
                            .addParameter(new QueryCustom.QueryCustomParameter(RequerimentoProjetoArquitetonicoSanitario.PROP_REQUERIMENTO_VIGILANCIA, requerimentoVigilancia))
                            .start()
                            .getVO();

        }

        return requerimento;
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

    public void setCodigoManager(Serializable key) {
        this.setCodigo((java.lang.Long) key);
    }
}