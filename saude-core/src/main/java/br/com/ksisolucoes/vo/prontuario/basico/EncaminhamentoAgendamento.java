package br.com.ksisolucoes.vo.prontuario.basico;

import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;

import br.com.ksisolucoes.vo.prontuario.basico.base.BaseEncaminhamentoAgendamento;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class EncaminhamentoAgendamento extends BaseEncaminhamentoAgendamento implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final Long STATUS_NORMAL = 0L;
        public static final Long STATUS_CANCELADO = 3L;
        public static final Long STATUS_NAO_COMPARECEU = 4L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EncaminhamentoAgendamento () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public EncaminhamentoAgendamento (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public EncaminhamentoAgendamento (
		java.lang.Long codigo,
		java.util.Date dataAgendamento,
		java.lang.Long status) {

		super (
			codigo,
			dataAgendamento,
			status);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoStatus() {
        if (STATUS_NORMAL.equals(getStatus())) {
            return Bundle.getStringApplication("rotulo_normal");
        } else if (STATUS_CANCELADO.equals(getStatus())) {
            return Bundle.getStringApplication("rotulo_cancelado");
        } else if (STATUS_NAO_COMPARECEU.equals(getStatus())) {
            return Bundle.getStringApplication("rotulo_nao_compareceu");
        }
        return null;
    }
    
}