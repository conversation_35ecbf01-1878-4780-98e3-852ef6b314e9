<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
	"-//Hibernate/Hibernate Mapping DTD//EN"
	"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.procedimento"  >
	<class
		name="ProcedimentoModalidade"
		table="procedimento_modalidade"
	>
		<composite-id name="id" class="ProcedimentoModalidadePK">
			<key-many-to-one
				name="procedimentoModalidadeCadastro"
				class="ProcedimentoModalidadeCadastro"
				column="cd_modalidade"
			/>
			<key-many-to-one class="ProcedimentoCompetencia" name="procedimentoCompetencia">
				<column name="cd_procedimento"/>
				<column name="dt_competencia"/>
			 </key-many-to-one>
		</composite-id> <version column="version" name="version" type="long" />

	</class>	
</hibernate-mapping>