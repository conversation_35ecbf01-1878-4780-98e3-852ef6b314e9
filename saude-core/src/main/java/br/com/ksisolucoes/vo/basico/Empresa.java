package br.com.ksisolucoes.vo.basico;

import br.com.celk.integracao.IntegracaoRest;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.TipoEstabelecimento;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.util.*;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.util.parametrogem.DefaultPanelConsultaBean;
import br.com.ksisolucoes.util.parametrogem.ParametroPanelConsultaBean;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.base.BaseEmpresa;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.DictionaryData;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.text.ParseException;

@ParametroPanelConsultaBean("br.com.celk.view.basico.empresa.autocomplete.AutoCompleteConsultaEmpresa")
@DefaultPanelConsultaBean("br.com.ksisolucoes.gui.basico.consulta.panel.PnlConsultaEmpresa")
@DictionaryData(referenceField = "referencia")
@JsonIgnoreProperties(ignoreUnknown = true)
@IntegracaoRest
public class Empresa extends BaseEmpresa implements CodigoManager, PesquisaObjectInterface, Comparable<Empresa> {

    private static final long serialVersionUID = 1L;

    public static final String PROP_REFERENCIA = "referencia";
    public static final String PROP_CNPJ_FORMATADO = "cnpjFormatado";
    public static final String PROP_TIPO_UNIDADE_FORMATADO = "tipoUnidadeFormatado";
    public static final String PROP_TELEFONE_FORMATADO = "telefoneFormatado";

    /**
     * Sempre que for possível, remover os atributos static
     * e passar a utilizaro ENUM TipoEstabelecimento
     */
    public static final Long TIPO_ESTABELECIMENTO_UNIDADE =  TipoEstabelecimento.UNIDADE.value();
    public static final Long TIPO_ESTABELECIMENTO_EXTERNO = TipoEstabelecimento.EXTERNO.value();
    public static final Long TIPO_ESTABELECIMENTO_PRESTADOR_SERVICO = TipoEstabelecimento.PRESTADOR_SERVICO.value();
    public static final Long TIPO_ESTABELECIMENTO_SECRETARIA_SAUDE = TipoEstabelecimento.SECRETARIA_SAUDE.value();
    public static final Long TIPO_ESTABELECIMENTO_ALMOXARIFADO_ODONTO = TipoEstabelecimento.ALMOXARIFADO_ODONTO.value();
    public static final Long TIPO_ESTABELECIMENTO_ALMOXARIFADO_MATERIAL = TipoEstabelecimento.ALMOXARIFADO_MATERIAL.value();
    public static final Long TIPO_ESTABELECIMENTO_ALMOXARIFADO_MEDICAMENTO = TipoEstabelecimento.ALMOXARIFADO_MEDICAMENTO.value();
    public static final Long TIPO_ESTABELECIMENTO_FARMACIA = TipoEstabelecimento.FARMACIA.value();
    public static final Long TIPO_ESTABELECIMENTO_CONSORCIO = TipoEstabelecimento.CONSORCIO.value();
    public static final Long TIPO_ESTABELECIMENTO_CONSORCIADO = TipoEstabelecimento.CONSORCIADO.value();
    public static final Long TIPO_ESTABELECIMENTO_VIGILANCIA_EXTERNO = TipoEstabelecimento.VIGILANCIA_EXTERNO.value();
    public static final Long TIPO_ESTABELECIMENTO_UNIDADE_FILANTROPICA = TipoEstabelecimento.UNIDADE_FILANTROPICA.value();

    public static final String PROP_DESCRICAO_FORMATADO = "descricaoFormatado";
    public static final String PROP_DESCRICAO_FORMATADO_CNES_SIGLA = "descricaoFormatadaCnesSigla";
    public static final String PROP_DESCRICAO_FORMATADO_CNES_DESCRICAO = "descricaoFormatadaCnesDescricao";
    public static final String PROP_DESCRICAO_FORMATADO_CONSORCIADO_ATIVO = "descricaoFormatadaConsorciadoAtivo";
    public static final String PROP_DESCRICAO_SITUACAO_BLOQUEIO = "descricaoSituacaoBloqueio";
    public static final String PROP_PERIODO_BLOQUEIO_CONSORCIO_DESCRICAO = "periodoBloqueioConsorcioDescricao";

    /**
     * Constante que corresponde ao valor "percentual" do atributo
     * flagPercentualQuantidade
     */
    public static final String FLAG_PERCENTUAL = "P";

    /**
     * Constante que corresponde ao valor "quantidade" do atributo
     * flagPercentualQuantidade
     */
    public static final String FLAG_QUANTIDADE = "Q";

    public static final Long DEMANDA_ESPONTANEA = 1L;
    public static final Long DEMANDA_REFERENCIADA = 2L;
    public static final Long DEMANDA_ESPONTANEA_E_REFERENCIADA = 3L;

    public static final Long TIPO_CONTROLE_MANUAL = 0L;
    public static final Long TIPO_CONTROLE_SISTEMA = 1L;

    public static final Long ATIVIDADE_PRONTO_ATENDIMENTO = 73L;

    public enum LocalAtendimento implements IEnum {

        UBS(1L, Bundle.getStringApplication("rotulo_ubs")),
        UNIDADE_MOVEL(2L, Bundle.getStringApplication("rotulo_unidade_movel")),
        RUA(3L, Bundle.getStringApplication("rotulo_rua")),
        DOMICILIO(4L, Bundle.getStringApplication("rotulo_domicilio")),
        ESCOLA_CRECHE(5L, Bundle.getStringApplication("rotulo_escola_creche")),
        OUTROS(6L, Bundle.getStringApplication("rotulo_outros")),
        POLO_ACADEMIA_SAUDE(7L, Bundle.getStringApplication("rotulo_polo_academia_saude")),
        INSTITUICAO_ABRIGO(8L, Bundle.getStringApplication("rotulo_instituicao_abrigo")),
        UNIDADE_PRISIONAL_CONGENERAS(9L, Bundle.getStringApplication("rotulo_unidade_prisional_congeneras")),
        UNIDADE_SOCIOEDUCATIVA(10L, Bundle.getStringApplication("rotulo_unidade_socioeducativa")),
        HOSPITAL(11L, Bundle.getStringApplication("rotulo_hospital")),
        UNIDADE_PRONTO_ATENDIMENTO(12L, Bundle.getStringApplication("rotulo_unidade_pronto_atendimento")),
        CACON_UNACON(13L, Bundle.getStringApplication("rotulo_cacon_unacon")),
        HOSPITAL_SOS_URGENCIA_EMERGENCIA(14L, Bundle.getStringApplication("rotulo_hospital_sos_urgencia_emergencia")),
        HOSPITAL_SOS_DEMAIS_SETORES(15L, Bundle.getStringApplication("rotulo_hospital_sos_demais_setores")),
        ;

        private final Long value;
        private final String descricao;

        LocalAtendimento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static LocalAtendimento valueOf(Long value) {
            for (LocalAtendimento localAtendimento : LocalAtendimento.values()) {
                if (localAtendimento.value().equals(value)) {
                    return localAtendimento;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    /*[CONSTRUCTOR MARKER BEGIN]*/
	public Empresa () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Empresa (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Empresa (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.basico.Cidade cidade,
		br.com.ksisolucoes.vo.basico.Atividade atividade,
		br.com.ksisolucoes.vo.basico.Cidade cidadeAdicional,
		br.com.ksisolucoes.vo.controle.Usuario usuarioResponsavel,
		java.lang.String descricao,
		java.lang.Long situacaoBloqueio,
		java.lang.String url) {

		super (
			codigo,
			cidade,
			atividade,
			cidadeAdicional,
			usuarioResponsavel,
			descricao,
			situacaoBloqueio,
			url);
	}

    /*[CONSTRUCTOR MARKER END]*/
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    /* (non-Javadoc)
     * @see br.com.ksisolucoes.vo.interfaces.IDManager#setId(java.lang.Long)
     */

    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }

    public String getDescricaoVO() {
        return this.getDescricao();
    }

    public String getIdentificador() {
        return this.getCodigo().toString();
    }

    /**
     * Retorna a descricao do produto formatada no seguinte formato:<br>
     * ( <codigo> ) <descricao>
     *
     * @return <code>String</code>
     */
    public String getDescricaoFormatado() {
        if (this.getReferencia() == null && this.getCodigo() == null) {
            return "";
        }
        return Util.getDescricaoFormatado(
                Coalesce.asString(this.getReferencia(), this.getCodigo().toString()),
                Coalesce.asString(this.getDescricao()));
    }

    public String getCnpjFormatado() {
        if (getCnpj() != null && !getCnpj().trim().equals("")) {
            String cpfCnpj = getCnpj();
            cpfCnpj = StringUtilKsi.getDigits(Coalesce.asString(cpfCnpj));
            if (cpfCnpj.trim().length() > 12) {
                try {
                    MaskFormatter m = new MaskFormatter("##.###.###/####-##");
                    m.setValueContainsLiteralCharacters(false);
                    return m.valueToString(cpfCnpj);
                } catch (ParseException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            } else {
                try {
                    MaskFormatter m = new MaskFormatter("###.###.###-##");
                    m.setValueContainsLiteralCharacters(false);
                    return m.valueToString(cpfCnpj);
                } catch (ParseException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        }
        return "";
    }

    public String getDescricaoFormatadaCnesSigla() {
        final String descricao = (Coalesce.asString(this.getCnes()) + " - " + Coalesce.asString(this.getSigla())).trim();
        return descricao.equals("-") ? "" : descricao;
    }

    public String getDescricaoFormatadaCnesDescricao() {
        final String descricao = (Coalesce.asString(this.getCnes()) + " - " + Coalesce.asString(this.getDescricao())).trim();
        return descricao.equals("-") ? "" : descricao;
    }

    public int compareTo(Empresa o) {
        int comparison;

        if (o == null) {
            comparison = -1;
        } else {
            comparison = this.getCodigo().compareTo(o.getCodigo());
        }

        return comparison;
    }

    @Override
    public String toString() {
        return getDescricaoFormatado();
    }

    public String getTipoUnidadeFormatado() {
        return getTipoUnidadeFormatado(getTipoUnidade());
    }

    public static String getTipoUnidadeFormatado(Long tipoUnidade) {
        return tipoUnidade != null ? TipoEstabelecimento.valeuOf(tipoUnidade).descricao() : "";
    }

    public String getTelefoneFormatado() {
        return Util.getTelefoneFormatado(getTelefone());
    }

    public String getCelularFormatado() {
        return Util.getTelefoneFormatado(getCelular());
    }

    public String getFaxFormatado() {
        return Util.getTelefoneFormatado(getFax());
    }

    public String getEnderecoFormatado() {
        String enderecoFormatado = null;
        if (getRua() != null) {
            enderecoFormatado = getRua();
        }
        if (getNumero() != null) {
            enderecoFormatado += ", " + getNumero();
        }
        if (getComplemento() != null) {
            enderecoFormatado += ", " + getComplemento();
        }
        return enderecoFormatado;
    }

    public String getEnderecoBairroFormatado() {
        String enderecoFormatado = null;
        if (getRua() != null) {
            enderecoFormatado = getRua();
        }
        if (getNumero() != null) {
            enderecoFormatado += ", " + getNumero();
        }
        if (getComplemento() != null) {
            enderecoFormatado += ", " + getComplemento();
        }
        if (getBairro() != null) {
            enderecoFormatado += ", " + getBairro();
        }
        return enderecoFormatado;
    }

    public String getEnderecoCidadeBairroFormatado() {
        StringBuilder builder = new StringBuilder();
        if (getRua() != null) {
            builder.append(getRua());
        }
        if (getNumero() != null) {
            builder.append(", ");
            builder.append(getNumero());
        } else {
            builder.append(", S/N");
        }
        if (getComplemento() != null && getComplemento().trim().length() > 0) {
            builder.append(", ");
            builder.append(getComplemento());
        }
        if (getBairro() != null) {
            builder.append(", ");
            builder.append(getBairro());
        }
        if (getCidade() != null) {
            builder.append(", ");
            builder.append(getCidade().getDescricaoCidadeUf());
        }
        return builder.toString();
    }

    public String getEnderecoRuaNumeroBairroCidadeEstadoFormatado() {
        StringBuilder builder = new StringBuilder();
        if (getRua() != null) {
            builder.append(getRua());
        }
        if (getComplemento() != null && getComplemento().trim().length() > 0) {
            builder.append(", ");
            builder.append(getComplemento());
        }
        if (getNumero() != null) {
            builder.append(", ");
            builder.append(getNumero());
        } else {
            builder.append(", S/N");
        }
        if (getBairro() != null) {
            builder.append(" - ");
            builder.append(getBairro());
        }
        if (getCidade() != null) {
            builder.append(" - ");
            builder.append(getCidade().getDescricaoCidadeUf());
        }
        return builder.toString();
    }

    public String getEnderecoCidadeBairroCepFormatado() {
        StringBuilder builder = new StringBuilder();
        if (getRua() != null) {
            builder.append(getRua());
        }
        if (getNumero() != null) {
            builder.append(", ");
            builder.append(getNumero());
        } else {
            builder.append(", S/N");
        }
        if (getComplemento() != null && getComplemento().trim().length() > 0) {
            builder.append(", ");
            builder.append(getComplemento());
        }
        if (getBairro() != null) {
            builder.append(", ");
            builder.append(getBairro());
        }
        if (getCidade() != null) {
            builder.append(", ");
            builder.append(getCidade().getDescricaoCidadeUf());
        }
        if (getCep() != null) {
            builder.append(", ");
            builder.append(getCepFormatado());
        }
        return builder.toString();
    }

    public String getCepFormatado() {
        try {
            if (getCep() != null) {
                MaskFormatter m = new MaskFormatter("#####-###");
                m.setValueContainsLiteralCharacters(false);

                return m.valueToString(getCep().replaceAll("[^0-9]", ""));
            }
        } catch (ParseException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }

        return "";
    }

    public String getDescricaoLocalAtendimento() {
        LocalAtendimento localAtendimento = LocalAtendimento.valueOf(getLocalAtendimento());
        if (localAtendimento != null && localAtendimento.descricao() != null) {
            return localAtendimento.descricao();
        }
        return "";
    }

    public String getDescricaoTipoPessoa() {
        return Pessoa.getFlagPessoaFisicaJuridicaDescricao(this.getFlagFisicaJuridica());
    }

    public String getDescricaoFormatadaConsorciadoAtivo() {
        if (RepositoryComponentDefault.SIM_LONG.equals(getConsorciadoAtivo())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else {
            return Bundle.getStringApplication("rotulo_nao");
        }
    }

    public enum SituacaoBloqueio implements IEnum {

        DESBLOQUEADO(0L, Bundle.getStringApplication("rotulo_desbloqueado")),
        BLOQUEADO_PERIODO(1L, Bundle.getStringApplication("rotulo_bloqueado_periodo")),
        BLOQUEADO_INDETERMINADO(2L, Bundle.getStringApplication("rotulo_bloqueado_inderteminado")),
        ;

        private final Long value;
        private final String descricao;

        SituacaoBloqueio(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static SituacaoBloqueio valueOf(Long value) {
            for (SituacaoBloqueio situacaoBloqueio : SituacaoBloqueio.values()) {
                if (situacaoBloqueio.value().equals(value)) {
                    return situacaoBloqueio;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

    public String getDescricaoSituacaoBloqueio() {
        if (getSituacaoBloqueio() != null) {
            if (SituacaoBloqueio.DESBLOQUEADO.value().equals(getSituacaoBloqueio())) {
                return SituacaoBloqueio.DESBLOQUEADO.descricao();
            } else if (SituacaoBloqueio.BLOQUEADO_PERIODO.value().equals(getSituacaoBloqueio())) {
                return SituacaoBloqueio.BLOQUEADO_PERIODO.descricao();
            } else if (SituacaoBloqueio.BLOQUEADO_INDETERMINADO.value().equals(getSituacaoBloqueio())) {
                return SituacaoBloqueio.BLOQUEADO_INDETERMINADO.descricao();
            }
        }
        return "";
    }

    public String getPeriodoBloqueioConsorcioDescricao() {
        StringBuilder periodo = new StringBuilder();

        if (getDataBloqueioInicial() != null && getDataBloqueioFinal() != null) {
            periodo.append(DataUtil.getFormatarDiaMesAno(getDataBloqueioInicial()) + " - " + DataUtil.getFormatarDiaMesAno(getDataBloqueioFinal()));
            return periodo.toString();
        }

        return "";
    }

    public TipoEstabelecimento getTipoEstabelecimento(){
        return TipoEstabelecimento.valeuOf(getTipoUnidade());
    }

    public boolean isTipoEstabelecimentoFilantropico(){
        return  TipoEstabelecimento.UNIDADE_FILANTROPICA.equals(getTipoEstabelecimento());
    }

    public boolean isTipoEstabelecimentoUnidade(){
        return  TipoEstabelecimento.UNIDADE.equals(getTipoEstabelecimento());
    }

    public boolean isTipoEstabelecimentoPresadorServico(){
        return  TipoEstabelecimento.PRESTADOR_SERVICO.equals(getTipoEstabelecimento());
    }

    public boolean isTipoEstabelecimentoSecretariaSaude(){
        return  TipoEstabelecimento.SECRETARIA_SAUDE.equals(getTipoEstabelecimento());
    }

    public static void validaConsomeCotaDaUnidade() throws DAOException, ValidacaoException {
        String consomeCotaOutraUnidadeAutorizadora = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("consomeCotaOutraUnidadeAutorizadora");
        Empresa consomeCotaDaUnidade = BOFactory.getBO(CommomFacade.class).modulo(Modulos.AGENDAMENTO).getParametro("consomeCotaDaUnidade");
        if (RepositoryComponentDefault.SIM.equals(consomeCotaOutraUnidadeAutorizadora) && consomeCotaDaUnidade == null) {
            throw new ValidacaoException(Bundle.getStringApplication("msg_valida_consome_cota_da_unidade"));
        }
    }
}
