<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus"  >
    <class name="TipoLogradouroCnes" table="tipo_logradouro_cnes">

        <id
            name="codigo"
            type="java.lang.String"
            column="cd_tipo_logradouro"
            length="3"
            >
            <generator class="assigned" />
        </id> <version column="version" name="version" type="long" />
 
        <property
            column="ds_tipo_logradouro"
            name="descricao"
            not-null="false"
            type="java.lang.String"
            length="60"
            />

    </class>

</hibernate-mapping>
