package br.com.ksisolucoes.vo.prontuario.basico.base;

import java.io.Serializable;

import br.com.ksisolucoes.vo.BaseRootVO;
import br.com.ksisolucoes.vo.interfaces.ValidacaoExceptionInterface;
import br.com.ksisolucoes.util.validacao.RetornoValidacao;


/**
 * This is an object that contains data related to the empresa_nat_proc_tp_atendimento table.
 * Do not modify this class because it will be overwritten if the configuration file
 * related to this class is modified.
 *
 * @hibernate.class
 *  table="empresa_nat_proc_tp_atendimento"
 */

public abstract class BaseEmpresaNaturezaProcuraTipoAtendimento extends BaseRootVO implements Serializable {

	public static String REF = "EmpresaNaturezaProcuraTipoAtendimento";
	public static final String PROP_CODIGO = "codigo";
	public static final String PROP_FALTA_PRONTUARIO = "faltaProntuario";
	public static final String PROP_CONTROLE_ATENDIMENTO = "controleAtendimento";
	public static final String PROP_EMPRESA_DISPENSACAO = "empresaDispensacao";
	public static final String PROP_EMPRESA = "empresa";
	public static final String PROP_NUMERO_VAGA_DIA = "numeroVagaDia";
	public static final String PROP_PAINEL = "painel";
	public static final String PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO = "naturezaProcuraTipoAtendimento";
	public static final String PROP_IMPRIME_FAA = "imprimeFaa";
	public static final String PROP_VISIVEL = "visivel";


	// constructors
	public BaseEmpresaNaturezaProcuraTipoAtendimento () {
		initialize();
	}

	/**
	 * Constructor for primary key
	 */
	public BaseEmpresaNaturezaProcuraTipoAtendimento (java.lang.Long codigo) {
		this.setCodigo(codigo);
		initialize();
	}

	protected void initialize () {}



	private int hashCode = Integer.MIN_VALUE;

	// primary key
	private java.lang.Long codigo;

	// fields
	private java.lang.Long numeroVagaDia;
	private java.lang.Long controleAtendimento;
	private java.lang.Long imprimeFaa;
	private java.lang.String visivel;
	private java.lang.Long faltaProntuario;

	// many to one
	private br.com.ksisolucoes.vo.basico.Empresa empresa;
	private br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento;
	private br.com.ksisolucoes.vo.atendimento.painel.Painel painel;
	private br.com.ksisolucoes.vo.basico.Empresa empresaDispensacao;



	/**
	 * Return the unique identifier of this class
     * @hibernate.id
     *  generator-class="assigned"
     *  column="cd_emp_nat_proc_tp_atendimento"
     */
	public java.lang.Long getCodigo () {
	    return getPropertyValue(this,  codigo, "codigo" );
	}

	/**
	 * Set the unique identifier of this class
	 * @param codigo the new ID
	 */
	public void setCodigo (java.lang.Long codigo) {
		this.codigo = codigo;
		this.hashCode = Integer.MIN_VALUE;
	}




	/**
	 * Return the value associated with the column: nr_vaga_dia
	 */
	public java.lang.Long getNumeroVagaDia () {
		return getPropertyValue(this, numeroVagaDia, PROP_NUMERO_VAGA_DIA); 
	}

	/**
	 * Set the value related to the column: nr_vaga_dia
	 * @param numeroVagaDia the nr_vaga_dia value
	 */
	public void setNumeroVagaDia (java.lang.Long numeroVagaDia) {
//        java.lang.Long numeroVagaDiaOld = this.numeroVagaDia;
		this.numeroVagaDia = numeroVagaDia;
//        this.getPropertyChangeSupport().firePropertyChange ("numeroVagaDia", numeroVagaDiaOld, numeroVagaDia);
	}



	/**
	 * Return the value associated with the column: ctr_atendimento
	 */
	public java.lang.Long getControleAtendimento () {
		return getPropertyValue(this, controleAtendimento, PROP_CONTROLE_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: ctr_atendimento
	 * @param controleAtendimento the ctr_atendimento value
	 */
	public void setControleAtendimento (java.lang.Long controleAtendimento) {
//        java.lang.Long controleAtendimentoOld = this.controleAtendimento;
		this.controleAtendimento = controleAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("controleAtendimento", controleAtendimentoOld, controleAtendimento);
	}



	/**
	 * Return the value associated with the column: imprime_faa
	 */
	public java.lang.Long getImprimeFaa () {
		return getPropertyValue(this, imprimeFaa, PROP_IMPRIME_FAA); 
	}

	/**
	 * Set the value related to the column: imprime_faa
	 * @param imprimeFaa the imprime_faa value
	 */
	public void setImprimeFaa (java.lang.Long imprimeFaa) {
//        java.lang.Long imprimeFaaOld = this.imprimeFaa;
		this.imprimeFaa = imprimeFaa;
//        this.getPropertyChangeSupport().firePropertyChange ("imprimeFaa", imprimeFaaOld, imprimeFaa);
	}



	/**
	 * Return the value associated with the column: visivel
	 */
	public java.lang.String getVisivel () {
		return getPropertyValue(this, visivel, PROP_VISIVEL); 
	}

	/**
	 * Set the value related to the column: visivel
	 * @param visivel the visivel value
	 */
	public void setVisivel (java.lang.String visivel) {
//        java.lang.String visivelOld = this.visivel;
		this.visivel = visivel;
//        this.getPropertyChangeSupport().firePropertyChange ("visivel", visivelOld, visivel);
	}



	/**
	 * Return the value associated with the column: falta_prontuario
	 */
	public java.lang.Long getFaltaProntuario () {
		return getPropertyValue(this, faltaProntuario, PROP_FALTA_PRONTUARIO); 
	}

	/**
	 * Set the value related to the column: falta_prontuario
	 * @param faltaProntuario the falta_prontuario value
	 */
	public void setFaltaProntuario (java.lang.Long faltaProntuario) {
//        java.lang.Long faltaProntuarioOld = this.faltaProntuario;
		this.faltaProntuario = faltaProntuario;
//        this.getPropertyChangeSupport().firePropertyChange ("faltaProntuario", faltaProntuarioOld, faltaProntuario);
	}



	/**
	 * Return the value associated with the column: empresa
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresa () {
		return getPropertyValue(this, empresa, PROP_EMPRESA); 
	}

	/**
	 * Set the value related to the column: empresa
	 * @param empresa the empresa value
	 */
	public void setEmpresa (br.com.ksisolucoes.vo.basico.Empresa empresa) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaOld = this.empresa;
		this.empresa = empresa;
//        this.getPropertyChangeSupport().firePropertyChange ("empresa", empresaOld, empresa);
	}



	/**
	 * Return the value associated with the column: cd_nat_proc_tp_atendimento
	 */
	public br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento getNaturezaProcuraTipoAtendimento () {
		return getPropertyValue(this, naturezaProcuraTipoAtendimento, PROP_NATUREZA_PROCURA_TIPO_ATENDIMENTO); 
	}

	/**
	 * Set the value related to the column: cd_nat_proc_tp_atendimento
	 * @param naturezaProcuraTipoAtendimento the cd_nat_proc_tp_atendimento value
	 */
	public void setNaturezaProcuraTipoAtendimento (br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimento) {
//        br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcuraTipoAtendimento naturezaProcuraTipoAtendimentoOld = this.naturezaProcuraTipoAtendimento;
		this.naturezaProcuraTipoAtendimento = naturezaProcuraTipoAtendimento;
//        this.getPropertyChangeSupport().firePropertyChange ("naturezaProcuraTipoAtendimento", naturezaProcuraTipoAtendimentoOld, naturezaProcuraTipoAtendimento);
	}



	/**
	 * Return the value associated with the column: cd_painel
	 */
	public br.com.ksisolucoes.vo.atendimento.painel.Painel getPainel () {
		return getPropertyValue(this, painel, PROP_PAINEL); 
	}

	/**
	 * Set the value related to the column: cd_painel
	 * @param painel the cd_painel value
	 */
	public void setPainel (br.com.ksisolucoes.vo.atendimento.painel.Painel painel) {
//        br.com.ksisolucoes.vo.atendimento.painel.Painel painelOld = this.painel;
		this.painel = painel;
//        this.getPropertyChangeSupport().firePropertyChange ("painel", painelOld, painel);
	}



	/**
	 * Return the value associated with the column: empresa_dispensacao
	 */
	public br.com.ksisolucoes.vo.basico.Empresa getEmpresaDispensacao () {
		return getPropertyValue(this, empresaDispensacao, PROP_EMPRESA_DISPENSACAO); 
	}

	/**
	 * Set the value related to the column: empresa_dispensacao
	 * @param empresaDispensacao the empresa_dispensacao value
	 */
	public void setEmpresaDispensacao (br.com.ksisolucoes.vo.basico.Empresa empresaDispensacao) {
//        br.com.ksisolucoes.vo.basico.Empresa empresaDispensacaoOld = this.empresaDispensacao;
		this.empresaDispensacao = empresaDispensacao;
//        this.getPropertyChangeSupport().firePropertyChange ("empresaDispensacao", empresaDispensacaoOld, empresaDispensacao);
	}




	public boolean equals (Object obj) {
		if (null == obj) return false;
		if (!(obj instanceof br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento)) return false;
		else {
			br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento empresaNaturezaProcuraTipoAtendimento = (br.com.ksisolucoes.vo.prontuario.basico.EmpresaNaturezaProcuraTipoAtendimento) obj;
			if (null == this.getCodigo() || null == empresaNaturezaProcuraTipoAtendimento.getCodigo()) return false;
			else return (this.getCodigo().equals(empresaNaturezaProcuraTipoAtendimento.getCodigo()));
		}
	}

	public int hashCode () {
		if (Integer.MIN_VALUE == this.hashCode) {
			if (null == this.getCodigo()) return super.hashCode();
			else {
				String hashStr = this.getClass().getName() + ":" + this.getCodigo().hashCode();
				this.hashCode = hashStr.hashCode();
			}
		}
		return this.hashCode;
	}


	public String toString () {
		return super.toString();
	}

	private RetornoValidacao retornoValidacao;

    public RetornoValidacao getRetornoValidacao() {
		if( this.retornoValidacao == null ) {
			this.retornoValidacao = new RetornoValidacao();
		}
        return this.retornoValidacao;
    }
 
    public void setRetornoValidacao(RetornoValidacao retornoValidacao) {
		this.retornoValidacao = retornoValidacao;
    }

//    private java.beans.PropertyChangeSupport propertyChangeSupport;
//
//  protected java.beans.PropertyChangeSupport getPropertyChangeSupport() {
//        if( this.propertyChangeSupport == null ) {
//            this.propertyChangeSupport =  new java.beans.PropertyChangeSupport(this);
//        }
//        return this.propertyChangeSupport;
//    }
//
//    public void addPropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().addPropertyChangeListener(l);
//    }
//
//    public void addPropertyChangeListener(String propertyName, java.beans.PropertyChangeListener listener) {
//		this.getPropertyChangeSupport().addPropertyChangeListener(propertyName, listener);
//    }
//
//    public void removePropertyChangeListener(java.beans.PropertyChangeListener l) {
//        this.getPropertyChangeSupport().removePropertyChangeListener(l);
//    }
}