package br.com.ksisolucoes.vo.geral;

import br.com.ksisolucoes.vo.geral.base.BaseEstruturaEquipamentoPK;
import br.com.ksisolucoes.vo.geral.interfaces.EstruturaEquipamentoPKInterface;

public class EstruturaEquipamentoPK extends BaseEstruturaEquipamentoPK implements EstruturaEquipamentoPKInterface{
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public EstruturaEquipamentoPK () {}
	
	public EstruturaEquipamentoPK (
		br.com.ksisolucoes.vo.entradas.estoque.Produto produto,
		br.com.ksisolucoes.vo.entradas.estoque.Produto componente,
		java.lang.Long item) {

		super (
			produto,
			componente,
			item);
	}
/*[CONSTRUCTOR MARKER END]*/


}