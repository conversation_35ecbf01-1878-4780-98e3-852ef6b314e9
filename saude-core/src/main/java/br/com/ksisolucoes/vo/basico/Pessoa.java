package br.com.ksisolucoes.vo.basico;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.StringUtilKsi;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.base.BasePessoa;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.interfaces.NaoValidaFaixaEmpresaInterface;
import br.com.ksisolucoes.vo.interfaces.PesquisaObjectInterface;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.swing.text.MaskFormatter;
import java.io.Serializable;
import java.text.ParseException;

/**
 * Esta classe est relacionado com a tabela pessoa. Classe dimensionada para
 * customizaes.
 *
 */
public class Pessoa extends BasePessoa implements PesquisaObjectInterface, CodigoManager, NaoValidaFaixaEmpresaInterface {
    
    private static final long serialVersionUID = 1L;
    
    public static final String PROP_ENDERECO_PRINCIPAL = "enderecoPrincipal";
    
    public static final String PROP_ENDERECO_ENTREGA = "enderecoEntrega";
    
    public static final String PROP_ENDERECO_COBRANCA = "enderecoCobranca";
    
    public static final String PROP_DESCRICAO_FORMATADO = "descricaoFormatado";
    
    public static final String PROP_DESCRICAO_SITUACAO = "descricaoSituacao";
    
    public static final String PROP_CNPJ_CPF_FORMATADO = "cnpjCpfFormatado";
    
    /*
     *  FLAG
     * -----
     * Referente ao tipo de processo executado na hora de salvar uma pessoa
     *---------------------------------------------------------------------*/
    /**
     * Prodiedade utilizada pelo controlador do BO do Save da pessoa. <br>
     *  Esta informa o detalhamento que se deve salvar a pessoa e seus relacionamentos. <br>
     *  Valor: {@value}
     */
    public static final String CADASTRO_COMPLETO = "C";
    
    /**
     * Prodiedade utilizada pelo controlador do BO do Save da pessoa. <br>
     *  Esta informa o detalhamento que se deve salvar a pessoa e seus relacionamentos. <br>
     *  Valor: {@value}
     */
    public static final String CADASTRO_EXPORTACAO = "E";
    /*--------------------------------------------------------------------*/
    
    /**
     * Utilizado nas flags de exportao
     */
    public static final String IS_CLIENTE = "S";
    
    /*
     * FLAG
     * ----
     * Referentes a property interno/externo
     *---------------------------------------------------------------------*/
    /**
     * Representa um cliente Externo. <br>
     *  Valor: {@value}
     */
    public static final String IS_EXTERNO = "E";
    /**
     * Representa um cliente Interno. <br>
     *  Valor: {@value}
     */
    public static final String IS_INTERNO = "I";
    /**
     * Representa um cliente Interno e Externo. <br>
     *  Valor: {@value}
     */
    public static final String IS_INTERNO_EXTERNO = "A";
    /*--------------------------------------------------------------------*/
    
    /*
     * FLAG
     * ----
     * Referente ao tipo de pessoa cadastrada
     *---------------------------------------------------------------------*/
    /**
     *  Valor: {@value}
     */
    public static final String NOT_CLIENTE = "N";
    /**
     *  Valor: {@value}
     */
    public static final String IS_REPRESENTANTE = "S";
    /**
     *  Valor: {@value}
     */
    public static final String NOT_REPRESENTANTE = "N";
    /**
     *  Valor: {@value}
     */
    public static final String IS_FORNECEDOR = "S";
    /**
     *  Valor: {@value}
     */
    public static final String NOT_FORNECEDOR = "N";
    /**
     *  Valor: {@value}
     */
    public static final String IS_FUNCIONARIO = "S";
    /**
     *  Valor: {@value}
     */
    public static final String NOT_FUNCIONARIO = "N";
    /**
     *  Valor: {@value}
     */
    public static final String PESSOA_REPRESENTANTE = "R";
    /**
     *  Valor: {@value}
     */
    public static final String PESSOA_CLIENTE = "C";
    /**
     *  Valor: {@value}
     */
    public static final String PESSOA_FORNECEDOR = "F";
    /**
     *  Valor: {@value}
     */
    public static final String PESSOA_FUNCIONARIO = "U";
    /*--------------------------------------------------------------------*/
    
    /*
     * FLAG
     * ----
     * Relatorios
     *---------------------------------------------------------------------*/
    /**
     * Utilizado nos relatorios. <br>
     * Valor: {@value}
     */
    public static final String PESSOA_TODOS = "T";
    /*--------------------------------------------------------------------*/
    
    /*
     * FLAG
     * ----
     * Tipo de pessoa.
     *---------------------------------------------------------------------*/
    /**
     * Valor: {@value}
     */
    public static final String PESSOA_FISICA = "F";
    /**
     * Valor: {@value}
     */
    public static final String PESSOA_JURIDICA = "J";
    /**
     * Valor: {@value}
     */
    public static final String PESSOA_EXPORTACAO = "E";
    /*--------------------------------------------------------------------*/
    
    /*
     * FLAG
     * ----
     * Referente a situao da pessoa
     *---------------------------------------------------------------------*/
    /**
     * Valor: {@value}
     */
    public static final String ATIVA = "A";
    /**
     * Valor: {@value}
     */
    public static final String EXCLUIDA = "E";
    /**
     * Valor: {@value}
     */
    public static final String INATIVA = "I";
    /*--------------------------------------------------------------------*/
    
    /*
     * FLAG
     * ----
     * Referente a situao da pessoa
     *---------------------------------------------------------------------*/
    /**
     * Valor: {@value}
     */
    public static final String SOLTEIRO = "S";
    /**
     * Valor: {@value}
     */
    public static final String CASADO = "C";
    /**
     * Valor: {@value}
     */
    public static final String VIUVO = "V";
    /**
     * Valor: {@value}
     */
    public static final String AMASIADO = "A";
    /**
     * Valor: {@value}
     */
    public static final String OUTROS = "O";
    /*--------------------------------------------------------------------*/
    
    //Variavel para saber se  para validar CPF/CNPJ
    private Boolean validarCpfCnpj = Boolean.FALSE;
    
    //Fileds de referencia multipla (Set)
    
    private PessoaEndereco enderecoPrincipal;
    
    private PessoaEndereco enderecoEntrega;
    
    private PessoaEndereco enderecoCobranca;
    
    private Usuario usuario;
    
    /* [CONSTRUCTOR MARKER BEGIN] */
	public Pessoa () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public Pessoa (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public Pessoa (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		br.com.ksisolucoes.vo.basico.Pessoa representante,
		br.com.ksisolucoes.vo.basico.TipoPessoa tipoPessoa,
		java.lang.String descricao,
		java.lang.String descricaoOriginal,
		java.lang.String flag,
		java.lang.String flagPessoaFisicaJuridica,
		java.util.Date dataCadastro,
		java.util.Date dataAlteracaoUsuario) {

		super (
			codigo,
			usuario,
			representante,
			tipoPessoa,
			descricao,
			descricaoOriginal,
			flag,
			flagPessoaFisicaJuridica,
			dataCadastro,
			dataAlteracaoUsuario);
	}

    /* [CONSTRUCTOR MARKER END] */
    
    /*
     * (non-Javadoc)
     *
     * @see br.com.ksisolucoes.vo.basico.base.BasePessoa#setCnpjCpf(java.lang.String)
     */
    public void setCnpjCpf(String cnpjCpf) {
        //if(ValidacaoAtributo.cpf(cnpjCpf))"
        super.setCnpjCpf(cnpjCpf);
        //else
        //TODO Validar CPF
        //retornoValidacao.add(this.PROP_CNPJ_CPF, "Cpf foi preenchido
        // incorretamente.");
    }
    
    public boolean isPessoaFisica() {
        return Pessoa.PESSOA_FISICA.equalsIgnoreCase(this
                .getFlagPessoaFisicaJuridica());
    }
    
    public boolean isPessoaJuridica() {
        return Pessoa.PESSOA_JURIDICA.equalsIgnoreCase(this
                .getFlagPessoaFisicaJuridica());
    }
    
    public boolean isPessoaExportacao() {
        return Pessoa.PESSOA_EXPORTACAO.equalsIgnoreCase(this
                .getFlagPessoaFisicaJuridica());
    }
    
    public boolean isAtiva() {
        if (Pessoa.ATIVA.equalsIgnoreCase(this.getFlag())) {
            return true;
        }
        
        return false;
    }
    
    public boolean isInativa() {
        if (Pessoa.INATIVA.equalsIgnoreCase(this.getFlag())) {
            return true;
        }
        
        return false;
    }
    
    public boolean isExcluida() {
        if (Pessoa.EXCLUIDA.equalsIgnoreCase(this.getFlag())) {
            return true;
        }
        
        return false;
    }
    
    public String getDescricaoVO() {
        return this.getDescricao();
    }
    
    public String getIdentificador() {
        return this.getCodigo().toString();
    }
    
    /*
     * (non-Javadoc)
     *
     * @see br.com.ksisolucoes.vo.interfaces.CodigoManager#getCodigoManager()
     */
    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    /*
     * (non-Javadoc)
     *
     * @see br.com.ksisolucoes.vo.interfaces.CodigoManager#setCodigoManager(java.lang.Long)
     */
    public void setCodigoManager(Serializable key) {
        this.setCodigo((Long) key);
    }
    
    public boolean isEnderecoPrincipalNull() {
        return this.enderecoPrincipal == null;
    }
    
    public PessoaEndereco getEnderecoPrincipal() {
        return this.enderecoPrincipal;
    }
    
    public void setEnderecoPrincipal(PessoaEndereco enderecoPrincipal) {
        this.enderecoPrincipal = enderecoPrincipal;
    }
    
    public boolean isEnderecoEntregaNull() {
        return this.enderecoEntrega == null;
    }
    
    public PessoaEndereco getEnderecoEntrega() {
        return this.enderecoEntrega;
    }
    
    public void setEnderecoEntrega(PessoaEndereco enderecoEntrega) {
        this.enderecoEntrega = enderecoEntrega;
    }
    
    public boolean isEnderecoCobrancaNull() {
        return this.enderecoCobranca == null;
    }
    
    public PessoaEndereco getEnderecoCobranca() {
        return this.enderecoCobranca;
    }
    
    public void setEnderecoCobranca(PessoaEndereco enderecoCobranca) {
        this.enderecoCobranca = enderecoCobranca;
    }
    
    /**
     * Retorna um <code>boolean</code> para quando o estado civil exige um cnjuge.
     * @return <code>true</code> para estados civis os quais necessitam de cnjuge
     * @param estadoCivil Estado Civil a ser consultado
     */
    public static boolean hasConjuge( String estadoCivil ){
        if ( estadoCivil.equals( Pessoa.SOLTEIRO ) ||
                estadoCivil.equals( Pessoa.OUTROS ) ){
            return false;
        } else{
            return true;
        }
    }
    
    public Boolean isValidarCpfCnpj() {
        return validarCpfCnpj;
    }
    
    public void setValidarCpfCnpj(Boolean validar) {
        this.validarCpfCnpj = validar;
    }
    
    /**
     * Metodo estatico que retorna a descricao da flag pessoa (Representante, Cliente, Fornecedor ou Funcionario)
     * @return String
     */
    
    public static String getFlagPessoaDescricao(String PESSOA) {
        String pessoa;
        
        if(PESSOA == Pessoa.PESSOA_REPRESENTANTE) {
            pessoa = Bundle.getStringApplication("rotulo_representante");
        } else if(PESSOA == Pessoa.PESSOA_CLIENTE) {
            pessoa = Bundle.getStringApplication("rotulo_cliente");
        } else if(PESSOA == Pessoa.PESSOA_FORNECEDOR) {
            pessoa = Bundle.getStringApplication("rotulo_fornecedor");
        } else if(PESSOA == Pessoa.PESSOA_FUNCIONARIO) {
            pessoa = Bundle.getStringApplication("rotulo_funcionario");
        } else {
            pessoa = null;
            Loggable.log.error("Pessoa inexistente!");
        }
        
        return pessoa;
    }
    
    /**
     * Metodo estatico que retorna a descricao da flag (Fisica, Juridica ou Exportacao)
     * @return String
     */
    
    public static String getFlagPessoaFisicaJuridicaDescricao(String PESSOA) {
        if (PESSOA == null) return null;

        String pessoa = "";

        if(Pessoa.PESSOA_FISICA.equals(PESSOA)) {
            pessoa = Bundle.getStringApplication("rotulo_fisica");
        } else if(Pessoa.PESSOA_JURIDICA.equals(PESSOA)) {
            pessoa = Bundle.getStringApplication("rotulo_juridica");
        } else if(Pessoa.PESSOA_EXPORTACAO.equals(PESSOA)) {
            pessoa = Bundle.getStringApplication("rotulo_exportacao");
        }
        return pessoa;
    }
    
    /**
     * Metodo que retorna as descricoes da flag pessoa separada por virgula (Representante, Cliente, Fornecedor ou Funcionario)
     * @return String
     */
    @JsonIgnore
    public String getFlagPessoaTodasDescricao() {
        return Pessoa.getFlagPessoaTodasDescricao(this.getFlagRepresentante(), this.getFlagCliente(), this.getFlagFornecedor(), this.getFlagFuncionario());
    }

    /**
     * Metodo estatico que retorna as descricoes da flag pessoa separada por virgula (Representante, Cliente, Fornecedor ou Funcionario)
     * @return String
     */
    @JsonIgnore
    public static String getFlagPessoaTodasDescricao(String flagRepresentante, String flagCliente,
            String flagFornecedor, String flagFuncionario) {
        StringBuffer pessoa = new StringBuffer();

        if(Pessoa.IS_REPRESENTANTE.equals(flagRepresentante)) {
            pessoa.append(Bundle.getStringApplication("rotulo_representante"));
        }
        if(Pessoa.IS_CLIENTE.equals(flagCliente)) {
            if(pessoa.length() > 0) {
                pessoa.append(", ");
            }
            pessoa.append(Bundle.getStringApplication("rotulo_cliente"));
        }
        if(Pessoa.IS_FORNECEDOR.equals(flagFornecedor)) {
            if(pessoa.length() > 0) {
                pessoa.append(", ");
            }
            pessoa.append(Bundle.getStringApplication("rotulo_fornecedor"));
        }
        if(Pessoa.IS_FUNCIONARIO.equals(flagFuncionario)) {
            if(pessoa.length() > 0) {
                pessoa.append(", ");
            }
            pessoa.append(Bundle.getStringApplication("rotulo_funcionario"));
        }

        return pessoa.toString();
    }

    /**
     * Retorna a descricao da pessoa formatada no seguinte formato:<br>
     * ( <codigo> ) <descricao>
     * @return <code>String</code> - se algum argumento for null ser retornado null
     */
    public String getDescricaoFormatado(){
        return Util.getDescricaoFormatado( this.getCodigo(), this.getDescricao() );
    }
    public static String getDescricaoFormatado(Long codigo, String descricao){
        return Util.getDescricaoFormatado( codigo, descricao );
    }
    
    @Override
    public String toString() {
        return this.getCodigo() + " - " + this.getDescricao();
    }
    
    public String getDescricaoSituacao(){
        if (ATIVA.equals(getFlag())) {
            return Bundle.getStringApplication( "rotulo_ativo");
        } else if (INATIVA.equals(getFlag())){
            return Bundle.getStringApplication("rotulo_inativo");
        } else if (EXCLUIDA.equals(getFlag())) {
            return Bundle.getStringApplication("rotulo_excluido");
        } else {
            return "";
        }
    }
    
    public String getCnpjCpfFormatado(){
        if (getCnpjCpf() != null && !getCnpjCpf().trim().equals("")) {
            String cpfCnpj = getCnpjCpf();
            cpfCnpj = StringUtilKsi.getDigits(Coalesce.asString(cpfCnpj));
            if (cpfCnpj.trim().length() > 12) {
                try {
                    MaskFormatter m = new MaskFormatter("##.###.###/####-##");
                    m.setValueContainsLiteralCharacters(false);
                    return m.valueToString(cpfCnpj);
                } catch (ParseException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            } else {
                try {
                    MaskFormatter m = new MaskFormatter("###.###.###-##");
                    m.setValueContainsLiteralCharacters(false);
                    return m.valueToString(cpfCnpj);
                } catch (ParseException ex) {
                    Loggable.log.error(ex.getMessage(), ex);
                }
            }
        }
        return "";
    }
}
