<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="EloGrupoAtendimentoCboTipoDocAtend" table="elo_grupo_atendimento_cbo_tipo_doc_atend" >
         
        <id
            name="codigo"
            column="cd_elo_grupo_atendimento_cbo_tipo_doc_atend"
            type="java.lang.Long"
        /> <version column="version" name="version" type="long" />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.grupos.GrupoAtendimentoCbo"
            column="cd_grupo_atend_cbo"
            name="grupoAtendimentoCbo"
            not-null="true"
        />

        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento"
            column="cd_tip_doc"
            name="tipoDocumentoAtendimento"
            not-null="true"
        />

    </class>
</hibernate-mapping>
