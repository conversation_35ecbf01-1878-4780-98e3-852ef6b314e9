package br.com.ksisolucoes.vo.atendimento.raas;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import java.io.Serializable;
import java.text.DecimalFormat;

import br.com.ksisolucoes.vo.atendimento.raas.base.BaseRaasPsi;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;



public class RaasPsi extends BaseRaasPsi implements CodigoManager {
    private static final long serialVersionUID = 1L;
    
    public static final String PROP_ORIGEM_INFORMACOES_RAS = "RAS";
    public static final String PROP_ORIGEM_INFORMACOES_EXT = "EXT";
    
    public static final String PROP_TIPO_DROGA_ALCOOL = "A";
    public static final String PROP_TIPO_DROGA_CRACK = "C";
    public static final String PROP_TIPO_DROGA_OUTROS = "O";
        
    public enum Raca implements IEnum {
        BRANCA(1L, Bundle.getStringApplication("rotulo_branca")),
        PRETA(2L, Bundle.getStringApplication("rotulo_preta")),
        PARDA(3L, Bundle.getStringApplication("rotulo_parda")),
        AMARELA(4L, Bundle.getStringApplication("rotulo_amarela")),
        INDIGENA(5L, Bundle.getStringApplication("rotulo_indigena")),
        SEM_INFORMACAO(99L, Bundle.getStringApplication("rotulo_sem_informacao")),
        ;

        private Long value;
        private String descricao;

        private Raca(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static Raca valeuOf(Long value) {
            for (Raca raca : Raca.values()) {
                if (raca.value().equals(value)) {
                    return raca;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
        
    public enum CaraterAtendimento implements IEnum {
        ELETIVO(1L, Bundle.getStringApplication("rotulo_eletivo")),
        URGENCIA(2L, Bundle.getStringApplication("rotulo_urgencia")),
        ACIDENTE_TRABALHO(3L, Bundle.getStringApplication("rotulo_acidente_local_trabalho_ou_servico_empresa")),
        ACIDENTE_TRAJETO_TRABALHO(4L, Bundle.getStringApplication("rotulo_acidente_trajeto_para_trabalho")),
        OUTROS_TIPOS_ACIDENTE_TRANSITO(5L, Bundle.getStringApplication("rotulo_outros_tipos_acidente_transito")),
        OUTROS_TIPOS_LESOES_AGENTES_QUIMICOS_FISICOS(6L, Bundle.getStringApplication("rotulo_outros_tipos_lesoes_envenenamentos_agentes_quimicos_fisicos")),
        ;

        private Long value;
        private String descricao;

        private CaraterAtendimento(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static CaraterAtendimento valeuOf(Long value) {
            for (CaraterAtendimento caraterAtendimento : CaraterAtendimento.values()) {
                if (caraterAtendimento.value().equals(value)) {
                    return caraterAtendimento;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
        
    public enum OrigemPaciente implements IEnum {
        SERVICO_URGENCIA(1L, Bundle.getStringApplication("rotulo_servico_urgencia")),
        ATENCAO_BASICA(2L, Bundle.getStringApplication("rotulo_atencao_basica")),
        INTERNACAO_HOSPITALAR(3L, Bundle.getStringApplication("rotulo_internacao_hospitalar")),
        CENTRO_ONCOLOGICO(4L, Bundle.getStringApplication("rotulo_centro_oncologico")),
        OUTROS(5L, Bundle.getStringApplication("rotulo_outros")),
        ;

        private Long value;
        private String descricao;

        private OrigemPaciente(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static OrigemPaciente valeuOf(Long value) {
            for (OrigemPaciente origemPaciente : OrigemPaciente.values()) {
                if (origemPaciente.value().equals(value)) {
                    return origemPaciente;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }
        
    public enum DestinoPaciente implements IEnum {
        PERMANENCIA(0L, Bundle.getStringApplication("rotulo_permanencia")),
        ALTA_CLINICA(1L, Bundle.getStringApplication("rotulo_alta_clinica")),
        ENCAMINHAMENTO_AD1(2L, Bundle.getStringApplication("rotulo_encaminhamento_ad1")),
        ALTA_ADMINISTRATIVA(3L, Bundle.getStringApplication("rotulo_alta_administrativa")),
        INTERNACAO_URGENCIA(4L, Bundle.getStringApplication("rotulo_internacao_urgencia")),
        INTERNACAO_HOSPITALAR(5L, Bundle.getStringApplication("rotulo_internacao_hospitalar")),
        OBITO(6L, Bundle.getStringApplication("rotulo_obito")),
        ;

        private Long value;
        private String descricao;

        private DestinoPaciente(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public static DestinoPaciente valeuOf(Long value) {
            for (DestinoPaciente destinoPaciente : DestinoPaciente.values()) {
                if (destinoPaciente.value().equals(value)) {
                    return destinoPaciente;
                }
            }
            return null;
        }

        @Override
        public Long value() {
            return value;
        }

        @Override
        public String descricao() {
            return descricao;
        }

    }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public RaasPsi () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public RaasPsi (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public RaasPsi (
		java.lang.Long codigo,
		br.com.ksisolucoes.vo.atendimento.raas.Raas raas,
		br.com.ksisolucoes.vo.controle.Usuario usuario,
		java.lang.Long linha,
		java.util.Date dataCadastro) {

		super (
			codigo,
			raas,
			usuario,
			linha,
			dataCadastro);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoRaca(){
        Raca raca = Raca.valeuOf(getRaca());
        if (raca != null && raca.descricao != null) {
            return raca.descricao();
        }
        return "";
    }
    
    public String getDescricaoCaraterAtendimento(){
        CaraterAtendimento caraterAtendimento = CaraterAtendimento.valeuOf(getCaraterAtendimento());
        if (caraterAtendimento != null && caraterAtendimento.descricao != null) {
            return caraterAtendimento.descricao();
        }
        return "";
    }
    
    public String getDescricaoOrigemPaciente(){
        OrigemPaciente origemPaciente = OrigemPaciente.valeuOf(getOrigemPaciente());
        if (origemPaciente != null && origemPaciente.descricao != null) {
            return origemPaciente.descricao();
        }
        return "";
    }
    
    public String getDescricaoDestinoPaciente(){
        DestinoPaciente destinoPaciente = DestinoPaciente.valeuOf(getDestinoPaciente());
        if (destinoPaciente != null && destinoPaciente.descricao != null) {
            return destinoPaciente.descricao();
        }
        return "";
    }

    public String getCodigoCoberturaEsfFormatado() {
        if (getCodigoCoberturaEsf() != null) {
            return new DecimalFormat("0000000").format(getCodigoCoberturaEsf());
        }
        return null;
    }
}