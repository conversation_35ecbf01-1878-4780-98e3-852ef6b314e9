package br.com.ksisolucoes.vo.prontuario.basico;

import java.io.Serializable;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;
import br.com.ksisolucoes.vo.prontuario.basico.base.BaseFormularioTabagismo;



public class FormularioTabagismo extends BaseFormularioTabagismo implements CodigoManager {
	private static final long serialVersionUID = 1L;
        
        public static final String PROP_DESCRICAO_BEBIDAS_ALCOOLICAS = "descricaoBebidasAlcoolicas";
        public static final String PROP_DESCRICAO_MESES_GRAVIDEZ = "descricaoMesesGravidez";
        public static final String PROP_DESCRICAO_PACIENTE_CONSULTA = "descricaoPacienteConsulta";
        public static final String PROP_DESCRICAO_TEMPO_PRIMEIRO_CIGARRO = "descricaoTempoPrimeiroCigarro";
        public static final String PROP_DESCRICAO_CIGARRO_SATISFACAO = "descricaoCigarroSatisfacao";
        public static final String PROP_DESCRICAO_CIGARRO_DIA = "descricaoCigarroDia";
        public static final String PROP_DESCRICAO_GRAU_MOTIVACAO = "descricaoGrauMotivacao";
        public static final String PROP_DESCRICAO_GRAU_DEPENDENCIA = "descricaoGrauDependencia";
        public static final String PROP_DESCRICAO_DEIXAR_DE_FUMAR = "descricaoDeixarDeFumar";
        
        public enum BebidasAlcoolicas implements IEnum {
            NUNCA(1L, Bundle.getStringApplication("rotulo_nunca")),
            TODOS_DIAS(2L, Bundle.getStringApplication("rotulo_todos_dias")),
            FINAIS_SEMANA(3L, Bundle.getStringApplication("rotulo_finais_semana")),
            RARAMENTE(4L, Bundle.getStringApplication("rotulo_raramente"))
            ;

            private Long value;
            private String descricao;

            private BebidasAlcoolicas(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static BebidasAlcoolicas valeuOf(Long value) {
                for (BebidasAlcoolicas bebidasAlcoolicas : BebidasAlcoolicas.values()) {
                    if (bebidasAlcoolicas.value().equals(value)) {
                        return bebidasAlcoolicas;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum MesesGravidez implements IEnum {
            UM(1L, Bundle.getStringApplication("rotulo_descricao_um")),
            DOIS(2L, Bundle.getStringApplication("rotulo_descricao_dois")),
            TRES(3L, Bundle.getStringApplication("rotulo_descricao_tres")),
            QUATRO(4L, Bundle.getStringApplication("rotulo_descricao_quatro")),
            CINCO(5L, Bundle.getStringApplication("rotulo_descricao_cinco")),
            SEIS(6L, Bundle.getStringApplication("rotulo_descricao_seis")),
            SETE(7L, Bundle.getStringApplication("rotulo_descricao_sete")),
            OITO(8L, Bundle.getStringApplication("rotulo_descricao_oito")),
            NOVE(9L, Bundle.getStringApplication("rotulo_descricao_nove")),
            ;

            private Long value;
            private String descricao;

            private MesesGravidez(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static MesesGravidez valeuOf(Long value) {
                for (MesesGravidez mesesGravidez : MesesGravidez.values()) {
                    if (mesesGravidez.value().equals(value)) {
                        return mesesGravidez;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum PacienteConsulta implements IEnum {
            AGITACAO(1L, Bundle.getStringApplication("rotulo_agitacao")),
            FALTA_CONCENTRACAO(2L, Bundle.getStringApplication("rotulo_falta_concentracao")),
            PENSAMENTO_FALA_LENTIFICAOS_ACELERADOS(3L, Bundle.getStringApplication("rotulo_pensamento_fala_lentificados_acelerados")),
            NENHUMA_ALTERACAO(4L, Bundle.getStringApplication("rotulo_nenhuma_alteracao"))
            ;

            private Long value;
            private String descricao;

            private PacienteConsulta(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static PacienteConsulta valeuOf(Long value) {
                for (PacienteConsulta pacienteConsulta : PacienteConsulta.values()) {
                    if (pacienteConsulta.value().equals(value)) {
                        return pacienteConsulta;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum TempoPrimeiroCigarro implements IEnum {
            CINCO_MINUTOS(3L, Bundle.getStringApplication("rotulo_dentro_cinco_minutos")),
            ENTRE_SEIS_E_TRINTA_MINUTOS(2L, Bundle.getStringApplication("rotulo_entre_seis_e_trinta_minutos")),
            ENTRE_TRINTA_UM_E_SESSENTA_MINUTOS(1L, Bundle.getStringApplication("rotulo_entre_trinta_um_e_sessenta_minutos")),
            APOS_SESSENTA_MINUTOS(0L, Bundle.getStringApplication("rotulo_apos_sessenta_minutos"))
            ;

            private Long value;
            private String descricao;

            private TempoPrimeiroCigarro(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static TempoPrimeiroCigarro valeuOf(Long value) {
                for (TempoPrimeiroCigarro tempoPrimeiroCigarro : TempoPrimeiroCigarro.values()) {
                    if (tempoPrimeiroCigarro.value().equals(value)) {
                        return tempoPrimeiroCigarro;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum CigarroSatisfacao implements IEnum {
            PRIMEIRO_MANHA(1L, Bundle.getStringApplication("rotulo_primeiro_manha")),
            OUTROS(0L, Bundle.getStringApplication("rotulo_outros"))
            ;

            private Long value;
            private String descricao;

            private CigarroSatisfacao(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static CigarroSatisfacao valeuOf(Long value) {
                for (CigarroSatisfacao cigarroSatisfacao : CigarroSatisfacao.values()) {
                    if (cigarroSatisfacao.value().equals(value)) {
                        return cigarroSatisfacao;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum CigarroDia implements IEnum {
            MENOS_DE_DEZ(0L, Bundle.getStringApplication("rotulo_menos_de_dez")),
            DE_ONZE_A_VINTE(1L, Bundle.getStringApplication("rotulo_de_onze_a_vinte")),
            DE_VINTE_UM_A_TRINTA(2L, Bundle.getStringApplication("rotulo_de_vinte_um_a_trinta")),
            MAIS_DE_TRINTA_UM(3L, Bundle.getStringApplication("rotulo_mais_de_trinta_um"))
            ;

            private Long value;
            private String descricao;

            private CigarroDia(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static CigarroDia valeuOf(Long value) {
                for (CigarroDia cigarroDia : CigarroDia.values()) {
                    if (cigarroDia.value().equals(value)) {
                        return cigarroDia;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum GrauMotivacao implements IEnum {
            ACAO(0L, Bundle.getStringApplication("rotulo_acao")),
            CONTEMPLATIVO(1L, Bundle.getStringApplication("rotulo_contemplativo")),
            RECAIDO(2L, Bundle.getStringApplication("rotulo_recaido"))
            ;

            private Long value;
            private String descricao;

            private GrauMotivacao(Long value, String descricao) {
                this.value = value;
                this.descricao = descricao;
            }

            public static GrauMotivacao valeuOf(Long value) {
                for (GrauMotivacao grauMotivacao : GrauMotivacao.values()) {
                    if (grauMotivacao.value().equals(value)) {
                        return grauMotivacao;
                    }
                }
                return null;
            }

            @Override
            public Long value() {
                return value;
            }

            @Override
            public String descricao() {
                return descricao;
            }

        }
        
        public enum SintomasGerais{

            TRISTEZA(1L),
            PERDA_DE_INTERESSE(2L),
            ENERGIA_REDUZIDA(4L);

            private Long value;

            private SintomasGerais(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (TRISTEZA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_tristeza");
                } else if (PERDA_DE_INTERESSE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_perda_interesse_prazer");
                } else if (ENERGIA_REDUZIDA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_energia_reduzida_grande_cansaço");
                }
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum SintomasEspecificos{

            CONCENTRACAO_ATENCAO_REDUZIDAS(1L),
            SENTIMENTO_CULPA_INUTILIDADE(2L),
            IDEIAS_ATITUDES_AUTO_LESIVAS_SUICIDIO(4L),
            APETITE_DIMINUIDO(8L),
            AUTO_ESTIMA_AUTO_CONFIANCA_REDUZIDA(16L),
            PESSIMISTA_DESLOCADO(32L),
            SONO_ALTERADO(64L),
            INQUIETACAO(128L);

            private Long value;

            private SintomasEspecificos(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (CONCENTRACAO_ATENCAO_REDUZIDAS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_concentracao_atencao_reduzidas");
                } else if (SENTIMENTO_CULPA_INUTILIDADE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_sentimento_culpa_inutilidade");
                } else if (IDEIAS_ATITUDES_AUTO_LESIVAS_SUICIDIO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_ideias_atitudes_auto_lesivas_suicidio");
                } else if (APETITE_DIMINUIDO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_apetite_diminuido");
                } else if (AUTO_ESTIMA_AUTO_CONFIANCA_REDUZIDA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_auto_estima_auto_confianca_reduzida");
                } else if (PESSIMISTA_DESLOCADO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_pessimista_deslocado");
                } else if (SONO_ALTERADO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_sono_alterado");
                } else if (INQUIETACAO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_inquietacao");
                }
                
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }
        
        public enum DeixarDeFumar{

            AFETANDO_SAUDE(1L),
            OUTRA_PESSOA_PRESSIONANDO(2L),
            BEM_ESTAR_FAMILIA(4L),
            PREOCUPADO_SAUDE_FUTURO(8L),
            FILHOS_PEDEM(16L),
            NAO_GOSTO_SER_DEPENDENTE(32L),
            FUMAR_ANTI_SOCIAL(64L),
            GASTO_MUITO_DINHEIRO(128L),
            MAL_EXEMPLO_CRIANCAS(256L),
            RESTRICOES_FUMAR_AMBIENTES_FECHADOS(512L);

            private Long value;

            private DeixarDeFumar(Long value) {
                this.value = value;
            }

            public Long value(){
                return value;
            }

            @Override
            public String toString() {
                if (AFETANDO_SAUDE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_porque_afetando_minha_saude");
                } else if (OUTRA_PESSOA_PRESSIONANDO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_outras_pessoas_estao_pressionando");
                } else if (BEM_ESTAR_FAMILIA.equals(this)) {
                    return Bundle.getStringApplication("rotulo_bem_estar_familia");
                } else if (PREOCUPADO_SAUDE_FUTURO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_preocupado_saude_futuro");
                } else if (FILHOS_PEDEM.equals(this)) {
                    return Bundle.getStringApplication("rotulo_porque_filhos_pedem");
                } else if (NAO_GOSTO_SER_DEPENDENTE.equals(this)) {
                    return Bundle.getStringApplication("rotulo_porque_nao_gosto_ser_dependente");
                } else if (FUMAR_ANTI_SOCIAL.equals(this)) {
                    return Bundle.getStringApplication("rotulo_fumar_anti_social");
                } else if (GASTO_MUITO_DINHEIRO.equals(this)) {
                    return Bundle.getStringApplication("rotulo_porque_gasto_muito_dinheiro_cigarro");
                } else if (MAL_EXEMPLO_CRIANCAS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_fumar_mal_exemplo_criancas");
                } else if (RESTRICOES_FUMAR_AMBIENTES_FECHADOS.equals(this)) {
                    return Bundle.getStringApplication("rotulo_por_conta_restricoes_fumar_ambientes_fechados");
                }
                
                return Bundle.getStringApplication("rotulo_desconhecido");
            }

        }

/*[CONSTRUCTOR MARKER BEGIN]*/
	public FormularioTabagismo () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public FormularioTabagismo (java.lang.Long codigo) {
		super(codigo);
	}

	/**
	 * Constructor for required fields
	 */
	public FormularioTabagismo (
		java.lang.Long codigo,
		java.lang.Long temLesoesBoca,
		java.lang.Long temDiabete,
		java.lang.Long temHipertensao,
		java.lang.Long temProblemaCardiaco,
		java.lang.Long temProblemaEstomago,
		java.lang.Long temProblemaPulmonar,
		java.lang.Long temAlergiaRespiratoria,
		java.lang.Long temAlergiaCutanea,
		java.lang.Long temLesaoTumor,
		java.lang.Long temConvulsao,
		java.lang.Long temAnorexia,
		java.lang.Long temCriseDepressao,
		java.lang.Long fazTratamentoPsicologico,
		java.lang.Long bebidasAlcoolicas,
		java.lang.Long medicamentoEmUso,
		java.lang.Long proteseDentaria,
		java.lang.Long participouGrupo,
		java.lang.Long porqueDeixarFumar,
		java.lang.Long viveFumante,
		java.lang.Long ganharPeso,
		java.lang.Long tempoPrimeiroCigarro,
		java.lang.Long dificilFumarLugarProibido,
		java.lang.Long cigarroSatisfacao,
		java.lang.Long cigarroDia,
		java.lang.Long fumaFrequenteManha,
		java.lang.Long fumaDoente,
		java.lang.Long grauDependencia,
		java.lang.Long grauMotivacao) {

		super (
			codigo,
			temLesoesBoca,
			temDiabete,
			temHipertensao,
			temProblemaCardiaco,
			temProblemaEstomago,
			temProblemaPulmonar,
			temAlergiaRespiratoria,
			temAlergiaCutanea,
			temLesaoTumor,
			temConvulsao,
			temAnorexia,
			temCriseDepressao,
			fazTratamentoPsicologico,
			bebidasAlcoolicas,
			medicamentoEmUso,
			proteseDentaria,
			participouGrupo,
			porqueDeixarFumar,
			viveFumante,
			ganharPeso,
			tempoPrimeiroCigarro,
			dificilFumarLugarProibido,
			cigarroSatisfacao,
			cigarroDia,
			fumaFrequenteManha,
			fumaDoente,
			grauDependencia,
			grauMotivacao);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }
    
    public String getDescricaoBebidasAlcoolicas(){
        BebidasAlcoolicas bebidasAlcoolicas = BebidasAlcoolicas.valeuOf(getBebidasAlcoolicas());
        if (bebidasAlcoolicas != null && bebidasAlcoolicas.descricao != null) {
            return bebidasAlcoolicas.descricao();
        }
        return "";
    }
    
    public String getDescricaoMesesGravidez(){
        MesesGravidez mesesGravidez = MesesGravidez.valeuOf(getQuantosMesesGravidez());
        if (mesesGravidez != null && mesesGravidez.descricao != null) {
            return mesesGravidez.descricao();
        }
        return "";
    }
    
    public String getDescricaoPacienteConsulta(){
        PacienteConsulta pacienteConsulta = PacienteConsulta.valeuOf(getPacienteConsulta());
        if (pacienteConsulta != null && pacienteConsulta.descricao != null) {
            return pacienteConsulta.descricao();
        }
        return "";
    }
    
    public String getDescricaoTempoPrimeiroCigarro(){
        TempoPrimeiroCigarro tempoPrimeiroCigarro = TempoPrimeiroCigarro.valeuOf(getTempoPrimeiroCigarro());
        if (tempoPrimeiroCigarro != null && tempoPrimeiroCigarro.descricao != null) {
            return tempoPrimeiroCigarro.descricao();
        }
        return "";
    }
    
    public String getDescricaoCigarroSatisfacao(){
        CigarroSatisfacao cigarroSatisfacao = CigarroSatisfacao.valeuOf(getCigarroSatisfacao());
        if (cigarroSatisfacao != null && cigarroSatisfacao.descricao != null) {
            return cigarroSatisfacao.descricao();
        }
        return "";
    }
    
    public String getDescricaoCigarroDia(){
        CigarroDia cigarroDia = CigarroDia.valeuOf(getCigarroDia());
        if (cigarroDia != null && cigarroDia.descricao != null) {
            return cigarroDia.descricao();
        }
        return "";
    }
    
    public String getDescricaoGrauMotivacao(){
        GrauMotivacao grauMotivacao = GrauMotivacao.valeuOf(getGrauMotivacao());
        if (grauMotivacao != null && grauMotivacao.descricao != null) {
            return grauMotivacao.descricao();
        }
        return "";
    }
    
    public String getDescricaoGrauDependencia(){
        return grauDependencia(getGrauDependencia());
    }
    
    public static String getDescricaoGrauDependencia(Long grauDependencia){
        return grauDependencia(grauDependencia);
    }
    
    public static String grauDependencia(Long grauDependencia){
        if(grauDependencia > 0L && grauDependencia < 3L){
            return Bundle.getStringApplication("rotulo_muito_baixo");
        } else if(grauDependencia < 5L){
            return Bundle.getStringApplication("rotulo_baixo");
        } else if(grauDependencia < 6L){
            return Bundle.getStringApplication("rotulo_medio");
        } else if(grauDependencia < 8L){
            return Bundle.getStringApplication("rotulo_elevado");
        } else if(grauDependencia < 11L){
            return Bundle.getStringApplication("rotulo_muito_elevado");
        }
        
        return "";
    }
    
    public String getDescricaoSintomasGerais(Long sintomasGerais) {
        for (SintomasGerais sintomas : SintomasGerais.values()) {
            if (sintomas.value().equals(sintomasGerais)) {
                return sintomas.toString();
            }
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }
    
    public String getDescricaoSintomasEspecificos(Long sintomasEspecificos) {
        for (SintomasEspecificos sintomas : SintomasEspecificos.values()) {
            if (sintomas.value().equals(sintomasEspecificos)) {
                return sintomas.toString();
            }
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }
    
    public String getDescricaoDeixarDeFumar(Long deixarDeFumar) {
        for (DeixarDeFumar ddf : DeixarDeFumar.values()) {
            if (ddf.value().equals(deixarDeFumar)) {
                return ddf.toString();
            }
        }
        return Bundle.getStringApplication("rotulo_desconhecido");
    }
}