<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
"-//Hibernate/Hibernate Mapping DTD//EN"
"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >
 
<hibernate-mapping package="br.com.ksisolucoes.vo.prontuario.basico"  >
    <class name="DocumentoParecerMedico" table="documento_parecer_medico" >
         
        <id
            name="codigo"
            column="cd_doc_parecer"
            type="java.lang.Long"
        /> <version column="version" name="version" type="long" />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Atendimento"
            name="atendimento"
            column="nr_atendimento"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.DocumentoAtendimento"
            name="documentoAtendimento"
            column="cd_documento_atendimento"
            not-null="true"
        />
        
        <many-to-one 
            class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
            name="usuarioCadsus"
            column="cd_usu_cadsus"
            not-null="true"
         />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.basico.Cid"
            name="cid"
            column="cd_cid"
            not-null="true"
        />
        
        <many-to-one
            class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
            name="tabelaCbo"
            column="cd_cbo"
            not-null="false"
        />
        
        <property
            name="dataAtestado"
            column="dt_atestado"
            type="java.util.Date"
            not-null="true"
        />
        
        <property
            column="parecer_medico"
            name="parecerMedico"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            name="tipoContratacao"
            column="tp_contratacao"
            type="java.lang.String"
            length="30"
            not-null="true"
        />
        
        <property
            column="nr_dia_afastamento"
            name="diasAfastamento"
            type="java.lang.Long"
            not-null="true"
        />
        
        <property 
            name="observacao"
            column="observacao"
            type="java.lang.String"
            not-null="false"
        />
        
        <property
            column="dt_cadastro"
            name="dataCadastro"
            not-null="true"
            type="java.util.Date"
        />
        
    </class>
</hibernate-mapping>
