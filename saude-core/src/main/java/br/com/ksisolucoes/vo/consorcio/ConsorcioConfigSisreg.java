package br.com.ksisolucoes.vo.consorcio;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.consorcio.base.BaseConsorcioConfigSisreg;
import br.com.ksisolucoes.vo.hospital.ipe.HonorariosIpe;
import br.com.ksisolucoes.vo.interfaces.CodigoManager;

import java.io.Serializable;



public class ConsorcioConfigSisreg extends BaseConsorcioConfigSisreg implements CodigoManager {
	private static final long serialVersionUID = 1L;

/*[CONSTRUCTOR MARKER BEGIN]*/
	public ConsorcioConfigSisreg () {
		super();
	}

	/**
	 * Constructor for primary key
	 */
	public ConsorcioConfigSisreg (java.lang.Long codigo) {
		super(codigo);
	}

/*[CONSTRUCTOR MARKER END]*/

    public void setCodigoManager(Serializable key) {
        this.setCodigo( (java.lang.Long)key );
    }

    public Serializable getCodigoManager() {
        return this.getCodigo();
    }

	public enum TipoDados implements IEnum {
		GRUPO(1L, Bundle.getStringApplication("rotulo_grupo")),
		SUBGRUPO(2L, Bundle.getStringApplication("rotulo_sub_grupo")),
		FORMA_ORGANIZACAO(3L, Bundle.getStringApplication("rotulo_forma_organizacao"));

		private Long value;
		private String descricao;

		private TipoDados(Long value, String descricao) {
			this.value = value;
			this.descricao = descricao;
		}

		@Override
		public Long value() {
			return value;
		}

		@Override
		public String descricao() {
			return descricao;
		}

		public static HonorariosIpe.TipoMoeda valeuOf(Long value) {
			for (HonorariosIpe.TipoMoeda tipoMoeda : HonorariosIpe.TipoMoeda.values()) {
				if (tipoMoeda.value().equals(value)) {
					return tipoMoeda;
				}
			}
			return null;
		}

		@Override
		public String toString() {
			return this.descricao;
		}
	}

}