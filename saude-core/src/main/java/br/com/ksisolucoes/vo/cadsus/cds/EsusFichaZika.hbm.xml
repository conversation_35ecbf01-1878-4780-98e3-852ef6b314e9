<?xml version="1.0"?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="br.com.ksisolucoes.vo.cadsus.cds">
    <class name="EsusFichaZika" table="esus_ficha_zika">

        <id
                name="codigo"
                type="java.lang.Long"
                column="cd_esus_ficha_zika"
        >
            <generator class="assigned"/>
        </id>
        <version column="version" name="version" type="long"/>

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.UsuarioCadsus"
                column="cd_usu_cadsus"
                name="usuarioCadsus"
                not-null="false"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.cadsus.Profissional"
                column="cd_profissional"
                name="profissional"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.controle.Usuario"
                column="cd_usuario"
                name="usuario"
                not-null="true"
        />

        <property
                column="dt_cadastro"
                name="dataCadastro"
                type="java.util.Date"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.basico.Empresa"
                column="empresa"
                name="empresa"
                not-null="true"
        />

        <many-to-one
                class="br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo"
                column="cd_cbo"
                name="cbo"
                not-null="true"
        />

        <property
                column="cod_ine"
                name="codigoIne"
                type="java.lang.String"
                length="10"
                not-null="false"
        />

        <property
                column="dt_atendimento"
                name="dataAtendimento"
                type="java.util.Date"
                not-null="true"
        />

        <property
                column="turno"
                name="turno"
                type="java.lang.Long"
                not-null="true"
        />

        <property
                column="cns"
                name="cns"
                type="java.lang.Long"
                length="15"
                not-null="true"
        />

        <property
                column="dt_teste_olhinho"
                name="dataTesteOlhinho"
                type="java.util.Date"
                not-null="false"
        />

        <property
                column="teste_olhinho"
                name="testeOlhinho"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="dt_exame_fundo_olho"
                name="dataExameFundoOlho"
                type="java.util.Date"
                not-null="false"
        />

        <property
                column="exame_fundo_olho"
                name="exameFundoOlho"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="dt_teste_orelhinha"
                    name="dataTesteOrelhinha"
                type="java.util.Date"
                not-null="false"
        />

        <property
                column="teste_orelhinha"
                name="testeOrelhinha"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="dt_us_transfontanela"
                name="dataUsTransfontanela"
                type="java.util.Date"
                not-null="false"
        />

        <property
                column="us_transfontanela"
                name="usTransfontanela"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="dt_tomografia_comp"
                name="dataTomografiaComp"
                type="java.util.Date"
                not-null="false"
        />

        <property
                column="tomografia_comp"
                name="tomografiaComp"
                type="java.lang.Long"
                not-null="false"
        />

        <property
                column="dt_ressonancia_mag"
                name="dataRessonanciaMag"
                type="java.util.Date"
                not-null="false"
        />

        <property
                column="ressonancia_mag"
                name="ressonanciaMag"
                type="java.lang.Long"
                not-null="false"
        />
    </class>
</hibernate-mapping>