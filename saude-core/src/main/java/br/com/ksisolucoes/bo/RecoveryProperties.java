/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.metadata.ClassMetadata;
import org.hibernate.type.Type;

import br.com.ksisolucoes.bo.command.CommandStart;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.server.HibernateSessionFactory;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

/**
 *
 * <AUTHOR>
 */
public class RecoveryProperties extends CommandStart<RecoveryProperties> {

    private static final String DESCRICAO = "descricao";
    private static final String NOME = "nome";
    private static final String REFERENCIA = "referencia";
    private static final String SIGLA = "sigla";
    private static final String VERSION = "version";
    
    private String[] idPropertiesValues;
    private String[] descriptionPropertiesValues;
    private String[] propertiesValues;
    private String[] singleProperties;
    private String[] firstLayerProperties;
    private String[] firstLayerSingleProperties;
    
    private Class entity;

    public RecoveryProperties(Class entity) {
        this.entity = entity;
    }
    
    public RecoveryProperties start() throws DAOException, ValidacaoException {
        Session session = HibernateSessionFactory.getSession();
        
        start(session);
        
//        session.disconnect();
//        session.close();

        return (RecoveryProperties)this;
    }
    
    public void start(Session session) throws DAOException, ValidacaoException {
        recovery(session, this.entity);
    }    

    public static String[] applyAlias(String alias, String[] properties) {
        List<String> props = new ArrayList<String>();
        for (String property : properties) {
            if (alias == null || alias.trim().equals("")) {
                return properties;
            } else {
                props.add(VOUtils.montarPath(alias, property));
            }
        }
        
        return props.toArray(new String[props.size()]);
    }

    private void recovery(Session session, Class entityName) throws DAOException, ValidacaoException {
        SessionFactory sessionFactory = session.getSessionFactory();
        ClassMetadata metadata = sessionFactory.getClassMetadata(entityName);
        
        if (metadata == null) {
            throw new DAOException("Entidade " + entityName + " no esta mapeada - possvel soluo: adicionar no hbms.txt");
        }
        
        String[] properties = metadata.getPropertyNames();
        ConvertKeyToProperties keyToProperties = new ConvertKeyToProperties(entity);
        keyToProperties.start(session);
        
        idPropertiesValues = keyToProperties.getProperties().toArray(new String[keyToProperties.getProperties().size()]);
        
        List<String> propertyList = new ArrayList<String>(keyToProperties.getProperties());
        List<String> singlePropertyList = new ArrayList<String>();
        List<String> firstLayerPropertyList = new ArrayList<String>(keyToProperties.getProperties());
        List<String> firstLayerSinglePropertyList = new ArrayList<String>(keyToProperties.getProperties());
        List<String> descriptionPropertiesValuesList = new ArrayList<String>();
        
        for (String property : properties) {
            Type type = metadata.getPropertyType(property);
            
            firstLayerPropertyList.add(property);
            if (!type.isAssociationType()) {
                firstLayerSinglePropertyList.add(property);
                propertyList.add(property);
                singlePropertyList.add(property);

                if (property.equals(DESCRICAO)) {
                    descriptionPropertiesValuesList.add(VOUtils.montarPath(DESCRICAO));
                }
                if (property.equals(SIGLA)) {
                    descriptionPropertiesValuesList.add(VOUtils.montarPath(SIGLA));
                }
                if (property.equals(NOME)) {
                    descriptionPropertiesValuesList.add(VOUtils.montarPath(NOME));
                }                
                if (property.equals(REFERENCIA)) {
                    descriptionPropertiesValuesList.add(VOUtils.montarPath(REFERENCIA));
                }                
                if (property.equals(VERSION)) {
                    descriptionPropertiesValuesList.add(VOUtils.montarPath(VERSION));
                }                
                
            } else {
                Class clazz = type.getReturnedClass();
                
                ConvertKeyToProperties keyToProperties_ = new ConvertKeyToProperties(clazz);
                keyToProperties_.start(session);
                List<String> propertyList_ = new ArrayList<String>(keyToProperties_.getProperties());
                for (String propertyUnit : propertyList_) {
                    firstLayerSinglePropertyList.add(VOUtils.montarPath(property, propertyUnit));
                    propertyList.add(VOUtils.montarPath(property, propertyUnit));
                }
                
                String[] _properties = sessionFactory.getClassMetadata(clazz).getPropertyNames();
                if (_properties != null && _properties.length > 0) {
                    List<String> props = Arrays.asList(_properties);
                    
                    if (props.contains(DESCRICAO)) {
                        propertyList.add(VOUtils.montarPath(property, DESCRICAO));
                        descriptionPropertiesValuesList.add(VOUtils.montarPath(property, DESCRICAO));
                    }
                    if (props.contains(SIGLA)) {
                        propertyList.add(VOUtils.montarPath(property, SIGLA));
                        descriptionPropertiesValuesList.add(VOUtils.montarPath(property, SIGLA));
                    }
                    if (props.contains(NOME)) {
                        propertyList.add(VOUtils.montarPath(property, NOME));
                        descriptionPropertiesValuesList.add(VOUtils.montarPath(property, NOME));
                    }
                    if (props.contains(REFERENCIA)) {
                        propertyList.add(VOUtils.montarPath(property, REFERENCIA));
                        descriptionPropertiesValuesList.add(VOUtils.montarPath(property, REFERENCIA));
                    }
                    if (props.contains(VERSION)) {
                        propertyList.add(VOUtils.montarPath(property, VERSION));
                        descriptionPropertiesValuesList.add(VOUtils.montarPath(property, VERSION));
                    }
                }                
            }
        }
        
        propertiesValues = propertyList.toArray(new String[propertyList.size()]);
        firstLayerProperties = firstLayerPropertyList.toArray(new String[firstLayerPropertyList.size()]);
        firstLayerSingleProperties = firstLayerSinglePropertyList.toArray(new String[firstLayerSinglePropertyList.size()]);
        singleProperties = singlePropertyList.toArray(new String[singlePropertyList.size()]);
        descriptionPropertiesValues = descriptionPropertiesValuesList.toArray(new String[descriptionPropertiesValuesList.size()]);
    }

    public String[] getIdPropertiesValues() {
        return idPropertiesValues;
    }

    public String[] getDescriptionPropertiesValues() {
        return descriptionPropertiesValues;
    }
    
    public String[] getPropertiesValues() {
        return propertiesValues;
    }

    public String[] getFirstLayerProperties() {
        return firstLayerProperties;
    }

    public String[] getSingleProperties() {
        return singleProperties;
    }

    public String[] getFirstLayerSingleProperties() {
        return firstLayerSingleProperties;
    }

    public Class getEntity() {
        return entity;
    }
}
