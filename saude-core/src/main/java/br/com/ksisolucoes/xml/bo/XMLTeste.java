/*
 * XMLTeste.java
 *
 * 
 */

package br.com.ksisolucoes.xml.bo;


import br.com.ksisolucoes.util.log.Loggable;

/**
 * Representa uma excesso padro para o tratamento de XML.
 *
 * Created on 18 de Maio de 2004, 11:05
 * <AUTHOR>
 * @since 1.4
 */
public class XMLTeste {
    
    /**
     * Contrutor o qual cria a excesso com a mensagem padro.
     */
    public XMLTeste() {
    }
    
    /**
     *
     * @param args the command line arguments
     */
    public static void main(String[] args) {
        XML xml = null;
        try{
            xml = new XML();
        }
        catch ( Exception e ){
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        
        testarGetEntidadePorNome(xml);
        testarGetEntidadePorClasse(xml);
        testarGetAtributo(xml);
        testarIsNotNull(xml);
        testarGetAtributosNotNull(xml);
        testarToString(xml);
        
    }
    
    private static void testarGetEntidadePorNome(XML xml){
        try{
            Loggable.log.info("----------GetEntidadePorNome----------");
            Loggable.log.info(xml.getEntidadePorNome("Atividade"));
            Loggable.log.info("----------GetEntidadePorNome----------\n");
        }
        catch ( Exception e ){
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }
    
    private static void testarGetEntidadePorClasse(XML xml){
        try{
            Loggable.log.info("----------GetEntidadePorClasse----------");
            Loggable.log.info(xml.getEntidadePorClasse("br.com.ksisolucoes.vo.basico.Atividade"));
            Loggable.log.info("----------GetEntidadePorClasse----------\n");
        }
        catch ( Exception e ){
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }
    
    private static void testarGetAtributo(XML xml){
        try{
            Loggable.log.info("----------GetAtributo----------");
            Loggable.log.info(xml.getAtributo("Atividade", "codigo"));
            Loggable.log.info("----------GetAtributo----------\n");
        }
        catch ( Exception e ){
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }
    
    private static void testarGetAtributosNotNull(XML xml){
        try{
            Loggable.log.info("----------GetAtributosNotNull----------");
            Loggable.log.info(xml.getAtributosNotNull("Atividade"));
            Loggable.log.info("----------GetAtributosNotNull----------\n");
        }
        catch ( Exception e ){
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }
    
    private static void testarIsNotNull(XML xml){
        try{
            Loggable.log.info("----------IsNotNull----------");
            Loggable.log.info(xml.isNotNull("Atividade", "codigo"));
            Loggable.log.info("----------IsNotNull----------\n");

            Loggable.log.info("----------IsNotNull----------");
            Loggable.log.info(xml.isNotNull("Atividade", "descricao"));
            Loggable.log.info("----------IsNotNull----------\n");
        }
        catch ( Exception e ){
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
    }
    
    private static void testarToString(XML xml){
        Loggable.log.info("----------ToString----------");
        Loggable.log.info(xml.toString());
        Loggable.log.info("----------ToString----------\n");
    }
    
    private static void testarWrite(XML xml){
        try{
            xml.write("saida.xml");
        }
        catch ( Exception e ){
             br.com.ksisolucoes.util.log.Loggable.log.error(e);
        }
        
    }
    
}
