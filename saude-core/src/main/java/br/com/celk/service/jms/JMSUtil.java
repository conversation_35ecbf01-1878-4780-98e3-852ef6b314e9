package br.com.celk.service.jms;

import br.com.celk.util.SSLUtils;
import br.com.ksisolucoes.bo.CommomFacade;
import br.com.ksisolucoes.system.factory.BOFactory;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Modulos;
import br.com.ksisolucoes.util.log.Loggable;
import org.jboss.resteasy.client.jaxrs.ResteasyClient;
import org.jboss.resteasy.client.jaxrs.ResteasyClientBuilder;
import org.jboss.resteasy.client.jaxrs.ResteasyWebTarget;

import javax.ws.rs.client.Entity;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class JMSUtil {

    public enum DestinoJms{
        PAINEL,
        MENSAGENS,
        NOTIFICACAO;
    }
    
    public static void enviarNotificacao(String mensagem, DestinoJms destino, String customParam) {

        String def = "";

        if (JMSUtil.DestinoJms.NOTIFICACAO.equals(destino)) {

            def = "asy:";
        } else if (JMSUtil.DestinoJms.MENSAGENS.equals(destino)) {

            def = "men:";
        }

        Map map = new HashMap();

        if (destino.equals(DestinoJms.PAINEL)) {

            map.put("servico", "PAINEL");
        } else {
            map.put("servico", "SISTEMA");
        }

        map.put("cliente", TenantContext.getContext());
        map.put("usuario", customParam);
        map.put("mensagem", def + mensagem);

        String urlPapaleguas = "";
        
        try {
            // FIXME no Java 8

            /* Está sendo utilizado o SSLUtils.getSSLContext() pois foi criado um proxy (IP fixo) para Torres/RS e o
               certificado HTTPS instalado nesta máquina não compatibilidade com o Java 7, somente, Java * ou superior,
               assim sendo quando for migrar para Java 8 esta opção deve ser removida. */

            urlPapaleguas = BOFactory.getBO(CommomFacade.class).modulo(Modulos.GERAL).getParametro("URL_Papaleguas");

            ResteasyClient client = new ResteasyClientBuilder().sslContext(SSLUtils.getSSLContext()).build();

            ResteasyWebTarget target = client.target(urlPapaleguas + "/papaleguas/rest/services/notificarCliente");

            Response response = target
                    .request(MediaType.APPLICATION_JSON_TYPE)
                    .buildPost(Entity.entity(map, MediaType.APPLICATION_JSON))
                    .invoke();


            if (response.getStatus() != 204) {
                StringBuilder sb = new StringBuilder();
                sb.append("Erro ao enviar notificação!")
                        .append(System.lineSeparator())
                        .append("Status: ").append(response.getStatus())
                        .append(System.lineSeparator())
                        .append(response.getStatusInfo().getReasonPhrase())
                        .append(System.lineSeparator())
                        .append("Target: ").append(urlPapaleguas).append("/papaleguas/rest/services/notificarCliente")
                        .append(System.lineSeparator())
                        .append("Mensagem: ").append(mensagem);
                throw new RuntimeException(sb.toString());
            }
        } catch (Exception ex) {
            Loggable.log.error(urlPapaleguas + " - " + map.get("cliente") + ": " + ex.getMessage(), ex);
        }
    }
}
