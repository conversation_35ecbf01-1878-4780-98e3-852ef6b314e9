package br.com.celk.helper.vacinas;

import br.com.celk.service.CelkMicroServicosService;
import br.com.celk.util.CollectionUtils;
import br.com.celk.util.DataUtil;
import br.com.ksisolucoes.bo.command.BuilderQueryCustom;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.bo.command.QueryCustom;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.VOUtils;
import br.com.ksisolucoes.util.basico.CargaBasicoPadrao;
import br.com.ksisolucoes.util.rnds.RndsUtil;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import br.com.ksisolucoes.vo.basico.ParametroRnds;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.base.BaseUsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.vacina.*;
import br.com.ksisolucoes.vo.vacina.base.BaseTipoVacina;
import br.com.ksisolucoes.vo.vacina.base.BaseVacinaAplicacao;
import org.joda.time.LocalDate;

import java.util.*;


public class AplicarVacinaHelper {
    private static final Map<Long, GrupoAtendimentoVacinacaoEsus> mapGrupoAtendimentoByCodigoEsus = new HashMap<>();
    private static final Map<String, GrupoAtendimentoVacinacaoEsus> mapGrupoAtendimentoByCodigoRnds = new HashMap<>();
    private static final Map<Long, LocalAplicacao> mapLocalAplicacao = new HashMap<>();
    private static List<GrupoAtendimentoVacinacaoEsus> grupoAtendimentoVacinacaoEsusList = new ArrayList<>();
    private static List<LocalAplicacao> localAplicacaoList = new ArrayList<>();
    private static final Map<Long, ViaAdministracao> mapViaAdministracao = new HashMap<>();
    private static List<ViaAdministracao> viaAdministracaoList = new ArrayList<>();
    private static AplicarVacinaHelper instance = new AplicarVacinaHelper();

    private AplicarVacinaHelper() {
    }

    public static AplicarVacinaHelper getInstance() {
        return instance;
    }

    public boolean integraRnds() {
        if (RndsUtil.utilizaNovaIntegracaoRnds()) {
            return CelkMicroServicosService.getInstance().getIntegracaoRndsAtiva();
        }
        ParametroRnds parametroRnds = CargaBasicoPadrao.getInstance(TenantContext.getRealContext()).getParametroRnds();
        return RepositoryComponentDefault.SIM_LONG.equals(parametroRnds.getIntegraVacinacao());
    }

    public boolean integraEsus(VacinaAplicacao vacinaAplicacao) {
        if ((RepositoryComponentDefault.SIM.equals(vacinaAplicacao.getTipoVacina().getFlagVacinaNaoExisteCalendario()))) {
            return false;
        }

        if (RepositoryComponentDefault.SIM_LONG.equals(vacinaAplicacao.getFlagHistorico())) {
            return false;
        }

        if (integraRnds(vacinaAplicacao)) {
            return RepositoryComponentDefault.SIM_LONG.equals(verificaParametroEmpresaEsus())
                    && !TipoVacina.VACINAS_COVID_INTEGRAR_RNDS.contains(vacinaAplicacao.getTipoVacina().getTipoEsus())
                    && !TipoVacina.OUTRAS_VACINAS_INTEGRAR_RNDS.contains(vacinaAplicacao.getTipoVacina().getTipoEsus());
        }

        return RepositoryComponentDefault.SIM_LONG.equals(verificaParametroEmpresaEsus());
    }

    public boolean integraRnds(VacinaAplicacao vacinaAplicacao) {
        if (!RndsUtil.utilizaNovaIntegracaoRnds()) {
            return integraRndsOld(vacinaAplicacao);
        }

        if (vacinaAplicacao != null && vacinaAplicacao.getTipoVacina() != null && vacinaAplicacao.getTipoVacina().getTipoEsus() != null) {
            return CelkMicroServicosService.getInstance().getIntegracaoRndsAtiva() &&
                    (TipoVacina.VACINAS_COVID_INTEGRAR_RNDS.contains(vacinaAplicacao.getTipoVacina().getTipoEsus())
                    || TipoVacina.OUTRAS_VACINAS_INTEGRAR_RNDS.contains(vacinaAplicacao.getTipoVacina().getTipoEsus()));
        }

        return false;
    }

    public boolean integraRndsOld(VacinaAplicacao vacinaAplicacao) {
        ParametroRnds parametroRnds = CargaBasicoPadrao.getInstance(TenantContext.getRealContext()).getParametroRnds();

        if (vacinaAplicacao != null && vacinaAplicacao.getTipoVacina() != null && vacinaAplicacao.getTipoVacina().getTipoEsus() != null) {
            return RepositoryComponentDefault.SIM_LONG.equals(parametroRnds.getIntegraVacinacao()) &&
                    (TipoVacina.VACINAS_COVID_INTEGRAR_RNDS.contains(vacinaAplicacao.getTipoVacina().getTipoEsus())
                    || TipoVacina.OUTRAS_VACINAS_INTEGRAR_RNDS.contains(vacinaAplicacao.getTipoVacina().getTipoEsus()));
        }

        return false;
    }


    public boolean isAplicacaoIntervalo(boolean isMinimo, Date dataNovaAplicacao, VacinaAplicacao vacinaAplicacao, VacinaCalendario vacinaCalendario) {
        if (vacinaAplicacao == null) return false;
        if (vacinaCalendario == null) return false;
        int diferencaEntreAplicacoes = DataUtil.getDiasDiferenca(vacinaAplicacao.getDataAplicacao(), dataNovaAplicacao == null ? vacinaAplicacao.getDataAplicacao() : dataNovaAplicacao);
        if (isMinimo)
            return diferencaEntreAplicacoes < (vacinaCalendario.getIntervaloMinimoDose() == null ? 0 : vacinaCalendario.getIntervaloMinimoDose());
        if (vacinaCalendario.getIntervaloMaximoDose() == null) return false;

        return diferencaEntreAplicacoes > vacinaCalendario.getIntervaloMaximoDose();
    }

    public boolean isAplicacaoEntreIntervaloMinimoMaximo(Date dataNovaAplicacao, VacinaAplicacao vacinaAplicacao, VacinaCalendario vacinaCalendario) {
        if (vacinaAplicacao == null) return false;
        if (vacinaCalendario == null) return false;
        int diferencaEntreAplicacoes = DataUtil.getDiasDiferenca(vacinaAplicacao.getDataAplicacao(), dataNovaAplicacao == null ? vacinaAplicacao.getDataAplicacao() : dataNovaAplicacao);
        long intervaloMinimo = vacinaCalendario.getIntervaloMinimoDose() == null ? 0 : vacinaCalendario.getIntervaloMinimoDose();
        long intervaloMaximo = vacinaCalendario.getIntervaloMaximoDose() == null ? 0 : vacinaCalendario.getIntervaloMaximoDose();
        return (diferencaEntreAplicacoes >= intervaloMinimo && diferencaEntreAplicacoes <= intervaloMaximo);
    }

    public boolean isCalendarioExportaEsus(Calendario calendario) {
        if (calendario == null) return false;
        return Calendario.EstrategiaEsusPniRnds.CAMPANHA_INDISCRIMINADA.getCodigoEsusPni().equals(calendario.getEstrategiaEsus())
                || Calendario.EstrategiaEsusPniRnds.CAMPANHA_SELETIVA.getCodigoEsusPni().equals(calendario.getEstrategiaEsus());
    }

    public boolean pacienteSemCPFeCNS(UsuarioCadsus usuarioCadsus) {
        if (usuarioCadsus == null) return false;
        return ((usuarioCadsus.getCns() == null || usuarioCadsus.getCns().isEmpty()) &&
                (usuarioCadsus.getCpf() == null || usuarioCadsus.getCpf().isEmpty()));
    }

    public boolean pacienteMenor28Dias(UsuarioCadsus usuarioCadsus) {
        if (usuarioCadsus == null) return false;
        if (usuarioCadsus.getDataNascimento() == null) return false;
        int quantidadeDiasIdade = DataUtil.getDiasDiferenca(new LocalDate(usuarioCadsus.getDataNascimento()).toDate(), LocalDate.now().toDate());
        return (quantidadeDiasIdade <= 28);
    }

    public boolean pacienteGrupoGestantePuerpera(UsuarioCadsus usuarioCadsus) {
        if (usuarioCadsus == null) return false;
        if (usuarioCadsus.getGrupoVacinacao() == null) return false;
        return GrupoAtendimentoVacinacaoEsus.GrupoAtendimentoEsusVacina.GESTANTE.value().equals(usuarioCadsus.getGrupoVacinacao()) || GrupoAtendimentoVacinacaoEsus.GrupoAtendimentoEsusVacina.PUERPERA.value().equals(usuarioCadsus.getGrupoVacinacao());
    }

    public boolean isTipoVacinaCovid19(TipoVacina tipoVacina) {
        if (tipoVacina == null || tipoVacina.getTipoEsus() == null) return false;
        return TipoVacina.TIPO_VACINA_COVID_19.contains(tipoVacina.getTipoEsus());
    }

    public boolean vacinaForaIdadeMaxima(UsuarioCadsus usuarioCadsus, VacinaCalendario vacinaCalendario) {
        if (vacinaCalendario != null) {
            Long idadeMaximaLimiteVacinaMeses = vacinaCalendario.getIdadeLimite();
            if (idadeMaximaLimiteVacinaMeses != null && RepositoryComponentDefault.SIM_LONG.equals(vacinaCalendario.getFlagAplicarAposLimite()) && idadeMaximaLimiteVacinaMeses <= usuarioCadsus.getIdadeEmMeses()) {
                return true;
            }
        }
        return false;
    }

    public boolean vacinaForaIdadeMinima(UsuarioCadsus usuarioCadsus, VacinaCalendario vacinaCalendario) {
        if (vacinaCalendario != null) {
            Long idadeLimiteVacinaMeses = vacinaCalendario.getIdade();
            if (idadeLimiteVacinaMeses != null && RepositoryComponentDefault.SIM_LONG.equals(vacinaCalendario.getFlagAplicarAntesLimite()) && idadeLimiteVacinaMeses > usuarioCadsus.getIdadeEmMeses()) {
                return true;
            }
        }
        return false;
    }

    public String idadeFormatada(Long idade) {
        if (idade == null) return "";

        if (idade == 0) return Bundle.getStringApplication("rotulo_ao_nascer");

        if (idade % 12 == 0) {
            idade = idade / 12;

            if (idade == 1L) return idade + " " + Bundle.getStringApplication("rotulo_ano");

            return idade + " " + Bundle.getStringApplication("rotulo_anos");
        } else {
            if (idade == 1L) return idade + " " + Bundle.getStringApplication("rotulo_mes");

            return idade + " " + Bundle.getStringApplication("rotulo_meses");
        }
    }

    public List<VacinaAplicacao> getVacinasCovidPaciente(UsuarioCadsus usuarioCadsus) {
        return LoadManager.getInstance(VacinaAplicacao.class)
                .addProperties(new HQLProperties(VacinaAplicacao.class).getProperties())
                .addProperties(new HQLProperties(TipoVacina.class, BaseVacinaAplicacao.PROP_TIPO_VACINA).getProperties())
                .addProperties(new HQLProperties(VacinaCalendario.class, BaseVacinaAplicacao.PROP_VACINA_CALENDARIO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseVacinaAplicacao.PROP_TIPO_VACINA, BaseTipoVacina.PROP_TIPO_ESUS), QueryCustom.QueryCustomParameter.IN, TipoVacina.TIPO_VACINA_COVID_19))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseVacinaAplicacao.PROP_USUARIO_CADSUS, BaseUsuarioCadsus.PROP_CODIGO), usuarioCadsus.getCodigo()))
                .addParameter(new QueryCustom.QueryCustomParameter(VOUtils.montarPath(BaseVacinaAplicacao.PROP_STATUS), BuilderQueryCustom.QueryParameter.IN,
                        Arrays.asList(VacinaAplicacao.StatusVacinaAplicacao.APLICADA.value(), VacinaAplicacao.StatusVacinaAplicacao.REAPLICADA.value())))
                .addSorter(new QueryCustom.QueryCustomSorter(BaseVacinaAplicacao.PROP_DOSE, BuilderQueryCustom.QuerySorter.DECRESCENTE))
                .start().getList();
    }

    private Long verificaParametroEmpresaEsus() {
        return SessaoAplicacaoImp.getInstance().getEmpresa().getFlagGeraProdVacinaEsus();
    }

    public GrupoAtendimentoVacinacaoEsus buscarGrupoAtendimentoVacinacaoEsusByCodigoEsus(Long codigoEsus) {
        if (mapGrupoAtendimentoByCodigoEsus.containsKey(codigoEsus)) {
            return mapGrupoAtendimentoByCodigoEsus.get(codigoEsus);
        }
        List<GrupoAtendimentoVacinacaoEsus> grupoAtendimentoVacinacaoEsuses = buscarGrupoAtendimentoVacinacaoEsusList();
        for (GrupoAtendimentoVacinacaoEsus grupoAtendimentoVacinacaoEsus : grupoAtendimentoVacinacaoEsuses) {
            mapGrupoAtendimentoByCodigoEsus.put(grupoAtendimentoVacinacaoEsus.getCodigoEsus(), grupoAtendimentoVacinacaoEsus);
        }

        return mapGrupoAtendimentoByCodigoEsus.get(codigoEsus);
    }

    public GrupoAtendimentoVacinacaoEsus buscarGrupoAtendimentoVacinacaoEsusByCodigoRnds(Long codigoRnds) {
        if (mapGrupoAtendimentoByCodigoRnds.containsKey(codigoRnds)) {
            return mapGrupoAtendimentoByCodigoRnds.get(codigoRnds);
        }
        List<GrupoAtendimentoVacinacaoEsus> grupoAtendimentoVacinacaoEsuses = buscarGrupoAtendimentoVacinacaoEsusList();
        for (GrupoAtendimentoVacinacaoEsus grupoAtendimentoVacinacaoEsus : grupoAtendimentoVacinacaoEsuses) {
            mapGrupoAtendimentoByCodigoRnds.put(grupoAtendimentoVacinacaoEsus.getCodigoRnds(), grupoAtendimentoVacinacaoEsus);
        }

        return mapGrupoAtendimentoByCodigoRnds.get(codigoRnds);
    }

    public List<GrupoAtendimentoVacinacaoEsus> buscarGrupoAtendimentoVacinacaoEsusList() {
        if (CollectionUtils.isNotNullEmpty(grupoAtendimentoVacinacaoEsusList)) {
            return grupoAtendimentoVacinacaoEsusList;
        }
        grupoAtendimentoVacinacaoEsusList = LoadManager.getInstance(GrupoAtendimentoVacinacaoEsus.class)
                .addProperties(new HQLProperties(GrupoAtendimentoVacinacaoEsus.class).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(GrupoAtendimentoVacinacaoEsus.PROP_ATIVO, RepositoryComponentDefault.ATIVO))
                .addSorter(new QueryCustom.QueryCustomSorter(GrupoAtendimentoVacinacaoEsus.PROP_DESCRICAO))
                .start().getList();

        return grupoAtendimentoVacinacaoEsusList;
    }

    public List<LocalAplicacao> buscarLocalAplicacaoList() {
        if (CollectionUtils.isNotNullEmpty(localAplicacaoList)) {
            return localAplicacaoList;
        }
        localAplicacaoList = LoadManager.getInstance(LocalAplicacao.class)
                .addProperties(new HQLProperties(LocalAplicacao.class).getProperties())
                .start().getList();

        return localAplicacaoList;
    }

    public LocalAplicacao buscarLocalAplicacaoByCodigo(Long codigo) {
        if (mapLocalAplicacao.containsKey(codigo)) {
            return mapLocalAplicacao.get(codigo);
        }
        List<LocalAplicacao> localAplicacaos = buscarLocalAplicacaoList();
        for (LocalAplicacao localAplicacao : localAplicacaos) {
            mapLocalAplicacao.put(localAplicacao.getCodigo(), localAplicacao);
        }

        return mapLocalAplicacao.get(codigo);
    }


    public  List<ViaAdministracao> buscarViaAdministracaoList() {
        if (CollectionUtils.isNotNullEmpty(viaAdministracaoList)) {
            return viaAdministracaoList;
        }
        viaAdministracaoList = LoadManager.getInstance(ViaAdministracao.class)
                .addProperties(new HQLProperties(ViaAdministracao.class).getProperties())
                .start().getList();

        return viaAdministracaoList;
    }

    public  ViaAdministracao buscarViaAdministracaoByCodigo(Long codigo) {
        if (mapViaAdministracao.containsKey(codigo)) {
            return mapViaAdministracao.get(codigo);
        }
        List<ViaAdministracao> viaAdministracaos = buscarViaAdministracaoList();
        for (ViaAdministracao viaAdministracao : viaAdministracaos) {
            mapViaAdministracao.put(viaAdministracao.getCodigo(), viaAdministracao);
        }

        return mapViaAdministracao.get(codigo);
    }

    public List<LocalAplicacao> getLocalAplicacaoList(ViaAdministracao viaAdministracao) {
        List<LocalAplicacao> localAplicacaoListforVia = new ArrayList<>();

        List<ViaAdministracaoLocalAplicacao> list =  LoadManager.getInstance(ViaAdministracaoLocalAplicacao.class)
                .addProperties(new HQLProperties(LocalAplicacao.class, ViaAdministracaoLocalAplicacao.PROP_LOCAL_APLICACAO).getProperties())
                .addParameter(new QueryCustom.QueryCustomParameter(ViaAdministracaoLocalAplicacao.PROP_VIA_ADMINISTRACAO, viaAdministracao))
                .start().getList();
        list.stream().map(ViaAdministracaoLocalAplicacao::getLocalAplicacao).forEach(localAplicacaoListforVia::add);

        return localAplicacaoListforVia;
    }

    public void validarCboAplicacao(VacinaAplicacao vacinaAplicacao) throws ValidacaoException {
        List<TabelaCbo> cboList = vacinaAplicacao.getProfissionalAplicacao().getListCboProfissional(vacinaAplicacao.getEmpresa());
        if (!CollectionUtils.isNotNullEmpty(cboList)) {
            throw new ValidacaoException("Profissional sem CBO cadastrado. Por favor, cadastre o CBO do profissional antes de continuar.");
        }

        TabelaCbo cbo = vacinaAplicacao.getProfissionalAplicacao().getCboValidoEsusVacina(cboList);
        if (cbo == null) {
            throw new ValidacaoException("O profissional " + vacinaAplicacao.getProfissionalAplicacao().getNome() + " não possui CBO válido conforme a regra da ficha de vacina do esus.");
        }
    }

    public boolean getVacinasRiaR(VacinaCalendario vacinaAplicacao) {
        if (vacinaAplicacao == null) return false;
        if (vacinaAplicacao.getTipoVacina() == null) return false;

        return (TipoVacina.TipoEsus.COVID_19_MODERNA.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus()) || TipoVacina.TipoEsus.VACINA_POLIO_ORAL.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                || TipoVacina.TipoEsus.VACINA_SARAMPO_CAXUMBA_RUBEOLA.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                ||  TipoVacina.TipoEsus.VACINA_COVID19_RECOMBINANTE_SERUM_ZALIKA.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                ||  TipoVacina.TipoEsus.COVID_19_PFIZER_COMIRNATY.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                || TipoVacina.TipoEsus.VACINA_POLIO_INJETAVEL.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus()));
    }

    public boolean getVacinasRiaRHistorico(VacinaAplicacao vacinaAplicacao) {
        if (vacinaAplicacao == null) return false;
        if (vacinaAplicacao.getTipoVacina() == null) return false;

        return (TipoVacina.TipoEsus.COVID_19_MODERNA.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus()) || TipoVacina.TipoEsus.VACINA_POLIO_ORAL.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                || TipoVacina.TipoEsus.VACINA_SARAMPO_CAXUMBA_RUBEOLA.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                ||  TipoVacina.TipoEsus.VACINA_COVID19_RECOMBINANTE_SERUM_ZALIKA.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                ||  TipoVacina.TipoEsus.COVID_19_PFIZER_COMIRNATY.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus())
                || TipoVacina.TipoEsus.VACINA_POLIO_INJETAVEL.value().equals(vacinaAplicacao.getTipoVacina().getTipoEsus()));
    }

}