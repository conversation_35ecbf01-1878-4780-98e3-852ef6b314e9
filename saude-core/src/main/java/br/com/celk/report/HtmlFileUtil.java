package br.com.celk.report;

import br.com.ksisolucoes.util.log.Loggable;
import com.itextpdf.html2pdf.HtmlConverter;

import java.io.*;

/**
 * Created by Leonardo
 */
public class HtmlFileUtil {
    public static File resolveHtmlReport(HtmlReport htmlReport) {
        try {
            File newFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".pdf");
            OutputStream stream = new FileOutputStream(newFile);
            HtmlConverter.convertToPdf(new ByteArrayInputStream(htmlReport.buildReport().getBytes("UTF-8")), stream);
            return newFile;
        } catch (IOException ex) {
            Loggable.log.error(ex.getMessage(), ex);
        }
        return null;
    }
}
