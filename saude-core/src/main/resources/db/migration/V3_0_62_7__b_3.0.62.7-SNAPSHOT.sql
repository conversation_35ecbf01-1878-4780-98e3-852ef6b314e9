SET application_name = 'flyway|3.0.62.7';


/*
    Roger - 07/02/2017 -  #13870
*/
update menu_web set ds_bundle = 'relatorioTratamentosOdontologicos' where cd_menu = 532;
update menu_web set ds_menu = 'Relatório de Tratamentos Odontológicos' where cd_menu = 532;

ALTER TABLE atendimento ADD COLUMN flag_gestante SMALLINT NULL;
ALTER TABLE auditschema.atendimento ADD COLUMN flag_gestante SMALLINT NULL;

/*
    Sulivan - 07/02/2017 - #14188
*/
alter table atendimento_item add column cd_teste_rapido_realizado INT8;
alter table auditschema.atendimento_item add column cd_teste_rapido_realizado INT8;

alter table atendimento_item
        add constraint FK_ATEND_ITEM_REF_TES_RAPI_REAL foreign key (cd_teste_rapido_realizado)
        references teste_rapido_realizado (cd_teste_rapido_realizado)
        on delete restrict on update restrict;

/*
    Sulivan - 08/02/2017 - #14353
*/
ALTER TABLE requerimento_defesa_previa ALTER num_auto_infracao TYPE VARCHAR(30);
ALTER TABLE auditschema.requerimento_defesa_previa ALTER num_auto_infracao TYPE VARCHAR(30);

/*
    Sulivan - 08/02/2017 - #14354
*/
ALTER TABLE responsavel_tecnico ADD COLUMN rg VARCHAR(20) NULL;
ALTER TABLE auditschema.responsavel_tecnico ADD COLUMN rg VARCHAR(20) NULL;
ALTER TABLE responsavel_tecnico ADD COLUMN dt_emissao_rg DATE NULL;
ALTER TABLE auditschema.responsavel_tecnico ADD COLUMN dt_emissao_rg DATE NULL;

/*
    Roger - 07/02/2017 -  #14348
*/
ALTER TABLE requerimento_receita_a ADD COLUMN cd_resp_tecnico  BIGINT NULL;
ALTER TABLE auditschema.requerimento_receita_a ADD COLUMN cd_resp_tecnico  BIGINT NULL;


ALTER TABLE requerimento_receita_b ADD COLUMN cd_resp_tecnico  BIGINT NULL;
ALTER TABLE auditschema.requerimento_receita_b ADD COLUMN cd_resp_tecnico  BIGINT NULL;

ALTER TABLE vigilancia_profissional ADD COLUMN dt_emissao_rg DATE NULL;
ALTER TABLE auditschema.vigilancia_profissional ADD COLUMN dt_emissao_rg DATE NULL;