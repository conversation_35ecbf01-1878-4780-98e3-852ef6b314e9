SET application_name = 'flyway|3.1.258';

SET statement_timeout TO 600000;

create table if not exists indicador_simas (
    cd_indicador_simas	  int8      primary key not null,
	descricao                   varchar(100)  null,
	situacao                    int2        not null,
	indicador                   int4        null,
    cd_usuario                 int8   	 not null REFERENCES public.usuarios(cd_usuario),
    dt_cadastro                timestamp	  not null,
    dt_alteracao               timestamp	  null,
    "version"					  int8		 not null
);
create sequence seq_indicador_simas start 1 increment 1;

CREATE TABLE auditschema.indicador_simas AS (SELECT t2.*, t1.* FROM indicador_simas t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_indicador_simas;
ALTER TABLE auditschema.indicador_simas ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.indicador_simas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table if not exists indicador_simas_elo_procedimento (
    cd_indicador_simas_elo_procedimento	  int8      primary key not null,
    cd_indicador_simas	  int8      not null REFERENCES public.Indicador_simas(cd_Indicador_simas),
    cd_procedimento	  int8      not null REFERENCES public.procedimento(cd_procedimento),
    cd_usuario                 int8   	 not null REFERENCES public.usuarios(cd_usuario),
    dt_cadastro                timestamp	  not null,
    dt_alteracao               timestamp	  null,
    "version"					  int8		 not null
);
create sequence seq_indicador_simas_elo_procedimento start 1 increment 1;

CREATE TABLE auditschema.indicador_simas_elo_procedimento AS (SELECT t2.*, t1.* FROM indicador_simas_elo_procedimento t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_indicador_simas_elo_procedimento;
ALTER TABLE auditschema.indicador_simas_elo_procedimento ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.indicador_simas_elo_procedimento FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create table if not exists indicador_simas_elo_cbo (
    cd_indicador_simas_elo_cbo	  int8      primary key not null,
    cd_indicador_simas	  int8      not null REFERENCES public.Indicador_simas(cd_Indicador_simas),
    cd_cbo                      varchar(10) not null REFERENCES public.tabela_cbo(cd_cbo),
    cd_usuario                 int8   	 not null REFERENCES public.usuarios(cd_usuario),
    dt_cadastro                timestamp	  not null,
    dt_alteracao               timestamp	  null,
    "version"					  int8		 not null
);
create sequence seq_indicador_simas_elo_cbo start 1 increment 1;

CREATE TABLE auditschema.indicador_simas_elo_cbo AS (SELECT t2.*, t1.* FROM indicador_simas_elo_cbo t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_indicador_simas_elo_cbo;
ALTER TABLE auditschema.indicador_simas_elo_cbo ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.indicador_simas_elo_cbo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO programa_pagina VALUES (2123, 'br.com.celk.view.controle.integracaosimas.ConsultaIndicadorSimasPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (1182, 'Indicador Simas', 2123, 'N');
INSERT INTO programa_web_pagina VALUES (2190, 1182, 2123);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1000193,'Indicador Simas','indicador_simas',396,1182,0,0,0);
INSERT INTO programa_pagina VALUES (2124, 'br.com.celk.view.controle.integracaosimas.CadastroIndicadorSimasPage', 'N');
INSERT INTO programa_web_pagina VALUES (2191, 1182, 2124);
