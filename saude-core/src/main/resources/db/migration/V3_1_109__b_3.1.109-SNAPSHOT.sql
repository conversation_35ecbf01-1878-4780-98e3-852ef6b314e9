SET application_name = 'flyway|3.1.109';

SET statement_timeout TO 600000;


/*
 * Feature REG-395 | REG-716
 * <PERSON> | Marcello | Elizabeth - 26/07/2021
 */

INSERT INTO parametro_gem
(cd_modulo, parametro, nivel, identificador, valor, observacao, "version")
VALUES(26, 'controlaCotaFinanceiraPPI', 'G', '0', 'N', 'Define se o agendamento deve controlar a cota dos municípios pactuados na PPI.', 0);

INSERT INTO parametro_gem
(cd_modulo, parametro, nivel, identificador, valor, observacao, "version")
VALUES(27, 'controlaCotaFinanceiraPPIDataInicio', 'G', '0', null, 'Data na qual o agendamento passa a consumir cota dos municípios pactuados na PPI.', 0);

update programa_pagina set cam_pagina = 'br.com.celk.view.unidadesaude.exames.ppi.ConsultaPpiPage' where cd_prg_pagina = 252;
update programa_pagina set cam_pagina = 'br.com.celk.view.unidadesaude.exames.ppi.CadastroPpiPage' where cd_prg_pagina = 253;
update programa_web set ds_prg_web = 'Consulta PPI' where cd_prg_web = 155;
update menu_web
    set ds_menu = 'Consulta PPI',
    ds_bundle = 'consultaPpi'
    where cd_menu = 219;

CREATE  TABLE "public".ppi_secretaria (
	cd_ppi_secretaria    bigserial  NOT NULL ,
	cd_secretaria        bigint  NOT NULL ,
	dt_competencia       date  NOT NULL ,
	vl_ppi               numeric(14,2) DEFAULT 0 NOT NULL ,
	vl_usado             numeric(14,2) DEFAULT 0 NOT NULL ,
	vl_adicional         numeric(14,2),
	vl_global            numeric(14,2) DEFAULT 0 NOT NULL,
	contratos_adicionais text,
	st_situacao          smallint DEFAULT 1 NOT NULL ,
	st_foi_resgatado     boolean DEFAULT false NOT NULL ,
	"version"            bigint   ,
	CONSTRAINT pk_ppi_secretaria_cd_ppi_secretaria PRIMARY KEY ( cd_ppi_secretaria )
 );

ALTER TABLE "public".ppi_secretaria ADD CONSTRAINT cns_ppi_secretaria_st_situacao CHECK ( st_situacao >= 1 and st_situacao <= 2 );
COMMENT ON CONSTRAINT cns_ppi_secretaria_st_situacao ON "public".ppi_secretaria IS '1 - Registro ativo.\n2 - Registro excluído.';
CREATE UNIQUE INDEX unq_ppi_secretaria ON "public".ppi_secretaria ( cd_secretaria, dt_competencia, st_situacao )  WHERE st_situacao = 1;
COMMENT ON COLUMN "public".ppi_secretaria.cd_ppi_secretaria IS 'Identificador único para esta pactuação (em um determinada competência) com a secretaria.';
COMMENT ON COLUMN "public".ppi_secretaria.cd_secretaria IS 'FK para a tabela empresa. Indica a secretaria do município contratante da PPI.';
COMMENT ON COLUMN "public".ppi_secretaria.dt_competencia IS 'Data de competência da validade (mês e ano de vigência) desta pactuação. Ela terá sempre o dia = 01, variando apenas o mês e o ano. Esta data deve ser considerada nas tabelas ppi_tipo_exame e ppi_exame por meio de joins.';
COMMENT ON COLUMN "public".ppi_secretaria.vl_ppi IS 'Valor (R$) da cota pactuada para esta secretaria.';
COMMENT ON COLUMN "public".ppi_secretaria.vl_usado IS 'Valor (R$) da cota que foi efetivamente utilizado. Este campo não deve ser editável, pois ele é calculado automaticamente pelo sistema sempre que um exame/consulta for realizado ou quando houver transferências de cotas.';
COMMENT ON COLUMN "public".ppi_secretaria.vl_adicional IS 'Valor  adicional total que deve ser adicionado ao valor da PPI da secretaria. Este campo só é visível quando a unidade for uma secretaria. Ele é o somatório dos valores adicionais cadastrados no campo contratos_adicionais.';
COMMENT ON COLUMN "public".ppi_secretaria.vl_global IS 'Valor (R$) da cota pactuada para esta secretaria somado ao valor total adicional. Este campo poderia ser calculado, mas para descontar a cota, teria que ser necessário uma verficação do valor total e do valor adicional. Com este campo, apenas este valor é descontado.';
COMMENT ON COLUMN "public".ppi_secretaria.contratos_adicionais IS 'Armazena uma lista de pares valorAdicional/numeroContrato para fins de histórico do valor adicional informado pela secretaria. O valor total é armazenado no campo vl_adicional. O número do contrato (formato livre) representa o termo utilizado para incluir o valor adicional. Este campo só é visível quando a unidade for uma secretaria.\n\n[\n    {\n        "valorAdicional": 1000.00,\n        "numeroContrato": "0001/2021"\n    },\n    {\n        "valorAdicional": 2000.00,\n        "numeroContrato": "0001/2021"\n    }\n]';
COMMENT ON COLUMN "public".ppi_secretaria.st_situacao IS '1 - Registro ativo.\n2 - Registro excluído. Um registro só pode ser excluído se não houverem solicitações de agendamento vinculadas. Caso não existam, a exclusão lógica deve ser feita em cascata com as tabelas ppi_tipo_exame e ppi_exame.';
COMMENT ON COLUMN "public".ppi_secretaria.st_foi_resgatado IS 'Indica se esta PPI já teve o saldo resgatado para a competência seguinte';
COMMENT ON COLUMN "public".ppi_secretaria."version" IS 'Utilizada para controle de concorrência otimista.';
COMMENT ON CONSTRAINT cns_ppi_secretaria_st_situacao ON "public".ppi_secretaria IS '1 - Registro ativo.\n2 - Registro excluído.';

CREATE  TABLE "public".ppi_tipo_exame (
	cd_ppi_tipo_exame    bigserial  NOT NULL ,
	cd_ppi_secretaria    bigint  NOT NULL ,
	cd_tp_exame          bigint  NOT NULL ,
	vl_ppi               numeric(14,2) DEFAULT 0 NOT NULL ,
	vl_usado             numeric(14,2) DEFAULT 0 NOT NULL ,
	st_situacao          smallint DEFAULT 1 NOT NULL ,
	"version"            bigint   ,
	CONSTRAINT pk_ppi_tipo_exame_cd_ppi_tipo_exame PRIMARY KEY ( cd_ppi_tipo_exame )
 );

ALTER TABLE "public".ppi_tipo_exame ADD CONSTRAINT cns_ppi_tipo_exame_st_situacao CHECK ( st_situacao >= 1 and st_situacao <= 2 );
COMMENT ON CONSTRAINT cns_ppi_tipo_exame_st_situacao ON "public".ppi_tipo_exame IS '1 - Registro ativo.\n2 - Registro excluído.';
CREATE UNIQUE INDEX unq_ppi_tipo_exame ON "public".ppi_tipo_exame ( cd_ppi_secretaria, cd_tp_exame, st_situacao )  WHERE st_situacao = 1;
COMMENT ON COLUMN "public".ppi_tipo_exame.cd_ppi_tipo_exame IS 'Identificador único para esta pactuação (em um determinada competência) com o tipo de exame.';
COMMENT ON COLUMN "public".ppi_tipo_exame.cd_ppi_secretaria IS 'FK para a PPI da secretaria desta PPI de tipo de exame.';
COMMENT ON COLUMN "public".ppi_tipo_exame.cd_tp_exame IS 'FK para a tabela tipo_exame.';
COMMENT ON COLUMN "public".ppi_tipo_exame.vl_ppi IS 'Valor (R$) da cota pactuada para este tipo de exame.';
COMMENT ON COLUMN "public".ppi_tipo_exame.vl_usado IS 'Valor (R$) da cota que foi efetivamente utilizado. Este campo não deve ser editável, pois ele é calculado automaticamente pelo sistema sempre que um exame/consulta deste tipo de exame for realizado ou quando houver transferências de cotas.';
COMMENT ON COLUMN "public".ppi_tipo_exame.st_situacao IS '1 - Registro ativo.\n2 - Registro excluído. Um registro só pode ser excluído se não houverem solicitações de agendamento vinculadas. Caso não existam, a exclusão lógica deve ser feita em cascata com a tabela ppi_exame.';
COMMENT ON COLUMN "public".ppi_tipo_exame."version" IS 'Utilizada para controle de concorrência otimista.';
COMMENT ON CONSTRAINT cns_ppi_tipo_exame_st_situacao ON "public".ppi_tipo_exame IS '1 - Registro ativo.\n2 - Registro excluído.';

CREATE  TABLE "public".ppi_exame (
	cd_ppi_exame         bigserial  NOT NULL ,
	cd_ppi_tipo_exame    bigint  NOT NULL ,
	cd_exame_procedimento bigint  NOT NULL ,
	vl_ppi               numeric(14,2) DEFAULT 0 NOT NULL ,
	vl_usado             numeric(14,2) DEFAULT 0 NOT NULL ,
	st_situacao          smallint DEFAULT 1 NOT NULL ,
	"version"            bigint   ,
	CONSTRAINT pk_ppi_exame_cd_ppi_exame PRIMARY KEY ( cd_ppi_exame )
 );

ALTER TABLE "public".ppi_exame ADD CONSTRAINT cns_ppi_exame_st_situacao CHECK ( st_situacao >= 1 and st_situacao <= 2 );
COMMENT ON CONSTRAINT cns_ppi_exame_st_situacao ON "public".ppi_exame IS '1 - Registro ativo.\n2 - Registro excluído.';
CREATE UNIQUE INDEX unq_ppi_exame_cd_ppi_tipo_exame ON "public".ppi_exame ( cd_ppi_tipo_exame, cd_exame_procedimento, st_situacao )  WHERE st_situacao = 1;
COMMENT ON COLUMN "public".ppi_exame.cd_ppi_exame IS 'Identificador único para esta pactuação (em um determinada competência) com o exame.';
COMMENT ON COLUMN "public".ppi_exame.cd_ppi_tipo_exame IS 'FK para a PPI do tipo de exame desta PPI de exame.';
COMMENT ON COLUMN "public".ppi_exame.cd_exame_procedimento IS 'FK para a tabela exame_procedimento.';
COMMENT ON COLUMN "public".ppi_exame.vl_ppi IS 'Valor (R$) da cota pactuada para este exame.';
COMMENT ON COLUMN "public".ppi_exame.vl_usado IS 'Valor (R$) da cota que foi efetivamente utilizado. Este campo não deve ser editável, pois ele é calculado automaticamente pelo sistema sempre que este exame/consulta for realizado ou quando houver transferências de cotas.';
COMMENT ON COLUMN "public".ppi_exame.st_situacao IS '1 - Registro ativo.\n2 - Registro excluído. Um registro só pode ser excluído se não houverem solicitações de agendamento vinculadas. Um registro só pode ser excluído se não houverem solicitações de agendamento vinculadas.';
COMMENT ON COLUMN "public".ppi_exame."version" IS 'Utilizada para controle de concorrência otimista.';
COMMENT ON CONSTRAINT cns_ppi_exame_st_situacao ON "public".ppi_exame IS '1 - Registro ativo.\n2 - Registro excluído.';

CREATE  TABLE "public".ppi_ocorrencia (
	cd_ppi_ocorrencia    bigserial  NOT NULL ,
	cd_ppi_secretaria    bigint  NOT NULL ,
	cd_ppi_tipo_exame    bigint   ,
	cd_ppi_exame         bigint   ,
	cd_solicitacao_agendamento bigint ,
	tp_ocorrencia        smallint DEFAULT 1 NOT NULL ,
	dt_ocorrencia        timestamp with time zone DEFAULT current_timestamp NOT NULL ,
	ds_ocorrencia        varchar(500)  NOT NULL ,
	cd_usuario           bigint  NOT NULL ,
	"version"            bigint   ,
	CONSTRAINT pk_ppi_ocorrencia_cd_ppi_ocorrencia PRIMARY KEY ( cd_ppi_ocorrencia )
 );

ALTER TABLE "public".ppi_ocorrencia ADD CONSTRAINT cns_ppi_ocorrencia_tp_ocorrencia CHECK ( tp_ocorrencia >= 1 AND tp_ocorrencia <= 9);
COMMENT ON CONSTRAINT cns_ppi_ocorrencia_tp_ocorrencia ON "public".ppi_ocorrencia IS '1 - Inserção\n2 - Exclusão\n3 - Edição\n4 - Cópia\n5 - Transferência Manual\n6 - Agendamento\n7 - Estorno\n8 - Trnasferência Automática\n9 - Resgate Saldo';
CREATE INDEX idx_ppi_ocorrencia_keys ON "public".ppi_ocorrencia ( cd_ppi_secretaria, cd_ppi_tipo_exame, cd_ppi_exame );
CREATE INDEX idx_ppi_ocorrencia_dt_competencia ON "public".ppi_ocorrencia ( dt_ocorrencia );
CREATE INDEX idx_ppi_ocorrencia_cd_solicitacao_agendamento ON "public".ppi_ocorrencia ( cd_solicitacao_agendamento ) WHERE cd_solicitacao_agendamento IS NOT NULL;
COMMENT ON TABLE "public".ppi_ocorrencia IS 'Todas as ocorrências (inclusões, alterações, exclusões, bloqueios, transferências etc.) relacionadas com a PPI. Esta mesma tabela armazena ocorrências relacionadas com secretarias, tipos de exame e exames. As chaves estrangeiras para estas 3 tabelas determinam a quem uma ocorrência pertence. Se for de uma secretaria, apenas a chave cd_ppi_secretaria deve estar preenchida. Se for um de um tipo de exame, então as chaves cd_ppi_secretaria e cd_ppi_tipo_exame devem estar preenchidas. Se for de um exame, então as 3 chaves devem estar preenchidas.';
COMMENT ON COLUMN "public".ppi_ocorrencia.cd_ppi_ocorrencia IS 'Identificador único para uma ocorrência de PPI.';
COMMENT ON COLUMN "public".ppi_ocorrencia.cd_ppi_secretaria IS 'FK para a ppi_secretaria, representando uma ocorrência relacionada com a mesma.';
COMMENT ON COLUMN "public".ppi_ocorrencia.cd_ppi_tipo_exame IS 'FK para a ppi_tipo_exame, representando uma ocorrência relacionada com o mesmo.';
COMMENT ON COLUMN "public".ppi_ocorrencia.cd_ppi_exame IS 'FK para a ppi_exame, representando uma ocorrência relacionada com o mesmo.';
COMMENT ON COLUMN "public".ppi_ocorrencia.cd_solicitacao_agendamento IS 'FK para a solicitação relacionada com esta ocorrência. Este campo será preenchido somente em ocorrências de estorno e agendamento. Utilizado para filtrar ocorrências de uma determinada solicitação.';
COMMENT ON COLUMN "public".ppi_ocorrencia.tp_ocorrencia IS 'Classifica esta ocorrência de acordo com a lista:\n1 - Inserção\n2 - Exclusão\n3 - Edição\n4 - Cópia\n5 - Transferência Manual\n6 - Agendamento\n7 - Estorno\n8 - Trnasferência Automática\n9 - Resgate Saldo';
COMMENT ON COLUMN "public".ppi_ocorrencia.dt_ocorrencia IS 'Data e hora da ocorrência.';
COMMENT ON COLUMN "public".ppi_ocorrencia.ds_ocorrencia IS 'Descrição da ocorrência, incluindo valores utilizados e informações sobre origem e destino.';
COMMENT ON COLUMN "public".ppi_ocorrencia.cd_usuario IS 'FK para a tabela usuarios. Indica o usuário operador do sistema que foi responsável por esta ocorrência.';
COMMENT ON COLUMN "public".ppi_ocorrencia."version" IS 'Utilizada para controle de concorrência otimista.';
COMMENT ON CONSTRAINT cns_ppi_ocorrencia_tp_ocorrencia ON "public".ppi_ocorrencia IS '1 - Inserção\n2 - Exclusão\n3 - Edição\n4 - Cópia\n5 - Transferência Manual\n6 - Agendamento\n7 - Estorno\n8 - Trnasferência Automática\n9 - Resgate Saldo';

ALTER TABLE "public".ppi_exame ADD CONSTRAINT fk_ppi_exame_exame_procedimento FOREIGN KEY ( cd_exame_procedimento ) REFERENCES "public".exame_procedimento( cd_exame_procedimento );
ALTER TABLE "public".ppi_exame ADD CONSTRAINT fk_ppi_exame_ppi_tipo_exame FOREIGN KEY ( cd_ppi_tipo_exame ) REFERENCES "public".ppi_tipo_exame( cd_ppi_tipo_exame );
ALTER TABLE "public".ppi_ocorrencia ADD CONSTRAINT fk_ppi_ocorrencia_ppi_secretaria FOREIGN KEY ( cd_ppi_secretaria ) REFERENCES "public".ppi_secretaria( cd_ppi_secretaria );
ALTER TABLE "public".ppi_ocorrencia ADD CONSTRAINT fk_ppi_ocorrencia_ppi_tipo_exame FOREIGN KEY ( cd_ppi_tipo_exame ) REFERENCES "public".ppi_tipo_exame( cd_ppi_tipo_exame );
ALTER TABLE "public".ppi_ocorrencia ADD CONSTRAINT fk_ppi_ocorrencia_ppi_exame FOREIGN KEY ( cd_ppi_exame ) REFERENCES "public".ppi_exame( cd_ppi_exame );
ALTER TABLE "public".ppi_ocorrencia ADD CONSTRAINT fk_ppi_ocorrencia_solicitacao_agendamento FOREIGN KEY ( cd_solicitacao_agendamento ) REFERENCES "public".solicitacao_agendamento( cd_solicitacao );
ALTER TABLE "public".ppi_ocorrencia ADD CONSTRAINT fk_ppi_ocorrencia_usuarios_cd_usuario FOREIGN KEY ( cd_usuario ) REFERENCES "public".usuarios( cd_usuario );
ALTER TABLE "public".ppi_secretaria ADD CONSTRAINT fk_ppi_unidade_empresa_cd_secretaria FOREIGN KEY ( cd_secretaria ) REFERENCES "public".empresa( empresa );
ALTER TABLE "public".ppi_tipo_exame ADD CONSTRAINT fk_ppi_tipo_exame_tipo_exame FOREIGN KEY ( cd_tp_exame ) REFERENCES "public".tipo_exame( cd_tp_exame );
ALTER TABLE "public".ppi_tipo_exame ADD CONSTRAINT fk_ppi_tipo_exame_ppi_secretaria FOREIGN KEY ( cd_ppi_secretaria ) REFERENCES "public".ppi_secretaria( cd_ppi_secretaria );

CREATE TABLE auditschema.ppi_secretaria
    AS SELECT t2.*, t1.* FROM ppi_secretaria t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_ppi_secretaria;
alter table auditschema.ppi_secretaria add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ppi_secretaria FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.ppi_tipo_exame
    AS SELECT t2.*, t1.* FROM ppi_tipo_exame t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_ppi_tipo_exame;
alter table auditschema.ppi_tipo_exame add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ppi_tipo_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.ppi_exame
    AS SELECT t2.*, t1.* FROM ppi_exame t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_ppi_exame;
alter table auditschema.ppi_exame add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ppi_exame FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

CREATE TABLE auditschema.ppi_ocorrencia
    AS SELECT t2.*, t1.* FROM ppi_ocorrencia t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_ppi_ocorrencia;
alter table auditschema.ppi_ocorrencia add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ppi_ocorrencia FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

INSERT INTO agendador_processo VALUES (76,76,'Resgate de Saldo PPI Não Utilizado de Competência Anterior', 'Resgate de saldo de cota PPI não utilizada na competência anterior para competência atual',0,null,null,null,null,null,null,null,null,null,null,null,null,null,0,null,4);

INSERT INTO programa_pagina_permissao VALUES(559, 5, 252, 0, 'copiar');
INSERT INTO programa_pagina_permissao VALUES(560, 1, 252, 0, 'consultar');
INSERT INTO programa_pagina_permissao VALUES(561, 3, 252, 0, 'editar');
INSERT INTO programa_pagina_permissao VALUES(562, 4, 252, 0, 'remover');
INSERT INTO programa_pagina_permissao VALUES(563, 39, 252, 0, 'ocorrencias');
INSERT INTO programa_pagina_permissao VALUES(564, 18, 252, 0, 'empresaPPI');
INSERT INTO programa_pagina_permissao VALUES(565, 32, 252, 0, 'cadastrarPPI');
INSERT INTO programa_pagina_permissao VALUES(571, 40, 252, 0, 'transferir');
insert into programa_pagina VALUES(1857, 'br.com.celk.view.unidadesaude.exames.ppi.ConsultaOcorrenciaPpiPage', 'N');
insert into programa_web_pagina VALUES(1950, 155, 1857);


/*
    Claudio - AMB-590 - 30/10/2021
*/
insert into programa_pagina_permissao values (573, 156, 1726, 0, 'permissaoNaoValidarSaldoLimite');
insert into programa_pagina_permissao values (574, 156, 8, 0, 'permissaoNaoValidarSaldoLimite');



/*
    João Miguel 21/10/2021 - GMT-599
*/
INSERT INTO public.grupos
(cd_grupo, nm_grupo, "version", utilidade)
VALUES((select nextval('public.seq_grupos')), 'Controlador Pedido Limite', 0, 'Dá permissão de inserir os valores limites dos produtos a serem enviados a Branet por Ano');

/*
    João Miguel 22/10/2021 - GMT-612
*/
INSERT INTO programa_pagina VALUES (1860, 'br.com.celk.view.materiais.pedidolimite.ConsultaLimitePedidoBranetPage', 'N');
INSERT INTO programa_pagina VALUES (1861, 'br.com.celk.view.materiais.pedidolimite.CadastroLimitePedidoBranetPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (1029, 'Consulta Pedido Limite', 1860, 'S');
INSERT INTO programa_web_pagina VALUES (1953, 1029, 1860);
INSERT INTO programa_web_pagina VALUES (1954, 1029, 1861);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1000043,'Pedido Limite','limitePedidoBranet',226,1029,14,0,0);
INSERT INTO programa_pagina_permissao VALUES(575, 32, 1860, 0, 'cadastrar');

UPDATE programa_pagina_permissao SET ds_bundle = 'permissaoNaoValidarSaldoLimiteIndividual' WHERE cd_permissao = 156 AND cd_prg_pagina = 8;
UPDATE programa_pagina_permissao SET ds_bundle = 'permissaoNaoValidarSaldoLimiteGrupo' WHERE cd_permissao = 156 AND cd_prg_pagina = 1726;

--Criar a tabela nova pedido_limite_branet
CREATE TABLE public.pedido_limite_branet (
	cd_pedido_limite int8 NOT NULL,
	empresa int4 NOT NULL,
	cd_usuario numeric(6) NOT NULL,
	cod_pro varchar(13) NOT NULL,
	dt_cadastro date NOT NULL,
	qtd_maxima_anual numeric(12, 4) NOT NULL,
	qtd_solicitada numeric(12, 2) NOT NULL,
	"version" int8 NOT NULL DEFAULT 0,
	CONSTRAINT pk_int_cd_pedido_limite PRIMARY KEY (cd_pedido_limite),
	CONSTRAINT pedido_limite_branet_fk_produto FOREIGN KEY (cod_pro) REFERENCES public.produtos(cod_pro),
	CONSTRAINT pedido_limite_branet_fk_empresa FOREIGN KEY (empresa) REFERENCES public.empresa(empresa),
	CONSTRAINT pedido_limite_branet_fk_usuarios FOREIGN KEY (cd_usuario) REFERENCES public.usuarios(cd_usuario)
);

CREATE SEQUENCE seq_pedido_limite_branet INCREMENT 1 START 1;

CREATE TABLE auditschema.pedido_limite_branet AS SELECT t2.*, t1.* FROM pedido_limite_branet t1, audit_temp t2 WHERE 1=2;
CREATE sequence seq_audit_id_pedido_limite_branet;
ALTER TABLE auditschema.pedido_limite_branet
ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON pedido_limite_branet for each ROW EXECUTE PROCEDURE process_emp_audit();
