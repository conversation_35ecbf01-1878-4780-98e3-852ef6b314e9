SET application_name = 'flyway|3.1.39';

SET statement_timeout TO 600000;


/*
    Elton - GMT-210 - 25/06/2020
*/
CREATE TABLE profissional_sem_vinculo (
	cd_profissional_sem_vinculo			int8			NOT NULL,
	cd_conselho_classe					int8			NULL,
	nm_profissional						varchar(200)	NOT NULL,
	nr_conselho							varchar(60)		NULL,
	uf_conselho_classe					varchar(2)  	NULL,
	status								int2			NOT null,
	cd_usuario							int8			NOT NULL,
	dt_cadastro							timestamp   	NOT NULL,
	version								int8			NOT NULL
);

alter table profissional_sem_vinculo add constraint pk_profissional_sem_vinculo primary key (cd_profissional_sem_vinculo);

alter table profissional_sem_vinculo add constraint FK_PROF_SEM_V_REF_ORG_EMISSOR foreign key (cd_conselho_classe) references orgao_emissor (cd_orgao_emissor) on delete restrict on update restrict;
alter table profissional_sem_vinculo add constraint FK_PROF_SEM_V_REF_USUARIOS foreign key (cd_usuario) references usuarios (cd_usuario) on delete restrict on update restrict;

CREATE TABLE auditschema.profissional_sem_vinculo AS SELECT t2.*, t1.* FROM profissional_sem_vinculo t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_profissional_sem_vinculo;
alter table auditschema.profissional_sem_vinculo add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON profissional_sem_vinculo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create sequence seq_profissional_sem_vinculo;

INSERT INTO programa_pagina VALUES (1834, 'br.com.celk.view.basico.profissionalsemvinculo.ConsultaProfissionalSemVinculoPage', 'N');
INSERT INTO programa_pagina VALUES (1835, 'br.com.celk.view.basico.profissionalsemvinculo.CadastroProfissionalSemVinculoPage', 'N');
INSERT INTO programa_web VALUES (1010, 'Profissional Não Vinculado a Unidade', 1834, 'N');
INSERT INTO programa_web_pagina VALUES (1926, 1010, 1834);
INSERT INTO programa_web_pagina VALUES (1927, 1010, 1835);
INSERT INTO menu_web VALUES (1257, 'Profissional Não Vinculado a Unidade', 'profissionalSemVinculo', 177, 1010, 0, 0, 0);

alter table dispensacao_medicamento add cd_profissional_sem_vinculo INT8 null;
alter table auditschema.dispensacao_medicamento add cd_profissional_sem_vinculo INT8 null;

alter table dispensacao_medicamento add constraint FK_DISP_REF_PROF_SEM_VINCULO foreign key (cd_profissional_sem_vinculo) references profissional_sem_vinculo (cd_profissional_sem_vinculo) on delete restrict on update restrict;

ALTER TABLE dispensacao_medicamento ALTER COLUMN nm_profissional DROP NOT NULL;
ALTER TABLE auditschema.dispensacao_medicamento ALTER COLUMN nm_profissional DROP NOT NULL;

/*
    Maicon - 03/07/2020 - VSA-1187
*/

 update requerimento_vistoria_hidro t0
 set cd_vigilancia_alto_baixo_risco = (select t1.cd_vigilancia_alto_baixo_risco
 										from vigilancia_alto_baixo_risco t1
 										where t1.regiao_coberta_rede_esgoto = t0.regiao_coberta_rede_esgoto
 										and t1.regiao_abastecida_agua = t0.regiao_abastecida_agua)
where t0.cd_vigilancia_alto_baixo_risco is null;