SET application_name = 'flyway|3.1.9.3';

/*
   Elton  16/04/2018  - #17818
*/
alter table registro_agravo alter column cd_cid drop not null;
alter table registro_agravo alter column cd_profissional drop not null;
alter table registro_agravo alter column cd_usuario drop not null;

alter table registro_agravo add cd_registro_agravo_integracao int8 null;
alter table auditschema.registro_agravo add column cd_registro_agravo_integracao int8 null;
alter table registro_agravo add ds_empresa_integracao varchar(60) null;
alter table auditschema.registro_agravo add column ds_empresa_integracao varchar(60) null;
alter table registro_agravo add ds_profissional_integracao varchar(60) null;
alter table auditschema.registro_agravo add column ds_profissional_integracao varchar(60) null;
alter table registro_agravo add dt_integracao timestamp null;
alter table auditschema.registro_agravo add column dt_integracao timestamp null;

/*  Celk Saúde - 0  Hospital   - 1 */
alter table registro_agravo add column origem int2;
alter table auditschema.registro_agravo add column origem int2;

INSERT INTO integracao_identificacao (codigo,nm_integracao,id,chave,version,habilitado) VALUES (7,'REGISTRO_AGRAVO','ofw7kfhs5hdgktu7bloshn9ohcuiafdf4u1g','qlj5kdl1kcn0htys4aizrlb6mrd9hfyoasdn', 0, 0);

/*
    Silvio - 22/05/2018 - #18757
*/

ALTER TABLE codigo_barras_produto ADD cd_pedido_item INT8;
ALTER TABLE auditschema.codigo_barras_produto ADD cd_pedido_item INT8;

/*
    Laudecir - 31/05/2018 - #19080
*/
alter table codigo_barras_produto drop constraint fk_cod_bar_prod_ref_gr_est;

alter table codigo_barras_produto
        add constraint FK_COD_BAR_PROD_REF_EMPRESA foreign key (empresa)
        references empresa (empresa)
        on delete restrict on update restrict;

alter table codigo_barras_produto
        add constraint FK_COD_BAR_PROD_REF_PRODUTO foreign key (cod_pro)
        references produtos (cod_pro)
        on delete restrict on update restrict;

/*
    Everton - 14/06/2018 - #19080
*/

alter table codigo_barras_produto alter empresa drop not null;
alter table codigo_barras_produto alter cod_deposito drop not null;
alter table codigo_barras_produto alter cd_localizacao_estrutura drop not null;