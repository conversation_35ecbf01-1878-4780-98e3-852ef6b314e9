SET application_name = 'flyway|3.0.72';

/*
    Leonardo 16/10/2017 -  #16271
*/

ALTER TABLE registro_inspecao ALTER COLUMN cd_estabelecimento DROP NOT NULL;
ALTER TABLE auditschema.registro_inspecao ALTER COLUMN cd_estabelecimento DROP NOT NULL;

ALTER TABLE registro_inspecao ADD COLUMN tipo_inspecionado int2 null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN tipo_inspecionado int2 null;

ALTER TABLE registro_inspecao ADD COLUMN tipo_pessoa int2 null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN tipo_pessoa int2 null;

ALTER TABLE registro_inspecao ADD COLUMN nm_inspecionado VARCHAR(150) null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN nm_inspecionado VARCHAR(150) null;

ALTER TABLE registro_inspecao ADD COLUMN nm_fantasia VARCHAR null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN nm_fantasia VARCHAR null;

ALTER TABLE registro_inspecao ADD COLUMN cnpj_cpf VARCHAR(15) null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN cnpj_cpf VARCHAR(15) null;

ALTER TABLE registro_inspecao ADD COLUMN ds_representante_legal VARCHAR null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN ds_representante_legal VARCHAR null;

ALTER TABLE registro_inspecao ADD COLUMN cd_vigilancia_endereco int8 null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN cd_vigilancia_endereco int8 null;
ALTER TABLE registro_inspecao
   add constraint FK_REGISTRO_INSPECAO_ENDERECO foreign key (cd_vigilancia_endereco)
      references vigilancia_endereco (cd_vigilancia_endereco)
      on delete restrict on update restrict;


ALTER TABLE registro_inspecao ADD COLUMN cd_atividade_estabelecimento int8 null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN cd_atividade_estabelecimento int8 null;
ALTER TABLE registro_inspecao
   add constraint FK_REGISTRO_INSPECAO_ATIVIDADE_ESTABELECIMENTO foreign key (cd_atividade_estabelecimento)
      references atividade_estabelecimento (cd_atividade_estabelecimento)
      on delete restrict on update restrict;

ALTER TABLE registro_inspecao ADD COLUMN numero_endereco VARCHAR(6) null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN numero_endereco VARCHAR(6) null;

ALTER TABLE registro_inspecao ADD COLUMN telefone VARCHAR(20) null;
ALTER TABLE auditschema.registro_inspecao ADD COLUMN telefone VARCHAR(20) null;


UPDATE registro_inspecao t1 SET tipo_inspecionado = 1; --registros antigos sempre são estabelecimentos, pois só existia esta forma de armazenamento

UPDATE registro_inspecao t1
   SET cd_vigilancia_endereco = e1.cd_vigilancia_endereco,
       nm_inspecionado = e1.razao_social,
       cnpj_cpf = e1.cnpj_cpf,
       nm_fantasia = e1.fantasia,
       tipo_pessoa =  e1.tp_pessoa,
       ds_representante_legal = e1.rep_legal_nome,
       numero_endereco = e1.nr_logradouro,
       telefone = e1.telefone,
       cd_atividade_estabelecimento = (SELECT cd_atividade_estabelecimento FROM estabelecimento_atividade s1 WHERE (s1.cd_estabelecimento = e1.cd_estabelecimento AND s1.flag_principal = 1))
  FROM estabelecimento e1
 WHERE e1.cd_estabelecimento = t1.cd_estabelecimento;

create table atendimento_soap (
    cd_atendimento_soap                  BIGINT            not null,
    nr_atendimento                       BIGINT            not null,
    cd_profissional                      BIGINT            not null,
    subjetivo                            VARCHAR           null,
    objetivo                             VARCHAR           null,
    avaliacao                            VARCHAR           null,
    plano                                VARCHAR           null,
    ds_alergico                          VARCHAR(300)      null,
    dt_primeiros_sintomas                DATE              null,
    com_sangue                           int2              null,
    resultado_exame_lab                  VARCHAR(50)       null,
    plano_tratamento                     CHAR              null,
    dt_cadastro                          TIMESTAMP         not null,
    dt_alteracao                         TIMESTAMP         not null,
    cd_usuario                           NUMERIC(6)        not null,
    version                              BIGINT            not null,
    constraint PK_ATEND_SOAP primary key (cd_atendimento_soap),
    constraint FK_ATEND_SOAP_REF_ATENDIMENTO foreign key (nr_atendimento) references atendimento (nr_atendimento),
    constraint FK_ATEND_SOAP_REF_PROF foreign key (cd_profissional) references profissional (cd_profissional),
    constraint FK_ATEND_SOAP_REF_USUARIO foreign key (cd_usuario) references usuarios (cd_usuario)
);

CREATE TABLE auditschema.atendimento_soap AS SELECT t2.*, t1.* FROM atendimento_soap t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_atendimento_soap;alter table auditschema.atendimento_soap add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON atendimento_soap FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

/*
    Izael - 18/10/2017 -  #16311
*/
update programa_web set ds_prg_web = 'Relatório Acompanhamento Pré-Natal' where cd_prg_web = 451;
update menu_web set ds_menu = 'Relatório Acompanhamento Pré-Natal' , ds_bundle = 'relacaoAcompanhamentoPreNatal' where cd_menu = 616;

/*
    Izael - 18/10/2017 -  #16321
*/
alter table atendimento_odonto_plano alter column observacao  type varchar(200);
alter table auditschema.atendimento_odonto_plano alter column observacao  type varchar(200);
alter table atendimento_odonto_ficha alter column observacao  type varchar(800);
alter table auditschema.atendimento_odonto_ficha alter column observacao  type varchar(800);

/*
    Izael - 18/10/2017 -  #16335
*/
alter table veiculo add column situacao int2 null;
alter table auditschema.veiculo add column situacao int2 null;
update veiculo set situacao = 0;
alter table veiculo alter column situacao set not null;

/*
   Izael  - 25/10/2017 -  #16379
*/

ALTER TABLE medicamento_descricao ADD COLUMN cd_usuario NUMERIC(6)  null;
ALTER TABLE auditschema.medicamento_descricao ADD COLUMN cd_usuario NUMERIC(6)  null;
ALTER TABLE medicamento_descricao
   add constraint FK_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;


/*
    Leonardo 24/10/2017 -  #16368
*/

ALTER TABLE lancamento_atividades_vigilancia ADD COLUMN cd_conta_paciente int8 null;
ALTER TABLE auditschema.lancamento_atividades_vigilancia ADD COLUMN cd_conta_paciente int8 null;
ALTER TABLE lancamento_atividades_vigilancia
   add constraint FK_LANCAMENTO_ATIVIDADE_CONTA foreign key (cd_conta_paciente)
      references conta_paciente (cd_conta_paciente)
      on delete restrict on update restrict;


/*
   Izael  - 27/10/2017 -  ##16396
*/

alter table configuracao_vigilancia add column obrigatoriedade_refrigerado int2 null;
alter table auditschema.configuracao_vigilancia add column obrigatoriedade_refrigerado int2 null;


/*
    Leonardo 26/10/2017 -  #16271
*/
UPDATE requerimento_vigilancia t1
   SET cnpj_cpf = e1.cnpj_cpf,
       telefone = e1.telefone,
       cd_vigilancia_endereco = e1.cd_vigilancia_endereco
  FROM requerimento_defesa_previa e1
 WHERE e1.cd_req_vigilancia = t1.cd_req_vigilancia;

UPDATE requerimento_vigilancia t1
   SET cnpj_cpf = e1.cnpj_cpf,
       telefone = e1.telefone,
       cd_vigilancia_endereco = e1.cd_vigilancia_endereco
  FROM requerimento_prorrogacao_prazo e1
 WHERE e1.cd_req_vigilancia = t1.cd_req_vigilancia;

UPDATE requerimento_vigilancia t1
   SET cnpj_cpf = e1.cnpj_cpf,
       telefone = e1.telefone,
       cd_vigilancia_endereco = e1.cd_vigilancia_endereco
  FROM requerimento_declaracao_veracidade e1
 WHERE e1.cd_req_vigilancia = t1.cd_req_vigilancia;

/*
    Laudecir - 30/10/2017 - #16421
*/
UPDATE esus_ficha_usuario_cadsus_esus efuce
   SET parentesco_responsavel = null
  FROM usuario_cadsus_esus uce
  JOIN usuario_cadsus uc ON(uc.cd_usu_cadsus = uce.cd_usu_cadsus)
 WHERE uce.cd_usu_cadsus_esus = efuce.cd_usu_cadsus_esus
   AND uc.flag_responsavel_familiar = 1 -- SIM
   AND efuce.parentesco_responsavel is not null;

UPDATE usuario_cadsus_esus uce
   SET parentesco_responsavel = null
  FROM usuario_cadsus uc
 WHERE uc.cd_usu_cadsus = uce.cd_usu_cadsus
   AND uc.flag_responsavel_familiar = 1 -- SIM
   AND uce.parentesco_responsavel is not null;

/*
   Leonardo - 06/11/2017 - #16368
*/
alter table lancamento_atividades_vigilancia disable trigger user;

   UPDATE lancamento_atividades_vigilancia l
    SET cd_conta_paciente = c.cd_conta_paciente
   FROM conta_paciente c
   WHERE (l.cd_lancamento_atividades_vigilancia = c.cd_lancamento_atividades_vigilancia)
     AND (c.flag_origem = 8);

alter table lancamento_atividades_vigilancia enable trigger user;

alter table conta_paciente disable trigger user;

   UPDATE conta_paciente
    SET cd_lancamento_atividades_vigilancia = null
   WHERE cd_lancamento_atividades_vigilancia IS NOT NULL;

alter table conta_paciente enable trigger user;

/*
    Laudecir - 03/11/2017 - #16451
*/
alter table atendimento add column cd_cbo_auxiliar varchar(10);
alter table auditschema.atendimento add column cd_cbo_auxiliar varchar(10);

ALTER TABLE atendimento
   add constraint FK_ATENDIMENTO_REF_TABELA_CBO_AUXILIAR foreign key (cd_cbo_auxiliar)
      references tabela_cbo (cd_cbo)
      on delete restrict on update restrict;


/*
    Izael  - 07/11/2017 - #16463
*/

ALTER TABLE atendimento_exame ADD COLUMN cd_tp_anexo int8 null;
ALTER TABLE auditschema.atendimento_exame ADD COLUMN cd_tp_anexo int8 null;
ALTER TABLE atendimento_exame
   add constraint FK_TIPO_ANEXO foreign key (cd_tp_anexo)
      references tipo_anexo (cd_tp_anexo)
      on delete restrict on update restrict;

/*
    Izael   - #16526
*/

alter table configuracao_vigilancia add column obrigatoriedade_anexo int2 null;
alter table auditschema.configuracao_vigilancia add column obrigatoriedade_anexo int2 null;

/*
    Evandro - 23/11/2017 - #16638
*/

UPDATE motivo_visita_domiciliar
   SET tp_motivo = 3
 WHERE cd_motivo_visita IN (34, 35, 36, 37);

/*
    Evandro - 27/11/2017 - #16685
*/

ALTER TABLE usuario_cadsus ADD profissao VARCHAR(50) null;
ALTER TABLE auditschema.usuario_cadsus ADD profissao VARCHAR(50) null;

/*
    Everton - 05/01/2018
*/

create table t_esus_integracao (cd_dom_esus int8, cd_domicilio int8);

insert into t_esus_integracao
SELECT a1.cd_esus_ficha_endereco_domicilio_esus, b3.cd_domicilio
  FROM esus_integracao_cds a1, esus_ficha_endereco_domicilio_esus b2, endereco_domicilio_esus b3
 WHERE
   a1.cd_esus_ficha_endereco_domicilio_esus = b2.cd_esus_ficha_endereco_domicilio_esus
   and b2.cd_end_dom_esus = b3.cd_end_dom_esus
   and (a1.cd_exp_proc is null or (a1.cd_exp_proc is not null and a1.uuid is null))
   and exists (select 1 from esus_ficha_endereco_domicilio_esus a2, endereco_domicilio_esus a3
                 where a1.cd_esus_ficha_endereco_domicilio_esus = a2.cd_esus_ficha_endereco_domicilio_esus
                   and a2.cd_end_dom_esus = a3.cd_end_dom_esus
                   and a3.cd_domicilio in (SELECT t3.cd_domicilio
	       					FROM esus_integracao_cds t1, esus_ficha_endereco_domicilio_esus t2, endereco_domicilio_esus t3
						where (cd_exp_proc is null or (cd_exp_proc is not null and uuid is null))
						and t1.cd_esus_ficha_endereco_domicilio_esus is not null
						and t1.cd_esus_ficha_endereco_domicilio_esus = t2.cd_esus_ficha_endereco_domicilio_esus
						and t2.cd_end_dom_esus = t3.cd_end_dom_esus
						group by 1
						having count(*) > 1));
delete FROM esus_integracao_cds a1
 WHERE (cd_exp_proc is null or (cd_exp_proc is not null and uuid is null))
   and cd_esus_ficha_endereco_domicilio_esus in (select min(cd_dom_esus) from t_esus_integracao group by cd_domicilio);

drop table t_esus_integracao;

/*
    Leonardo - #16888
*/

ALTER TABLE movimentacao_financeira alter ds_movimentacao type VARCHAR(100);
ALTER TABLE auditschema.movimentacao_financeira alter ds_movimentacao type VARCHAR(100);

/*
    Silvio - #16977
*/

ALTER TABLE configuracao_vigilancia ADD COLUMN num_relatorio_inspecao int8 null;
ALTER TABLE auditschema.configuracao_vigilancia ADD COLUMN num_relatorio_inspecao int8 null;
ALTER TABLE relatorio_inspecao ADD COLUMN num_relatorio_inspecao int8 null;
ALTER TABLE auditschema.relatorio_inspecao ADD COLUMN num_relatorio_inspecao int8 null;

/*
    Maicon - Nova tabela de medicamentos
*/
alter table produtos alter column descricao type varchar(200);
alter table auditschema.produtos alter column descricao type varchar(200);

/*
    Izael - #16379
*/

ALTER TABLE medicamento_descricao ADD COLUMN cd_profissional INT4  null;
ALTER TABLE auditschema.medicamento_descricao ADD COLUMN cd_profissional INT4  null;
ALTER TABLE medicamento_descricao
   add constraint FK_PROFISSIONAL foreign key (cd_profissional)
      references profissional (cd_profissional)
      on delete restrict on update restrict;

/*
    Izael - #17072
*/

alter table consorcio_guia_procedimento ADD cd_endereco INT8 null;
alter table auditschema.consorcio_guia_procedimento ADD cd_endereco INT8 null;

alter table consorcio_guia_procedimento
   add constraint FK_CONS_GUIA_PROC_REF_ENDERECO foreign key (cd_endereco)
      references endereco_usuario_cadsus (cd_endereco)
      on delete restrict on update restrict;

/*
    Everton - #17072
*/

alter table consorcio_guia_procedimento disable trigger user;
update consorcio_guia_procedimento t1 set cd_endereco = (select cd_endereco from usuario_cadsus where cd_usu_cadsus = t1.cd_usu_cadsus)
  where cd_usu_cadsus is not null;
alter table consorcio_guia_procedimento enable trigger user;
