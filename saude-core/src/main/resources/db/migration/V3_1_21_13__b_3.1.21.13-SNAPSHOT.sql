SET application_name = 'flyway|3.1.21.13';
SET statement_timeout TO 600000;

/*
 Everton - #25618
*/


CREATE TABLE ped_transf_licitacao_elo_oc (
  cd_ped_transf_elo_oc   int8 NOT NULL,
  cd_ped_transf_lic_item int8 NOT NULL,
  cd_oc_item             int8 NOT NULL,
  qtd_compra             numeric(12,2) NOT NULL,  
  version                                   int8 NOT NULL,
  CONSTRAINT pk_ped_transf_licitacao_elo_oc PRIMARY KEY (cd_ped_transf_elo_oc),
  CONSTRAINT fk_ped_transf_elo_oc_ref_ped_transf FOREIGN KEY (cd_ped_transf_lic_item) 
     REFERENCES ped_transf_licitacao_item (cd_ped_transf_lic_item) ON UPDATE RESTRICT ON DELETE RESTRICT,
  CONSTRAINT fk_ped_transf_elo_oc_ref_oc FOREIGN KEY (cd_oc_item) 
     REFERENCES ordem_compra_item (cd_oc_item) ON UPDATE RESTRICT ON DELETE RESTRICT
);
CREATE TABLE auditschema.ped_transf_licitacao_elo_oc AS SELECT t2.*, t1.* FROM ped_transf_licitacao_elo_oc t1, audit_temp t2 WHERE 1=2;
create sequence seq_audit_id_ped_transf_licitacao_elo_oc;
alter table auditschema.ped_transf_licitacao_elo_oc add primary key (audit_id);
CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON ped_transf_licitacao_elo_oc FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

alter table ordem_compra add cd_ped_transf_lic int8;
alter table auditschema.ordem_compra add cd_ped_transf_lic int8;

alter table ordem_compra add empresa_consorciado int8;
alter table auditschema.ordem_compra add empresa_consorciado int8;

create index idx_ordem_compra_001 on ordem_compra(cd_ped_transf_lic);

alter table ordem_compra_item add qtd_compra_original numeric(12,2);
alter table auditschema.ordem_compra_item add qtd_compra_original numeric(12,2);

alter table ordem_compra_item add qtd_compra_ped_transf numeric(12,2);
alter table auditschema.ordem_compra_item add qtd_compra_ped_transf numeric(12,2);

/*
    Sulivan - 23/10/2019
*/
INSERT INTO programa_pagina VALUES (1754, 'br.com.celk.view.materiais.catmat.ConsultaMedicamentoCatmatPage', 'N');
INSERT INTO programa_pagina VALUES (1755, 'br.com.celk.view.materiais.catmat.CadastroMedicamentoCatmatPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (972, 'CATMAT', 1754, 'N');
INSERT INTO programa_web_pagina VALUES (1876, 972, 1754);
INSERT INTO programa_web_pagina VALUES (1877, 972, 1755);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1222,'CATMAT','catmat',166,972,14,0,0);

/*
    Sulivan - #25489 - 23/10/2019
*/

ALTER TABLE grupo_estoque DISABLE trigger user;
update grupo_estoque ge set cd_fabricante = (select min(t1.cd_fabricante) from grupo_estoque t1 where cd_fabricante is not null and t1.cod_pro = ge.cod_pro and ge.grupo_estoque = t1.grupo_estoque)
where ge.cd_fabricante is null and exists(select 1 from grupo_estoque t1 where cd_fabricante is not null and t1.cod_pro = ge.cod_pro and ge.grupo_estoque = t1.grupo_estoque);
ALTER TABLE grupo_estoque ENABLE trigger user;

/*
    Sulivan - #28230 - 23/10/2019
*/
-- Seta para Pendente as denúncias que estão em Monitoramento
update denuncia set status = 1 where cd_req_vigilancia is null and status = 0;

create sequence seq_protocolo_visa start 1;

do $$
declare maxid int;
begin
    select cast(iniciar_seq AS INT8) + 1 from configuracao_vigilancia into maxid;
    execute 'alter SEQUENCE seq_protocolo_visa RESTART with '|| coalesce(maxid, 1);
end;
$$ language plpgsql;

alter table requerimento_vigilancia add column cd_denuncia INT8;
alter table auditschema.requerimento_vigilancia add column cd_denuncia INT8;

-- DENUNCIA TIPO PESSOA - PENDENTE
INSERT INTO requerimento_vigilancia
(       cd_req_vigilancia,  cd_tipo_solicitacao,                                                                                  cd_estabelecimento, nm_solicitante,                                              rg_cpf_solicitante, cargo_solicitante, telefone_solicitante,                                                    situacao, data_requerimento, version, cd_veiculo, data_inicio_validade, data_final_validade, descricao_periodo_validade, dt_nascimento_solicitante, naturalidade_solicitante, endereco_solicitante,                                                   carteira_pro_solicitante, protocolo_solicitante, dt_palestra, cd_profissional_palestra, protocolo,                     tipo_documento, motivo_cancelamento, motivo_finalizacao, dt_finalizacao, cd_usuario_can, cd_usuario_fin, dt_cancelamento, dt_analise, cd_vigilancia_profissional, nome,                                                                                     cd_setor_vigilancia, origem, chave_qrcode, cd_vigilancia_endereco, telefone, cnpj_cpf, parentesco_solicitante, email_solicitante,                                                 cpf_solicitante,                                                      rg_solicitante, celular_solicitante,                                                     cd_usuario_cad, cd_usuario_alteracao, dt_usuario,     dt_integracao, flag_integrado, numero, complemento, dt_entrega, nome_entrega, vinculo_entrega, cd_tipo_logradouro, obs_requerimento, dt_emissao_rg_solic, situacao_ocorrencia, cd_estabelecimento_setores, qtd_taxa, valor_taxa, valor_cobrado, tp_pessoa,                                         situacao_aprovacao, dt_aprovacao, motivo_aprovacao, cd_vigilancia_financeiro, situacao_analise_projetos, dt_inspecao, flag_isento_mei, descricao_isento_outro, empresa_cadastro, cd_vigilancia_pessoa,            tp_requerente, cnpj_solicitante, situacao_anterior_conclusao, motivo_reversao_finalizacao, situacao_fin_complementar, cd_denuncia)
(SELECT nextval('seq_gem'), (select cd_tipo_solicitacao from tipo_solicitacao where ds_tipo_solicitacao = 'Denúncia/Reclamação'), null,               (case when flag_anonimo = 0 then denunciante else null end), null,               null,              (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), 9,        t1.dt_cadastro,    0,       null,       null,                 null,                null,                       null,                      null,                     (case when flag_anonimo = 0 then logradouro_denunciante else null end), null,                     null,                  null,        null,                     nextval('seq_protocolo_visa'), 39,             null,                null,               null,           null,           null,           null,            null,       null,                       (case when flag_anonimo = 0 then (t2.nome || ' - ' || t2.nome_fantasia) else null end),  null,                0,      null,       t1.cd_vigilancia_endereco, null,     null,     null,                   (case when flag_anonimo = 0 then email_denunciante else null end), (case when flag_anonimo = 0 then cnpj_cpf_denunciante else null end), null,           (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), t1.cd_usuario,  t1.cd_usuario,        t1.dt_cadastro, null,          0 ,             null,   null,        null,       null,         null,            null,               null,             null,                0,                   null,                       null,     null,       null ,         (case when flag_anonimo = 0 then 0 else null end), 0,                  null,         null,             null,                     null,                      null,        null,            null,                   null,             cd_vigilancia_pessoa_denunciada, null,          null,             null,                        null,                        0                        , cd_denuncia        FROM denuncia t1 left join vigilancia_pessoa t2 on(t1.cd_vigilancia_pessoa_denunciada = t2.cd_vigilancia_pessoa) where status = 1 and tp_denunciado = 0 and not exists(select * from requerimento_vigilancia rv where rv.cd_req_vigilancia = t1.cd_req_vigilancia));

-- DENUNCIA TIPO ESTABELECIMENTO - PENDENTE
INSERT INTO requerimento_vigilancia
(       cd_req_vigilancia,  cd_tipo_solicitacao,                                                                                  cd_estabelecimento,               nm_solicitante,                                              rg_cpf_solicitante, cargo_solicitante, telefone_solicitante,                                                    situacao, data_requerimento, version, cd_veiculo, data_inicio_validade, data_final_validade, descricao_periodo_validade, dt_nascimento_solicitante, naturalidade_solicitante, endereco_solicitante,                                                   carteira_pro_solicitante, protocolo_solicitante, dt_palestra, cd_profissional_palestra, protocolo,                     tipo_documento, motivo_cancelamento, motivo_finalizacao, dt_finalizacao, cd_usuario_can, cd_usuario_fin, dt_cancelamento, dt_analise, cd_vigilancia_profissional, nome,                                                                                       cd_setor_vigilancia, origem, chave_qrcode, cd_vigilancia_endereco,    telefone, cnpj_cpf, parentesco_solicitante, email_solicitante,                                                 cpf_solicitante, rg_solicitante, celular_solicitante,                                                     cd_usuario_cad, cd_usuario_alteracao, dt_usuario,  dt_integracao, flag_integrado, numero, complemento, dt_entrega, nome_entrega, vinculo_entrega, cd_tipo_logradouro, obs_requerimento, dt_emissao_rg_solic, situacao_ocorrencia, cd_estabelecimento_setores, qtd_taxa, valor_taxa, valor_cobrado, tp_pessoa,                                         situacao_aprovacao, dt_aprovacao, motivo_aprovacao, cd_vigilancia_financeiro, situacao_analise_projetos, dt_inspecao, flag_isento_mei, descricao_isento_outro, empresa_cadastro, cd_vigilancia_pessoa,            tp_requerente, cnpj_solicitante,                                                     situacao_anterior_conclusao, motivo_reversao_finalizacao, situacao_fin_complementar, cd_denuncia)
(SELECT nextval('seq_gem'), (select cd_tipo_solicitacao from tipo_solicitacao where ds_tipo_solicitacao = 'Denúncia/Reclamação'), t1.cd_estabelecimento_denunciado, (case when flag_anonimo = 0 then denunciante else null end), null,               null,              (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), 9,        t1.dt_cadastro,    0,       null,       null,                 null,                null,                       null,                      null,                     (case when flag_anonimo = 0 then logradouro_denunciante else null end), null,                     null,                  null,        null,                     nextval('seq_protocolo_visa'), 39,             null,                null,               null,           null,           null,           null,            null,       null,                       (case when flag_anonimo = 0 then (t2.razao_social || ' - ' || t2.fantasia) else null end),  null,                0,      null,         t1.cd_vigilancia_endereco, null,     null,     null,                   (case when flag_anonimo = 0 then email_denunciante else null end), null,            null,           (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), t1.cd_usuario,     t1.cd_usuario,           t1.dt_cadastro, null,          0 ,             null,   null,        null,       null,         null,            null,               null,             null,                0,                   null,                       null,     null,       null ,         (case when flag_anonimo = 0 then 1 else null end), 0,                  null,         null,             null,                     null,                      null,        null,            null,                   null,             cd_vigilancia_pessoa_denunciada, null,          (case when flag_anonimo = 0 then cnpj_cpf_denunciante else null end), null,                        null,                        0               , cd_denuncia        FROM denuncia t1 left join estabelecimento t2 on(t1.cd_estabelecimento_denunciado = t2.cd_estabelecimento) where status = 1 and tp_denunciado = 1 and not exists(select * from requerimento_vigilancia rv where rv.cd_req_vigilancia = t1.cd_req_vigilancia));

-- DENUNCIA TIPO PESSOA - CONCLUÍDO
INSERT INTO requerimento_vigilancia
(       cd_req_vigilancia,  cd_tipo_solicitacao,                                                                                  cd_estabelecimento, nm_solicitante,                                              rg_cpf_solicitante, cargo_solicitante, telefone_solicitante,                                                    situacao, data_requerimento, version, cd_veiculo, data_inicio_validade, data_final_validade, descricao_periodo_validade, dt_nascimento_solicitante, naturalidade_solicitante, endereco_solicitante,                                                   carteira_pro_solicitante, protocolo_solicitante, dt_palestra, cd_profissional_palestra, protocolo,                     tipo_documento, motivo_cancelamento, motivo_finalizacao, dt_finalizacao,  cd_usuario_can, cd_usuario_fin, dt_cancelamento, dt_analise, cd_vigilancia_profissional, nome,                                                                                     cd_setor_vigilancia, origem, chave_qrcode, cd_vigilancia_endereco, telefone, cnpj_cpf, parentesco_solicitante, email_solicitante,                                                 cpf_solicitante,                                                      rg_solicitante, celular_solicitante,                                                     cd_usuario_cad, cd_usuario_alteracao, dt_usuario,     dt_integracao, flag_integrado, numero, complemento, dt_entrega, nome_entrega, vinculo_entrega, cd_tipo_logradouro, obs_requerimento, dt_emissao_rg_solic, situacao_ocorrencia, cd_estabelecimento_setores, qtd_taxa, valor_taxa, valor_cobrado, tp_pessoa,                                         situacao_aprovacao, dt_aprovacao, motivo_aprovacao, cd_vigilancia_financeiro, situacao_analise_projetos, dt_inspecao, flag_isento_mei, descricao_isento_outro, empresa_cadastro, cd_vigilancia_pessoa,            tp_requerente, cnpj_solicitante, situacao_anterior_conclusao, motivo_reversao_finalizacao, situacao_fin_complementar, cd_denuncia)
(SELECT nextval('seq_gem'), (select cd_tipo_solicitacao from tipo_solicitacao where ds_tipo_solicitacao = 'Denúncia/Reclamação'), null,               (case when flag_anonimo = 0 then denunciante else null end), null,               null,              (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), 8,        t1.dt_cadastro,    0,       null,       null,                 null,                null,                       null,                      null,                     (case when flag_anonimo = 0 then logradouro_denunciante else null end), null,                     null,                  null,        null,                     nextval('seq_protocolo_visa'), 39,             null,                null,               t1.dt_conclusao, null,           null,           null,            null,       null,                       (case when flag_anonimo = 0 then (t2.nome || ' - ' || t2.nome_fantasia) else null end),  null,                0,      null,       t1.cd_vigilancia_endereco, null,     null,     null,                   (case when flag_anonimo = 0 then email_denunciante else null end), (case when flag_anonimo = 0 then cnpj_cpf_denunciante else null end), null,           (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), t1.cd_usuario,  t1.cd_usuario,        t1.dt_cadastro, null,          0 ,             null,   null,        null,       null,         null,            null,               null,             null,                0,                   null,                       null,     null,       null ,         (case when flag_anonimo = 0 then 0 else null end), 0,                  null,         null,             null,                     null,                      null,        null,            null,                   null,             cd_vigilancia_pessoa_denunciada, null,          null,             null,                        null,                        0                        , cd_denuncia        FROM denuncia t1 left join vigilancia_pessoa t2 on(t1.cd_vigilancia_pessoa_denunciada = t2.cd_vigilancia_pessoa) where status = 2 and tp_denunciado = 0 and not exists(select * from requerimento_vigilancia rv where rv.cd_req_vigilancia = t1.cd_req_vigilancia));

-- DENUNCIA TIPO ESTABELECIMENTO - CONCLUÍDO
INSERT INTO requerimento_vigilancia
(       cd_req_vigilancia,  cd_tipo_solicitacao,                                                                                  cd_estabelecimento,               nm_solicitante,                                              rg_cpf_solicitante, cargo_solicitante, telefone_solicitante,                                                    situacao, data_requerimento, version, cd_veiculo, data_inicio_validade, data_final_validade, descricao_periodo_validade, dt_nascimento_solicitante, naturalidade_solicitante, endereco_solicitante,                                                   carteira_pro_solicitante, protocolo_solicitante, dt_palestra, cd_profissional_palestra, protocolo,                     tipo_documento, motivo_cancelamento, motivo_finalizacao, dt_finalizacao,  cd_usuario_can, cd_usuario_fin, dt_cancelamento, dt_analise, cd_vigilancia_profissional, nome,                                                                                       cd_setor_vigilancia, origem, chave_qrcode, cd_vigilancia_endereco,    telefone, cnpj_cpf, parentesco_solicitante, email_solicitante,                                                 cpf_solicitante, rg_solicitante, celular_solicitante,                                                     cd_usuario_cad, cd_usuario_alteracao, dt_usuario,  dt_integracao, flag_integrado, numero, complemento, dt_entrega, nome_entrega, vinculo_entrega, cd_tipo_logradouro, obs_requerimento, dt_emissao_rg_solic, situacao_ocorrencia, cd_estabelecimento_setores, qtd_taxa, valor_taxa, valor_cobrado, tp_pessoa,                                         situacao_aprovacao, dt_aprovacao, motivo_aprovacao, cd_vigilancia_financeiro, situacao_analise_projetos, dt_inspecao, flag_isento_mei, descricao_isento_outro, empresa_cadastro, cd_vigilancia_pessoa,            tp_requerente, cnpj_solicitante,                                                     situacao_anterior_conclusao, motivo_reversao_finalizacao, situacao_fin_complementar, cd_denuncia)
(SELECT nextval('seq_gem'), (select cd_tipo_solicitacao from tipo_solicitacao where ds_tipo_solicitacao = 'Denúncia/Reclamação'), t1.cd_estabelecimento_denunciado, (case when flag_anonimo = 0 then denunciante else null end), null,               null,              (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), 8,        t1.dt_cadastro,    0,       null,       null,                 null,                null,                       null,                      null,                     (case when flag_anonimo = 0 then logradouro_denunciante else null end), null,                     null,                  null,        null,                     nextval('seq_protocolo_visa'), 39,             null,                null,               t1.dt_conclusao, null,           null,           null,            null,       null,                       (case when flag_anonimo = 0 then (t2.razao_social || ' - ' || t2.fantasia) else null end),  null,                0,      null,         t1.cd_vigilancia_endereco, null,     null,     null,                   (case when flag_anonimo = 0 then email_denunciante else null end), null,            null,           (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), t1.cd_usuario,     t1.cd_usuario,           t1.dt_cadastro, null,          0 ,             null,   null,        null,       null,         null,            null,               null,             null,                0,                   null,                       null,     null,       null ,         (case when flag_anonimo = 0 then 1 else null end), 0,                  null,         null,             null,                     null,                      null,        null,            null,                   null,             cd_vigilancia_pessoa_denunciada, null,          (case when flag_anonimo = 0 then cnpj_cpf_denunciante else null end), null,                        null,                        0               , cd_denuncia        FROM denuncia t1 left join estabelecimento t2 on(t1.cd_estabelecimento_denunciado = t2.cd_estabelecimento) where status = 2 and tp_denunciado = 1 and not exists(select * from requerimento_vigilancia rv where rv.cd_req_vigilancia = t1.cd_req_vigilancia));

-- DENUNCIA TIPO PESSOA - CANCELADO
INSERT INTO requerimento_vigilancia
(       cd_req_vigilancia,  cd_tipo_solicitacao,                                                                                  cd_estabelecimento, nm_solicitante,                                              rg_cpf_solicitante, cargo_solicitante, telefone_solicitante,                                                    situacao, data_requerimento, version, cd_veiculo, data_inicio_validade, data_final_validade, descricao_periodo_validade, dt_nascimento_solicitante, naturalidade_solicitante, endereco_solicitante,                                                   carteira_pro_solicitante, protocolo_solicitante, dt_palestra, cd_profissional_palestra, protocolo,                     tipo_documento, motivo_cancelamento, motivo_finalizacao, dt_finalizacao,  cd_usuario_can, cd_usuario_fin, dt_cancelamento,    dt_analise, cd_vigilancia_profissional, nome,                                                                                     cd_setor_vigilancia, origem, chave_qrcode, cd_vigilancia_endereco, telefone, cnpj_cpf, parentesco_solicitante, email_solicitante,                                                 cpf_solicitante,                                                      rg_solicitante, celular_solicitante,                                                     cd_usuario_cad, cd_usuario_alteracao, dt_usuario,     dt_integracao, flag_integrado, numero, complemento, dt_entrega, nome_entrega, vinculo_entrega, cd_tipo_logradouro, obs_requerimento, dt_emissao_rg_solic, situacao_ocorrencia, cd_estabelecimento_setores, qtd_taxa, valor_taxa, valor_cobrado, tp_pessoa,                                         situacao_aprovacao, dt_aprovacao, motivo_aprovacao, cd_vigilancia_financeiro, situacao_analise_projetos, dt_inspecao, flag_isento_mei, descricao_isento_outro, empresa_cadastro, cd_vigilancia_pessoa,            tp_requerente, cnpj_solicitante, situacao_anterior_conclusao, motivo_reversao_finalizacao, situacao_fin_complementar, cd_denuncia)
(SELECT nextval('seq_gem'), (select cd_tipo_solicitacao from tipo_solicitacao where ds_tipo_solicitacao = 'Denúncia/Reclamação'), null,               (case when flag_anonimo = 0 then denunciante else null end), null,               null,              (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), 7,        t1.dt_cadastro,    0,       null,       null,                 null,                null,                       null,                      null,                     (case when flag_anonimo = 0 then logradouro_denunciante else null end), null,                     null,                  null,        null,                     nextval('seq_protocolo_visa'), 39,             t1.mot_cancelamento, null,               null,            null,           null,           t1.dt_cancelamento, null,       null,                       (case when flag_anonimo = 0 then (t2.nome || ' - ' || t2.nome_fantasia) else null end),  null,                0,      null,       t1.cd_vigilancia_endereco, null,     null,     null,                   (case when flag_anonimo = 0 then email_denunciante else null end), (case when flag_anonimo = 0 then cnpj_cpf_denunciante else null end), null,           (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), t1.cd_usuario,  t1.cd_usuario,        t1.dt_cadastro, null,          0 ,             null,   null,        null,       null,         null,            null,               null,             null,                0,                   null,                       null,     null,       null ,         (case when flag_anonimo = 0 then 0 else null end), 0,                  null,         null,             null,                     null,                      null,        null,            null,                   null,             cd_vigilancia_pessoa_denunciada, null,          null,             null,                        null,                        0                        , cd_denuncia        FROM denuncia t1 left join vigilancia_pessoa t2 on(t1.cd_vigilancia_pessoa_denunciada = t2.cd_vigilancia_pessoa) where status = 3 and tp_denunciado = 0 and not exists(select * from requerimento_vigilancia rv where rv.cd_req_vigilancia = t1.cd_req_vigilancia));

-- DENUNCIA TIPO ESTABELECIMENTO - CANCELADO
INSERT INTO requerimento_vigilancia
(       cd_req_vigilancia,  cd_tipo_solicitacao,                                                                                  cd_estabelecimento,               nm_solicitante,                                              rg_cpf_solicitante, cargo_solicitante, telefone_solicitante,                                                    situacao, data_requerimento, version, cd_veiculo, data_inicio_validade, data_final_validade, descricao_periodo_validade, dt_nascimento_solicitante, naturalidade_solicitante, endereco_solicitante,                                                   carteira_pro_solicitante, protocolo_solicitante, dt_palestra, cd_profissional_palestra, protocolo,                     tipo_documento, motivo_cancelamento, motivo_finalizacao, dt_finalizacao,  cd_usuario_can, cd_usuario_fin, dt_cancelamento,    dt_analise, cd_vigilancia_profissional, nome,                                                                                       cd_setor_vigilancia, origem, chave_qrcode, cd_vigilancia_endereco,    telefone, cnpj_cpf, parentesco_solicitante, email_solicitante,                                                 cpf_solicitante, rg_solicitante, celular_solicitante,                                                     cd_usuario_cad, cd_usuario_alteracao, dt_usuario,  dt_integracao, flag_integrado, numero, complemento, dt_entrega, nome_entrega, vinculo_entrega, cd_tipo_logradouro, obs_requerimento, dt_emissao_rg_solic, situacao_ocorrencia, cd_estabelecimento_setores, qtd_taxa, valor_taxa, valor_cobrado, tp_pessoa,                                         situacao_aprovacao, dt_aprovacao, motivo_aprovacao, cd_vigilancia_financeiro, situacao_analise_projetos, dt_inspecao, flag_isento_mei, descricao_isento_outro, empresa_cadastro, cd_vigilancia_pessoa,            tp_requerente, cnpj_solicitante,                                                     situacao_anterior_conclusao, motivo_reversao_finalizacao, situacao_fin_complementar, cd_denuncia)
(SELECT nextval('seq_gem'), (select cd_tipo_solicitacao from tipo_solicitacao where ds_tipo_solicitacao = 'Denúncia/Reclamação'), t1.cd_estabelecimento_denunciado, (case when flag_anonimo = 0 then denunciante else null end), null,               null,              (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), 7,        t1.dt_cadastro,    0,       null,       null,                 null,                null,                       null,                      null,                     (case when flag_anonimo = 0 then logradouro_denunciante else null end), null,                     null,                  null,        null,                     nextval('seq_protocolo_visa'), 39,             t1.mot_cancelamento, null,               null,            null,           null,           t1.dt_cancelamento, null,       null,                       (case when flag_anonimo = 0 then (t2.razao_social || ' - ' || t2.fantasia) else null end),  null,                0,      null,         t1.cd_vigilancia_endereco, null,     null,     null,                   (case when flag_anonimo = 0 then email_denunciante else null end), null,            null,           (case when flag_anonimo = 0 then nr_telefone_denunciante else null end), t1.cd_usuario,     t1.cd_usuario,           t1.dt_cadastro, null,          0 ,             null,   null,        null,       null,         null,            null,               null,             null,                0,                   null,                       null,     null,       null ,         (case when flag_anonimo = 0 then 1 else null end), 0,                  null,         null,             null,                     null,                      null,        null,            null,                   null,             cd_vigilancia_pessoa_denunciada, null,          (case when flag_anonimo = 0 then cnpj_cpf_denunciante else null end), null,                        null,                        0               , cd_denuncia        FROM denuncia t1 left join estabelecimento t2 on(t1.cd_estabelecimento_denunciado = t2.cd_estabelecimento) where status = 3 and tp_denunciado = 1 and not exists(select * from requerimento_vigilancia rv where rv.cd_req_vigilancia = t1.cd_req_vigilancia));

update configuracao_vigilancia set iniciar_seq = (select nextval('seq_protocolo_visa') -1);

drop sequence seq_protocolo_visa;

update requerimento_vigilancia set protocolo = cast(protocolo || '' || (select ano_base_geral from configuracao_vigilancia) as INT) where cd_denuncia is not null;

update denuncia t1 set cd_req_vigilancia = (select rv.cd_req_vigilancia from requerimento_vigilancia rv where rv.cd_denuncia = t1.cd_denuncia) where cd_req_vigilancia is null;

alter table requerimento_vigilancia drop column cd_denuncia;
alter table auditschema.requerimento_vigilancia drop column cd_denuncia;

update requerimento_vigilancia rv set nome = (select t1.denunciado from denuncia t1 where t1.cd_req_vigilancia = rv.cd_req_vigilancia) where tipo_documento = 39;