/*
    Leando - 13/03/2013
*/
/*==============================================================*/
/* Table: tipo_solicitacao                                      */
/*==============================================================*/
create table tipo_solicitacao (
cd_tipo_solicitacao  INT8                 not null,
ds_tipo_solicitacao  VARCHAR(50)          not null,
tipo_documento       INT2                 not null,
tipo_acao            int2                 not null,
version              INT8                 not null
);

alter table tipo_solicitacao
   add constraint PK_TIPO_SOLICITACAO primary key (cd_tipo_solicitacao);

/*
    Marcus - 13/03/2013
*/
insert into programa_pagina values(399, 'br.com.celk.view.vigilancia.tiposolicitacao.ConsultaTipoSolicitacaoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (244,'Tipo de Solicitação',399,'N');
insert into programa_web_pagina values (400, 244, 399);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (342,'Tipo de Solicitação','tipoSolicitacao',309,244,307);
insert into programa_pagina values(400, 'br.com.celk.view.vigilancia.tiposolicitacao.CadastroTipoSolicitacaoPage', 'N');
insert into programa_web_pagina values (401, 244, 400);

/*
    Leando - 13/03/2013
*/
/*==============================================================*/
/* Table: requerimento_vigilancia                               */
/*==============================================================*/
create table requerimento_vigilancia (
cd_req_vigilancia    INT8                 not null,
cd_tipo_solicitacao  INT8                 not null,
cd_estabelecimento   INT8                 null,
nm_solicitante       VARCHAR(100)         not null,
rg_cpf_solicitante   VARCHAR(15)          not null,
cargo_solicitante    VARCHAR(50)          null,
telefone_solicitante VARCHAR(15)          null,
situacao             INT2                 not null,
data_requerimento    DATE                 not null,
version              INT8                 not null,
data_validade        DATE                 null
);

alter table requerimento_vigilancia
   add constraint PK_REQUERIMENTO_VIGILANCIA primary key (cd_req_vigilancia);

alter table requerimento_vigilancia
   add constraint FK_REQ_REF_TP_SOL foreign key (cd_tipo_solicitacao)
      references tipo_solicitacao (cd_tipo_solicitacao)
      on delete restrict on update restrict;

alter table requerimento_vigilancia
   add constraint FK_REQUERIM_REFERENCE_ESTABELE foreign key (cd_estabelecimento)
      references estabelecimento (cd_estabelecimento)
      on delete restrict on update restrict;

/*
    Marcus - 13/03/2013
*/
insert into programa_pagina values(401, 'br.com.celk.view.vigilancia.estabelecimento.CadastroRequerimentoEstabelecimentoPage', 'N');
insert into programa_web_pagina values (402, 223, 401);

/*
    Leando - 14/03/2013
*/
/*==============================================================*/
/* Table: veiculo_estabelecimento                               */
/*==============================================================*/
create table veiculo_estabelecimento (
cd_veiculo           INT8                 not null,
cd_estabelecimento   INT8                 not null,
placa                VARCHAR(7)           not null,
tipo_veiculo         VARCHAR(300)         not null,
especificacao        VARCHAR(300)         not null,
restricoes           VARCHAR(300)         null,
version              INT2                 null
);

alter table veiculo_estabelecimento
   add constraint PK_VEICULO_ESTABELECIMENTO primary key (cd_veiculo);

alter table veiculo_estabelecimento
   add constraint FK_VEICULO_ESTABELECIMENTO foreign key (cd_estabelecimento)
      references estabelecimento (cd_estabelecimento)
      on delete restrict on update restrict;

/*==============================================================*/
/* Index: idx_placa_estabelecimento                             */
/*==============================================================*/
create unique index idx_placa_estabelecimento on veiculo_estabelecimento (
cd_estabelecimento,
placa
);

/*
    Marcus - 14/03/2013
*/
insert into programa_pagina values(402, 'br.com.celk.view.vigilancia.estabelecimento.tabbedpanel.DadosVeiculoEstabelecimentoTab', 'N');
insert into programa_web_pagina values (403, 223, 402);

/*
    Leando - 14/03/2013
*/
alter table atividade_estabelecimento add observacao_alvara varchar(300) null;

/*
    Leando - 15/03/2013
*/
alter table requerimento_vigilancia add cd_veiculo int8 null;

alter table requerimento_vigilancia
   add constraint FK_REQ_VIG_REF_VEICULO foreign key (cd_veiculo)
      references veiculo_estabelecimento (cd_veiculo)
      on delete restrict on update restrict;

/*
    Marcus - 19/03/2013
*/
insert into programa_pagina values(403, 'br.com.celk.view.vigilancia.requerimentovigilancia.ConsultaRequerimentoVigilanciaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (245,'Requerimento',403,'N');
insert into programa_web_pagina values (404, 245, 403);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (343,'Requerimento','requerimento',309,245,307);
insert into programa_pagina values(404, 'br.com.celk.view.vigilancia.requerimentovigilancia.ConsultarDetalhesRequerimentoVigilanciaPage', 'N');
insert into programa_web_pagina values (405, 245, 404);
insert into programa_pagina values(405, 'br.com.celk.view.vigilancia.requerimentovigilancia.RegistrarInspecaoPage', 'N');
insert into programa_web_pagina values (406, 245, 405);

alter table requerimento_vigilancia alter column cd_estabelecimento set not null;
alter table programa_pagina_permissao
add column ds_bundle varchar;
insert into permissao_web values (21,'Receber',0);
insert into permissao_web values (22,'Registrar',0);
insert into permissao_web values (23,'Receber Documento',0);

insert into programa_pagina_permissao values ('30',12,403,0,null);
insert into programa_pagina_permissao values ('31',21,403,0,null);
insert into programa_pagina_permissao values ('32',22,403,0,'registrarInspecao');
insert into programa_pagina_permissao values ('33',6,403,0,'impressaoDocumento');
insert into programa_pagina_permissao values ('34',7,403,0,'encaminharDocumentoEntrega');
insert into programa_pagina_permissao values ('35',23,403,0,'receberDocumentoParaEntrega');
insert into programa_pagina_permissao values ('36',8,403,0,'confirmarEntregaDocumento');


/*
    Leandro - 19/03/2013
*/
/*==============================================================*/
/* Table: registro_inspecao_vigilancia                          */
/*==============================================================*/
create table registro_inspecao_vigilancia (
cd_registro_insp_vig INT8                 not null,
motivo               VARCHAR(1000)        null,
resultado            INT2                 not null,
version              INT8                 not null
);

alter table registro_inspecao_vigilancia
   add constraint PK_REGISTRO_INSPECAO_VIGILANCI primary key (cd_registro_insp_vig);

/*==============================================================*/
/* Table: ocorrencia_req_vigilancia                             */
/*==============================================================*/
create table ocorrencia_req_vigilancia (
cd_ocorr_req_vig     INT8                 not null,
cd_usuario           NUMERIC(6)           not null,
cd_req_vigilancia    INT8                 not null,
cd_registro_insp_vig INT8                 null,
data_ocorrencia      TIMESTAMP            not null,
descricao_ocorrencia VARCHAR(1000)        not null,
version              int8                 not null
);

alter table ocorrencia_req_vigilancia
   add constraint PK_OCORRENCIA_REQ_VIGILANCIA primary key (cd_ocorr_req_vig);

alter table ocorrencia_req_vigilancia
   add constraint FK_OCORR_REF_REG_INSP foreign key (cd_registro_insp_vig)
      references registro_inspecao_vigilancia (cd_registro_insp_vig)
      on delete restrict on update restrict;

alter table ocorrencia_req_vigilancia
   add constraint FK_OCORR_REF_REQ_VIG foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

alter table ocorrencia_req_vigilancia
   add constraint FK_OCORR_REF_USU foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Everton - 19/03/2013
*/
ALTER TABLE tipo_atendimento ADD cd_cla_atendimento   INT4                 null;
alter table tipo_atendimento
   add constraint FK_TP_ATEND_REF_CLA_ATE foreign key (cd_cla_atendimento)
      references classificacao_atendimento (cd_cla_atendimento)
      on delete restrict on update restrict;

/*
 PostgreSQL
 Everton - 19/03/2013
*/

ALTER TABLE atendimento_primario ADD frequencia_cardiaca  INT4                 null;
alter table usuario_cadsus_dado add frequencia_cardiaca  INT4 null;

/*
    Leandro - 20/03/2013
*/
/*==============================================================*/
/* Table: documento_requerimento                                */
/*==============================================================*/
create table documento_requerimento (
cd_doc_req           INT8                 not null,
cd_req_vigilancia    INT8                 not null,
cd_estabelecimento   INT8                 not null,
cd_atividade_estabelecimento INT8                 null,
cd_grupo_estabelecimento INT8                 null,
cod_cid              NUMERIC(6)           null,
tipo_documento       INT2                 not null,
numero               VARCHAR(20)          not null,
protocolo            VARCHAR(15)          not null,
nm_estabelecimento   VARCHAR(300)         not null,
nm_fantasia_estabelecimento VARCHAR(300)         not null,
cnpj_cpf             VARCHAR(20)          not null,
telefone             VARCHAR(20)          null,
endereco             VARCHAR(500)         not null,
bairro               VARCHAR(50)          not null,
responsavel          VARCHAR(100)         not null,
autorizacao_funcionamento VARCHAR(500)         null,
data_validade        DATE                 not null,
observacao_destaque  VARCHAR(500)         null,
data_impressao       DATE                 not null,
concedido_por        VARCHAR(50)          not null,
version              INT8                 not null,
tipo_veiculo         VARCHAR(300)         null,
especificacao        VARCHAR(300)         null,
placa                VARCHAR(7)           null,
restricoes           VARCHAR(300)         null
);

alter table documento_requerimento
   add constraint PK_DOCUMENTO_REQUERIMENTO primary key (cd_doc_req);

alter table documento_requerimento
   add constraint FK_DOC_REQ_REF_ESTAB foreign key (cd_estabelecimento)
      references estabelecimento (cd_estabelecimento)
      on delete restrict on update restrict;

alter table documento_requerimento
   add constraint FK_DOC_REQ_REQ_VIG foreign key (cd_req_vigilancia)
      references requerimento_vigilancia (cd_req_vigilancia)
      on delete restrict on update restrict;

alter table documento_requerimento
   add constraint FK_DOCUMENT_REFERENCE_ATIVIDAD foreign key (cd_atividade_estabelecimento)
      references atividade_estabelecimento (cd_atividade_estabelecimento)
      on delete restrict on update restrict;

alter table documento_requerimento
   add constraint FK_DOCUMENT_REFERENCE_GRUPO_ES foreign key (cd_grupo_estabelecimento)
      references grupo_estabelecimento (cd_grupo_estabelecimento)
      on delete restrict on update restrict;

alter table documento_requerimento
   add constraint FK_DOCUMENT_REFERENCE_CIDADE foreign key (cod_cid)
      references cidade (cod_cid)
      on delete restrict on update restrict;

/*==============================================================*/
/* Table: documento_sequencial                                  */
/*==============================================================*/
create table documento_sequencial (
ano                  INT8                 not null,
sequencial_alvara    INT8                 not null,
version              INT8                 not null,
sequencial_licenca   INT8                 not null
);

alter table documento_sequencial
   add constraint PK_DOCUMENTO_SEQUENCIAL primary key (ano);

alter table estabelecimento add cd_doc_alvara int8 null;

alter table estabelecimento
   add constraint FK_ESTAB_REF_ALVARA foreign key (cd_doc_alvara)
      references documento_requerimento (cd_doc_req)
      on delete restrict on update restrict;

/*
    Marcus - 20/03/2013
*/
insert into modulo values (28,'Vigilancia',null,null,0); 

/*
    Marcus - 21/03/2013
*/
update menu_web
set ds_bundle = 'relacaoFamilias'
where cd_menu = 336;

/*
    Leandro - 21/03/2013
*/
/*==============================================================*/
/* Table: classificacao_grupo_estab                             */
/*==============================================================*/
create table classificacao_grupo_estab (
cd_cla_grupo_estab   INT8                 not null,
ds_cla_grupo_estab   VARCHAR(50)          not null,
version              INT8                 not null
);

alter table classificacao_grupo_estab
   add constraint PK_CLASSIFICACAO_GRUPO_ESTAB primary key (cd_cla_grupo_estab);

/*
    Marcus - 21/03/2013
*/
insert into programa_pagina values(407, 'br.com.celk.view.vigilancia.classificacaogrupoestabelecimento.ConsultaClassificacaoGrupoEstabelecimentoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (246,'Classificação de Grupo de Estabelecimento',407,'N');
insert into programa_web_pagina values (408, 246, 407);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (344,'Classificação de Grupo de Estabelecimento','classificacaoGrupoEstabelecimento',309,246,307);
insert into programa_pagina values(408, 'br.com.celk.view.vigilancia.classificacaogrupoestabelecimento.CadastroClassificacaoGrupoEstabelecimentoPage', 'N');
insert into programa_web_pagina values (409, 246, 408);

/*
    Leandro - 25/03/2013
*/
alter table requerimento_vigilancia add data_inicio_validade date null;
alter table requerimento_vigilancia add data_final_validade date null;
alter table requerimento_vigilancia add descricao_periodo_validade varchar(100) null;

update requerimento_vigilancia set data_final_validade = data_validade;

alter table requerimento_vigilancia drop column data_validade;

/*
    Leandro - 25/03/2013
*/
INSERT INTO classificacao_grupo_estab (cd_cla_grupo_estab,ds_cla_grupo_estab,version) VALUES (1,'GERAL',0);

alter table grupo_estabelecimento add cd_cla_grupo_estab int8 null;

alter table grupo_estabelecimento
   add constraint FK_GRU_REF_CLASS_ESTAB foreign key (cd_cla_grupo_estab)
      references classificacao_grupo_estab (cd_cla_grupo_estab)
      on delete restrict on update restrict;

update grupo_estabelecimento set cd_cla_grupo_estab = 1 where cd_cla_grupo_estab is null;

alter table grupo_estabelecimento alter column cd_cla_grupo_estab set not null;

/*
    Leandro - 25/03/2013
*/
/*==============================================================*/
/* Table: template_doc_vigilancia                               */
/*==============================================================*/
create table template_doc_vigilancia (
cd_templ_doc_vig     INT8                 not null,
nm_template_doc_vigilancia VARCHAR(100)         not null,
texto_template_doc_vig VARCHAR              not null,
version              INT8                 not null
);

alter table template_doc_vigilancia
   add constraint PK_TEMPLATE_DOC_VIGILANCIA primary key (cd_templ_doc_vig);

/*
    Marcus - 25/03/2013
*/
insert into programa_pagina values(410, 'br.com.celk.view.vigilancia.documentopersonalizado.CadastroDocumentoPersonalizadoPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (248,'Cadastro de Documento Personalizado',410,'N');
insert into programa_web_pagina values (411, 248, 410);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (346,'Cadastro de Documento Personalizado','cadastroDocumentoPersonalizado',309,248,307);

/*
    Marcus - 26/03/2013
*/
alter table tipo_solicitacao add cd_templ_doc_vig INT8 null;
alter table tipo_solicitacao
   add constraint FK_TP_SOL_REF_TEMPL_DOC foreign key (cd_templ_doc_vig)
      references template_doc_vigilancia (cd_templ_doc_vig)
      on delete restrict on update restrict;

/*
    Leandro - 25/03/2013
*/
drop table documento_sequencial;

/*==============================================================*/
/* Table: documento_sequencial                                  */
/*==============================================================*/
create table documento_sequencial (
cd_documento_sequencial INT8                 not null,
cd_cla_grupo_estab   INT8                 not null,
ano                  INT8                 not null,
tipo_documento       INT2                 not null,
sequencial           INT8                 not null,
version              INT8                 not null
);

alter table documento_sequencial
   add constraint PK_DOCUMENTO_SEQUENCIAL primary key (cd_documento_sequencial);

/*==============================================================*/
/* Index: IDX_DOCUMENTO_SEQUENCIAL                              */
/*==============================================================*/
create unique index IDX_DOCUMENTO_SEQUENCIAL on documento_sequencial (
cd_cla_grupo_estab,
ano,
tipo_documento
);

alter table documento_sequencial
   add constraint FK_DOC_SEQ_REF_CLA_GRU_ESTAB foreign key (cd_cla_grupo_estab)
      references classificacao_grupo_estab (cd_cla_grupo_estab)
      on delete restrict on update restrict;

alter table documento_requerimento add data_inicio_validade date null;
alter table documento_requerimento add data_fim_validade date null;
alter table documento_requerimento alter column endereco drop not null;
alter table documento_requerimento alter column bairro drop not null;
alter table documento_requerimento alter column responsavel drop not null;
alter table documento_requerimento alter column concedido_por drop not null;
alter table documento_requerimento drop column data_validade;

/*
    Leandro - 27/03/2013
*/
/*==============================================================*/
/* Table: setor_vigilancia                                      */
/*==============================================================*/
create table setor_vigilancia (
cd_setor_vigilancia  INT8                 not null,
ds_setor_vigilancia  VARCHAR(100)         not null,
version              INT8                 not null
);

alter table setor_vigilancia
   add constraint PK_SETOR_VIGILANCIA primary key (cd_setor_vigilancia);


/*
    Marcus - 27/03/2013
*/
insert into programa_pagina values(411, 'br.com.celk.view.vigilancia.setorvigilancia.ConsultaSetorVigilanciaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (249,'Setor Vigilância',411,'N');
insert into programa_web_pagina values (412, 249, 411);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (347,'Setor Vigilância','setorVigilancia',309,249,307);
insert into programa_pagina values(412, 'br.com.celk.view.vigilancia.setorvigilancia.CadastroSetorVigilanciaPage', 'N');
insert into programa_web_pagina values (413, 249, 412);

/*
    Marcus - 27/03/2013
*/
alter table atividade_estabelecimento add cd_setor_vigilancia int8 null;
alter table atividade_estabelecimento
   add constraint FK_ATIV_ESTAB_REF_SETOR foreign key (cd_setor_vigilancia)
      references setor_vigilancia (cd_setor_vigilancia);

/*
    Felipe 27/03/2013
*/
create table nodo_atendimento_web (
cd_nodo_atendimento    INT8                 not null,
cd_tp_atendimento   INT4                 not null,
classe_nodo       VARCHAR(512)         not null,
ordem             INT2                 not null,
version              INT8                 not null
);

alter table nodo_atendimento_web
   add constraint PK_NOD_ATEND_WEB primary key (cd_nodo_atendimento);

alter table nodo_atendimento_web
   add constraint FK_NOD_ATEND_WEB_REF_TP_ATEND foreign key (cd_tp_atendimento)
      references tipo_atendimento (cd_tp_atendimento);

/*
    Leandro - 27/03/2013
*/
/*==============================================================*/
/* Table: usuario_setor_vigilancia                              */
/*==============================================================*/
create table usuario_setor_vigilancia (
cd_usu_setor         INT8                 not null,
cd_usuario           NUMERIC(6)           not null,
cd_setor_vigilancia  INT8                 not null,
version              INT8                 not null
);

alter table usuario_setor_vigilancia
   add constraint PK_USUARIO_SETOR_VIGILANCIA primary key (cd_usu_setor);

alter table usuario_setor_vigilancia
   add constraint FK_USU_SETOR_REF_SETOR foreign key (cd_setor_vigilancia)
      references setor_vigilancia (cd_setor_vigilancia)
      on delete restrict on update restrict;

alter table usuario_setor_vigilancia
   add constraint FK_USU_SETOR_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

/*
    Marcus - 27/03/2013
*/
insert into programa_pagina values(413, 'br.com.celk.view.vigilancia.veiculo.usuariosetorvigilancia.CadastroUsuarioSetorVigilanciaPage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (250,'Usuários por Setor',413,'N');
insert into programa_web_pagina values (414, 250, 413);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo) VALUES (348,'Usuários por Setor','usuariosSetor',309,250,307);

/*
 PostgreSQL
 Everton - 27/03/2013
*/

alter table programa_saude_usuario alter tp_programa type int8;

alter table tipo_atividade_grupo alter cd_tp_atv_grupo type int8;
alter table atividade_grupo alter cd_tp_atv_grupo type int8;
alter table tipo_atividade_grupo_cbo alter cd_tp_atv_grupo type int8;
alter table atividade_grupo_profissional alter cd_tp_acao_coletiva type int8;

alter table tipo_atividade_grupo_cbo alter cd_atv_grupo_cbo type int8;

alter table atividade_grupo_profissional alter cd_relacionamento type int8;

alter table local_atividade_grupo alter cd_local_acao type int8;
alter table atividade_grupo alter cd_local_acao type int8;

alter table grupo_programa_saude alter cd_grupo_paciente type int8;
alter table atividade_grupo alter cd_grupo_paciente type int8;
alter table programa_saude_usuario alter cd_grupo_paciente type int8;

alter table programa_saude alter cd_programa_saude type int8;
alter table programa_saude_usuario alter cd_prg_saude type int8;
alter table produto_programa_saude alter cd_programa_saude type int8;
alter table tipo_atividade_grupo alter cd_programa_saude type int8;
alter table grupo_programa_saude alter cd_programa_saude type int8;
alter table veiculo alter cd_programa_saude type int8;

alter table atividade_grupo alter cd_atv_grupo type int8;
alter table bpa_atividade alter cd_atv_grupo type int8;
alter table atividade_grupo_paciente alter cd_atv_grupo type int8;

alter table hiperdia alter seq_hiperdia type int8; 
alter table hiperdia_medicamento alter nr_hiperdia type int8;

/*
    Claudio - 01/04/2013
*/
alter table parametro_atendimento add flag_bpa_gera_tfd char;

/*
    Claudio - 02/04/2013
*/
update programa_web set ds_prg_web = 'Cadastro da Receita do Paciente' where cd_prg_web = 114;
update menu_web set ds_menu = 'Cadastro da Receita do Paciente', ds_bundle = 'cadastroReceitaPaciente' where cd_menu = 137;

/*
    Marcus - 05/04/2013
*/
INSERT INTO programa_pagina (cd_prg_pagina,cam_pagina,publico,version) VALUES (409,'br.com.celk.view.vigilancia.veiculo.CadastroVeiculoEstabelecimentoPage','N',0);
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo,version) VALUES (247,'Veículos',409,'N',1);
INSERT INTO programa_web_pagina (cd_prg_web_pagina,cd_prg_web,cd_prg_pagina,version) VALUES (410,247,409,0);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,version) VALUES (345,'Veículos','veiculos',309,247,307,0);

/*
    Marcus - 05/04/2013
*/
update menu_web
set cd_menu_pai = 305
where cd_menu = 274;

/*
    Marcus - 09/04/2013
*/
alter table documento_requerimento add titulo_documento_personalizado varchar(100);
alter table documento_requerimento add texto_documento_personalizado varchar;

/*
    Marcus - 09/04/2013
*/
update programa_pagina
set cam_pagina = 'br.com.celk.view.vigilancia.documentopersonalizado.ConsultaTemplateDocumentoVigilanciaPage'
where cd_prg_pagina = 410;
insert into programa_pagina values(422, 'br.com.celk.view.vigilancia.documentopersonalizado.CadastroTemplateDocumentoVigilanciaPage', 'N');
insert into programa_web_pagina values (423, 248, 422);

/*
    Everton - 10/04/2013
*/
ALTER TABLE requerimento_vigilancia ALTER data_requerimento TYPE TIMESTAMP;