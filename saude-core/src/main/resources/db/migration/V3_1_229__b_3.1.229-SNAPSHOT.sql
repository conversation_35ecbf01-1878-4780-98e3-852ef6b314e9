SET application_name = 'flyway|3.1.229';

SET statement_timeout TO 600000;

/*
       <PERSON>
*/
ALTER TABLE tipo_procedimento_agenda
ADD res_sex_idade int2 DEFAULT 1 CHECK (res_sex_idade BETWEEN 0 AND 1) not null;

alter table auditschema.tipo_procedimento_agenda
ADD res_sex_idade int2 DEFAULT 1 CHECK (res_sex_idade BETWEEN 0 AND 1) not null;

/*
    <PERSON>rilo Vieira Cruz  01/04/2024
*/

ALTER TABLE bnafar_dispensacao ADD column if not exists cd_fabricante INT8;
ALTER TABLE bnafar_dispensacao ADD constraint fk_fabricante foreign key (cd_fabricante) references fabricante(cd_fabricante);

ALTER TABLE auditschema.bnafar_dispensacao ADD if not exists cd_fabricante INT8;

ALTER TABLE bnafar_entrada ADD column if not exists cd_empresa_distribuidor INT8;
ALTER TABLE bnafar_entrada ADD constraint fk_empresa_distribuidor foreign key (cd_empresa_distribuidor) references empresa(empresa);

ALTER TABLE auditschema.bnafar_entrada ADD if not exists cd_empresa_distribuidor INT8;