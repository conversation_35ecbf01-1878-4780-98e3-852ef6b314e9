SET application_name = 'flyway|*********';

/*
    Silvio 17/07/2019 - #26125
*/
ALTER TABLE grupo_problemas_condicoes ADD COLUMN descricao text;
ALTER TABLE auditschema.grupo_problemas_condicoes ADD COLUMN descricao text;

/*
    #26385 - Sulivan - 11/07/2019
*/
INSERT INTO programa_pagina VALUES (1714, 'br.com.celk.view.agenda.agendamento.AgendamentoListaEsperaLotePage', 'N');
INSERT INTO programa_web (cd_prg_web,ds_prg_web,cd_prg_pag_principal,ativo) VALUES (951, 'Agendamento da Lista de Espera por Lote', 1714, 'N');
INSERT INTO programa_web_pagina VALUES (1828, 951, 1714);
INSERT INTO menu_web (cd_menu,ds_menu,ds_bundle,cd_menu_pai,cd_prg_web,cd_menu_modulo,layout_menu,version) VALUES (1203,'Agendamento da Lista de Espera por Lote','agendamentoListaEsperaLote',159,951,3,0,0);

create table agendamento_lote_processo (
     cd_agendamento_lote_processo INT8                 not null,
     cd_tp_procedimento           INT8                 not null,
     cd_usuario                   NUMERIC(6)           not null,
     dt_geracao                   timestamp            not null,
     quantidade                   int2                 not null,
     status                       int2                 not null,
     msg_erro                     varchar              null,
     version                      int8                 not null
);

alter table agendamento_lote_processo
   add constraint PK_AG_LOTE_PROCESSO primary key (cd_agendamento_lote_processo);

alter table agendamento_lote_processo
   add constraint FK_AG_LOTE_PROCESSO_REF_USUARIO foreign key (cd_usuario)
      references usuarios (cd_usuario)
      on delete restrict on update restrict;

alter table agendamento_lote_processo
   add constraint FK_AG_LOTE_PROCESSO_REF_TP_PROC foreign key (cd_tp_procedimento)
      references tipo_procedimento (cd_tp_procedimento)
      on delete restrict on update restrict;

CREATE TABLE auditschema.agendamento_lote_processo AS SELECT t2.*, t1.* FROM agendamento_lote_processo t1, audit_temp t2 WHERE 1=2;create sequence seq_audit_id_agendamento_lote_processo;alter table auditschema.agendamento_lote_processo add primary key (audit_id);CREATE TRIGGER emp_audit AFTER INSERT OR UPDATE OR DELETE ON agendamento_lote_processo FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();

create sequence seq_agendamento_lote_processo;

INSERT INTO programa_pagina_permissao VALUES(504, 22, 1714, 0, 'tipoFilaRegulacao');