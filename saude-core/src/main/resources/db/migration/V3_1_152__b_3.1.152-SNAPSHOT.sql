SET application_name = 'flyway|3.1.152';

SET statement_timeout TO 600000;

/*
 * <PERSON><PERSON><PERSON> - feature/REG-1202 - 11/07/2022
 */

INSERT INTO agendador_processo (cd_agendador_processo, cd_processo, nm_servico, ds_servico, status, version, tp_processo)
VALUES (80, 80, 'Notificação de Vencimento das FPO do Prestador de Serviço', 'Executa processo para notificar o vencimento das FPO do Prestador de Serviço.', 0, 0, 4);

alter table exame_prestador_contrato add column flag_vencimento_do_contrato_notificado int2;
alter table auditschema.exame_prestador_contrato add column flag_vencimento_do_contrato_notificado int2;

/*
 *   Fernanda Bianchini - 22/09/2022 - VSA-88
 */

INSERT INTO public.ficha_investigacao_agravo(cd_ficha_investigacao_agravo, ordem, dt_cadastro, descricao, status, "version")
VALUES (41, 1, NOW(), 'AIDS CRIANÇAS', 1, 0);

CREATE TABLE public.investigacao_agr_aids_criancas(
     cd_investigacao_agr_aids_criancas int8 PRIMARY KEY NOT NULL,
     "version" int8 NOT NULL,
     flag_informacoes_complementares varchar(1) NOT NULL DEFAULT 'S'::character varying,
     cd_registro_agravo int8 NOT NULL REFERENCES public.registro_agravo(cd_registro_agravo),

    --INVESTIGAÇÃO
     dt_investigacao date NULL,

    --ANTECEDENTES EPIDEMIOLOGICOS DA MAE
     idade_mae varchar(2) NULL,
     escolaridade_mae int2 NULL,
     raca_cor_mae int2 NULL,
     ocupacao_cbo_mae varchar(10) NULL REFERENCES public.tabela_cbo(cd_cbo),


     --ANTECEDENTES EPIDEMIOLÓGICOS DE CRIANÇAS
     transmissao_vertical int2 NULL,
     transmissao_sexual int2 NULL,

     transmissao_sanguinea_drogas int2 NULL,
     transmissao_sanguinea_hemotransfusao int2 NULL,
     transmissao_sanguinea_sanguinea int2 NULL,
     transmissao_sanguinea_acidente_material int2 NULL,


     dt_transfusao_acidente date NULL,
     cd_municipio int8 NULL REFERENCES public.cidade(cod_cid),
     cd_instituicao int8 NULL REFERENCES public.empresa(empresa),

     causa_infeccao_hiv int2 NULL,

    --DADOS DO LABORATÓRIO
     antes_18_meses_primeiro_teste int2 NULL,
     dt_antes_18_meses_primeiro_teste date NULL,
     antes_18_meses_segundo_teste int2 NULL,
     dt_antes_18_meses_segundo_teste date NULL,
     antes_18_meses_terceiro_teste int2 NULL,
     dt_antes_18_meses_terceiro_teste date NULL,

     apos_18_meses_triagem_teste int2 NULL,
     dt_apos_18_meses_triagem_teste date NULL,
     apos_18_meses_confirmatorio_teste int2 NULL,
     dt_apos_18_meses_confirmatorio_teste date NULL,
     apos_18_meses_teste_um int2 NULL,
     apos_18_meses_teste_dois int2 NULL,
     apos_18_meses_teste_tres int2 NULL,


    --CRITÉRIOS DE DEFINIÇÃO DE CASO
     doenca_leve_aumento_cronico int2 NULL,
     doenca_leve_dermatite int2 NULL,
     doenca_leve_esplenomegalia int2 NULL,
     doenca_leve_hepatomegalia int2 NULL,
     doenca_leve_infeccoes int2 NULL,
     doenca_leve_linfadenopatia int2 NULL,

     doenca_grave_anemia int2 NULL,
     doenca_grave_candidose_esofago int2 NULL,
     doenca_grave_candidose_traqueia int2 NULL,
     doenca_grave_candidose_oral int2 NULL,
     doenca_grave_citomegalovirose int2 NULL,
     doenca_grave_criptococose int2 NULL,
     doenca_grave_criptosporidiose int2 NULL,
     doenca_grave_diarreia int2 NULL,
     doenca_grave_encefalopatia int2 NULL,
     doenca_grave_febre int2 NULL,
     doenca_grave_gengivo int2 NULL,
     doenca_grave_hepatite_hiv int2 NULL,
     doenca_grave_herpes_bronquios int2 NULL,
     doenca_grave_herpes_mucocutaneo int2 NULL,
     doenca_grave_herpes_zoster int2 NULL,
     doenca_grave_histoplasmose int2 NULL,
     doenca_grave_infeccoes_bacterianas int2 NULL,
     doenca_grave_infeccao_citomegalovirus int2 NULL,
     doenca_grave_isosporidiose int2 NULL,
     doenca_grave_leiomiossarcoma int2 NULL,
     doenca_grave_leucoencefalopatia int2 NULL,
     doenca_grave_linfopenia int2 NULL,
     doenca_grave_linfoma int2 NULL,
     doenca_grave_linfoma_primario int2 NULL,
     doenca_grave_miocardiopatia int2 NULL,
     doenca_grave_micobacteriose int2 NULL,
     doenca_grave_meningite_bacteriana int2 NULL,
     doenca_grave_nefropatia int2 NULL,
     doenca_grave_nocardiose int2 NULL,
     doenca_grave_pneumonia_linfoide int2 NULL,
     doenca_grave_pneumonia_pneumocystis int2 NULL,
     doenca_grave_salmonelose int2 NULL,
     doenca_grave_sarcoma int2 NULL,
     doenca_grave_emaciacao int2 NULL,
     doenca_grave_toxoplasmose int2 NULL,
     doenca_grave_toxoplasmose_antes_1_mes int2 NULL,
     doenca_grave_trombocitopenia int2 NULL,
     doenca_grave_tuberculose_pulmonar int2 NULL,
     doenca_grave_tuberculose_disseminada int2 NULL,
     doenca_grave_varicela int2 NULL,

     achados_laboratoriais_menor_1500 int2 NULL,
     achados_laboratoriais_menor_1000 int2 NULL,
     achados_laboratoriais_menor_500 int2 NULL,

     criterio_obito int2 NULL,

    --TRATAMENTO
     cd_municipio_tratamento int8 NULL REFERENCES public.cidade(cod_cid),
     cd_unidade_saude_tratamento int8 NULL REFERENCES public.empresa(empresa),

     --EVOLUÇÃO
     evolucao_caso int2 NULL,
     dt_obito date NULL,

    --OBSERVAÇÃO
     observacao text NULL,

    --ENCERRAMENTO
     dt_encerramento date NULL,
     cd_usuario_encerramento int8 NULL
);

CREATE SEQUENCE seq_investigacao_agr_aids_criancas INCREMENT 1 START 1;
CREATE TABLE auditschema.investigacao_agr_aids_criancas AS (SELECT t2.*, t1.* FROM investigacao_agr_aids_criancas t1, audit_temp t2 WHERE 1=2);
CREATE sequence seq_audit_id_investigacao_agr_aids_criancas;
ALTER TABLE auditschema.investigacao_agr_aids_criancas ADD PRIMARY KEY (audit_id);
CREATE trigger emp_audit AFTER INSERT OR UPDATE OR DELETE ON public.investigacao_agr_aids_criancas FOR EACH ROW EXECUTE PROCEDURE process_emp_audit();
