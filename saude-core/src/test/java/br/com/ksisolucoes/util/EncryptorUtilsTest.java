package br.com.ksisolucoes.util;


import org.junit.Assert;
import org.junit.Test;

public class EncryptorUtilsTest {

    @Test
    public void shouldEncryptAndDecryptString() {
        String originalText = "Original Text";
        String encryptedText = EncryptorUtils.encrypt(originalText);
        String dencryptedText = EncryptorUtils.decrypt(encryptedText);

        Assert.assertEquals("dencrypted text should be same as original text", originalText, dencryptedText);
    }

}