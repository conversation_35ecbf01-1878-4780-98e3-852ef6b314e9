package br.com.ksisolucoes.vo.vacina.helper;

import br.com.celk.helper.vacinas.AplicarVacinaHelper;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;
import junit.framework.TestCase;
import org.joda.time.LocalDate;
import org.junit.Test;

import java.util.Date;

public class AplicarVacinaHelperTest extends TestCase {

    private AplicarVacinaHelper aplicarVacinaHelper;

    public void setUp() throws Exception {
        super.setUp();
        aplicarVacinaHelper = AplicarVacinaHelper.getInstance();
    }

    public void testValidaIntervalo_parametrosNullNaoDeveQuebrar() {
        VacinaAplicacao vacinaAplicacao = new VacinaAplicacao();
        vacinaAplicacao.setDataAplicacao(new LocalDate("2020-01-01").toDate());
        VacinaCalendario vacinaCalendario = new VacinaCalendario();

        vacinaCalendario.setIntervaloMinimoDose(null);
        assertFalse(aplicarVacinaHelper.isAplicacaoIntervalo(true, null, vacinaAplicacao, vacinaCalendario));

        vacinaCalendario.setIntervaloMaximoDose(null);
        assertFalse(aplicarVacinaHelper.isAplicacaoIntervalo(false, null, vacinaAplicacao, vacinaCalendario));

        vacinaAplicacao.setDataAplicacao(null);
        assertFalse(aplicarVacinaHelper.isAplicacaoIntervalo(false, null, vacinaAplicacao, vacinaCalendario));
    }

    public void testValidaIntervalo_intervaloMinimo() {
        Date dataAplicando = new LocalDate("2020-01-10").toDate();
        Date dataVacinaAplicada = new LocalDate("2020-01-01").toDate();
        VacinaAplicacao vacinaAplicacao = new VacinaAplicacao();
        vacinaAplicacao.setDataAplicacao(dataVacinaAplicada);
        VacinaCalendario vacinaCalendario = new VacinaCalendario();
        vacinaCalendario.setIntervaloMinimoDose(1L);
        assertFalse(aplicarVacinaHelper.isAplicacaoIntervalo(true, dataAplicando, vacinaAplicacao, vacinaCalendario));

        vacinaCalendario.setIntervaloMinimoDose(10L);
        assertTrue(aplicarVacinaHelper.isAplicacaoIntervalo(true, dataAplicando, vacinaAplicacao, vacinaCalendario));
    }

    public void testValidaIntervalo_intervaloMaximo() {
        Date dataAplicando = new LocalDate("2020-01-10").toDate();
        Date dataVacinaAplicada = new LocalDate("2020-01-01").toDate();
        VacinaAplicacao vacinaAplicacao = new VacinaAplicacao();
        vacinaAplicacao.setDataAplicacao(dataVacinaAplicada);
        VacinaCalendario vacinaCalendario = new VacinaCalendario();
        vacinaCalendario.setIntervaloMaximoDose(1L);
        assertTrue(aplicarVacinaHelper.isAplicacaoIntervalo(false, dataAplicando, vacinaAplicacao, vacinaCalendario));

        vacinaCalendario.setIntervaloMaximoDose(10L);
        assertFalse(aplicarVacinaHelper.isAplicacaoIntervalo(false, dataAplicando, vacinaAplicacao, vacinaCalendario));
    }

    @Test
    public void testPacienteMenor28Dias_retornarFalso() {
        UsuarioCadsus usuarioCadsus = new UsuarioCadsus();

        assertFalse(aplicarVacinaHelper.pacienteMenor28Dias(null));

        usuarioCadsus.setDataNascimento(Data.removeDias(LocalDate.now().toDate(),29));
        assertFalse(aplicarVacinaHelper.pacienteMenor28Dias(usuarioCadsus));
        usuarioCadsus.setDataNascimento(Data.removeDias(LocalDate.now().toDate(),290));
        assertFalse(aplicarVacinaHelper.pacienteMenor28Dias(usuarioCadsus));
        usuarioCadsus.setDataNascimento(null);
        assertFalse(aplicarVacinaHelper.pacienteMenor28Dias(usuarioCadsus));
    }
    @Test
    public void testPacienteMenor28Dias_retornarVerdadeiro() {
        UsuarioCadsus usuarioCadsus = new UsuarioCadsus();

        usuarioCadsus.setDataNascimento(LocalDate.now().toDate());
        assertTrue(aplicarVacinaHelper.pacienteMenor28Dias(usuarioCadsus));

        usuarioCadsus.setDataNascimento(Data.removeDias(LocalDate.now().toDate(),28));
        assertTrue(aplicarVacinaHelper.pacienteMenor28Dias(usuarioCadsus));

        usuarioCadsus.setDataNascimento(Data.removeDias(LocalDate.now().toDate(),27));
        assertTrue(aplicarVacinaHelper.pacienteMenor28Dias(usuarioCadsus));
    }

    @Test
    public void testPacienteSemCPFeCNS_retornarFalso() {
        UsuarioCadsus usuarioCadsus = new UsuarioCadsus();

        assertFalse(aplicarVacinaHelper.pacienteSemCPFeCNS(null));

        usuarioCadsus.setCpf("123");
        usuarioCadsus.cnsCarregado="123";
        assertFalse(aplicarVacinaHelper.pacienteSemCPFeCNS(usuarioCadsus));

    }
}