package br.com.celk.sms.restservice.response;

import br.com.celk.sms.restservice.util.ISmsEnum;

/**
 * <AUTHOR>
 * Criado em: Nov 14, 2013
 */
public enum ResponseCode implements ISmsEnum{

    REQUISICAO_BEM_SUCEDIDA(200),
    REQUISICAO_BEM_SUCEDIDA_E_ATIVIDADE_CRIADA(201),
    OS_PARAMETROS_DA_REQUISICAO_ESTAO_INCORRETOS(422),
    ACESSO_NAO_AUTORIZADO(401),
    PAGINA_NAO_ENCONTRADA(404),
    ERRO_INTERNO_NO_SERVIDOR(500);

    private final int value;

    ResponseCode(int value) {
        this.value = value;
    }

    @Override
    public int value(){
        return value;
    }

    @Override
    public String toString() {
        return "(" + value + ") "+super.toString();
    }
}
