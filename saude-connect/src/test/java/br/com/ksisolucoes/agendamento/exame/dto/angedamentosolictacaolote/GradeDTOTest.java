package br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote;

import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote.BuildCenarioGradeDTOTest.*;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class GradeDTOTest {

    @Test
    public void deveTerHorarioDisponivel() {
        GradeDTO gradeDTO = BuildCenarioGradeDTOTest.buildGradeDTOComHorarioDisponivel();
        assertTrue("Deve ter horario disponivel", gradeDTO.temHorariosDisponiveis());
    }

    @Test
    public void naoDeveTerHorarioDisponivel() {
        GradeDTO gradeDTO = BuildCenarioGradeDTOTest.buildGradeDTOSemHorarioDisponivel();
        assertFalse("Não deve ter horario disponivel", gradeDTO.temHorariosDisponiveis());
    }

    @Test
    public void devePossuirPeloMenosUmProcedimento() {
        GradeDTO gradeDTO = BuildCenarioGradeDTOTest.buildGradeDTOComHorarioDisponivel();
        List<ExameProcedimento> procedimentos = Arrays.asList(exame_procedimento_1, exame_procedimento_4);
        assertTrue("Deve atender os procedimentos", gradeDTO.atendeAosProcedimentos(procedimentos));
    }

    @Test
    public void naoDevePossuirProcedimento() {
        GradeDTO gradeDTO = BuildCenarioGradeDTOTest.buildGradeDTOComHorarioDisponivel();
        List<ExameProcedimento> procedimentos = Arrays.asList(exame_procedimento_3, exame_procedimento_4);
        assertFalse("Não deve atender os procedimentos", gradeDTO.atendeAosProcedimentos(procedimentos));
    }

    @Test
    public void deveAtenderAosProcedimentos() {
        GradeDTO gradeDTO = BuildCenarioGradeDTOTest.buildGradeDTOComHorarioDisponivel();
        gradeDTO.setExames(new ArrayList<GradeExameDTO>());
        List<ExameProcedimento> procedimentos = Arrays.asList(exame_procedimento_1, exame_procedimento_4);
        assertTrue("Deve atender os procedimentos", gradeDTO.atendeAosProcedimentos(procedimentos));
    }
}