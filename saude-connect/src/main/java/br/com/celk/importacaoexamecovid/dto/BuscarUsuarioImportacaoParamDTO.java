package br.com.celk.importacaoexamecovid.dto;

import java.io.Serializable;
import java.util.List;

public class BuscarUsuarioImportacaoParamDTO implements Serializable {

    private ImportacaoExameCovidDTO importacaoExameCovid;
    private List<UsurioCadsusImportacaoExameCovidDTO> usuariosImportacaoCovidPorCpf;
    private List<UsurioCadsusImportacaoExameCovidDTO> usuariosImportacaoCovidPorDataNascimento;

    public BuscarUsuarioImportacaoParamDTO() { }

    public BuscarUsuarioImportacaoParamDTO(ImportacaoExameCovidDTO importacaoExameCovid, List<UsurioCadsusImportacaoExameCovidDTO> usuariosImportacaoCovidPorCpf, List<UsurioCadsusImportacaoExameCovidDTO> usuariosImportacaoCovidPorDataNascimento) {
        this.importacaoExameCovid = importacaoExameCovid;
        this.usuariosImportacaoCovidPorCpf = usuariosImportacaoCovidPorCpf;
        this.usuariosImportacaoCovidPorDataNascimento = usuariosImportacaoCovidPorDataNascimento;
    }

    public ImportacaoExameCovidDTO getImportacaoExameCovid() {
        return importacaoExameCovid;
    }

    public void setImportacaoExameCovid(ImportacaoExameCovidDTO importacaoExameCovid) {
        this.importacaoExameCovid = importacaoExameCovid;
    }

    public List<UsurioCadsusImportacaoExameCovidDTO> getUsuariosImportacaoCovidPorCpf() {
        return usuariosImportacaoCovidPorCpf;
    }

    public void setUsuariosImportacaoCovidPorCpf(List<UsurioCadsusImportacaoExameCovidDTO> usuariosImportacaoCovidPorCpf) {
        this.usuariosImportacaoCovidPorCpf = usuariosImportacaoCovidPorCpf;
    }

    public List<UsurioCadsusImportacaoExameCovidDTO> getUsuariosImportacaoCovidPorDataNascimento() {
        return usuariosImportacaoCovidPorDataNascimento;
    }

    public void setUsuariosImportacaoCovidPorDataNascimento(List<UsurioCadsusImportacaoExameCovidDTO> usuariosImportacaoCovidPorDataNascimento) {
        this.usuariosImportacaoCovidPorDataNascimento = usuariosImportacaoCovidPorDataNascimento;
    }
}
