package br.com.celk.unidadesaude.esus.relatorios;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR> bilert
 */
public class RelacaoGestantesSemAderenciaIndicadoresDTO implements Serializable {

    private Long cdUsuarioCadsus;
    private String nmUsuarioCadsus;
    private Date dtNascimento;
    private String dsEquipe;
    private Date dum;
    private Date dpp;
    private Integer idade;
    private String telefone;
    private String indicador2;
    private String indicador1PrimeiraConsultaPN;
    private String indicador1QtdeAtendimentos;
    private String indicador3ConsultaOdontologica;
    private String consulta1AntesDaSemana12;
    private String seisOuMaisConsultas;
    private String indicador2ExameSifilis;
    private String endereco;
    private String indicador2ExameHIV;
    private String bairro;
    private String tipoEquipe;
    private String equipeAtiva;
    private String equipeAtendimento;
    private String equipeResidencia;
    private String microArea;
    private String celular;

    public RelacaoGestantesSemAderenciaIndicadoresDTO(){}

    public RelacaoGestantesSemAderenciaIndicadoresDTO(Long cdUsuarioCadsus, String nmUsuarioCadsus, Date dtNascimento, String dsEquipe, Date dum, Date dpp, Integer idade, String telefone, String indicador2, String indicador1PrimeiraConsultaPN, String indicador1QtdeAtendimentos, String indicador3ConsultaOdontologica, String consulta1AntesDaSemana12, String seisOuMaisConsultas, String indicador2ExameSifilis, String endereco, String indicador2ExameHIV, String bairro, String tipoEquipe, String equipeAtiva, String equipeAtendimento, String equipeResidencia, String microArea, String celular) {
        this.cdUsuarioCadsus = cdUsuarioCadsus;
        this.nmUsuarioCadsus = nmUsuarioCadsus;
        this.dtNascimento = dtNascimento;
        this.dsEquipe = dsEquipe;
        this.dum = dum;
        this.dpp = dpp;
        this.idade = idade;
        this.telefone = telefone;
        this.indicador2 = indicador2;
        this.indicador1PrimeiraConsultaPN = indicador1PrimeiraConsultaPN;
        this.indicador1QtdeAtendimentos = indicador1QtdeAtendimentos;
        this.indicador3ConsultaOdontologica = indicador3ConsultaOdontologica;
        this.consulta1AntesDaSemana12 = consulta1AntesDaSemana12;
        this.seisOuMaisConsultas = seisOuMaisConsultas;
        this.indicador2ExameSifilis = indicador2ExameSifilis;
        this.endereco = endereco;
        this.indicador2ExameHIV = indicador2ExameHIV;
        this.bairro = bairro;
        this.tipoEquipe = tipoEquipe;
        this.equipeAtiva = equipeAtiva;
        this.equipeAtendimento = equipeAtendimento;
        this.equipeResidencia = equipeResidencia;
        this.microArea = microArea;
        this.celular = celular;
    }

    public Long getCdUsuarioCadsus() {
        return cdUsuarioCadsus;
    }

    public void setCdUsuarioCadsus(Long cdUsuarioCadsus) {
        this.cdUsuarioCadsus = cdUsuarioCadsus;
    }

    public String getNmUsuarioCadsus() {
        return nmUsuarioCadsus;
    }

    public void setNmUsuarioCadsus(String nmUsuarioCadsus) {
        this.nmUsuarioCadsus = nmUsuarioCadsus;
    }


    public Date getDtNascimento() {
        return dtNascimento;
    }

    public void setDtNascimento(Date dtNascimento) {
        this.dtNascimento = dtNascimento;
    }

    public String getDsEquipe() {
        return dsEquipe;
    }

    public void setDsEquipe(String dsEquipe) {
        this.dsEquipe = dsEquipe;
    }

    public Date getDum() {
        return dum;
    }

    public void setDum(Date dum) {
        this.dum = dum;
    }

    public Date getDpp() {
        return dpp;
    }

    public void setDpp(Date dpp) {
        this.dpp = dpp;
    }

    public String getTelefone() {
        return telefone;
    }

    public void setTelefone(String telefone) {
        this.telefone = telefone;
    }

    public String getConsulta1AntesDaSemana12() {
        return consulta1AntesDaSemana12;
    }

    public void setConsulta1AntesDaSemana12(String consulta1AntesDaSemana12) {
        this.consulta1AntesDaSemana12 = consulta1AntesDaSemana12;
    }

    public String getSeisOuMaisConsultas() {
        return seisOuMaisConsultas;
    }

    public void setSeisOuMaisConsultas(String seisOuMaisConsultas) {
        this.seisOuMaisConsultas = seisOuMaisConsultas;
    }

    public String getIndicador2() {
        return indicador2;
    }

    public void setIndicador2(String indicador2) {
        this.indicador2 = indicador2;
    }

    public String getIndicador1PrimeiraConsultaPN() {
        return indicador1PrimeiraConsultaPN;
    }

    public void setIndicador1PrimeiraConsultaPN(String indicador1PrimeiraConsultaPN) {
        this.indicador1PrimeiraConsultaPN = indicador1PrimeiraConsultaPN;
    }

    public String getIndicador1QtdeAtendimentos() {
        return indicador1QtdeAtendimentos;
    }

    public void setIndicador1QtdeAtendimentos(String indicador1QtdeAtendimentos) {
        this.indicador1QtdeAtendimentos = indicador1QtdeAtendimentos;
    }

    public String getIndicador3ConsultaOdontologica() {
        return indicador3ConsultaOdontologica;
    }

    public void setIndicador3ConsultaOdontologica(String indicador3ConsultaOdontologica) {
        this.indicador3ConsultaOdontologica = indicador3ConsultaOdontologica;
    }

    public String getIndicador2ExameSifilis() {
        return indicador2ExameSifilis;
    }

    public void setIndicador2ExameSifilis(String indicador2ExameSifilis) {
        this.indicador2ExameSifilis = indicador2ExameSifilis;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public String getTipoEquipe() {
        return tipoEquipe;
    }

    public void setTipoEquipe(String tipoEquipe) {
        this.tipoEquipe = tipoEquipe;
    }

    public String getIndicador2ExameHIV() {
        return indicador2ExameHIV;
    }

    public void setIndicador2ExameHIV(String indicador2ExameHIV) {
        this.indicador2ExameHIV = indicador2ExameHIV;
    }

    public String getBairro() {
        return bairro;
    }

    public void setBairro(String bairro) {
        this.bairro = bairro;
    }

    public String getEquipeAtiva() {
        return equipeAtiva;
    }

    public void setEquipeAtiva(String equipeAtiva) {
        this.equipeAtiva = equipeAtiva;
    }

    public Integer getIdade() {
        return idade;
    }

    public void setIdade(Integer idade) {
        this.idade = idade;
    }

    public String getEquipeAtendimento() {
        return equipeAtendimento;
    }

    public void setEquipeAtendimento(String equipeAtendimento) {
        this.equipeAtendimento = equipeAtendimento;
    }

    public String getEquipeResidencia() {
        return equipeResidencia;
    }

    public void setEquipeResidencia(String equipeResidencia) {
        this.equipeResidencia = equipeResidencia;
    }

    public String getMicroArea() {
        return microArea;
    }

    public void setMicroArea(String microArea) {
        this.microArea = microArea;
    }

    public String getCelular() {
        return celular;
    }

    public void setCelular(String celular) {
        this.celular = celular;
    }
}
