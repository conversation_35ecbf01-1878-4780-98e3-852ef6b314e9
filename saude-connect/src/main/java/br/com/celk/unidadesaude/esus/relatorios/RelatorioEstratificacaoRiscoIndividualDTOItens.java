package br.com.celk.unidadesaude.esus.relatorios;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.EstratificacaoRisco;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class RelatorioEstratificacaoRiscoIndividualDTOItens implements Serializable {
    private Long quantidade;
    private Long flagClassificacaoRisco;
    private EquipeArea equipeArea;
    private EquipeMicroArea equipeMicroArea;
    private UsuarioCadsus usuarioCadsus;
    private Date dataCadastro;

    private String descricao;

    public RelatorioEstratificacaoRiscoIndividualDTOItens(String descricao, Long quantidade, Long flagClassificacaoRisco) {
        this.quantidade = quantidade;
        this.flagClassificacaoRisco = flagClassificacaoRisco;
    }

    public RelatorioEstratificacaoRiscoIndividualDTOItens() {
    }

    public Long getQuantidade() {
        return Coalesce.asLong(quantidade);
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Long getFlagClassificacaoRisco() {
        return flagClassificacaoRisco;
    }

    public void setFlagClassificacaoRisco(Long flagClassificacaoRisco) {
        this.flagClassificacaoRisco = flagClassificacaoRisco;
    }

    public EquipeArea getEquipeArea() {
        return equipeArea;
    }

    public void setEquipeArea(EquipeArea equipeArea) {
        this.equipeArea = equipeArea;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public String getDescricaoArea() {
        StringBuilder descricao = new StringBuilder();
        descricao.append(Bundle.getStringApplication("rotulo_area"));
        descricao.append(": ");

        if (getEquipeArea() != null && getEquipeArea().getCodigo() != null) {
            descricao.append(getEquipeArea().getDescricao());
        } else {
            descricao.append("Sem Área");
        }

        return descricao.toString();
    }

    public String getDescricaoMicroArea() {
        StringBuilder descricao = new StringBuilder();
        descricao.append(Bundle.getStringApplication("rotulo_micro_area"));
        descricao.append(": ");

        if (getEquipeMicroArea() != null && getEquipeMicroArea().getCodigo() != null && getEquipeArea() != null && getEquipeArea().getCodigo() != null) {
            descricao.append(getEquipeArea().getDescricao());

            descricao.append(" / ");
            if (getEquipeMicroArea().getMicroArea() != null) {
                descricao.append(getEquipeMicroArea().getMicroArea());
            } else {
                descricao.append("Sem Microárea");
            }
        } else {
            descricao.append("Sem Área / Sem Microárea");
        }

        return descricao.toString();
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getDescricaoClassificacaoRisco(){
        if(flagClassificacaoRisco != null){
            return  EstratificacaoRisco.Risco.valeuOf(flagClassificacaoRisco).descricao();
        }
        return "";
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }
}
