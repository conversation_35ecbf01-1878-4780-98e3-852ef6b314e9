package br.com.celk.unidadesaude.exames;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class ExamesAutorizadosDTOParam implements Serializable {

    private Long exame;
    private String paciente;
    private Empresa executante;
    private Empresa solicitante;
    private TipoExame tipoExame;
    private DatePeriod periodo;
    private String propSort;
    private boolean ascending;

    public Long getExame() {
        return exame;
    }

    public void setExame(Long exame) {
        this.exame = exame;
    }

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public Empresa getExecutante() {
        return executante;
    }

    public void setExecutante(Empresa executante) {
        this.executante = executante;
    }

    public Empresa getSolicitante() {
        return solicitante;
    }

    public void setSolicitante(Empresa solicitante) {
        this.solicitante = solicitante;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public void setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }
}
