package br.com.celk.unidadesaude.esus.cds.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.EquipeArea;
import br.com.ksisolucoes.vo.basico.EquipeMicroArea;
import br.com.ksisolucoes.vo.cadsus.Profissional;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by sulivan on 21/07/17.
 */
public class DetalhesItensIntegracaoEsusDTOParam implements Serializable {

    private Long codigoExportacaoEsusProcesso;
    private Long codigoAtendimento;
    private Long codigoAtividadeGrupo;
    private Long codigoVisitaDomiciliar;
    private Long codigoPaciente;
    private Long codigoDomicilio;
    private Long codigoTermoRecusa;
    private Long comInconsistencia;
    private Long codigoUsuarioCadsus;
    private Long numeroFamilia;
    private String endereco;
    private String paciente;
    private String nomeMae;
    private String uuid;
    private String inconsistencia;
    private String numeroCartao;
    private String propSort;
    private boolean ascending;
    private Empresa empresa;
    private Profissional profissional;
    private Date dataAtendimento;
    private Date dataAtividadeGrupo;
    private Date dataVisita;
    private Date dataNascimento;
    private EquipeMicroArea equipeMicroArea;
    private EquipeArea area;
    private Long foraDeArea;

    public Long getCodigoExportacaoEsusProcesso() {
        return codigoExportacaoEsusProcesso;
    }

    public void setCodigoExportacaoEsusProcesso(Long codigoExportacaoEsusProcesso) {
        this.codigoExportacaoEsusProcesso = codigoExportacaoEsusProcesso;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getInconsistencia() {
        return inconsistencia;
    }

    public void setInconsistencia(String inconsistencia) {
        this.inconsistencia = inconsistencia;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public String getPaciente() {
        return paciente;
    }

    public void setPaciente(String paciente) {
        this.paciente = paciente;
    }

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public Long getCodigoAtendimento() {
        return codigoAtendimento;
    }

    public void setCodigoAtendimento(Long codigoAtendimento) {
        this.codigoAtendimento = codigoAtendimento;
    }

    public String getNumeroCartao() {
        return numeroCartao;
    }

    public void setNumeroCartao(String numeroCartao) {
        this.numeroCartao = numeroCartao;
    }

    public Long getCodigoAtividadeGrupo() {
        return codigoAtividadeGrupo;
    }

    public void setCodigoAtividadeGrupo(Long codigoAtividadeGrupo) {
        this.codigoAtividadeGrupo = codigoAtividadeGrupo;
    }

    public Long getCodigoVisitaDomiciliar() {
        return codigoVisitaDomiciliar;
    }

    public void setCodigoVisitaDomiciliar(Long codigoVisitaDomiciliar) {
        this.codigoVisitaDomiciliar = codigoVisitaDomiciliar;
    }

    public Date getDataVisita() {
        return dataVisita;
    }

    public void setDataVisita(Date dataVisita) {
        this.dataVisita = dataVisita;
    }

    public Date getDataAtividadeGrupo() {
        return dataAtividadeGrupo;
    }

    public void setDataAtividadeGrupo(Date dataAtividadeGrupo) {
        this.dataAtividadeGrupo = dataAtividadeGrupo;
    }

    public Long getNumeroFamilia() {
        return numeroFamilia;
    }

    public void setNumeroFamilia(Long numeroFamilia) {
        this.numeroFamilia = numeroFamilia;
    }

    public String getNomeMae() {
        return nomeMae;
    }

    public void setNomeMae(String nomeMae) {
        this.nomeMae = nomeMae;
    }

    public Date getDataNascimento() {
        return dataNascimento;
    }

    public void setDataNascimento(Date dataNascimento) {
        this.dataNascimento = dataNascimento;
    }

    public Long getCodigoPaciente() {
        return codigoPaciente;
    }

    public void setCodigoPaciente(Long codigoPaciente) {
        this.codigoPaciente = codigoPaciente;
    }

    public String getEndereco() {
        return endereco;
    }

    public void setEndereco(String endereco) {
        this.endereco = endereco;
    }

    public Long getCodigoDomicilio() {
        return codigoDomicilio;
    }

    public void setCodigoDomicilio(Long codigoDomicilio) {
        this.codigoDomicilio = codigoDomicilio;
    }

    public EquipeMicroArea getEquipeMicroArea() {
        return equipeMicroArea;
    }

    public void setEquipeMicroArea(EquipeMicroArea equipeMicroArea) {
        this.equipeMicroArea = equipeMicroArea;
    }

    public EquipeArea getArea() {
        return area;
    }

    public void setArea(EquipeArea area) {
        this.area = area;
    }

    public Long getComInconsistencia() {
        return comInconsistencia;
    }

    public void setComInconsistencia(Long comInconsistencia) {
        this.comInconsistencia = comInconsistencia;
    }

    public Long getCodigoTermoRecusa() {
        return codigoTermoRecusa;
    }

    public void setCodigoTermoRecusa(Long codigoTermoRecusa) {
        this.codigoTermoRecusa = codigoTermoRecusa;
    }

    public Long getForaDeArea() {
        return foraDeArea;
    }

    public void setForaDeArea(Long foraDeArea) {
        this.foraDeArea = foraDeArea;
    }

    public Long getCodigoUsuarioCadsus() {
        return codigoUsuarioCadsus;
    }

    public void setCodigoUsuarioCadsus(Long codigoUsuarioCadsus) {
        this.codigoUsuarioCadsus = codigoUsuarioCadsus;
    }
}
