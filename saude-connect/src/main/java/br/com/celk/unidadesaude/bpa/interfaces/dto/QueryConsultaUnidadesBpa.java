package br.com.celk.unidadesaude.bpa.interfaces.dto;

import br.com.ksisolucoes.bo.command.CommandQuery;
import br.com.ksisolucoes.dao.HQLHelper;
import br.com.ksisolucoes.dao.exception.DAOException;
import br.com.ksisolucoes.vo.atendimento.Bpa;
import br.com.ksisolucoes.vo.atendimento.BpaProcesso;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public class QueryConsultaUnidadesBpa extends CommandQuery {

    private final BpaProcesso bpaProcesso;
    private List<Bpa> result;

    public QueryConsultaUnidadesBpa(BpaProcesso bpaProcesso) {
        this.bpaProcesso = bpaProcesso;
    }

    @Override
    protected void createQuery(HQLHelper hql) throws DAOException {
        hql.setTypeSelect(Bpa.class.getName());

        hql.addToSelect("empresa.codigo", "empresa.codigo");
        hql.addToSelect("empresa.descricao", "empresa.descricao");

        hql.addToFrom("Bpa bpa "
                + "inner join bpa.empresa empresa");

        hql.addToWhereWhithAnd("bpa.bpaProcesso = ", bpaProcesso.getCodigo());
        hql.addToOrder("empresa.descricao");
        hql.setDistinct(true);
    }

    @Override
    protected void result(HQLHelper hql, Object result) {
        this.result = hql.getBeanList((List<Map<String, Object>>) result, false);
    }

    public Collection getResult() {
        return result;
    }
}
