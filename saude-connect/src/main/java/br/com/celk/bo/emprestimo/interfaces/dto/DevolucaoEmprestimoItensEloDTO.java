package br.com.celk.bo.emprestimo.interfaces.dto;

import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoElo;
import br.com.ksisolucoes.vo.emprestimo.DevolucaoEmprestimoItem;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class DevolucaoEmprestimoItensEloDTO implements Serializable {
    
    private DevolucaoEmprestimoItem devolucaoEmprestimoItem = new DevolucaoEmprestimoItem();
    private List<DevolucaoEmprestimoElo> devolucaoEmprestimoEloList = new ArrayList<DevolucaoEmprestimoElo>();

    public DevolucaoEmprestimoItem getDevolucaoEmprestimoItem() {
        return devolucaoEmprestimoItem;
    }

    public void setDevolucaoEmprestimoItem(DevolucaoEmprestimoItem devolucaoEmprestimoItem) {
        this.devolucaoEmprestimoItem = devolucaoEmprestimoItem;
    }

    public List<DevolucaoEmprestimoElo> getDevolucaoEmprestimoEloList() {
        return devolucaoEmprestimoEloList;
    }

    public void setDevolucaoEmprestimoEloList(List<DevolucaoEmprestimoElo> devolucaoEmprestimoEloList) {
        this.devolucaoEmprestimoEloList = devolucaoEmprestimoEloList;
    }
}
