package br.com.celk.bo.service.rest.assinaturadigital.bry.dto.kms;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

@JsonIgnoreProperties(ignoreUnknown = true)
public class KmsData {

    @JsonProperty("pin")
    private String pin;

    @JsonProperty("token")
    private String token;

    @JsonProperty("uuid_cert")
    private String uuidCertificado;

    @JsonProperty("user")
    private String user;

    private String url;

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUuidCertificado() {
        return uuidCertificado;
    }

    public void setUuidCertificado(String uuidCertificado) {
        this.uuidCertificado = uuidCertificado;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
