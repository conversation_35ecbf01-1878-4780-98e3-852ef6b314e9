package br.com.celk.bo.service.rest.integracao.simsistemas;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

public class SimResponseEstabelecimentoDTO implements Serializable {

    @JsonProperty("nome_entidade")
    private String nomeEntidade;
    @JsonProperty("dados")
    private List<EstabelecimentoSimDTO> dados;
    @JsonProperty("pagina")
    private int pagina;
    @JsonProperty("registros_nesta_pagina")
    private int registrosNestaPagina;
    @JsonProperty("total_de_paginas")
    private int totalDePaginas;
    @JsonProperty("total_registros")
    private int totalRegistros;

    public SimResponseEstabelecimentoDTO() {}

    public SimResponseEstabelecimentoDTO(String nomeEntidade, List<EstabelecimentoSimDTO> dados, int pagina, int registrosNestaPagina, int totalDePaginas, int totalRegistros) {
        this.nomeEntidade = nomeEntidade;
        this.dados = dados;
        this.pagina = pagina;
        this.registrosNestaPagina = registrosNestaPagina;
        this.totalDePaginas = totalDePaginas;
        this.totalRegistros = totalRegistros;
    }

    public String getNomeEntidade() {
        return nomeEntidade;
    }

    public void setNomeEntidade(String nomeEntidade) {
        this.nomeEntidade = nomeEntidade;
    }

    public List<EstabelecimentoSimDTO> getDados() {
        return dados;
    }

    public void setDados(List<EstabelecimentoSimDTO> dados) {
        this.dados = dados;
    }

    public int getPagina() {
        return pagina;
    }

    public void setPagina(int pagina) {
        this.pagina = pagina;
    }

    public int getRegistrosNestaPagina() {
        return registrosNestaPagina;
    }

    public void setRegistrosNestaPagina(int registrosNestaPagina) {
        this.registrosNestaPagina = registrosNestaPagina;
    }

    public int getTotalDePaginas() {
        return totalDePaginas;
    }

    public void setTotalDePaginas(int totalDePaginas) {
        this.totalDePaginas = totalDePaginas;
    }

    public int getTotalRegistros() {
        return totalRegistros;
    }

    public void setTotalRegistros(int totalRegistros) {
        this.totalRegistros = totalRegistros;
    }
}
