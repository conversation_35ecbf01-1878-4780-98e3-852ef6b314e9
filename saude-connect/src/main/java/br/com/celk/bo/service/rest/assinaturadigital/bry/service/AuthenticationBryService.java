package br.com.celk.bo.service.rest.assinaturadigital.bry.service;

import br.com.celk.bo.service.rest.assinaturadigital.bry.dto.autenticacao.TokenServiceBry;
import br.com.celk.util.service.rest.WebserviceResponse;
import br.com.celk.util.service.rest.WebserviceUtil;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class AuthenticationBryService {
    public Map<String, String> loadAuthenticationToken(String clientId, String clientSecret) throws ValidacaoException, IOException {
        TokenServiceBry tokenServiceBry = new TokenServiceBry();
        tokenServiceBry.setClientId(clientId);
        tokenServiceBry.setClientSecret(clientSecret);
        tokenServiceBry.setGrantType("client_credentials");

        WebserviceResponse response = new WebserviceUtil().sendPostRequestFormUrlEncoded(tokenServiceBry.getAuthEndpoint(), tokenServiceBry, null);
        JSONObject json = new JSONObject(response.getResponseMessage());
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + json.getString("access_token"));
        return headers;
    }
}
