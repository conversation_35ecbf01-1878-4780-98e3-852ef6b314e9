package br.com.celk.esus.interfaces.dto;

import br.com.ksisolucoes.vo.esus.Esus;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by maicon on 03/10/16.
 */
public class EsusDetalheDTO implements Serializable {

    private Esus esus;
    private List<EsusDetalheTransporteDTO> esusTransporteList = new ArrayList<EsusDetalheTransporteDTO>();

    public Esus getEsus() {
        return esus;
    }

    public void setEsus(Esus esus) {
        this.esus = esus;
    }

    public List<EsusDetalheTransporteDTO> getEsusTransporteList() {
        return esusTransporteList;
    }

    public void setEsusTransporteList(List<EsusDetalheTransporteDTO> esusTransporteList) {
        this.esusTransporteList = esusTransporteList;
    }
}
