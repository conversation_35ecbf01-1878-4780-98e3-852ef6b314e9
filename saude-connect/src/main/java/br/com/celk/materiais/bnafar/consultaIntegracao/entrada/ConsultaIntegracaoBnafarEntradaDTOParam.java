package br.com.celk.materiais.bnafar.consultaIntegracao.entrada;


import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.entradas.dispensacao.DispensacaoMedicamento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;

import java.io.Serializable;

public class ConsultaIntegracaoBnafarEntradaDTOParam implements Serializable {

    private Empresa Empresa;
    private Produto Produto;
    private DatePeriod dataEntrada;
    private Long statusRegistro;
    private String numeroDocumento;

    public Empresa getEmpresa() {
        return Empresa;
    }

    public void setEmpresa(Empresa empresa) {
        Empresa = empresa;
    }

    public Produto getProduto() {
        return Produto;
    }

    public void setProduto(Produto produto) {
        Produto = produto;
    }


    public DatePeriod getDataEntrada() {
        return dataEntrada;
    }

    public void setDataEntrada(DatePeriod dataEntrada) {
        this.dataEntrada = dataEntrada;
    }

    public Long getStatusRegistro() {
        return statusRegistro;
    }

    public void setStatusRegistro(Long statusRegistro) {
        this.statusRegistro = statusRegistro;
    }

    public String getNumeroDocumento() {
        return numeroDocumento;
    }

    public void setNumeroDocumento(String numeroDocumento) {
        this.numeroDocumento = numeroDocumento;
    }

}
