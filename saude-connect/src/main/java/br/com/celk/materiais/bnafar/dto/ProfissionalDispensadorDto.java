package br.com.celk.materiais.bnafar.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ProfissionalDispensadorDto extends ProfissionalDto implements Serializable {

    public ProfissionalDispensadorDto() {
        super();
    }

    private String numeroCrf;
    private String ufCrf;

    public String getNumeroCrf() {
        return numeroCrf;
    }

    public void setNumeroCrf(String numeroCrf) {
        this.numeroCrf = numeroCrf;
    }

    public String getUfCrf() {
        return ufCrf;
    }

    public void setUfCrf(String ufCrf) {
        this.ufCrf = ufCrf;
    }

    public String toJson() {
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"cns\": ").append(valida(cns)).append(",");
        sb.append("\"cpf\": ").append(valida(cpf)).append(",");
        sb.append("\"numeroCrm\": ").append(valida(numeroCrf)).append(",");
        sb.append("\"ufCrm\": ").append(valida(ufCrf));
        sb.append("}");
        return sb.toString();
    }

    private String valida(String obj){
        return (StringUtils.isNotEmpty(obj)) ? "\"" + obj + "\"" : null;
    }

}
