package br.com.celk.importacaoExame;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.validacao.exception.ValidacaoException;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ImportacaoExameExternoCovidUtils {

    private ImportacaoExameExternoCovidUtils() {}

    public static Date getDate(String data) throws ValidacaoException {
        if (data == null) {
            throw new ValidacaoException(Bundle.getStringApplication("rotulo_csv_erro_formato_data"));
        }

        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        try {
            return sdf.parse(data);
        } catch (ParseException e1) {
            sdf = new SimpleDateFormat("dd/MM/yyyy hh:mm:ss");
            try {
                return sdf.parse(data);
            } catch (ParseException e2) {
                sdf = new SimpleDateFormat("dd/MM/yyyy hh:mm");
                try {
                    return sdf.parse(data);
                } catch (ParseException e3) {
                    throw new ValidacaoException(Bundle.getStringApplication("rotulo_csv_erro_formato_data"));
                }
            }
        }
    }
}
