package br.com.celk.report.vigilancia.roteiroinspecao.dto;

import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.ItemInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.ItemInspecaoPergunta;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroInspecao;
import br.com.ksisolucoes.vo.vigilancia.roteiroinspecao.RoteiroItemInspecao;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ImpressaoRoteiroInspecaoExternoDTO implements Serializable {

    private List<ItemInspecaoPergunta> itemInspecaoPerguntaList;
    private ItemInspecao itemInspecao;
    private RoteiroInspecao roteiroInspecao;
    private RoteiroItemInspecao roteiroItemInspecao;

    public List<ItemInspecaoPergunta> getItemInspecaoPerguntaList() {
        return itemInspecaoPerguntaList;
    }

    public void setItemInspecaoPerguntaList(List<ItemInspecaoPergunta> itemInspecaoPerguntaList) {
        this.itemInspecaoPerguntaList = itemInspecaoPerguntaList;
    }

    public ItemInspecao getItemInspecao() {
        return itemInspecao;
    }

    public void setItemInspecao(ItemInspecao itemInspecao) {
        this.itemInspecao = itemInspecao;
    }

    public RoteiroInspecao getRoteiroInspecao() {
        return roteiroInspecao;
    }

    public void setRoteiroInspecao(RoteiroInspecao roteiroInspecao) {
        this.roteiroInspecao = roteiroInspecao;
    }

    public RoteiroItemInspecao getRoteiroItemInspecao() {
        return roteiroItemInspecao;
    }

    public void setRoteiroItemInspecao(RoteiroItemInspecao roteiroItemInspecao) {
        this.roteiroItemInspecao = roteiroItemInspecao;
    }
}
