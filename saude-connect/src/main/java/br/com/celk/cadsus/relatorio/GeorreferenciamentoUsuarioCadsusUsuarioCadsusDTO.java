package br.com.celk.cadsus.relatorio;

import br.com.ksisolucoes.vo.cadsus.EnderecoDomicilio;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class GeorreferenciamentoUsuarioCadsusUsuarioCadsusDTO implements Serializable {

    private UsuarioCadsus usuarioCadsus;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private EnderecoDomicilio enderecoDomicilio;
    private Double latitude;
    private Double longitude;

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public EnderecoDomicilio getEnderecoDomicilio() {
        return enderecoDomicilio;
    }

    public void setEnderecoDomicilio(EnderecoDomicilio enderecoDomicilio) {
        this.enderecoDomicilio = enderecoDomicilio;
    }
}
