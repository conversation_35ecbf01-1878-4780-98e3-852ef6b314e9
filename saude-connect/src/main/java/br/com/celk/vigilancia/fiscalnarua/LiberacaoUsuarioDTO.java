package br.com.celk.vigilancia.fiscalnarua;

import br.com.ksisolucoes.system.sessao.TenantContext;
import br.com.ksisolucoes.vo.controle.Usuario;

import java.io.Serializable;

public class LiberacaoUsuarioDTO implements Serializable {
    private Long id;
    private String nome;
    private String senha;
    private String login;
    private String status;
    private Long idProfissional;
    private String tenant;

    public LiberacaoUsuarioDTO(Usuario usuario) {
        this.id = usuario.getCodigo();
        this.nome = usuario.getNome();
        this.senha = usuario.getSenha();
        this.login = usuario.getLogin();
        this.status = usuario.getStatus();
        this.idProfissional = usuario.getProfissional().getCodigo();
        this.tenant = TenantContext.getContext();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getSenha() {
        return senha;
    }

    public void setSenha(String senha) {
        this.senha = senha;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getIdProfissional() {
        return idProfissional;
    }

    public void setIdProfissional(Long idProfissional) {
        this.idProfissional = idProfissional;
    }

    public String getTenant() {
        return tenant;
    }

    public void setTenant(String tenant) {
        this.tenant = tenant;
    }
}