package br.com.celk.vigilancia.dto;

import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vigilancia.autoinfracao.AutoInfracao;
import br.com.ksisolucoes.vo.vigilancia.autointimacao.AutoIntimacaoExigencia;
import br.com.ksisolucoes.vo.vigilancia.automulta.AutoMulta;
import br.com.ksisolucoes.vo.vigilancia.processoadministrativo.ProcessoAdministrativo;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class NotificarVctoPrazoDefesaRecursoDTO implements Serializable {

    private Usuario usuario;
    private AutoIntimacaoExigencia autoIntimacaoExigencia;
    private AutoInfracao autoInfracao;
    private AutoMulta autoMulta;
    private ProcessoAdministrativo processoAdministrativo;

    private Date dataPrazo;

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    public AutoIntimacaoExigencia getAutoIntimacaoExigencia() {
        return autoIntimacaoExigencia;
    }

    public void setAutoIntimacaoExigencia(AutoIntimacaoExigencia autoIntimacaoExigencia) {
        this.autoIntimacaoExigencia = autoIntimacaoExigencia;
    }

    public AutoInfracao getAutoInfracao() {
        return autoInfracao;
    }

    public void setAutoInfracao(AutoInfracao autoInfracao) {
        this.autoInfracao = autoInfracao;
    }

    public AutoMulta getAutoMulta() {
        return autoMulta;
    }

    public void setAutoMulta(AutoMulta autoMulta) {
        this.autoMulta = autoMulta;
    }

    public ProcessoAdministrativo getProcessoAdministrativo() {
        return processoAdministrativo;
    }

    public void setProcessoAdministrativo(ProcessoAdministrativo processoAdministrativo) {
        this.processoAdministrativo = processoAdministrativo;
    }

    public Date getDataPrazo() {
        return dataPrazo;
    }

    public void setDataPrazo(Date dataPrazo) {
        this.dataPrazo = dataPrazo;
    }

}
