package br.com.celk.inovamfri.integracao.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.EnumUtil;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario.Doses;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class VacinaIntegrationDTO implements Serializable {
    
    private String idRegistroClient;
    private String nomePaciente;
    private String cnsPaciente;
    private String dataAplicacao;
    private String descricaoVacina;
    private String situacao;
    private String dose;
    @JsonIgnore
    private Long codigoDose;

    private Long codigoUsuarioCadSus;

    @JsonIgnore
    public Long getCodigoUsuarioCadSus() {
        return codigoUsuarioCadSus;
    }

    @JsonIgnore
    public void setCodigoUsuarioCadSus(Long codigoUsuarioCadSus) {
        this.codigoUsuarioCadSus = codigoUsuarioCadSus;
    }

    public VacinaIntegrationDTO() {
    }

    public VacinaIntegrationDTO(String idRegistroClient, String nomePaciente, String cnsPaciente, String dataAplicacao, String descricaoVacina, String dose) {
        this.idRegistroClient = idRegistroClient;
        this.nomePaciente = nomePaciente;
        this.cnsPaciente = cnsPaciente;
        this.dataAplicacao = dataAplicacao;
        this.descricaoVacina = descricaoVacina;
        this.dose = dose;
    }

    public String getIdRegistroClient() {
        return idRegistroClient;
    }

    public void setIdRegistroClient(String idRegistroClient) {
        this.idRegistroClient = idRegistroClient;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getDataAplicacao() {
        return dataAplicacao;
    }

    public void setDataAplicacao(String dataAplicacao) {
        this.dataAplicacao = dataAplicacao;
    }

    public String getDescricaoVacina() {
        return descricaoVacina;
    }

    public void setDescricaoVacina(String descricaoVacina) {
        this.descricaoVacina = descricaoVacina;
    }

    public Long getCodigoDose() {
        return codigoDose;
    }

    public void setCodigoDose(Long codigoDose) {
        this.codigoDose = codigoDose;
    }

    public void setDose(String dose) {
        this.dose = dose;
    }

    public String getSituacao() {
        if(situacao != null){
            return new EnumUtil().resolveDescricao(VacinaAplicacao.StatusVacinaAplicacao.values(), Long.valueOf(situacao));
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getDose() {
        Doses doses = Doses.valueOf(getCodigoDose());
        if (doses != null && !"".equals(Coalesce.asString(doses.toString()))) {
            return doses.toString();
        }
        return "";
    }
    @JsonIgnore
    @Override
    public String toString() {
        return "Código: " + getIdRegistroClient() + ", paciente: " + getNomePaciente() + ", data de aplicação: " + getDataAplicacao()
                + ", vacina: " + getDescricaoVacina() + ", dose: " + getDose();
    }
}
