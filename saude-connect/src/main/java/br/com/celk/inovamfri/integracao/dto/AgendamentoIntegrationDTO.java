package br.com.celk.inovamfri.integracao.dto;

import br.com.ksisolucoes.util.Bundle;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;

import static br.com.ksisolucoes.util.Bundle.getStringApplication;
import static br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario.*;

/**
 * <AUTHOR>
 */
public class AgendamentoIntegrationDTO implements Serializable {

    private String idRegistroClient;
    private String nomePaciente;
    private String cnsPaciente;
    private String tipoProcedimento;
    private String dataAgendamento;
    private String situacao;
    private String estabelecimentoAtendimento;
    private String estabelecimentoSolicitante;
    private String idRegistroClientEstabelecimentoAtendimento;
    private String idRegistroClientEstabelecimentoSolicitante;

    private Long codigoUsuarioCadSus;

    @JsonIgnore
    public Long getCodigoUsuarioCadSus() {
        return codigoUsuarioCadSus;
    }

    @JsonIgnore
    public void setCodigoUsuarioCadSus(Long codigoUsuarioCadSus) {
        this.codigoUsuarioCadSus = codigoUsuarioCadSus;
    }

    public String getIdRegistroClientEstabelecimentoAtendimento() {
        return idRegistroClientEstabelecimentoAtendimento;
    }

    public void setIdRegistroClientEstabelecimentoAtendimento(String idRegistroClientEstabelecimentoAtendimento) {
        this.idRegistroClientEstabelecimentoAtendimento = idRegistroClientEstabelecimentoAtendimento;
    }

    public String getIdRegistroClientEstabelecimentoSolicitante() {
        return idRegistroClientEstabelecimentoSolicitante;
    }

    public void setIdRegistroClientEstabelecimentoSolicitante(String idRegistroClientEstabelecimentoSolicitante) {
        this.idRegistroClientEstabelecimentoSolicitante = idRegistroClientEstabelecimentoSolicitante;
    }

    public String getIdRegistroClient() {
        return idRegistroClient;
    }

    public void setIdRegistroClient(String idRegistroClient) {
        this.idRegistroClient = idRegistroClient;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public String getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(String cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(String tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public String getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(String dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public String getSituacao() {
        if (STATUS_AGENDADO.toString().equals(this.situacao)) {
            situacao = Bundle.getStringApplication("rotulo_agendado");
        } else if (STATUS_CONCLUIDO.toString().equals(this.situacao)) {
            situacao = getStringApplication("rotulo_confirmado");
        } else if (STATUS_CANCELADO.toString().equals(this.situacao)) {
            situacao = getStringApplication("rotulo_cancelado");
        } else if (STATUS_NAO_COMPARECEU.toString().equals(this.situacao)) {
            situacao = getStringApplication("rotulo_nao_compareceu_abv");
        } else if (STATUS_REMANEJADO.toString().equals(this.situacao)) {
            situacao = getStringApplication("rotulo_remanejado");
        } else {
            situacao = "";
        }
        return situacao;
    }

    public void setSituacao(String situacao) {
        this.situacao = situacao;
    }

    public String getEstabelecimentoAtendimento() {
        return estabelecimentoAtendimento;
    }

    public void setEstabelecimentoAtendimento(String estabelecimentoAtendimento) {
        this.estabelecimentoAtendimento = estabelecimentoAtendimento;
    }

    public String getEstabelecimentoSolicitante() {
        return estabelecimentoSolicitante;
    }

    public void setEstabelecimentoSolicitante(String estabelecimentoSolicitante) {
        this.estabelecimentoSolicitante = estabelecimentoSolicitante;
    }

    @Override
    public String toString() {
        return "IdRegistroClient: "+idRegistroClient+" CNS: "+cnsPaciente+" Paciente: "+nomePaciente;
    }

}
