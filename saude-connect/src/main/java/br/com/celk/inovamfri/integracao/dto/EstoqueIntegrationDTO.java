package br.com.celk.inovamfri.integracao.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Dinheiro;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public class EstoqueIntegrationDTO implements Serializable {

    private String idRegistroClient;

    private String descricaoMedicamento;
    private String unidadeMedida;
    private String principioAtivo;
    private String estabelecimento;
    private List<EstoqueIntegrationItemDTO> estoqueMedicamentoLoteDTOList;
//    private String saldoEstabelecimento;

    private String idRegistroClientUnidade;

    @JsonIgnore
    private String codigoProduto;
    @JsonIgnore
    private String lote;
    @JsonIgnore
    private Date dataValidade;

    @JsonIgnore
    private Double estoqueFisico;
    @JsonIgnore
    private Double estoqueEncomendado;
    @JsonIgnore
    private Double estoqueReservado;

    @JsonIgnore
    public String getCodigoProduto() {
        return codigoProduto;
    }

    @JsonIgnore
    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getIdRegistroClientUnidade() {
        return idRegistroClientUnidade;
    }

    public void setIdRegistroClientUnidade(String idRegistroClientUnidade) {
        this.idRegistroClientUnidade = idRegistroClientUnidade;
    }

    public String getIdRegistroClient() {
        return idRegistroClient;
    }

    public void setIdRegistroClient(String idRegistroClient) {
        this.idRegistroClient = idRegistroClient;
    }

    public String getDescricaoMedicamento() {
        return descricaoMedicamento;
    }

    public void setDescricaoMedicamento(String descricaoMedicamento) {
        this.descricaoMedicamento = descricaoMedicamento;
    }

    public String getUnidadeMedida() {
        return unidadeMedida;
    }

    public void setUnidadeMedida(String unidadeMedida) {
        this.unidadeMedida = unidadeMedida;
    }

    public String getPrincipioAtivo() {
        return principioAtivo;
    }

    public void setPrincipioAtivo(String principioAtivo) {
        this.principioAtivo = principioAtivo;
    }

    public String getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(String estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

//    public String getSaldoEstabelecimento() {
//        return saldoEstabelecimento;
//    }
//
//    public void setSaldoEstabelecimento(String saldoEstabelecimento) {
//        this.saldoEstabelecimento = saldoEstabelecimento;
//    }

    @Override
    public String toString() {
        return "ID: "+idRegistroClient+" descricao: "+descricaoMedicamento;
    }

    public Double getEstoqueFisico() {
        return estoqueFisico;
    }

    public void setEstoqueFisico(Double estoqueFisico) {
        this.estoqueFisico = estoqueFisico;
    }

    public Double getEstoqueEncomendado() {
        return estoqueEncomendado;
    }

    public void setEstoqueEncomendado(Double estoqueEncomendado) {
        this.estoqueEncomendado = estoqueEncomendado;
    }

    public Double getEstoqueReservado() {
        return estoqueReservado;
    }

    public void setEstoqueReservado(Double estoqueReservado) {
        this.estoqueReservado = estoqueReservado;
    }

    public List<EstoqueIntegrationItemDTO> getEstoqueMedicamentoLoteDTOList() {
        return estoqueMedicamentoLoteDTOList;
    }

    public void setEstoqueMedicamentoLoteDTOList(List<EstoqueIntegrationItemDTO> estoqueMedicamentoLoteDTOList) {
        this.estoqueMedicamentoLoteDTOList = estoqueMedicamentoLoteDTOList;
    }

    public String getLote() {
        return lote;
    }

    public void setLote(String lote) {
        this.lote = lote;
    }

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    @JsonIgnore
    public Double getSaldoLote(){
        return new Dinheiro(Coalesce.asDouble(getEstoqueFisico())).somar(Coalesce.asDouble(getEstoqueEncomendado())).subtrair(Coalesce.asDouble(getEstoqueReservado())).doubleValue();
    }
}
