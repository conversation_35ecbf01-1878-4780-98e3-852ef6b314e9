package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.vo.prontuario.basico.PreNatal;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class QueryPagerConsultaHistoricoAtendimentosPreNatalDTOparam implements Serializable {

    private PreNatal preNatal;
    private String propSort;
    private boolean ascending;

    private DTOParamConfigureDefault configureParam;

    public DTOParamConfigureDefault getConfigureParam() {
        if (configureParam == null) {
            configureParam = new DTOParamConfigureDefault();
        }
        return configureParam;
    }

    public PreNatal getPreNatal() {
        return preNatal;
    }

    public void setPreNatal(PreNatal preNatal) {
        this.preNatal = preNatal;
    }

    public void setConfigureParam(DTOParamConfigureDefault configureParam) {
        this.configureParam = configureParam;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

}
