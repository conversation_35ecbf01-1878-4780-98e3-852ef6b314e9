package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.ResponsavelTecnico;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CadastroResponsavelTecnicoDTO implements Serializable {
    
    private ResponsavelTecnico responsavelTecnico;
    private List<ResponsavelTecnicoAnexoDTO> responsavelTecnicoAnexoDTOList = new ArrayList<>();
    private List<ResponsavelTecnicoAnexoDTO> responsavelTecnicoAnexoDTOExcluidoList = new ArrayList<>();
    private Map<String, Map<String, String>> camposAlterados;

    public ResponsavelTecnico getResponsavelTecnico() {
        return responsavelTecnico;
    }

    public void setResponsavelTecnico(ResponsavelTecnico responsavelTecnico) {
        this.responsavelTecnico = responsavelTecnico;
    }

    public List<ResponsavelTecnicoAnexoDTO> getResponsavelTecnicoAnexoDTOList() {
        return responsavelTecnicoAnexoDTOList;
    }

    public void setResponsavelTecnicoAnexoDTOList(List<ResponsavelTecnicoAnexoDTO> responsavelTecnicoAnexoDTOList) {
        this.responsavelTecnicoAnexoDTOList = responsavelTecnicoAnexoDTOList;
    }

    public List<ResponsavelTecnicoAnexoDTO> getResponsavelTecnicoAnexoDTOExcluidoList() {
        return responsavelTecnicoAnexoDTOExcluidoList;
    }

    public void setResponsavelTecnicoAnexoDTOExcluidoList(List<ResponsavelTecnicoAnexoDTO> responsavelTecnicoAnexoDTOExcluidoList) {
        this.responsavelTecnicoAnexoDTOExcluidoList = responsavelTecnicoAnexoDTOExcluidoList;
    }

    public Map<String, Map<String, String>> getCamposAlterados() {
        return camposAlterados;
    }

    public void setCamposAlterados(Map<String, Map<String, String>> camposAlterados) {
        this.camposAlterados = camposAlterados;
    }
}
