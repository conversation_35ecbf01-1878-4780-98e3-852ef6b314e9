package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QuerySaldoTransferenciaProdutoDTOParam implements Serializable{

    private Empresa empresa;
    private Produto produto;
    private Long codigoPedidoTransferencia;
    private Long codigoLicitacao;

    public Long getCodigoLicitacao() {
        return codigoLicitacao;
    }

    public void setCodigoLicitacao(Long codigoLicitacao) {
        this.codigoLicitacao = codigoLicitacao;
    }

    public Long getCodigoPedidoTransferencia() {
        return codigoPedidoTransferencia;
    }

    public void setCodigoPedidoTransferencia(Long codigoPedidoTransferencia) {
        this.codigoPedidoTransferencia = codigoPedidoTransferencia;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }
    
}
