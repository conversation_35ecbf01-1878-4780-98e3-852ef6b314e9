package br.com.ksisolucoes.bo.integracao.cnes.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.util.List;

/**
 * Created by sulivan on 16/06/17.
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class CnesProcessoHabilitacaoDTO implements Serializable {

    @XmlElement(name = "DADOS_HABILITACAO")
    private List<CnesProcessoHabilitacaoDadosDTO> dadosHabilitacaoList;

    public List<CnesProcessoHabilitacaoDadosDTO> getDadosHabilitacaoList() {
        return dadosHabilitacaoList;
    }

    public void setDadosHabilitacaoList(List<CnesProcessoHabilitacaoDadosDTO> dadosHabilitacaoList) {
        this.dadosHabilitacaoList = dadosHabilitacaoList;
    }
}
