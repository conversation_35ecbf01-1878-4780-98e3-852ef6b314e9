package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;

import java.io.Serializable;

/**
 * Created by sulivan on 09/06/17.
 */
public class ConsultaAtendimentosDTOParam implements Serializable {

    public static enum PeriodoPor implements IEnum<PeriodoPor> {

        DATA_CHEGADA(1L, Bundle.getStringApplication("rotulo_data_chegada")),
        DATA_ATENDIMENTO(2L, Bundle.getStringApplication("rotulo_data_atendimento"));

        private Long value;
        private String descricao;

        private PeriodoPor(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    private Long numeroAtendimento;
    private Long numeroAtendimentoPrincipal;
    private Long situacao;
    private Long periodoPor;
    private UsuarioCadsus paciente;
    private DatePeriod data;
    private Empresa empresa;
    private Profissional profissional;
    private TipoAtendimento tipoAtendimento;
    private Boolean exibeAtendimentoPainel;

    public Long getNumeroAtendimento() {
        return numeroAtendimento;
    }

    public void setNumeroAtendimento(Long numeroAtendimento) {
        this.numeroAtendimento = numeroAtendimento;
    }

    public Long getNumeroAtendimentoPrincipal() {
        return numeroAtendimentoPrincipal;
    }

    public void setNumeroAtendimentoPrincipal(Long numeroAtendimentoPrincipal) {
        this.numeroAtendimentoPrincipal = numeroAtendimentoPrincipal;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    public DatePeriod getData() {
        return data;
    }

    public void setData(DatePeriod data) {
        this.data = data;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public Long getPeriodoPor() {
        return periodoPor;
    }

    public void setPeriodoPor(Long periodoPor) {
        this.periodoPor = periodoPor;
    }

    public Boolean getExibeAtendimentoPainel() {
        return exibeAtendimentoPainel;
    }

    public void setExibeAtendimentoPainel(Boolean exibeAtendimentoPainel) {
        this.exibeAtendimentoPainel = exibeAtendimentoPainel;
    }

}
