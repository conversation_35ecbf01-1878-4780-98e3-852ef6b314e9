/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class QueryHistoricoMedicamentoReceituarioDTO implements Serializable {
    
    public static final String PROP_TIPO_RECEITA = "tipoReceita";
    public static final String PROP_CID = "cid";
    public static final String PROP_DATA_CADASTRO = "dataCadastro";
    public static final String PROP_NOME_PRODUTO = "nomeProduto";
    
    private TipoReceita tipoReceita;
    private Cid cid;
    private Date dataCadastro;
    private String nomeProduto;

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public Date getDataCadastro() {
        return dataCadastro;
    }

    public void setDataCadastro(Date dataCadastro) {
        this.dataCadastro = dataCadastro;
    }

    public String getNomeProduto() {
        return nomeProduto;
    }

    public void setNomeProduto(String nomeProduto) {
        this.nomeProduto = nomeProduto;
    }

    public TipoReceita getTipoReceita() {
        return tipoReceita;
    }

    public void setTipoReceita(TipoReceita tipoReceita) {
        this.tipoReceita = tipoReceita;
    }
    
}
