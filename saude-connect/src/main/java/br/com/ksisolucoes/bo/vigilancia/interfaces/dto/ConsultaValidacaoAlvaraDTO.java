package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.helper.VigilanciaHelper;

import java.io.Serializable;

public class ConsultaValidacaoAlvaraDTO implements Serializable {

    private Long tipoSolicitacao;
    private RequerimentoVigilancia requerimentoVigilancia;
    private Long numero;
    private String dataValidade;

    public String getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(String dataValidade) {
        this.dataValidade = dataValidade;
    }

    public Long getTipoSolicitacao() {
        return tipoSolicitacao;
    }

    public void setTipoSolicitacao(Long tipoSolicitacao) {
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public Long getNumero() {
        return numero;
    }

    public String getNumeroFormatado() {
        if (getNumero() != null) {
            return VigilanciaHelper.formatarProtocolo(getNumero());
        }
        return "";
    }

    public void setNumero(Long numero) {
        this.numero = numero;
    }

    public RequerimentoVigilancia getRequerimentoVigilancia() {
        return requerimentoVigilancia;
    }

    public void setRequerimentoVigilancia(RequerimentoVigilancia requerimentoVigilancia) {
        this.requerimentoVigilancia = requerimentoVigilancia;
    }
}
