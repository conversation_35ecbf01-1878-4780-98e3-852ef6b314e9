package br.com.ksisolucoes.bo.basico.pesquisa.dto;

import br.com.ksisolucoes.util.Valor;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaPesquisa;
import br.com.ksisolucoes.vo.basico.pesquisa.PerguntaResposta;
import br.com.ksisolucoes.vo.basico.pesquisa.Pesquisa;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class GraficoResultadoPesquisaDTO implements Serializable {

    private Long quantidade;
    private Long total;
    private Double porcentagem;
    private Pesquisa pesquisa;
    private PerguntaPesquisa perguntaPesquisa;
    private PerguntaResposta perguntaResposta;

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Long getTotal() {
        return total;
    }

    public void setTotal(Long total) {
        this.total = total;
    }

    public Double getPorcentagem() {
        if (porcentagem == null) {
            porcentagem = getQuantidade().doubleValue() * 100D / getTotal().doubleValue();
        }
        return porcentagem;
    }

    public void setPorcentagem(Double porcentagem) {
        this.porcentagem = porcentagem;
    }

    public Pesquisa getPesquisa() {
        return pesquisa;
    }

    public void setPesquisa(Pesquisa pesquisa) {
        this.pesquisa = pesquisa;
    }

    public PerguntaPesquisa getPerguntaPesquisa() {
        return perguntaPesquisa;
    }

    public void setPerguntaPesquisa(PerguntaPesquisa perguntaPesquisa) {
        this.perguntaPesquisa = perguntaPesquisa;
    }

    public PerguntaResposta getPerguntaResposta() {
        return perguntaResposta;
    }

    public void setPerguntaResposta(PerguntaResposta perguntaResposta) {
        this.perguntaResposta = perguntaResposta;
    }

    public String getLabel() {
        String label = "Quantidade: " + getQuantidade()
                + " - " + Valor.adicionarFormatacaoMonetaria(getPorcentagem()) + "%";
        return label;
    }
}
