package br.com.ksisolucoes.bo.vigilancia.interfaces.dto.requerimentos.inspecaosanitaria;

import br.com.ksisolucoes.vo.vigilancia.RequerimentoInspecaoSanitariaAreaFisica;

import java.io.Serializable;

public class AreaFisicaDTO implements Serializable {

    private Long areaConstruida;
    private Double area;
    private RequerimentoInspecaoSanitariaAreaFisica requerimentoInspecaoSanitariaAreaFisica;


    public Long getAreaConstruida() {
        return areaConstruida;
    }

    public void setAreaConstruida(Long areaConstruida) {
        this.areaConstruida = areaConstruida;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    public RequerimentoInspecaoSanitariaAreaFisica getRequerimentoInspecaoSanitariaAreaFisica() {
        return requerimentoInspecaoSanitariaAreaFisica;
    }

    public void setRequerimentoInspecaoSanitariaAreaFisica(RequerimentoInspecaoSanitariaAreaFisica requerimentoInspecaoSanitariaAreaFisica) {
        this.requerimentoInspecaoSanitariaAreaFisica = requerimentoInspecaoSanitariaAreaFisica;
    }

    public String getAreaConstruidaFormatada() {
        if (this.getArea() != null){
            return RequerimentoInspecaoSanitariaAreaFisica.AreaConstruida.valueOf(this.getAreaConstruida()).descricao();
        }
        return "";
    }
}
