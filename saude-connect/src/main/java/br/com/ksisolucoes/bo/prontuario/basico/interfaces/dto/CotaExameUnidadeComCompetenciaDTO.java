package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadeCota;
import br.com.ksisolucoes.vo.prontuario.basico.ExameUnidadeProcedimento;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CotaExameUnidadeComCompetenciaDTO implements Serializable{

    public static final String PROP_EXAME_UNIDADE_COTA = "exameUnidadeCota";
    public static final String PROP_EXAME_UNIDADE_COMPETENCIA = "exameUnidadeCompetencia";
    public static final String PROP_VALOR_MENSAL = "valorMensal";
    public static final String PROP_COTA = "cotaUtilizada";

    private ExameUnidadeCota exameUnidadeCota;
    private Double valorMensal;
    private Double cotaUtilizada;
    private boolean gerarCotaMesAtual;
    private List<ExameUnidadeProcedimento> lstExames = new ArrayList<ExameUnidadeProcedimento>();

    public ExameUnidadeCota getExameUnidadeCota() {
        return exameUnidadeCota;
    }

    public void setExameUnidadeCota(ExameUnidadeCota exameUnidadeCota) {
        this.exameUnidadeCota = exameUnidadeCota;
    }

    public Double getValorMensal() {
        return valorMensal;
    }

    public void setValorMensal(Double valorMensal) {
        this.valorMensal = valorMensal;
    }

    public Double getCotaUtilizada() {
        return cotaUtilizada;
    }

    public void setCotaUtilizada(Double cotaUtilizada) {
        this.cotaUtilizada = cotaUtilizada;
    }

    public List<ExameUnidadeProcedimento> getLstExames() {
        return lstExames;
    }

    public void setLstExames(List<ExameUnidadeProcedimento> lstExames) {
        this.lstExames = lstExames;
    }

    public boolean isGerarCotaMesAtual() {
        return gerarCotaMesAtual;
    }

    public void setGerarCotaMesAtual(boolean gerarCotaMesAtual) {
        this.gerarCotaMesAtual = gerarCotaMesAtual;
    }
}
