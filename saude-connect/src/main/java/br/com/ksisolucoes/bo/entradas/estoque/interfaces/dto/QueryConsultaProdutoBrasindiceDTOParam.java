package br.com.ksisolucoes.bo.entradas.estoque.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaProdutoBrasindiceDTOParam implements Serializable {

    private String keyword;
    private String codigoTuss;
    private String codigoTiss;
    private String propSort;
    private boolean ascending;

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getCodigoTuss() {
        return codigoTuss;
    }

    public void setCodigoTuss(String codigoTuss) {
        this.codigoTuss = codigoTuss;
    }

    public String getCodigoTiss() {
        return codigoTiss;
    }

    public void setCodigoTiss(String codigoTiss) {
        this.codigoTiss = codigoTiss;
    }

}
