package br.com.ksisolucoes.bo.sae.dto;

import br.com.ksisolucoes.vo.prontuario.basico.ResultadoEsperado;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaIntervencoesEnfermagemDTOParam implements Serializable{
    
    private Long codigo;
    private String descricao;
    private String referencia;
    private String keyword;
    private String propSort;
    private boolean ascending;
    private DiagnosticoEnfermagemSae diagnosticoEnfermagemSae;
    private ResultadoEsperado resultadoEsperado;

    public String getReferencia() {
        return referencia;
    }

    public void setReferencia(String referencia) {
        this.referencia = referencia;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public DiagnosticoEnfermagemSae getDiagnosticoEnfermagemSae() {
        return diagnosticoEnfermagemSae;
    }

    public void setDiagnosticoEnfermagemSae(DiagnosticoEnfermagemSae diagnosticoEnfermagemSae) {
        this.diagnosticoEnfermagemSae = diagnosticoEnfermagemSae;
    }

    public ResultadoEsperado getResultadoEsperado() {
        return resultadoEsperado;
    }

    public void setResultadoEsperado(ResultadoEsperado resultadoEsperado) {
        this.resultadoEsperado = resultadoEsperado;
    }
}
