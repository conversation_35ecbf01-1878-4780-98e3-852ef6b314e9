/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.atividadegrupo.interfaces.dto;

import br.com.ksisolucoes.vo.atividadegrupo.AtividadeGrupoProfissional;
import br.com.ksisolucoes.vo.basico.Empresa;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AtividadeGrupoProfissionalDTO implements Serializable{
    private Empresa unidadeBase;
    private List<AtividadeGrupoProfissional> atividadeGrupoProfissionalList;

    public List<AtividadeGrupoProfissional> getAtividadeGrupoProfissionalList() {
        return atividadeGrupoProfissionalList;
    }

    public void setAtividadeGrupoProfissionalList(List<AtividadeGrupoProfissional> atividadeGrupoProfissionalList) {
        this.atividadeGrupoProfissionalList = atividadeGrupoProfissionalList;
    }

    public Empresa getUnidadeBase() {
        return unidadeBase;
    }

    public void setUnidadeBase(Empresa unidadeBase) {
        this.unidadeBase = unidadeBase;
    }

}
