package br.com.ksisolucoes.bo.integracao.cnes.dto;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by sulivan on 16/06/17.
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class CnesProcessoEquipeProfissionalDTO implements Serializable {

    @XmlElement(name = "DADOS_PROF_EQUIPE")
    private List<CnesProcessoEquipeProfissionalDadosDTO> dadosProfissionalEquipeList;

    public List<CnesProcessoEquipeProfissionalDadosDTO> getDadosProfissionalEquipeList() {
        return dadosProfissionalEquipeList;
    }

    public void setDadosProfissionalEquipeList(List<CnesProcessoEquipeProfissionalDadosDTO> dadosProfissionalEquipeList) {
        this.dadosProfissionalEquipeList = dadosProfissionalEquipeList;
    }

    public void addDadosProfissionalEquipeList (CnesProcessoEquipeProfissionalDadosDTO dados){
        if(dadosProfissionalEquipeList == null){
            dadosProfissionalEquipeList = new ArrayList<>();
        }
        dadosProfissionalEquipeList.add(dados);
    }
}
