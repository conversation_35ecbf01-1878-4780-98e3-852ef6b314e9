package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.*;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.sae.diagnosticoenfermagemsae.DiagnosticoEnfermagemSae;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;

public class RegistroEspecializadoDTO implements Serializable {

    private AtendimentoRegistroEspecializado atendimentoRegistroEspecializado;
    private AtendimentoPrimario atendimentoPrimario;
    private String descricaoAlergia;
    private String descricaoEvolucao;
    private Long diasRetorno;
    private Boolean registrarCidPatologiasPaciente;
    private ClassificacaoAtendimento classificacaoAtendimento;
    private Conduta conduta;
    private List<CondutaAtendimento> condutaList;
    private Long diarreia;
    private AtendimentoMDDA atendimentoMDDA;
    private Long vacinaEmDia;
    private Long permiteOutraNotificacaoCID;
    private Long flagPreencheuNotificacao;

    private Long racionalidadeSaude;
    private Long nasfs;
    private Long localAtendimento;
    private Profissional profissionalAuxiliar;

    private Atendimento atendimento;
    private Long atendimentoCompartilhado;
    private Long tipoAtendimentoEsus;
    private Long adicionarListaProblemas;
    private Long atencaoDomiciliar;
    private TabelaCbo cboProfissionalAuxiliar;

    private Date dumGestante;
    private Date dppDum;
    private Date dppUsg;
    private Date dataUltimaGestacao;
    private Long gravidezPlanejada;
    private Long idadeGestacionalDum;
    private Long idadeGestacionalUsg;
    private String descricaoIdadeGestacionalDum;
    private String descricaoIdadeGestacionalUsg;
    private Date dataPrimeiraUsg;
    private Long numeroGestasPrevias;
    private Long numeroPartos;
    private Long vaginal;
    private Long cesariana;
    private Long abortos;

    private Boolean carregarDadosPreNatal;
    private Boolean carregarAtendimentoPrincipal;
    private Boolean carregarCondutaLocalAtendimento;

    private List<CiapAtendimento> ciapAtendimentoList;
    private Ciap ciapIntervencao;
    private Ciap ciap;

    private Cid cidSecundario;
    private boolean visibleCid = true;
    private boolean visibleCipe = false;

    private List<AtendimentoSoapCiap> atendimentoSoapCiapListSubjetivo;
    private List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList;
    private HashSet<ClassificacaoAtendimento> classificacaoAtendimentoRemovidosList;

    private DiagnosticoEnfermagemSae cipe;
    private List<AtendimentoCipe> cipeList;

    public Cid getCidSecundario() {
        return cidSecundario;
    }

    public void setCidSecundario(Cid cidSecundario) {
        this.cidSecundario = cidSecundario;
    }

    public AtendimentoPrimario getAtendimentoPrimario() {
        return atendimentoPrimario;
    }

    public void setAtendimentoPrimario(AtendimentoPrimario atendimentoPrimario) {
        this.atendimentoPrimario = atendimentoPrimario;
    }

    public Profissional getProfissionalAuxiliar() {
        return profissionalAuxiliar;
    }

    public void setProfissionalAuxiliar(Profissional profissionalAuxiliar) {
        this.profissionalAuxiliar = profissionalAuxiliar;
    }

    public ClassificacaoAtendimento getClassificacaoAtendimento() {
        return classificacaoAtendimento;
    }

    public void setClassificacaoAtendimento(ClassificacaoAtendimento classificacaoAtendimento) {
        this.classificacaoAtendimento = classificacaoAtendimento;
    }

    public Boolean getRegistrarCidPatologiasPaciente() {
        return registrarCidPatologiasPaciente;
    }

    public void setRegistrarCidPatologiasPaciente(Boolean registrarCidPatologiasPaciente) {
        this.registrarCidPatologiasPaciente = registrarCidPatologiasPaciente;
    }

    public Long getDiasRetorno() {
        return diasRetorno;
    }

    public void setDiasRetorno(Long diasRetorno) {
        this.diasRetorno = diasRetorno;
    }

    public Conduta getConduta() {
        return conduta;
    }

    public void setConduta(Conduta conduta) {
        this.conduta = conduta;
    }

    public Long getDiarreia() {
        return diarreia;
    }

    public void setDiarreia(Long diarreia) {
        this.diarreia = diarreia;
    }

    public String getDescricaoAlergia() {
        return descricaoAlergia;
    }

    public void setDescricaoAlergia(String descricaoAlergia) {
        this.descricaoAlergia = descricaoAlergia;
    }

    public List<CondutaAtendimento> getCondutaList() {
        if (CollectionUtils.isEmpty(condutaList)) {
            condutaList = new ArrayList<>();
        }
        return condutaList;
    }

    public void setCondutaList(List<CondutaAtendimento> condutaList) {
        this.condutaList = condutaList;
    }

    public Long getRacionalidadeSaude() {
        return racionalidadeSaude;
    }

    public void setRacionalidadeSaude(Long racionalidadeSaude) {
        this.racionalidadeSaude = racionalidadeSaude;
    }

    public Long getNasfs() {
        return nasfs;
    }

    public void setNasfs(Long nasfs) {
        this.nasfs = nasfs;
    }

    public Long getLocalAtendimento() {
        return localAtendimento;
    }

    public void setLocalAtendimento(Long localAtendimento) {
        this.localAtendimento = localAtendimento;
    }

    public boolean isCarregarCondutaLocalAtendimento() {
        setCarregarCondutaLocalAtendimento(carregarCondutaLocalAtendimento == null);
        return carregarCondutaLocalAtendimento;
    }

    public void setCarregarCondutaLocalAtendimento(boolean carregarCondutaLocalAtendimento) {
        this.carregarCondutaLocalAtendimento = carregarCondutaLocalAtendimento;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public Long getAtendimentoCompartilhado() {
        return atendimentoCompartilhado;
    }

    public void setAtendimentoCompartilhado(Long atendimentoCompartilhado) {
        this.atendimentoCompartilhado = atendimentoCompartilhado;
    }

    public Ciap getCiapIntervencao() {
        return ciapIntervencao;
    }

    public void setCiapIntervencao(Ciap ciapIntervencao) {
        this.ciapIntervencao = ciapIntervencao;
    }

    public AtendimentoRegistroEspecializado getAtendimentoRegistroEspecializado() {
        if(atendimentoRegistroEspecializado == null){
            atendimentoRegistroEspecializado = new AtendimentoRegistroEspecializado();
        }
        return atendimentoRegistroEspecializado;
    }

    public void setAtendimentoRegistroEspecializado(AtendimentoRegistroEspecializado atendimentoRegistroEspecializado) {
        this.atendimentoRegistroEspecializado = atendimentoRegistroEspecializado;
    }

    public Long getTipoAtendimentoEsus() {
        return tipoAtendimentoEsus;
    }

    public void setTipoAtendimentoEsus(Long tipoAtendimentoEsus) {
        this.tipoAtendimentoEsus = tipoAtendimentoEsus;
    }

    public Long getAtencaoDomiciliar() {
        return atencaoDomiciliar;
    }

    public void setAtencaoDomiciliar(Long atencaoDomiciliar) {
        this.atencaoDomiciliar = atencaoDomiciliar;
    }

    public Date getDumGestante() {
        return dumGestante;
    }

    public void setDumGestante(Date dumGestante) {
        this.dumGestante = dumGestante;
    }

    public Date getDataUltimaGestacao() {
        return dataUltimaGestacao;
    }

    public void setDataUltimaGestacao(Date dataUltimaGestacao) {
        this.dataUltimaGestacao = dataUltimaGestacao;
    }

    public Long getGravidezPlanejada() {
        return gravidezPlanejada;
    }

    public void setGravidezPlanejada(Long gravidezPlanejada) {
        this.gravidezPlanejada = gravidezPlanejada;
    }

    public Long getNumeroGestasPrevias() {
        return numeroGestasPrevias;
    }

    public void setNumeroGestasPrevias(Long numeroGestasPrevias) {
        this.numeroGestasPrevias = numeroGestasPrevias;
    }

    public Long getNumeroPartos() {
        return numeroPartos;
    }

    public void setNumeroPartos(Long numeroPartos) {
        this.numeroPartos = numeroPartos;
    }

    public boolean isCarregarDadosPreNatal() {
        setCarregarDadosPreNatal(carregarDadosPreNatal == null);
        return carregarDadosPreNatal;
    }

    public void setCarregarDadosPreNatal(boolean carregarDadosPreNatal) {
        this.carregarDadosPreNatal = carregarDadosPreNatal;
    }

    public TabelaCbo getCboProfissionalAuxiliar() {
        return cboProfissionalAuxiliar;
    }

    public void setCboProfissionalAuxiliar(TabelaCbo cboProfissionalAuxiliar) {
        this.cboProfissionalAuxiliar = cboProfissionalAuxiliar;
    }

    public boolean isCarregarAtendimentoPrincipal() {
        setCarregarAtendimentoPrincipal(carregarAtendimentoPrincipal == null);
        return carregarAtendimentoPrincipal;
    }

    public void setCarregarAtendimentoPrincipal(boolean carregarAtendimentoPrincipal) {
        this.carregarAtendimentoPrincipal = carregarAtendimentoPrincipal;
    }

    public boolean isVisibleCid() {
        return visibleCid;
    }

    public void setVisibleCid(boolean visibleCid) {
        this.visibleCid = visibleCid;
    }

    public String getDescricaoEvolucao() {
        return descricaoEvolucao;
    }

    public void setDescricaoEvolucao(String descricaoEvolucao) {
        this.descricaoEvolucao = descricaoEvolucao;
    }

    public Long getVaginal() {
        return vaginal;
    }

    public void setVaginal(Long vaginal) {
        this.vaginal = vaginal;
    }

    public Long getCesariana() {
        return cesariana;
    }

    public void setCesariana(Long cesariana) {
        this.cesariana = cesariana;
    }

    public Long getAbortos() {
        return abortos;
    }

    public void setAbortos(Long abortos) {
        this.abortos = abortos;
    }

    public Date getDppDum() {
        return dppDum;
    }

    public void setDppDum(Date dppDum) {
        this.dppDum = dppDum;
    }

    public Date getDppUsg() {
        return dppUsg;
    }

    public void setDppUsg(Date dppUsg) {
        this.dppUsg = dppUsg;
    }

    public Date getDataPrimeiraUsg() {
        return dataPrimeiraUsg;
    }

    public void setDataPrimeiraUsg(Date dataPrimeiraUsg) {
        this.dataPrimeiraUsg = dataPrimeiraUsg;
    }

    public Long getAdicionarListaProblemas() {
        return adicionarListaProblemas;
    }

    public void setAdicionarListaProblemas(Long adicionarListaProblemas) {
        this.adicionarListaProblemas = adicionarListaProblemas;
    }

    public Long getIdadeGestacionalDum() {
        return idadeGestacionalDum;
    }

    public void setIdadeGestacionalDum(Long idadeGestacionalDum) {
        this.idadeGestacionalDum = idadeGestacionalDum;
    }

    public void setIdadeGestacionalUsg(Long idadeGestacionalUsg) {
        this.idadeGestacionalUsg = idadeGestacionalUsg;
    }

    public String getDescricaoIdadeGestacionalDum() {
        return descricaoIdadeGestacionalDum;
    }

    public void setDescricaoIdadeGestacionalDum(String descricaoIdadeGestacionalDum) {
        this.descricaoIdadeGestacionalDum = descricaoIdadeGestacionalDum;
    }

    public String getDescricaoIdadeGestacionalUsg() {
        return descricaoIdadeGestacionalUsg;
    }

    public void setDescricaoIdadeGestacionalUsg(String descricaoIdadeGestacionalUsg) {
        this.descricaoIdadeGestacionalUsg = descricaoIdadeGestacionalUsg;
    }

    public Long getIdadeGestacionalUsg() {
        return idadeGestacionalUsg;
    }

    public AtendimentoMDDA getAtendimentoMDDA() {
        return atendimentoMDDA;
    }

    public void setAtendimentoMDDA(AtendimentoMDDA atendimentoMDDA) {
        this.atendimentoMDDA = atendimentoMDDA;
    }

    public Long getVacinaEmDia() {
        return vacinaEmDia;
    }

    public void setVacinaEmDia(Long vacinaEmDia) {
        this.vacinaEmDia = vacinaEmDia;
    }

    public List<AtendimentoClassificacaoAtendimento> getAtendimentoClassificacaoAtendimentoList() {
        if(CollectionUtils.isEmpty(atendimentoClassificacaoAtendimentoList)){
            atendimentoClassificacaoAtendimentoList = new ArrayList<>();
        }
        return atendimentoClassificacaoAtendimentoList;
    }

    public void setAtendimentoClassificacaoAtendimentoList(List<AtendimentoClassificacaoAtendimento> atendimentoClassificacaoAtendimentoList) {
        this.atendimentoClassificacaoAtendimentoList = atendimentoClassificacaoAtendimentoList;
    }

    public HashSet<ClassificacaoAtendimento> getClassificacaoAtendimentoRemovidosList() {
        if(CollectionUtils.isEmpty(classificacaoAtendimentoRemovidosList)){
            classificacaoAtendimentoRemovidosList = new HashSet<>();
        }
        return classificacaoAtendimentoRemovidosList;
    }

    public void setClassificacaoAtendimentoRemovidosList(HashSet<ClassificacaoAtendimento> classificacaoAtendimentoRemovidosList) {
        this.classificacaoAtendimentoRemovidosList = classificacaoAtendimentoRemovidosList;
    }

    public Long getPermiteOutraNotificacaoCID() {
        return permiteOutraNotificacaoCID;
    }

    public void setPermiteOutraNotificacaoCID(Long permiteOutraNotificacaoCID) {
        this.permiteOutraNotificacaoCID = permiteOutraNotificacaoCID;
    }

    public Long getFlagPreencheuNotificacao() {
        return flagPreencheuNotificacao;
    }

    public void setFlagPreencheuNotificacao(Long flagPreencheuNotificacao) {
        this.flagPreencheuNotificacao = flagPreencheuNotificacao;
    }

    public List<AtendimentoCipe> getCipeList() {
        if (CollectionUtils.isEmpty(cipeList)) {
            cipeList = new ArrayList<>();
        }
        return cipeList;
    }

    public void setCipeList(List<AtendimentoCipe> cipeList) {
        this.cipeList = cipeList;
    }

    public DiagnosticoEnfermagemSae getCipe() {
        return cipe;
    }

    public void setCipe(DiagnosticoEnfermagemSae cipe) {
        this.cipe = cipe;
    }

    public boolean isVisibleCipe() {
        return visibleCipe;
    }

    public void setVisibleCipe(boolean visibleCipe) {
        this.visibleCipe = visibleCipe;
    }

    public List<CiapAtendimento> getCiapAtendimentoList() {
        if(CollectionUtils.isEmpty(ciapAtendimentoList)){
            ciapAtendimentoList = new ArrayList<>();
        }
        return ciapAtendimentoList;
    }

    public void setCiapAtendimentoList(List<CiapAtendimento> ciapAtendimentoList) {
        this.ciapAtendimentoList = ciapAtendimentoList;
    }

    public Ciap getCiap() {
        return ciap;
    }

    public void setCiap(Ciap ciap) {
        this.ciap = ciap;
    }
}