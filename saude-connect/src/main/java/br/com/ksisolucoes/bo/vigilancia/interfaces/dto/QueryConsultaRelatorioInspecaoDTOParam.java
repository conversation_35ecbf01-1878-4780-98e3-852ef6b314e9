package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.Estabelecimento;
import br.com.ksisolucoes.vo.vigilancia.MotivoVisita;
import br.com.ksisolucoes.vo.vigilancia.VigilanciaPessoa;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class QueryConsultaRelatorioInspecaoDTOParam implements Serializable {

    private VigilanciaPessoa vigilanciaPessoa;
    private Estabelecimento estabelecimento;
    private Profissional profissional;
    private MotivoVisita motivoVisita;
    private DatePeriod periodoCadastro;
    private Date dataInspecao;
    private String propSort;
    private boolean ascending;
    private Long numRelaorioInspecao;

    public Estabelecimento getEstabelecimento() {
        return estabelecimento;
    }

    public void setEstabelecimento(Estabelecimento estabelecimento) {
        this.estabelecimento = estabelecimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public MotivoVisita getMotivoVisita() {
        return motivoVisita;
    }

    public void setMotivoVisita(MotivoVisita motivoVisita) {
        this.motivoVisita = motivoVisita;
    }

    public DatePeriod getPeriodoCadastro() {
        return periodoCadastro;
    }

    public void setPeriodoCadastro(DatePeriod periodoCadastro) {
        this.periodoCadastro = periodoCadastro;
    }

    public Date getDataInspecao() {
        return dataInspecao;
    }

    public void setDataInspecao(Date dataInspecao) {
        this.dataInspecao = dataInspecao;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public VigilanciaPessoa getVigilanciaPessoa() {
        return vigilanciaPessoa;
    }

    public void setVigilanciaPessoa(VigilanciaPessoa vigilanciaPessoa) {
        this.vigilanciaPessoa = vigilanciaPessoa;
    }

    public Long getNumRelaorioInspecao() {
        return numRelaorioInspecao;
    }

    public void setNumRelaorioInspecao(Long numRelaorioInspecao) {
        this.numRelaorioInspecao = numRelaorioInspecao;
    }
}
