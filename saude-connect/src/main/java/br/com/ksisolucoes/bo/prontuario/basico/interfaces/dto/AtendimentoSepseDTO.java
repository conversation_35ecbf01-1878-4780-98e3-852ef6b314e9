package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;


import br.com.ksisolucoes.bo.prontuario.web.exame.dto.RequisicaoExamesDTO;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;
import br.com.ksisolucoes.vo.unidadesaude.protocolosepse.AtendimentoSepse;
import br.com.ksisolucoes.vo.unidadesaude.protocolosepse.AtendimentoSepseItem;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class AtendimentoSepseDTO implements Serializable {

    private String orientacoes;
    private AtendimentoSepse atendimentoSepse;
    private Long situacao;
    private Long newsReavaliado;
    private Long drogaVasoativaIndicada;
    private Long confirmacaoSepse;
    private Long hipoteseSepse;
    private Long newsFinal;
    private Long desfecho;
    private RequisicaoExamesDTO requisicaoExamesDTO;
    private PrescricaoInternaDTO prescricaoInternaDTO;
    private Profissional profissionalConfimacaoSepse;
    private Profissional profissionalReavaliacaoSepse;
    private Profissional profissionalAvaliacaoFinalSepse;
    private Date horaReavaliacao;
    private Date horaAvaliacaoFinal;
    private String tempoDecorrente;
    private AtendimentoSepseItem atendimentoSepseItem;
    private List<ReceituarioItem> receituarioItemList;
    private List<ExameRequisicao> exameRequisicaoList;

    public AtendimentoSepseDTO() {
    }

    public AtendimentoSepseDTO(RequisicaoExamesDTO requisicaoExamesDTO) {
        this.requisicaoExamesDTO = requisicaoExamesDTO;
    }

    public AtendimentoSepseDTO(PrescricaoInternaDTO prescricaoInternaDTO) {
        this.prescricaoInternaDTO = prescricaoInternaDTO;
    }

    public String getOrientacoes() {
        return "Hidratação INDICADA' .\n" +
                "Iniciar em até 60 minutos após a abertura do protocolo;\n" +
                "Recomenda  02 acessos periféricos calibrosos (nº 14, 16 ou 18);\n" +
                "INDICADA-30ml/kg se PAS ≤ 90 e/ou PAM ≤ 65 e/ou sinais de hipoperfusão ou choque\n" +
                "NÃO INDICADA .\n" +
                "CONTRAINDA- Justificar na evolução do prontuário.\n" +
                "\n" +
                "ANTIBIÓTICOS\n" +
                "Iniciar em até 60 minutos após a abertura do protocolo;\n" +
                "\n" +
                "EXAMES\n" +
                "Iniciar em até 30 minutos após a abertura do protocolo;\n" +
                "\n" +
                "Foco da SEPSE\n" +
                "PULMONAR - PNEUMONIA COMUNITÁRIA\n" +
                "Opção 1: Ceftriaxona 1g: 02 FA em 100ml de SF 0,9% EV+ Azitromicina 500mg: 01 CP VO\n" +
                "Opção 2: Levofloxacino 500mg: 01 bolsa e meia (750mg) EV - Evitar se evidência de IRA ou DRC\n" +
                "\n" +
                "PULMONAR - PNEUMONIA BRONCOASPIRATIVA\n" +
                "Opção 1: Ceftriaxona 1g: 02 FA em 100ml de SF 0,9% EV+ Clindamicina 600mg: 01 FA em 100ml de SF 0,9%, EV\n" +
                "\n" +
                "PULMONAR - INSTITUCIONALIZADO(A), DPOC E/OU INTERNAÇÃO RECENTE (ALTA HÁ MENOS DE 15 DIAS)\n" +
                "Opção 1: Cefepime 2g: 01 FA em 100ml de SF 0,9% EV\n" +
                "\n" +
                "\n" +
                "ABDOMINAL - PERITONITE BACTERIANA ESPONTÂNEA\n" +
                "Opção 1: Ceftriaxona 1g: 02 FA em 100ml de SF 0,9% EV\n" +
                "\n" +
                "ABDOMINAL - INFECÇÃO LOCALIZADA, PERITONITE BACTERIANA SECUNDÁRIA, ABDOME AGUDO\n" +
                "Opção 1: Ciprofloxacino 200mg: 02 bolsas EV + Metronidazol 500mg: 01 bolsa EV -- Evitar se evidência de IRA ou DRC\n" +
                "Opção 2: Ceftriaxona 1g: 02 FA em 100ml SF 0,9% EV + Metronidazol 500mg: 01 bolsa EV\n" +
                "\n" +
                "\n" +
                "URINÁRIO - INFECÇÃO COMUNITÁRIA\n" +
                "Opção 1: Ciprofloxacino 200mg: 02 bolsas EV - Evitar se evidência de IRA ou DRC\n" +
                "Opção 2: Ceftriaxona 1g: 02 FA + 100ml SF EV\n" +
                "\n" +
                "URINÁRIO -INTERNAÇÃO PRÉVIA COM ALTA HÁ MENOS DE 15 DIAS OU LITÍASE RENAL COM ITU DE REPETIÇÃO\n" +
                "Opção 1: Piperacilina + Tazobactam 4,5g: 01 FA em 100ml de SF 0,9% EV\n" +
                "\n" +
                "\n" +
                "CUTÂNEO - ERISIPELA E CELULITE NÃO BOLHOSA\n" +
                "Opção 1: Ceftriaxona 1g: 02 FA em 100ml de SF 0,9% EV\n" +
                "Opção 2: Oxacilina 500mg: 04 FA em 100ml de SF 0,9% EV de 4/4h\n" +
                "\n" +
                "CUTÂNEO - ERISIPELA BOLHOSA OU PÉ DIABÉTICO (COM OU SEM NECROSE)\n" +
                "Opção 1: Ceftriaxona 1g: 02 FA em 100ml d SF 0,9% EV + Clindamicina 600mg: 01 FA em 100ML SF 0,9% EV\n" +
                "\n" +
                "\n" +
                "SNC - MENINGOCOCO, PNEUMOCOCO OU HAEMOPHYLUS INFLUENZA\n" +
                "Opção 1: Ceftriaxona 1g: 02 FA em 100ml de SF 0,9% EV\n" +
                "\n" +
                "INDETERMINADO\n" +
                "Opção 1: Ceftriaxona 1g: 02 FA em 100ml de SF 0,9% EV + Oxacilina 500mg: 04 FA em 100ml SF 0,9% EV de 4/4h";
    }

    public void setOrientacoes(String orientacoes) {
        this.orientacoes = orientacoes;
    }

    public AtendimentoSepse getAtendimentoSepse() {
        return atendimentoSepse;
    }

    public void setAtendimentoSepse(AtendimentoSepse atendimentoSepse) {
        this.atendimentoSepse = atendimentoSepse;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }

    public Long getNewsReavaliado() {
        return newsReavaliado;
    }

    public void setNewsReavaliado(Long newsReavaliado) {
        this.newsReavaliado = newsReavaliado;
    }

    public Long getDrogaVasoativaIndicada() {
        return drogaVasoativaIndicada;
    }

    public void setDrogaVasoativaIndicada(Long drogaVasoativaIndicada) {
        this.drogaVasoativaIndicada = drogaVasoativaIndicada;
    }

    public Long getHipoteseSepse() {
        return hipoteseSepse;
    }

    public void setHipoteseSepse(Long hipoteseSepse) {
        this.hipoteseSepse = hipoteseSepse;
    }

    public Long getNewsFinal() {
        return newsFinal;
    }

    public void setNewsFinal(Long newsFinal) {
        this.newsFinal = newsFinal;
    }

    public Long getDesfecho() {
        return desfecho;
    }

    public void setDesfecho(Long desfecho) {
        this.desfecho = desfecho;
    }

    public RequisicaoExamesDTO getRequisicaoExamesDTO() {
        if (this.requisicaoExamesDTO == null) {
            this.requisicaoExamesDTO = new RequisicaoExamesDTO();
        }
        return requisicaoExamesDTO;
    }

    public void setRequisicaoExamesDTO(RequisicaoExamesDTO requisicaoExamesDTO) {
        this.requisicaoExamesDTO = requisicaoExamesDTO;
    }

    public PrescricaoInternaDTO getPrescricaoInternaDTO() {
        if (this.prescricaoInternaDTO == null) {
            this.prescricaoInternaDTO = new PrescricaoInternaDTO();
        }
        return prescricaoInternaDTO;
    }

    public void setPrescricaoInternaDTO(PrescricaoInternaDTO prescricaoInternaDTO) {
        this.prescricaoInternaDTO = prescricaoInternaDTO;
    }

    public Profissional getProfissionalConfimacaoSepse() {
        return profissionalConfimacaoSepse;
    }

    public void setProfissionalConfimacaoSepse(Profissional profissionalConfimacaoSepse) {
        this.profissionalConfimacaoSepse = profissionalConfimacaoSepse;
    }

    public Profissional getProfissionalReavaliacaoSepse() {
        return profissionalReavaliacaoSepse;
    }

    public void setProfissionalReavaliacaoSepse(Profissional profissionalReavaliacaoSepse) {
        this.profissionalReavaliacaoSepse = profissionalReavaliacaoSepse;
    }

    public Profissional getProfissionalAvaliacaoFinalSepse() {
        return profissionalAvaliacaoFinalSepse;
    }

    public void setProfissionalAvaliacaoFinalSepse(Profissional profissionalAvaliacaoFinalSepse) {
        this.profissionalAvaliacaoFinalSepse = profissionalAvaliacaoFinalSepse;
    }

    public Date getHoraReavaliacao() {
        return horaReavaliacao;
    }

    public void setHoraReavaliacao(Date horaReavaliacao) {
        this.horaReavaliacao = horaReavaliacao;
    }

    public Date getHoraAvaliacaoFinal() {
        return horaAvaliacaoFinal;
    }

    public void setHoraAvaliacaoFinal(Date horaAvaliacaoFinal) {
        this.horaAvaliacaoFinal = horaAvaliacaoFinal;
    }

    public String getTempoDecorrente() {
        return tempoDecorrente;
    }

    public void setTempoDecorrente(String tempoDecorrente) {
        this.tempoDecorrente = tempoDecorrente;
    }

    public AtendimentoSepseItem getAtendimentoSepseItem() {
        return atendimentoSepseItem;
    }

    public void setAtendimentoSepseItem(AtendimentoSepseItem atendimentoSepseItem) {
        this.atendimentoSepseItem = atendimentoSepseItem;
    }

    public List<ReceituarioItem> getReceituarioItemList() {
        return receituarioItemList;
    }

    public void setReceituarioItemList(List<ReceituarioItem> receituarioItemList) {
        this.receituarioItemList = receituarioItemList;
    }

    public List<ExameRequisicao> getExameRequisicaoList() {
        return exameRequisicaoList;
    }

    public void setExameRequisicaoList(List<ExameRequisicao> exameRequisicaoList) {
        this.exameRequisicaoList = exameRequisicaoList;
    }

    public Long getConfirmacaoSepse() {
        return confirmacaoSepse;
    }

    public void setConfirmacaoSepse(Long confirmacaoSepse) {
        this.confirmacaoSepse = confirmacaoSepse;
    }
}
