package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.ConsorcioPrestadorAgendaHorario;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class SalvarGuiaProcedimentoDTO implements Serializable{

    private ConsorcioGuiaProcedimento consorcioGuiaProcedimento;
    private List<ConsorcioGuiaProcedimentoItemDTO> itens;
    private String codigoSisregGuia;
    private String chaveSisregGuia;
    private Date dataSolicitacaoSisregGuia;
    private boolean auditoriaGuias;
    private ConsorcioPrestadorAgendaHorario consorcioPrestadorAgendaHorario;
    public ConsorcioGuiaProcedimento getConsorcioGuiaProcedimento() {
        return consorcioGuiaProcedimento;
    }

    public void setConsorcioGuiaProcedimento(ConsorcioGuiaProcedimento consorcioGuiaProcedimento) {
        this.consorcioGuiaProcedimento = consorcioGuiaProcedimento;
    }

    public List<ConsorcioGuiaProcedimentoItemDTO> getItens() {
        return itens;
    }

    public void setItens(List<ConsorcioGuiaProcedimentoItemDTO> itens) {
        this.itens = itens;
    }

    public String getCodigoSisregGuia() {
        return codigoSisregGuia;
    }

    public void setCodigoSisregGuia(String codigoSisregGuia) {
        this.codigoSisregGuia = codigoSisregGuia;
    }

    public String getChaveSisregGuia() {
        return chaveSisregGuia;
    }

    public void setChaveSisregGuia(String chaveSisregGuia) {
        this.chaveSisregGuia = chaveSisregGuia;
    }

    public Date getDataSolicitacaoSisregGuia() {
        return dataSolicitacaoSisregGuia;
    }

    public void setDataSolicitacaoSisregGuia(Date dataSolicitacaoSisregGuia) {
        this.dataSolicitacaoSisregGuia = dataSolicitacaoSisregGuia;
    }

    public boolean isAuditoriaGuias() {
        return auditoriaGuias;
    }

    public void setAuditoriaGuias(boolean auditoriaGuias) {
        this.auditoriaGuias = auditoriaGuias;
    }

    public ConsorcioPrestadorAgendaHorario getConsorcioPrestadorAgendaHorario() {
        return consorcioPrestadorAgendaHorario;
    }

    public void setConsorcioPrestadorAgendaHorario(ConsorcioPrestadorAgendaHorario consorcioPrestadorAgendaHorario) {
        this.consorcioPrestadorAgendaHorario = consorcioPrestadorAgendaHorario;
    }
}
