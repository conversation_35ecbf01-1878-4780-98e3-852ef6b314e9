package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Cid;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaCidDTOParam implements Serializable {

    private Long codigo;
    private String descricao;
    private String keyword;
    private String propSort;
    private boolean ascending;
    private Boolean validaCategoria;
    private String sexo;
    private boolean isOcultarCidsInativos;
    private List<String> cidsMedicamento;

    private boolean isApenasCidNotificavel;

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public Boolean getValidaCategoria() {
        return validaCategoria;
    }

    public void setValidaCategoria(Boolean categoria) {
        this.validaCategoria = categoria;
    }

    public String getSexo() {
        return sexo;
    }

    public void setSexo(String sexo) {
        this.sexo = sexo;
    }

    public boolean getIsOcultarCidsInativos() {
        return isOcultarCidsInativos;
    }

    public void setIsOcultarCidsInativos(boolean isOcultarCidsInativos) {
        this.isOcultarCidsInativos = isOcultarCidsInativos;
    }

    public boolean isApenasCidNotificavel() {
        return isApenasCidNotificavel;
    }

    public void setApenasCidNotificavel(boolean apenasCidNotificavel) {
        isApenasCidNotificavel = apenasCidNotificavel;
    }

    public List<String> getCidsMedicamento() {
        return cidsMedicamento;
    }

    public void setCidsMedicamento(List<String> cidsMedicamento) {
        this.cidsMedicamento = cidsMedicamento;
    }
}
