package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ActionAtendimentoWebDTO implements Serializable {
    
    private AtendimentoWebDTO atendimentoWebDTO;
    private List<Long> permissionsList;
    private boolean usuarioNaoIdentificavel;
    private Profissional profissional;
    private boolean reclassificacao;

    private boolean isTelaAtendimento = false;

    private boolean isTelaAtendimentoAtencaoBasica = false;

    public AtendimentoWebDTO getAtendimentoWebDTO() {
        return atendimentoWebDTO;
    }

    public void setAtendimentoWebDTO(AtendimentoWebDTO atendimentoWebDTO) {
        this.atendimentoWebDTO = atendimentoWebDTO;
    }

    public List<Long> getPermissionsList() {
        return permissionsList;
    }

    public void setPermissionsList(List<Long> permissionsList) {
        this.permissionsList = permissionsList;
    }

    public boolean isUsuarioNaoIdentificavel() {
        return usuarioNaoIdentificavel;
    }

    public void setUsuarioNaoIdentificavel(boolean usuarioNaoIdentificavel) {
        this.usuarioNaoIdentificavel = usuarioNaoIdentificavel;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public boolean isReclassificacao() {
        return reclassificacao;
    }

    public void setReclassificacao(boolean reclassificacao) {
        this.reclassificacao = reclassificacao;
    }

    public boolean isTelaAtendimento() {
        return isTelaAtendimento;
    }

    public void setTelaAtendimento(boolean telaAtendimento) {
        isTelaAtendimento = telaAtendimento;
    }

    public boolean isTelaAtendimentoAtencaoBasica() {
        return isTelaAtendimentoAtencaoBasica;
    }

    public void setTelaAtendimentoAtencaoBasica(boolean telaAtendimentoAtencaoBasica) {
        isTelaAtendimentoAtencaoBasica = telaAtendimentoAtencaoBasica;
    }
}
