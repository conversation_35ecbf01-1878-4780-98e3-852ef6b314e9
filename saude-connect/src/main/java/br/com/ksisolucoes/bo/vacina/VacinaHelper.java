package br.com.ksisolucoes.bo.vacina;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.bo.command.LoadManager;
import br.com.ksisolucoes.dao.HQLProperties;
import br.com.ksisolucoes.system.sessao.SessaoAplicacaoImp;
import br.com.ksisolucoes.vo.controle.Usuario;
import br.com.ksisolucoes.vo.vacina.GrupoAtendimentoVacinacaoEsus;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.ViaAdministracao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;



/**
 * <AUTHOR>
 */
public class VacinaHelper implements Serializable {



    private static VacinaHelper instance = new VacinaHelper();

    public VacinaHelper() {
    }

    public static VacinaHelper getInstance() {
        return instance;
    }

    public static boolean permissaoCrudVacinas() {
        Usuario usuarioSessao = getUsuarioSessao();
        if (usuarioSessao == null) return false;
        return usuarioSessao.isNivelMaster();
    }

    public static Usuario getUsuarioSessao() {
        if (SessaoAplicacaoImp.getInstance() != null) {
            return SessaoAplicacaoImp.getInstance().getUsuario();
        }
        return null;
    }

    public static boolean isVacinaCovid(TipoVacina tipoVacina) {
        return (TipoVacina.TIPO_VACINA_COVID_19.contains(tipoVacina.getTipoEsus()));
    }

    public static GrupoAtendimentoVacinacaoEsus filterGrupoAtendimentoVacinacaoEsusByCodigo(List<GrupoAtendimentoVacinacaoEsus> grupoAtendimentoVacinacaoEsusList, Long codigo) {
        return grupoAtendimentoVacinacaoEsusList.stream()
                .filter(grupo -> grupo.getCodigo().equals(codigo))
                .findFirst()
                .orElse(null);
    }

}