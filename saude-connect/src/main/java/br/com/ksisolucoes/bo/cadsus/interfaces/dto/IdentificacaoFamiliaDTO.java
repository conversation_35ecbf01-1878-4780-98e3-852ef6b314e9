package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class IdentificacaoFamiliaDTO implements Serializable{
    
    private String area;
    private String segmento;
    private Long microArea;
    private Long numeroFamilia;
    private Long codigoDomicilio;
    private String agenteComunitario;
    private String unidade;

    public String getArea() {
        return area;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getSegmento() {
        return segmento;
    }

    public void setSegmento(String segmento) {
        this.segmento = segmento;
    }

    public Long getMicroArea() {
        return microArea;
    }

    public void setMicroArea(Long microArea) {
        this.microArea = microArea;
    }

    public Long getNumeroFamilia() {
        return numeroFamilia;
    }

    public void setNumeroFamilia(Long numeroFamilia) {
        this.numeroFamilia = numeroFamilia;
    }

    public Long getCodigoDomicilio() {
        return codigoDomicilio;
    }

    public void setCodigoDomicilio(Long codigoDomicilio) {
        this.codigoDomicilio = codigoDomicilio;
    }

    public String getAgenteComunitario() {
        return agenteComunitario;
    }

    public void setAgenteComunitario(String agenteComunitario) {
        this.agenteComunitario = agenteComunitario;
    }

    public String getUnidade() {
        return unidade;
    }

    public void setUnidade(String unidade) {
        this.unidade = unidade;
    }    
    
}
