package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto.consultaenfermagem;

import br.com.ksisolucoes.bo.prontuario.receituario.interfaces.dto.ReceituarioItemDTO;
import br.com.ksisolucoes.vo.prontuario.basico.Cuidados;
import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.enfermagem.AtendimentoEnfermagemIntervencaoPrescricao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class AtendimentoEnfermagemPrescricaoDTOParam implements Serializable {
    private AtendimentoEnfermagemPrescricaoDTO enfermagemPrescricaoDTO;

    public AtendimentoEnfermagemPrescricaoDTO getEnfermagemPrescricaoDTO() {
        return enfermagemPrescricaoDTO;
    }

    public void setEnfermagemPrescricaoDTO(AtendimentoEnfermagemPrescricaoDTO enfermagemPrescricaoDTO) {
        this.enfermagemPrescricaoDTO = enfermagemPrescricaoDTO;
    }
}