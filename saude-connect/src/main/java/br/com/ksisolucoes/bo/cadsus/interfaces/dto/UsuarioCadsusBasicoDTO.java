package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDocumento;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusEndereco;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class UsuarioCadsusBasicoDTO implements Serializable {

    private UsuarioCadsus usuarioCadsus;
    private List<UsuarioCadsusDocumento> documentosList;
    private UsuarioCadsusCns usuarioCadsusCns;
    private List<Long> tipoDocumentosList;
    private boolean validarDocumentosObrigatorios = true;
    private UsuarioCadsusEndereco usuarioCadsusEndereco;


    public boolean isValidarDocumentosObrigatorios() {
        return validarDocumentosObrigatorios;
    }

    public void setValidarDocumentosObrigatorios(boolean validarDocumentosObrigatorios) {
        this.validarDocumentosObrigatorios = validarDocumentosObrigatorios;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public List<UsuarioCadsusDocumento> getDocumentosList() {
        return documentosList;
    }

    public void setDocumentosList(List<UsuarioCadsusDocumento> documentosList) {
        this.documentosList = documentosList;
    }

    public List<Long> getTipoDocumentosList() {
        return tipoDocumentosList;
    }

    public void setTipoDocumentosList(List<Long> tipoDocumentosList) {
        this.tipoDocumentosList = tipoDocumentosList;
    }

    public UsuarioCadsusEndereco getUsuarioCadsusEndereco() {
        return usuarioCadsusEndereco;
    }

    public void setUsuarioCadsusEndereco(UsuarioCadsusEndereco usuarioCadsusEndereco) {
        this.usuarioCadsusEndereco = usuarioCadsusEndereco;
    }
}
