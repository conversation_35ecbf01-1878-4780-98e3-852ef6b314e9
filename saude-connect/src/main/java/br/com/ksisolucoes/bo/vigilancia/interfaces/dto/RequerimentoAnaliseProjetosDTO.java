package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.celk.util.CollectionUtils;
import br.com.ksisolucoes.vo.vigilancia.*;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoAnaliseProjeto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RequerimentoAnaliseProjetosDTO implements Serializable {
    
    private RequerimentoAnaliseProjeto requerimentoAnaliseProjeto;
    private TipoSolicitacao tipoSolicitacao;
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList = new ArrayList();
    private List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList = new ArrayList<>();
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList;
    private List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList;
    private List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir;
    private List<EloRequerimentoVigilanciaResponsavelTecnico> eloRequerimentoVigilanciaResponsavelTecnicoList;
    private List<EloRequerimentoVigilanciaResponsavelTecnico> eloRequerimentoVigilanciaResponsavelTecnicoExcluirList;
    private List<RequerimentoVigilanciaInscricaoImob> requerimentoVigilanciaInscricaoImobList;
    private List<RequerimentoVigilanciaInscricaoImob> requerimentoVigilanciaInscricaoImobExcluirList;

    public RequerimentoAnaliseProjeto getRequerimentoAnaliseProjeto() {
        return requerimentoAnaliseProjeto;
    }

    public void setRequerimentoAnaliseProjeto(RequerimentoAnaliseProjeto requerimentoAnaliseProjeto) {
        this.requerimentoAnaliseProjeto = requerimentoAnaliseProjeto;
    }

    public TipoSolicitacao getTipoSolicitacao() {
        return tipoSolicitacao;
    }

    public void setTipoSolicitacao(TipoSolicitacao tipoSolicitacao) {
        this.tipoSolicitacao = tipoSolicitacao;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoDTOList() {
        return requerimentoVigilanciaAnexoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoDTOList) {
        this.requerimentoVigilanciaAnexoDTOList = requerimentoVigilanciaAnexoDTOList;
    }

    public List<RequerimentoVigilanciaAnexoDTO> getRequerimentoVigilanciaAnexoExcluidoDTOList() {
        return requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public void setRequerimentoVigilanciaAnexoExcluidoDTOList(List<RequerimentoVigilanciaAnexoDTO> requerimentoVigilanciaAnexoExcluidoDTOList) {
        this.requerimentoVigilanciaAnexoExcluidoDTOList = requerimentoVigilanciaAnexoExcluidoDTOList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaList() {
        return eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaList = eloRequerimentoVigilanciaSetorVigilanciaList;
    }

    public List<EloRequerimentoVigilanciaSetorVigilancia> getEloRequerimentoVigilanciaSetorVigilanciaExcluirList() {
        return eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public void setEloRequerimentoVigilanciaSetorVigilanciaExcluirList(List<EloRequerimentoVigilanciaSetorVigilancia> eloRequerimentoVigilanciaSetorVigilanciaExcluirList) {
        this.eloRequerimentoVigilanciaSetorVigilanciaExcluirList = eloRequerimentoVigilanciaSetorVigilanciaExcluirList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalList() {
        return requerimentoVigilanciaFiscalList;
    }

    public void setRequerimentoVigilanciaFiscalList(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalList) {
        this.requerimentoVigilanciaFiscalList = requerimentoVigilanciaFiscalList;
    }

    public List<RequerimentoVigilanciaFiscal> getRequerimentoVigilanciaFiscalListExcluir() {
        return requerimentoVigilanciaFiscalListExcluir;
    }

    public void setRequerimentoVigilanciaFiscalListExcluir(List<RequerimentoVigilanciaFiscal> requerimentoVigilanciaFiscalListExcluir) {
        this.requerimentoVigilanciaFiscalListExcluir = requerimentoVigilanciaFiscalListExcluir;
    }

    public List<EloRequerimentoVigilanciaResponsavelTecnico> getEloRequerimentoVigilanciaResponsavelTecnicoList() {
        if (CollectionUtils.isEmpty(eloRequerimentoVigilanciaResponsavelTecnicoList)) {
            eloRequerimentoVigilanciaResponsavelTecnicoList = new ArrayList<>();
        }
        return eloRequerimentoVigilanciaResponsavelTecnicoList;
    }

    public void setEloRequerimentoVigilanciaResponsavelTecnicoList(List<EloRequerimentoVigilanciaResponsavelTecnico> eloRequerimentoVigilanciaResponsavelTecnicoList) {
        this.eloRequerimentoVigilanciaResponsavelTecnicoList = eloRequerimentoVigilanciaResponsavelTecnicoList;
    }

    public List<EloRequerimentoVigilanciaResponsavelTecnico> getEloRequerimentoVigilanciaResponsavelTecnicoExcluirList() {
        if (CollectionUtils.isEmpty(eloRequerimentoVigilanciaResponsavelTecnicoExcluirList)) {
            eloRequerimentoVigilanciaResponsavelTecnicoExcluirList = new ArrayList<>();
        }
        return eloRequerimentoVigilanciaResponsavelTecnicoExcluirList;
    }

    public void setEloRequerimentoVigilanciaResponsavelTecnicoExcluirList(List<EloRequerimentoVigilanciaResponsavelTecnico> eloRequerimentoVigilanciaResponsavelTecnicoExcluirList) {
        this.eloRequerimentoVigilanciaResponsavelTecnicoExcluirList = eloRequerimentoVigilanciaResponsavelTecnicoExcluirList;
    }

    public List<RequerimentoVigilanciaInscricaoImob> getRequerimentoVigilanciaInscricaoImobList() {
        if (CollectionUtils.isEmpty(requerimentoVigilanciaInscricaoImobList)) {
            requerimentoVigilanciaInscricaoImobList = new ArrayList<>();
        }
        return requerimentoVigilanciaInscricaoImobList;
    }

    public void setRequerimentoVigilanciaInscricaoImobList(List<RequerimentoVigilanciaInscricaoImob> requerimentoVigilanciaInscricaoImobList) {
        this.requerimentoVigilanciaInscricaoImobList = requerimentoVigilanciaInscricaoImobList;
    }

    public List<RequerimentoVigilanciaInscricaoImob> getRequerimentoVigilanciaInscricaoImobExcluirList() {
        if (CollectionUtils.isEmpty(requerimentoVigilanciaInscricaoImobExcluirList)) {
            requerimentoVigilanciaInscricaoImobExcluirList = new ArrayList<>();
        }
        return requerimentoVigilanciaInscricaoImobExcluirList;
    }

    public void setRequerimentoVigilanciaInscricaoImobExcluirList(List<RequerimentoVigilanciaInscricaoImob> requerimentoVigilanciaInscricaoImobExcluirList) {
        this.requerimentoVigilanciaInscricaoImobExcluirList = requerimentoVigilanciaInscricaoImobExcluirList;
    }
}
