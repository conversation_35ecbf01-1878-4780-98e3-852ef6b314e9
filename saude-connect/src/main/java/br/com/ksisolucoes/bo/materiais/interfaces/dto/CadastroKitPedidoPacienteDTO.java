package br.com.ksisolucoes.bo.materiais.interfaces.dto;

import br.com.ksisolucoes.vo.materiais.KitPedidoPaciente;
import br.com.ksisolucoes.vo.materiais.KitPedidoPacienteItem;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class CadastroKitPedidoPacienteDTO implements Serializable {

    private KitPedidoPaciente kitPedidoPaciente;
    private List<KitPedidoPacienteItem> kitPedidoPacienteItems = new ArrayList<KitPedidoPacienteItem>();

    public KitPedidoPaciente getKitPedidoPaciente() {
        return kitPedidoPaciente;
    }

    public void setKitPedidoPaciente(KitPedidoPaciente kitPedidoPaciente) {
        this.kitPedidoPaciente = kitPedidoPaciente;
    }

    public List<KitPedidoPacienteItem> getKitPedidoPacienteItems() {
        return kitPedidoPacienteItems;
    }

    public void setKitPedidoPacienteItems(List<KitPedidoPacienteItem> kitPedidoPacienteItems) {
        this.kitPedidoPacienteItems = kitPedidoPacienteItems;
    }
}
