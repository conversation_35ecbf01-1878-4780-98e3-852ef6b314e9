package br.com.ksisolucoes.bo.basico.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.EloTipoUnidade;
import br.com.ksisolucoes.vo.basico.Empresa;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class QueryConsultaDominioEstabelecimentoDTOParam implements Serializable{

    private String keyword;
    private String nome;
    private String cnes;
    private String sigla;
    private Long codigo;
    private String cnpjCpf;
    private List<Long> tiposEstabelecimento;
    private List<Long> codigoEmpresaList;
    private String propSort;
    private boolean ascending;
    private boolean validaUsuarioEmpresa;
    private List<EloTipoUnidade.TipoUnidade> tiposUnidade;
    private List<String> codigosProdutos;
    private boolean validaAcessoRestrito = true;
    private boolean validaUnidadesAlmoxarifado;
    private Empresa empresaPedidoAlmoxarifado;
    private Cidade cidade;

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public String getNome() {
        return nome;
    }

    public void setNome(String nome) {
        this.nome = nome;
    }

    public String getCnes() {
        return cnes;
    }

    public void setCnes(String cnes) {
        this.cnes = cnes;
    }

    public String getSigla() {
        return sigla;
    }

    public void setSigla(String sigla) {
        this.sigla = sigla;
    }

    public String getCnpjCpf() {
        return cnpjCpf;
    }

    public void setCnpjCpf(String cnpjCpf) {
        this.cnpjCpf = cnpjCpf;
    }

    public List<Long> getTiposEstabelecimento() {
        return tiposEstabelecimento;
    }

    public void setTiposEstabelecimento(List<Long> tiposEstabelecimento) {
        this.tiposEstabelecimento = tiposEstabelecimento;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public boolean isValidaUsuarioEmpresa() {
        return validaUsuarioEmpresa;
    }

    public void setValidaUsuarioEmpresa(boolean validaUsuarioEmpresa) {
        this.validaUsuarioEmpresa = validaUsuarioEmpresa;
    }

    public List<EloTipoUnidade.TipoUnidade> getTiposUnidade() {
        return tiposUnidade;
    }

    public void setTiposUnidade(List<EloTipoUnidade.TipoUnidade> tiposUnidade) {
        this.tiposUnidade = tiposUnidade;
    }

    public List<String> getCodigosProdutos() {
        return codigosProdutos;
    }

    public void setCodigosProdutos(List<String> codigosProdutos) {
        this.codigosProdutos = codigosProdutos;
    }

    public boolean isValidaAcessoRestrito() {
        return validaAcessoRestrito;
    }

    public void setValidaAcessoRestrito(boolean validaAcessoRestrito) {
        this.validaAcessoRestrito = validaAcessoRestrito;
    }

    public List<Long> getCodigoEmpresaList() {
        return codigoEmpresaList;
    }

    public void setCodigoEmpresaList(List<Long> codigoEmpresaList) {
        this.codigoEmpresaList = codigoEmpresaList;
    }

    public boolean isValidaUnidadesAlmoxarifado() {
        return validaUnidadesAlmoxarifado;
    }

    public void setValidaUnidadesAlmoxarifado(boolean validaUnidadesAlmoxarifado) {
        this.validaUnidadesAlmoxarifado = validaUnidadesAlmoxarifado;
    }

    public Empresa getEmpresaPedidoAlmoxarifado() {
        return empresaPedidoAlmoxarifado;
    }

    public void setEmpresaPedidoAlmoxarifado(Empresa empresaPedidoAlmoxarifado) {
        this.empresaPedidoAlmoxarifado = empresaPedidoAlmoxarifado;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }
}
