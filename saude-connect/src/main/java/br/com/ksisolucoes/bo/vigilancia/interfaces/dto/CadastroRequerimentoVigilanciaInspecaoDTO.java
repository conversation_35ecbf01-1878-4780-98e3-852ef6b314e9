package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.vigilancia.RequerimentoVigilancia;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by sulivan on 30/05/17.
 */
public class CadastroRequerimentoVigilanciaInspecaoDTO implements Serializable {

    private List<RequerimentoVigilancia> requerimentoVigilanciaList;
    private List<Profissional> profissionalList;
    private Date dataInspecao;
    private Long tipoInspecao;

    public List<RequerimentoVigilancia> getRequerimentoVigilanciaList() {
        return requerimentoVigilanciaList;
    }

    public void setRequerimentoVigilanciaList(List<RequerimentoVigilancia> requerimentoVigilanciaList) {
        this.requerimentoVigilanciaList = requerimentoVigilanciaList;
    }

    public List<Profissional> getProfissionalList() {
        return profissionalList;
    }

    public void setProfissionalList(List<Profissional> profissionalList) {
        this.profissionalList = profissionalList;
    }

    public Date getDataInspecao() {
        return dataInspecao;
    }

    public void setDataInspecao(Date dataInspecao) {
        this.dataInspecao = dataInspecao;
    }

    public Long getTipoInspecao() {
        return tipoInspecao;
    }

    public void setTipoInspecao(Long tipoInspecao) {
        this.tipoInspecao = tipoInspecao;
    }
}
