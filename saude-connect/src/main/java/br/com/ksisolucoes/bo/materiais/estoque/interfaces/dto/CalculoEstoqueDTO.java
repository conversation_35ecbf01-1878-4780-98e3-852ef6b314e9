package br.com.ksisolucoes.bo.materiais.estoque.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;

import java.io.Serializable;

public class CalculoEstoqueDTO implements Serializable {

    private Empresa empresa;
    private SubGrupo subGrupo;
    private GrupoProduto grupoProduto;
    private Produto produto;
    private Long tempoReposicao;
    private Long estoqueMaximo;
    private Long flagPossuiEstoqueZerado;
    private String curva;
    private String criticidade;
    private Integer periodo;

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public SubGrupo getSubGrupo() {
        return subGrupo;
    }

    public void setSubGrupo(SubGrupo subGrupo) {
        this.subGrupo = subGrupo;
    }

    public GrupoProduto getGrupoProduto() {
        return grupoProduto;
    }

    public void setGrupoProduto(GrupoProduto grupoProduto) {
        this.grupoProduto = grupoProduto;
    }

    public Long getTempoReposicao() {
        return tempoReposicao;
    }

    public void setTempoReposicao(Long tempoReposicao) {
        this.tempoReposicao = tempoReposicao;
    }

    public Long getEstoqueMaximo() {
        return estoqueMaximo;
    }

    public void setEstoqueMaximo(Long estoqueMaximo) {
        this.estoqueMaximo = estoqueMaximo;
    }

    public Long getFlagPossuiEstoqueZerado() {
        return flagPossuiEstoqueZerado;
    }

    public void setFlagPossuiEstoqueZerado(Long flagPossuiEstoqueZerado) {
        this.flagPossuiEstoqueZerado = flagPossuiEstoqueZerado;
    }

    public String getCurva() {
        return curva;
    }

    public void setCurva(String curva) {
        this.curva = curva;
    }

    public String getCriticidade() {
        return criticidade;
    }

    public void setCriticidade(String criticidade) {
        this.criticidade = criticidade;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Integer getPeriodo() {
        return periodo;
    }

    public void setPeriodo(Integer periodo) {
        this.periodo = periodo;
    }

    @Override
    public String toString() {
        return "CalculoEstoqueDTO{" +
                "empresa=" + empresa +
                ", subGrupo=" + subGrupo +
                ", grupoProduto=" + grupoProduto +
                ", produto=" + produto +
                ", tempoReposicao=" + tempoReposicao +
                ", estoqueMaximo=" + estoqueMaximo +
                ", flagPossuiEstoqueZerado=" + flagPossuiEstoqueZerado +
                ", curva='" + curva + '\'' +
                ", criticidade='" + criticidade + '\'' +
                '}';
    }
}
