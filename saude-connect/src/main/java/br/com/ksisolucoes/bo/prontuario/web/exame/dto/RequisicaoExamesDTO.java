/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.bo.prontuario.web.exame.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RequisicaoExamesDTO implements Serializable{
    
    private List<Exame> exames = new ArrayList<Exame>();
    private List<ExameRequisicao> examesRequisicao = new ArrayList<ExameRequisicao>();

    public List<Exame> getExames() {
        return exames;
    }

    public void setExames(List<Exame> exames) {
        this.exames = exames;
    }

    public List<ExameRequisicao> getExamesRequisicao() {
        return examesRequisicao;
    }

    public void setExamesRequisicao(List<ExameRequisicao> examesRequisicao) {
        this.examesRequisicao = examesRequisicao;
    }
    
}
