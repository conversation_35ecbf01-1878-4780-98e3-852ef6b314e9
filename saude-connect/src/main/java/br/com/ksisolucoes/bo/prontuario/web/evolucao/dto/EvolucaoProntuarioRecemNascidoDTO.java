package br.com.ksisolucoes.bo.prontuario.web.evolucao.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.EvolucaoProntuario;
import br.com.ksisolucoes.vo.prontuario.basico.TipoDocumentoAtendimento;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class EvolucaoProntuarioRecemNascidoDTO implements Serializable{
    
    private UsuarioCadsus usuarioCadsus;
    private EvolucaoProntuario evolucaoProntuario;
    private TipoDocumentoAtendimento tipoDocumentoAtendimento;
    private List<EvolucaoRecemNascidoDTO> evolucaoRecemNascidoDTOs = new ArrayList<EvolucaoRecemNascidoDTO>();

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public EvolucaoProntuario getEvolucaoProntuario() {
        return evolucaoProntuario;
    }

    public void setEvolucaoProntuario(EvolucaoProntuario evolucaoProntuario) {
        this.evolucaoProntuario = evolucaoProntuario;
    }

    public TipoDocumentoAtendimento getTipoDocumentoAtendimento() {
        return tipoDocumentoAtendimento;
    }

    public void setTipoDocumentoAtendimento(TipoDocumentoAtendimento tipoDocumentoAtendimento) {
        this.tipoDocumentoAtendimento = tipoDocumentoAtendimento;
    }

    public List<EvolucaoRecemNascidoDTO> getEvolucaoRecemNascidoDTOs() {
        return evolucaoRecemNascidoDTOs;
    }

    public void setEvolucaoRecemNascidoDTOs(List<EvolucaoRecemNascidoDTO> evolucaoRecemNascidoDTOs) {
        this.evolucaoRecemNascidoDTOs = evolucaoRecemNascidoDTOs;
    }
}
