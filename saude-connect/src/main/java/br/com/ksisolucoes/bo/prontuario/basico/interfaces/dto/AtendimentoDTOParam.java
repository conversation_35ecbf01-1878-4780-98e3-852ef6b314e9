package br.com.ksisolucoes.bo.prontuario.basico.interfaces.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Convenio;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class AtendimentoDTOParam implements Serializable {

    private Empresa empresa;
    private Profissional profissional;
    private Profissional profissionalResponsavel;
    private TipoAtendimento tipoAtendimento;
    private NaturezaProcura naturezaProcura;
    private List<Long> situacoes;
    private List<Long> codigosTipoAtendimento;
    private OperadorValor<List<Long>> tiposAtendimento;
    private boolean pacienteSemCadastro;
    private UsuarioCadsus usuarioCadsus;
    private String nomePaciente;
    private boolean validarEmpresa = true;
    private Convenio convenio;
    private boolean somentePacientesInternados;

    public Profissional getProfissionalResponsavel() {
        return profissionalResponsavel;
    }

    public void setProfissionalResponsavel(Profissional profissionalResponsavel) {
        this.profissionalResponsavel = profissionalResponsavel;
    }

    public boolean isValidarEmpresa() {
        return validarEmpresa;
    }

    public void setValidarEmpresa(boolean validarEmpresa) {
        this.validarEmpresa = validarEmpresa;
    }

    private DTOParamConfigureDefault configureParam;

    public AtendimentoDTOParam() {
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }
    
    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public boolean isPacienteSemCadastro() {
        return pacienteSemCadastro;
    }

    public void setPacienteSemCadastro(boolean pacienteSemCadastro) {
        this.pacienteSemCadastro = pacienteSemCadastro;
    }

    public NaturezaProcura getNaturezaProcura() {
        return naturezaProcura;
    }

    public void setNaturezaProcura(NaturezaProcura naturezaProcura) {
        this.naturezaProcura = naturezaProcura;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public List<Long> getSituacoes() {
        return situacoes;
    }

    public void setSituacoes(List<Long> situacoes) {
        this.situacoes = situacoes;
    }

    public TipoAtendimento getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(TipoAtendimento tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    public List<Long> getCodigosTipoAtendimento() {
        return codigosTipoAtendimento;
    }

    public void setCodigosTipoAtendimento(List<Long> codigosTipoAtendimento) {
        this.codigosTipoAtendimento = codigosTipoAtendimento;
    }

    public OperadorValor<List<Long>> getTiposAtendimento() {
        return tiposAtendimento;
    }

    public void setTiposAtendimento(OperadorValor<List<Long>> tiposAtendimento) {
        this.tiposAtendimento = tiposAtendimento;
    }

    public DTOParamConfigureDefault getConfigureParam() {
        if (this.configureParam == null) {
            this.configureParam = new DTOParamConfigureDefault();
        }
        return this.configureParam;
    }

    public void setConfigureParam(DTOParamConfigureDefault configureParam) {
        this.configureParam = configureParam;
    }

    public Convenio getConvenio() {
        return convenio;
    }

    public void setConvenio(Convenio convenio) {
        this.convenio = convenio;
    }

    public boolean isSomentePacientesInternados() {
        return somentePacientesInternados;
    }

    public void setSomentePacientesInternados(boolean somentePacientesInternados) {
        this.somentePacientesInternados = somentePacientesInternados;
    }
}
