package br.com.ksisolucoes.bo.consorcio.interfaces.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;

import java.io.Serializable;
import java.util.Date;

public class ConsultaBaixaGuiaSisregDTOparam implements Serializable {
    private Empresa empresaConsorcio;
    private Empresa empresaPrestador;
    private UsuarioCadsus usuarioCadsus;
    private Long situacaoSisreg;
    private DatePeriod periodo;
    private String numeroChave;
    private String codigoSisreg;
    private Date dataSisreg;
    private boolean count;
    private String propSort;
    private boolean ascending;
    private Long numeroGuia;

    public Long getNumeroGuia() {
        return numeroGuia;
    }

    public void setNumeroGuia(Long numeroGuia) {
        this.numeroGuia = numeroGuia;
    }

    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

    public boolean isCount() {
        return count;
    }

    public void setCount(boolean count) {
        this.count = count;
    }

    public Empresa getEmpresaConsorcio() {
        return empresaConsorcio;
    }

    public void setEmpresaConsorcio(Empresa empresaConsorcio) {
        this.empresaConsorcio = empresaConsorcio;
    }

    public Empresa getEmpresaPrestador() {
        return empresaPrestador;
    }

    public void setEmpresaPrestador(Empresa empresaPrestador) {
        this.empresaPrestador = empresaPrestador;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Long getSituacaoSisreg() {
        return situacaoSisreg;
    }

    public void setSituacaoSisreg(Long situacaoSisreg) {
        this.situacaoSisreg = situacaoSisreg;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public String getNumeroChave() {
        return numeroChave;
    }

    public void setNumeroChave(String numeroChave) {
        this.numeroChave = numeroChave;
    }

    public String getCodigoSisreg() {
        return codigoSisreg;
    }

    public void setCodigoSisreg(String codigoSisreg) {
        this.codigoSisreg = codigoSisreg;
    }

    public Date getDataSisreg() {
        return dataSisreg;
    }

    public void setDataSisreg(Date dataSisreg) {
        this.dataSisreg = dataSisreg;
    }

}
