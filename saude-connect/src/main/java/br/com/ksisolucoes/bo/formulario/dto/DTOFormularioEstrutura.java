/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.formulario.dto;

import br.com.ksisolucoes.vo.formulario.Formulario;
import br.com.ksisolucoes.vo.formulario.FormularioItem;
import br.com.ksisolucoes.vo.formulario.FormularioItemDado;
import java.io.Serializable;
import java.util.Collection;

/**
 *
 * <AUTHOR>
 */
public class DTOFormularioEstrutura implements Serializable{

    private Formulario formulario;
    private Collection<FormularioItem> formularioItems;

    public Formulario getFormulario() {
        return formulario;
    }

    public void setFormulario(Formulario formulario) {
        this.formulario = formulario;
    }

    public Collection<FormularioItem> getFormularioItems() {
        return formularioItems;
    }

    public void setFormularioItems(Collection<FormularioItem> formularioItems) {
        this.formularioItems = formularioItems;
    }

    public FormularioItem getFormularioItem(String chave){
        if (this.formularioItems == null) {
            return null;
        }
        for (FormularioItem formularioItem : this.formularioItems) {
            if (formularioItem.getChave().equals(chave)) {
                return formularioItem;
            }
        }
        return null;
    }

}
