package br.com.ksisolucoes.bo.vigilancia.interfaces.dto;

import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecer;
import br.com.ksisolucoes.vo.vigilancia.requerimentos.RequerimentoProjetoHidrossanitarioParecerResposta;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RequerimentoProjetoHidroParecerRespostaDTO implements Serializable {

    private RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer;
    private RequerimentoProjetoHidrossanitarioParecerResposta requerimentoProjetoHidrossanitarioParecerResposta;

    public RequerimentoProjetoHidrossanitarioParecer getRequerimentoProjetoHidrossanitarioParecer() {
        return requerimentoProjetoHidrossanitarioParecer;
    }

    public void setRequerimentoProjetoHidrossanitarioParecer(RequerimentoProjetoHidrossanitarioParecer requerimentoProjetoHidrossanitarioParecer) {
        this.requerimentoProjetoHidrossanitarioParecer = requerimentoProjetoHidrossanitarioParecer;
    }

    public RequerimentoProjetoHidrossanitarioParecerResposta getRequerimentoProjetoHidrossanitarioParecerResposta() {
        return requerimentoProjetoHidrossanitarioParecerResposta;
    }

    public void setRequerimentoProjetoHidrossanitarioParecerResposta(RequerimentoProjetoHidrossanitarioParecerResposta requerimentoProjetoHidrossanitarioParecerResposta) {
        this.requerimentoProjetoHidrossanitarioParecerResposta = requerimentoProjetoHidrossanitarioParecerResposta;
    }
}
