package br.com.ksisolucoes.bo.comunicacao.interfaces.dto;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class EnviarEmailDTO implements Serializable{

    private String assunto;
    private String mensagem;
    private String emailDestino;
    private String content; // default MediaType.TEXT_HTML
    private boolean incluirAvisoMensagemAutomatica = true;

    public String getAssunto() {
        return assunto;
    }

    public void setAssunto(String assunto) {
        this.assunto = assunto;
    }

    public String getMensagem() {
        if (incluirAvisoMensagemAutomatica) {
            this.mensagem = mensagem.concat("<br/>").concat("<br/>").concat("<br/>").concat("<br/>").concat("<hr/>").concat("<p> Esta é uma mensagem automática, favor não responder este e-mail</p>");
        }
        return mensagem;
    }

    public void setMensagem(String mensagem) {
        this.mensagem = mensagem;
    }

    public String getEmailDestino() {
        return emailDestino;
    }

    public void setEmailDestino(String emailDestino) {
        this.emailDestino = emailDestino;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
