package br.com.ksisolucoes.bo.vacina.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.vacina.VacinaAplicacao;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ParamConsultaVacinaAplicacaoSemFicha implements Serializable {

    private Date dataInicio;
    private Date dataFim;
    private int limit;
    private Empresa empresa;
    private Long empresaGeraEsusVacina;
    private Long isVacinaAplicacaoHistorico;
    private List<Long> statusVacinasAplicacoes = new ArrayList<>();

    public Date getDataInicio() {
        return dataInicio;
    }

    public void setDataInicio(Date dataInicio) {
        this.dataInicio = dataInicio;
    }

    public Date getDataFim() {
        return dataFim;
    }

    public void setDataFim(Date dataFim) {
        this.dataFim = dataFim;
    }

    public Long isEmpresaGeraEsusVacina() {
        return empresaGeraEsusVacina;
    }

    public void setEmpresaGeraEsusVacina(Long empresaGeraEsusVacina) {
        this.empresaGeraEsusVacina = empresaGeraEsusVacina;
    }

    public Long isVacinaAplicacaoHistorico() {
        return isVacinaAplicacaoHistorico;
    }

    public void setIsVacinaAplicacaoHistorico(Long isVacinaAplicacaoHistorico) {
        this.isVacinaAplicacaoHistorico = isVacinaAplicacaoHistorico;
    }

    public List<Long> getStatusVacinasAplicacoes() {
        return statusVacinasAplicacoes;
    }

    public void addStatusVacinasAplicacoes(VacinaAplicacao.StatusVacinaAplicacao statusVacinaAplicacao) {
        this.statusVacinasAplicacoes.add(statusVacinaAplicacao.value());
    }

    public int getLimit() {
        return limit;
    }

    public void setLimit(int limit) {
        this.limit = limit;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
