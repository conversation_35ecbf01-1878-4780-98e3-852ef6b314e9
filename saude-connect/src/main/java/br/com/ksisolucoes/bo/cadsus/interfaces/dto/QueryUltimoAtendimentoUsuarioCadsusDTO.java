/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.bo.cadsus.interfaces.dto;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class QueryUltimoAtendimentoUsuarioCadsusDTO implements Serializable{

    private Date dataAtendimento;
    private String descricaoEmpresa;
    private String motivoCancelamento;
    private Long status;

    public Date getDataAtendimento() {
        return dataAtendimento;
    }

    public void setDataAtendimento(Date dataAtendimento) {
        this.dataAtendimento = dataAtendimento;
    }

    public String getDescricaoEmpresa() {
        return descricaoEmpresa;
    }

    public void setDescricaoEmpresa(String descricaoEmpresa) {
        this.descricaoEmpresa = descricaoEmpresa;
    }

    public String getMotivoCancelamento() {
        return motivoCancelamento;
    }

    public void setMotivoCancelamento(String motivoCancelamento) {
        this.motivoCancelamento = motivoCancelamento;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public String getDescricao(){
        StringBuilder descricao = new StringBuilder();
        descricao.append(Data.formatar(this.getDataAtendimento()));
        descricao.append(" - ");
        descricao.append(descricaoEmpresa);
        if (Atendimento.STATUS_CANCELADO.equals(this.status)
                && motivoCancelamento != null) {
            descricao.append(" - ");
            descricao.append(motivoCancelamento);
        }
        return descricao.toString();
    }

}
