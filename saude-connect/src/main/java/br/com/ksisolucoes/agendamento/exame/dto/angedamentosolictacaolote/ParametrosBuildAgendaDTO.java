package br.com.ksisolucoes.agendamento.exame.dto.angedamentosolictacaolote;

import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTO;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoDTOParam;
import br.com.ksisolucoes.agendamento.exame.dto.AgendaGradeAtendimentoPacienteDTO;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeExame;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorCompetencia;
import br.com.ksisolucoes.vo.prontuario.basico.ExamePrestadorProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;

import java.util.List;

public class ParametrosBuildAgendaDTO {

    private List<AgendaGradeAtendimentoDTO> agendasGradeAtendimeto;
    private List<AgendaGradeExame> agendasExames;
    private List<AgendaGradeAtendimentoPacienteDTO> horarios;
    private List<ExamePrestadorProcedimento> examesPrestador;
    private List<ExamePrestadorCompetencia> competencias;
    private AgendaGradeAtendimentoDTOParam param;
    private TipoExame tipoExame;
    private Long tipoTeto;

    public List<AgendaGradeAtendimentoDTO> getAgendasGradeAtendimeto() {
        return agendasGradeAtendimeto;
    }

    public ParametrosBuildAgendaDTO setAgendasGradeAtendimeto(List<AgendaGradeAtendimentoDTO> agendasGradeAtendimeto) {
        this.agendasGradeAtendimeto = agendasGradeAtendimeto;
        return this;
    }

    public List<AgendaGradeExame> getAgendasExames() {
        return agendasExames;
    }

    public ParametrosBuildAgendaDTO setAgendasExames(List<AgendaGradeExame> agendasExames) {
        this.agendasExames = agendasExames;
        return this;
    }

    public List<AgendaGradeAtendimentoPacienteDTO> getHorarios() {
        return horarios;
    }

    public ParametrosBuildAgendaDTO setHorarios(List<AgendaGradeAtendimentoPacienteDTO> horarios) {
        this.horarios = horarios;
        return this;
    }

    public List<ExamePrestadorProcedimento> getExamesPrestador() {
        return examesPrestador;
    }

    public ParametrosBuildAgendaDTO setExamesPrestador(List<ExamePrestadorProcedimento> examesPrestador) {
        this.examesPrestador = examesPrestador;
        return this;
    }

    public List<ExamePrestadorCompetencia> getCompetencias() {
        return competencias;
    }

    public ParametrosBuildAgendaDTO setCompetencias(List<ExamePrestadorCompetencia> competencias) {
        this.competencias = competencias;
        return this;
    }

    public AgendaGradeAtendimentoDTOParam getParam() {
        return param;
    }

    public ParametrosBuildAgendaDTO setParam(AgendaGradeAtendimentoDTOParam param) {
        this.param = param;
        return this;
    }

    public TipoExame getTipoExame() {
        return tipoExame;
    }

    public ParametrosBuildAgendaDTO setTipoExame(TipoExame tipoExame) {
        this.tipoExame = tipoExame;
        return this;
    }

    public Long getTipoTeto() {
        return tipoTeto;
    }

    public ParametrosBuildAgendaDTO setTipoTeto(Long tipoTeto) {
        this.tipoTeto = tipoTeto;
        return this;
    }
}
