/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.ExameProcedimento;
import br.com.ksisolucoes.vo.prontuario.basico.ExameRequisicao;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoCompetencia;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class ExameCadastroDTO implements Serializable {

    public static final String PROP_EXAME_PROCEDIMENTO = "exameProcedimento";
    public static final String PROP_URGENTE = "urgente";
    public static final String PROP_URGENTE_FORMATADO = "urgenteFormatado";
    public static final String PROP_MOTIVO = "motivo";
    public static final String PROP_PROCEDIMENTO_COMPETENCIA = "procedimentoCompetencia";
    public static final String PROP_COMPLEMENTO = "complemento";
    public static final String PROP_QUANTIDADE = "quantidade";
    public static final String PROP_HIV = "hiv";
    public static final String PROP_RETRATAMENTO = "retratamento";

    private ExameProcedimento exameProcedimento;
    private String complemento;
    private String urgente;
    private String motivo;
    private Date dataSolicitacao;
    private ProcedimentoCompetencia procedimentoCompetencia;
    private Double valorProcedimento;
    private Long quantidade;
    private Long hiv;
    private Long retratamento;
    private String justificativa;

    private boolean exameRelacionado;
    private ExameCadastroDTO examePrincipal;

    private ExameRequisicao exameRequisicaoSalvo;

    public ExameRequisicao getExameRequisicaoSalvo() {
        return exameRequisicaoSalvo;
    }

    public void setExameRequisicaoSalvo(ExameRequisicao exameRequisicaoSalvo) {
        this.exameRequisicaoSalvo = exameRequisicaoSalvo;
    }

    public String getComplemento() {
        return complemento;
    }

    public void setComplemento(String complemento) {
        this.complemento = complemento;
    }

    public ExameProcedimento getExameProcedimento() {
        return exameProcedimento;
    }

    public void setExameProcedimento(ExameProcedimento exameProcedimento) {
        this.exameProcedimento = exameProcedimento;
    }

    public ExameCadastroDTO getExamePrincipal() {
        return examePrincipal;
    }

    public void setExamePrincipal(ExameCadastroDTO examePrincipal) {
        this.examePrincipal = examePrincipal;
    }

    public String getMotivo() {
        return motivo;
    }

    public void setMotivo(String motivo) {
        this.motivo = motivo;
    }

    public String getUrgente() {
        return urgente;
    }

    public void setUrgente(String urgente) {
        this.urgente = urgente;
    }

    public String getUrgenteFormatado() {
        if (Exame.URGENTE_SIM.equals(getUrgente())) {
            return Bundle.getStringApplication("rotulo_sim");
        } else if (Exame.URGENTE_NAO.equals(getUrgente())) {
            return Bundle.getStringApplication("rotulo_nao");
        }
        return "";
    }

    public Date getDataSolicitacao() {
        return dataSolicitacao;
    }

    public void setDataSolicitacao(Date dataSolicitacao) {
        this.dataSolicitacao = dataSolicitacao;
    }

    public ProcedimentoCompetencia getProcedimentoCompetencia() {
        return procedimentoCompetencia;
    }

    public void setProcedimentoCompetencia(ProcedimentoCompetencia procedimentoCompetencia) {
        this.procedimentoCompetencia = procedimentoCompetencia;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Double getValorProcedimento() {
        return valorProcedimento;
    }

    public void setValorProcedimento(Double valor) {
        this.valorProcedimento = valor;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public boolean isExameRelacionado() {
        return exameRelacionado;
    }

    public void setExameRelacionado(boolean exameRelacionado) {
        this.exameRelacionado = exameRelacionado;
    }

    public Long getHiv() {
        return hiv;
    }

    public void setHiv(Long hiv) {
        this.hiv = hiv;
    }

    public Long getRetratamento() {
        return retratamento;
    }

    public void setRetratamento(Long retratamento) {
        this.retratamento = retratamento;
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final ExameCadastroDTO other = (ExameCadastroDTO) obj;
        if (this.exameProcedimento != other.exameProcedimento && (this.exameProcedimento == null || !this.exameProcedimento.equals(other.exameProcedimento))) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        int hash = 5;
        return hash;
    }

}
