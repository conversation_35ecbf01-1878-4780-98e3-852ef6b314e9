package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.vo.agendamento.AgendaGrade;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeAtendimentoHorario;
import br.com.ksisolucoes.vo.agendamento.AgendaGradeHorario;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;

/**
 * <AUTHOR>
 */
public class AgendaGradeAtendimentoPacienteDTO implements AgendaGradeHorarioDTO {

    private static final long serialVersionUID = 2337948681784769727L;

    public AgendaGradeHorario agendaGradeHorario;
    public AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario;
    public String nomePaciente;
    public boolean visibleContainerAtendimento;
    public Empresa empresa;
    public Profissional profissional;
    public AgendaGrade agendaGrade;
    public String lembrete;

    public AgendaGradeHorario getAgendaGradeHorario() {
        return agendaGradeHorario;
    }

    public void setAgendaGradeHorario(AgendaGradeHorario agendaGradeHorario) {
        this.agendaGradeHorario = agendaGradeHorario;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public AgendaGradeAtendimentoHorario getAgendaGradeAtendimentoHorario() {
        return agendaGradeAtendimentoHorario;
    }

    public void setAgendaGradeAtendimentoHorario(AgendaGradeAtendimentoHorario agendaGradeAtendimentoHorario) {
        this.agendaGradeAtendimentoHorario = agendaGradeAtendimentoHorario;
    }

    public String getDescricaoPacienteObservacaoFormatado() {
        String descricao = "";

        if (getNomePaciente() != null) {
            descricao += getNomePaciente();
            if (getAgendaGradeAtendimentoHorario().getObservacaoAtendimento() != null) {
                descricao += "<br>";
                descricao += getAgendaGradeAtendimentoHorario().getObservacaoAtendimento();
            }
        }
        return descricao;
    }

    public boolean isVisibleContainerAtendimento() {
        return visibleContainerAtendimento;
    }

    public void setVisibleContainerAtendimento(boolean visibleContainerAtendimento) {
        this.visibleContainerAtendimento = visibleContainerAtendimento;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public AgendaGrade getAgendaGrade() {
        return agendaGrade;
    }

    public void setAgendaGrade(AgendaGrade agendaGrade) {
        this.agendaGrade = agendaGrade;
    }

    @Override
    public boolean isStatusPendente() {
        return agendaGradeHorario.isStatusPendente();
    }

    public String getLembrete() {
        return lembrete;
    }

    public void setLembrete(String lembrete) {
        this.lembrete = lembrete;
    }
}
