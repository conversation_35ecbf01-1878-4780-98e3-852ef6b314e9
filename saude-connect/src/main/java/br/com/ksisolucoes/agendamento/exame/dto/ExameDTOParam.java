package br.com.ksisolucoes.agendamento.exame.dto;

import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Exame;
import br.com.ksisolucoes.vo.prontuario.basico.TipoExame;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ExameDTOParam implements Serializable {

    private Long codigo;
    private List<Empresa> empresas;
    private List<Empresa> localExame;
    private List<Empresa> empresaSolicitante;
    private List<TipoExame> tipoExames;
    private List<Profissional> profissionals;
    private List<Long> status;
    private Long convenio;
    private DatePeriod periodo;
    private String tipoData;
    private String flagAgendado;
    private String solicitado;
    private String recebido;
    private String autorizado;
    private String concluido;
    private String todas;
    private String apenasNaoConfirmados;
    private boolean validarDataInicioAgendamento;
    private List<UsuarioCadsus> pacientes;
    private String nomePaciente;
    private boolean calcularMediaEspera;
    private Ordenacao ordenacao;
    private boolean dataEntregaNull;
    private boolean possuiAtendimento;
    private String campoOrdenacao;
    private String tipoOrdenacao;

    public enum Ordenacao{
        PADRAO,
        DATA_SOLICITACAO,
        CONSULTA_EXAME,
        ENTREGAR_EXAME,
        PRIORIDADE;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public boolean isCalcularMediaEspera() {
        return calcularMediaEspera;
    }

    public void setCalcularMediaEspera(boolean calcularMediaEspera) {
        this.calcularMediaEspera = calcularMediaEspera;
    }

    public List<UsuarioCadsus> getPacientes() {
        return pacientes;
    }

    public void setPacientes(List<UsuarioCadsus> pacientes) {
        this.pacientes = pacientes;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public Long getConvenio() {
        return convenio;
    }

    public void setConvenio(Long convenio) {
        this.convenio = convenio;
    }

    public String getFlagAgendado() {
        return flagAgendado;
    }

    public void setFlagAgendado(String flagAgendado) {
        this.flagAgendado = flagAgendado;
    }

    public List<Empresa> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
        this.empresas = empresas;
    }

    public List<Empresa> getLocalExame() {
        return localExame;
    }

    public void setLocalExame(List<Empresa> localExame) {
        this.localExame = localExame;
    }

    public List<Empresa> getEmpresaSolicitante() {
        return empresaSolicitante;
    }

    public void setEmpresaSolicitante(List<Empresa> empresaSolicitante) {
        this.empresaSolicitante = empresaSolicitante;
    }

    public List<TipoExame> getTipoExames() {
        return tipoExames;
    }

    public void setTipoExames(List<TipoExame> tipoExames) {
        this.tipoExames = tipoExames;
    }

    public List<Long> getStatus() {
        return status;
    }

    public void setStatus(List<Long> status) {
        this.status = status;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public String getTipoData() {
        return tipoData;
    }

    public void setTipoData(String tipoData) {
        this.tipoData = tipoData;
    }

    public String getAutorizado() {
        return autorizado;
    }

    public void setAutorizado(String autorizado) {
        this.autorizado = autorizado;
    }

    public String getConcluido() {
        return concluido;
    }

    public void setConcluido(String concluido) {
        this.concluido = concluido;
    }

    public String getRecebido() {
        return recebido;
    }

    public void setRecebido(String recebido) {
        this.recebido = recebido;
    }

    public String getSolicitado() {
        return solicitado;
    }

    public void setSolicitado(String solicitado) {
        this.solicitado = solicitado;
    }

    public String getTodas() {
        return todas;
    }

    public void setTodas(String todas) {
        this.todas = todas;
    }

    public String getApenasNaoConfirmados() {
        return apenasNaoConfirmados;
    }

    public void setApenasNaoConfirmados(String apenasNaoConfirmados) {
        this.apenasNaoConfirmados = apenasNaoConfirmados;
    }

    public List<Long> getInStatus(){
        if (RepositoryComponentDefault.SIM.equals(getTodas())) {
            return null;
        }

        List<Long> statusList = new ArrayList<Long>();

        if (RepositoryComponentDefault.SIM.equals(getSolicitado())) {
            statusList.add(Exame.STATUS_SOLICITADO);
        }
        if (RepositoryComponentDefault.SIM.equals(getRecebido())) {
            statusList.add(Exame.STATUS_RECEBIDO);
        }
        if (RepositoryComponentDefault.SIM.equals(getAutorizado())) {
            statusList.add(Exame.STATUS_AUTORIZADO);
        }
        if (RepositoryComponentDefault.SIM.equals(getConcluido())) {
            statusList.add(Exame.STATUS_CONCLUIDO_SUS);
        }

        return statusList;
    }

    public Ordenacao getOrdenacao() {
        if (ordenacao == null) {
            return Ordenacao.PADRAO;
        }
        return ordenacao;
    }
    
    public String getCampoOrdenacao() {
        return campoOrdenacao;
    }

    public void setCampoOrdenacao(String campoOrdenacao) {
        this.campoOrdenacao = campoOrdenacao;
    }

    public String getTipoOrdenacao() {
        return tipoOrdenacao;
    }

    public void setTipoOrdenacao(String tipoOrdenacao) {
        this.tipoOrdenacao = tipoOrdenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    public boolean isValidarDataInicioAgendamento() {
        return validarDataInicioAgendamento;
    }

    public void setValidarDataInicioAgendamento(boolean validarDataInicioAgendamento) {
        this.validarDataInicioAgendamento = validarDataInicioAgendamento;
    }

    public List<Profissional> getProfissionals() {
        return profissionals;
    }

    public void setProfissionals(List<Profissional> profissionals) {
        this.profissionals = profissionals;
    }

    public boolean isDataEntregaNull() {
        return dataEntregaNull;
    }

    public void setDataEntregaNull(boolean dataEntregaNull) {
        this.dataEntregaNull = dataEntregaNull;
    }

    public boolean isPossuiAtendimento() {
        return possuiAtendimento;
    }

    public void setPossuiAtendimento(boolean possuiAtendimento) {
        this.possuiAtendimento = possuiAtendimento;
    }

}
