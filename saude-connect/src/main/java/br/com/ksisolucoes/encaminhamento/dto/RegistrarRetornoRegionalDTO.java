/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.ksisolucoes.encaminhamento.dto;

import br.com.ksisolucoes.bo.agendamento.tfd.pedidotfdagendamento.dto.DataAgendamentoRetornoTfdDTO;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.agendamento.tfd.PedidoTfd.Parecer;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RegistrarRetornoRegionalDTO implements Serializable {

    private Long codigoPedido;
    private Long versionPedido;
    private Empresa unidadeAgendamento;
    private Date dataParecer;
    private Date dataAgendamento;
    private String justificativa;
    private Profissional ProfissionalAgendamento;
    private String nomeProfissionalAgendamento;
    private Parecer parecer;
    private List<DataAgendamentoRetornoTfdDTO> dtoDatasRetorno;
    private String contato;

    public Parecer getParecer() {
        return parecer;
    }

    public void setParecer(Parecer parecer) {
        this.parecer = parecer;
    }

    public Profissional getProfissionalAgendamento() {
        return ProfissionalAgendamento;
    }

    public void setProfissionalAgendamento(Profissional ProfissionalAgendamento) {
        this.ProfissionalAgendamento = ProfissionalAgendamento;
    }

    public String getNomeProfissionalAgendamento() {
        return nomeProfissionalAgendamento;
    }

    public void setNomeProfissionalAgendamento(String nomeProfissionalAgendamento) {
        this.nomeProfissionalAgendamento = nomeProfissionalAgendamento;
    }

    public Date getDataAgendamento() {
        return dataAgendamento;
    }

    public void setDataAgendamento(Date dataAgendamento) {
        this.dataAgendamento = dataAgendamento;
    }

    public Date getDataParecer() {
        return dataParecer;
    }

    public void setDataParecer(Date dataParecer) {
        this.dataParecer = dataParecer;
    }

    public String getJustificativa() {
        return justificativa;
    }

    public void setJustificativa(String justificativa) {
        this.justificativa = justificativa;
    }

    public Empresa getUnidadeAgendamento() {
        return unidadeAgendamento;
    }

    public void setUnidadeAgendamento(Empresa unidadeAgendamento) {
        this.unidadeAgendamento = unidadeAgendamento;
    }

    public Long getCodigoPedido() {
        return codigoPedido;
    }

    public void setCodigoPedido(Long codigoPedido) {
        this.codigoPedido = codigoPedido;
    }

    public Long getVersionPedido() {
        return versionPedido;
    }

    public void setVersionPedido(Long versionPedido) {
        this.versionPedido = versionPedido;
    }

    public List<DataAgendamentoRetornoTfdDTO> getDtoDatasRetorno() {
        return dtoDatasRetorno;
    }

    public void setDtoDatasRetorno(List<DataAgendamentoRetornoTfdDTO> datasAgendamentoRetornoTfdDto) {
        this.dtoDatasRetorno = datasAgendamentoRetornoTfdDto;
    }

    public String getContato() {
        return contato;
    }

    public void setContato(String contato) {
        this.contato = contato;
    }

}
