package br.com.ksisolucoes.encaminhamento.interfaces.dto;

import br.com.ksisolucoes.util.DTOParamConfigureDefault;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPagerConsultaCancelamentoLaudoDTOparam implements Serializable {

    private String nomePaciente;
    private Empresa empresa;
    private TipoProcedimento tipoProcedimento;
    private String numeroPedido;
    private DatePeriod periodo;
    private Long situacao;
    private String propSort;
    private boolean ascending;

    private DTOParamConfigureDefault configureParam;

    public DTOParamConfigureDefault getConfigureParam() {
        if (configureParam == null) {
            configureParam = new DTOParamConfigureDefault();
        }
        return configureParam;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public String getNumeroPedido() {
        return numeroPedido;
    }

    public void setNumeroPedido(String numeroPedido) {
        this.numeroPedido = numeroPedido;
    }

    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    public Long getSituacao() {
        return situacao;
    }

    public void setSituacao(Long situacao) {
        this.situacao = situacao;
    }
    
    public String getPropSort() {
        return propSort;
    }

    public void setPropSort(String propSort) {
        this.propSort = propSort;
    }

    public boolean isAscending() {
        return ascending;
    }

    public void setAscending(boolean ascending) {
        this.ascending = ascending;
    }

}
