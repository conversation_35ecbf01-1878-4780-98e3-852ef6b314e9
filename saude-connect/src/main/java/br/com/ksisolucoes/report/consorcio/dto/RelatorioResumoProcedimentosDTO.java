package br.com.ksisolucoes.report.consorcio.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.Dinheiro;
import br.com.ksisolucoes.vo.basico.Cidade;

import java.io.Serializable;
import java.util.Calendar;
import java.util.GregorianCalendar;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoProcedimentosDTO implements Serializable {

    private String formaApresentacao;
    private String tipoResumo;
    private Long numeroGuias;
    private Double valor;
    private Long quantidade;
    private Long quantidadeAplicada;
    private Integer diaResumo;
    private Integer mesResumo;
    private Integer anoResumo;
    private Integer diaForma;
    private Integer mesForma;
    private Integer anoForma;
    private Cidade cidade;
    private Double valorProcedimento;
    private Long somaQuantidade;
    private Double valorSUS;
    private Double valorDif;

    public String getFormaApresentacao() {
        if (diaForma != null) {
            Calendar c = GregorianCalendar.getInstance();
            c.set(Calendar.YEAR, anoForma);
            c.set(Calendar.MONTH, mesForma - 1);
            c.set(Calendar.DAY_OF_MONTH, diaForma);
            return Data.formatar(c.getTime());
        } else if (mesForma != null) {
            return Data.getDescricaoMes(mesForma - 1) + "/" + anoForma;
        }
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public String getTipoResumo() {
        if (diaResumo != null) {
            Calendar c = GregorianCalendar.getInstance();
            c.set(Calendar.YEAR, anoResumo);
            c.set(Calendar.MONTH, mesResumo - 1);
            c.set(Calendar.DAY_OF_MONTH, diaResumo);
            return Data.formatar(c.getTime());
        } else if (mesResumo != null) {
            return Data.getDescricaoMes(mesResumo - 1) + "/" + anoResumo;
        }
        return tipoResumo;
    }

    public void setTipoResumo(String tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    public Long getNumeroGuias() {
        return numeroGuias;
    }

    public void setNumeroGuias(Long numeroGuias) {
        this.numeroGuias = numeroGuias;
    }

    public Double getValor() {
        return valor;
    }

    public void setValor(Double valor) {
        this.valor = valor;
    }

    public Integer getDiaResumo() {
        return diaResumo;
    }

    public void setDiaResumo(Integer diaResumo) {
        this.diaResumo = diaResumo;
    }

    public Integer getMesResumo() {
        return mesResumo;
    }

    public void setMesResumo(Integer mesResumo) {
        this.mesResumo = mesResumo;
    }

    public Integer getAnoResumo() {
        return anoResumo;
    }

    public void setAnoResumo(Integer anoResumo) {
        this.anoResumo = anoResumo;
    }

    public Integer getDiaForma() {
        return diaForma;
    }

    public void setDiaForma(Integer diaForma) {
        this.diaForma = diaForma;
    }

    public Integer getMesForma() {
        return mesForma;
    }

    public void setMesForma(Integer mesForma) {
        this.mesForma = mesForma;
    }

    public Integer getAnoForma() {
        return anoForma;
    }

    public void setAnoForma(Integer anoForma) {
        this.anoForma = anoForma;
    }

    public Long getQuantidade() {
        return quantidade;
    }

    public void setQuantidade(Long quantidade) {
        this.quantidade = quantidade;
    }

    public Long getQuantidadeAplicada() {
        return quantidadeAplicada;
    }

    public void setQuantidadeAplicada(Long quantidadeAplicada) {
        this.quantidadeAplicada = quantidadeAplicada;
    }

    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    public Double getValorProcedimento() {
        Double valorProc = 0D;
        if(getValor().compareTo(0D) > 0) {
            valorProc = new Dinheiro(Coalesce.asDouble(getValor())).dividir(Coalesce.asLong(getSomaQuantidade())).doubleValue();
        }
        return valorProc;
    }

    public void setValorProcedimento(Double valorProcedimento) {
        this.valorProcedimento = valorProcedimento;
    }

    public Long getSomaQuantidade() {
        return somaQuantidade;
    }

    public void setSomaQuantidade(Long somaQuantidade) {
        this.somaQuantidade = somaQuantidade;
    }

    public Double getValorSUS() {
        return valorSUS;
    }

    public void setValorSUS(Double valorSUS) {
        this.valorSUS = valorSUS;
    }

    public Double getValorDif() {
        return new Dinheiro(getValor()).subtrair(getValorSUS()).doubleValue();
    }

}
