package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

public class RelatorioRMNRADTOParam implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * Holds value of property dataInicial.
     */
    private Date dataInicial;

    /**
     * Getter for property dataInicial.
     * @return Value of property dataInicial.
     */
    public Date getDataInicial() {
        if (this.dataInicial == null) {
            Calendar calendar = Data.getDefaultCalendar();
            try {
                calendar.set(Calendar.MONTH, Data.getMes(this.anoMes) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.YEAR, Data.getAno(this.anoMes));
            } catch (ParseException ex) {
                Loggable.log.error(ex);
                return null;
            }
            this.dataInicial = calendar.getTime();
        }
        return this.dataInicial;
    }
    /**
     * Holds value of property dataFinal.
     */
    private Date dataFinal;

    /**
     * Getter for property dataFinal.
     * @return Value of property dataFinal.
     */
    public Date getDataFinal() {
        if (this.dataFinal == null) {
            Calendar calendar = Data.getDefaultCalendar();
            try {
                calendar.set(Calendar.MONTH, Data.getMes(this.anoMes) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, new Long(Data.getUltimoDiaMes(Data.formatar(this.anoMes))).intValue());
                calendar.set(Calendar.YEAR, Data.getAno(this.anoMes));
            } catch (ParseException ex) {
                Loggable.log.error(ex);
                return null;
            }
            this.dataFinal = calendar.getTime();
        }
        return this.dataFinal;
    }
    private Profissional profissionalResponsavel;

    public Profissional getProfissionalResponsavel() {
        return profissionalResponsavel;
    }

    public void setProfissionalResponsavel(Profissional profissionalResponsavel) {
        this.profissionalResponsavel = profissionalResponsavel;
    }
    /**
     * Holds value of property responsavel.
     */
    private String responsavel;

    /**
     * Getter for property farmaceutico.
     * @return Value of property farmaceutico.
     */
    public String getResponsavel() {
        return this.responsavel;
    }

    /**
     * Setter for property farmaceutico.
     * @param farmaceutico New value of property farmaceutico.
     */
    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }
    /**
     * Holds value of property empresa.
     */
    private Empresa empresa;

    /**
     * Getter for property empresas.
     * @return Value of property empresas.
     */
    public Empresa getEmpresa() {
        return this.empresa;
    }

    /**
     * Setter for property empresas.
     * @param empresas New value of property empresas.
     */
    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
    /**
     * Holds value of property anoMes.
     */
    private Date anoMes;

    /**
     * Getter for property anoMes.
     * @return Value of property anoMes.
     */
    public Date getAnoMes() {
        return this.anoMes;
    }

    /**
     * Setter for property anoMes.
     * @param exercicio New value of property anoMes.
     */
    public void setAnoMes(Date anoMes) {
        this.anoMes = anoMes;
    }
    /**
     * Holds value of property crf.
     */
    private String crf;

    /**
     * Getter for property crf.
     * @return Value of property crf.
     */
    public String getCrf() {
        return this.crf;
    }

    /**
     * Setter for property crf.
     * @param crf New value of property crf.
     */
    public void setCrf(String crf) {
        this.crf = crf;
    }
}
