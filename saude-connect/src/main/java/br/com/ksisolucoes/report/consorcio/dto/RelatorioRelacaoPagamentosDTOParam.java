package br.com.ksisolucoes.report.consorcio.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.TipoConta;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoPagamentosDTOParam implements Serializable{

    public enum FormaApresentacao{
        MENSAL(Bundle.getStringApplication("rotulo_mensal")),
        PRESTADOR(Bundle.getStringApplication("rotulo_prestador")),
        BANCO(Bundle.getStringApplication("rotulo_banco")),;
        
        private String descricao;
        
        private FormaApresentacao(String descricao){
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }
    
    public enum TipoResumo{
        MENSAL(Bundle.getStringApplication("rotulo_mensal")),
        PRESTADOR(Bundle.getStringApplication("rotulo_prestador")),
        ;
        
        private String descricao;
        
        private TipoResumo(String descricao){
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public enum Ordenacao{
        COD_PAGAMENTO(Bundle.getStringApplication("rotulo_codigo_pagamento")),
        DESCRICAO_PRESTADOR(Bundle.getStringApplication("rotulo_nome_prestador")),
        ;

        private String descricao;

        private Ordenacao(String descricao){
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }
    
    private Empresa prestador;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private TipoResumo tipoResumo;
    private Ordenacao ordenacao;
    private String tipoPessoa;
    private Long apenasINSS;
    private Long comImposto;
    private TipoConta tipoConta;

    @DescricaoParametro("rotulo_prestador")
    public Empresa getPrestador() {
        return prestador;
    }

    public void setPrestador(Empresa prestador) {
        this.prestador = prestador;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_tipo_resumo")
    public TipoResumo getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(TipoResumo tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_tipo_pessoa")
    public String getTipoPessoa() {
        return tipoPessoa;
    }

    public void setTipoPessoa(String tipoPessoa) {
        this.tipoPessoa = tipoPessoa;
    }

    public Long getApenasINSS() {
        return apenasINSS;
    }

    public void setApenasINSS(Long apenasINSS) {
        this.apenasINSS = apenasINSS;
    }

    @DescricaoParametro("rotulo_apenas_inss")
    public String getApenasINSSFormatado() {
        if(RepositoryComponentDefault.SIM_LONG.equals(this.apenasINSS)){
            return Bundle.getStringApplication("rotulo_sim");
        } else {
            return Bundle.getStringApplication("rotulo_nao");
        }
    }

    public Long getComImposto() {
        return comImposto;
    }

    public void setComImposto(Long comImposto) {
        this.comImposto = comImposto;
    }

    @DescricaoParametro("rotulo_apenas_inss")
    public String getComImpostoFormatado() {
        if(this.comImposto == null){
            return Bundle.getStringApplication("rotulo_ambos");
        } else {
            if (RepositoryComponentDefault.SIM_LONG.equals(this.apenasINSS)) {
                return Bundle.getStringApplication("rotulo_sim");
            } else {
                return Bundle.getStringApplication("rotulo_nao");
            }
        }
    }

    @DescricaoParametro("rotulo_tipo_conta")
    public TipoConta getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(TipoConta tipoConta) {
        this.tipoConta = tipoConta;
    }
}
