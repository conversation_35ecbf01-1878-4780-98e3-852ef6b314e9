package br.com.ksisolucoes.report.cadsus.interfaces.dto;

import br.com.ksisolucoes.vo.cadsus.PlanejamentoVisita;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class QueryPlanejamentoVisitasDTOParam implements Serializable {

    private PlanejamentoVisita planejamentoVisita;
    private boolean imprimirEndereco;
    private boolean imprimirDoenca;
    private boolean ordenarRua;

    public PlanejamentoVisita getPlanejamentoVisita() {
        return planejamentoVisita;
    }

    public void setPlanejamentoVisita(PlanejamentoVisita planejamentoVisita) {
        this.planejamentoVisita = planejamentoVisita;
    }

    public boolean isImprimirEndereco() {
        return imprimirEndereco;
    }

    public void setImprimirEndereco(boolean imprimirEndereco) {
        this.imprimirEndereco = imprimirEndereco;
    }

    public boolean isImprimirDoenca() {
         return imprimirDoenca;
    }

    public void setImprimirDoenca(boolean imprimirDoenca) {
        this.imprimirDoenca = imprimirDoenca;
    }

    public boolean isOrdenarRua() {
        return ordenarRua;
    }

    public void setOrdenarRua(boolean ordenarRua) {
        this.ordenarRua = ordenarRua;
    }
}
