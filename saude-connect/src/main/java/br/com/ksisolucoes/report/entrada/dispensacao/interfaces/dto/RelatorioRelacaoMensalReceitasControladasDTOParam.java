package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Data;
import br.com.ksisolucoes.util.log.Loggable;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.entradas.estoque.GrupoProduto;
import br.com.ksisolucoes.vo.entradas.estoque.SubGrupo;
import br.com.ksisolucoes.vo.prontuario.basico.TipoReceita;
import java.io.Serializable;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;

public class RelatorioRelacaoMensalReceitasControladasDTOParam implements Serializable {

    private Profissional profissionalResponsavel;
    private String responsavel;
    private Empresa empresa;
    private Date anoMes;
    private Date dataFinal;
    private Date dataInicial;
    private GrupoProduto grupoProduto;
    private SubGrupo subGrupo;
    private String formaApresentacao;
    private TipoReceita tipoReceita;
    private Long numeroLista;

    public Date getDataInicial() {
        if (this.dataInicial == null) {
            Calendar calendar = Data.getDefaultCalendar();
            try {
                calendar.set(Calendar.MONTH, Data.getMes(this.anoMes) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, 1);
                calendar.set(Calendar.YEAR, Data.getAno(this.anoMes));
            } catch (ParseException ex) {
                Loggable.log.error(ex);
                return null;
            }
            this.dataInicial = calendar.getTime();
        }
        return this.dataInicial;
    }

    public Date getDataFinal() {
        if (this.dataFinal == null) {
            Calendar calendar = Data.getDefaultCalendar();
            try {
                calendar.set(Calendar.MONTH, Data.getMes(this.anoMes) - 1);
                calendar.set(Calendar.DAY_OF_MONTH, new Long(Data.getUltimoDiaMes(Data.formatar(this.anoMes))).intValue());
                calendar.set(Calendar.YEAR, Data.getAno(this.anoMes));
                calendar.set(Calendar.HOUR, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
            } catch (ParseException ex) {
                Loggable.log.error(ex);
                return null;
            }
            this.dataFinal = calendar.getTime();
        }
        return this.dataFinal;
    }

    public static enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        GRUPO_PRODUTO(Bundle.getStringApplication("rotulo_grupo_subgrupo2"));

        private final String descricao;

        private FormaApresentacao(final String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public Profissional getProfissionalResponsavel() {
        return profissionalResponsavel;
    }

    public void setProfissionalResponsavel(Profissional profissionalResponsavel) {
        this.profissionalResponsavel = profissionalResponsavel;
    }

    public String getResponsavel() {
        return this.responsavel;
    }

    public void setResponsavel(String responsavel) {
        this.responsavel = responsavel;
    }

    public Empresa getEmpresa() {
        return this.empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Date getAnoMes() {
        return this.anoMes;
    }

    public void setAnoMes(Date anoMes) {
        this.anoMes = anoMes;
    }
    private String crf;

    public String getCrf() {
        return this.crf;
    }

    public void setCrf(String crf) {
        this.crf = crf;
    }

    public GrupoProduto getGrupoProduto() {
        return grupoProduto;
    }

    public void setGrupoProduto(GrupoProduto grupoProduto) {
        this.grupoProduto = grupoProduto;
    }

    public SubGrupo getSubGrupo() {
        return subGrupo;
    }

    public void setSubGrupo(SubGrupo subGrupo) {
        this.subGrupo = subGrupo;
    }

    public String getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public TipoReceita getTipoReceita() {
        return tipoReceita;
    }

    public void setTipoReceita(TipoReceita tipoReceita) {
        this.tipoReceita = tipoReceita;
    }

    public Long getNumeroLista() {
        return numeroLista;
    }

    public void setNumeroLista(Long numeroLista) {
        this.numeroLista = numeroLista;
    }

}
