/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.cadsus.interfaces.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCbo;
import br.com.ksisolucoes.vo.prontuario.procedimento.TabelaCboSubGrupo;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioProfissionalDTOParam implements Serializable {

    private OperadorValor<List<Empresa>> empresas;
    private OperadorValor<List<TabelaCbo>> tabelasCbo;
    private OperadorValor<List<Profissional>> profissionais;
    private String exibirVinculos;
    private TabelaCboSubGrupo tabelaCboSubGrupo;
    private String formaApresentacao;
    private Date competenciaInicial;
    private Date competenciaFinal;

    @DescricaoParametro("rotulo_competencia_final")
    public Date getCompetenciaFinal() {
        return competenciaFinal;
    }

    @DescricaoParametro("rotulo_competencia_inicial")
    public Date getCompetenciaInicial() {
        return competenciaInicial;
    }

    public void setCompetenciaFinal(Date competenciaFinal) {
        this.competenciaFinal = competenciaFinal;
    }

    public void setCompetenciaInicial(Date competenciaInicial) {
        this.competenciaInicial = competenciaInicial;
    }

    @DescricaoParametro("rotulo_exibir_vinculos")
    public String getExibirVinculos() {
        return exibirVinculos;
    }

    public void setExibirVinculos(String exibirVinculos) {
        this.exibirVinculos = exibirVinculos;
    }

    @DescricaoParametro("rotulo_profissional")
    public OperadorValor<List<Profissional>> getProfissionais() {
        return profissionais;
    }

    public void setProfissionais(OperadorValor<List<Profissional>> profissionais) {
        this.profissionais = profissionais;
    }

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(OperadorValor<List<Empresa>> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_cbo")
    public OperadorValor<List<TabelaCbo>> getTabelasCbo() {
        return tabelasCbo;
    }

    public void setTabelasCbo(OperadorValor<List<TabelaCbo>> tabelasCbo) {
        this.tabelasCbo = tabelasCbo;
    }

    public TabelaCboSubGrupo getTabelaCboSubGrupo() {
        return tabelaCboSubGrupo;
    }

    public void setTabelaCboSubGrupo(TabelaCboSubGrupo tabelaCboSubGrupo) {
        this.tabelaCboSubGrupo = tabelaCboSubGrupo;
    }

}
