package br.com.ksisolucoes.report.consorcio.dto;

import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioSaldoContasDTOParam implements Serializable{

    private TipoConta tipoConta;
    private Empresa consorciado;
    private String totalGeral;
    private Boolean controlaSaldoPorAno;
    private Long ano;

    @DescricaoParametro("rotulo_tipo_conta")
    public TipoConta getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(TipoConta tipoConta) {
        this.tipoConta = tipoConta;
    }
 
    @DescricaoParametro("rotulo_consorciado")
    public Empresa getConsorciado() {
        return consorciado;
    }

    public void setConsorciado(Empresa consorciado) {
        this.consorciado = consorciado;
    }

    public String getTotalGeral() {
        return totalGeral;
    }

    public void setTotalGeral(String totalGeral) {
        this.totalGeral = totalGeral;
    }

    public Boolean getControlaSaldoPorAno() {
        return controlaSaldoPorAno;
    }

    public void setControlaSaldoPorAno(Boolean controlaSaldoPorAno) {
        this.controlaSaldoPorAno = controlaSaldoPorAno;
    }

    @DescricaoParametro("rotulo_exercicio")
    public Long getAno() {
        return ano;
    }

    public void setAno(Long ano) {
        this.ano = ano;
    }
}
