package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;

import java.io.Serializable;

public class RelatorioRelacaoInventarioDTO implements Serializable {
    
    private String codigoEmpresa;
    private String descricaoEmpresa;
    private Long codigoGrupoProduto;
    private String descricaoGrupoProduto;
    private Long codigoSubGrupo;
    private String descricaoSubGrupo;
    private String codigoProduto;
    private String descricaoProduto;
    private String unidadeProduto;
    private Long codigoLocalizacao;
    private String descricaoLocalizacao;
    private Double estoqueFisico;
    private String flagAtivoEstoqueEmpresa;

    private String descricaoFormatadoSubGrupo;
    private String descricaoLote;
    private Double quantidadeLote;

    public String getCodigoEmpresa() {
        return this.codigoEmpresa;
    }

    public void setCodigoEmpresa( String codigoEmpresa ) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public Long getCodigoGrupoProduto() {
        return this.codigoGrupoProduto;
    }

    public void setCodigoGrupoProduto( Long codigoGrupoProduto ) {
        this.codigoGrupoProduto = codigoGrupoProduto;
    }

    public Long getCodigoLocalizacao() {
        return this.codigoLocalizacao;
    }

    public void setCodigoLocalizacao( Long codigoLocalizacao ) {
        this.codigoLocalizacao = codigoLocalizacao;
    }

    public String getCodigoProduto() {
        return this.codigoProduto;
    }

    public void setCodigoProduto( String codigoProduto ) {
        this.codigoProduto = codigoProduto;
    }

    public Long getCodigoSubGrupo() {
        return this.codigoSubGrupo;
    }

    public void setCodigoSubGrupo( Long codigoSubGrupo ) {
        this.codigoSubGrupo = codigoSubGrupo;
    }

    public String getDescricaoEmpresa() {
        return this.descricaoEmpresa;
    }

    public void setDescricaoEmpresa( String descricaoEmpresa ) {
        this.descricaoEmpresa = descricaoEmpresa;
    }

    public String getDescricaoGrupoProduto() {
        return this.descricaoGrupoProduto;
    }

    public void setDescricaoGrupoProduto( String descricaoGrupoProduto ) {
        this.descricaoGrupoProduto = descricaoGrupoProduto;
    }

    public String getDescricaoLocalizacao() {
        return this.descricaoLocalizacao;
    }

    public void setDescricaoLocalizacao( String descricaoLocalizacao ) {
        this.descricaoLocalizacao = descricaoLocalizacao;
    }

    public String getDescricaoProduto() {
        return this.descricaoProduto;
    }

    public void setDescricaoProduto( String descricaoProduto ) {
        this.descricaoProduto = descricaoProduto;
    }

    public String getDescricaoSubGrupo() {
        return this.descricaoSubGrupo;
    }

    public void setDescricaoSubGrupo( String descricaoSubGrupo ) {
        this.descricaoSubGrupo = descricaoSubGrupo;
    }

    public Double getEstoqueFisico() {
        return this.estoqueFisico;
    }

    public void setEstoqueFisico( Double estoqueFisico ) {
        this.estoqueFisico = estoqueFisico;
    }

    public String getUnidadeProduto() {
        return this.unidadeProduto;
    }

    public void setUnidadeProduto( String unidadeProduto ) {
        this.unidadeProduto = unidadeProduto;
    }

    public String getDescricaoFormatadoGrupoProduto(){
        return Util.getDescricaoFormatado(Coalesce.asString( getCodigoGrupoProduto(), "" ), Coalesce.asString( this.getDescricaoGrupoProduto() ) );
    }

    public String getDescricaoFormatadoSubGrupo(){
        if( this.descricaoFormatadoSubGrupo == null ) {
            this.descricaoFormatadoSubGrupo = Util.getDescricaoFormatado(Coalesce.asString( getCodigoSubGrupo(), "" ), Coalesce.asString( this.getDescricaoSubGrupo() ) );
        }

        return this.descricaoFormatadoSubGrupo;
    }

    public void setDescricaoFormatadoSubGrupo( String descricaoFormatadoSubGrupoProduto ) {
        this.descricaoFormatadoSubGrupo = descricaoFormatadoSubGrupoProduto;
    }

    public String getDescricaoFormatadoProduto(){
        return Util.getDescricaoFormatado(Coalesce.asString( getCodigoProduto(), "" ), Coalesce.asString( this.getDescricaoProduto() ) );
    }

    public String getFlagAtivoEstoqueEmpresaDescricao() {
        if (RepositoryComponentDefault.SIM.equals(getFlagAtivoEstoqueEmpresa())) {
            return Bundle.getStringApplication("rotulo_ativo");
        } else if (RepositoryComponentDefault.NAO.equals(getFlagAtivoEstoqueEmpresa())) {
            return Bundle.getStringApplication("rotulo_inativo");
        }
        return null;
    }
    public String getFlagAtivoEstoqueEmpresa() {
        return flagAtivoEstoqueEmpresa;
    }

    public void setFlagAtivoEstoqueEmpresa(String flagAtivoEstoqueEmpresa) {
        this.flagAtivoEstoqueEmpresa = flagAtivoEstoqueEmpresa;
    }

    public String getDescricaoLote() {
        return descricaoLote;
    }

    public void setDescricaoLote(String descricaoLote) {
        this.descricaoLote = descricaoLote;
    }

    public Double getQuantidadeLote() {
        return quantidadeLote;
    }

    public void setQuantidadeLote(Double quantidadeLote) {
        this.quantidadeLote = quantidadeLote;
    }
}
