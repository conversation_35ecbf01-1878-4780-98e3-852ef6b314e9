/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package br.com.ksisolucoes.report.agendamento.dto;

import br.com.ksisolucoes.operadorvalor.OperadorValor;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.NaturezaProcura;
import br.com.ksisolucoes.vo.prontuario.basico.TipoAtendimento;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioDistribuicaoCotasPpiDTOParam implements Serializable{

    public enum FormaApresentacao{
        DATA(Bundle.getStringApplication("rotulo_data")),
        TIPO_AGENDAMENTO(Bundle.getStringApplication("rotulo_tipo_agendamento")),
        SECRETARIA(Bundle.getStringApplication("rotulo_secretaria")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
        UNIDADE(Bundle.getStringApplication("rotulo_empresa"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    public enum Ordenacao{
        DATA(Bundle.getStringApplication("rotulo_data")),
        UNIDADE(Bundle.getStringApplication("rotulo_empresa")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
        TIPO_AGENDAMENTO(Bundle.getStringApplication("rotulo_tipo_agendamento")),
        SECRETARIA(Bundle.getStringApplication("rotulo_secretaria"));

        private String name;

        private Ordenacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }

    }

    private FormaApresentacao formaApresentacao;
    private Ordenacao ordenacao;
    private OperadorValor<List<Profissional>> profissional;
    private OperadorValor<List<NaturezaProcura>> naturezaProcura;
    private OperadorValor<List<TipoAtendimento>> tipoAtendimento;
    private OperadorValor<List<Empresa>> empresa;
    private List<Empresa> secretaria;
    private DatePeriod periodo;

// <editor-fold defaultstate="collapsed" desc="GetterAndSetter">
    @DescricaoParametro("rotulo_natureza_agendamento")
    public OperadorValor<List<NaturezaProcura>> getNaturezaProcura() {
        return naturezaProcura;
    }

    public void setNaturezaProcura(OperadorValor<List<NaturezaProcura>> naturezaProcura) {
        this.naturezaProcura = naturezaProcura;
    }

    @DescricaoParametro("rotulo_profissional")
    public OperadorValor<List<Profissional>> getProfissional() {
        return profissional;
    }

    public void setProfissional(OperadorValor<List<Profissional>> profissional) {
        this.profissional = profissional;
    }

    @DescricaoParametro("rotulo_tipo_agendamento")
    public OperadorValor<List<TipoAtendimento>> getTipoAtendimento() {
        return tipoAtendimento;
    }

    public void setTipoAtendimento(OperadorValor<List<TipoAtendimento>> tipoAtendimento) {
        this.tipoAtendimento = tipoAtendimento;
    }

    @DescricaoParametro("rotulo_secretaria")
    public List<Empresa> getSecretaria() {
        return secretaria;
    }

    public void setSecretaria(List<Empresa> secretaria) {
        this.secretaria = secretaria;
    }

    @DescricaoParametro("rotulo_empresa")
    public OperadorValor<List<Empresa>> getEmpresa() {
        return empresa;
    }

    public void setEmpresa(OperadorValor<List<Empresa>> empresa) {
        this.empresa = empresa;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }// </editor-fold>
    
}
