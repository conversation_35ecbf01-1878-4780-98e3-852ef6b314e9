package br.com.ksisolucoes.report.agendamento.interfaces.facade;

import br.com.celk.provider.ejb.EJBLocation;
import br.com.ksisolucoes.report.agendamento.exame.dto.RelatorioProcedPrestadorServicoDTOParam;
import br.com.ksisolucoes.report.DataReport;
import br.com.ksisolucoes.report.agendamento.dto.*;
import br.com.ksisolucoes.report.agendamento.exame.dto.*;
import br.com.ksisolucoes.report.agendamento.solicitacaoagendamento.dto.RelatorioComprovanteSolicitacaoAgendamentoDTOParam;
import br.com.ksisolucoes.report.encaminhamento.dto.RelacaoPacientesNaoCompareceramTFDDTOParam;
import br.com.ksisolucoes.report.encaminhamento.dto.RelatorioEncaminhadosRegionalDTOParam;
import br.com.ksisolucoes.report.exception.ReportException;
import br.com.ksisolucoes.report.relacaoidosos.dto.RelatorioCriancasDTOParam;
import br.com.ksisolucoes.report.relacaoidosos.dto.RelatorioIdososDTOParam;
import br.com.ksisolucoes.vo.agendamento.LoteSolicitacaoAgendamento;

import java.util.List;

/**
 *
 * <AUTHOR>
 */
@EJBLocation("br.com.ksisolucoes.report.agendamento.AgendamentoReportBO")
public interface AgendamentoReportFacade {

    DataReport relatorioUnidadeCota(RelatorioUnidadeCotaDTOParam param) throws ReportException;

    DataReport relatorioDistribuicaoCotasPpi(RelatorioDistribuicaoCotasPpiDTOParam param) throws ReportException;

    DataReport relatorioEncaminhadosRegional(RelatorioEncaminhadosRegionalDTOParam param) throws ReportException;

    DataReport relacaoPacientesNaoCompareceramTFD(RelacaoPacientesNaoCompareceramTFDDTOParam param) throws ReportException;

    DataReport relacaoPacientesNaoCompareceram(RelacaoPacientesNaoCompareceramDTOParam param) throws ReportException;

    DataReport relacaoAgenda(RelacaoAgendaDTOParam param) throws ReportException;

    DataReport relatorioAgendamentoExames(RelatorioExameAgendamentoDTOParam param) throws ReportException;

    DataReport relatorioResumoAgendamentoExames(RelatorioResumoExameAgendamentoDTOParam param) throws ReportException;

    DataReport graficoDistribuicaoExameAgendamentos(GraficoDistribuicaoExameAgendamentosDTOParam param) throws ReportException;

    DataReport graficoDistribuicaoMensalExameAgendamentos(GraficoDistribuicaoMensalExameAgendamentosDTOParam param) throws ReportException;

    DataReport relatorioImpressaoAutorizacaoExame(RelatorioImpressaoAutorizacaoExameDTOParam param) throws ReportException;

    DataReport relatorioImpressaoComprovanteEntregaExame(RelatorioImpressaoComprovanteEntregaExameDTOParam param) throws ReportException;

    DataReport relatorioImpressaoComprovanteAgendamentoExameForaRede(RelatorioImpressaoComprovanteAgendamentoExameForaRedeDTOParam param) throws ReportException;

    DataReport relatorioComprovanteSolicitacaoAgendamento(RelatorioComprovanteSolicitacaoAgendamentoDTOParam param) throws ReportException;

    DataReport relatorioComprovanteSolicitacaoAgendamentoAsync(RelatorioComprovanteSolicitacaoAgendamentoDTOParam param) throws ReportException;

    DataReport relatorioResumoSolicitacoes(RelatorioResumoSolicitacoesDTOParam param) throws ReportException;

    DataReport relatorioAcompanhamentoSolicitacoes(RelatorioAcompanhamentoSolicitacoesDTOParam param) throws ReportException;

    DataReport relacaoSolicitacoes(RelacaoSolicitacoesDTOParam param) throws ReportException;

    DataReport relatorioResumoExames(RelatorioResumoExamesDTOParam param) throws ReportException;

    DataReport relatorioRelacaoExames(RelatorioRelacaoExamesDTOParam param) throws ReportException;

    DataReport relacaoAgendasUnidade(RelacaoAgendasUnidadeDTOParam param) throws ReportException;

    DataReport relacaoVagasCotas(RelacaoVagasCotasDTOParam param) throws ReportException;

    DataReport graficoEstatisticasNaoComparecimento(GraficoEstatisticasNaoComparecimentoDTOParam param) throws ReportException;

    DataReport relatorioImpressaoExameBpai(Long param) throws ReportException;

    DataReport relatorioImpressaoExameApac(List<Long> param) throws ReportException;

    DataReport relatorioImpressaoFichaAgendamento(RelatorioImpressaoFichaAgendamentoDTOParam param) throws ReportException;

    DataReport relatorioResumoProcedimentosSolicitados(RelatorioResumoProcedimentosSolicitadosDTOParam param) throws ReportException;

    DataReport relacaoAgendaContato(RelacaoAgendaContatoDTOParam param) throws ReportException;

    DataReport relatorioImpressaoLoteSolicitacao(LoteSolicitacaoAgendamento param) throws ReportException;

    DataReport relatorioResumoExamesAutorizados(RelatorioResumoExamesAutorizadosDTOParam param) throws ReportException;

    DataReport relacaoSolicitacoesViagem(RelacaoSolicitacoesViagemDTOParam param) throws ReportException;

    DataReport relacaoAgendasCanceladasRemanejadas(RelacaoAgendasCanceladasRemanejadasDTOParam param) throws ReportException;

    DataReport relacaoListaEspera(RelacaoListaEsperaDTOParam param) throws ReportException;

    DataReport relatorioImpressaoLaudoExame(RelatorioImpressaoLaudoExameDTOParam param) throws ReportException;

    DataReport relatorioResumoFilaEspera(RelatorioResumoFilaEsperaDTOParam param) throws ReportException;

    DataReport relatorioExames(RelatorioExamesDTOParam param) throws ReportException;

    DataReport relatorioIdosos(RelatorioIdososDTOParam param) throws ReportException;

    DataReport relatorioCriancas(RelatorioCriancasDTOParam param) throws ReportException;

    DataReport relacaoVagasAgenda(RelacaoVagasAgendaDTOParam param) throws ReportException;

    DataReport relatorioTempoMedioFilaEspera(RelatorioTempoMedioFilaEsperaDTOParam param) throws ReportException;

    public DataReport relatorioMonitoramentoSolicitacoes(RelatorioMonitoramentoSolicitacoesDTOParam param) throws ReportException;

    DataReport relatorioProcedimentosPrestadorServico(RelatorioProcedPrestadorServicoDTOParam param) throws ReportException;

    DataReport relatorioFPOPrestadorServico(RelatorioFPOPrestadorServicoDTOParam param) throws ReportException;

    DataReport relatorioExportacaoAgendaLaboratorio(RelatorioExportacaoAgendaLaboratorioPdfDTOParam param) throws ReportException;

    DataReport relatorioProcedimentoRealizadoPrestConsolidComp(RelatorioRealizadoPrestConsoliCompDTOParam param) throws ReportException;
}