package br.com.ksisolucoes.report.vacina.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.vo.vacina.TipoVacina;
import br.com.ksisolucoes.vo.vacina.VacinaCalendario;

import java.io.Serializable;

/**
 *
 * <AUTHOR> Prado
 */
public class RelatorioImpressaoCalendarioVacinacaoDTO implements Serializable{

    private VacinaCalendario vacinaCalendario;

    private Long codigo;
    private Long idade;
    private TipoVacina tipoVacina;
    private Long dose;
    private String opcional;
    private String doencasEvitadas;
    private String observacao;

    public String getIdadeFormatado() {
        this.idade = getIdade();

        if (idade != null) {
            if (idade == 0) {
                return Bundle.getStringApplication("rotulo_ao_nascer");
            }
            if (idade % 12 == 0) {
                idade = idade / 12;

                if (idade == 1L) {
                    return idade + " " + Bundle.getStringApplication("rotulo_ano");
                }
                return idade + " " + Bundle.getStringApplication("rotulo_anos");
            } else {
                if (idade == 1L) {
                    return idade + " " + Bundle.getStringApplication("rotulo_mes");
                }
                return idade + " " + Bundle.getStringApplication("rotulo_meses");
            }
        }
        return "";
    }

    public String getOpcionalFormatado(){  return null; }

    public String getDosesDescricao(){  return null; }


    public VacinaCalendario getVacinaCalendario() {
        return vacinaCalendario;
    }

    public void setVacinaCalendario(VacinaCalendario vacinaCalendario) {
        this.vacinaCalendario = vacinaCalendario;
    }

    public Long getCodigo() {
        return codigo;
    }

    public void setCodigo(Long codigo) {
        this.codigo = codigo;
    }

    public Long getIdade() {
        return idade;
    }

    public void setIdade(Long idade) {
        this.idade = idade;
    }

    public TipoVacina getTipoVacina() {
        return tipoVacina;
    }

    public void setTipoVacina(TipoVacina tipoVacina) {
        this.tipoVacina = tipoVacina;
    }

    public Long getDose() {
        return dose;
    }

    public void setDose(Long dose) {
        this.dose = dose;
    }

    public String getOpcional() {
        return opcional;
    }

    public void setOpcional(String opcional) {
        this.opcional = opcional;
    }

    public String getDoencasEvitadas() {
        return doencasEvitadas;
    }

    public void setDoencasEvitadas(String doencasEvitadas) {
        this.doencasEvitadas = doencasEvitadas;
    }

    public String getObservacao() {
        return observacao;
    }

    public void setObservacao(String observacao) {
        this.observacao = observacao;
    }
}
