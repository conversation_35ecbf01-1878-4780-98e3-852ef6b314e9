package br.com.ksisolucoes.report.agendamento.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.prontuario.basico.Cid;
import br.com.ksisolucoes.vo.prontuario.basico.TipoProcedimento;
import java.io.Serializable;

/**
 *
 * <AUTHOR> Colombo
 */
public class RelatorioResumoSolicitacoesDTO implements Serializable {

    private Empresa empresa;
    private Profissional profissional;
    private TipoProcedimento tipoProcedimento;
    private Cid cid;
    private Long totalSolicitacoes;
    private Long diasMediaEspera;

    public Cid getCid() {
        return cid;
    }

    public void setCid(Cid cid) {
        this.cid = cid;
    }

    public Long getDiasMediaEspera() {
        return diasMediaEspera;
    }

    public void setDiasMediaEspera(Long diasMediaEspera) {
        this.diasMediaEspera = diasMediaEspera;
    }

    public Long getTotalSolicitacoes() {
        return totalSolicitacoes;
    }

    public void setTotalSolicitacoes(Long totalSolicitacoes) {
        this.totalSolicitacoes = totalSolicitacoes;
    }

    public TipoProcedimento getTipoProcedimento() {
        return tipoProcedimento;
    }

    public void setTipoProcedimento(TipoProcedimento tipoProcedimento) {
        this.tipoProcedimento = tipoProcedimento;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }
}
