package br.com.ksisolucoes.report.prontuario.procedimento.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.procedimento.Procedimento;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoRegistroCadastro;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class ConsultaProcedimentoRegistroDTOParam implements Serializable {

    private List<ProcedimentoRegistroCadastro> procedimentoRegistroCadastros;
    private List<Procedimento> procedimentos;
    private Date dataCompetencia;

    public Date getDataCompetencia() {
        return dataCompetencia;
    }

    public void setDataCompetencia(Date dataCompetencia) {
        this.dataCompetencia = dataCompetencia;
    }

    public List<ProcedimentoRegistroCadastro> getProcedimentoRegistroCadastros() {
        return procedimentoRegistroCadastros;
    }

    public void setProcedimentoRegistroCadastros(List<ProcedimentoRegistroCadastro> procedimentoRegistroCadastros) {
        this.procedimentoRegistroCadastros = procedimentoRegistroCadastros;
    }

    public List<Procedimento> getProcedimentos() {
        return procedimentos;
    }

    public void setProcedimentos(List<Procedimento> procedimentos) {
        this.procedimentos = procedimentos;
    }
}
