package br.com.ksisolucoes.report.entrada.estoque.interfaces.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.util.Util;
import br.com.ksisolucoes.vo.entradas.estoque.MovimentoEstoque;
import java.io.Serializable;

public final class RelatorioAnaliseEstoqueDTO extends MovimentoEstoque implements Serializable {

    private static final long serialVersionUID = 1L;
    private Double quantidadeSaida;
    private Double saldoEstoqueFisico;
    private Double saldoValor;
    private String codigoProduto;
    private String descricaoProduto;
    private Double quantidadeEntrada;
    private Double valorSaida;
    private Double valorEntrada;
    private Long codigoSubGrupo;
    private String descricaoEmpresa;
    private String codigoEmpresa;
    private Long codigoGrupo;
    private String descricaoGrupo;
    private String descricaoSubGrupo;
    private Long codigoCentroCusto;
    private String descricaoCentroCusto;

    public RelatorioAnaliseEstoqueDTO() {
        super();
    }

    public String getDescricaoEmpresa() {
        return this.descricaoEmpresa;
    }

    public void setDescricaoEmpresa(String descricaoEmpresa) {
        this.descricaoEmpresa = descricaoEmpresa;
    }

    public String getCodigoEmpresa() {
        return this.codigoEmpresa;
    }

    public void setCodigoEmpresa(String codigoEmpresa) {
        this.codigoEmpresa = codigoEmpresa;
    }

    public void setCodigoGrupo(Long codigoGrupo) {
        this.codigoGrupo = codigoGrupo;
    }

    public Long getCodigoGrupo() {
        return this.codigoGrupo;
    }

    public String getDescricaoGrupo() {
        return this.descricaoGrupo;
    }

    public void setDescricaoGrupo(String descricaoGrupo) {
        this.descricaoGrupo = descricaoGrupo;
    }

    public String getDescricaoGrupoFormatado() {
        return Util.getDescricaoFormatado(Coalesce.asString(this.getCodigoGrupo(), ""), Coalesce.asString(this.getDescricaoGrupo()));
    }

    public String getDescricaoSubGrupo() {
        return this.descricaoSubGrupo;
    }

    public void setDescricaoSubGrupo(String descricaoSubGrupo) {
        this.descricaoSubGrupo = descricaoSubGrupo;
    }

    public Long getCodigoSubGrupo() {
        return this.codigoSubGrupo;
    }

    public void setCodigoSubGrupo(Long codigoSubGrupo) {
        this.codigoSubGrupo = codigoSubGrupo;
    }

    public String getDescricaoSubGrupoFormatado() {
        return Util.getDescricaoFormatado(Coalesce.asString(this.getCodigoSubGrupo(), ""), Coalesce.asString(this.getDescricaoSubGrupo()));
    }

    public String getDescricaoProduto() {
        return this.descricaoProduto;
    }

    public void setDescricaoProduto(String descricaoProduto) {
        this.descricaoProduto = descricaoProduto;
    }

    public String getCodigoProduto() {
        return this.codigoProduto;
    }

    public void setCodigoProduto(String codigoProduto) {
        this.codigoProduto = codigoProduto;
    }

    public String getDescricaoProdutoFormatado() {
        return Util.getDescricaoFormatado(Coalesce.asString(this.getCodigoProduto()), Coalesce.asString(this.getDescricaoProduto()));
    }

    public Double getQuantidadeEntrada() {
        return this.quantidadeEntrada;
    }

    public void setQuantidadeEntrada(Double quantidadeEntrada) {
        this.quantidadeEntrada = quantidadeEntrada;
    }

    public Double getValorEntrada() {
        return this.valorEntrada;
    }

    public void setValorEntrada(Double valorEntrada) {
        this.valorEntrada = valorEntrada;
    }

    public Double getValorSaida() {
        return this.valorSaida;
    }

    public void setValorSaida(Double valorSaida) {
        this.valorSaida = valorSaida;
    }

    public Double getSaldoEstoqueFisico() {
        return this.saldoEstoqueFisico;
    }

    public void setSaldoEstoqueFisico(Double saldoEstoqueFisico) {
        this.saldoEstoqueFisico = saldoEstoqueFisico;
    }

    public Double getSaldoValor() {
        return this.saldoValor;
    }

    public void setSaldoValor(Double saldoValor) {
        this.saldoValor = saldoValor;
    }

    public Double getQuantidadeSaida() {
        return this.quantidadeSaida;
    }

    public void setQuantidadeSaida(Double quantidadeSaida) {
        this.quantidadeSaida = quantidadeSaida;
    }

    public Long getCodigoCentroCusto() {
        return codigoCentroCusto;
    }

    public void setCodigoCentroCusto(Long codigoCentroCusto) {
        this.codigoCentroCusto = codigoCentroCusto;
    }

    public String getDescricaoCentroCusto() {
        return descricaoCentroCusto;
    }

    public void setDescricaoCentroCusto(String descricaoCentroCusto) {
        this.descricaoCentroCusto = descricaoCentroCusto;
    }

    public String getDescricaoCentroCustoFormatado() {
        if (getCodigoCentroCusto()!=null) {
            return Util.getDescricaoFormatado(Coalesce.asString(this.getCodigoCentroCusto()), Coalesce.asString(this.getDescricaoCentroCusto()));
        }
        return Bundle.getStringApplication("rotulo_nao_definido");
    }
}
