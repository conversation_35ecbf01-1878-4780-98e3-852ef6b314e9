package br.com.ksisolucoes.report.consorcio.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Cidade;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGrupo;
import br.com.ksisolucoes.vo.consorcio.ConsorcioGuiaProcedimento;
import br.com.ksisolucoes.vo.consorcio.ConsorcioProcedimento;
import br.com.ksisolucoes.vo.consorcio.TipoConta;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoFormaOrganizacao;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoGrupo;
import br.com.ksisolucoes.vo.prontuario.procedimento.ProcedimentoSubGrupo;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioResumoProcedimentosDTOParam implements Serializable {

    public enum FormaApresentacao {

        PRESTADOR(Bundle.getStringApplication("rotulo_prestador")),
        PROCEDIMENTO(Bundle.getStringApplication("rotulo_procedimento")),
        GRUPO_PROCEDIMENTO(Bundle.getStringApplication("rotulo_grupo_procedimento")),
        USUARIO(Bundle.getStringApplication("rotulo_usuario")),
        CONSORCIADO(Bundle.getStringApplication("rotulo_consorciado")),
        DIARIO(Bundle.getStringApplication("rotulo_diario")),
        MENSAL(Bundle.getStringApplication("rotulo_mensal")),
        CIDADE(Bundle.getStringApplication("rotulo_cidade"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum TipoResumo {

        PRESTADOR(Bundle.getStringApplication("rotulo_prestador")),
        PROCEDIMENTO(Bundle.getStringApplication("rotulo_procedimento")),
        GRUPO_PROCEDIMENTO(Bundle.getStringApplication("rotulo_grupo_procedimento")),
        USUARIO(Bundle.getStringApplication("rotulo_usuario")),
        CONSORCIADO(Bundle.getStringApplication("rotulo_consorciado")),
        DIARIO(Bundle.getStringApplication("rotulo_diario")),
        MENSAL(Bundle.getStringApplication("rotulo_mensal")),
        CIDADE(Bundle.getStringApplication("rotulo_cidade"));

        private String name;

        private TipoResumo(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    public enum TipoData {

        CADASTRO("dataCadastro", Bundle.getStringApplication("rotulo_cadastro")),
        UTILIZACAO("dataAplicacao", Bundle.getStringApplication("rotulo_utilizacao")),
        AGENDAMENTO("dataAgendamento", Bundle.getStringApplication("rotulo_agendamento")),
        CANCELAMENTO("dataCancelamento", Bundle.getStringApplication("rotulo_cancelamento")),
        PAGAMENTO("dataPagamento", Bundle.getStringApplication("rotulo_pagamento")),;

        private String value;
        private String descricao;

        private TipoData(String value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        public String value() {
            return value;
        }

        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }

    }

    public enum Ordenacao {

        VALOR(Bundle.getStringApplication("rotulo_valor")),
        TIPO_RESUMO(Bundle.getStringApplication("rotulo_tipo_resumo")),
        QUANTIDADE(Bundle.getStringApplication("rotulo_quantidade_solicitada")),
        QUANTIDADE_APLICADA(Bundle.getStringApplication("rotulo_quantidade_aplicada"));

        private String descricao;

        private Ordenacao(String descricao) {
            this.descricao = descricao;
        }

        public String descricao() {
            return descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }

    }

    private ConsorcioProcedimento consorcioProcedimento;
    private Empresa consorciado;
    private Empresa prestador;
    private UsuarioCadsus paciente;
    private TipoData tipoData;
    private DatePeriod periodo;
    private boolean situacaoAberta;
    private boolean situacaoCancelada;
    private boolean situacaoUtilizada;
    private boolean situacaoPaga;
    private FormaApresentacao formaApresentacao;
    private TipoResumo tipoResumo;
    private Ordenacao ordenacao;
    private Cidade cidade;
    private TipoConta tipoConta;
    private ProcedimentoGrupo procedimentoGrupo;
    private ProcedimentoSubGrupo procedimentoSubGrupo;
    private ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao;
    private ConsorcioGrupo consorcioGrupo;

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_consorciado")
    public Empresa getConsorciado() {
        return consorciado;
    }

    public void setConsorciado(Empresa consorciado) {
        this.consorciado = consorciado;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_tipo_resumo")
    public TipoResumo getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(TipoResumo tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    @DescricaoParametro("rotulo_prestador")
    public Empresa getPrestador() {
        return prestador;
    }

    public void setPrestador(Empresa prestador) {
        this.prestador = prestador;
    }

    @DescricaoParametro("rotulo_paciente")
    public UsuarioCadsus getPaciente() {
        return paciente;
    }

    public void setPaciente(UsuarioCadsus paciente) {
        this.paciente = paciente;
    }

    @DescricaoParametro("rotulo_tipo_data")
    public TipoData getTipoData() {
        return tipoData;
    }

    public void setTipoData(TipoData tipoData) {
        this.tipoData = tipoData;
    }

    @DescricaoParametro("rotulo_grupo_procedimento")
    public String getConsorcioGrupoDescricao() {
        if (getConsorcioGrupo() == null) {
            return Bundle.getStringApplication("rotulo_todos");
        }
        return getConsorcioGrupo().getDescricao();
    }

    public ConsorcioGrupo getConsorcioGrupo() {
        return consorcioGrupo;
    }

    public void setConsorcioGrupo(ConsorcioGrupo consorcioGrupo) {
        this.consorcioGrupo = consorcioGrupo;
    }

    @DescricaoParametro("rotulo_situacao")
    public String getSituacaoFormatado() {
        String situacaoFormatado = null;
        if (situacaoAberta) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            }
            situacaoFormatado += ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.descricao();
        }
        if (situacaoCancelada) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            } else {
                situacaoFormatado += ", ";
            }
            situacaoFormatado += ConsorcioGuiaProcedimento.StatusGuiaProcedimento.CANCELADA.descricao();
        }
        if (situacaoPaga) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            } else {
                situacaoFormatado += ", ";
            }
            situacaoFormatado += ConsorcioGuiaProcedimento.StatusGuiaProcedimento.PAGA.descricao();
        }
        if (situacaoUtilizada) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            } else {
                situacaoFormatado += ", ";
            }
            situacaoFormatado += ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.descricao();
        }
        return situacaoFormatado;
    }

    public List<Long> getInSituacao() {
        List<Long> situacoes = new ArrayList<Long>();

        if (situacaoAberta) {
            situacoes.add(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.ABERTA.value());
        }
        if (situacaoCancelada) {
            situacoes.add(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.CANCELADA.value());
        }
        if (situacaoPaga) {
            situacoes.add(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.PAGA.value());
        }
        if (situacaoUtilizada) {
            situacoes.add(ConsorcioGuiaProcedimento.StatusGuiaProcedimento.A_PAGAR.value());
        }

        return situacoes;
    }

    public boolean isSituacaoAberta() {
        return situacaoAberta;
    }

    public void setSituacaoAberta(boolean situacaoAberta) {
        this.situacaoAberta = situacaoAberta;
    }

    public boolean isSituacaoCancelada() {
        return situacaoCancelada;
    }

    public void setSituacaoCancelada(boolean situacaoCancelada) {
        this.situacaoCancelada = situacaoCancelada;
    }

    public boolean isSituacaoUtilizada() {
        return situacaoUtilizada;
    }

    public void setSituacaoUtilizada(boolean situacaoUtilizada) {
        this.situacaoUtilizada = situacaoUtilizada;
    }

    public boolean isSituacaoPaga() {
        return situacaoPaga;
    }

    public void setSituacaoPaga(boolean situacaoPaga) {
        this.situacaoPaga = situacaoPaga;
    }

    public ConsorcioProcedimento getConsorcioProcedimento() {
        return consorcioProcedimento;
    }

    public void setConsorcioProcedimento(ConsorcioProcedimento consorcioProcedimento) {
        this.consorcioProcedimento = consorcioProcedimento;
    }

    @DescricaoParametro("rotulo_procedimento")
    public String getDescricaoProcedimento() {
        if (getConsorcioProcedimento() != null) {
            return getConsorcioProcedimento().getDescricaoProcedimentoFormatado();
        }
        return null;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }

    @DescricaoParametro("rotulo_cidade")
    public Cidade getCidade() {
        return cidade;
    }

    public void setCidade(Cidade cidade) {
        this.cidade = cidade;
    }

    @DescricaoParametro("rotulo_grupo")
    public ProcedimentoGrupo getProcedimentoGrupo() {
        return procedimentoGrupo;
    }

    public void setProcedimentoGrupo(ProcedimentoGrupo procedimentoGrupo) {
        this.procedimentoGrupo = procedimentoGrupo;
    }

    @DescricaoParametro("rotulo_sub_grupo")
    public ProcedimentoSubGrupo getProcedimentoSubGrupo() {
        return procedimentoSubGrupo;
    }

    public void setProcedimentoSubGrupo(ProcedimentoSubGrupo procedimentoSubGrupo) {
        this.procedimentoSubGrupo = procedimentoSubGrupo;
    }

    @DescricaoParametro("rotulo_forma_organizacao")
    public ProcedimentoFormaOrganizacao getProcedimentoFormaOrganizacao() {
        return procedimentoFormaOrganizacao;
    }

    public void setProcedimentoFormaOrganizacao(ProcedimentoFormaOrganizacao procedimentoFormaOrganizacao) {
        this.procedimentoFormaOrganizacao = procedimentoFormaOrganizacao;
    }

    @DescricaoParametro("rotulo_tipo_conta")
    public TipoConta getTipoConta() {
        return tipoConta;
    }

    public void setTipoConta(TipoConta tipoConta) {
        this.tipoConta = tipoConta;
    }
}
