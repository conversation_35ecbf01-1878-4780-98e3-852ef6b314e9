package br.com.ksisolucoes.report.patrimonio.interfaces.dto;

import br.com.ksisolucoes.vo.patrimonio.BemPatrimonio;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelacaoPatrimoniosDTO implements Serializable{

    
    private BemPatrimonio bemPatrimonio;

    public BemPatrimonio getBemPatrimonio() {
        return bemPatrimonio;
    }

    public void setBemPatrimonio(BemPatrimonio bemPatrimonio) {
        this.bemPatrimonio = bemPatrimonio;
    }

    
    
}
