package br.com.ksisolucoes.report.materiais.judicial.interfaces.dto;

import br.com.ksisolucoes.util.Coalesce;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitado;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoItem;
import br.com.ksisolucoes.vo.basico.ProdutoSolicitadoMovimento;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoMedicamentoJudicialDTO implements Serializable {
    
    public static final String PROP_PRODUTO_SOLICITADO = "produtoSolicitado";
    public static final String PROP_ESTOQUE = "estoque";

    private Produto produto;
    private Double quantidadeMensal;
    private ProdutoSolicitado produtoSolicitado;
    private Double estoque;
    private List<ProdutoSolicitadoMovimento> movimentosList;
    private Date dataValidade;
    private ProdutoSolicitadoItem produtoSolicitadoItem;

    public Date getDataValidade() {
        return dataValidade;
    }

    public void setDataValidade(Date dataValidade) {
        this.dataValidade = dataValidade;
    }

    public List<ProdutoSolicitadoMovimento> getMovimentosList() {
        return movimentosList;
    }

    public void setMovimentosList(List movimentosList) {
        this.movimentosList = movimentosList;
    }

    public Double getEstoque() {
        return Coalesce.asDouble(estoque);
    }

    public void setEstoque(Double estoque) {
        this.estoque = estoque;
    }

    public ProdutoSolicitado getProdutoSolicitado() {
        return produtoSolicitado;
    }

    public void setProdutoSolicitado(ProdutoSolicitado produtoSolicitado) {
        this.produtoSolicitado = produtoSolicitado;
    }

    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public Double getQuantidadeMensal() {
        return quantidadeMensal;
    }

    public void setQuantidadeMensal(Double quantidadeMensal) {
        this.quantidadeMensal = quantidadeMensal;
    }

    public ProdutoSolicitadoItem getProdutoSolicitadoItem() {
        return produtoSolicitadoItem;
    }

    public void setProdutoSolicitadoItem(ProdutoSolicitadoItem produtoSolicitadoItem) {
        this.produtoSolicitadoItem = produtoSolicitadoItem;
    }
}
