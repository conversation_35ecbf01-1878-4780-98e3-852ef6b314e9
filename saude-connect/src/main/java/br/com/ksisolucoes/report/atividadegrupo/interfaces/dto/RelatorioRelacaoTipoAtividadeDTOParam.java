package br.com.ksisolucoes.report.atividadegrupo.interfaces.dto;

import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.programasaude.ProgramaSaude;
import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoTipoAtividadeDTOParam implements Serializable {

    private List<Empresa> empresas;
    private List<ProgramaSaude> programasSaude;
    private String formaApresentacao;

    @DescricaoParametro("rotulo_empresa")
    public List<Empresa> getEmpresas() {
        return empresas;
    }

    public void setEmpresas(List<Empresa> empresas) {
        this.empresas = empresas;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public String getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(String formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_programas_saude")
    public List<ProgramaSaude> getProgramasSaude() {
        return programasSaude;
    }

    public void setProgramasSaude(List<ProgramaSaude> programasSaude) {
        this.programasSaude = programasSaude;
    }

}
