package br.com.ksisolucoes.report.encaminhamento.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusDado;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioImpressaoPedidoTfdDTO implements Serializable {

    private LaudoTfd laudoTfd;
    private UsuarioCadsusCns usuarioCadsusCns;
    private UsuarioCadsusDado usuarioCadsusDado;

    public LaudoTfd getLaudoTfd() {
        return laudoTfd;
    }

    public void setLaudoTfd(LaudoTfd laudoTfd) {
        this.laudoTfd = laudoTfd;
    }

    public UsuarioCadsusDado getUsuarioCadsusDado() {
        return usuarioCadsusDado;
    }

    public void setUsuarioCadsusDado(UsuarioCadsusDado usuarioCadsusDado) {
        this.usuarioCadsusDado = usuarioCadsusDado;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }

    public RelatorioImpressaoPedidoTfdDTO getThis(){
        return this;
    }

}
