package br.com.ksisolucoes.report.encaminhamento.dto;

import br.com.ksisolucoes.vo.cadsus.UsuarioCadsusCns;
import br.com.ksisolucoes.vo.agendamento.tfd.LaudoTfd;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioEncaminhadosRegionalDTO implements Serializable {

    private LaudoTfd laudoTfd;
    private UsuarioCadsusCns usuarioCadsusCns;

    public LaudoTfd getLaudoTfd() {
        return laudoTfd;
    }

    public void setLaudoTfd(LaudoTfd laudoTfd) {
        this.laudoTfd = laudoTfd;
    }

    public UsuarioCadsusCns getUsuarioCadsusCns() {
        return usuarioCadsusCns;
    }

    public void setUsuarioCadsusCns(UsuarioCadsusCns usuarioCadsusCns) {
        this.usuarioCadsusCns = usuarioCadsusCns;
    }
}
