package br.com.ksisolucoes.report.consorcio.dto;

import br.com.celk.util.Coalesce;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.basico.Pessoa;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.consorcio.PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao;
import br.com.ksisolucoes.vo.entradas.estoque.Produto;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class RelatorioPedidosTransferenciaFornecedorDTOParam implements Serializable {

    private Empresa consorciado;
    private Pessoa fornecedor;
    private Produto produto;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;
    private TipoResumo tipoResumo;
    private String flagListaProdutos;
    private BaseCalculo baseCalculo;
    private TipoEstoque tipoEstoque;

    private boolean situacaoAberto;
    private boolean situacaoCancelado;
    private boolean situacaoEncaminhado;
    private boolean situacaoSeparado;
    private boolean situacaoFechado;

    @DescricaoParametro("rotulo_tipo_estoque")
    public TipoEstoque getTipoEstoque() {
        return tipoEstoque;
    }

    public void setTipoEstoque(TipoEstoque tipoEstoque) {
        this.tipoEstoque = tipoEstoque;
    }

    @DescricaoParametro("rotulo_consorcio")
    public Empresa getConsorciado() {
        return consorciado;
    }

    public void setConsorciado(Empresa consorciado) {
        this.consorciado = consorciado;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_fornecedor")
    public Pessoa getFornecedor() {
        return fornecedor;
    }

    public void setFornecedor(Pessoa fornecedor) {
        this.fornecedor = fornecedor;
    }

    @DescricaoParametro("rotulo_situacao_pedido")
    public String getDescricaoSituacaoPedido() {
        String situacaoFormatado = null;
        if (situacaoAberto) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            }
            situacaoFormatado += PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.ABERTO.descricao();
        }
        if (situacaoEncaminhado) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            } else {
                situacaoFormatado += ", ";
            }
            situacaoFormatado += PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.ENCAMINHADO.descricao();
        }
        if (situacaoSeparado) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            } else {
                situacaoFormatado += ", ";
            }
            situacaoFormatado += PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.SEPARADO.descricao();
        }
        if (situacaoCancelado) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            } else {
                situacaoFormatado += ", ";
            }
            situacaoFormatado += PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.CANCELADO.descricao();
        }
        if (situacaoFechado) {
            if (situacaoFormatado == null) {
                situacaoFormatado = "";
            } else {
                situacaoFormatado += ", ";
            }
            situacaoFormatado += PedidoTransferenciaLicitacao.StatusPedidoTransferenciaLicitacao.FECHADO.descricao();
        }
        return situacaoFormatado;
    }

    public List<Long> getInSituacao() {
        List<Long> situacoes = new ArrayList<Long>();
        if (situacaoAberto) {
            situacoes.add(StatusPedidoTransferenciaLicitacao.ABERTO.value());
        }
        if (situacaoEncaminhado) {
            situacoes.add(StatusPedidoTransferenciaLicitacao.ENCAMINHADO.value());
        }
        if (situacaoSeparado) {
            situacoes.add(StatusPedidoTransferenciaLicitacao.SEPARADO.value());
        }
        if (situacaoCancelado) {
            situacoes.add(StatusPedidoTransferenciaLicitacao.CANCELADO.value());
        }
        if (situacaoFechado) {
            situacoes.add(StatusPedidoTransferenciaLicitacao.FECHADO.value());
        }

        return situacoes;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    @DescricaoParametro("rotulo_tipo_resumo")
    public TipoResumo getTipoResumo() {
        return tipoResumo;
    }

    public void setTipoResumo(TipoResumo tipoResumo) {
        this.tipoResumo = tipoResumo;
    }

    @DescricaoParametro("baseCalculo")
    public BaseCalculo getBaseCalculo() {
        return baseCalculo;
    }

    public void setBaseCalculo(BaseCalculo baseCalculo) {
        this.baseCalculo = baseCalculo;
    }

    @DescricaoParametro("rotulo_produto")
    public Produto getProduto() {
        return produto;
    }

    public void setProduto(Produto produto) {
        this.produto = produto;
    }

    public String getFlagListaProdutos() {
        return flagListaProdutos;
    }

    public void setFlagListaProdutos(String flagListaProdutos) {
        this.flagListaProdutos = flagListaProdutos;
    }

    public boolean isSituacaoAberto() {
        return situacaoAberto;
    }

    public void setSituacaoAberto(boolean situacaoAberto) {
        this.situacaoAberto = situacaoAberto;
    }

    public boolean isSituacaoCancelado() {
        return situacaoCancelado;
    }

    public void setSituacaoCancelado(boolean situacaoCancelado) {
        this.situacaoCancelado = situacaoCancelado;
    }

    public boolean isSituacaoEncaminhado() {
        return situacaoEncaminhado;
    }

    public void setSituacaoEncaminhado(boolean situacaoEncaminhado) {
        this.situacaoEncaminhado = situacaoEncaminhado;
    }

    public boolean isSituacaoSeparado() {
        return situacaoSeparado;
    }

    public void setSituacaoSeparado(boolean situacaoSeparado) {
        this.situacaoSeparado = situacaoSeparado;
    }

    public boolean isSituacaoFechado() {
        return situacaoFechado;
    }

    public void setSituacaoFechado(boolean situacaoFechado) {
        this.situacaoFechado = situacaoFechado;
    }

    public static enum FormaApresentacao implements IEnum<FormaApresentacao> {

        CONSORCIADO(0L, Bundle.getStringApplication("rotulo_consorciado")),
        FORNECEDOR(1L, Bundle.getStringApplication("rotulo_fornecedor"));

        private Long value;
        private String descricao;

        private FormaApresentacao(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum TipoResumo implements IEnum<TipoResumo> {

        FORNECEDOR(1L, Bundle.getStringApplication("rotulo_fornecedor")),
        CONSORCIADO(0L, Bundle.getStringApplication("rotulo_consorciado"));

        private Long value;
        private String descricao;

        private TipoResumo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum BaseCalculo implements IEnum<BaseCalculo> {

        QUANTIDADE(1L, Bundle.getStringApplication("rotulo_quantidade")),
        SALDO(0L, Bundle.getStringApplication("rotulo_saldo"));

        private Long value;
        private String descricao;

        private BaseCalculo(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }

    public static enum TipoEstoque implements IEnum<TipoEstoque> {

        DISPONIVEL(1L, Bundle.getStringApplication("rotulo_disponivel")),
        FISICO(0L, Bundle.getStringApplication("rotulo_fisico"));

        private Long value;
        private String descricao;

        private TipoEstoque(Long value, String descricao) {
            this.value = value;
            this.descricao = descricao;
        }

        @Override
        public Object value() {
            return this.value;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public String toString() {
            return descricao;
        }
    }
}
