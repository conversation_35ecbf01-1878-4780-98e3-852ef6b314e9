package br.com.ksisolucoes.report.frota.interfaces.dto;

import br.com.celk.system.report.TipoRelatorio;
import br.com.ksisolucoes.enums.IEnum;
import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.util.validacao.RepositoryComponentDefault;
import br.com.ksisolucoes.vo.frota.Motorista;
import br.com.ksisolucoes.vo.frota.Veiculo;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioRelacaoViagensDTOParam implements Serializable {
    
    public enum FormaApresentacao implements IEnum<FormaApresentacao> {
        GERAL(Bundle.getStringApplication("rotulo_geral")),
        VEICULO(Bundle.getStringApplication("rotulo_veiculo")),
        MOTORISTA(Bundle.getStringApplication("rotulo_motorista")),
        DESTINO(Bundle.getStringApplication("rotulo_destino")),;
        private String descricao;

        private FormaApresentacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Object value() {
            return this;
        }

        public static FormaApresentacao valueOf(Long value) {
            for (FormaApresentacao formaApresentacao : FormaApresentacao.values()) {
                if (formaApresentacao.value().equals(value)) {
                    return formaApresentacao;
                }
            }
            return null;
        }
    }
    
    public enum Ordenacao implements IEnum<RelacaoDiarioBordoDTOParam.Ordenacao> {

        VEICULO(Bundle.getStringApplication("rotulo_veiculo")),
        MOTORISTA(Bundle.getStringApplication("rotulo_motorista")),
        DESTINO(Bundle.getStringApplication("rotulo_destino")),;
        private String descricao;

        private Ordenacao(String descricao) {
            this.descricao = descricao;
        }

        @Override
        public String descricao() {
            return this.descricao;
        }

        @Override
        public Object value() {
            return this;
        }

        public static Ordenacao valueOf(Long value) {
            for (Ordenacao ordenacao : Ordenacao.values()) {
                if (ordenacao.value().equals(value)) {
                    return ordenacao;
                }
            }
            return null;
        }
    }
    
    private Veiculo veiculo;
    private Motorista motorista;
    private String destino;
    private FormaApresentacao formaApresentacao;
    private String visualizarPassageiros;
    private DatePeriod periodo;
    private TipoRelatorio tipoRelatorio;
    private Ordenacao ordenacao;

    @DescricaoParametro("rotulo_veiculo")
    public Veiculo getVeiculo() {
        return veiculo;
    }

    public void setVeiculo(Veiculo veiculo) {
        this.veiculo = veiculo;
    }

    @DescricaoParametro("rotulo_motorista")
    public Motorista getMotorista() {
        return motorista;
    }

    public void setMotorista(Motorista motorista) {
        this.motorista = motorista;
    }

    @DescricaoParametro("rotulo_destino")
    public String getDestino() {
        return destino;
    }

    public void setDestino(String destino) {
        this.destino = destino;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }    

    public String getVisualizarPassageiros() {
        return visualizarPassageiros;
    }

    public void setVisualizarPassageiros(String visualizarPassageiros) {
        this.visualizarPassageiros = visualizarPassageiros;
    }
    
    @DescricaoParametro("rotulo_visualizar_passageiros")
    public String getVisualizarPassageirosDescricao() {
        if(RepositoryComponentDefault.SIM.equals(getVisualizarPassageiros())){
            return Bundle.getStringApplication("rotulo_sim");
        } else {
            return Bundle.getStringApplication("rotulo_nao");
        }
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_tipo_relatorio")
    public TipoRelatorio getTipoRelatorio() {
        return tipoRelatorio;
    }

    public void setTipoRelatorio(TipoRelatorio tipoRelatorio) {
        this.tipoRelatorio = tipoRelatorio;
    }

    @DescricaoParametro("rotulo_ordenacao")
    public Ordenacao getOrdenacao() {
        return ordenacao;
    }

    public void setOrdenacao(Ordenacao ordenacao) {
        this.ordenacao = ordenacao;
    }
}
