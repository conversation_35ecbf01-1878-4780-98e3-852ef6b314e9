package br.com.ksisolucoes.report.hospital.interfaces.dto.tiss;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class GuiaSolicitacaoInternacaoDTO implements Serializable {

    private Long codigoConta;

    /* Dados Gerais */
    private String registroAns;
    private String numeroGuiaOperadora;
    private String numeroGuiaPrestador;
    private Date dataAutorizacao;
    private String senhaAutorizacao;
    private Date validadeSenha;
    private Date dataSolicitacao;

    /* Dados do Beneficiário */
    private String carteiraBeneficiario;
    private Date validadeCarteira;
    private String atendimentoRN;
    private String nomePaciente;
    private Long cnsPaciente;

    /* Dados Contratado Solicitando */
    private String codigoPrestadorSolicitanteNaOperadora;
    private String nomeContratadoSolicitante;
    private String nomeProfissional;
    private Long conselhoClasseProfissional;
    private String numeroConselhoProfissional;
    private String ufConselho;
    private String cboProfissional;

    /* Dados do Hospital / Local Solicitado / Dados da Internação */
    private String codigoOperadora;
    private String nomeOperadora;
    private Date dataSugeridaInternacao;
    private Long caraterAtendimento;
    private String tipoInternacao;
    private String regimeInternacao;
    private String qtdeDiariasSolicitadas;
    private String previsaoUsoOpme;
    private String previsaoUsoQuimioterapico;
    private String cidPrincipal;
    private String cidOpcional2;
    private String cidOpcional3;
    private String cidOpcional4;
    private String indicacaoAcidente;

    /* Dados da Autorização */
    private Date dataProvavelAdmissaoHospitalar;
    private String qtdeDiariasAutorizadas;
    private String tipoAcomodacaoAutorizada;
    private String codigoOperadoraAutorizada;
    private String nomeOperadoraAutorizada;
    private String codigoCnesOperadoraAutorizada;

    private List<GuiaSolicitacaoInternacaoItemDTO> itemList = new ArrayList<GuiaSolicitacaoInternacaoItemDTO>();

    public Long getCodigoConta() {
        return codigoConta;
    }

    public void setCodigoConta(Long codigoConta) {
        this.codigoConta = codigoConta;
    }

    public String getRegistroAns() {
        return registroAns;
    }

    public void setRegistroAns(String registroAns) {
        this.registroAns = registroAns;
    }

    public String getNumeroGuiaOperadora() {
        return numeroGuiaOperadora;
    }

    public void setNumeroGuiaOperadora(String numeroGuiaOperadora) {
        this.numeroGuiaOperadora = numeroGuiaOperadora;
    }

    public String getNumeroGuiaPrestador() {
        return numeroGuiaPrestador;
    }

    public void setNumeroGuiaPrestador(String numeroGuiaPrestador) {
        this.numeroGuiaPrestador = numeroGuiaPrestador;
    }

    public Date getDataAutorizacao() {
        return dataAutorizacao;
    }

    public void setDataAutorizacao(Date dataAutorizacao) {
        this.dataAutorizacao = dataAutorizacao;
    }

    public String getSenhaAutorizacao() {
        return senhaAutorizacao;
    }

    public void setSenhaAutorizacao(String senhaAutorizacao) {
        this.senhaAutorizacao = senhaAutorizacao;
    }

    public Date getValidadeSenha() {
        return validadeSenha;
    }

    public void setValidadeSenha(Date validadeSenha) {
        this.validadeSenha = validadeSenha;
    }

    public Date getDataSolicitacao() {
        return dataSolicitacao;
    }

    public void setDataSolicitacao(Date dataSolicitacao) {
        this.dataSolicitacao = dataSolicitacao;
    }

    public String getCarteiraBeneficiario() {
        return carteiraBeneficiario;
    }

    public void setCarteiraBeneficiario(String carteiraBeneficiario) {
        this.carteiraBeneficiario = carteiraBeneficiario;
    }

    public Date getValidadeCarteira() {
        return validadeCarteira;
    }

    public void setValidadeCarteira(Date validadeCarteira) {
        this.validadeCarteira = validadeCarteira;
    }

    public String getAtendimentoRN() {
        return atendimentoRN;
    }

    public void setAtendimentoRN(String atendimentoRN) {
        this.atendimentoRN = atendimentoRN;
    }

    public String getNomePaciente() {
        return nomePaciente;
    }

    public void setNomePaciente(String nomePaciente) {
        this.nomePaciente = nomePaciente;
    }

    public Long getCnsPaciente() {
        return cnsPaciente;
    }

    public void setCnsPaciente(Long cnsPaciente) {
        this.cnsPaciente = cnsPaciente;
    }

    public String getCodigoPrestadorSolicitanteNaOperadora() {
        return codigoPrestadorSolicitanteNaOperadora;
    }

    public void setCodigoPrestadorSolicitanteNaOperadora(String codigoPrestadorSolicitanteNaOperadora) {
        this.codigoPrestadorSolicitanteNaOperadora = codigoPrestadorSolicitanteNaOperadora;
    }

    public String getNomeContratadoSolicitante() {
        return nomeContratadoSolicitante;
    }

    public void setNomeContratadoSolicitante(String nomeContratadoSolicitante) {
        this.nomeContratadoSolicitante = nomeContratadoSolicitante;
    }

    public String getNomeProfissional() {
        return nomeProfissional;
    }

    public void setNomeProfissional(String nomeProfissional) {
        this.nomeProfissional = nomeProfissional;
    }

    public Long getConselhoClasseProfissional() {
        return conselhoClasseProfissional;
    }

    public void setConselhoClasseProfissional(Long conselhoClasseProfissional) {
        this.conselhoClasseProfissional = conselhoClasseProfissional;
    }

    public String getNumeroConselhoProfissional() {
        return numeroConselhoProfissional;
    }

    public void setNumeroConselhoProfissional(String numeroConselhoProfissional) {
        this.numeroConselhoProfissional = numeroConselhoProfissional;
    }

    public String getUfConselho() {
        return ufConselho;
    }

    public void setUfConselho(String ufConselho) {
        this.ufConselho = ufConselho;
    }

    public String getCboProfissional() {
        return cboProfissional;
    }

    public void setCboProfissional(String cboProfissional) {
        this.cboProfissional = cboProfissional;
    }

    public String getCodigoOperadora() {
        return codigoOperadora;
    }

    public void setCodigoOperadora(String codigoOperadora) {
        this.codigoOperadora = codigoOperadora;
    }

    public String getNomeOperadora() {
        return nomeOperadora;
    }

    public void setNomeOperadora(String nomeOperadora) {
        this.nomeOperadora = nomeOperadora;
    }

    public Date getDataSugeridaInternacao() {
        return dataSugeridaInternacao;
    }

    public void setDataSugeridaInternacao(Date dataSugeridaInternacao) {
        this.dataSugeridaInternacao = dataSugeridaInternacao;
    }

    public Long getCaraterAtendimento() {
        return caraterAtendimento;
    }

    public void setCaraterAtendimento(Long caraterAtendimento) {
        this.caraterAtendimento = caraterAtendimento;
    }

    public String getTipoInternacao() {
        return tipoInternacao;
    }

    public void setTipoInternacao(String tipoInternacao) {
        this.tipoInternacao = tipoInternacao;
    }

    public String getRegimeInternacao() {
        return regimeInternacao;
    }

    public void setRegimeInternacao(String regimeInternacao) {
        this.regimeInternacao = regimeInternacao;
    }

    public String getQtdeDiariasSolicitadas() {
        return qtdeDiariasSolicitadas;
    }

    public void setQtdeDiariasSolicitadas(String qtdeDiariasSolicitadas) {
        this.qtdeDiariasSolicitadas = qtdeDiariasSolicitadas;
    }

    public String getPrevisaoUsoOpme() {
        return previsaoUsoOpme;
    }

    public void setPrevisaoUsoOpme(String previsaoUsoOpme) {
        this.previsaoUsoOpme = previsaoUsoOpme;
    }

    public String getPrevisaoUsoQuimioterapico() {
        return previsaoUsoQuimioterapico;
    }

    public void setPrevisaoUsoQuimioterapico(String previsaoUsoQuimioterapico) {
        this.previsaoUsoQuimioterapico = previsaoUsoQuimioterapico;
    }

    public String getCidPrincipal() {
        return cidPrincipal;
    }

    public void setCidPrincipal(String cidPrincipal) {
        this.cidPrincipal = cidPrincipal;
    }

    public String getCidOpcional2() {
        return cidOpcional2;
    }

    public void setCidOpcional2(String cidOpcional2) {
        this.cidOpcional2 = cidOpcional2;
    }

    public String getCidOpcional3() {
        return cidOpcional3;
    }

    public void setCidOpcional3(String cidOpcional3) {
        this.cidOpcional3 = cidOpcional3;
    }

    public String getCidOpcional4() {
        return cidOpcional4;
    }

    public void setCidOpcional4(String cidOpcional4) {
        this.cidOpcional4 = cidOpcional4;
    }

    public String getIndicacaoAcidente() {
        return indicacaoAcidente;
    }

    public void setIndicacaoAcidente(String indicacaoAcidente) {
        this.indicacaoAcidente = indicacaoAcidente;
    }

    public Date getDataProvavelAdmissaoHospitalar() {
        return dataProvavelAdmissaoHospitalar;
    }

    public void setDataProvavelAdmissaoHospitalar(Date dataProvavelAdmissaoHospitalar) {
        this.dataProvavelAdmissaoHospitalar = dataProvavelAdmissaoHospitalar;
    }

    public String getQtdeDiariasAutorizadas() {
        return qtdeDiariasAutorizadas;
    }

    public void setQtdeDiariasAutorizadas(String qtdeDiariasAutorizadas) {
        this.qtdeDiariasAutorizadas = qtdeDiariasAutorizadas;
    }

    public String getTipoAcomodacaoAutorizada() {
        return tipoAcomodacaoAutorizada;
    }

    public void setTipoAcomodacaoAutorizada(String tipoAcomodacaoAutorizada) {
        this.tipoAcomodacaoAutorizada = tipoAcomodacaoAutorizada;
    }

    public String getCodigoOperadoraAutorizada() {
        return codigoOperadoraAutorizada;
    }

    public void setCodigoOperadoraAutorizada(String codigoOperadoraAutorizada) {
        this.codigoOperadoraAutorizada = codigoOperadoraAutorizada;
    }

    public String getNomeOperadoraAutorizada() {
        return nomeOperadoraAutorizada;
    }

    public void setNomeOperadoraAutorizada(String nomeOperadoraAutorizada) {
        this.nomeOperadoraAutorizada = nomeOperadoraAutorizada;
    }

    public String getCodigoCnesOperadoraAutorizada() {
        return codigoCnesOperadoraAutorizada;
    }

    public void setCodigoCnesOperadoraAutorizada(String codigoCnesOperadoraAutorizada) {
        this.codigoCnesOperadoraAutorizada = codigoCnesOperadoraAutorizada;
    }

    public List<GuiaSolicitacaoInternacaoItemDTO> getItemList() {
        return itemList;
    }

    public void setItemList(List<GuiaSolicitacaoInternacaoItemDTO> itemList) {
        this.itemList = itemList;
    }
}
