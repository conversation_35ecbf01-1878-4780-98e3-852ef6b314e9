package br.com.ksisolucoes.report.vigilancia.dengue.armadilhavisita.dto;

import br.com.ksisolucoes.util.Bundle;
import br.com.ksisolucoes.util.DatePeriod;
import br.com.ksisolucoes.util.descricaoparametro.annotations.DescricaoParametro;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueAreaVigilancia;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueArmadilha;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueLocalidade;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueMicroAreaVigilancia;
import br.com.ksisolucoes.vo.vigilancia.dengue.DengueTipoImovel;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class RelatorioVisitaArmadilhaDTOParam implements Serializable {

    private DengueTipoImovel tipoImovel;
    private DengueAreaVigilancia area;
    private DengueMicroAreaVigilancia microArea;
    private DengueLocalidade localidade;
    private Long tipoArmadilha;
    private DatePeriod periodo;
    private FormaApresentacao formaApresentacao;

    public enum FormaApresentacao {

        GERAL(Bundle.getStringApplication("rotulo_geral")),
        DATA_VISITA(Bundle.getStringApplication("rotulo_data_visita")),
        PROFISSIONAL(Bundle.getStringApplication("rotulo_profissional")),
        ARMADILHA(Bundle.getStringApplication("rotulo_armadilha")),
        LOCALIDADE(Bundle.getStringApplication("rotulo_localidade"));

        private String name;

        private FormaApresentacao(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return name;
        }
    }

    @DescricaoParametro("rotulo_tipo_imovel")
    public DengueTipoImovel getTipoImovel() {
        return tipoImovel;
    }

    public void setTipoImovel(DengueTipoImovel tipoImovel) {
        this.tipoImovel = tipoImovel;
    }

    @DescricaoParametro("rotulo_area")
    public DengueAreaVigilancia getArea() {
        return area;
    }

    public void setArea(DengueAreaVigilancia area) {
        this.area = area;
    }

    @DescricaoParametro("rotulo_microarea")
    public DengueMicroAreaVigilancia getMicroArea() {
        return microArea;
    }

    public void setMicroArea(DengueMicroAreaVigilancia microArea) {
        this.microArea = microArea;
    }

    @DescricaoParametro("rotulo_localidade")
    public DengueLocalidade getLocalidade() {
        return localidade;
    }

    public void setLocalidade(DengueLocalidade localidade) {
        this.localidade = localidade;
    }

    @DescricaoParametro("rotulo_periodo")
    public DatePeriod getPeriodo() {
        return periodo;
    }

    public void setPeriodo(DatePeriod periodo) {
        this.periodo = periodo;
    }

    @DescricaoParametro("rotulo_forma_apresentacao")
    public FormaApresentacao getFormaApresentacao() {
        return formaApresentacao;
    }

    public void setFormaApresentacao(FormaApresentacao formaApresentacao) {
        this.formaApresentacao = formaApresentacao;
    }

    public Long getTipoArmadilha() {
        return tipoArmadilha;
    }

    public void setTipoArmadilha(Long tipoArmadilha) {
        this.tipoArmadilha = tipoArmadilha;
    }
    
    @DescricaoParametro("rotulo_tipo_armadilha")
    public String getDescricaoTipoArmadilha(){
        return DengueArmadilha.TipoArmadilha.valeuOf(getTipoArmadilha()).descricao();
    }
}
