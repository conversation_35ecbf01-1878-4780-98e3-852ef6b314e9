package br.com.ksisolucoes.report.entrada.dispensacao.interfaces.dto;

import br.com.ksisolucoes.vo.prontuario.basico.Receituario;
import br.com.ksisolucoes.vo.prontuario.basico.ReceituarioItem;

import java.io.Serializable;

public class RelatorioDispensacaoReceitaDTO implements Serializable {

    private ReceituarioItem receituarioItem;
    private Receituario receituario;

    public RelatorioDispensacaoReceitaDTO() {
    }

    public ReceituarioItem getReceituarioItem() {
        return receituarioItem;
    }

    public void setReceituarioItem(ReceituarioItem receituarioItem) {
        this.receituarioItem = receituarioItem;
    }

    public Receituario getReceituario() {
        return receituario;
    }

    public void setReceituario(Receituario receituario) {
        this.receituario = receituario;
    }
}
