package br.com.ksisolucoes.report.prontuario.exame.interfaces.dto;

import br.com.ksisolucoes.vo.basico.Empresa;
import br.com.ksisolucoes.vo.cadsus.EnderecoUsuarioCadsus;
import br.com.ksisolucoes.vo.cadsus.Profissional;
import br.com.ksisolucoes.vo.cadsus.UsuarioCadsus;
import br.com.ksisolucoes.vo.prontuario.basico.Atendimento;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapido;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoConjunto;
import br.com.ksisolucoes.vo.prontuario.basico.TesteRapidoRealizado;
import br.com.ksisolucoes.vo.prontuario.exame.ResultadoAidsAvancado;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class ImpressaoTesteRapidoDTO implements Serializable {

    private UsuarioCadsus usuarioCadsus;
    private Profissional profissional;
    private Empresa empresa;
    private Atendimento atendimento;
    private EnderecoUsuarioCadsus enderecoUsuarioCadsus;
    private TesteRapido testeRapido;
    private TesteRapidoRealizado testeRapidoRealizado;
    private TesteRapidoConjunto testeRapidoConjunto;
    private ResultadoAidsAvancado resultadoAidsAvancado;

    boolean imprimeFicha;
    boolean imprimeRelatorioFicha;

    List<ImpressaoTesteRapidoDTO> testeRapidoHepatiteB;
    List<ImpressaoTesteRapidoDTO> testeRapidoHepatiteC;
    List<ImpressaoTesteRapidoDTO> testeRapidoSifilis;
    List<ImpressaoTesteRapidoDTO> testeRapidoTuberculose;
    List<ImpressaoTesteRapidoDTO> testeRapidoHIV;
    List<ImpressaoTesteRapidoDTO> testeRapidoCovid19;
    List<ImpressaoTesteRapidoDTO> testeRapidoDengue;
    List<ImpressaoTesteRapidoDTO> testeRapidoInfluenza;
    List<ImpressaoTesteRapidoDTO> testeRapidoHanseniase;
    List<ImpressaoTesteRapidoDTO> testeRapidoHivSifilis;
    List<ImpressaoTesteRapidoDTO> testeRapidoAidsAvancado;
    List<ImpressaoTesteRapidoDTO> fichaTesteRapido;

    public ImpressaoTesteRapidoDTO() {
        this.imprimeFicha = true;
        this.imprimeRelatorioFicha = true;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoCovid19() {
        return testeRapidoCovid19;
    }

    public void setTesteRapidoCovid19(List<ImpressaoTesteRapidoDTO> testeRapidoCovid19) {
        this.testeRapidoCovid19 = testeRapidoCovid19;
    }

    public UsuarioCadsus getUsuarioCadsus() {
        return usuarioCadsus;
    }

    public void setUsuarioCadsus(UsuarioCadsus usuarioCadsus) {
        this.usuarioCadsus = usuarioCadsus;
    }

    public Profissional getProfissional() {
        return profissional;
    }

    public void setProfissional(Profissional profissional) {
        this.profissional = profissional;
    }

    public Empresa getEmpresa() {
        return empresa;
    }

    public void setEmpresa(Empresa empresa) {
        this.empresa = empresa;
    }

    public Atendimento getAtendimento() {
        return atendimento;
    }

    public void setAtendimento(Atendimento atendimento) {
        this.atendimento = atendimento;
    }

    public EnderecoUsuarioCadsus getEnderecoUsuarioCadsus() {
        return enderecoUsuarioCadsus;
    }

    public void setEnderecoUsuarioCadsus(EnderecoUsuarioCadsus enderecoUsuarioCadsus) {
        this.enderecoUsuarioCadsus = enderecoUsuarioCadsus;
    }

    public TesteRapido getTesteRapido() {
        return testeRapido;
    }

    public void setTesteRapido(TesteRapido testeRapido) {
        this.testeRapido = testeRapido;
    }

    public TesteRapidoRealizado getTesteRapidoRealizado() {
        return testeRapidoRealizado;
    }

    public void setTesteRapidoRealizado(TesteRapidoRealizado testeRapidoRealizado) {
        this.testeRapidoRealizado = testeRapidoRealizado;
    }

    public TesteRapidoConjunto getTesteRapidoConjunto() {
        return testeRapidoConjunto;
    }

    public void setTesteRapidoConjunto(TesteRapidoConjunto testeRapidoConjunto) {
        this.testeRapidoConjunto = testeRapidoConjunto;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoHepatiteB() {
        return testeRapidoHepatiteB;
    }

    public void setTesteRapidoHepatiteB(List<ImpressaoTesteRapidoDTO> testeRapidoHepatiteB) {
        this.testeRapidoHepatiteB = testeRapidoHepatiteB;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoHepatiteC() {
        return testeRapidoHepatiteC;
    }

    public void setTesteRapidoHepatiteC(List<ImpressaoTesteRapidoDTO> testeRapidoHepatiteC) {
        this.testeRapidoHepatiteC = testeRapidoHepatiteC;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoSifilis() {
        return testeRapidoSifilis;
    }

    public void setTesteRapidoSifilis(List<ImpressaoTesteRapidoDTO> testeRapidoSifilis) {
        this.testeRapidoSifilis = testeRapidoSifilis;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoTuberculose() {
        return testeRapidoTuberculose;
    }

    public void setTesteRapidoTuberculose(List<ImpressaoTesteRapidoDTO> testeRapidoTuberculose) {
        this.testeRapidoTuberculose = testeRapidoTuberculose;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoHIV() {
        return testeRapidoHIV;
    }

    public void setTesteRapidoHIV(List<ImpressaoTesteRapidoDTO> testeRapidoHIV) {
        this.testeRapidoHIV = testeRapidoHIV;
    }

    public List<ImpressaoTesteRapidoDTO> getFichaTesteRapido() {
        return fichaTesteRapido;
    }

    public void setFichaTesteRapido(List<ImpressaoTesteRapidoDTO> fichaTesteRapido) {
        this.fichaTesteRapido = fichaTesteRapido;
    }

    public boolean isImprimeFicha() {
        return imprimeFicha;
    }

    public void setImprimeFicha(boolean imprimeFicha) {
        this.imprimeFicha = imprimeFicha;
    }

    public boolean isImprimeRelatorioFicha() {
        return imprimeRelatorioFicha;
    }

    public void setImprimeRelatorioFicha(boolean imprimeRelatorioFicha) {
        this.imprimeRelatorioFicha = imprimeRelatorioFicha;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoDengue() {
        return testeRapidoDengue;
    }

    public void setTesteRapidoDengue(List<ImpressaoTesteRapidoDTO> testeRapidoDengue) {
        this.testeRapidoDengue = testeRapidoDengue;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoInfluenza() {
        return testeRapidoInfluenza;
    }

    public void setTesteRapidoInfluenza(List<ImpressaoTesteRapidoDTO> testeRapidoInfluenza) {
        this.testeRapidoInfluenza = testeRapidoInfluenza;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoHanseniase() {
        return testeRapidoHanseniase;
    }

    public void setTesteRapidoHanseniase(List<ImpressaoTesteRapidoDTO> testeRapidoHanseniase) {
        this.testeRapidoHanseniase = testeRapidoHanseniase;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoHivSifilis() {
        return testeRapidoHivSifilis;
    }

    public void setTesteRapidoHivSifilis(List<ImpressaoTesteRapidoDTO> testeRapidoHivSifilis) {
        this.testeRapidoHivSifilis = testeRapidoHivSifilis;
    }

    public List<ImpressaoTesteRapidoDTO> getTesteRapidoAidsAvancado() {
        return testeRapidoAidsAvancado;
    }

    public void setTesteRapidoAidsAvancado(List<ImpressaoTesteRapidoDTO> testeRapidoAidsAvancado) {
        this.testeRapidoAidsAvancado = testeRapidoAidsAvancado;
    }

    public ResultadoAidsAvancado getResultadoAidsAvancado() {
        return resultadoAidsAvancado;
    }

    public void setResultadoAidsAvancado(ResultadoAidsAvancado resultadoAidsAvancado) {
        this.resultadoAidsAvancado = resultadoAidsAvancado;
    }
}
